package com.mmt.hotels.clientgateway.configuration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.api.ISecretsManager;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.impl.SecretsManagerConfig;
import com.mmt.hotels.impl.SecretsManagerImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Configuration
public class SecretMangerConfig {

        private static final Logger LOGGER = LoggerFactory.getLogger(SecretMangerConfig.class);

        @Value("${spring.aws.secretsmanager.secretname.prod}")
        private String secretPathProd;

        @Value("${spring.aws.secretsmanager.region.prod}")
        private String awsRegionProd;

        @Value("${spring.aws.secretsmanager.secretname.alpha}")
        private String secretPathAlpha;

        @Value("${spring.aws.secretsmanager.region.alpha}")
        private String awsRegionAlpha;

        @Value("${spring.aws.secretsmanager.secretname.dev}")
        private String secretPathDev;

        @Value("${spring.aws.secretsmanager.region.dev}")
        private String awsRegionDev;

        private String secretsPath;

        private ISecretsManager secretsManager;

        @Autowired
        private ObjectMapperUtil objectMapperUtil;

        private JsonNode dbSecrets;

        @PostConstruct
        public void init(){
        try {
            SecretsManagerConfig secretsManagerConfig = new SecretsManagerConfig();
            secretsManager = SecretsManagerImpl.getInstance(secretsManagerConfig);
            fetchAndPopulateSecrets();
        }
        catch (Exception ex){
            LOGGER.error("Problem getting AWS secrets for Clientgateway GI Service : {}", ex.getMessage(), ex);
        }
    }

        private void fetchAndPopulateSecrets() throws Exception {
        String env = System.getenv("environment");

        if(env != null)
            secretsPath = env.contains("dev") ? secretPathDev : (env.contains("alpha") ? secretPathAlpha : secretPathProd);
        else
            secretsPath = secretPathProd;

        String secrets = secretsManager.getSecret(secretsPath);
        ObjectMapper objectMapper = new ObjectMapper();
        dbSecrets = objectMapper.readTree(secrets);
    }

        public String getSecretKeyForEncryption() {
        return dbSecrets.get("AESHCPSecretKey").textValue();
    }
}
