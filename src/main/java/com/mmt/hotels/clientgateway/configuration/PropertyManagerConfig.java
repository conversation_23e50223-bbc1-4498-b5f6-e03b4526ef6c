package com.mmt.hotels.clientgateway.configuration;

import java.util.Arrays;

import com.mmt.hotels.clientgateway.pms.PMSUrls;
import com.mmt.hotels.clientgateway.service.ListingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.mmt.propertymanager.config.PropertyManager;
import org.springframework.core.env.Environment;

@Configuration
public class PropertyManagerConfig {

	private static final Logger logger = LoggerFactory.getLogger(PropertyManagerConfig.class);

	@Autowired
	Environment environment;

	@Bean
	@Qualifier("propertyManager")
	public PropertyManager getPropertyManager(){
		PropertyManager p = new PropertyManager();
		p.setCallbackPrefix( "http://" );
		p.setCallbackSuffix( "/clientbackend/pmsListener" );
		p.setPropManagerSubscriptionUrl( PMSUrls.GIRGIT_HOST + "/pms/client/createOrUpdateSubscription" );
		p.setBasePackages (Arrays.asList( "com.mmt".split(",")));
		return p ;
	}
}
