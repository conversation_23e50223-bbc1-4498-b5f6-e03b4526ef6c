package com.mmt.hotels.clientgateway.configuration;

import com.mmt.hotels.clientgateway.request.GroupBookingRequest;
import com.mmt.hotels.kafka.JsonKafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.kafka.connect.json.JsonSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.mmt.hotels.data.KafkaLoggingConfiguration;
import com.mmt.hotels.kafka.AsyncKafkaLogger;

import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConfig {
	
	@Value("${kafka.queue.schedule.sec:30}")
	private int kafkaLoggingIntervalInSec;
	@Value("${kafka.queue.max.size:30000}")
	private int kafkaMaxQueueSize;
	@Value("${kafka.throttle.Onperformance:false}")
	private boolean isThrottleOnSystemPerformance;
	@Value("${kafka.system.cpu.threshold.percent:70}")
	private int kafkaCPUPercThreshold;
	@Value("${kafka.system.heap.threshold.percent:70}")
	private int kafkaHeapPercThreshold;
	@Value("${kafka.system.performance.avgPollTime:5}")
	private int checkMemoryAverageInterval;
	@Value("${kafka.broker.list}")
	private String kafkaBrokerList;
	@Value("${krb.conf.filepath}")
	private String kerberosFilePath;
	@Value("${jaas.conf.filepath}")
	private String jaasFilePath;
	
	@Bean(name = "asyncKafkaLogger")
	public AsyncKafkaLogger<?> getAsyncKafkaLogger() {
		KafkaLoggingConfiguration kafkaLoggingConfiguration = new KafkaLoggingConfiguration();
		kafkaLoggingConfiguration.setKafkaLoggingIntervalInSec(kafkaLoggingIntervalInSec);
		kafkaLoggingConfiguration.setKafkaMaxQueueSize(kafkaMaxQueueSize);
		kafkaLoggingConfiguration.setThrottleOnSystemPerformance(isThrottleOnSystemPerformance);
		kafkaLoggingConfiguration.setKafkaCPUPercThreshold(kafkaCPUPercThreshold);
		kafkaLoggingConfiguration.setKafkaHeapPercThreshold(kafkaHeapPercThreshold);
		kafkaLoggingConfiguration.setCheckMemoryAverageInterval(checkMemoryAverageInterval);
		kafkaLoggingConfiguration.setKafkaBrokerPrimaryList(kafkaBrokerList);
		kafkaLoggingConfiguration.setKafkaBrokerSecondaryList(kafkaBrokerList);
		kafkaLoggingConfiguration.setJaasConfFilePath(jaasFilePath);
		kafkaLoggingConfiguration.setKrbConfFilePath(kerberosFilePath);
//		return AsyncKafkaLogger.getInstance(kafkaLoggingConfiguration);
		return null;
	}

	@Bean(name = "jsonKafkaProducer")
	public JsonKafkaProducer<?> getJsonKafkaProducer(){
//		KafkaLoggingConfiguration kafkaLoggingConfiguration=new KafkaLoggingConfiguration();
//		kafkaLoggingConfiguration.setJaasConfFilePath(jaasFilePath);
//		kafkaLoggingConfiguration.setKrbConfFilePath(kerberosFilePath);
//		kafkaLoggingConfiguration.setKafkaBrokerPrimaryList(kafkaBrokerList);
//		kafkaLoggingConfiguration.setKafkaBrokerSecondaryList(kafkaBrokerList);
//		JsonKafkaProducer jsonKafkaProducer= new JsonKafkaProducer<>(kafkaLoggingConfiguration);
//		return jsonKafkaProducer ;
		return null;
	}

}
