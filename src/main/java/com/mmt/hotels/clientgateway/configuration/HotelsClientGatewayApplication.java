package com.mmt.hotels.clientgateway.configuration;

import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.lib.MetricManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Description;
import org.springframework.context.annotation.PropertySource;

@SpringBootApplication(scanBasePackages = {
        "com.mmt.hotels.clientgateway"})
@PropertySource("classpath:application-hotels.properties")
@PropertySource("classpath:droolConfig.properties")
@PropertySource("classpath:availRooms.properties")
@PropertySource("classpath:thankyou.properties")
@PropertySource("classpath:filters.properties")
@PropertySource(value = "file:/opt/clientbackend/application-oth.properties", ignoreResourceNotFound = true)
public class HotelsClientGatewayApplication implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(HotelsClientGatewayApplication.class);

	@Value("${aws.metric}")
	private String awsVersion;

	@Value("${pool.name}")
	private String poolName;

	@Value("${metric.name}")
	private String metricName;

	@Value("${metric.time.in.mins}")
	private int metricTimeInMins;

	@Autowired
	private MetricAspect metricAspect;

	final static long startTime = System.currentTimeMillis();

    public static void main(String[] args) {
    	try {
    		SpringApplication.run(HotelsClientGatewayApplication.class, args);
			System.out.println("-------------------------------------------------------------");
			System.out.println("----------------- CG GI Application Started -----------------");
			System.out.println("-------------------------------------------------------------");
		}catch (Exception ex){
    		logger.error("Exception in startup :{}", ex.getMessage(), ex);
    	}catch(Throwable th){
    		logger.error("Exception in startup :{}", th.getMessage(), th);
    	}

    }

	@Override
	public void run(String ...args){
		long timeTaken = System.currentTimeMillis() - startTime;
		metricAspect.addToTime("ClientGatewayApplicationStartUpTime","", timeTaken);
		logger.warn("Client Gateway StartUp Time {}", timeTaken);
	}

	@Bean(name = "metricManager")
	@Description("Metric MMT Bean")
	public MetricManager getMetricManager() {
		String version=System.getenv(awsVersion);
		return new MetricManager(metricName.concat(poolName),
			metricTimeInMins, true,true,version);

	}
}
