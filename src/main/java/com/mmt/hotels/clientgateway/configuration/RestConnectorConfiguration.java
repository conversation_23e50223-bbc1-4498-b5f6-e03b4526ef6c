package com.mmt.hotels.clientgateway.configuration;

import com.mmt.hotels.clientgateway.enums.RestConfigurationEnums;
import org.apache.http.HttpHost;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.enums.RestConfigurationEnums.*;

@Component
public class RestConnectorConfiguration {
	
	@Value("${http.connection.timeout.search.hotels}")
	private int searchHotelsConnectionTimeout;
	
	@Value("${search.hotels.conn.req.timeout}")
	private int searchHotelsConnectionRequestTimeout;
	
	@Value("${search.hotels.so.timeout}")
	private int searchHotelsSocketTimeout;
	
	@Value("${http.connection.pool.size.search.hotels}")
	private int searchHotelsConnectionPoolSize;

	@Value("${http.connection.timeout.mob.landing}")
	private int mobLandingConnectionTimeout;

	@Value("${mob.landing.conn.req.timeout}")
	private int mobLandingConnectionRequestTimeout;

	@Value("${mob.landing.so.timeout}")
	private int mobLandingSocketTimeout;
	
	@Value("${http.connection.pool.size.mob.landing}")
	private int mobLandingConnectionPoolSize;
	
	@Value("${http.connection.timeout.listing.map}")
	private int listingMapConnectionTimeout;

	@Value("${http.connection.pool.size.listing.map}")
	private int listingMapConnectionPoolSize;

	@Value("${listing.map.conn.req.timeout}")
	private int listingMapConnectionRequestTimeout;

	@Value("${listing.map.so.timeout}")
	private int listingMapSocketTimeout;

	@Value("${http.connection.timeout.near.by}")
	private int nearByConnectionTimeout;

	@Value("${http.connection.pool.size.near.by}")
	private int nearByConnectionPoolSize;

	@Value("${near.by.conn.req.timeout}")
	private int nearByConnectionRequestTimeout;

	@Value("${near.by.so.timeout}")
	private int nearBySocketTimeout;
	
	@Value("${http.connection.timeout.search.rooms}")
	private int searchRoomsConnectionTimeout;
	
	@Value("${search.rooms.conn.req.timeout}")
	private int searchRoomsConnectionRequestTimeout;
	
	@Value("${search.rooms.so.timeout}")
	private int searchRoomsSocketTimeout;
	
	@Value("${http.connection.pool.size.search.rooms}")
	private int searchRoomsConnectionPoolSize;
	
	@Value("${http.connection.timeout.alternate.dates}")
	private int alternateDatesConnectionTimeout;
	
	@Value("${alternate.dates.conn.req.timeout}")
	private int alternateDatesConnectionRequestTimeout;
	
	@Value("${alternate.dates.so.timeout}")
	private int alternateDatesSocketTimeout;
	
	@Value("${http.connection.pool.size.alternate.dates}")
	private int alternateDatesConnectionPoolSize;
	
	@Value("${http.connection.timeout.static.rooms.detail}")
	private int staticRoomsDetailConnectionTimeout;

	@Value("${program.18.conn.req.timeout}")
	private int getProgram18ConnectionRequestTimeout;
	@Value("${ugc.submit.answers.conn.req.timeout}")
	private int ugcSubmitAnswersConnectionRequestTimeout;
	@Value("${ugc.image.upload.conn.req.timeout}")
	private int ugcUploadImagesConnectionRequestTimeout;
	@Value("${get.booking.details.conn.req.timeout}")
	private int getBookingDetailsConnectionRequestTimeout;

	@Value("${http.connection.timeout.program.18}")
	private int getProgram18ConnectionTimeout;
	@Value("${http.connection.timeout.ugc.submit.answers}")
	private int ugcSubmitAnswersConnectionTimeout;
	@Value("${http.connection.timeout.ugc.image.upload}")
	private int ugcUploadImagesConnectionTimeout;
	@Value("${http.connection.timeout.get.booking.details}")
	private int getBookingDetailsConnectionTimeout;

	@Value("${program.18.detail.so.timeout}")
	private int getProgram18ConnectionSocketTimeout;

	@Value("${ugc.submit.answers.detail.so.timeout}")
	private int ugcSubmitAnswersConnectionSocketTimeout;
	@Value("${ugc.image.upload.so.timeout}")
	private int ugcUploadImagesConnectionSocketTimeout;
	@Value("${get.booking.details.so.timeout}")
	private int getBookingDetailsConnectionSocketTimeout;

	@Value("${http.connection.pool.size.program.18}")
	private int getProgram18ConnectionPoolSize;

	@Value("${http.connection.pool.size.ugc.submit.answers}")
	private int ugcSubmitAnswersConnectionPoolSize;

	@Value("${http.connection.pool.size.ugc.image.upload}")
	private int ugcUploadImagesConnectionPoolSize;

	@Value("${http.connection.pool.size.get.booking.details}")
	private int getBookingDetailsConnectionPoolSize;
	
	@Value("${static.rooms.detail.conn.req.timeout}")
	private int staticRoomsDetailConnectionRequestTimeout;
	
	@Value("${static.rooms.detail.so.timeout}")
	private int staticRoomsDetailSocketTimeout;
	
	@Value("${http.connection.pool.size.static.rooms.detail}")
	private int staticRoomsDetailConnectionPoolSize;

	@Value("${http.connection.timeout.txn.data}")
	private int txnDataConnectionTimeout;

	@Value("${txn.data.conn.req.timeout}")
	private int txnDataConnectionRequestTimeout;

	@Value("${txn.data.so.timeout}")
	private int txnDataSocketTimeout;

	@Value("${http.connection.pool.size.txn.data}")
	private int txnDataConnectionPoolSize;

	@Value("${http.connection.timeout.affiliate}")
	private int affiliateConnectionTimeout;

	@Value("${affiliate.conn.req.timeout}")
	private int affiliateConnectionRequestTimeout;

	@Value("${affiliate.so.timeout}")
	private int affiliateSocketTimeout;

	@Value("${http.connection.pool.size.affiliate}")
	private int affiliateConnectionPoolSize;

	@Value("${http.connection.timeout.static.detail}")
	private int staticDetailConnectionTimeout;

	@Value("${static.detail.conn.req.timeout}")
	private int staticDetailConnectionRequestTimeout;
		       
	@Value("${static.detail.so.timeout}")
	private int staticDetailSocketTimeout;
	       
	@Value("${http.connection.pool.size.static.detail}")
	private int staticDetailConnectionPoolSize;
	
	@Value("${http.connection.timeout.updated.price}")
	private int updatedPriceConnectionTimeout;

	@Value("${updated.price.conn.req.timeout}")
	private int updatedPriceConnectionRequestTimeout;

	@Value("${updated.price.so.timeout}")
	private int updatedPriceSocketTimeout;

	@Value("${http.connection.pool.size.updated.price}")
	private int updatedPriceConnectionPoolSize;

	@Value("${http.connection.timeout.avail.rooms}")
	private int availRoomsConnectionTimeout;

	@Value("${avail.rooms.conn.req.timeout}")
	private int availRoomsConnectionRequestTimeout;

	@Value("${avail.rooms.so.timeout}")
	private int availRoomsSocketTimeout;

	@Value("${http.connection.pool.size.avail.rooms}")
	private int availRoomsConnectionPoolSize;
	
	@Value("${http.connection.timeout.updated.price.occu.less}")
	private int updatedPriceOccuLessConnectionTimeout;

	@Value("${updated.price.occu.less.conn.req.timeout}")
	private int updatedPriceOccuLessConnectionRequestTimeout;

	@Value("${updated.price.occu.less.so.timeout}")
	private int updatedPriceOccuLessSocketTimeout;

	@Value("${http.connection.pool.size.updated.price.occu.less}")
	private int updatedPriceOccuLessConnectionPoolSize;
	
	@Value("${http.connection.timeout.pokus.experiment}")
	private int pokusExperimentConnectionTimeout;

	@Value("${pokus.experiment.conn.req.timeout}")
	private int pokusExperimentConnectionRequestTimeout;
		       
	@Value("${pokus.experiment.so.timeout}")
	private int pokusExperimentSocketTimeout;
	       
	@Value("${http.connection.pool.size.pokus.experiment}")
	private int pokusExperimentConnectionPoolSize;

	@Value("${http.connection.timeout.user.service}")
	private int userServiceConnectionTimeout;

	@Value("${user.service.conn.req.timeout}")
	private int userServiceConnectionRequestTimeout;
		       
	@Value("${user.service.so.timeout}")
	private int userServiceSocketTimeout;
	       
	@Value("${http.connection.pool.size.user.service}")
	private int userServiceConnectionPoolSize;
	
	@Value("${http.connection.timeout.user.first.time.state}")
	private int userFirstTimeStateConnectionTimeout;

	@Value("${user.first.time.state.conn.req.timeout}")
	private int userFirstTimeStateConnectionRequestTimeout;
		       
	@Value("${user.first.time.state.so.timeout}")
	private int userFirstTimeStateSocketTimeout;
	       
	@Value("${http.connection.pool.size.user.first.time.state}")
	private int userFirstTimeStateConnectionPoolSize;
	
	@Value("${http.connection.timeout.last.booked.flight}")
	private int lastBookedFlightConnectionTimeout;

	@Value("${last.booked.flight.conn.req.timeout}")
	private int lastBookedFlightConnectionRequestTimeout;
		       
	@Value("${last.booked.flight.so.timeout}")
	private int lastBookedFlightSocketTimeout;
	       
	@Value("${http.connection.pool.size.last.booked.flight}")
	private int lastBookedFlightConnectionPoolSize;
	
	@Value("${http.connection.timeout.bypass}")
	private int byPassConnectionTimeout;

	@Value("${bypass.conn.req.timeout}")
	private int byPassConnectionRequestTimeout;
		       
	@Value("${bypass.so.timeout}")
	private int byPassSocketTimeout;
	       
	@Value("${http.connection.pool.size.bypass}")
	private int byPassConnectionPoolSize;
	
	@Value("${http.connection.timeout.emiDetail}")
	private int emiDetailConnectionTimeout;

	@Value("${emiDetail.conn.req.timeout}")
	private int emiDetailConnectionRequestTimeout;
		       
	@Value("${emiDetail.so.timeout}")
	private int emiDetailSocketTimeout;
	       
	@Value("${http.connection.pool.size.emiDetail}")
	private int emiDetailConnectionPoolSize;
	
	@Value("${http.connection.timeout.totalPrice}")
	private int totalPriceConnectionTimeout;

	@Value("${totalPrice.conn.req.timeout}")
	private int totalPriceConnectionRequestTimeout;
		       
	@Value("${totalPrice.so.timeout}")
	private int totalPriceSocketTimeout;
	       
	@Value("${http.connection.pool.size.totalPrice}")
	private int totalPriceConnectionPoolSize;

	@Value("${http.connection.timeout.payment.checkout}")
	private int paymentCheckoutConnectionTimeout;

	@Value("${payment.checkout.conn.req.timeout}")
	private int paymentCheckoutConnectionRequestTimeout;

	@Value("${payment.checkout.so.timeout}")
	private int paymentCheckoutSocketTimeout;

	@Value("${http.connection.pool.size.payment.checkout}")
	private int paymentCheckoutConnectionPoolSize;

	@Value("${http.connection.timeout.payment.checkout.mod.booking}")
	private int paymentCheckoutModBookingConnectionTimeout;

	@Value("${payment.checkout.mod.booking.conn.req.timeout}")
	private int paymentCheckoutModBookingConnectionRequestTimeout;

	@Value("${payment.checkout.mod.booking.so.timeout}")
	private int paymentCheckoutModBookingSocketTimeout;

	@Value("${http.connection.pool.size.payment.checkout.mod.booking}")
	private int paymentCheckoutModBookingConnectionPoolSize;

	@Value("${http.connection.timeout.fetch.collection}")
	private int fetchCollectionConnTimeout;

	@Value("${http.connection.pool.size.fetch.collection}")
	private int fetchCollectionPoolSize;

	@Value("${fetch.collection.conn.req.timeout}")
	private int fetchCollectionRequestTimeout;

	@Value("${fetch.collection.so.timeout}")
	private int fetchCollectionSocketTimeout;

	@Value("${http.connection.timeout.policies}")
	private int policiesConnectionTimeout;

	@Value("${policies.conn.req.timeout}")
	private int policiesConnectionRequestTimeout;
		       
	@Value("${policies.so.timeout}")
	private int policiesSocketTimeout;
	       
	@Value("${http.connection.pool.size.policies}")
	private int policiesConnectionPoolSize;
	
	@Value("${http.connection.timeout.ltlng}")
	private int ltlngConnectionTimeout;

	@Value("${ltlng.conn.req.timeout}")
	private int ltlngConnectionRequestTimeout;

	@Value("${ltlng.so.timeout}")
	private int ltlngSocketTimeout;

	@Value("${http.connection.pool.size.ltlng}")
	private int ltlngConnectionPoolSize;

	@Value("${http.connection.timeout.addons}")
	private int addonsConnectionTimeout;

	@Value("${addons.conn.req.timeout}")
	private int addonsConnectionRequestTimeout;
		       
	@Value("${addons.so.timeout}")
	private int addonsSocketTimeout;
	       
	@Value("${http.connection.pool.size.addons}")
	private int addonsConnectionPoolSize;
	
	@Value("${http.connection.timeout.corporate.workflow.data}")
	private int workFlowByAuthCodeConnTimeout;

	@Value("${http.connection.pool.size.corporate.workflow.data}")
	private int workFlowByAuthCodePoolSize;

	@Value("${corporate.workflow.data.conn.req.timeout}")
	private int workFlowByAuthCodeRequestTimeout;

	@Value("${corporate.workflow.data.so.timeout}")
	private int workFlowByAuthCodeSocketTimeout;

	@Value("${http.connection.timeout.update.approval}")
	private int updateApprovalConnTimeout;

	@Value("${http.connection.pool.size.update.approval}")
	private int updateApprovalPoolSize;

	@Value("${update.approval.conn.req.timeout}")
	private int updateApprovalRequestTimeout;

	@Value("${update.approval.so.timeout}")
	private int updateApprovalSocketTimeout;

	@Value("${http.connection.timeout.set.approval}")
	private int setApprovalConnTimeout;

	@Value("${http.connection.pool.size.set.approval}")
	private int setApprovalPoolSize;

	@Value("${set.approval.conn.req.timeout}")
	private int setApprovalRequestTimeout;

	@Value("${set.approval.so.timeout}")
	private int setApprovalSocketTimeout;

	@Value("${http.connection.timeout.update.policy}")
	private int updatePolicyConnectionTimeout;

	@Value("${update.policy.conn.req.timeout}")
	private int updatePolicyConnectionRequestTimeout;

	@Value("${update.policy.so.timeout}")
	private int updatePolicySocketTimeout;

	@Value("${http.connection.pool.size.update.policy}")
	private int updatePolicyConnectionPoolSize;

	@Value("${polyglot.connection.timeout}")
	private int polyglotConnectionTimeout;

	@Value("${polyglot.connectionRequest.timeout}")
	private int polyglotConnectionRequestTimeout;

	@Value("${polyglot.socket.timeout}")
	private int polyglotSocketTimeout;

	@Value("${polyglot.connection.pool.size}")
	private int polyglotConnectionPoolSize;

	@Value("${connection.request.timeout.filter.count}")
	private int filterCountConnectionRequestTimeout;

	@Value("${http.connection.timeout.filter.count}")
	private int filterCountConnectionTimeout;

	@Value("${socket.timeout.filter.count}")
	private int filterCountSocketTimeout;

	@Value("${http.connection.min.pool.size.filter.count}")
	private int filterCountConnectionMinPoolSize;

	@Value("${http.connection.max.pool.size.filter.count}")
	private int filterCountConnectionMaxPoolSize;

	@Value("${paylater.connection.timeout}")
	private int paylaterConnectionTimeout;

	@Value("${paylater.connectionRequest.timeout}")
	private int paylaterConnectionRequestTimeout;

	@Value("${paylater.socket.timeout}")
	private int paylaterSocketTimeout;

	@Value("${paylater.connection.pool.size}")
	private int paylaterConnectionPoolSize;

	@Value("${commons.wishlist.hotels.connectionRequest.timeout}")
	private int commonsWishListHotelsConnectionRequestTimeout;

	@Value("${commons.wishlist.hotels.connection.timeout}")
	private int commonsWishListHotelsConnectionTimeout;

	@Value("${commons.wishlist.hotels.socket.timeout}")
	private int commonsWishListHotelsSocketTimeout;

	@Value("${commons.wishlist.hotels.connection.pool.size}")
	private int commonsWishListHotelsConnectionPoolSize;

	@Value("${commons.wishlist.reviews.connectionRequest.timeout}")
	private int commonsWishListReviewsRequestTimeout;

	@Value("${commons.wishlist.reviews.connection.timeout}")
	private int commonsWishListReviewsConnectionTimeout;

	@Value("${commons.wishlist.reviews.socket.timeout}")
	private int commonsWishListReviewsSocketTimeout;

	@Value("${commons.wishlist.reviews.connection.pool.size}")
	private int commonsWishListReviewsConnectionPoolSize;

	@Value("${calendar.availability.connectionRequest.timeout}")
	private int calendarAvailabilityConnectionRequestTimeout;

	@Value("${calendar.availability.connection.timeout}")
	private int calendarAvailabilityConnectionTimeout;

	@Value("${calendar.availability.socket.timeout}")
	private int calendarAvailabilitySocketTimeout;

	@Value("${calendar.availability.connection.pool.size}")
	private int calendarAvailabilityConnectionPoolSize;

	@Value("${data.platform.connectionRequest.timeout}")
	private int dataPlatformConnectionRequestTimeout;

	@Value("${data.platform.connection.timeout}")
	private int dataPlatformConnectionTimeout;

	@Value("${data.platform.socket.timeout}")
	private int dataPlatformSocketTimeout;

	@Value("${data.platform.connection.pool.size}")
	private int dataPlatformConnectionPoolSize;

	@Value("${ugc.fetch.reviews.connectionRequest.timeout}")
	private int ugcFetchReviewsConnectionRequestTimeout;

	@Value("${ugc.fetch.reviews.connection.timeout}")
	private int ugcFetchReviewsConnectionTimeout;

	@Value("${ugc.fetch.reviews.socket.timeout}")
	private int ugcFetchReviewsSocketTimeout;

	@Value("${ugc.fetch.reviews.connection.pool.size}")
	private int ugcFetchReviewsConnectionPoolSize;

	@Value("${ugc.summary.connectionRequest.timeout}")
	private int ugcSummaryConnectionRequestTimeout;

	@Value("${ugc.summary.connection.timeout}")
	private int ugcSummaryConnectionTimeout;

	@Value("${ugc.summary.socket.timeout}")
	private int ugcSummarySocketTimeout;

	@Value("${ugc.summary.connection.pool.size}")
	private int ugcSummaryConnectionPoolSize;

	@Value("${landing.discovery.conn.req.timeout}")
	private int landingDiscoveryConnectionRequestTimeout;

	@Value("${http.connection.timeout.landing.discovery}")
	private int landingDiscoveryConnectionTimeout;

	@Value("${landing.discovery.so.timeout}")
	private int landingDiscoverySocketTimeout;

	@Value("${http.connection.pool.size.landing.discovery}")
	private int landingDiscoveryConnectionPoolSize;

	@Value("${streaks.info.conn.req.timeout}")
	private int streaksInfoConnectionRequestTimeout;

	@Value("${http.connection.timeout.streaks.info}")
	private int streaksInfoConnectionTimeout;

	@Value("${streaks.info.so.timeout}")
	private int streaksInfoSocketTimeout;

	@Value("${http.connection.pool.size.streaks.info}")
	private int streaksInfoConnectionPoolSize;

	@Value("${streaks.balance.conn.req.timeout}")
	private int streaksBalanceConnectionRequestTimeout;

	@Value("${http.connection.timeout.streaks.balance}")
	private int streaksBalanceConnectionTimeout;

	@Value("${streaks.balance.so.timeout}")
	private int streaksBalanceSocketTimeout;

	@Value("${http.connection.pool.size.streaks.balance}")
	private int streaksBalanceConnectionPoolSize;

	@Value("${tagged.media.conn.req.timeout}")
	private int taggedMediaConnectionRequestTimeout;

	@Value("${http.connection.timeout.tagged.media}")
	private int taggedMediaConnectionTimeout;

	@Value("${tagged.media.so.timeout}")
	private int taggedMediaSocketTimeout;

	@Value("${http.connection.pool.size.tagged.media}")
	private int taggedMediaConnectionPoolSize;

	@Value("${media.by.tag.id.conn.req.timeout}")
	private int mediaByTagIdConnectionRequestTimeout;

	@Value("${http.connection.timeout.media.by.tag.id}")
	private int mediaByTagIdConnectionTimeout;

	@Value("${media.by.tag.id.so.timeout}")
	private int mediaByTagIdSocketTimeout;

	@Value("${http.connection.pool.size.media.by.tag.id}")
	private int mediaByTagIdConnectionPoolSize;

	@Value("${hermes.api.detail.price.conn.req.timeout}")
	private int hermesDetailPriceConnReqTimeout;

	@Value("${http.connection.timeout.hermes.detail.price}")
	private int hermesDetailPriceConnTimeout;

	@Value("${hermes.detail.price.so.timeout}")
	private int hermesDetailPriceSocketTimeout;

	@Value("${http.connection.pool.size.hermes.detail.price}")
	private int hermesDetailPricePoolSize;

	@Value("${http.connection.timeout.offer.details}")
	private int offerDetailsConnectionTimeout;

	@Value("${offer.details.conn.req.timeout}")
	private int offerDetailsConnectionRequestTimeout;

	@Value("${offer.details.so.timeout}")
	private int offerDetailsSocketTimeout;

	@Value("${http.connection.pool.size.offer.details}")
	private int offerDetailsConnectionPoolSize;

	@Value("${orch.static.details.conn.req.timeout}")
	private int orchStaticDetailsConnectionRequestTimeout;

	@Value("${http.connection.timeout.orch.static.details}")
	private int orchStaticDetailsConnectionTimeout;

	@Value("${orch.static.details.so.timeout}")
	private int orchStaticDetailsSocketTimeout;

	@Value("${http.connection.pool.size.orch.static.details}")
	private int orchStaticDetailsConnectionPoolSize;

	@Bean
	public Map<RestConfigurationEnums, HttpClient> getConnectorMap() {
		Map<RestConfigurationEnums, HttpClient> connectorMap = new HashMap<>();
		connectorMap.put(SEARCH_HOTELS_REST_CONNECTOR, getSearchHotelsClient());
		connectorMap.put(MOB_LANDING_REST_CONNECTOR, getMobLandingClient());
		connectorMap.put(FILTER_COUNT_REST_CONNECTOR, getFilterCountClient());
		connectorMap.put(LISTING_MAP_REST_CONNECTOR, getListingMapClient());
		connectorMap.put(SEARCH_ROOMS_REST_CONNECTOR, getSearchRoomsClient());
		connectorMap.put(STATIC_ROOMS_REST_CONNECTOR, getStaticRoomsDetailClient());
		connectorMap.put(STATIC_DETAIL_REST_CONNECTOR, getStaticDetailClient());
		connectorMap.put(UPDATED_PRICE_REST_CONNECTOR, getUpdatedPriceClient());
		connectorMap.put(AVAIL_ROOMS_REST_CONNECTOR, getAvailRoomsClient());
		connectorMap.put(POKUS_EXPERIMENT_CONNECTOR, getPokusClient());
		connectorMap.put(USER_SERVICE_CONNECTOR, getUserServiceClient());
		connectorMap.put(USER_FIRST_TIME_STATE_CONNECTOR, getUserFirstTimeStateClient());
		connectorMap.put(LAST_BOOKED_FLIGHT_CONNECTOR, getLastBookedFlightClient());
		connectorMap.put(OFFER_DETAILS_REST_CONNECTOR, getOfferDetailsClient());
		connectorMap.put(BYPASS_CONNECTOR, getByPassConnector());
		connectorMap.put(PAYMENT_CHECKOUT_REST_CONNECTOR, getPaymentCheckoutClient());
		connectorMap.put(PAYMENT_CHECKOUT_REST_CONNECTOR_MOD_BOOKING, getPaymentCheckoutModBookingClient());
		connectorMap.put(FETCH_COLLECTION,getFetchCollectionClient());
		connectorMap.put(EMI_DETAILS_CONNECTOR, getEmiDetailConnector());
		connectorMap.put(TOTAL_PRICE_CONNECTOR, getTotalPriceConnector());
		connectorMap.put(POLICIES_API_CONNECTOR, getPoliciesAPIConnector());
		connectorMap.put(AFFILIATE_CONNECTOR, getAffiliateConnector());
		connectorMap.put(CORPORATE_WORKFLOW_DATA_CONNECTOR, getWorkflowDataClient());
		connectorMap.put(CORP_UPDATE_APPROVAL_CONNECTOR, getUpdateApprovalClient());
		connectorMap.put(CORP_WORKFLOW_INFO_CONNECTOR, getWorkFlowInfoAuthCodeClient());
		connectorMap.put(CORP_APPROVALS_INFO_CONNECTOR, getApprovalsInfoClient());
		connectorMap.put(PLACE_MAP_TO_MMT_API, getLatLngPlacesAPIConnector());
		connectorMap.put(THANKYOU_REST_CONNECTOR, getThankYouClient());
		connectorMap.put(SEARCH_ADDONS_CONNECTOR, getAddonsConnector());
		connectorMap.put(NEAR_BY_REST_CONNECTOR, getNearByHotelsAPIConnector());
		connectorMap.put(GET_UPDATED_PRICE_OCCU_LESS_REST_CONNECTOR, getUpdatedPriceOccuLessClient());
		connectorMap.put(ALTERNATE_DATES_REST_CONNECTOR, getAlternateDatesClient());
		connectorMap.put(CORPORATE_UPDATE_POLICY_CONNECTOR, getUpdatePolicyClient());
		connectorMap.put(POLYGLOT_REST_CONNECTOR, getPolyglotClient());
		connectorMap.put(PAY_LATER_CONNECTOR,getPayLaterHESClient());
		connectorMap.put(COMMON_DATELESS_WISHLISTS_HOTELS_CONNECTOR, getCommonsWishListHotelsClient());
		connectorMap.put(COMMON_DATELESS_WISHLISTS_REVIEW_CONNECTOR, getCommonsWishListReviewsClient());
		connectorMap.put(CALENDAR_AVAILABILITY_CONNECTOR, getCalendarAvailabilityClient());
		connectorMap.put(LANDING_DISCOVERY_REST_CONNECTOR, getLandingDiscoveryClient());
		connectorMap.put(TAGGED_MEDIA_CONNECTOR, getTaggedMediaClient());
		connectorMap.put(MEDIA_BY_TAG_ID_CONNECTOR, getMediaByTagIdClient());
		connectorMap.put(HERMES_DETAIL_PRICE_CONNECTOR, getHermesDetailPriceClient());
		connectorMap.put(STREAKS_USER_INFO_CONNECTOR,getUserStreaksUserInfoClient());
		connectorMap.put(STREAKS_USER_BALANCE_CONNECTOR,getUserStreaksUserBalanceClient());
		connectorMap.put(DATA_PLATFORM_CONNECTOR, getDataPlatformClient());
		connectorMap.put(UGC_REVIEW_CONNECTOR, getFetchUgcReviewsClient());
		connectorMap.put(ORCH_STATIC_DETAILS_CONNECTOR, getOrchStaticDetailsClient());
		connectorMap.put(UGC_SUMMARY_CONNECTOR, getFetchUgcSummaryClient());
		connectorMap.put(HOTEL_IMAGE, getFetchHotelClient());
		connectorMap.put(GET_BOOKING_DETAILS_CONNECTOR, getBookingDetailsClient());
		connectorMap.put(PROGRAM_18_CONNECTOR, getProgram18Client());
		connectorMap.put(SUBMIT_ANSWERS_CONNTECTOR, getUgcSubmitAnswersClient());
		connectorMap.put(UPLOAD_IMAGES_CONNECTOR, getUgcUploadImagesClient());
		connectorMap.put(ORCH_SEARCH_HOTELS_CONNECTOR, getOrchSearchHotelsClient());
		connectorMap.put(ORCH_SEARCH_ROOMS_CONNECTOR,getOrchSearchRoomsClient());
		return connectorMap;
	}

	private HttpClient getHermesDetailPriceClient() {
		return getHttpClientWithConfiguration(hermesDetailPriceConnReqTimeout, hermesDetailPriceConnTimeout,
				hermesDetailPriceSocketTimeout, hermesDetailPricePoolSize, hermesDetailPricePoolSize);
	}

	private HttpClient getByPassConnector() {
		return getHttpClientWithConfiguration(byPassConnectionRequestTimeout, byPassConnectionTimeout,
				byPassSocketTimeout, byPassConnectionPoolSize, byPassConnectionPoolSize);
	}
	
	private HttpClient getEmiDetailConnector() {
		return getHttpClientWithConfiguration(emiDetailConnectionRequestTimeout, emiDetailConnectionTimeout,
				emiDetailSocketTimeout, emiDetailConnectionPoolSize, emiDetailConnectionPoolSize);
	}
	
	private HttpClient getTotalPriceConnector() {
		return getHttpClientWithConfiguration(totalPriceConnectionRequestTimeout, totalPriceConnectionTimeout,
				totalPriceSocketTimeout, totalPriceConnectionPoolSize, totalPriceConnectionPoolSize);
	}

	private HttpClient getLastBookedFlightClient() {
		return getHttpClientWithConfiguration(lastBookedFlightConnectionRequestTimeout, lastBookedFlightConnectionTimeout,
				lastBookedFlightSocketTimeout, lastBookedFlightConnectionPoolSize, lastBookedFlightConnectionPoolSize);
	}

	private HttpClient getOfferDetailsClient() {
		return getHttpClientWithConfiguration(offerDetailsConnectionRequestTimeout, offerDetailsConnectionTimeout,
				offerDetailsSocketTimeout, offerDetailsConnectionPoolSize, offerDetailsConnectionPoolSize);
	}

	private HttpClient getUserFirstTimeStateClient() {
		return getHttpClientWithConfiguration(userFirstTimeStateConnectionRequestTimeout, userFirstTimeStateConnectionTimeout,
				userFirstTimeStateSocketTimeout, userFirstTimeStateConnectionPoolSize, userFirstTimeStateConnectionPoolSize);
	}

	private HttpClient getUserServiceClient() {
		return getHttpClientWithConfiguration(userServiceConnectionRequestTimeout, userServiceConnectionTimeout,
				userServiceSocketTimeout, userServiceConnectionPoolSize, userServiceConnectionPoolSize);
	}

	private HttpClient getPokusClient() {
		return getHttpClientWithConfiguration(pokusExperimentConnectionRequestTimeout, pokusExperimentConnectionTimeout,
				pokusExperimentSocketTimeout, pokusExperimentConnectionPoolSize, pokusExperimentConnectionPoolSize);
	}

	private HttpClient getStaticRoomsDetailClient() {
		return getHttpClientWithConfiguration(staticRoomsDetailConnectionRequestTimeout, staticRoomsDetailConnectionTimeout,
				staticRoomsDetailSocketTimeout, staticRoomsDetailConnectionPoolSize, staticRoomsDetailConnectionPoolSize);
	}

	private HttpClient getThankYouClient() {
		return getHttpClientWithConfiguration(txnDataConnectionRequestTimeout, txnDataConnectionTimeout,
				txnDataSocketTimeout, txnDataConnectionPoolSize, txnDataConnectionPoolSize);
	}

	private HttpClient getAffiliateConnector() {
		return getHttpClientWithConfiguration(affiliateConnectionRequestTimeout, affiliateConnectionTimeout,
				affiliateSocketTimeout, affiliateConnectionPoolSize, affiliateConnectionPoolSize);
	}
	private HttpClient getAvailRoomsClient() {
		return getHttpClientWithConfiguration(availRoomsConnectionRequestTimeout, availRoomsConnectionTimeout,
				availRoomsSocketTimeout, availRoomsConnectionPoolSize, availRoomsConnectionPoolSize);
	}
	
	private HttpClient getUpdatedPriceOccuLessClient() {
		return getHttpClientWithConfiguration(updatedPriceOccuLessConnectionRequestTimeout, updatedPriceOccuLessConnectionTimeout,
				updatedPriceOccuLessSocketTimeout, updatedPriceOccuLessConnectionPoolSize, updatedPriceOccuLessConnectionPoolSize);
	}

	private HttpClient getUpdatedPriceClient() {
		return getHttpClientWithConfiguration(updatedPriceConnectionRequestTimeout, updatedPriceConnectionTimeout,
				updatedPriceSocketTimeout, updatedPriceConnectionPoolSize, updatedPriceConnectionPoolSize);
	}
	
	private HttpClient getStaticDetailClient() {
		return getHttpClientWithConfiguration(staticDetailConnectionRequestTimeout, staticDetailConnectionTimeout,
				staticDetailSocketTimeout, staticDetailConnectionPoolSize, staticDetailConnectionPoolSize);
	}

	private HttpClient getListingMapClient() {
		return getHttpClientWithConfiguration(listingMapConnectionRequestTimeout, listingMapConnectionTimeout,
				listingMapSocketTimeout, listingMapConnectionPoolSize, listingMapConnectionPoolSize);
	}

	private HttpClient getMobLandingClient() {
		return getHttpClientWithConfiguration(mobLandingConnectionRequestTimeout, mobLandingConnectionTimeout,
				mobLandingSocketTimeout, mobLandingConnectionPoolSize, mobLandingConnectionPoolSize);
	}
	
	private HttpClient getSearchRoomsClient() {
		return getHttpClientWithConfiguration(searchRoomsConnectionRequestTimeout, searchRoomsConnectionTimeout,
				searchRoomsSocketTimeout, searchRoomsConnectionPoolSize, searchRoomsConnectionPoolSize);
	}
	
	private HttpClient getAlternateDatesClient() {
		return getHttpClientWithConfiguration(alternateDatesConnectionRequestTimeout, alternateDatesConnectionTimeout,
				alternateDatesSocketTimeout, alternateDatesConnectionPoolSize, alternateDatesConnectionPoolSize);
	}

	private HttpClient getSearchHotelsClient() {
		return getHttpClientWithConfiguration(searchHotelsConnectionRequestTimeout, searchHotelsConnectionTimeout,
				searchHotelsSocketTimeout, searchHotelsConnectionPoolSize, searchHotelsConnectionPoolSize);
	}

	private HttpClient getFilterCountClient() {
		return getHttpClientWithConfiguration(filterCountConnectionRequestTimeout, filterCountConnectionTimeout,
											  filterCountSocketTimeout, filterCountConnectionMinPoolSize, filterCountConnectionMaxPoolSize);
	}


	private HttpClient getPaymentCheckoutClient() {
		return getHttpClientWithConfiguration(paymentCheckoutConnectionRequestTimeout, paymentCheckoutConnectionTimeout,
				paymentCheckoutSocketTimeout, paymentCheckoutConnectionPoolSize, paymentCheckoutConnectionPoolSize);
	}

	private HttpClient getPaymentCheckoutModBookingClient() {
		return getHttpClientWithConfiguration(paymentCheckoutModBookingConnectionRequestTimeout, paymentCheckoutModBookingConnectionTimeout,
				paymentCheckoutModBookingSocketTimeout, paymentCheckoutModBookingConnectionPoolSize, paymentCheckoutModBookingConnectionPoolSize);
	}

	private HttpClient getFetchCollectionClient() {
		return getHttpClientWithConfiguration(fetchCollectionRequestTimeout, fetchCollectionConnTimeout,
				fetchCollectionSocketTimeout, fetchCollectionPoolSize, fetchCollectionPoolSize);
	}

	private HttpClient getPoliciesAPIConnector() {
		return getHttpClientWithConfiguration(policiesConnectionRequestTimeout, policiesConnectionTimeout,
				policiesSocketTimeout, policiesConnectionPoolSize, policiesConnectionPoolSize);
	}
	
	private HttpClient getWorkflowDataClient() {
		return getHttpClientWithConfiguration(workFlowByAuthCodeRequestTimeout, workFlowByAuthCodeConnTimeout,
				workFlowByAuthCodeSocketTimeout, workFlowByAuthCodePoolSize, workFlowByAuthCodePoolSize);
	}

	private HttpClient getUpdateApprovalClient() {
		return getHttpClientWithConfiguration(updateApprovalRequestTimeout, updateApprovalConnTimeout,
				updateApprovalSocketTimeout, updateApprovalPoolSize, updateApprovalPoolSize);
	}

	private HttpClient getWorkFlowInfoAuthCodeClient() {
		return getHttpClientWithConfiguration(setApprovalRequestTimeout, setApprovalConnTimeout,
				setApprovalSocketTimeout, setApprovalPoolSize, setApprovalPoolSize);
	}

	private HttpClient getApprovalsInfoClient() {
		return getHttpClientWithConfiguration(setApprovalRequestTimeout, setApprovalConnTimeout,
				setApprovalSocketTimeout, setApprovalPoolSize, setApprovalPoolSize);
	}

	private HttpClient getLatLngPlacesAPIConnector() {
		return getHttpClientWithConfiguration(ltlngConnectionRequestTimeout, ltlngConnectionTimeout,
				ltlngSocketTimeout, ltlngConnectionPoolSize, ltlngConnectionPoolSize);
	}
	
	private HttpClient getAddonsConnector() {
		return getHttpClientWithConfiguration(addonsConnectionRequestTimeout, addonsConnectionTimeout,
				addonsSocketTimeout, addonsConnectionPoolSize, addonsConnectionPoolSize);
	}

	private HttpClient getUpdatePolicyClient() {
		return getHttpClientWithConfiguration(updatePolicyConnectionRequestTimeout, updatePolicyConnectionTimeout,
				updatePolicySocketTimeout, updatePolicyConnectionPoolSize, updatePolicyConnectionPoolSize);
	}
	private HttpClient getNearByHotelsAPIConnector() {
		return getHttpClientWithConfiguration(nearByConnectionRequestTimeout, nearByConnectionTimeout,
				nearBySocketTimeout, nearByConnectionPoolSize, nearByConnectionPoolSize);
	}

	private HttpClient getPolyglotClient() {
		return getHttpClientWithConfiguration(polyglotConnectionRequestTimeout, polyglotConnectionTimeout,
				polyglotSocketTimeout, polyglotConnectionPoolSize, polyglotConnectionPoolSize);
	}

	private HttpClient getPayLaterHESClient() {
		return getHttpClientWithConfiguration(paylaterConnectionRequestTimeout, paylaterConnectionTimeout,
				paylaterSocketTimeout, paylaterConnectionPoolSize, paylaterConnectionPoolSize);
	}

	private HttpClient getCommonsWishListHotelsClient() {
		return getHttpClientWithConfiguration(commonsWishListHotelsConnectionRequestTimeout, commonsWishListHotelsConnectionTimeout,
				commonsWishListHotelsSocketTimeout, commonsWishListHotelsConnectionPoolSize, commonsWishListHotelsConnectionPoolSize);
	}

	private HttpClient getCommonsWishListReviewsClient() {
		return getHttpClientWithConfiguration(commonsWishListReviewsRequestTimeout, commonsWishListReviewsConnectionTimeout,
				commonsWishListReviewsSocketTimeout, commonsWishListReviewsConnectionPoolSize, commonsWishListReviewsConnectionPoolSize);
	}

	private HttpClient getCalendarAvailabilityClient() {
		return getHttpClientWithConfiguration(calendarAvailabilityConnectionRequestTimeout, calendarAvailabilityConnectionTimeout,
				calendarAvailabilitySocketTimeout, calendarAvailabilityConnectionPoolSize, calendarAvailabilityConnectionPoolSize);
	}

	private HttpClient getDataPlatformClient() {
		return getHttpClientWithConfiguration(dataPlatformConnectionRequestTimeout, dataPlatformConnectionTimeout,
				dataPlatformSocketTimeout, dataPlatformConnectionPoolSize, dataPlatformConnectionPoolSize);
	}

	private HttpClient getFetchUgcReviewsClient() {
		return getHttpClientWithConfiguration(ugcFetchReviewsConnectionRequestTimeout, ugcFetchReviewsConnectionTimeout,
				ugcFetchReviewsSocketTimeout, ugcFetchReviewsConnectionPoolSize, ugcFetchReviewsConnectionPoolSize);
	}

	private HttpClient getFetchUgcSummaryClient() {
		return getHttpClientWithConfiguration(ugcSummaryConnectionRequestTimeout, ugcSummaryConnectionTimeout,
				ugcSummarySocketTimeout, ugcSummaryConnectionPoolSize, ugcSummaryConnectionPoolSize);
	}
	private HttpClient getFetchHotelClient() {
		return getHttpClientWithConfiguration(ugcSummaryConnectionRequestTimeout, ugcSummaryConnectionTimeout,
				ugcSummarySocketTimeout, ugcSummaryConnectionPoolSize, ugcSummaryConnectionPoolSize);
	}

	private HttpClient getLandingDiscoveryClient() {
		return getHttpClientWithConfiguration(landingDiscoveryConnectionRequestTimeout, landingDiscoveryConnectionTimeout,
				landingDiscoverySocketTimeout, landingDiscoveryConnectionPoolSize, landingDiscoveryConnectionPoolSize);
	}

	private HttpClient getTaggedMediaClient() {
		return getHttpClientWithConfiguration(taggedMediaConnectionRequestTimeout, taggedMediaConnectionTimeout,
				taggedMediaSocketTimeout, taggedMediaConnectionPoolSize, taggedMediaConnectionPoolSize);
	}

	private HttpClient getMediaByTagIdClient() {
		return getHttpClientWithConfiguration(mediaByTagIdConnectionRequestTimeout, mediaByTagIdConnectionTimeout,
				mediaByTagIdSocketTimeout, mediaByTagIdConnectionPoolSize, mediaByTagIdConnectionPoolSize);
	}

	private HttpClient getBabelfishClient(){
		return  getHttpClientWithConfiguration(streaksInfoConnectionRequestTimeout, streaksInfoConnectionTimeout,
				streaksInfoSocketTimeout, streaksInfoConnectionPoolSize, streaksInfoConnectionPoolSize);
	}

	private HttpClient getUserStreaksUserInfoClient(){
		return  getHttpClientWithConfiguration(streaksInfoConnectionRequestTimeout, streaksInfoConnectionTimeout,
				streaksInfoSocketTimeout, streaksInfoConnectionPoolSize, streaksInfoConnectionPoolSize);
	}
	private HttpClient getUserStreaksUserBalanceClient(){
		return  getHttpClientWithConfiguration(streaksBalanceConnectionRequestTimeout, streaksBalanceConnectionTimeout,
				streaksBalanceSocketTimeout, streaksBalanceConnectionPoolSize, streaksBalanceConnectionPoolSize);
	}

	private HttpClient getHttpClientWithConfiguration(int connectionRequestTimeout, int connectionTimeout,
			  int socketTimeout, int maxPerROute, int maxTotal){
		RequestConfig config = getRequestConfig(connectionRequestTimeout, connectionTimeout, socketTimeout);
		PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
		connManager.setDefaultMaxPerRoute(maxPerROute);
		connManager.setMaxTotal(maxTotal);
		//return HttpClients.custom().setDefaultRequestConfig(config).setConnectionManager(connManager).build();

		HttpHost proxy = new HttpHost("127.0.0.1", 8888); // Charles Proxy
		return HttpClients.custom()
				.setProxy(proxy)
				.setDefaultRequestConfig(config)
				.setConnectionManager(connManager)
				.build();
	}

	private RequestConfig getRequestConfig(int connectionRequestTimeout, int connectionTimeout, int socketTimeout) {
		return RequestConfig.custom().setConnectionRequestTimeout(connectionRequestTimeout)
                    .setConnectTimeout(connectionTimeout).setSocketTimeout(socketTimeout).build();
	}

	private HttpClient getBookingDetailsClient() {
		return getHttpClientWithConfiguration(getBookingDetailsConnectionRequestTimeout, getBookingDetailsConnectionTimeout,
				getBookingDetailsConnectionSocketTimeout, getBookingDetailsConnectionPoolSize, getBookingDetailsConnectionPoolSize);
	}

	private HttpClient getProgram18Client() {
		return getHttpClientWithConfiguration(getProgram18ConnectionRequestTimeout, getProgram18ConnectionTimeout,
				getProgram18ConnectionSocketTimeout, getProgram18ConnectionPoolSize, getProgram18ConnectionPoolSize);
	}

	private HttpClient getUgcSubmitAnswersClient() {
		return getHttpClientWithConfiguration(ugcSubmitAnswersConnectionRequestTimeout, ugcSubmitAnswersConnectionTimeout,
				ugcSubmitAnswersConnectionSocketTimeout, ugcSubmitAnswersConnectionPoolSize, ugcSubmitAnswersConnectionPoolSize);
	}

	private HttpClient getUgcUploadImagesClient() {
		return getHttpClientWithConfiguration(ugcUploadImagesConnectionRequestTimeout, staticRoomsDetailConnectionTimeout,
				ugcUploadImagesConnectionSocketTimeout, ugcUploadImagesConnectionPoolSize, ugcUploadImagesConnectionPoolSize);
	}


	private HttpClient getOrchSearchHotelsClient() {
		//Todo Configure for Orch
		return getHttpClientWithConfiguration(searchHotelsConnectionRequestTimeout, searchHotelsConnectionTimeout,
				searchHotelsSocketTimeout, searchHotelsConnectionPoolSize, searchHotelsConnectionPoolSize);
	}

	private HttpClient getOrchSearchRoomsClient() {
		return getHttpClientWithConfiguration(searchRoomsConnectionRequestTimeout, searchRoomsConnectionTimeout,
				searchRoomsSocketTimeout, searchRoomsConnectionPoolSize, searchRoomsConnectionPoolSize);

	}

	private HttpClient getOrchStaticDetailsClient() {
		return getHttpClientWithConfiguration(
				orchStaticDetailsConnectionRequestTimeout,
				orchStaticDetailsConnectionTimeout,
				orchStaticDetailsSocketTimeout,
				orchStaticDetailsConnectionPoolSize,
				orchStaticDetailsConnectionPoolSize
		);
	}



}
