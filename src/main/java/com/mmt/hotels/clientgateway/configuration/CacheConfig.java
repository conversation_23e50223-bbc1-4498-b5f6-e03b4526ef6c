package com.mmt.hotels.clientgateway.configuration;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.math.Stats;
import com.mmt.hotels.clientgateway.constants.Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@EnableCaching
@Configuration
@PropertySource("classpath:cache.properties")
public class CacheConfig {

    @Value("${cache.expiry.time.minutes:60}")
    private long cacheExpiryTime;

    @Value("${cache.expiry.max.size:1000}")
    private long maximumSize;


    @Bean
    public Caffeine<Object, Object> caffeineConfig() {
        return Caffeine.newBuilder()
                .expireAfterAccess(cacheExpiryTime, TimeUnit.MINUTES)
                .maximumSize(maximumSize)
                .recordStats();
    }

    @Bean
    public CacheManager cacheManager(Caffeine<Object, Object> caffeine) {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCacheNames(Arrays.asList(Constants.TRANSLATION_CACHE, Constants.MOBGEN_CACHE));
        caffeineCacheManager.setCaffeine(caffeine);
        return caffeineCacheManager;
    }
}