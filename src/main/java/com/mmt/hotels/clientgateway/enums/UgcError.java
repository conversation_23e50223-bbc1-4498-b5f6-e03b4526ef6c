package com.mmt.hotels.clientgateway.enums;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum UgcError {
    BOOKING_ID_NOT_FOUND("60601",ConstantsTranslation.BOOKING_NOT_FOUND_MSG,ConstantsTranslation.BOOKING_NOT_FOUND_TITLE ),
    PROGRAM_LOADING_ISSUE("60602",ConstantsTranslation.PROGRAM_LOADING_ISSUE_MSG,ConstantsTranslation.PROGRAM_LOADING_ISSUE_TITLE),
    NO_DATA_FOUND_FOR_UUID("60603",ConstantsTranslation.NO_DATA_FOUND_FOR_UUID,ConstantsTranslation.NO_DATA_FOUND_FOR_UUID_TITLE),
    GENERIC_ERROR("60604",ConstantsTranslation.QUESTION_GENERIC_ERROR_MSG ,ConstantsTranslation.QUESTION_GENERIC_ERROR_TITLE );
    private String errorCode;
    private String errorMsg;
    private String title;

    public static UgcError resolve(String errorCode) {
        if (StringUtils.isBlank(errorCode))
            return null;
        Optional<UgcError> optional = Arrays.stream(UgcError.values()).filter(e->(e.getErrorCode().equalsIgnoreCase(errorCode))).findAny();
        return optional.isPresent() ? optional.get() : null;
    }

    private UgcError(String errorCode, String message, String title){
        this.errorCode = errorCode;
        this.errorMsg = message;
        this.title = title;
    }
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getTitle() {
        return title;
    }
    public void setTitle(String title) {
        this.title = title;
    }
}
