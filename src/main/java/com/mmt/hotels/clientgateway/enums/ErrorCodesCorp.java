package com.mmt.hotels.clientgateway.enums;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum ErrorCodesCorp {
    ERROR_PRICE_CHANGE_CODE_116("116"),
    ERROR_PRICE_CHANGE_CODE_115("115"),
    ERROR_SOLD_OUT_CODE_117("117"),
    ERROR_SOLD_OUT_CODE_166("166"),
    APPROVAL_REQUEST_EXPIRY_CODE("7201"),
    ROOM_NOT_AVAILABLE_ERROR_CODE("PBK_E119"),
    TXN_KEY_EXPIRED("413405");

    private String errorCode;

    ErrorCodesCorp(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public static boolean resolve(String errorCode) {
        if (StringUtils.isBlank(errorCode))
            return false;
        Optional<ErrorCodesCorp> optional = Arrays.stream(ErrorCodesCorp.values()).filter(e->(e.getErrorCode().equalsIgnoreCase(errorCode))).findAny();
        return optional.isPresent();
    }
}
