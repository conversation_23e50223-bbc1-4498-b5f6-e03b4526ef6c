package com.mmt.hotels.clientgateway.enums;

public enum UnexpectedErrors {
	
	INTERNAL_SERVER_ERROR("00", "Internal server error"),
	INTERNAL_ERROR("01", "Internal error");
	
	private String errorCode;
	private String errorMsg;
	
	private UnexpectedErrors(String errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
}
