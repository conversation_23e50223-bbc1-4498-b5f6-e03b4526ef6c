package com.mmt.hotels.clientgateway.enums;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum SpecifiedErrorsRequestor {
    CHECKOUT_ERROR_PRICE_CHANGE_CODE_116("116", ConstantsTranslation.CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY, ConstantsTranslation.CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY),
    CHECKOUT_ERROR_PRICE_CHANGE_CODE_115("115", ConstantsTranslation.CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY, ConstantsTranslation.CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY),
    CHECKOUT_ERROR_SOLD_OUT_CODE_117("117", ConstantsTranslation.CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY, ConstantsTranslation.CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY),
    CHECKOUT_ERROR_SOLD_OUT_CODE_166("166", ConstantsTranslation.CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY, ConstantsTranslation.CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY),
    CORP_APPROVAL_REQUEST_EXPIRY_CODE("7201", ConstantsTranslation.CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE, ConstantsTranslation.CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE),
    CORP_ROOM_NOT_AVAILABLE_ERROR_CODE("PBK_E119", ConstantsTranslation.CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE, ConstantsTranslation.CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE),
    CORP_TXT_KEY_EXPIRED_CODE("413405", ConstantsTranslation.CORP_TXT_KEY_EXPIRED_CODE_TITLE, ConstantsTranslation.CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE);

    private String errorCode;

    private String title;

    private String subTitle;

    private SpecifiedErrorsRequestor(String errorCode, String title, String subTitle){
        this.errorCode = errorCode;
        this.title = title;
        this.subTitle = subTitle;
    }

    public static SpecifiedErrorsRequestor resolve(String errorCode) {
        if (StringUtils.isBlank(errorCode))
            return null;
        Optional<SpecifiedErrorsRequestor> optional = Arrays.stream(SpecifiedErrorsRequestor.values()).filter(e->(e.getErrorCode().equalsIgnoreCase(errorCode))).findAny();
        return optional.isPresent() ? optional.get() : null;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

}
