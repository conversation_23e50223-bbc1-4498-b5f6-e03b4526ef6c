package com.mmt.hotels.clientgateway.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum Brand {

    MMT("MMT"),
    GI("GI"),
    REDBUS("REDBUS"),
    UNKNOWN("UNKNOWN");
    private final String name;
    private static final Map<String, Brand> map = new HashMap<>();
    static {
        for (Brand type : Brand.values()) {
            map.put(type.name.toLowerCase(), type);
        }
    }

    Brand(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public static Brand fromString(String brand) {
        if ( StringUtils.isEmpty(brand) )
            return Brand.UNKNOWN;
        Brand status = map.get(brand.toLowerCase());
        if (status == null)
            return Brand.UNKNOWN;
        return status;
    }
}