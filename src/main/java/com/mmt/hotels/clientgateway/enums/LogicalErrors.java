package com.mmt.hotels.clientgateway.enums;

public enum LogicalErrors {
    UUID_SCRAMBLER("00","Error occurred while Scrambling communication details"),
    NO_COMMON_RATE_PLAN("01", "No common rate plan between hermes and HES");

    private String errorCode;
    private String errorMsg;

    private LogicalErrors(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
