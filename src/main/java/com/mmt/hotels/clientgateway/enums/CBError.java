package com.mmt.hotels.clientgateway.enums;


@Deprecated   /*please don't add any error code of CG in this, This is only to provide backward compatible error codes*/
public enum CBError {

    GENERIC_ERROR("3409", "There is a problem in processing your request"),
    UNAUTHORIZED_USER("3401", "Unauthorized User"),
    SOCKET_TIMEO_OUT_ERROR("3408", "There is a problem in connectivity with downstream"),
    BAD_REQUEST("3400", "Bad Request"),
    HOTEL_UPSELL_API_ERROR("3704", "Upsell API response is invalid"),
    NO_DATA_FOUND_THANKYOU("3705", "No data received from COUCHBASE API"),
    NO_DATA_FOUND("3430","No data found for altacco"),
    NO_COLLECTION_DATA_FROM_CG("53", "No Collection Data from CG");


    private final String code;
    private final String description;

    CBError(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String toString() {
        return code + ": " + description;
    }
}