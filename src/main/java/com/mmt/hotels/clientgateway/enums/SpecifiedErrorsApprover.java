package com.mmt.hotels.clientgateway.enums;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum SpecifiedErrorsApprover {
    APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_116("116", ConstantsTranslation.APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY, ConstantsTranslation.APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY),
    APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_115("115", ConstantsTranslation.APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY, ConstantsTranslation.APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY),
    APP_CHECKOUT_ERROR_SOLD_OUT_CODE_117("117", ConstantsTranslation.APP_CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY, ConstantsTranslation.APP_CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY),
    APP_CHECKOUT_ERROR_SOLD_OUT_CODE_166("166", ConstantsTranslation.APP_CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY, ConstantsTranslation.APP_CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY),
    APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE("7201", ConstantsTranslation.APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE, ConstantsTranslation.APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE),
    APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE("PBK_E119", ConstantsTranslation.APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE, ConstantsTranslation.APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE),
    APP_CORP_TXT_KEY_EXPIRED_CODE("413405", ConstantsTranslation.APP_CORP_TXT_KEY_EXPIRED_CODE_TITLE, ConstantsTranslation.APP_CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE);

    private String errorCode;

    private String title;

    private String subTitle;

    private SpecifiedErrorsApprover(String errorCode, String title, String subTitle){
        this.errorCode = errorCode;
        this.title = title;
        this.subTitle = subTitle;
    }

    public static SpecifiedErrorsApprover resolve(String errorCode) {
        if (StringUtils.isBlank(errorCode))
            return null;
        Optional<SpecifiedErrorsApprover> optional = Arrays.stream(SpecifiedErrorsApprover.values()).filter(e->(e.getErrorCode().equalsIgnoreCase(errorCode))).findAny();
        return optional.isPresent() ? optional.get() : null;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

}
