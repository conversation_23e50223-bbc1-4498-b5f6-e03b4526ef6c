package com.mmt.hotels.clientgateway.enums;

public enum ConnectivityErrors {
	
	IO_ERROR("00", "input/output failed during api call"),
	REST_ERROR("01", "rest error occured during api call");
	
	private String errorCode;
	private String errorMsg;
	
	private ConnectivityErrors(String errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
}
