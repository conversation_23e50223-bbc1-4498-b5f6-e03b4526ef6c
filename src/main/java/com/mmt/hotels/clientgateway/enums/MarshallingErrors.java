package com.mmt.hotels.clientgateway.enums;

public enum MarshallingErrors {
	
	JSON_TO_OBJECT_FAILURE("00", "error while converting json to object"),
	OBJECT_TO_JSON_FAILURE("01", "error while converting object to json"),
	NO_DATA_FOUND("03","No data found");
	
	private String errorCode;
	private String errorMsg;
	
	private MarshallingErrors(String errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
}
