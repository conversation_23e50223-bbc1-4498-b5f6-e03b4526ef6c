package com.mmt.hotels.clientgateway.grpcconnectors;

import com.gommt.hotels.content.proto.mediaid.MediaIdGrpc;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaGrpc;
import com.mmt.hotels.clientgateway.constants.Constants;
import io.grpc.Deadline;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.NettyChannelBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLException;
import java.io.File;
import java.util.concurrent.TimeUnit;

@Component
public class StaticContentGrpcConnector {

    @Value("${static.content.grpc.host}")
    private String grpcHost;

    @Value("${static.content.grpc.port}")
    private int grpcPort;

    @Value("${static.content.max.grpc.resp.size}")
    private int maxGrpcRespSize;

    @Value("${static.content.ssl.certificate.required}")
    private boolean staticContentEngineCertificateRequired;


    @Autowired
    @Qualifier("nettyChannelExecutorHsc")
    private TaskExecutor taskExecutor;

    public ManagedChannel channel=null;
    public ManagedChannel channelDesktop=null;
    public ManagedChannel channelIos=null;
    public ManagedChannel channelAndroid=null;
    public ManagedChannel channelPwa=null;
    private static final Logger LOGGER = LoggerFactory.getLogger(StaticContentGrpcConnector.class);

    @PostConstruct
    public void init() throws SSLException {
        LOGGER.debug("Establishing connection with Static Content server");
        if(staticContentEngineCertificateRequired){
            LOGGER.debug("SSL certificate reqd");
            NettyChannelBuilder channelBuilder = NettyChannelBuilder.forAddress(grpcHost, grpcPort);
            channelBuilder.sslContext(GrpcSslContexts.forClient().trustManager(new File("/opt/manthan/san-aws-ecs-mmt.crt")).build());
            channelBuilder.maxInboundMessageSize(maxGrpcRespSize);
            channelBuilder.executor(taskExecutor);
            channelAndroid = channelBuilder.build();
            channelIos = channelBuilder.build();
            channelDesktop = channelBuilder.build();
            channelPwa = channelBuilder.build();
        }else{
            channel  = ManagedChannelBuilder
                    .forAddress(grpcHost,grpcPort).usePlaintext()
                    .maxInboundMessageSize(maxGrpcRespSize)
                    .build();
            // Assigning all the channels to a single channel, it is only being used while testing in local, QA, dev servers and not used in prod.
            channelDesktop = channel;
            channelIos = channel;
            channelAndroid = channel;
            channelPwa = channel;
        }
    }

    public ManagedChannel getChannel(String deviceType){
        if(StringUtils.isBlank(deviceType))
            return channelDesktop;
        switch (deviceType.toUpperCase()) {
            case Constants.DEVICE_OS_ANDROID:
                return channelAndroid;
            case Constants.DEVICE_OS_IOS:
                return channelIos;
            case Constants.DEVICE_OS_PWA:
                return channelPwa;
            default:
                return channelDesktop;
        }
    }
}
