package com.mmt.hotels.clientgateway.grpcconnectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
public class GrpcConnectorConfig {
    @Value("${hsc.netty.thread.pool.size}")
    private int threadPoolSizeHsc;

    @Value("${hsc.netty.max.thread.pool.size}")
    private int maxThreadPoolSizeHsc;

    private static final Logger LOGGER = LoggerFactory.getLogger(GrpcConnectorConfig.class);

    @Bean(name = "nettyChannelExecutorHsc")
    public TaskExecutor executorManagedChannelHsc() {
        LOGGER.warn("CG GI netty task executor for HSC");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolSizeHsc);
        executor.setMaxPoolSize(maxThreadPoolSizeHsc);
        executor.setThreadNamePrefix("managed-channel-pool");
        executor.initialize();
        return executor;
    }
}
