package com.mmt.hotels.clientgateway.grpcexecutors;

import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Grpc;
import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Request;
import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Response;
import com.gommt.hotels.content.proto.mediaid.MediaIdGrpc;
import com.gommt.hotels.content.proto.mediaid.MediaIdRequest;
import com.gommt.hotels.content.proto.mediaid.MediaIdResponse;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaGrpc;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaRequest;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaResponse;
import com.mmt.hotels.clientgateway.grpcconnectors.StaticContentGrpcConnector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class StaticContentGrpcExecutor {

    @Autowired
    StaticContentGrpcConnector staticContentGrpcConnector;
    @Value("${static.content.default.timeout}")
    private int staticContentDefaultTimeout;

    private static final Logger LOGGER = LoggerFactory.getLogger(StaticContentGrpcExecutor.class);

    public TaggedMediaResponse getTaggedMedia(TaggedMediaRequest request, String bookingDevice){
        LOGGER.warn("Sending TaggedMedia request to HSC for brand {}", request.getTenant());
        return TaggedMediaGrpc.newBlockingStub(staticContentGrpcConnector.getChannel(bookingDevice)).withDeadlineAfter(staticContentDefaultTimeout,TimeUnit.MILLISECONDS).getTaggedMedia(request);
    }

    public MediaIdResponse getMediaId(MediaIdRequest request, String bookingDevice){
        LOGGER.warn("Sending MediaIdDetails request to HSC for brand {}", request.getTenant());
        return MediaIdGrpc.newBlockingStub(staticContentGrpcConnector.getChannel(bookingDevice)).withDeadlineAfter(staticContentDefaultTimeout,TimeUnit.MILLISECONDS).getMediaIdDetails(request);
    }

    public HotelsDataV2Response getHotelIdMapping(HotelsDataV2Request request, String bookingDevice){
        LOGGER.warn("Sending HotelsDataV2 request to HSC for brand {}",request.getTenant());
        return HotelsDataV2Grpc.newBlockingStub(staticContentGrpcConnector.getChannel(bookingDevice)).withDeadlineAfter(staticContentDefaultTimeout, TimeUnit.MILLISECONDS).getHotelsDataV2(request);
    }
}
