package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class StaticDetailExecutor {
	
	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private RestConnectorUtil restConnectorUtil;
	
	@Value("${static.details.url}")
	private String staticDetailUrl;

	@Value("${city.guide.url}")
	private String cityGuideUrl;

	@Value("${hotels.hotstore.url}")
	private String hotelsFromHotStoreUrl;

	@Value("${hotels.review.summary.url}")
	private String hotelsReviewSummaryUrl;

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private PricingEngineHelper pricingEngineHelper;

	private static final Logger logger = LoggerFactory.getLogger(StaticDetailExecutor.class);

	public HotelDetailWrapperResponse getStaticDetail(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		String result = getStaticDetailsResponse(hotelDetailsMobRequestBody, parameterMap, headers);
        HotelDetailWrapperResponse hotelDetailWrapperResponse = objectMapperUtil.getObjectFromJson(result, HotelDetailWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
        if (hotelDetailWrapperResponse != null && hotelDetailWrapperResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(hotelDetailWrapperResponse.getResponseErrors().getErrorList())) {
        	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
        			hotelDetailWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
        			hotelDetailWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return hotelDetailWrapperResponse;
	}

	public String getStaticDetailsResponse(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, Map<String, String[]> parameterMap,Map<String, String> httpHeaderMap) throws ClientGatewayException {
		Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
        String request = objectMapperUtil.getJsonFromObject(hotelDetailsMobRequestBody, DependencyLayer.CLIENTGATEWAY);
		//staticDetailUrl = RestURLHelper.getDestinationUrl(staticDetailUrl);
		String relativeUrl = Utility.getcompleteURL(staticDetailUrl, parameterMap, hotelDetailsMobRequestBody.getCorrelationKey());
		relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, hotelDetailsMobRequestBody.getExperimentData());
		logger.debug(relativeUrl);
		logger.debug(request);
		logger.warn("Static detail CG to HES header: {}", headerMap);

		return restConnectorUtil.performStaticDetailPost(request, headerMap, relativeUrl);
	}


	public CityGuideResponse getCityGuildeResponse(CityGuideRequest cityGuideRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
		String request = objectMapperUtil.getJsonFromObject(cityGuideRequest, DependencyLayer.CLIENTGATEWAY);
		//cityGuideUrl = RestURLHelper.getDestinationUrl(cityGuideUrl);
		String relativeUrl = Utility.getcompleteURL(cityGuideUrl, parameterMap, cityGuideRequest.getCorrelationKey());
		logger.debug(relativeUrl);
		logger.debug(request);

		String response = restConnectorUtil.performStaticDetailPost(request, headerMap, relativeUrl);
		return objectMapperUtil.getObjectFromJson(response, CityGuideResponse.class, DependencyLayer.ORCHESTRATOR);
	}


	private Map<String, String> getHeaderMap(Map<String, String> httpHeaderMap) {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		headerMap.put("tid",httpHeaderMap.get("tid"));
		headerMap.put("x-forwarded-for",httpHeaderMap.get("X-Forwarded-For"));
		/*GETTING AKAMAI HEADER*/
		String akmaiHeader = httpHeaderMap.get(Constants.HEADER_AKAMAI);
		if (StringUtils.isEmpty(akmaiHeader))
			akmaiHeader = httpHeaderMap.get(Constants.HEADER_AKAMAI.toLowerCase());
		if(StringUtils.isNotEmpty(akmaiHeader)){
			headerMap.put(Constants.HEADER_AKAMAI.toLowerCase(),akmaiHeader);
		}

		if (StringUtils.isNotEmpty(httpHeaderMap.get("mmt-auth")))
			headerMap.put("mmt-auth", httpHeaderMap.get("mmt-auth"));
		return headerMap;
	}

	public HotStoreHotelsWrapperResponse getWishListedHotelsFromHotStore(HotStoreHotelsRequestBody hotStoreHotelsRequestBody, Map<String, String> httpHeaderMap)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
			String request = objectMapperUtil.getJsonFromObject(hotStoreHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
			//hotelsFromHotStoreUrl = RestURLHelper.getDestinationUrl(hotelsFromHotStoreUrl);
			String relativeUrl = String.format(hotelsFromHotStoreUrl, hotStoreHotelsRequestBody.getCorrelationKey());
			logger.debug(request);
			logger.debug(relativeUrl);
			String result = restConnectorUtil.performHotelsHotStorePost(request, headerMap, relativeUrl);
			HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse = objectMapperUtil.getObjectFromJson(result, HotStoreHotelsWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
			if (hotStoreHotelsWrapperResponse != null && hotStoreHotelsWrapperResponse.getResponseErrors() != null
					&& CollectionUtils.isNotEmpty(hotStoreHotelsWrapperResponse.getResponseErrors().getErrorList())) {
				throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
						hotStoreHotelsWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
						hotStoreHotelsWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
			}
			return hotStoreHotelsWrapperResponse;
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "getWishListedHotelsFromHotStore", new Date().getTime() - start);
		}
	}

	public FlyfishReviewWrapperResponse getFlyFishReviewSummary(FlyfishReviewRequestBody flyfishReviewRequestBody, Map<String, String> httpHeaderMap) throws JsonParseException {
		long start = new Date().getTime();
		FlyfishReviewWrapperResponse flyfishReviewWrapperResponse = new FlyfishReviewWrapperResponse();
		try {
			Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
			String request = objectMapperUtil.getJsonFromObject(flyfishReviewRequestBody, DependencyLayer.CLIENTGATEWAY);
			//hotelsReviewSummaryUrl = RestURLHelper.getDestinationUrl(hotelsReviewSummaryUrl);
			String relativeUrl = String.format(hotelsReviewSummaryUrl, flyfishReviewRequestBody.getCorrelationKey());
			logger.debug(request);
			logger.debug(relativeUrl);
			String result = restConnectorUtil.performHotelsReviewSummaryPost(request, headerMap, relativeUrl);
			flyfishReviewWrapperResponse = objectMapperUtil.getObjectFromJson(result, FlyfishReviewWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
			if (flyfishReviewWrapperResponse != null && flyfishReviewWrapperResponse.getResponseErrors() != null
					&& CollectionUtils.isNotEmpty(flyfishReviewWrapperResponse.getResponseErrors().getErrorList())) {
				logger.debug("Error while getting FlyFishReviewSummary {}", flyfishReviewWrapperResponse.getResponseErrors().getErrorList());
			}
		} catch (Exception e) {
			logger.debug("Exception occurred while getting FlyFishReviewSummary for dateLess case {}", flyfishReviewWrapperResponse.getResponseErrors().getErrorList(), e);
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "getFlyFishReviewSummary", new Date().getTime() - start);
		}
		return flyfishReviewWrapperResponse;
	}

}
