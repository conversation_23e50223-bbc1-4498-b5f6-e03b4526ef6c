package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TaggedMediaExecutor {

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    public String executeGetTaggedMediaRequest(Map<String,String> httpHeaderMap, String destinationUrl)
            throws ClientGatewayException{
        return restConnectorUtil.performGetTaggedMedia(httpHeaderMap, destinationUrl);
    }

    public String executeGetMediaByTagIdRequest(Map<String,String> httpHeaderMap, String destinationUrl)
            throws ClientGatewayException{
        return restConnectorUtil.performGetMediaByTagId(httpHeaderMap, destinationUrl);
    }

}
