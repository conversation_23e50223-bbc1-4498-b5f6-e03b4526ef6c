package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class SearchHotelsExecutor {

	private static final Logger logger = LoggerFactory.getLogger(SearchHotelsExecutor.class);

	@Autowired
	private ObjectMapperUtil objectMapperUtil;

	@Autowired
	private RestConnectorUtil restConnectorUtil;

	@Autowired
	MetricAspect metricAspect;

	@Value("${search.personalized.hotels.url}")
	private String searchPersonalizedHotelsUrl;

	@Value("${search.hotels.url}")
	private String searchHotelsUrl;

	@Value("${landing.discovery.url}")
	private String landingDiscoveryUrl;

	@Value("${listing.map.hes.url}")
	private String listingMapUrl;

	@Value("${fetch.collection.url}")
	private String fetchCollectionUrl;

	@Value("${meta.data.url}")
	private String metaDataUrl;

	@Value("${nearby.hotels.url}")
	private String nearByUrl;

	@Autowired
	PricingEngineHelper pricingEngineHelper;


	public ListingPagePersonalizationResponsBO searchPersonalizedHotels(
			SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
			long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			headerMap.put("X-Forwarded-For", "ok");
			headerMap.put("X-Akamai-Edgescape", "**********");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			//searchPersonalizedHotelsUrl = RestURLHelper.getDestinationUrl(searchPersonalizedHotelsUrl);
			String relativeUrl = Utility.getcompleteURL(searchPersonalizedHotelsUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			relativeUrl = pricingEngineHelper.appendFilterServiceExpInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			logger.debug(request);
			logger.debug(relativeUrl);
			String result = restConnectorUtil.performSearchHotelsPost(request, headerMap, relativeUrl);
			ListingPagePersonalizationResponsBO listingPagePersonalizationResponsBO = objectMapperUtil.getObjectFromJson(result, ListingPagePersonalizationResponsBO.class,
					DependencyLayer.ORCHESTRATOR);
			if (listingPagePersonalizationResponsBO != null && listingPagePersonalizationResponsBO.getResponseErrors() != null
					&& CollectionUtils.isNotEmpty(listingPagePersonalizationResponsBO.getResponseErrors().getErrorList())) {
				throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
						listingPagePersonalizationResponsBO.getResponseErrors().getErrorList().get(0).getErrorCode(),
						listingPagePersonalizationResponsBO.getResponseErrors().getErrorList().get(0).getErrorMessage());
			}
			return listingPagePersonalizationResponsBO;
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchPersonalizedHotels", new Date().getTime() - start);
		}
	}

	public SearchWrapperResponseBO<SearchWrapperHotelEntity> searchHotels(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			headerMap.put("X-Forwarded-For", "ok");
			headerMap.put("X-Akamai-Edgescape", "**********");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(searchHotelsUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			relativeUrl = pricingEngineHelper.appendFilterServiceExpInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			logger.debug(request);
			logger.debug(relativeUrl);

			String result = restConnectorUtil.performSearchHotelsPost(request, headerMap, relativeUrl);
			SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = objectMapperUtil.getObjectFromJsonWithType
					(result, new TypeReference<SearchWrapperResponseBO<SearchWrapperHotelEntity>>() {
					}, DependencyLayer.ORCHESTRATOR);
			if (searchWrapperResponseBO != null && searchWrapperResponseBO.getResponseErrors() != null
					&& CollectionUtils.isNotEmpty(searchWrapperResponseBO.getResponseErrors().getErrorList())) {
				throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, searchWrapperResponseBO.getResponseErrors().getErrorList().get(0).getErrorCode(),
						searchWrapperResponseBO.getResponseErrors().getErrorList().get(0).getErrorMessage());
			}
			return searchWrapperResponseBO;
		}finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchHotels", new Date().getTime() - start);

		}
	}

	public String searchHotelsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			headerMap.put("X-Forwarded-For", "ok");
			headerMap.put("X-Akamai-Edgescape", "**********");

			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(searchHotelsUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			relativeUrl = pricingEngineHelper.appendFilterServiceExpInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			logger.debug(request);
			logger.debug(relativeUrl);
			return restConnectorUtil.performSearchHotelsPost(request, headerMap, relativeUrl);
		}finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchHotelsOld", new Date().getTime() - start);
		}
	}

	public String landingDiscoveryOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(landingDiscoveryUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			logger.debug("Landing discovery request :: {}",request);
			logger.debug("Landing discovery URL :: {}",relativeUrl);
			return restConnectorUtil.performLandingDiscoveryPost(request, headerMap, relativeUrl);
		}finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "landingDiscoveryOld", new Date().getTime() - start);
		}
	}

	public String listingMapOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("content-type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(listingMapUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			logger.debug(request);
			logger.debug(relativeUrl);

			return restConnectorUtil.performSearchHotelsPost(request, headerMap, relativeUrl);
		}finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "listingMapOld", new Date().getTime() - start);

		}
	}

	public String searchPersonalizedHotelsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(searchPersonalizedHotelsUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			relativeUrl = pricingEngineHelper.appendFilterServiceExpInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			logger.debug(request);
			logger.debug(relativeUrl);

			return restConnectorUtil.performSearchHotelsPost(request, headerMap, relativeUrl);
		}finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchPersonalizedHotelsOld", new Date().getTime() - start);

		}
	}

	public String fetchCollectionsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(fetchCollectionUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			if (Constants.REQUEST_SOURCE_SCION.equalsIgnoreCase(searchWrapperInputRequest.getRequester())) {
				relativeUrl = relativeUrl + Constants.AMP_SOURCE_CLIENT_SCION;
			}
			logger.debug(request);
			logger.debug(relativeUrl);

			return restConnectorUtil.performFetchCollectionPost(request, headerMap, relativeUrl);
		}finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "fetchCollectionsOld", new Date().getTime() - start);
		}
	}

	public String getMetaDataResponse(String cityId, Map<String, String> requestParams) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");

		String url = Utility.appendQueryParamsInUrl(String.format(metaDataUrl,cityId), requestParams);

		return restConnectorUtil.performGetByPass(headerMap,url);
	}
	public SearchWrapperResponseBO<SearchWrapperHotelEntity> nearByHotels(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(nearByUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			logger.debug(request);
			logger.debug(relativeUrl);

			String result = restConnectorUtil.performNearByPost(request, headerMap, relativeUrl);
			SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = objectMapperUtil.getObjectFromJsonWithType
					(result, new TypeReference<SearchWrapperResponseBO<SearchWrapperHotelEntity>>() {
					}, DependencyLayer.ORCHESTRATOR);
			if (searchWrapperResponseBO != null && searchWrapperResponseBO.getResponseErrors() != null
					&& CollectionUtils.isNotEmpty(searchWrapperResponseBO.getResponseErrors().getErrorList())) {
				throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, searchWrapperResponseBO.getResponseErrors().getErrorList().get(0).getErrorCode(),
						searchWrapperResponseBO.getResponseErrors().getErrorList().get(0).getErrorMessage());
			}
			return searchWrapperResponseBO;
		}finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "nearByHotels", new Date().getTime() - start);
		}
	}

	public String nearByHotelsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = Utility.getcompleteURL(nearByUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, searchWrapperInputRequest.getExperimentData());
			logger.debug(request);
			logger.debug(relativeUrl);

			return restConnectorUtil.performNearByPost(request, headerMap, relativeUrl);
		}finally {
		metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "nearByHotelsOld", new Date().getTime() - start);
	}
	}
}
