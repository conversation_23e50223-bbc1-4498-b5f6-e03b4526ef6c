package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.image.HotelImageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.HEADER_CONTENT_TYPE;
@Component
public class ImageDataExecutor {
    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${static.image.url}")
    private String staticImageUrl;


    public HotelImage getStaticImageDetailsResponse(HotelImageRequest hotelImageRequest) throws JsonParseException, RestConnectorException {
        String request = objectMapperUtil.getJsonFromObject(hotelImageRequest, DependencyLayer.CLIENTGATEWAY);
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, "application/json");
        String resp = restConnectorUtil.performFetchImagePost(request, headers,staticImageUrl );
        return objectMapperUtil.getObjectFromJson(resp, HotelImage.class, DependencyLayer.CLIENTGATEWAY);
    }
}
