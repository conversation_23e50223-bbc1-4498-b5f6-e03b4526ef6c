package com.mmt.hotels.clientgateway.restexecutors;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.ByPassUrls;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.ImageCategoryEntityBO;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.PayLaterEligibilityResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.model.response.staticdata.HotelsImageResponseEntity;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

@Component
public class AvailRoomsExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${avail.rooms.url}")
    private String availRoomsUrl;
    
    @Value("${updated.price.occu.less.url}")
    private String updatedPriceOccuLessUrl;

    @Value("${paylater.eligibility.url}")
    private String payLaterEligibilityUrl;

    @Autowired
    private MetricAspect metricAspect;

    @Value("${hotel.image.url}")
    private String hotelImageUrl;

    @Autowired
    @Qualifier("reviewServiceThreadPool")
    private ThreadPoolTaskExecutor reviewServiceThreadPool;


    private static final Gson gson = new Gson();

    private static final Logger logger = LoggerFactory.getLogger(AvailRoomsExecutor.class);

    public RoomDetailsResponse availRooms(PriceByHotelsRequestBody availRoomsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(availRoomsRequestBody, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(availRoomsUrl, parameterMap, availRoomsRequestBody.getCorrelationKey());
        logger.debug(request);
        logger.debug(relativeUrl);

        long startTime = new Date().getTime();
        String result = null;
        try {
            result = restConnectorUtil.performAvailRoomsPost(request, headerMap, relativeUrl);
        } catch (Exception ex) {
            throw ex;
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "availrooms/availPrice", new Date().getTime() - startTime);
        }

        logger.debug(result);

        RoomDetailsResponse roomDetailsResponse = objectMapperUtil.getObjectFromJson(result, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
        if (roomDetailsResponse != null &&  roomDetailsResponse.getResponseErrors() != null &&
                CollectionUtils.isNotEmpty(roomDetailsResponse.getResponseErrors().getErrorList() )) {
        	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
        			roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
        			roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return roomDetailsResponse;
    }

    public String availRoomsOld(PriceByHotelsRequestBody availRoomsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(availRoomsRequestBody, DependencyLayer.CLIENTGATEWAY);
        //availRoomsUrl = RestURLHelper.getDestinationUrl(availRoomsUrl);
        String relativeUrl = Utility.getcompleteURL(availRoomsUrl, parameterMap, availRoomsRequestBody.getCorrelationKey());
        logger.debug(request);
        logger.debug(relativeUrl);

        String response = restConnectorUtil.performAvailRoomsPost(request, headerMap, relativeUrl);
        RoomDetailsResponse responseObject  =objectMapperUtil.getObjectFromJson(response, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
        if (null!=responseObject) {
            response = objectMapperUtil.getJsonFromObjectWithView(responseObject, DependencyLayer.ORCHESTRATOR, PIIView.External.class);
        } else {
            response = null;
        }
        return response;
    }
    
    public String updatedPriceOccuLessOld(PriceByHotelsRequestBody availRoomsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(availRoomsRequestBody, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(updatedPriceOccuLessUrl, parameterMap, availRoomsRequestBody.getCorrelationKey());
        logger.debug(request);
        logger.debug(relativeUrl);

        return restConnectorUtil.performUpdatedPriceOccuLessPost(request, headerMap, relativeUrl);
    }

    public HotelsRoomInfoResponseEntity getRoomStaticDetails() {
        return null;
    }
    
	public TotalPricingResponse getTotalPricingDetails(TotalPricingRequest getTotalPriceRequest, Map<String, String[]> parameterMap, String correlationKey, Map<String,String> headers)
            throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(getTotalPriceRequest, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(ByPassUrls.DESTINATION_GET_TOTAL_PRICING_URL, parameterMap,correlationKey);
        logger.debug(request);
        logger.debug(relativeUrl);

        String result = restConnectorUtil.performTotalPricePost(request, headerMap, relativeUrl);
        TotalPricingResponse totalPricingRsp = objectMapperUtil.getObjectFromJson(result, TotalPricingResponse.class, DependencyLayer.ORCHESTRATOR);
        if (totalPricingRsp != null && totalPricingRsp.getResponseErrors() !=null &&
	    		CollectionUtils.isNotEmpty(totalPricingRsp.getResponseErrors().getErrorList())) {
	    	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, 
	    			totalPricingRsp.getResponseErrors().getErrorList().get(0).getErrorCode(),
	    			totalPricingRsp.getResponseErrors().getErrorList().get(0).getErrorMessage());
	    }
		return totalPricingRsp;
	}

    public PayLaterEligibilityResponse fetchPayLaterEligibility(PayLaterEligibilityRequest payLaterEligibilityRequest, Map<String, String[]> parameterMap, String correlationKey, Map<String,String> headers)
            throws ClientGatewayException {
        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(payLaterEligibilityRequest, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(payLaterEligibilityUrl, parameterMap,correlationKey);
        logger.debug(request);
        logger.debug(relativeUrl);

        String result = restConnectorUtil.checkPayLaterEligibilityPost(request, headerMap, relativeUrl);
        PayLaterEligibilityResponse resp = objectMapperUtil.getObjectFromJson(result, PayLaterEligibilityResponse.class, DependencyLayer.ORCHESTRATOR);
        if (resp != null && resp.getResponseErrors() !=null &&
                CollectionUtils.isNotEmpty(resp.getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    resp.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    resp.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return resp;
    }

    public Future<HotelImage> getHotelImages(AvailRoomsRequest availRoomsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap,  String pageContext,CountDownLatch countDownLatchAsync) {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return reviewServiceThreadPool.submit(() -> {
        if (mdcMap != null) {
            MDC.setContextMap(mdcMap);
        }
        Map<String, String[]> params = buildParamMap(availRoomsRequest);
        parameterMap.putAll(params);
        String url = Utility.getcompleteURL(hotelImageUrl, parameterMap, availRoomsRequest.getCorrelationKey());
        url = Utility.appendPageContextToURL(url, pageContext);
        long startTime = new Date().getTime();
        String result = null;
        try {
            result = restConnectorUtil.performStaticRoomDetailGet(httpHeaderMap, url);
        } catch (RestConnectorException e) {
            e.printStackTrace();
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "availRooms/roomImages", new Date().getTime() - startTime);
        }
        try {
            HotelsImageResponseEntity response = objectMapperUtil.getObjectFromJson(result, HotelsImageResponseEntity.class, DependencyLayer.ORCHESTRATOR);
            if (CollectionUtils.isNotEmpty(response.getImages())) {
                countDownLatchAsync.countDown();
                return response.getImages().get(0);
            } else {
                countDownLatchAsync.countDown();
                return null;
            }
        } catch (JsonParseException e) {
            e.printStackTrace();
        }
        countDownLatchAsync.countDown();
        return null;
        });
    }

    public Map<String, String[]> buildParamMap(AvailRoomsRequest availRoomsRequest) {
        Map<String, String[]> params = new HashMap<>();
        if (availRoomsRequest.getSearchCriteria() != null) {
            String MMTorGIHotelId = Utility.getMMTorGIHotelId(availRoomsRequest.getSearchCriteria().getHotelId(), availRoomsRequest.getSearchCriteria().getGiHotelId());
            if (MMTorGIHotelId != null)
                params.put(Constants.HOTEL_IDS_PARAM, new String[]{new JSONArray(Collections.singletonList(MMTorGIHotelId)).toString()});
            if (availRoomsRequest.getSearchCriteria().getCountryCode() != null)
                params.put(Constants.COUNTRY_CODE_PARAM, new String[]{availRoomsRequest.getSearchCriteria().getCountryCode()});
            if (availRoomsRequest.getSearchCriteria().getVcId() != null)
                params.put(Constants.VCID_PARAM, new String[]{availRoomsRequest.getSearchCriteria().getVcId()});
        }

        if (availRoomsRequest.getDeviceDetails().getNetworkType() != null)
            params.put(Constants.NETWORK_TYPE_PARAM, new String[]{availRoomsRequest.getDeviceDetails().getNetworkType()});
        List<ImageCategoryEntityBO> imageCategoriesList = new ArrayList<>();
        ImageCategoryEntityBO imageCategory = new ImageCategoryEntityBO();
        imageCategory.setCount(100);
        imageCategory.setCategory("R");
        imageCategoriesList.add(imageCategory);
        params.put(Constants.IMAGE_CATEGORY_PARAM, new String[]{new JSONArray(imageCategoriesList).toString()});
        params.put(Constants.THUMBNAIL_REQD_PARAM, new String[]{"true"});
        if (availRoomsRequest.getRequestDetails() != null && availRoomsRequest.getRequestDetails().getBrand() != null)
            params.put(Constants.BRAND_PARAM, new String[]{availRoomsRequest.getRequestDetails().getBrand()});
        params.put(Constants.IMAGE_TYPE_PARAM, new String[]{"[\"professional\"]"});
        return params;
    }
}
