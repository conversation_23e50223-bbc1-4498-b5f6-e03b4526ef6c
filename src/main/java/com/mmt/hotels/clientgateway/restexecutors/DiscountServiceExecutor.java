package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class DiscountServiceExecutor {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(DiscountServiceExecutor.class);

	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private RestConnectorUtil restConnectorUtil;

	@Value("${validate.coupon.url}")
	private String validateCouponURL;

	public ValidateCouponResponse getValidateCouponResponse(ValidateCouponRequestBody validateCouponRequestBody,  Map<String, String[]> parameterMap) throws ClientGatewayException {
		
		Map<String, String> headerMap = new HashMap<String, String>();
	    headerMap.put("Content-Type", "application/json");
	    headerMap.put("Accept-Encoding", "gzip");
	    
        String requestBody = objectMapperUtil.getJsonFromObject(validateCouponRequestBody, DependencyLayer.CLIENTGATEWAY);
		String relativeUrl = Utility.getcompleteURL(validateCouponURL, parameterMap, validateCouponRequestBody.getCorrelationKey());

		LOGGER.debug("Validate coupon request :: " + requestBody);
		LOGGER.debug("Validate coupon relativeUrl :: " + relativeUrl);
        
		String result = restConnectorUtil.performValidateCouponPost(requestBody, headerMap, relativeUrl);

		LOGGER.debug("Validate coupon response :: " + result);
		
		ValidateCouponResponse response = objectMapperUtil.getObjectFromJson(result, ValidateCouponResponse.class, 
				DependencyLayer.ORCHESTRATOR);
		
		if (response != null && response.getResponseErrors() != null
				&& CollectionUtils.isNotEmpty(response.getResponseErrors().getErrorList())) {
			com.mmt.hotels.model.response.errors.Error error = response.getResponseErrors().getErrorList().get(0);
			throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
					error.getErrorCode(), error.getErrorMessage());
		}
		return response;
	}

}
