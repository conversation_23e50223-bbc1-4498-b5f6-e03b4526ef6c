package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.dpt.FeatureStoreRequest;
import com.mmt.hotels.model.response.dpt.FeatureStoreResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class DataPlatformExecutor {

    private static final Logger logger = LoggerFactory.getLogger(DataPlatformExecutor.class);

    @Value("${dpt.get.features.url}")
    private String dptFeatureStoreUrl;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    MetricAspect metricAspect;


    public FeatureStoreResponse getFeatures(FeatureStoreRequest request) throws Exception {
        long startTime = new Date().getTime();
        try {
            String requestString = objectMapperUtil.getJsonFromObject(request, DependencyLayer.CLIENTGATEWAY);
            Map<String, String> headers = new HashMap<>();
            headers.put(Constants.HEADER_CONTENT_TYPE, Constants.HEADER_CONTENT_APPLICATION_JSON);
            headers.put(Constants.HEADER_ACCEPT_TYPE, Constants.HEADER_CONTENT_APPLICATION_JSON_UTF_8);
            headers.put(Constants.HEADER_ACCEPT_ENCODING, Constants.HEADER_ACCEPT_ENCODING_GZIP_ENCODING);
            logger.debug("DPT getFeatures Request: {}", requestString);
            String result = restConnectorUtil.performDataPlatformPost(requestString, headers, dptFeatureStoreUrl);
            logger.debug("DPT getFeatures Response: {}", result);
            return objectMapperUtil.getObjectFromJson(result, FeatureStoreResponse.class, DependencyLayer.CLIENTGATEWAY);
        } finally {
            metricAspect.addToTime(DependencyLayer.DPT.name(), "getFeatures", new Date().getTime() - startTime);
        }

    }


}
