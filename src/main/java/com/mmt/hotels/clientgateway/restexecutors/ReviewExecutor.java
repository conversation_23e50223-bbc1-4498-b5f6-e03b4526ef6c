package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Brand;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UgcError;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.LogicalException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.request.ugc.GetBookingDetailsRequest;
import com.mmt.hotels.clientgateway.request.ugc.QuestionSet;
import com.mmt.hotels.clientgateway.response.ugc.*;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponseData;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.response.flyfish.UgcReviewRequest;
import com.mmt.hotels.model.response.flyfish.UgcSummaryRequest;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class ReviewExecutor {
    private static final Logger logger = LoggerFactory.getLogger(ReviewExecutor.class);

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Value("${ugc.reviews.url}")
    private String ugcReviewUrl;

    @Value("${ugc.summary.url}")
    private String ugcSummaryUrl;

    @Value("${load.program.url}")
    private String loadProgramUrl;

    @Value("${submit.answers.url}")
    private String submitanswersurl;

    @Value("${platforms.image.upload.url}")
    private String platformsImageUploadUrl;

    @Value("${get.booking.details.url}")
    private String getBookingDetailsUrl;

    public UgcReviewResponseData getReviewData(UgcReviewRequest request, String ck) throws JsonParseException, RestConnectorException {
        String lob = request.getCountryCode().equalsIgnoreCase(DOM_COUNTRY) ? DH : IH;
        request.setLob(lob);
        request.setClient(Brand.GI.name());
        request.setTrafficSource(CG_GI);
        String requestString = objectMapperUtil.getJsonFromObject(request, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Request for UGC Reviews: " + requestString);
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, "application/json");
        String url = String.format(ugcReviewUrl, ck);
        String resp = restConnectorUtil.performFetchUgcReviewsPost(requestString, headers, url);
        return objectMapperUtil.getObjectFromJson(resp, UgcReviewResponseData.class, DependencyLayer.CLIENTGATEWAY);
    }

    public UGCPlatformReviewSummaryDTO getUgcSummary(UgcSummaryRequest ugcSummaryRequest) throws JsonParseException, RestConnectorException {
        String requestString = objectMapperUtil.getJsonFromObject(ugcSummaryRequest, DependencyLayer.CLIENTGATEWAY);
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, "application/json");
        String resp = restConnectorUtil.performFetchUgcSummaryPost(requestString, headers, ugcSummaryUrl);
        return objectMapperUtil.getObjectFromJson(resp, UGCPlatformReviewSummaryDTO.class, DependencyLayer.CLIENTGATEWAY);
    }

    public UgcResponse fetchLoadProgram(ClientLoadProgramRequest request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws RestConnectorException, JsonProcessingException, LogicalException {
        String bookingId = "";
        String metaSrc = "";
        String lob = GI_DH;
        if (request.getUgc() == null || request.getUgc().getBookingId() == null || request.getUgc().getBookingId().isEmpty()) {
            bookingId = getBookingIdFromBooker(request);
            if(request.getUgcQr()!=null && request.getUgcQr().getMetaSrc()!=null)
                metaSrc = request.getUgcQr().getMetaSrc();
        } else {
            bookingId = request.getUgc().getBookingId();
            if(request.getUgc()!= null && request.getUgc().getLob()!=null){
                lob=request.getUgc().getLob();
            }
            if(request.getUgc()!= null && request.getUgc().getMetaSrc()!=null){
                metaSrc=request.getUgc().getMetaSrc();
            }
        }
        if(lob.isEmpty() || !(GI_DH.equals(lob) || GI_IH.equals(lob))){
            lob = GI_DH;
        }
        String url = String.format(loadProgramUrl,bookingId, lob, metaSrc);

        Map<String, String> headers= new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, HEADER_CONTENT_APPLICATION_JSON);
        if (httpHeaderMap != null && !httpHeaderMap.isEmpty()) {
            String deviceId = httpHeaderMap.getOrDefault(HEADER_DEVICE_ID, "");
            String oauthGoibibo = httpHeaderMap.getOrDefault(HEADER_OAUTH_GOIBIBO, "");

            if (!deviceId.isEmpty()) {
                headers.put(HEADER_DEVICE_ID_LOAD_PROGRAM_API, deviceId);
            }
            if (!oauthGoibibo.isEmpty()) {
                headers.put(HEADER_OAUTH_GOIBIBO_LOAD_PROGRAM_API, oauthGoibibo);
            }
        }

        String result = restConnectorUtil.performLoadProgramGet(headers,url);
        ObjectMapper mapper = new ObjectMapper();
        QuestionData responseData = mapper.readValue(result, QuestionData.class);
        if (responseData == null) {
            throw new LogicalException(DependencyLayer.PFM, ErrorType.DOWNSTREAM, UgcError.PROGRAM_LOADING_ISSUE.getErrorCode(),
                    UgcError.PROGRAM_LOADING_ISSUE.getErrorMsg(), UgcError.PROGRAM_LOADING_ISSUE.getTitle());
        }
        if (responseData.getError() != null && responseData.getError().getCode() != null && responseData.getError().getStatus() != null) {
            throw new LogicalException(DependencyLayer.PFM, ErrorType.DOWNSTREAM, UgcError.PROGRAM_LOADING_ISSUE.getErrorCode(),
                    UgcError.PROGRAM_LOADING_ISSUE.getErrorMsg(), UgcError.PROGRAM_LOADING_ISSUE.getTitle());
        }

        if(responseData.getErrors() != null) {
            String errorCode = UgcError.GENERIC_ERROR.getErrorCode();
            if(responseData.getErrors().getErrorList() != null && !responseData.getErrors().getErrorList().isEmpty()) {
                if(responseData.getErrors().getErrorList().get(0) != null) {
                    String firstErrorCode = responseData.getErrors().getErrorList().get(0).getCode();
                    if(firstErrorCode != null) {
                        errorCode = firstErrorCode;
                    }
                }
            }
            throw new LogicalException(DependencyLayer.PFM, ErrorType.DOWNSTREAM, errorCode, null);
        }


        UgcResponse ugcResponse = new UgcResponse();
        ugcResponse.setQuestionData(responseData);
        return ugcResponse;
    }

    public UgcResponse submitAnswersToPlatforms(ClientSubmitApiRequest request, List<ImageUploadResult> imageUploadResults, String correlationKey) throws RestConnectorException, JsonProcessingException {
        String url = submitanswersurl;
        Map<String, String> headers= new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, HEADER_CONTENT_APPLICATION_JSON);
        ObjectMapper mapper = new ObjectMapper();
        String submitAnswersRequestString = createSubmitAnswersRequest(request, imageUploadResults, correlationKey);
        String result = restConnectorUtil.submitAnswersPlatforms(submitAnswersRequestString,headers,url);
        UgcResponse responseData = mapper.readValue(result, UgcResponse.class);
        return responseData;
    }

    public List<ImageUploadResult> uploadImagesToPlatformsS3(MultipartRequest multipartRequest, String correlationKey) throws RestConnectorException, IOException {
        try {
            String url = platformsImageUploadUrl;
            Map<String, String> headers = new HashMap<>();
            headers.put(HEADER_CONTENT_TYPE, HEADER_CONTENT_MULTIPART_FORM_DATA);
            List<ImageUploadResult> imageUploadResults = new ArrayList<>();
            String result = "";
            Map<String, MultipartFile> multipartFileList = multipartRequest.getFileMap();
            for (Map.Entry<String, MultipartFile> file : multipartFileList.entrySet()) {
                if (file.getValue().getContentType().toString().equalsIgnoreCase(Constants.HEADER_CONTENT_APPLICATION_JSON) || file.getValue().getContentType().toString().equalsIgnoreCase(Constants.HEADER_CONTENT_APPLICATION_JSON_UTF_8)) {
                    continue;
                }
                MultipartEntityBuilder builder = MultipartEntityBuilder.create();
                builder.addBinaryBody("file", file.getValue().getInputStream(), ContentType.MULTIPART_FORM_DATA, file.getValue().getOriginalFilename());
                HttpEntity multipart = builder.build();
                result = restConnectorUtil.uploadImagesToPlatform(multipart, headers, url);
                ObjectMapper mapper = new ObjectMapper();
                ImageUploadResult responseData = mapper.readValue(result, ImageUploadResult.class);
                imageUploadResults.add(responseData);
            }
            return imageUploadResults;
        }
        catch (Exception e) {
            logger.error("Error in uploadImagesToPlatformsS3 while processing request for ",correlationKey, e);
            return null;
        }
    }

    public String getBookingIdFromBooker(ClientLoadProgramRequest request) throws JsonProcessingException, RestConnectorException, LogicalException {
        String bookingId = "";
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> headers= new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, HEADER_CONTENT_APPLICATION_JSON);
        GetBookingDetailsRequest getBookingDetailsRequest = getBookingDetailsRequest(request);
        String jsonRequest = objectMapper.writeValueAsString(getBookingDetailsRequest);
        logger.info("Request for getBookingDetails: " + jsonRequest);
        String result = restConnectorUtil.getBookingDetails(jsonRequest, headers, getBookingDetailsUrl);
        logger.info("Response for getBookingDetails: " + jsonRequest + result);
        if (result==null || result.trim().equalsIgnoreCase((NO_DATA_FOUND_FOR_UUID + getBookingDetailsRequest.getUuid()).trim())) {
            throw new LogicalException(
                    DependencyLayer.PFM, ErrorType.DOWNSTREAM, UgcError.NO_DATA_FOUND_FOR_UUID.getErrorCode(),
                    UgcError.NO_DATA_FOUND_FOR_UUID.getErrorMsg(), UgcError.NO_DATA_FOUND_FOR_UUID.getTitle()
            );
        }
        GetBookingDetailsResponse responseData = objectMapper.readValue(result, GetBookingDetailsResponse.class);
        bookingId = responseData.getBookingId();
        if (bookingId == null || bookingId.isEmpty()) {
            throw new LogicalException(
                    DependencyLayer.PFM, ErrorType.DOWNSTREAM, UgcError.BOOKING_ID_NOT_FOUND.getErrorCode(),
                    UgcError.BOOKING_ID_NOT_FOUND.getErrorMsg(), UgcError.BOOKING_ID_NOT_FOUND.getTitle()
            );
        }
        return bookingId;
    }

    public GetBookingDetailsRequest getBookingDetailsRequest(ClientLoadProgramRequest request) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BOOKER_DATE_PATTERN);
        LocalDate thirtyDaysAgo = LocalDate.now().plusDays(BOOKER_PLUS_DAYS);
        GetBookingDetailsRequest getBookingDetailsRequest = new GetBookingDetailsRequest();
        if(request.getUgcQr()!=null) {
            getBookingDetailsRequest.setUuid(request.getUgcQr().getUuid());
            if(request.getUgcQr().getHotelId()!=null && !request.getUgcQr().getHotelId().isEmpty()) {
                getBookingDetailsRequest.setHotelId(request.getUgcQr().getHotelId());
            }
        }
        getBookingDetailsRequest.setDateKey(formatter.format(thirtyDaysAgo.atStartOfDay()));
        getBookingDetailsRequest.setProfileType(Constants.PROFILE_TYPE);
        getBookingDetailsRequest.setLob(DH.toUpperCase());
        return getBookingDetailsRequest;
    }

    public String createSubmitAnswersRequest(ClientSubmitApiRequest request, List<ImageUploadResult> imageUploadResults, String correlationKey) {
        try {
            SubmitRequest submitRequest = new SubmitRequest();
            submitRequest.setUgcId(request.getUgcId());
            submitRequest.setProgramId(request.getProgramId());
            submitRequest.setContentId(request.getHotelId());
            if(request.getUserPage()!=null) {
                submitRequest.setUserPage(request.getUserPage());
            }
            submitRequest.setUserPage(request.getPageId());

            List<QuestionSet> questionSets = request.getQuestions();
            List<Page> pages = new ArrayList<>();
            Page page = new Page();
            List<QuestionDataClient> questions = new ArrayList<>();

            for(QuestionSet questionSet : questionSets) {
                String questionType = "";
                if(questionSet.getQuestionType()!=null) {
                    questionType = questionSet.getQuestionType();
                }
                String questionId = "";
                if(questionSet.getQuestionId()!=null) {
                    questionId = questionSet.getQuestionId();
                }
                QuestionDataClient questionDataClient = new QuestionDataClient();

                switch (questionType) {
                    case "IMAGE":
                        List<MediaDetails> mediaDetails = new ArrayList<>();
                        List<MediaDetails> alreadyUploadedMediaDetails = questionSet.getMediaDetails();
                        if(alreadyUploadedMediaDetails!=null && !alreadyUploadedMediaDetails.isEmpty())
                        {
                            mediaDetails.addAll(alreadyUploadedMediaDetails);
                        }
                        for (ImageUploadResult imageUploadResult : imageUploadResults) {
                            MediaDetails mediaDetail = new MediaDetails();
                            mediaDetail.setMediaId(imageUploadResult.getFileList().get(0).getId());
                            mediaDetail.setMediaType(imageUploadResult.getFileList().get(0).getMediaType());
                            mediaDetail.setMediaUrl(imageUploadResult.getFileList().get(0).getUrl());
                            mediaDetails.add(mediaDetail);
                        }
                        questionDataClient.setMediaDetails(mediaDetails);
                        break;
                    case "STAR_RATING":
                        questionDataClient.setRatingValue(questionSet.getRating());
                        break;
                    case "TEXT_AREA":
                        List<AdditionalDetail> additionalDetails = new ArrayList<>();
                        AdditionalDetail additionalDetail = new AdditionalDetail();
                        TextDetails contentText = new TextDetails();
                        TextDetails titleText = new TextDetails();
                        contentText.setText(questionSet.getText());
                        titleText.setText(questionSet.getTitle());
                        additionalDetail.setType(TITLE_TEXT);
                        additionalDetail.setTextDetails(titleText);
                        additionalDetails.add(additionalDetail);
                        questionDataClient.setAdditionalDetails(additionalDetails);
                        questionDataClient.setTextDetails(contentText);
                        break;
                    // handles all multichoice type of questions, including single select and multi select, emote
                    default:
                        if(questionSet.getSelected()!=null) {
                            List<String> submittedAnswers = questionSet.getSelected();
                            List<String> answers = new ArrayList<>();
                            for (String answer : submittedAnswers) {
                                answers.add(answer);
                            }
                            questionDataClient.setSelectedOptionIds(answers);
                        }
                }
                questionDataClient.setQuestionId(questionId);
                questions.add(questionDataClient);
            }

            page.setPageId(request.getPageId());
            page.setQuestions(questions);
            pages.add(page);
            submitRequest.setPages(pages);
            ObjectMapper mapper = new ObjectMapper();

            // Convert the object to JSON string
            String jsonString = mapper.writeValueAsString(submitRequest);
            return jsonString;
        } catch (Exception e) {
            logger.error("Error in createSubmitAnswersRequest for processing request",correlationKey, e);
            return "";
        }
    }
}
