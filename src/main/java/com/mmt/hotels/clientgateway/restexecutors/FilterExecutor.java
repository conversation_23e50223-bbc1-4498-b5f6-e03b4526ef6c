package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class FilterExecutor {


    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${filter.count.url}")
    private String filterCountUrl;

    @Autowired
    PricingEngineHelper pricingEngineHelper;
    
    private static final Logger logger = LoggerFactory.getLogger(FilterExecutor.class);

    public <T> T filterCount(SearchWrapperInputRequest filterRequest, Map<String, String> headers,Class<T> type) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(filterRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug(request);
        //filterCountUrl = RestURLHelper.getDestinationUrl(filterCountUrl);
        String relativeUrl = pricingEngineHelper.appendFilterServiceExpInUrl(filterCountUrl, filterRequest.getExperimentData());
        String result = restConnectorUtil.performFilterCountPost(request, headerMap, relativeUrl);
        if(type != String.class)
            return objectMapperUtil.getObjectFromJson(result, type, DependencyLayer.ORCHESTRATOR);
        else
            return (T)result;
    }


}
