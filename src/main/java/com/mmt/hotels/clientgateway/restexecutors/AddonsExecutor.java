package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class AddonsExecutor {

	private static final Logger LOGGER = LoggerFactory.getLogger(AddonsExecutor.class);
	
	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private RestConnectorUtil restConnectorUtil;
	
	@Value("${webapi.addons.get.url}")
	private String getAddonsURL;
	
	public <T> T getAddonsResponse(GetAddonsRequest getAddonsRequest, Map<String, String[]> parameterMap, Class<T> type) throws ClientGatewayException {
		
		Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        
        String request = objectMapperUtil.getJsonFromObject(getAddonsRequest, DependencyLayer.CLIENTGATEWAY);
		//getAddonsURL = RestURLHelper.getDestinationUrl(getAddonsURL);
		String relativeUrl = Utility.getcompleteURL(getAddonsURL, parameterMap, getAddonsRequest.getCorrelationKey());
        LOGGER.debug("Addons request :: {}" , request);
		LOGGER.debug("Addons relativeUrl :: {}" , relativeUrl);
        
        String result = restConnectorUtil.performSearchRoomsPost(request, headerMap, getAddonsURL);
        LOGGER.debug("Addons response :: {}" , result);

        if (type != String.class)
        	return objectMapperUtil.getObjectFromJson(result, type, DependencyLayer.ORCHESTRATOR);
        else
        	return (T)result;

	}

}
