package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ListingMapExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${listing.map.hes.url}")
    private String listingMapUrl;
    
    private static final Logger logger = LoggerFactory.getLogger(ListingMapExecutor.class);

    public HotelListingMapResponse listingMap(SearchWrapperInputRequest listingMapRequestBody, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(listingMapRequestBody, DependencyLayer.CLIENTGATEWAY);
        logger.debug("For URL : {} MODIFIEDREQUEST: {}",listingMapUrl, request);
        String result = restConnectorUtil.performListingMapPost(request, headerMap, listingMapUrl);
        logger.debug("RESPONSE: {}",result);
        HotelListingMapResponse hotelListingMapResponse = objectMapperUtil.getObjectFromJson(result, HotelListingMapResponse.class, DependencyLayer.ORCHESTRATOR);
        if (hotelListingMapResponse != null && hotelListingMapResponse.getResponseErrors() != null &&
        		CollectionUtils.isNotEmpty(hotelListingMapResponse.getResponseErrors().getErrorList())) {
        	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
        			hotelListingMapResponse.getResponseErrors().getErrorList().get(0).getErrorCode(), 
        			hotelListingMapResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return hotelListingMapResponse;
    }
}