package com.mmt.hotels.clientgateway.restexecutors;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.pojo.response.landing.HotelLandingWrapperResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;

import static com.mmt.hotels.clientgateway.constants.Constants.ANDROID;
import static com.mmt.hotels.clientgateway.constants.Constants.DEVICE_IOS;

@Component
public class MobLandingExecutor {


    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${mob.landing.url}")
    private String mobLandingUrl;

    @Value("${pagemaker.rendered.template.url}")
    private String pageMakerRenderedTemplateUrl;

    @Value("${pagemaker.chunk.data.url}")
    private String pageMakerChunkUrl;

    @Value("${pagemaker.personalized.chunk.data.url}")
    private String pageMakerPersonalizedChunkUrl;

    @Value("${smartengage.offer.url}")
    private String smartEngageUrl;
    
    @Value("${place.lat.lng.url}")
    private String placeToLatLngURL;
    
    @Value("${lat.lng.city.url}")
    private String latLngToCityCodeURL;

    @Value("${list.personalization.card.url}")
    private String listPersonalizedCardURL;

    @Autowired
    @Qualifier("landingServiceThreadPool")
    private ThreadPoolTaskExecutor landingServiceThreadPool;
    
    private static final Logger LOGGER = LoggerFactory.getLogger(MobLandingExecutor.class);

    private static Gson gson = new Gson();

    public Future<String> moblanding(HotelLandingMobRequestBody hotelLandingMobRequestBody, Map<String, String[]> paramMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return landingServiceThreadPool.submit(() -> {
            if (mdcMap != null) {
                MDC.setContextMap(mdcMap);
            }
            Map<String, String> headerMap = new HashMap<String, String>();
            headerMap.put("Content-Type", "application/json");
            headerMap.put("Accept-Encoding", "gzip");
            headerMap.put("srcRequest", "ClientGateway");
            headerMap.put("X-Forwarded-For", "**********");
            headerMap.put("X-Akamai-Edgescape", "ok");
            if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
                headerMap.put("mmt-auth", headers.get("mmt-auth"));
            String request = objectMapperUtil.getJsonFromObject(hotelLandingMobRequestBody, DependencyLayer.CLIENTGATEWAY);
            //mobLandingUrl = RestURLHelper.getDestinationUrl(mobLandingUrl);
            String relativeUrl = Utility.getcompleteURL(mobLandingUrl, paramMap, hotelLandingMobRequestBody.getCorrelationKey());
            LOGGER.debug(request);
            LOGGER.debug(relativeUrl);
            String result = restConnectorUtil.performMobLandingPost(request, headerMap, relativeUrl);
            LOGGER.debug(result);
            return result;
        });
    }
    
    public String getLatLngFromGooglePlaceId(String placeId,Double lat, Double lng) throws ClientGatewayException{

    	Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");

		String apiURL = null;
		if (StringUtils.isNotEmpty(placeId)) {
			try {
                //placeToLatLngURL = RestURLHelper.getDestinationUrl(placeToLatLngURL);
				apiURL = String.format(placeToLatLngURL,URLEncoder.encode(placeId, "UTF-8"));
			} catch (UnsupportedEncodingException e) {
				LOGGER.error("Exception in getLatLngFromGooglePlaceId", e);
				throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.UNEXPECTED, "", e.getMessage());
			}
			LOGGER.warn("request param queryString = {} getCityDataFromLatLngOrPlaceID ", placeId);
		} else {
            //latLngToCityCodeURL = RestURLHelper.getDestinationUrl(latLngToCityCodeURL);
			apiURL = String.format(latLngToCityCodeURL, lat, lng);
			LOGGER.warn("request param queryString = {},{} getCityDataFromLatLngOrPlaceID ", lat, lng);
		}
		
		return restConnectorUtil.getLatLngFromGooglePlaceId(headerMap, apiURL);
    }

    public String listPersonalizedCards(HotelLandingMobRequestBody listPersonalizationRequest,  Map<String, String[]> paramMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(listPersonalizationRequest, DependencyLayer.CLIENTGATEWAY);
        LOGGER.debug(request);
        //listPersonalizedCardURL = RestURLHelper.getDestinationUrl(listPersonalizedCardURL);
        String relativeUrl = Utility.getcompleteURL(listPersonalizedCardURL, paramMap, listPersonalizationRequest.getCorrelationKey());
        LOGGER.debug(relativeUrl);
        return restConnectorUtil.performMobLandingPost(request, headerMap, relativeUrl);
    }

    public Future<Object> getSmartAds(String flavour,String version, String siteDomain, String deviceType, Map<String, Object> paramMap, Map<String, String> headers, String pageContext) throws ClientGatewayException {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return landingServiceThreadPool.submit(() -> {
            if (mdcMap != null) {
                MDC.setContextMap(mdcMap);
            }
            String finalUrl = Utility.buildSmartEngageUrl(smartEngageUrl, flavour, paramMap,version, deviceType,siteDomain,pageContext);
            String result = restConnectorUtil.performSmartEngageGet(null, headers, finalUrl);
            return gson.fromJson(result, Object.class);
        });
    }

    public Future<Object> getPageMakerChunkData(String funnelSource,String flavour,String version, Map<String, String> headers) throws ClientGatewayException {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return landingServiceThreadPool.submit(() -> {
            if (mdcMap != null) {
                MDC.setContextMap(mdcMap);
            }
            String initialUrl;
            if(ANDROID.equalsIgnoreCase(flavour) || DEVICE_IOS.equalsIgnoreCase(flavour)) {
                initialUrl = pageMakerChunkUrl;
            } else{
                initialUrl = pageMakerPersonalizedChunkUrl;
            }
            String finalUrl = Utility.buildPageMakerChundDataUrl(initialUrl, funnelSource, flavour, version);
            String result = restConnectorUtil.performPageMakerDataGet(null, headers, finalUrl);
            return gson.fromJson(result, Object.class);
        });
    }

    public Future<Object> getPageMakerRenderedData(String flavour, String version, Map<String, String> headers) throws ClientGatewayException {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return landingServiceThreadPool.submit(() -> {
            if (mdcMap != null) {
                MDC.setContextMap(mdcMap);
            }
            String url = Utility.buildPagemakerRenderedTemplateUrl(pageMakerRenderedTemplateUrl,flavour,version);
            String result = restConnectorUtil.performPageMakerDataGet(null, headers, url);
            return gson.fromJson(result, Object.class);
        });
    }

}
