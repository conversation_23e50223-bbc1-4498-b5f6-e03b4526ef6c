package com.mmt.hotels.clientgateway.restexecutors;

import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.HeaderConstants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class OrchDetailExecutor {

    private static final Logger logger = LoggerFactory.getLogger(OrchDetailExecutor.class);

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    MetricAspect metricAspect;

    @Value("${orch.search.rooms.url}")
    private String orchSearchRooms;

    @Value("${orch.static.details.url}")
    private String orchStaticDetailsUrl;

    @Value("${orch.update.price.url}")
    private String orchUpdatePrice;

    @Value("${orch.calendar.availability.url}")
    private String calendarAvailabilityUrl;

    public HotelDetailsResponse searchRooms(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Convert the detail request to JSON
            String requestJson = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);

            // Build the complete URL
            String region = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getSiteDomain().getName().toUpperCase() : Constants.DEFAULT_SITE_DOMAIN;
            String requestId = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getRequestId() : null;
            String country = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getCountry().getName() : null;
            String trafficSource = detailRequest.getClientDetails() != null && detailRequest.getClientDetails().getRequestDetails().getTrafficType().getName() != null ? detailRequest.getClientDetails().getRequestDetails().getTrafficType().name() : "B2C";
            String funnelSource = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getFunnelSource().name() : "";
            String journeyId = detailRequest.getClientDetails() != null && detailRequest.getClientDetails().getRequestDetails().getJourneyId() != null ? detailRequest.getClientDetails().getRequestDetails().getJourneyId() : "";
            Map<String, String[]> parameterMapNew =  new HashMap<>(parameterMap);
            parameterMapNew.put(HeaderConstants.COUNTRY, new String[]{country});
            parameterMapNew.put(HeaderConstants.REQUEST_ID, new String[]{requestId});
            parameterMapNew.put(HeaderConstants.REGION, new String[]{region});
            parameterMapNew.put(HeaderConstants.FUNNEL, new String[]{funnelSource});
            parameterMapNew.put(HeaderConstants.TRAFFIC_SOURCE, new String[]{trafficSource});
            parameterMapNew.put(HeaderConstants.JOURNEY_ID, new String[]{journeyId});

            String requestUrl = Utility.getCompleteUrl(orchSearchRooms, parameterMapNew);

            // Log the request details
            logger.debug("Request Body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator search rooms post request
            String responseJson = restConnectorUtil.performOrchestratorSearchRoomsPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Response: {}", responseJson);

            // Convert the response JSON to ListingResponse object
            HotelDetailsResponse detailResponse = objectMapperUtil.getObjectFromJson(responseJson, HotelDetailsResponse.class, DependencyLayer.ORCHESTRATOR_NEW);


            // Check for errors in the response
            if (detailResponse != null && detailResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        detailResponse.getError().getCode(), detailResponse.getError().getMessage(), detailResponse.getError().getDescription());
            }
            return detailResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchSearchRooms", new Date().getTime() - startTime);
        }
    }

    public UpdatePriceResponse updatePrice(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Null pointer check for detailRequest
            if (detailRequest == null) {
                logger.error("DetailRequest is null in updatePrice method");
                throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, "INVALID_REQUEST", "DetailRequest cannot be null");
            }

            // Convert the request to JSON
            String requestJson = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);

            // Build the complete URL for update price with null-safe parameter extraction
            String region = Constants.DEFAULT_SITE_DOMAIN;
            String requestId = null;
            String country = null;
            String trafficSource = "B2C";
            String funnelSource = "";
            String journeyId = "";

            // Safe navigation for nested object access
            if (detailRequest.getClientDetails() != null &&
                    detailRequest.getClientDetails().getRequestDetails() != null) {

                com.gommt.hotels.orchestrator.detail.model.state.RequestDetails requestDetails = detailRequest.getClientDetails().getRequestDetails();

                // Safe extraction of region
                if (requestDetails.getSiteDomain() != null && requestDetails.getSiteDomain().getName() != null) {
                    region = requestDetails.getSiteDomain().getName().toUpperCase();
                }

                // Safe extraction of requestId
                requestId = requestDetails.getRequestId();

                // Safe extraction of country
                if (requestDetails.getCountry() != null && requestDetails.getCountry().getName() != null) {
                    country = requestDetails.getCountry().getName();
                }

                // Safe extraction of trafficSource
                if (requestDetails.getTrafficType() != null && requestDetails.getTrafficType().getName() != null) {
                    trafficSource = requestDetails.getTrafficType().name();
                }

                // Safe extraction of funnelSource
                if (requestDetails.getFunnelSource() != null) {
                    funnelSource = requestDetails.getFunnelSource().name();
                }

                // Safe extraction of journeyId
                if (requestDetails.getJourneyId() != null) {
                    journeyId = requestDetails.getJourneyId();
                }
            } else {
                logger.warn("ClientDetails or RequestDetails is null, using default values for URL parameters");
            }

            // Null pointer check for parameterMap
            Map<String, String[]> parameterMapNew;
            if (parameterMap != null) {
                parameterMapNew = new HashMap<>(parameterMap);
            } else {
                logger.warn("ParameterMap is null, creating new empty map");
                parameterMapNew = new HashMap<>();
            }

            // Add parameters with null-safe values
            parameterMapNew.put("country", new String[]{country != null ? country : ""});
            parameterMapNew.put("requestId", new String[]{requestId != null ? requestId : ""});
            parameterMapNew.put("region", new String[]{region});
            parameterMapNew.put("funnel", new String[]{funnelSource});
            parameterMapNew.put("trafficsource", new String[]{trafficSource});
            parameterMapNew.put("journeyid", new String[]{journeyId});

            String requestUrl = Utility.getCompleteUrl(orchUpdatePrice, parameterMapNew);

            // Log the request details
            logger.debug("UpdatePrice Request Body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator update price post request
            String responseJson = restConnectorUtil.performOrchestratorSearchHotelsPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("UpdatePrice Response: {}", responseJson);

            // Null pointer check for response
            if (responseJson == null || responseJson.trim().isEmpty()) {
                logger.error("Received null or empty response from orchestrator updatePrice API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "EMPTY_RESPONSE", "Received null or empty response from orchestrator updatePrice API");
            }

            // Convert the response JSON to PriceBreakDownResponse object
            UpdatePriceResponse updatePriceResponse = objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW);

            // Null pointer check for parsed response
            if (updatePriceResponse == null) {
                logger.error("Failed to parse response from orchestrator updatePrice API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "PARSE_ERROR", "Failed to parse response from orchestrator updatePrice API");
            }

            if (updatePriceResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        updatePriceResponse.getError().getCode(),
                        updatePriceResponse.getError().getMessage(),
                        updatePriceResponse.getError().getDescription());
            }

            return updatePriceResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchUpdatePrice", new Date().getTime() - startTime);
        }
    }

    public HotelStaticContentResponse staticDetails(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Convert the listing request to JSON
            String requestJson = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);

            // Build the complete URL
            String region = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getSiteDomain().getName().toUpperCase() : Constants.DEFAULT_SITE_DOMAIN;
            String requestId = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getRequestId() : null;
            String country = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getCountry().getName() : null;
            String trafficSource = detailRequest.getClientDetails() != null && detailRequest.getClientDetails().getRequestDetails().getTrafficType().getName() != null ? detailRequest.getClientDetails().getRequestDetails().getTrafficType().name() : "B2C";
            String funnelSource = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails().getFunnelSource().name() : "";
            String journeyId = detailRequest.getClientDetails() != null && detailRequest.getClientDetails().getRequestDetails().getJourneyId() != null ? detailRequest.getClientDetails().getRequestDetails().getJourneyId() : "";
            Map<String, String[]> parameterMapNew =  new HashMap<>(parameterMap);
            parameterMapNew.put("country", new String[]{country});
            parameterMapNew.put("requestId", new String[]{requestId});
            parameterMapNew.put("region", new String[]{region});
            parameterMapNew.put("funnel", new String[]{funnelSource});
            parameterMapNew.put("trafficsource", new String[]{trafficSource});
            parameterMapNew.put("journeyid", new String[]{journeyId});

            String requestUrl = Utility.getCompleteUrl(orchStaticDetailsUrl, parameterMapNew);

            // Log the request details
            logger.debug("Request Body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator search hotels post request
            String responseJson = restConnectorUtil.performOrchestratorStaticDetailsPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Response: {}", responseJson);

            // Convert the response JSON to ListingResponse object
            HotelStaticContentResponse hotelStaticContentResponse = objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL);


            // Check for errors in the response
            if (hotelStaticContentResponse != null && hotelStaticContentResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        hotelStaticContentResponse.getError().getCode(),
                        hotelStaticContentResponse.getError().getMessage(),
                        hotelStaticContentResponse.getError().getDescription());
            }

            return hotelStaticContentResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchSearchHotels", new Date().getTime() - startTime);
        }
    }

    public ConsolidatedCalendarAvailabilityResponse getCalendarAvailability(DetailRequest calendarAvailabilityRequest, String correlationKey, Map<String, String[]> parameterMap,
                                                                            Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Accept-Encoding", "gzip");
            headerMap.put("Content-Type", "application/json");
            if (StringUtils.isNotEmpty(headers.get("mmt-auth"))) {
                headerMap.put("mmt-auth", headers.get("mmt-auth"));
            }
            String request = objectMapperUtil.getJsonFromObject(calendarAvailabilityRequest, DependencyLayer.CLIENTGATEWAY);
            //calendarAvailabilityUrl = RestURLHelper.getDestinationUrl(calendarAvailabilityUrl);
            String relativeUrl = Utility.getcompleteURL(calendarAvailabilityUrl, parameterMap, correlationKey);
            logger.debug(request);
            logger.debug(relativeUrl);
            String result = restConnectorUtil.performCalendarAvailabilityPost(request, headerMap, relativeUrl);
            ConsolidatedCalendarAvailabilityResponse response = objectMapperUtil.getObjectFromJson(result, ConsolidatedCalendarAvailabilityResponse.class, DependencyLayer.ORCHESTRATOR);
            if (response != null && CollectionUtils.isNotEmpty(response.getErrorList())) {
                throw new ErrorResponseFromDownstreamException(
                        DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                        response.getErrorList().get(0).getCode(),
                        response.getErrorList().get(0).getMessage()
                );
            }
            return response;
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "calendarAvailability", new Date().getTime() - startTime);
        }
    }

    private Map<String, String> buildHeaderMap(Map<String, String> headers) {
        //Add Headers
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        if (MapUtils.isNotEmpty(headers) && StringUtils.isNotEmpty(headers.get(HeaderConstants.HIDDEN_PARAMS))) {
            headerMap.put(HeaderConstants.HIDDEN_PARAMS, headers.get(HeaderConstants.HIDDEN_PARAMS));
        }
        return headerMap;
    }

}
