package com.mmt.hotels.clientgateway.restexecutors;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;

@Component
public class ByPassExecutor {
	
    @Autowired
    private RestConnectorUtil restConnectorUtil;
	
	public String executeByPassRequest(String requestBody, Map<String,String> httpHeaderMap, String destinationUrl, String correlationKey) 
			throws ClientGatewayException{
		return restConnectorUtil.performPostByPass(requestBody, httpHeaderMap, destinationUrl);
	}
	
	public String executeGetByPassRequest(Map<String,String> httpHeaderMap, String destinationUrl, String correlationKey) 
			throws ClientGatewayException{
		return restConnectorUtil.performGetByPass(httpHeaderMap, destinationUrl);
	}

	public String executeByPassRequestFlyfish(String requestBody, Map<String,String> httpHeaderMap, String destinationUrl, String correlationKey)
			throws Client<PERSON>atewayException{
		return restConnectorUtil.performPostByPass(requestBody, httpHeaderMap, destinationUrl);
	}
}
