package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraUserSegmentRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraLastBookedFlightResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraUserFirstTimeStateResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraUserSegmentResponse;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class HydraExecutor {
	
	private static final Logger logger = LoggerFactory.getLogger(HydraExecutor.class);
	
	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	private RestConnectorUtil restConnectorUtil;

	@Autowired
	private CommonConfigHelper commonConfigHelper;
	 
	@Value("${hydra.url}")
	private String hydraUrl;
	
	@Value("${hydra.user.segment.url}")
	private String hydraUserSegmentUrl;
	
	private static final String REQUESTOR_HEADER = "X-HYDRA-REQUESTOR";
	
	private static final String REQUEST_ID_HEADER = "X-HYDRA-REQUEST-ID";
	
	private static final String APIKEY = "apikey";
	
	private static final String HOTELS_HYDRA_REQUESTOR = "clientbackend";
	

	public HydraLastBookedFlightResponse getHydraLastBookedFlightResponse(HydraRequest hydraRequest, 
			String correlationKey) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Accept", "application/json");
		headerMap.put("Content-Type", "application/json");
		headerMap.put(REQUEST_ID_HEADER, correlationKey);
		headerMap.put(REQUESTOR_HEADER, HOTELS_HYDRA_REQUESTOR);
		headerMap.put(APIKEY, commonConfigHelper.getHydraAPIKey());
		headerMap.put("X-Consumer-name", HOTELS_HYDRA_REQUESTOR);
		String request = objectMapperUtil.getJsonFromObject(hydraRequest, DependencyLayer.CLIENTGATEWAY);
		String result = restConnectorUtil.performLastBookedFlightResponsePost(request, headerMap, hydraUrl);
		if (StringUtils.isNotEmpty(result)) {
			logger.warn("Last flight booked response: {}", result);
		}
		logger.debug("Last flight booked request: {} headers: {} result: {}", request, headerMap, result);
		HydraLastBookedFlightResponse hydraResponse = objectMapperUtil.getObjectFromJson(result, HydraLastBookedFlightResponse.class, 
				DependencyLayer.HYDRA);
		if (hydraResponse != null && MapUtils.isNotEmpty(hydraResponse.getError())) {
			for (String key: hydraResponse.getError().keySet()) {
				throw new ErrorResponseFromDownstreamException(DependencyLayer.HYDRA, ErrorType.DOWNSTREAM,
						key, hydraResponse.getError().get(key));
			}
		}
		return hydraResponse;
	}

	
	public HydraUserSegmentResponse getHydraMatchedSegment(HydraUserSegmentRequest hydraUserSegmentRequest,
			String correlationKey) throws ClientGatewayException{
		if (hydraUserSegmentRequest == null)
			return null;
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Accept", "application/json");
		headerMap.put("Content-Type", "application/json");
		headerMap.put(REQUEST_ID_HEADER, correlationKey);
		headerMap.put(REQUESTOR_HEADER, HOTELS_HYDRA_REQUESTOR);
		headerMap.put(APIKEY, commonConfigHelper.getHydraAPIKey());
		headerMap.put("X-Consumer-name", HOTELS_HYDRA_REQUESTOR);
		String request = objectMapperUtil.getJsonFromObject(hydraUserSegmentRequest, DependencyLayer.CLIENTGATEWAY);
		String result = restConnectorUtil.performHydraMatchedSegment(request, headerMap, hydraUserSegmentUrl);
		logger.debug("Hydra segment request: " + request + " headers: " + headerMap + " result: " + result);
		HydraUserSegmentResponse hydraResponse = null;
		if (StringUtils.isNotEmpty(result)) {
			hydraResponse = objectMapperUtil.getObjectFromJson(result, HydraUserSegmentResponse.class,
					DependencyLayer.HYDRA);
		}
		if (hydraResponse != null && MapUtils.isNotEmpty(hydraResponse.getError()) &&
				hydraResponse.getData() == null) {
			for (String key: hydraResponse.getError().keySet()) {
				throw new ErrorResponseFromDownstreamException(DependencyLayer.HYDRA, ErrorType.DOWNSTREAM,
						key, hydraResponse.getError().get(key));
			}
		}
		if (hydraResponse != null && hydraResponse.getData()== null){
			logger.error("hydraSegments not received at CG from dpt");
		}
		return hydraResponse;
	}
}
