package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.PoliciesRequest;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.response.policy.HotelPolicyResponseEntityBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class PolicyExecutor {

	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private RestConnectorUtil restConnectorUtil;

	@Value("${webapi.getPolicies.url}")
	private String getPoliciesURL;

	public HotelPolicyResponseEntityBO getPolicies(PoliciesRequest policiesRequest) throws ClientGatewayException{
		Map<String, String> headerMap = new HashMap<String, String>();
	    headerMap.put("Content-Type", "application/json");
	    headerMap.put("Accept-Encoding", "gzip");

		//getPoliciesURL = RestURLHelper.getDestinationUrl(getPoliciesURL);
	    String url = String.format(getPoliciesURL, policiesRequest.getCorrelationKey(), policiesRequest.getTxnKey());
	    
		String result = restConnectorUtil.performPoliciesGet(headerMap, url);
		
		HotelPolicyResponseEntityBO response = objectMapperUtil.getObjectFromJson(result, HotelPolicyResponseEntityBO.class, 
				DependencyLayer.ORCHESTRATOR);
		
		if (response != null && response.getResponseErrors() != null
				&& CollectionUtils.isNotEmpty(response.getResponseErrors().getErrorList())) {
			com.mmt.hotels.model.response.errors.Error error = response.getResponseErrors().getErrorList().get(0);
			throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
					error.getErrorCode(), error.getErrorMessage());
		}
		return response;
	}
	
}
