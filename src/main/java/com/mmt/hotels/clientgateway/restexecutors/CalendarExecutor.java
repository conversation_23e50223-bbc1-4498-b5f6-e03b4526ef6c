package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.model.response.CalendarAvailabilityResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class CalendarExecutor {
    private static final Logger logger = LoggerFactory.getLogger(CalendarExecutor.class);

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    MetricAspect metricAspect;

    @Value("${calendar.availability.url}")
    private String calendarAvailabilityUrl;


    public CalendarAvailabilityResponse getCalendarAvailability(PriceByHotelsRequestBody calendarAvailabilityRequest, String correlationKey, Map<String, String[]> parameterMap,
                                                             Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Accept-Encoding", "gzip");
            headerMap.put("Content-Type", "application/json");
            if (StringUtils.isNotEmpty(headers.get("mmt-auth"))) {
                headerMap.put("mmt-auth", headers.get("mmt-auth"));
            }
            String request = objectMapperUtil.getJsonFromObject(calendarAvailabilityRequest, DependencyLayer.CLIENTGATEWAY);
            //calendarAvailabilityUrl = RestURLHelper.getDestinationUrl(calendarAvailabilityUrl);
            String relativeUrl = Utility.getcompleteURL(calendarAvailabilityUrl, parameterMap, correlationKey);
            logger.debug(request);
            logger.debug(relativeUrl);
            String result = restConnectorUtil.performCalendarAvailabilityPost(request, headerMap, relativeUrl);
            CalendarAvailabilityResponse response = objectMapperUtil.getObjectFromJson(result, CalendarAvailabilityResponse.class, DependencyLayer.ORCHESTRATOR);
            if(response != null && response.getResponseErrors() != null && CollectionUtils.isNotEmpty(response.getResponseErrors().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(
                        DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                        response.getResponseErrors().getErrorList().get(0).getErrorCode(),
                        response.getResponseErrors().getErrorList().get(0).getErrorMessage()
                );
            }
            return response;
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "calendarAvailability", new Date().getTime() - startTime);
        }
    }
}
