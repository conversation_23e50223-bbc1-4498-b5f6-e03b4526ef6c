package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class UpdatedPriceExecutor {


    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${updated.price.url}")
    private String updatedPriceUrl;
    
    private static final Logger logger = LoggerFactory.getLogger(UpdatedPriceExecutor.class);

    public PriceBreakDownResponse updatedPrice(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(priceByHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
        //updatedPriceUrl = RestURLHelper.getDestinationUrl(updatedPriceUrl);
        String relativeUrl = Utility.getcompleteURL(updatedPriceUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
        logger.debug(request);
        logger.debug(relativeUrl);

        String result = restConnectorUtil.performUpdatedPricePost(request, headerMap, relativeUrl);
        PriceBreakDownResponse priceBreakDownResponse = objectMapperUtil.getObjectFromJson(result, PriceBreakDownResponse.class, DependencyLayer.ORCHESTRATOR);
        if (priceBreakDownResponse != null && priceBreakDownResponse.getResponseErrors() !=null
        		&& CollectionUtils.isNotEmpty(priceBreakDownResponse.getResponseErrors().getErrorList())) {
        	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
        			priceBreakDownResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
        			priceBreakDownResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return priceBreakDownResponse;
    }
}
