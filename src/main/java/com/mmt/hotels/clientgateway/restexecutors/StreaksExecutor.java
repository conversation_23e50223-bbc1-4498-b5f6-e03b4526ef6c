package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.StreaksUserRequest;
import com.mmt.hotels.clientgateway.response.streaks.balance.StreaksUserEarningResponse;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Map;

@Component
public class StreaksExecutor {
    @Value("${streaks.user.info.url}")
    private String streaksUserInfoUrl;

    @Value("${streaks.user.earning.url}")
    private String streaksUserEarningUrl;
    @Autowired
    private MetricAspect metricAspect;
    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    private CommonHelper commonHelper;


    private static final Logger logger = LoggerFactory.getLogger(StreaksExecutor.class);

    public StreaksUserInfoResponse getStreaksUserInfo( Map<String, String[]> parameterMap, Map<String, String> headers, String correlationId,String uuid) throws ClientGatewayException {
        return getStreaksUserInfoResponse(parameterMap, headers ,correlationId,uuid);
    }

    public StreaksUserInfoResponse getStreaksUserInfoResponse(Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap,String correlationId,String uuid) throws ClientGatewayException {

        String url = Utility.getcompleteURL(String.format(streaksUserInfoUrl, uuid), parameterMap, correlationId);
        logger.debug("Streaks User Earning Url :: {}", url);
        String result = restConnectorUtil.performStreaksInfoGet(httpHeaderMap, url);
        logger.debug("Streaks User Earning Result :: {}" , result);
        StreaksUserInfoResponse response = objectMapperUtil.getObjectFromJson(result, StreaksUserInfoResponse.class,
                DependencyLayer.BABELFISH);

        if (response != null && response.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(response.getResponseErrors().getErrorList())) {
            com.mmt.hotels.model.response.errors.Error error = response.getResponseErrors().getErrorList().get(0);
            throw new ErrorResponseFromDownstreamException(DependencyLayer.BABELFISH, ErrorType.DOWNSTREAM,
                    error.getErrorCode(), error.getErrorMessage());
        }
        return response;
    }


    public StreaksUserEarningResponse getStreaksUserEarning(
            StreaksUserRequest streaksUserRequest, Map<String, String[]> parameterMap, Map<String, String> headers)
            throws ClientGatewayException {
        return getStreaksUserEarningResponse(streaksUserRequest, parameterMap, headers);
    }

    public StreaksUserEarningResponse getStreaksUserEarningResponse(
            StreaksUserRequest userStreaksRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap)
            throws ClientGatewayException {

        String url = Utility.getcompleteURL(String.format(streaksUserEarningUrl, userStreaksRequest.getUuid()), parameterMap, userStreaksRequest.getCorrelationKey());
        logger.debug("Streaks User Earning Url :: {}", url);

        String result = restConnectorUtil.performStreaksUserEarningGet(httpHeaderMap, url);
        StreaksUserEarningResponse response = objectMapperUtil.getObjectFromJson(result, StreaksUserEarningResponse.class,
                DependencyLayer.BABELFISH);
        logger.debug("Streaks User Earning Result :: {}" , result);
        if (response != null && response.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(response.getResponseErrors().getErrorList())) {
            com.mmt.hotels.model.response.errors.Error error = response.getResponseErrors().getErrorList().get(0);
            throw new ErrorResponseFromDownstreamException(DependencyLayer.BABELFISH, ErrorType.DOWNSTREAM,
                    error.getErrorCode(), error.getErrorMessage());
        }
        return response;
    }
}
