package com.mmt.hotels.clientgateway.restexecutors;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.HermesHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.hermes.HermesPriceApiResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.ImageCategoryEntityBO;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.upsell.UpsellHotelRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.model.response.staticdata.HotelsImageResponseEntity;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

@Component
public class SearchRoomsExecutor {


	@Autowired
	@Qualifier("detailServiceThreadPool")
	private ThreadPoolTaskExecutor detailServiceThreadPool;

	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private RestConnectorUtil restConnectorUtil;

	@Autowired
	private MetricAspect metricAspect;

	@Value("${search.rooms.url}")
	private String searchRoomsUrl;

	@Value("${static.rooms.url}")
	private String staticRoomsUrl;

	@Value("${alternate.dates.url}")
	private String alternateDatesPriceUrl;

	private static final Gson gson = new Gson();

	@Value("${comparator.url}")
	private String comparatorUrl;

	private static final Logger logger = LoggerFactory.getLogger(SearchRoomsExecutor.class);
	@Value("${hotel.image.url}")
	private String hotelImageUrl;

	@Autowired
	PricingEngineHelper pricingEngineHelper;

	@Autowired
	HermesHelper hermesHelper;

	public Future<RoomDetailsResponse> getRoomPrices(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers, CountDownLatch countDownLatchAsync) throws ClientGatewayException {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			if (mdcMap != null) {
				MDC.setContextMap(mdcMap);
			}
			Map<String, String> headerMap = new HashMap<String, String>();
			headerMap.put("Content-Type", "application/json");
			headerMap.put("Accept-Encoding", "gzip");
			headerMap.put("srcRequest", "ClientGateway");
			if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
				headerMap.put("mmt-auth", headers.get("mmt-auth"));

			/*ADDING HIDDEN PARAMS FOR ENCRYPTION KEY*/
			if (StringUtils.isNotEmpty(headers.get("x-hidden-params"))) {
				headerMap.put("x-hidden-params", headers.get("x-hidden-params"));
			}

			String request = objectMapperUtil.getJsonFromObject(priceByHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
			//searchRoomsUrl = RestURLHelper.getDestinationUrl(searchRoomsUrl);
			String relativeUrl = Utility.getcompleteURL(searchRoomsUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
			relativeUrl = pricingEngineHelper.appendPEEDInUrl(relativeUrl, priceByHotelsRequestBody.getExperimentData());

			logger.debug(request);
			logger.debug(relativeUrl);

			long startTime = new Date().getTime();
			String result = null;
			try {
				result = restConnectorUtil.performSearchRoomsPost(request, headerMap, relativeUrl);
			} catch (Exception ex) {
				countDownLatchAsync.countDown();
				throw ex;
			} finally {
				metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRooms/searchPrice", new Date().getTime() - startTime);
			}

			logger.debug("Search Rooms Response :{}",result);
			RoomDetailsResponse roomDetailsResponse = objectMapperUtil.getObjectFromJson(result, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
			if (roomDetailsResponse != null && roomDetailsResponse.getResponseErrors() != null &&
					CollectionUtils.isNotEmpty(roomDetailsResponse.getResponseErrors().getErrorList())) {
				logger.error("ErrorResponseFromDownstreamException exception thrown :- ERROR CODE: {} ERROR MESSAGE: {}", roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
						roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
				countDownLatchAsync.countDown();
				throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
						roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
						roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage(),
						roomDetailsResponse.getResponseErrors().getErrorList().get(0).getAlternateMessage());
			}
			countDownLatchAsync.countDown();
			return roomDetailsResponse;
		});
	}

	public Future<HotelsRoomInfoResponseEntity> getRoomStaticDetails(String hotelId, String vcId, String brand,
																	 int totalCandidate, String correlationKey,
																	 Map<String, String[]> parameterMap,
																	 Map<String, String> headers,
																	 CountDownLatch countDownLatchAsync, String pageContext,
																	 AvailRoomsRequest availRoomsRequest, String cityCode) throws ClientGatewayException {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			if (mdcMap != null) {
				MDC.setContextMap(mdcMap);
			}
			HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = null;
			if  (!Constants.PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext) || (availRoomsRequest!=null && availRoomsRequest.getFeatureFlags() != null && availRoomsRequest.getFeatureFlags().isRoomInfoRequired())) {
				Map<String, String> headerMap = new HashMap<String, String>();
				headerMap.put("Accept-Encoding", "gzip");
				headerMap.put("srcRequest", "ClientGateway");
				if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
					headerMap.put("mmt-auth", headers.get("mmt-auth"));
				Map<String, String> params = new HashMap<>();
				params.put("hotelIds", new JSONArray(Collections.singletonList(hotelId)).toString());
				params.put("totalCandidates", String.valueOf(totalCandidate));
				params.put("vcId", vcId);
				params.put("brand", brand);
				//staticRoomsUrl = RestURLHelper.getDestinationUrl(staticRoomsUrl);
				if (StringUtils.isNotBlank(cityCode))
					params.put("cityCode", cityCode);
				StringBuilder sb = new StringBuilder(staticRoomsUrl);
				if (StringUtils.isNotBlank(cityCode))
					params.put("cityCode", cityCode);
				sb.append("?");
				if (null != params) {
					for (Map.Entry<String, String> entry : params.entrySet()) {
						if (entry.getKey() != null && entry.getValue() != null) {
							sb.append("&");
							sb.append(entry.getKey());
							sb.append("=");
							try {
								sb.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
							} catch (UnsupportedEncodingException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}
					}
				}

				String relativeUrl = Utility.getcompleteURL(sb.toString(), parameterMap, correlationKey);
				relativeUrl = Utility.appendPageContextToURL(relativeUrl, pageContext);
				logger.debug(relativeUrl);

				long startTime = new Date().getTime();
				String result = null;
				try {
					result = restConnectorUtil.performStaticRoomDetailGet(headerMap, relativeUrl);
				} catch (Exception ex) {
					countDownLatchAsync.countDown();
					throw ex;
				} finally {
					metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRooms/staticDetails", new Date().getTime() - startTime);
				}
				 hotelsRoomInfoResponseEntity = objectMapperUtil.getObjectFromJson(result, HotelsRoomInfoResponseEntity.class, DependencyLayer.ORCHESTRATOR);
				if (hotelsRoomInfoResponseEntity != null && hotelsRoomInfoResponseEntity.getResponseErrors() != null &&
						CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getResponseErrors().getErrorList())) {
					countDownLatchAsync.countDown();
					throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
							hotelsRoomInfoResponseEntity.getResponseErrors().getErrorList().get(0).getErrorCode(),
							hotelsRoomInfoResponseEntity.getResponseErrors().getErrorList().get(0).getErrorMessage());
				}
			}
			countDownLatchAsync.countDown();
			return hotelsRoomInfoResponseEntity;
		});
	}

	public String getRoomPricesOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

		String request = objectMapperUtil.getJsonFromObject(priceByHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
		//searchRoomsUrl = RestURLHelper.getDestinationUrl(searchRoomsUrl);
		String relativeUrl = Utility.getcompleteURL(searchRoomsUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
		relativeUrl = pricingEngineHelper.appendPEEDInUrl(relativeUrl, priceByHotelsRequestBody.getExperimentData());
		logger.debug(request);
		logger.debug(relativeUrl);

		String response= restConnectorUtil.performSearchRoomsPost(request, headerMap, relativeUrl);
		RoomDetailsResponse responseObject  =objectMapperUtil.getObjectFromJson(response, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
		if(null!=responseObject){
			response = objectMapperUtil.getJsonFromObjectWithView(responseObject, DependencyLayer.ORCHESTRATOR, PIIView.class);
		}else{
			response=null;
		}
		return response;
	}

	public  <T> T getComparatorOld(UpsellHotelRequest upsellHotelRequest, Map<String, String[]> parameterMap, Map<String, String> headers, Class<T> type) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

		String request = objectMapperUtil.getJsonFromObject(upsellHotelRequest, DependencyLayer.CLIENTGATEWAY);
		//comparatorUrl = RestURLHelper.getDestinationUrl(comparatorUrl);
		String relativeUrl = Utility.getcompleteURL(comparatorUrl,parameterMap,upsellHotelRequest.getCorrelationKey());
		logger.debug(request);
		logger.debug(relativeUrl);

		String result =restConnectorUtil.performSearchRoomsPost(request, headerMap, relativeUrl);
		if(type != String.class)
			return objectMapperUtil.getObjectFromJson(result, type, DependencyLayer.ORCHESTRATOR);
		else
			return (T)result;
	}
	
	public String alternateDatesPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));
		//alternateDatesPriceUrl = RestURLHelper.getDestinationUrl(alternateDatesPriceUrl);
		String relativeUrl = Utility.getcompleteURL(alternateDatesPriceUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
		String request = objectMapperUtil.getJsonFromObject(priceByHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
		logger.debug(request);
		logger.debug(relativeUrl);

		return restConnectorUtil.performAlternateDatesPost(request, headerMap, relativeUrl);
	}

	public Future<HotelImage> getHotelImages(SearchRoomsRequest searchRoomsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CountDownLatch countDownLatchAsync, String pageContext) {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			if (mdcMap != null) {
				MDC.setContextMap(mdcMap);
			}
		Map<String, String> params = new HashMap<>();
		String MMTorGIHotelId = Utility.getMMTorGIHotelId(searchRoomsRequest.getSearchCriteria().getHotelId(), searchRoomsRequest.getSearchCriteria().getGiHotelId());
		params.put("hotelIds", new JSONArray(Collections.singletonList(MMTorGIHotelId)).toString());
		params.put("networkType", searchRoomsRequest.getDeviceDetails().getNetworkType());
		params.put("imageType", "[\"professional\"]");

		List<ImageCategoryEntityBO> imageCategoriesList = new ArrayList<>();
		ImageCategoryEntityBO imageCategory = new ImageCategoryEntityBO();
		imageCategory.setCount(100);
		imageCategory.setCategory("R");
		imageCategoriesList.add(imageCategory);

		params.put("imageCategory", new JSONArray(imageCategoriesList).toString());
		params.put("isThumbnailRequired", "true");
		params.put("countryCode", searchRoomsRequest.getSearchCriteria().getCountryCode());
		params.put("vcId", searchRoomsRequest.getSearchCriteria().getVcId());
		params.put("brand", searchRoomsRequest.getRequestDetails().getBrand());

		if (searchRoomsRequest.getExpData() != null) {
			Type type = new TypeToken<Map<String, String>>() {
			}.getType();
			String expData = searchRoomsRequest.getExpData().replaceAll("^\"|\"$", "");
			Map<String, String> expDataMap = gson.fromJson(expData, type);
			if (expDataMap != null) {
				String imgSeq = expDataMap.get("HIS");
				if (imgSeq != null) {
					params.put("imgSeq", imgSeq);
				}
			}
		}
		//hotelImageUrl = RestURLHelper.getDestinationUrl(hotelImageUrl);
		StringBuilder sb = new StringBuilder(hotelImageUrl);
		sb.append("?");
		if (null != params) {
			for (Map.Entry<String, String> entry : params.entrySet()) {
				if (entry.getKey() != null && entry.getValue() != null) {
					sb.append("&");
					sb.append(entry.getKey());
					sb.append("=");
					try {
						sb.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
					} catch (UnsupportedEncodingException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
		String url = Utility.getcompleteURL(sb.toString(),parameterMap,searchRoomsRequest.getCorrelationKey());
		url = Utility.appendPageContextToURL(url, pageContext);
		long startTime = new Date().getTime();
		String result = null;
		try {
			result = restConnectorUtil.performStaticRoomDetailGet(httpHeaderMap, url);
		} catch (RestConnectorException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRooms/roomImages", new Date().getTime() - startTime);
		}
		try {
			HotelsImageResponseEntity response = objectMapperUtil.getObjectFromJson(result, HotelsImageResponseEntity.class, DependencyLayer.ORCHESTRATOR);

			if (CollectionUtils.isNotEmpty(response.getImages())) {
				countDownLatchAsync.countDown();
				return response.getImages().get(0);
			} else {
				countDownLatchAsync.countDown();
				return null;
			}
		} catch (JsonParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		countDownLatchAsync.countDown();
		return null;
		});
	}

	public Future<HermesPriceApiResponse> getHermesPriceApiResponse(SearchRoomsRequest searchRoomsRequest,
																	CountDownLatch countDownLatchAsync,
																	Map<String, String> httpHeaderMap) {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			if (mdcMap != null) {
				MDC.setContextMap(mdcMap);
			}
			String hermesDetailPriceEndPoint = hermesHelper.getHermesDetailPriceEndPoint(searchRoomsRequest);
			long startTime = new Date().getTime();
			try {

				logger.debug(hermesDetailPriceEndPoint);
				String hermesResponse = restConnectorUtil.performHermesDetailPriceGet(
						httpHeaderMap, hermesDetailPriceEndPoint);
				metricAspect.addToTime(DependencyLayer.HERMES.name(), "hermes/detailPrice",
						new Date().getTime() - startTime);
				HermesPriceApiResponse hermesPriceApiResponse = objectMapperUtil.getObjectFromJson(
						hermesResponse, HermesPriceApiResponse.class, DependencyLayer.HERMES);
				logger.warn("hermes Price Api Response :{}", objectMapperUtil.getJsonFromObject(hermesPriceApiResponse, DependencyLayer.HERMES));
				return hermesPriceApiResponse;
			} catch (JsonParseException jsonParseException) {
				logger.error("ERROR during parsing hermes response for end point:: {}, exception message:: {}",
						hermesDetailPriceEndPoint, jsonParseException.toString());
				throw jsonParseException;
			} catch (Exception ex) {
				logger.error("EXCEPTION during calling hermes for detail price:: {} ", ex.toString());
				throw new ErrorResponseFromDownstreamException(DependencyLayer.HERMES, ErrorType.CONNECTIVITY,
						"Unable to fetch hermes rate plans");
			} finally {
				countDownLatchAsync.countDown();
			}
		});
	}

	public void setSearchRoomsUrl(String searchRoomsUrl) {
		this.searchRoomsUrl = searchRoomsUrl;
	}
}
