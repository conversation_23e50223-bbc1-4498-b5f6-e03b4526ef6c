package com.mmt.hotels.clientgateway.restexecutors;

import com.gommt.hotels.orchestrator.model.request.listing.ListingRequest;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class OrchSearchHotelsExecutor {

    private static final Logger logger = LoggerFactory.getLogger(OrchSearchHotelsExecutor.class);

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    MetricAspect metricAspect;

    @Value("${orch.search.hotels.url}")
    private String orchSearchHotels;

    public ListingResponse searchHotels(ListingRequest listingRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Convert the listing request to JSON
            String requestJson = objectMapperUtil.getJsonFromObject(listingRequest, DependencyLayer.CLIENTGATEWAY);

            // Build the complete URL
            String region = listingRequest.getClientDetails() != null ? listingRequest.getClientDetails().getRequestDetails().getSiteDomain().getName().toUpperCase() : Constants.DEFAULT_SITE_DOMAIN;
            String requestId = listingRequest.getClientDetails() != null ? listingRequest.getClientDetails().getRequestDetails().getRequestId() : null;
            String country = listingRequest.getClientDetails() != null ? listingRequest.getClientDetails().getRequestDetails().getCountry().getName() : null;
            parameterMap.put("country", new String[]{country});
            parameterMap.put("requestId", new String[]{requestId});
            parameterMap.put("region", new String[]{region});
            String requestUrl = Utility.getCompleteUrl(orchSearchHotels, parameterMap);

            // Log the request details
            logger.debug("Request Body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator search hotels post request
            String responseJson = restConnectorUtil.performOrchestratorSearchHotelsPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Response: {}", responseJson);

            // Convert the response JSON to ListingResponse object
            ListingResponse listingResponse = objectMapperUtil.getObjectFromJson(responseJson, ListingResponse.class, DependencyLayer.ORCHESTRATOR_NEW);

            // Check for errors in the response
            if (listingResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        listingResponse.getError().getCode(),
                        listingResponse.getError().getMessage(),
                        listingResponse.getError().getDescription());
            }

            return listingResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchSearchHotels", new Date().getTime() - startTime);
        }
    }

    private Map<String, String> buildHeaderMap(Map<String, String> headers) {
        //Add Headers
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");

        //TODO uncomment if needed
//    headerMap.put("srcRequest", "ClientGateway");
//    if (StringUtils.isNotEmpty(headers.get("mmt-auth"))) {
//        headerMap.put("mmt-auth", headers.get("mmt-auth"));
//    }
//    String akmaiHeader = headers.get(Constants.HEADER_AKAMAI);
//    if (StringUtils.isEmpty(akmaiHeader)) {
//        akmaiHeader = headers.get(Constants.HEADER_AKAMAI.toLowerCase());
//    }
//    if (StringUtils.isNotEmpty(akmaiHeader)) {
//        headerMap.put(Constants.HEADER_AKAMAI.toLowerCase(), akmaiHeader);
//    }
        return headerMap;
    }
}
