package com.mmt.hotels.clientgateway.businessobjects;

import lombok.Data;

import java.util.LinkedHashMap;

@Data
public class FilterConfigCategory {
    private LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups;
    private String title;
    private String subTitle;
    private String appliedTitle;
    private boolean showMore;
    private Integer minItemsToShow;
    private boolean visible;
    private String viewType;
    private boolean showCustomRange;
    private String customRangeTitle;
    private LinkedHashMap<String, String> condition;
    private String iconUrl;
    private boolean showImageUrl;
    private boolean singleSelection;

}
