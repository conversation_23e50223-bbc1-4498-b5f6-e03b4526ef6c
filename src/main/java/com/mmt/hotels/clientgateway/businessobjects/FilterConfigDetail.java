package com.mmt.hotels.clientgateway.businessobjects;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FilterConfigDetail {

    private String title;
    private String subTitle;
    private String appliedTitle;
    private boolean preApplied;
    private String imageUrl;
    private List<String> iconList;
    private String description; //added to show message along with toggle filter HTL-38235
    private String country; // Country will be set to "DOM"/"INTL" in PMS config, if a filter is only required for Domestic/International. Will be empty if no such conditions are required.
}
