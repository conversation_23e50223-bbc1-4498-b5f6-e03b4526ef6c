package com.mmt.hotels.clientgateway.businessobjects;

import com.mmt.hotels.clientgateway.request.FilterRules;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.model.request.UserLocation;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class CommonModifierResponse {
	private ExtendedUser extendedUser;
	private HydraResponse hydraResponse;
	private List<FilterRules> filterRules;
	private String cdfContextId;
	private boolean categoryRequired;
	private int numberOfCoupons;
	private String domain;
	private boolean cityTaxExclusive;
	private boolean greenHornNewHotelFlag;
	private String mmtAuth;
	private String mobile;
	private String affiliateId;
    private String mcId;
    private int applicationId;
    private String expData;
	private LinkedHashMap<String, String> expDataMap;
	private String variantKey;
	private Map<String, String> manthanExpDataMap;
	private String brand;
	private GoCashExpData goCashExpData;
	private StreaksUserInfoResponse streaksUserInfoResponse;
	private UserLocation userLocation;
	private String appVersionIntGi;
	private String flavour;
	private boolean isSkipRoomSelectEnabled;
	private String funnelSource;
	private String trafficSource;
}
