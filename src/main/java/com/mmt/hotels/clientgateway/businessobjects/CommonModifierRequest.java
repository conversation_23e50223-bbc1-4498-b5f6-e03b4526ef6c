package com.mmt.hotels.clientgateway.businessobjects;

import lombok.Data;

import java.util.Map;

@Data
public class CommonModifierRequest {
    private String mmtAuth;
    private String region;
    private String bookingDevice;
    private String correlationKey;
    private String pageContext;
    private String channel;
    private String corpAuthCode;
    private String uuid;
    private String cityCode;
    private String locationId;
    private String idContext;
    private String appVersion;
    private String mcid;
    private String deviceId;
    private String visitorId;
    private String expData;
    private Integer apWindow;
    private Integer los;
    private String currency;
    private String countryCode;
    private String trafficSource;
    private String checkInDate;
    private Map<String, String> manthanExpDataMap;
    private Integer roomCount;
    private Integer adultCount;
    private String crossLob;
    private int flyerThresholdLoggedout;

}
