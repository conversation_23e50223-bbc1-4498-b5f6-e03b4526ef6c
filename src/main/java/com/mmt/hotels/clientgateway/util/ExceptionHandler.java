package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.*;
import com.mmt.scrambler.exception.ScramblerClientException;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;

public class ExceptionHandler {

	public static ExceptionHandlerResponse handleException(Throwable throwable){
		ClientGatewayException clientGatewayException = null;
		if (throwable instanceof ValidationException) {
			clientGatewayException = (ClientGatewayException) throwable;
			clientGatewayException.setHttpStatusCode(HttpStatus.BAD_REQUEST);
		}else if (throwable instanceof AuthenticationException) {
			clientGatewayException = (ClientGatewayException) throwable;
			clientGatewayException.setHttpStatusCode(HttpStatus.FORBIDDEN);
		}else if (throwable instanceof ErrorResponseFromDownstreamException) {
			clientGatewayException = (ClientGatewayException) throwable;
			clientGatewayException.setHttpStatusCode(HttpStatus.OK);
		}else if(throwable instanceof OTPAuthenticationException) {
			clientGatewayException = (ClientGatewayException) throwable;
			clientGatewayException.setHttpStatusCode(HttpStatus.OK);
		}else if(throwable instanceof LogicalException){
			clientGatewayException = (ClientGatewayException)throwable;
			clientGatewayException.setHttpStatusCode(HttpStatus.OK);
		} else if (throwable instanceof ClientGatewayException) {
			clientGatewayException = (ClientGatewayException) throwable;
			clientGatewayException.setHttpStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
		} else if (throwable != null && throwable.getCause() != null && throwable.getCause() instanceof  ClientGatewayException){
//			Exception was nested in throwable, hence used that nested exception to check if it is instance of ClientGatewayException
			clientGatewayException = (ClientGatewayException) throwable.getCause();
			clientGatewayException.setHttpStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
		}else {
			clientGatewayException = new ClientGatewayException();
			clientGatewayException.setDependencyLayer(DependencyLayer.CLIENTGATEWAY);
			clientGatewayException.setErrorType(ErrorType.UNEXPECTED);
			clientGatewayException.setCode(UnexpectedErrors.INTERNAL_SERVER_ERROR.getErrorCode());
			clientGatewayException.setMessage(UnexpectedErrors.INTERNAL_SERVER_ERROR.getErrorMsg());
			clientGatewayException.setHttpStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
		}

		MetricError metricError = new MetricError(clientGatewayException.getDependencyLayer(),
				clientGatewayException.getErrorType(), clientGatewayException.getCode(),
				clientGatewayException.getMessage());
		return new ExceptionHandlerResponse(metricError, clientGatewayException);
	}
}
