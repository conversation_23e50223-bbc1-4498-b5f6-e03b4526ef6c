package com.mmt.hotels.clientgateway.util;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;

import java.util.Map;
import java.util.UUID;

import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_SOURCE;
import static com.mmt.hotels.clientgateway.constants.Constants.SEARCH_ROOMS_END_POINT;

@Component
public class RequestHandler {
	
	@Autowired
	private MetricErrorLogger metricErrorLogger;
	
	private static final Logger logger = LoggerFactory.getLogger(RequestHandler.class);
	
	public void validatHeadersAndCreateMDC(HttpServletRequest httpServletRequest, String client, 
			BaseRequest baseRequest, String correlationKey) throws ClientGatewayException {
		String tid = httpServletRequest.getHeader("tid");
		logger.warn("tid received: {} client: {} correlationKey: {}", tid, client, correlationKey);
		try {
			CustomValidator.validate(tid, client);
			baseRequest.setCorrelationKey(correlationKey);
			baseRequest.setClient(client);
		} catch (Exception e) {
			logger.error("error occurred in validateHeadersAndCreateMDC: " + e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public Tuple<String,Map<String,String>> handleCommonRequest(HttpServletRequest httpRequest, HttpServletResponse httpResponse, String correlationKey, String client , String idContext, String controller, BaseSearchRequest request){
		if (StringUtils.isEmpty(correlationKey)) {
			correlationKey = UUID.randomUUID().toString();
		}
		org.jboss.logging.MDC.put(MDCHelper.MDCKeys.CORRELATION.getStringValue(), correlationKey);
		Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
		logger.debug("Headers are : {}", httpHeaderMap);

		String region = httpHeaderMap.containsKey(Constants.REGION) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.REGION)) ? httpHeaderMap.get(Constants.REGION) : Constants.DEFAULT_SITE_DOMAIN;
		String language = httpHeaderMap.containsKey(Constants.LANGUAGE)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.LANGUAGE)) ? httpHeaderMap.get(Constants.LANGUAGE) : Constants.DEFAULT_LANGUAGE;
		String currency = httpHeaderMap.containsKey(Constants.CURRENCY)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.CURRENCY)) ? httpHeaderMap.get(Constants.CURRENCY) : Constants.DEFAULT_CUR_INR;
		String funnelSource = null;
		if (httpRequest.getParameterMap() != null && httpRequest.getParameterMap().get(FUNNEL_SOURCE) != null && httpRequest.getParameterMap().get(FUNNEL_SOURCE)[0] != null)
			funnelSource = httpRequest.getParameterMap().get(FUNNEL_SOURCE)[0];

		String country = "";
		if(SEARCH_ROOMS_END_POINT.equalsIgnoreCase(controller) && request instanceof SearchRoomsRequest && ((SearchRoomsRequest) request).getSearchCriteria()!=null) {
			country = ((SearchRoomsRequest) request).getSearchCriteria().getCountryCode();
		}
		MDCHelper.createMDC(client, null, correlationKey, region.toUpperCase(), language, currency.toUpperCase(), controller, idContext,
				request != null && request.getRequestDetails() != null && request.getRequestDetails().getTrafficSource() != null ?
						request.getRequestDetails().getTrafficSource().getType() : StringUtils.EMPTY, funnelSource, country);
		if (request != null && request.getRequestDetails() != null){
			request.getRequestDetails().setSiteDomain(region.toUpperCase());
			updateRequestForMetaGi(request);
			updateRequestForSEM(request);
		}


		HeadersUtil.prepareHttpServletResponsefromMap(httpHeaderMap, httpResponse);
		httpResponse.addHeader(Constants.CORRELATIONKEY, correlationKey);
		return new Tuple<>(correlationKey,httpHeaderMap);
	}

	private void updateRequestForSEM(BaseSearchRequest request) {
		if(request.getRequestDetails().getTrafficSource() != null
				&& Constants.CMP.equalsIgnoreCase(request.getRequestDetails().getTrafficSource().getType())
				&& StringUtils.isNotBlank(request.getRequestDetails().getTrafficSource().getSource())){
			if (request.getRequestDetails().getTrafficSource().getSource().toLowerCase().startsWith(Constants.TRAFFIC_SOURCE_SEM_PREFIX)) {
				request.getRequestDetails().getTrafficSource().setSource(Constants.TRAFFIC_SOURCE_SEM);
			}
		}
	}

	private void updateRequestForMetaGi(BaseSearchRequest request) {
		if(request.getRequestDetails().getTrafficSource() != null
				&& Constants.CMP.equalsIgnoreCase(request.getRequestDetails().getTrafficSource().getType())
				&& StringUtils.isNotBlank(request.getRequestDetails().getTrafficSource().getSource())){
			if (request.getRequestDetails().getTrafficSource().getSource().toLowerCase().contains(Constants.TRAFFIC_SOURCE_HPA)) {
				request.getRequestDetails().getTrafficSource().setSource(Constants.TRAFFIC_SOURCE_HPA);
			} else if (request.getRequestDetails().getTrafficSource().getSource().toLowerCase().contains(Constants.TRAFFIC_SOURCE_TA)) {
				request.getRequestDetails().getTrafficSource().setSource(Constants.TRAFFIC_SOURCE_TA);
			} else if (request.getRequestDetails().getTrafficSource().getSource().toLowerCase().contains(Constants.TRAFFIC_SOURCE_TRV)) {
				request.getRequestDetails().getTrafficSource().setSource(Constants.TRAFFIC_SOURCE_TRV);
			} else if (request.getRequestDetails().getTrafficSource().getSource().toLowerCase().contains(Constants.TRAFFIC_SOURCE_SKYSCN)) {
				request.getRequestDetails().getTrafficSource().setSource(Constants.TRAFFIC_SOURCE_SKYSCN);
			}
		}
	}

	public Tuple<String,Map<String,String>> handleCommonRequest(HttpServletRequest httpRequest, HttpServletResponse httpResponse, String correlationKey, String client , String idContext, String controller){
		if (StringUtils.isEmpty(correlationKey)) {
			logger.debug("correlationKey is missing for : {}", MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			correlationKey = UUID.randomUUID().toString();
		}
		Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
		String region = httpHeaderMap.containsKey(Constants.REGION) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.REGION)) ? httpHeaderMap.get(Constants.REGION) : Constants.DEFAULT_SITE_DOMAIN;
		String language = httpHeaderMap.containsKey(Constants.LANGUAGE)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.LANGUAGE)) ? httpHeaderMap.get(Constants.LANGUAGE) : Constants.DEFAULT_LANGUAGE;
		String currency = httpHeaderMap.containsKey(Constants.CURRENCY)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.CURRENCY)) ? httpHeaderMap.get(Constants.CURRENCY) : Constants.DEFAULT_CUR_INR;

		MDCHelper.createMDC(client, null, correlationKey, region.toUpperCase(), language, currency.toUpperCase(), controller, idContext, StringUtils.EMPTY,null, null);

		HeadersUtil.prepareHttpServletResponsefromMap(httpHeaderMap, httpResponse);
		httpResponse.addHeader(Constants.CORRELATIONKEY, correlationKey);
		return new Tuple<>(correlationKey,httpHeaderMap);
	}

	public String effectiveCorrelationKey(String requestId, String correlationKey) {
		if (!StringUtils.isEmpty(requestId)) {
			return requestId;
		}
		return correlationKey;
	}

	public void validateHotelId(StaticDetailRequest staticDetailRequest) throws ClientGatewayException {
		try {
			CustomValidator.validateMMTorGIHotelId(staticDetailRequest.getSearchCriteria().getHotelId(), staticDetailRequest.getSearchCriteria().getGiHotelId());
		} catch (Exception e) {
			logger.error("error occurred in validateMMTorGIHotelId: " + e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public void validateHotelIdSearchRoom(SearchRoomsRequest searchRoomsRequest) throws ClientGatewayException  {
		try {
			CustomValidator.validateMMTorGIHotelId(searchRoomsRequest.getSearchCriteria().getHotelId(), searchRoomsRequest.getSearchCriteria().getGiHotelId());
		} catch (Exception e) {
			logger.error("error occurred in validateMMTorGIHotelId: " + e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public void validateHotelIdsListAlternateDates(PriceByHotelsRequestBody priceByHotelsRequestBody) throws ClientGatewayException  {
		try {
			CustomValidator.validateMMTorGIHotelIdList(priceByHotelsRequestBody.getHotelIds(), priceByHotelsRequestBody.getGiHotelIds());
		} catch (Exception e) {
			logger.error("error occurred in validateMMTorGIHotelIdList: " + e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public void validateHotelIdTaggedMedia(String mmtHotelID, String voyagerId) throws ClientGatewayException {
		try {
			CustomValidator.validateMMTorGIHotelId(mmtHotelID, voyagerId);
		} catch (Exception e) {
			logger.error("error occurred in validateHotelIdTaggedMedia: " + e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public void validateAvailRequest(AvailRoomsRequest availRoomsRequest) throws ClientGatewayException {
		try {
			// GI hotel id and Vcid must be present in availRooms request as HES passes it to booker.
			// If the above parameters are not present in request, there will be nav push failure alerts.
			if (availRoomsRequest != null && availRoomsRequest.getSearchCriteria() != null) {
				CustomValidator.validateGIHotelId(availRoomsRequest.getSearchCriteria().getGiHotelId());
				CustomValidator.validateCityId(availRoomsRequest.getSearchCriteria().getVcId());
			}
		} catch (Exception e) {
			logger.error("error occurred in validateAvailRequest: " + e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}
}