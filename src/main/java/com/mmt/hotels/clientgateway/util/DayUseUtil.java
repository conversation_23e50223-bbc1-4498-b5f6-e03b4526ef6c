package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.NINE;

@Component
public class DayUseUtil {

	// HTL-37846 => dont send price detail or slashed price for day use if it is less than hourly prices 3h/6h/9h
	public boolean shouldSetPriceDetailsForDayUse(List<SlotDetail> slotDetails, DisplayFare displayFare) {
		if (CollectionUtils.isNotEmpty(slotDetails) && displayFare != null) {
			for (SlotDetail slotDetail : slotDetails) {
				if (displayFare.getSlashedPrice() != null && slotDetail != null && !isSlashedPriceGreaterThanHourlyPrice(slotDetail, displayFare.getSlashedPrice().getSellingPriceWithTax())) {
					return false;
				}
			}
		}
		return true;
	}

	private boolean isSlashedPriceGreaterThanHourlyPrice(SlotDetail slotDetail, double slashedPrice) {
		if(slotDetail != null && slotDetail.getPriceDetail() == null)
			return true;
		return (slotDetail.getPriceDetail().getDiscountedPriceWithTax() < slashedPrice);
	}

	// HTL-37846 => % difference between 9hr and 1 night price i.e. we will not show 1 night slashed price if (1 night - 9hr)/1 night < X%
	public boolean isXPercentRulePassed(List<SlotDetail> slotDetails, DisplayFare displayFare, int thresholdForSlashedAndDefaultHourPrice) {
		if (CollectionUtils.isNotEmpty(slotDetails) && displayFare != null && displayFare.getSlashedPrice() != null && thresholdForSlashedAndDefaultHourPrice != 0) {
			double slashedPrice = displayFare.getSlashedPrice().getSellingPriceWithTax();
			for (SlotDetail slotDetail : slotDetails) {
				if (slotDetail != null && slotDetail.getPriceDetail() != null && slotDetail.getSlot() != null && slotDetail.getSlot().getDuration() != null && slotDetail.getSlot().getDuration().equals(NINE)) {
					return (slashedPrice * 100 - slotDetail.getPriceDetail().getDiscountedPriceWithTax() * 100) > thresholdForSlashedAndDefaultHourPrice * slashedPrice;
				}
			}
		}
		return true;
	}

	// HTL-37846 => dont send price detail or slashed price for day use if it is less than hourly prices 3h/6h/9h on detail page
	public boolean shouldSetPriceDetailsForDayUseOnDetailPage(List<DayUseSlotPlan> slotPlans, double slashedPrice) {
		if (CollectionUtils.isNotEmpty(slotPlans)) {
			for (DayUseSlotPlan dayUseSlotPlan : slotPlans) {
				if (!isSlashedPriceGreaterThanHourlyPriceOnDetailPage(dayUseSlotPlan, slashedPrice)) {
					return false;
				}
			}
		}
		return true;
	}

	private boolean isSlashedPriceGreaterThanHourlyPriceOnDetailPage(DayUseSlotPlan dayUseSlotPlan, double slashedPrice) {
		return dayUseSlotPlan != null && dayUseSlotPlan.getPriceDetail() != null && (dayUseSlotPlan.getPriceDetail().getTotalPrice() < slashedPrice);
	}

	// HTL-37846 => % difference between 9hr and 1 night price i.e. we will not show 1 night slashed price if (1 night - 9hr)/1 night < X% on detail page
	public boolean isXPercentRulePassedOnDetailPage(List<DayUseSlotPlan> slotDetails, double slashedPrice, int thresholdForSlashedAndDefaultHourPrice) {
		if (CollectionUtils.isNotEmpty(slotDetails) && thresholdForSlashedAndDefaultHourPrice != 0) {
			for (DayUseSlotPlan slotDetail : slotDetails) {
				if (slotDetail != null && slotDetail.getPriceDetail() != null && slotDetail.getSlot() != null && slotDetail.getSlot().getDuration() != null && slotDetail.getSlot().getDuration().equals(NINE)) {
					return (slashedPrice * 100 - slotDetail.getPriceDetail().getTotalPrice() * 100) > thresholdForSlashedAndDefaultHourPrice * slashedPrice;
				}
			}
		}
		return true;
	}
}
