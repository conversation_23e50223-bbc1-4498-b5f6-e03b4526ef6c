package com.mmt.hotels.clientgateway.util;

import lombok.Data;

@Data
public class MetricErrorCodes {
	
	// topicId
	private String m2;
	
	//templateId
	private String tpl1;
	
	//correlationKey
	private String api208;
	
	//errorCode
	private String ps438;
	
	//errorMessage
	private String pd147;
	
	//dependencyLayer
	private String pm626; 
	
	//timestampRecorded
	private String m124;

	//isColumbus
	private boolean m126;
	
	//clientIP
	private final String u125 = "";
	
	//serverIP
	private String m127;
	
	//publishedOn
	private String bd359;

	//url
	private String m101 = "";

	//severity
	private final String pm627 = "";

	//timestampSent
	private String m123;

	@Override
	public String toString() {
		return "MetricErrorCodes{" +
				" correlationKey api208='" + api208 + '\'' +
				", errorCode ps438='" + ps438 + '\'' +
				", errorMessage pd147='" + pd147 + '\'' +
				", dependencyLayer pm626='" + pm626 + '\'' +
				", clientIP u125='" + u125 + '\'' +
				", serverIP m127='" + m127 + '\'' +
				", controller url m101='" + m101 + '\'' +
				", severity pm627='" + pm627 + '\'' +
				'}';
	}
}
