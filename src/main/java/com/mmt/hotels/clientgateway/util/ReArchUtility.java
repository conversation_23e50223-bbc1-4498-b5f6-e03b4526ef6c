package com.mmt.hotels.clientgateway.util;

import com.gi.hotels.model.response.staticdata.Amenities;
import com.gi.hotels.model.response.staticdata.Categorized;
import com.gi.hotels.model.response.staticdata.CategorizedV2;
import com.gi.hotels.model.response.staticdata.Transformed;
import com.gommt.hotels.orchestrator.detail.model.response.chatbot.ChatBotInfo;
import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.content.LocationInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenitySubAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.response.AmenitiesTag;
import com.mmt.hotels.clientgateway.response.HighlightedAmenity;
import com.mmt.hotels.clientgateway.response.SelectRoomAmenities;
import com.mmt.hotels.clientgateway.response.SelectRoomFacility;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.model.response.staticdata.ChatbotInfo;
import com.mmt.hotels.model.response.staticdata.TooltipData;
import com.mmt.model.AttributesFacility;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.SubAttributeFacility;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.DOM_COUNTRY_CODE;
import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;
import static java.lang.Math.max;
import static java.util.Comparator.comparingInt;

@Component
public class ReArchUtility extends Utility {

    private static final String SPACE = " ";
    private static final String AMP = "&";
    private static final String COMMA_SPACE = ", ";
    private static final String EXTRA = "extra";
    private static final String AVAILABLE = "AVAILABLE";
    private static final String PIPE_SEPARATOR = " | ";
    private static final String PIPE_SEPARATOR_WITH_BACKSLASH = "\\|";
    private static final String OCCUPANCY_PARAMETER = "{{OCCUPANCY}}";

    /**
     * Build SharedInfo from DisplayItem
     */
    public SharedInfo buildSharedInfo(DisplayItem hesSharedInfo) {
        if (hesSharedInfo == null)
            return null;
        SharedInfo sharedInfo = new SharedInfo();
        sharedInfo.setIconUrl(hesSharedInfo.getIconUrl());
        sharedInfo.setInfoText(hesSharedInfo.getText());
        return sharedInfo;
    }

    /**
     * Build space inclusion list
     */
    public List<String> buildSpaceInclusion(Space hesSpace) {
        StringBuilder responseString = new StringBuilder();
        if (hesSpace != null) {
            if (hesSpace.getSleepingDetails() != null) {
                if (hesSpace.getSleepingDetails().getBedInfo() != null) {
                    for (ArrangementInfo bedsInfo : hesSpace.getSleepingDetails().getBedInfo()) {
                        if (StringUtils.isNotEmpty(responseString.toString())) {
                            responseString.append(SPACE).append(AMP).append(SPACE).append(bedsInfo.getType());
                        } else {
                            responseString.append(bedsInfo.getCount()).append(SPACE).append(bedsInfo.getType());
                        }
                    }
                }
                if (hesSpace.getSleepingDetails().getExtraBedInfo() != null) {
                    for (ArrangementInfo bedsInfo : hesSpace.getSleepingDetails().getExtraBedInfo()) {
                        responseString.append(COMMA_SPACE).append(EXTRA).append(SPACE).append(bedsInfo.getCount()).append(SPACE).append(bedsInfo.getType()).append(SPACE).append(AVAILABLE.toLowerCase());
                    }
                }
            }
            if (StringUtils.isNotEmpty(responseString.toString()) && StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
                responseString.append(PIPE_SEPARATOR);
            }
            if (StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
                responseString.append(hesSpace.getDescriptionText());
            }
        }
        return StringUtils.isNotEmpty(responseString.toString()) ? Arrays.asList(responseString.toString().split(PIPE_SEPARATOR_WITH_BACKSLASH)) : null;
    }

    /**
     * Get space data V2
     */
    public com.mmt.hotels.clientgateway.response.rooms.SpaceData getSpaceDataV2(SpaceData hesSpaceData, boolean isPrivateSpace, Set< com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList) {
        if (hesSpaceData == null)
            return null;

        com.mmt.hotels.clientgateway.response.rooms.SpaceData cgSpaceData = new com.mmt.hotels.clientgateway.response.rooms.SpaceData();
        int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;
        
        if (hesSpaceData != null) {
            cgSpaceData.setDescriptive(hesSpaceData.getDescriptive());
            cgSpaceData.setSharedInfo(buildSharedInfo(hesSpaceData.getDisplayItem()));
            List<com.mmt.hotels.clientgateway.response.rooms.Space> spaceList = new ArrayList<>();
            
            for (Space hesSpace : hesSpaceData.getSpaces()) {
                com.mmt.hotels.clientgateway.response.rooms.Space cgSpace = new com.mmt.hotels.clientgateway.response.rooms.Space();
                cgSpace.setName(hesSpace.getName());
                cgSpace.setSpaceId(hesSpace.getSpaceId());
                cgSpace.setSpaceType(hesSpace.getSpaceType());
                cgSpace.setAreaText(hesSpace.getAreaText());
                cgSpace.setSpaceInclusions(buildSpaceInclusion(hesSpace));
                
                if (isPrivateSpace) {
                    cgSpace.setAreaText(null);
                } else {
                    cgSpace.setDescriptionText(hesSpace.getDescriptionText());
                    String subText = hesSpace.getSubText();
                    if (hesSpace.getSpaceType() != null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
                        int finalOccupancy = hesSpace.getFinalOccupancy();
                        int occupancy = max(finalOccupancy, hesSpace.getBaseOccupancy());
                        if (occupancy > 0)
                            subText = (occupancy > 1) ? polyglotService.getTranslatedData("SPACE_OCCUPANCY_TEXT") : polyglotService.getTranslatedData("SPACE_SINGLE_OCCUPANCY_TEXT");
                        else
                            subText = null;
                        if (subText != null) {
                            subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
                        }
                    }
                    cgSpace.setSubText(subText);
                }
                
                if (CollectionUtils.isNotEmpty(hesSpace.getMedia())) {
                    List<com.mmt.hotels.clientgateway.response.rooms.MediaData> mediaDataList = new ArrayList<>();
                    for (RoomEntity hesMediaData : hesSpace.getMedia()) {
                        com.mmt.hotels.clientgateway.response.rooms.MediaData cgMediaData = new com.mmt.hotels.clientgateway.response.rooms.MediaData();
                        cgMediaData.setMediaType(hesMediaData.getCategory());
                        cgMediaData.setUrl(hesMediaData.getUrl());
                        mediaDataList.add(cgMediaData);
                    }
                    cgSpace.setMedia(mediaDataList);
                }
                spaceList.add(cgSpace);
            }
            cgSpaceData.setSpaces(spaceList);
        }
        
        cgSpaceData.setBaseGuests(totalBaseOccupancy);
        cgSpaceData.setExtraBeds(extraBedCount);
        cgSpaceData.setMaxGuests(totalMaxOccupancy);
        spaceDataList.add(cgSpaceData);
        return cgSpaceData;
    }

    /**
     * Build chatbot info for static detail
     */
//    public ChatbotInfo buildChatbotInfoStaticDetail(ChatBotInfo chatbotInfo, boolean isTravelPlexEnabled) {
//        ChatbotInfo chatbotInfoStaticDetail = new ChatbotInfo();
//        if (chatbotInfo != null) {
//            chatbotDetailsHes(chatbotInfo, chatbotInfoStaticDetail);
//        }
//        consulStaticDetails(chatbotInfoStaticDetail, isTravelPlexEnabled);
//        return chatbotInfoStaticDetail;
//    }

//    private void consulStaticDetails(ChatbotInfo chatbotInfoStaticDetail, boolean isTravelPlexEnabled) {
//        if (chatBotInfoConsulV2 != null && isTravelPlexEnabled) {
//            chatbotInfoStaticDetail.setBorderColor(chatBotInfoConsulV2.getBorderColor());
//            chatbotInfoStaticDetail.setExpandBgColor(chatBotInfoConsulV2.getExpandBgColor());
//            chatbotInfoStaticDetail.setPersuasions(chatBotInfoConsulV2.getPersuasions());
//            String iconUrl = chatBotInfoConsulV2.getIconUrl();
//            if (StringUtils.isNotEmpty(iconUrl)) {
//                chatbotInfoStaticDetail.setIconUrl(iconUrl);
//            }
//            if (chatBotInfoConsulV2.getExpandDelay() != null) {
//                chatbotInfoStaticDetail.setExpandDelay(chatBotInfoConsulV2.getExpandDelay());
//            }
//            if (chatBotInfoConsulV2.getTooltipData() != null) {
//                chatbotInfoStaticDetail.setTooltipData(chatBotInfoConsulV2.getTooltipData());
//            }
//            if (chatBotInfoConsulV2.getPersuasionDelay() != null) {
//                chatbotInfoStaticDetail.setPersuasionDelay(chatBotInfoConsulV2.getPersuasionDelay());
//            }
//            return;
//        }
//        if (chatbotInfoConsul != null) {
//            chatbotInfoStaticDetail.setBorderColor(chatbotInfoConsul.getBorderColor());
//            chatbotInfoStaticDetail.setExpandBgColor(chatbotInfoConsul.getExpandBgColor());
//            chatbotInfoStaticDetail.setPersuasions(chatbotInfoConsul.getPersuasions());
//            if (chatbotInfoConsul.getExpandDelay() != null) {
//                chatbotInfoStaticDetail.setExpandDelay(chatbotInfoConsul.getExpandDelay());
//            }
//            if (chatbotInfoConsul.getPersuasionDelay() != null) {
//                chatbotInfoStaticDetail.setPersuasionDelay(chatbotInfoConsul.getPersuasionDelay());
//            }
//        }
//    }

    private void chatbotDetailsHes(ChatBotInfo chatbotInfo, ChatbotInfo chatbotInfoStaticDetail) {
        chatbotInfoStaticDetail.setChatBotUrl(chatbotInfo.getChatBotUrl());
        chatbotInfoStaticDetail.setIconUrl(chatbotInfo.getIconUrl());
        chatbotInfoStaticDetail.setType(chatbotInfo.getType());
        TooltipData tooltipData = new TooltipData();
        if (chatbotInfo.getTooltipData() != null) {
            tooltipData.setText(chatbotInfo.getTooltipData().getText());
            tooltipData.setTimer(chatbotInfo.getTooltipData().getTimer());
        }
        chatbotInfoStaticDetail.setTooltipData(tooltipData);
        chatbotInfoStaticDetail.setLobMetaData(chatbotInfo.getLobMetaData());
    }

    public String getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType type) {
        String color = null;
        if (type == com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP) {
            color = "#007E7D";
        } else if( type == com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE) {
            color = "#CF8100";
        }
        return color;
    }


    public static com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo buildRuleTableInfo(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo ruleTableInfoCB){
        if (ruleTableInfoCB == null || CollectionUtils.isEmpty(ruleTableInfoCB.getInfoList())) {
            return null;
        }

        if (CollectionUtils.isEmpty(ruleTableInfoCB.getInfoList())) {
            return null;
        }
        List<com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo> ruleInfoListCg = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo ruleInfo : ruleTableInfoCB.getInfoList()) {
            com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo ruleInfoCg = new com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo();
            ruleInfoCg.setKey(ruleInfo.getKey());
            ruleInfoCg.setValue(ruleInfo.getValue());
            ruleInfoListCg.add(ruleInfoCg);

        }
        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo ruleTableInfoCg = new  com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo();
        ruleTableInfoCg.setKeyTitle(StringUtils.isBlank(ruleTableInfoCB.getKeyTitle()) ? EMPTY_STRING : ruleTableInfoCB.getKeyTitle());
        ruleTableInfoCg.setValueTitle(StringUtils.isBlank(ruleTableInfoCB.getValueTitle()) ? EMPTY_STRING : ruleTableInfoCB.getValueTitle());
        ruleTableInfoCg.setInfoList(ruleInfoListCg);
        return ruleTableInfoCg;
    }

    /**
     * Get amenities
     */

    public boolean isInternationalProperty(LocationInfo hotelResultCB, StaticDetailCriteria staticDetailCriteria) {
        return (hotelResultCB != null && hotelResultCB.getCountryCode() != null && !StringUtils.equalsIgnoreCase(DOM_COUNTRY_CODE, hotelResultCB.getCountryCode()))
                || (staticDetailCriteria != null && staticDetailCriteria.getCountryCode() != null && !StringUtils.equalsIgnoreCase(DOM_COUNTRY_CODE, staticDetailCriteria.getCountryCode()));
    }

    public void updateAmenitiesGIRearch(StaticDetailResponse staticDetailResponse, List<AmenityGroup> amenities) {
        Amenities amenitiesGi = staticDetailResponse.getAmenitiesGI();
        if (amenitiesGi != null && CollectionUtils.isNotEmpty(amenitiesGi.getCategorized())) {
            List<CategorizedV2> categorizedV2List = new ArrayList<>();
            for (Categorized category : amenitiesGi.getCategorized()) {
                CategorizedV2 categorizedV2 = new CategorizedV2();
                categorizedV2.setLabel(category.getLabel());
                categorizedV2.setData(buildCategorizedV2(category.getLabel(),amenities));
                categorizedV2List.add(categorizedV2);
            }
            amenitiesGi.setCategorizedV2(categorizedV2List);
            amenitiesGi.setCategorized(null);
        }
    }

    private String buildL2Amenities(Amenity facility) {
        String subText = null;
        if (CollectionUtils.isNotEmpty(facility.getChildAttributes()) && StringUtils.isNotEmpty(facility.getDisplayType())) {
            if ("1".equalsIgnoreCase(facility.getDisplayType())) {
                StringBuilder stringBuilder = new StringBuilder();
                for (AmenityAttribute cf : facility.getChildAttributes()) {
                    stringBuilder.append(cf.getName()).append(Constants.COMMA);
                }
                subText= Constants.AMENITIES_OPEN_BRACE +
                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE;
            }
            if ("2".equalsIgnoreCase(facility.getDisplayType())) {
                StringBuilder stringBuilder = new StringBuilder();
                AmenityAttribute childAttribute = facility.getChildAttributes().get(0);
                stringBuilder.append(childAttribute.getName())
                        .append(Constants.SPACE)
                        .append(Constants.HYPEN)
                        .append(Constants.SPACE);
                if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
                    for (AmenitySubAttribute subAttributeFacility : childAttribute.getSubAttributes()) {
                        stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
                    }
                }
                subText = Constants.AMENITIES_OPEN_BRACE +
                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE;
            }
        }
        return subText;
    }

    private List<Transformed> buildCategorizedV2(String label, List<AmenityGroup> amenities) {
        Optional<AmenityGroup> facilityGroup =  amenities.stream().filter(fg -> label.equalsIgnoreCase(fg.getName())).findFirst();
        if(facilityGroup.isPresent()){
            return facilityGroup.get().getAmenities().stream().map(facility -> {
                Transformed transformed = new Transformed();
                transformed.setName(facility.getName());
                transformed.setSubText(buildL2Amenities(facility));
                return transformed;
            }).collect(Collectors.toList());
        }
        return null;
    }

    public List<SelectRoomAmenities> getAmenities(List<AmenityGroup> amenities) {
        List<SelectRoomAmenities> amenitiesList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(amenities)) {
            for (AmenityGroup fg : amenities) {
                SelectRoomAmenities selectRoomAmenities = new SelectRoomAmenities();
                selectRoomAmenities.setName(fg.getName());
                if (CollectionUtils.isNotEmpty(fg.getAmenities())) {
                    List<SelectRoomFacility> selectRoomFacilities = new ArrayList<>();
                    for (Amenity facility : fg.getAmenities()) {
                        SelectRoomFacility selectRoomFacility = new SelectRoomFacility();
                        selectRoomFacility.setName(facility.getName());
                        if (StringUtils.isNotEmpty(facility.getDisplayType())) {
                            if ("1".equalsIgnoreCase(facility.getDisplayType())) {
                                StringBuilder stringBuilder = new StringBuilder();
                                for (AmenityAttribute cf : facility.getChildAttributes()) {
                                    stringBuilder.append(cf.getName()).append(Constants.COMMA);
                                }
                                selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
                                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
                            }
                            if ("2".equalsIgnoreCase(facility.getDisplayType())) {
                                StringBuilder stringBuilder = new StringBuilder();
                                if (CollectionUtils.isNotEmpty(facility.getChildAttributes()) && facility.getChildAttributes().get(0) != null) {
                                    AmenityAttribute childAttribute = facility.getChildAttributes().get(0);
                                    stringBuilder.append(childAttribute.getName())
                                            .append(Constants.SPACE)
                                            .append(Constants.HYPEN)
                                            .append(Constants.SPACE);
                                    if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
                                        for (AmenitySubAttribute subAttributeFacility : childAttribute.getSubAttributes()) {
                                            stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
                                        }
                                    }
                                }
                                selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
                                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
                            }
                        }
                        selectRoomFacilities.add(selectRoomFacility);
                    }
                    selectRoomAmenities.setFacilities(selectRoomFacilities);
                    selectRoomAmenities.setImages(fg.getImages());
                }
                amenitiesList.add(selectRoomAmenities);
            }
        }
        return amenitiesList;
    }

    private List<SelectRoomFacility> processFacilities(List<Amenity> amenities, boolean isStrikeThrough) {
        List<SelectRoomFacility> selectRoomFacilities = new ArrayList<>();
        if (CollectionUtils.isEmpty(amenities)) {
            return selectRoomFacilities;
        }
        for (Amenity amenity : amenities) {
            SelectRoomFacility selectRoomFacility = new SelectRoomFacility();
            selectRoomFacility.setName(amenity.getName());
            selectRoomFacility.setIconUrl(amenity.getIconUrl());
            selectRoomFacility.setStrikeThrough(isStrikeThrough);
            if (CollectionUtils.isNotEmpty(amenity.getChildAttributes()) && StringUtils.isNotEmpty(amenity.getDisplayType())) {
                if ("1".equalsIgnoreCase(amenity.getDisplayType())) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (AmenityAttribute cf : amenity.getChildAttributes()) {
                        stringBuilder.append(cf.getName()).append(Constants.COMMA);
                    }
                    selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
                            StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
                }
                if ("2".equalsIgnoreCase(amenity.getDisplayType())) {
                    StringBuilder stringBuilder = new StringBuilder();
                    AmenityAttribute childAttribute = amenity.getChildAttributes().get(0);
                    stringBuilder.append(childAttribute.getName())
                            .append(Constants.SPACE)
                            .append(Constants.HYPEN)
                            .append(Constants.SPACE);
                    if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
                        for (AmenitySubAttribute subAttributeFacility : childAttribute.getSubAttributes()) {
                            stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
                        }
                    }
                    selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
                            StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
                }
            }
            selectRoomFacilities.add(selectRoomFacility);
        }
        return selectRoomFacilities;
    }

    /**
     * Get highlighted amenities
     */
    public List<String> getHighlightedAmenities(List<AmenityGroup> highlightedAmenities) {
        List<String> hltAmenities = null;
        if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
            hltAmenities = new ArrayList<>();
            for (AmenityGroup fg : highlightedAmenities) {
                if (CollectionUtils.isNotEmpty(fg.getAmenities())) {
                    for (Amenity amenity : fg.getAmenities()) {
                        hltAmenities.add(amenity.getName());
                    }
                }
            }
        }
        return hltAmenities;
    }

    /**
     * Get highlighted amenities V2
     */
    public List<HighlightedAmenity> getHighlightedAmenitiesV2(List<AmenityGroup> highlightedAmenities) {
        List<HighlightedAmenity> hltAmenities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
            for (AmenityGroup fg : highlightedAmenities) {
                if (CollectionUtils.isNotEmpty(fg.getAmenities())) {
                    for (Amenity facility : fg.getAmenities()) {
                        HighlightedAmenity amenity = new HighlightedAmenity();
                        amenity.setTitle(facility.getName());
                        amenity.setIconUrl(facility.getIconUrl());
                        hltAmenities.add(amenity);
                    }
                }
            }
        }
        return hltAmenities;
    }

    public List<com.mmt.hotels.clientgateway.response.FacilityGroup> buildAmenities(List<AmenityGroup> facilityWithGrp, List<AmenityGroup> starFacilities, List<AmenityGroup> highlightedFacilities) {
        if (CollectionUtils.isEmpty(facilityWithGrp))
            return null;
        List<com.mmt.hotels.clientgateway.response.FacilityGroup> amenitiesCGList = new ArrayList<>();

        Set<String> starAmenitySet = new HashSet<>();

        if (CollectionUtils.isNotEmpty(starFacilities)) {
            for (AmenityGroup facilityGroup : starFacilities) {
                if (CollectionUtils.isNotEmpty(facilityGroup.getAmenities())) {
                    for (Amenity facility : facilityGroup.getAmenities()) {
                        starAmenitySet.add(facility.getName());
                    }
                }

            }
        }

        List<com.mmt.hotels.clientgateway.response.Facility> starFacilityCGs = new ArrayList<>();
        for (AmenityGroup facilityGroup : facilityWithGrp) {
            com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
            facility.setName(facilityGroup.getName());
            List<com.mmt.hotels.clientgateway.response.Facility> facilityCGs = new ArrayList<>();
            for (Amenity facilityHes : facilityGroup.getAmenities()) {

                com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
                facilityCG.setAttributeName(facilityHes.getAttributeName());
                facilityCG.setCategoryName(facilityHes.getCategoryName());
                facilityCG.setDisplayType(facilityHes.getDisplayType());
                facilityCG.setHighlightedName(facilityHes.getHighlightedName());
                facilityCG.setName(facilityHes.getName());
                facilityCG.setSequence(facilityHes.getSequence());
                facilityCG.setTags(facilityHes.getTags());
                facilityCG.setType(facilityHes.getType());
                if(facilityHes.getChildAttributes() != null) {
                    List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
                    facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
                }

                if (starAmenitySet.contains(facilityHes.getName())) {
                    starAmenitySet.remove(facilityHes.getName());
                    starFacilityCGs.add(facilityCG);
                } else {
                    facilityCGs.add(facilityCG);
                }
            }

            if (CollectionUtils.isNotEmpty(facilityCGs)) {
                facility.setFacilities(facilityCGs);
                amenitiesCGList.add(facility);
            }
        }

        if (CollectionUtils.isNotEmpty(starFacilities) && !starAmenitySet.isEmpty()) {
            for (AmenityGroup facilityGroup : starFacilities) {
                for (Amenity facilityHes : facilityGroup.getAmenities()) {

                    if (!starAmenitySet.contains(facilityHes.getName())) {
                        continue;
                    }
                    com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
                    facilityCG.setAttributeName(facilityHes.getAttributeName());
                    facilityCG.setCategoryName(facilityHes.getCategoryName());
                    facilityCG.setDisplayType(facilityHes.getDisplayType());
                    facilityCG.setHighlightedName(facilityHes.getHighlightedName());
                    facilityCG.setName(facilityHes.getName());
                    facilityCG.setSequence(facilityHes.getSequence());
                    facilityCG.setTags(facilityHes.getTags());
                    facilityCG.setType(facilityHes.getType());
                    if(facilityHes.getChildAttributes() != null) {
                        List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
                        facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
                    }
                    starFacilityCGs.add(facilityCG);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(starFacilityCGs)) {
            Collections.sort(starFacilityCGs,comparingInt(starFacilityCG -> (starFacilityCG.getSequence() == null ? Integer.MAX_VALUE : starFacilityCG.getSequence())));
            com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
            facility.setName(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES));
            facility.setType(Constants.BOLD_TYPE);
            facility.setFacilities(starFacilityCGs);
            amenitiesCGList.add(0, facility);
        }


        return amenitiesCGList;
    }

    public List<com.mmt.hotels.clientgateway.response.AttributesFacility> buildChildAttributesCgFromHes(List<AmenityAttribute> childAttributesHes) {

        List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = new ArrayList<>();
        for(AmenityAttribute childAttributeHes : childAttributesHes) {
            com.mmt.hotels.clientgateway.response.AttributesFacility childAttributeCG = new com.mmt.hotels.clientgateway.response.AttributesFacility();
            BeanUtils.copyProperties(childAttributeHes, childAttributeCG);
            childAttributesCG.add(childAttributeCG);
        }
        return  childAttributesCG;
    }
} 