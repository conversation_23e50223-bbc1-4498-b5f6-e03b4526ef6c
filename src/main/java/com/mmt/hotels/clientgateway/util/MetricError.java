package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import lombok.Getter;

@Getter
public class MetricError {
	
	private DependencyLayer dependencyLayer;
	
	private ErrorType errorType;
	
	private String errorCode;
	
	private String errorMessage;
	
	public MetricError (DependencyLayer dependencyLayer, ErrorType errorType,
			String errorCode, String errorMessage) {
		this.dependencyLayer = dependencyLayer;
		this.errorType = errorType;
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
	}
}
