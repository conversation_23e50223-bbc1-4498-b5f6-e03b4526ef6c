package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;

import lombok.Data;

@Data
public class ExceptionHandlerResponse {
	
	private MetricError metricError;
	
	private ClientGatewayException clientGatewayException;
	
	public ExceptionHandlerResponse(MetricError metricError,
			ClientGatewayException clientGatewayException) {
		this.metricError = metricError;
		this.clientGatewayException = clientGatewayException;
	}
}
