package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.response.filter.FilterGroup;

public enum Currency {
	
	INR("Indian Rupee","₹"),
    USD("US Dollar","$"),
    EUR("European Euro","€"),
    AED("UAE Dirham","AED"),
    GBP("Pound Sterling","£"),
    AUD("Australian Dollar","AUD"),
    CAD("Canadian Dollar","CAD"),
    SGD("Singapore Dollar","SGD"),
    BDT("Bangladeshi Taka","৳"),
    SAR("Saudi Arabian Riyal","SAR"),
    NPR("Nepalese Rupee","NPR"),
    KWD("Kuwaiti Dinar","KWD"),
    QAR("Qatari Riyal","QAR"),
    MYR("Malaysian Ringgit","MYR"),
    NZD("New Zealand Dollar","NZD"),
    OMR("Omani Rial","OMR"),
    THB("Thai Baht","THB"),
    ZAR("South African Rand","ZAR"),
    IDR("Indonesian Rupiah","IDR"),
    CNY("Chinese Yuan Renminbi","CNY"),
    RUB("Russian Ruble","₽"),
    KRW("Won", "₩"),
    JPY("Yen", "¥");
	
	private String currencyName;
	private String currencySymbol;
	
	Currency(String currencyName, String currencySymbol){
		this.currencyName = currencyName;
		this.currencySymbol = currencySymbol;
	}
    
	public String getCurrencyName() {
		return currencyName;
	}
	
	public String getCurrencySymbol() {
		return currencySymbol;
	}

	public static Currency getCurrencyEnum(String currency) {
        for (Currency currencyEnum: Currency.values()) {
            if (currencyEnum.name().equalsIgnoreCase(currency))
                return currencyEnum;
        }
        return Currency.INR;
    }
}
