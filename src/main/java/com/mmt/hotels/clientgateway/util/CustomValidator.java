package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ValidationException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class CustomValidator {

	public static void validate(String tid, String client) throws ClientGatewayException {
		validateTransactionId(tid);
		validateClient(client);
	}
	
	private static void validateTransactionId(String tid) throws ValidationException {
		if (StringUtils.isEmpty(tid)) 
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
					ValidationErrors.EMPTY_TID.getErrorCode(), ValidationErrors.EMPTY_TID.getErrorMsg());
	}
	
	private static void validateClient(String client) throws ClientGatewayException{
		if (StringUtils.isEmpty(client))
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION, 
					ValidationErrors.EMPTY_CLIENT.getErrorCode(), ValidationErrors.EMPTY_CLIENT.getErrorMsg());
		for (Clients registeredClient: Clients.values()) {
			if (registeredClient.name().equals(client))
				return;
		}
		throw new AuthenticationException(DependencyLayer.CLIENTS, ErrorType.AUTHENTICATION, 
				AuthenticationErrors.INVALID_CLIENT.getErrorCode(), AuthenticationErrors.INVALID_CLIENT.getErrorMsg());
	}

	public static void validateMMTorGIHotelId(String hotelId, String giHotelId) throws ClientGatewayException {
		if (StringUtils.isEmpty(hotelId) && StringUtils.isEmpty(giHotelId))
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
					ValidationErrors.EMPTY_MMT_GI_HOTEL_ID.getErrorCode(), ValidationErrors.EMPTY_MMT_GI_HOTEL_ID.getErrorMsg());
	}

	public static void validateMMTorGIHotelIdList(List<String> hotelIdList, List<String> giHotelIdList) throws ClientGatewayException {
		if (CollectionUtils.isEmpty(hotelIdList) && CollectionUtils.isEmpty(giHotelIdList))
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
					ValidationErrors.EMPTY_MMT_GI_HOTEL_ID.getErrorCode(), ValidationErrors.EMPTY_MMT_GI_HOTEL_ID.getErrorMsg());
	}

	public static void validateGIHotelId(String giHotelId) throws ClientGatewayException {
		if (StringUtils.isBlank(giHotelId))
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
					ValidationErrors.EMPTY_GI_HOTEL_ID.getErrorCode(), ValidationErrors.EMPTY_GI_HOTEL_ID.getErrorMsg());
	}

	public static void validateCityId(String cityId) throws ClientGatewayException {
		if (StringUtils.isBlank(cityId))
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
					ValidationErrors.EMPTY_CITY_ID.getErrorCode(), ValidationErrors.EMPTY_CITY_ID.getErrorMsg());
	}
}