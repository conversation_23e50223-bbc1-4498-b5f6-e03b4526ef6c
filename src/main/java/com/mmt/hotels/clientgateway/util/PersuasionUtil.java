package com.mmt.hotels.clientgateway.util;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.PersuasionTemplate;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;


@Component
public class PersuasionUtil {

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    private static final Logger logger = LoggerFactory.getLogger(PersuasionUtil.class);

    @Value("${hidden.gems.persuasion.style}")
    String hiddenGemPersuasionConfig;

    Map<String, Map<String,PersuasionData>> hiddenGemPersuasionConfigMap;

    static final String HIDDEN_GEM_STYLE = "hiddenGem";
    static final String HIDDEN_GEM_ICON_STYLE = "hiddenGemIcon";
    static final String HOME_STAY_TITLE_STYLE = "homeStayTitle";
    static final String HOME_STAY_SUB_TITLE_STYLE = "homeStaySubTitle";

    @PostConstruct
    public void init() {
        hiddenGemPersuasionConfigMap = new Gson().fromJson(hiddenGemPersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
        }.getType());

    }

    /**
     * Method to build Hidden Gem Persuasion with Text,
     * Styling is configured in application-properties against each client,
     * and text is fetched from HES -> Hotstore (Hidden Gem USP)
     */
    public PersuasionObject buildHiddenGemPersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HIDDEN_GEM_STYLE) : new PersuasionData();
        if (hotelEntity != null && StringUtils.isNotEmpty(hotelEntity.getHiddenGemPersuasionText()) && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.MULTI_PERSUASION_V.name());
            PersuasionData persuasionData = new PersuasionData();
            //Building html tag for this persuasion
            String persuasionText = "<font color=\"" + persuasionStyleConfig.getStyle().getTextColor() + "\"><i>\"" + hotelEntity.getHiddenGemPersuasionText() + "\"</i> </font>";
            persuasionData.setText(persuasionText);
            persuasionData.setHasAction(false);
            persuasionData.setHtml(true);
            persuasionData.setIconurl(persuasionStyleConfig.getIconurl());
            persuasionData.setIcontype(persuasionStyleConfig.getIcontype());

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            persuasionData.setStyle(persuasionStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    /**
     * Method to build Hidden Gem Icon Persuasion.
     * Styling is configured in application-properties against each client, and Hidden Gem boolean is decided on the basis of
     * category which is fetched from HES -> Hotstore (Hidden Gem USP).
     */
    public PersuasionObject buildHiddenGemIconPersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HIDDEN_GEM_ICON_STYLE) : new PersuasionData();
        if (hotelEntity != null && hotelEntity.isHiddenGem() && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.IMAGE_TEXT_H.name());
            PersuasionData persuasionData = new PersuasionData();
            persuasionData.setHasAction(false);
            persuasionData.setIconurl(persuasionStyleConfig.getIconurl());
            persuasionData.setIcontype(persuasionStyleConfig.getIcontype());

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            persuasionData.setStyle(persuasionStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    /**
     * Method to build Home Stays Title Persuasion.
     * Styling is configured in application-properties against each client,
     * and Home Stays Details data is prepared in HES.
     */
    public PersuasionObject buildHomeStaysTitlePersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HOME_STAY_TITLE_STYLE) : new PersuasionData();
        if (hotelEntity != null && hotelEntity.getHomeStayDetails() != null && StringUtils.isNotEmpty(hotelEntity.getHomeStayDetails().getStayType()) &&
                !Constants.STAY_TYPE_HOTEL.equalsIgnoreCase(hotelEntity.getHomeStayDetails().getStayType()) && !hotelEntity.isLastBooked() && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.MULTI_PERSUASION_V.name());
            PersuasionData persuasionData = new PersuasionData();
            persuasionData.setPersuasionType(Constants.STAY_TYPE);
            persuasionData.setText(hotelEntity.getHomeStayDetails().getStayType().toUpperCase());
            persuasionData.setHasAction(false);
            persuasionData.setHtml(true);

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            PersuasionStyle topLevelStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            BeanUtils.copyProperties(persuasionStyleConfig.getTopLevelStyle() !=null ? persuasionStyleConfig.getTopLevelStyle() : new PersuasionStyle(), topLevelStyle);
            persuasionData.setStyle(persuasionStyle);
            persuasion.setStyle(topLevelStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    /**
     * Method to build Home Stays Title Persuasion.
     * Styling is configured in application-properties against each client
     * and Home Stays Details data is prepared in HES.
     */
    public PersuasionObject buildHomeStaysSubTitlePersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HOME_STAY_SUB_TITLE_STYLE) : new PersuasionData();
        if (hotelEntity != null && hotelEntity.getHomeStayDetails() != null && StringUtils.isNotEmpty(hotelEntity.getHomeStayDetails().getStayTypeInfo()) && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.MULTI_PERSUASION_V.name());
            PersuasionData persuasionData = new PersuasionData();
            persuasionData.setPersuasionType(Constants.STAY_TYPE);
            persuasionData.setText(hotelEntity.getHomeStayDetails().getStayTypeInfo());
            persuasionData.setHasAction(false);
            persuasionData.setHtml(true);

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            PersuasionStyle topLevelStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            BeanUtils.copyProperties(persuasionStyleConfig.getTopLevelStyle() !=null ? persuasionStyleConfig.getTopLevelStyle() : new PersuasionStyle(), topLevelStyle);
            persuasionData.setStyle(persuasionStyle);
            persuasion.setStyle(topLevelStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    public boolean checkIfIndianessPersuasionExists(Object hotelPersuasions) {
        if (hotelPersuasions == null) return false;
        try {
            JSONObject persuasions = new JSONObject(objectMapperUtil.getJsonFromObject(hotelPersuasions, DependencyLayer.CLIENTGATEWAY));
            for (String placeHolder : persuasions.keySet()) {
                JSONObject persuasion = persuasions.has(placeHolder) ? persuasions.getJSONObject(placeHolder) : null;
                if (null != persuasion && persuasion.has("data")) {
                    JSONArray persuasionDataList = persuasion.getJSONArray("data");
                    if (persuasionDataList != null) {
                        for (int i = 0; i < persuasionDataList.length(); i++) {
                            JSONObject persuasionData = persuasionDataList.getJSONObject(i);
                            if (persuasionData != null && persuasionData.has("hover") && persuasionData.getJSONObject("hover") != null && StringUtils.isNotEmpty(persuasionData.getJSONObject("hover").getString("tooltipType"))
                                    && TOOL_TIP_INDIANNESS.equalsIgnoreCase(persuasionData.getJSONObject("hover").getString("tooltipType"))) {
                                return true;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error Occurred while accessing for indianess persuasions", e);
        }
        return false;
    }
}
