package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.RestConfigurationEnums;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import org.apache.http.HttpEntity;
import org.apache.http.client.HttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.mmt.hotels.clientgateway.enums.RestConfigurationEnums.*;
import static com.mmt.hotels.clientgateway.enums.RestConfigurationEnums.ORCH_SEARCH_HOTELS_CONNECTOR;
import static com.mmt.hotels.clientgateway.enums.RestConfigurationEnums.ORCH_SEARCH_ROOMS_CONNECTOR;
import static com.mmt.hotels.clientgateway.enums.RestConfigurationEnums.ORCH_STATIC_DETAILS_CONNECTOR;

@Component
public class RestConnectorUtil {
	
	@Autowired
	Map<RestConfigurationEnums, HttpClient> connectorMap;
	
	@Autowired
	private RestConnector restConnector;
	
	public String performSearchHotelsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(SEARCH_HOTELS_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performMobLandingPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(MOB_LANDING_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performFilterCountPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(FILTER_COUNT_REST_CONNECTOR), 
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performListingMapPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(LISTING_MAP_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performSearchRoomsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(SEARCH_ROOMS_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}
	
	public String performAlternateDatesPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(ALTERNATE_DATES_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performStaticRoomDetailGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(STATIC_ROOMS_REST_CONNECTOR), 
				headers, url, DependencyLayer.ORCHESTRATOR);
	}
	
	public String performStaticDetailPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(STATIC_DETAIL_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performUpdatedPricePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(UPDATED_PRICE_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performAvailRoomsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(AVAIL_ROOMS_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}
	
	public String performUpdatedPriceOccuLessPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(GET_UPDATED_PRICE_OCCU_LESS_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performPokusExperimentPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(POKUS_EXPERIMENT_CONNECTOR),
				requestBody, headers, url, DependencyLayer.POKUS);
	}

	public String performUserFirstTimeStatePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(USER_FIRST_TIME_STATE_CONNECTOR),
				requestBody, headers, url, DependencyLayer.HYDRA);
	}

	public String performLastBookedFlightResponsePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(LAST_BOOKED_FLIGHT_CONNECTOR),
				requestBody, headers, url, DependencyLayer.HYDRA);
	}

	public String performUserServicePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(USER_SERVICE_CONNECTOR),
				requestBody, headers, url, DependencyLayer.USERSERVICE);
	}

	public String performHydraMatchedSegment(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(USER_SERVICE_CONNECTOR),
				requestBody, headers, url, DependencyLayer.USERSERVICE);
	}

	public String performPostByPass(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(BYPASS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}
	public String performUserServiceGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(USER_SERVICE_CONNECTOR),
				headers, url, DependencyLayer.USERSERVICE);
	}

	public String performPaymentCheckoutPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(PAYMENT_CHECKOUT_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performPaymentCheckoutPostForModifiedBooking(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(PAYMENT_CHECKOUT_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}
	
	public String performGetByPass(Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.getMessages(connectorMap.get(BYPASS_CONNECTOR),
				headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performGetTaggedMedia(Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.getMessages(connectorMap.get(TAGGED_MEDIA_CONNECTOR),
				headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performGetMediaByTagId(Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.getMessages(connectorMap.get(MEDIA_BY_TAG_ID_CONNECTOR),
				headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performStreaksInfoGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(STREAKS_USER_INFO_CONNECTOR),
				headers, url, DependencyLayer.BABELFISH);
	}

	public String performStreaksUserEarningGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(STREAKS_USER_BALANCE_CONNECTOR),
				headers, url, DependencyLayer.BABELFISH);
	}

	public String performFetchCollectionPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(FETCH_COLLECTION),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performEmiDetailPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(EMI_DETAILS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}
	
	public String performTotalPricePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(TOTAL_PRICE_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}
	
	public String performPoliciesGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(POLICIES_API_CONNECTOR), 
				headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performValidateCouponPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(AVAIL_ROOMS_REST_CONNECTOR), 
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}


	public String performCorporateWorkflowGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(CORPORATE_WORKFLOW_DATA_CONNECTOR),
				headers, url, DependencyLayer.CORPORATE);
	}

	public String performCorporateApprovalPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(CORP_UPDATE_APPROVAL_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performWorkflowInfoByAuthcodePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(CORP_WORKFLOW_INFO_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performApprovalsInfoPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(CORP_APPROVALS_INFO_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String getLatLngFromGooglePlaceId(Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.getMessages(connectorMap.get(PLACE_MAP_TO_MMT_API), headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performThankYouGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(THANKYOU_REST_CONNECTOR),
				headers, url, DependencyLayer.ORCHESTRATOR);
	}
	
	public String performSearchAddonsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(SEARCH_ADDONS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performNearByPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(NEAR_BY_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performPostByPassFlyfish(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(BYPASS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.FLYFISH);

	}
	
	public String performAffiliateFeeUpdatePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(AFFILIATE_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performAffiliateCreateQuotePost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(AFFILIATE_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performGetQuoteDataMerged(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(AFFILIATE_CONNECTOR), requestBody,
				 headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performCorporateInitApprovalPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.postMessages(connectorMap.get(CORP_UPDATE_APPROVAL_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

    public String performUpdatePolicyPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(CORPORATE_UPDATE_POLICY_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
    }

	public String fetchTranslatedData(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(POLYGLOT_REST_CONNECTOR), headers, url, DependencyLayer.POLYGLOT);
	}

	public String checkPayLaterEligibilityPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(PAY_LATER_CONNECTOR), requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performHotelsHotStorePost(String request, Map<String, String> headerMap, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(COMMON_DATELESS_WISHLISTS_HOTELS_CONNECTOR), request, headerMap, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performHotelsReviewSummaryPost(String request, Map<String, String> headerMap, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(COMMON_DATELESS_WISHLISTS_REVIEW_CONNECTOR), request, headerMap, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performCalendarAvailabilityPost(String request, Map<String, String> headerMap, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(CALENDAR_AVAILABILITY_CONNECTOR), request, headerMap, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performLandingDiscoveryPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(LANDING_DISCOVERY_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performHermesDetailPriceGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(HERMES_DETAIL_PRICE_CONNECTOR),
				headers, url, DependencyLayer.HERMES, false);
	}

	public String performSmartEngageGet(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.getMessages(connectorMap.get(MOB_LANDING_REST_CONNECTOR),
				headers, url, DependencyLayer.CLIENTGATEWAY);
	}

	public String performPageMakerDataGet(String requestBody, Map<String, String> headers, String url) throws RestConnectorException{
		return restConnector.getMessages(connectorMap.get(MOB_LANDING_REST_CONNECTOR),
				headers, url, DependencyLayer.CLIENTGATEWAY);
	}

	public String performDataPlatformPost(String request, Map<String, String> headerMap, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(DATA_PLATFORM_CONNECTOR), request, headerMap, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performOfferDetailsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(OFFER_DETAILS_REST_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performFetchUgcReviewsPost(String request, Map<String, String> headerMap, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(UGC_REVIEW_CONNECTOR), request, headerMap, url, DependencyLayer.ORCHESTRATOR);
	}

	public String performFetchUgcSummaryPost(String request, Map<String, String> headerMap, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(UGC_SUMMARY_CONNECTOR), request, headerMap, url, DependencyLayer.ORCHESTRATOR);
	}
	public String performFetchImagePost(String request, Map<String, String> headerMap, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(HOTEL_IMAGE), request, headerMap, url, DependencyLayer.ORCHESTRATOR);
	}

	public String getBookingDetails(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(GET_BOOKING_DETAILS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.CLIENTGATEWAY);
	}

	public String performLoadProgramGet(Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.getMessages(connectorMap.get(PROGRAM_18_CONNECTOR),
				headers, url, DependencyLayer.CLIENTGATEWAY);
	}

	public String submitAnswersPlatforms(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(SUBMIT_ANSWERS_CONNTECTOR),
				requestBody, headers, url, DependencyLayer.CLIENTGATEWAY);
	}

	public String uploadImagesToPlatform(HttpEntity requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessagesMultipart(connectorMap.get(UPLOAD_IMAGES_CONNECTOR),
				requestBody, headers, url, DependencyLayer.CLIENTGATEWAY);
	}


	public String performOrchestratorSearchHotelsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(ORCH_SEARCH_HOTELS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR_NEW);
	}

	public String performOrchestratorSearchRoomsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(ORCH_SEARCH_ROOMS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR_NEW);
	}

	public String performOrchestratorStaticDetailsPost(String requestBody, Map<String, String> headers, String url) throws RestConnectorException {
		return restConnector.postMessages(connectorMap.get(ORCH_STATIC_DETAILS_CONNECTOR),
				requestBody, headers, url, DependencyLayer.ORCHESTRATOR_DETAIL);
	}
}
