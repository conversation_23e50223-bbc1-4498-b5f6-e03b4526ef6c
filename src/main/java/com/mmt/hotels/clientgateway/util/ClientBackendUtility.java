package com.mmt.hotels.clientgateway.util;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.response.cbresponse.CBGenericResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorEntity;
import com.mmt.hotels.clientgateway.response.cbresponse.FailureReason;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.util.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@Deprecated
public class ClientBackendUtility {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientBackendUtility.class);

    public static String setCBErrorResponse(CBError erroEnum) {

        String responseString = null;

        try {

            CGServerResponse errorResponse;
            FailureReason failureReason = new FailureReason();
            failureReason.setMsg(erroEnum.getDescription());
            failureReason.setErrorCode(erroEnum.getCode());
            ErrorEntity errorEntity = new ErrorEntity();
            errorEntity.setErrorCode(erroEnum.getCode());
            errorEntity.setMsg(erroEnum.getDescription());
            errorResponse = new CGServerResponse();
            errorResponse.setFailureReason(failureReason);
            errorResponse.setErrorEntity(errorEntity);
            Gson gson = new Gson();
            responseString = gson.toJson(errorResponse);

        } catch (Exception e) {
            LOGGER.error("Error by setFailureMessage error ", e);
        }
        return responseString;
    }

    @SuppressWarnings("unchecked")
    public static Tuple<ResponseErrors , com.mmt.hotels.model.response.base.FailureReason> setResponseErrors(CBError erroEnum) {


        com.mmt.hotels.model.response.base.FailureReason failureReason = new com.mmt.hotels.model.response.base.FailureReason();

        List< com.mmt.hotels.model.response.errors.Error> errorsList = new ArrayList<>();
        failureReason.setMsg(erroEnum.getDescription());
        failureReason.setErrorCode(erroEnum.getCode());
        com.mmt.hotels.model.response.errors.Error error = new  com.mmt.hotels.model.response.errors.Error.Builder()
                .buildErrorCode(erroEnum.getCode(),erroEnum.getDescription()).build();
        errorsList.add(error);
        ResponseErrors responseErrors = new ResponseErrors.Builder().buildErrorList(errorsList).build();

        return new Tuple<>(responseErrors,failureReason);
    }

    public static <T> void setCBErrorResponse(CBGenericResponse<T> genericResponse, CBError erroEnum) {

        com.mmt.hotels.model.response.base.FailureReason failureReason = new com.mmt.hotels.model.response.base.FailureReason();
        failureReason.setMsg(erroEnum.getDescription());
        failureReason.setErrorCode(erroEnum.getCode());

        com.mmt.hotels.pojo.response.ErrorEntity errorEntity = new com.mmt.hotels.pojo.response.ErrorEntity();
        errorEntity.setErrorCode(erroEnum.getCode());
        errorEntity.setMsg(erroEnum.getDescription());

        genericResponse.setErrorEntity(errorEntity);
        genericResponse.setFailureReason(failureReason);
    }
}
