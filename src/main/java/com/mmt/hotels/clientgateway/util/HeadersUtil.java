package com.mmt.hotels.clientgateway.util;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HeadersUtil {
	
	 private static final String AKAMAI_BOT_HEADER = "Akamai-Bot";
	
     private static final String BOT_HDR_NAME="mmt-bot-details";

	private static final Logger logger = LoggerFactory.getLogger(HeadersUtil.class);

	public static Map<String, String> getHeadersFromServletRequest(HttpServletRequest request) {
		logger.debug("Headers received from client");
		 Map<String, String> headerMap = new HashMap<>();
		 String headerName = null;
		 String headerValue = null;
		 if(null!=request) {
			 headerMap = new HashMap<>();
			 Enumeration<String> headerNames = request.getHeaderNames();
			 if (headerNames != null) {
				 while (headerNames.hasMoreElements()) {
					 headerName = headerNames.nextElement();
					 headerValue = request.getHeader(headerName);
					 logger.debug("{} : {}",headerName,headerValue);
					 if(!headerName.equalsIgnoreCase("host") && !headerName.equalsIgnoreCase("Content-Length")) {
						 headerMap.put(headerName, headerValue);
					 }
				 }
			 }
		 }
		 if (MapUtils.isNotEmpty(headerMap)) {
			 if(headerMap.containsKey("oauth-goibibo"))
				 headerMap.put("mmt-auth", headerMap.get("oauth-goibibo"));
			 else if(headerMap.containsKey("OAUTH-GOIBIBO"))
				 headerMap.put("mmt-auth", headerMap.get("OAUTH-GOIBIBO"));
		 }
		 return headerMap;
	 }
	 
	 public static void prepareHttpServletResponsefromMap(Map<String, String> map ,HttpServletResponse response){
		 if(MapUtils.isNotEmpty(map)) {
			 for (Map.Entry<String, String> entry : map.entrySet()) {
				 if ("Content-Length".equalsIgnoreCase(entry.getKey()))
					 continue;
				 response.setHeader(entry.getKey(), entry.getValue());
				 if(AKAMAI_BOT_HEADER.equalsIgnoreCase(entry.getKey())){
					 response.setHeader(BOT_HDR_NAME, entry.getValue());
				 }
			 }
		 }
	 }
}
