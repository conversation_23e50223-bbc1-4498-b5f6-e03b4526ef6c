package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.databind.DeserializationFeature;

import javax.annotation.PostConstruct;

import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.MarshallingErrors;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.mmt.hotels.clientgateway.exception.JsonParseException;

@Component
public class ObjectMapperUtil {
	
	private ObjectMapper mapper;
	private static final Logger LOGGER = LoggerFactory.getLogger(ObjectMapperUtil.class);
	private ObjectMapper mapperIgnoreUnknown;
	
	@PostConstruct
	public void init() {
		mapper = new ObjectMapper();
		mapper.writerWithView(PIIView.External.class);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		mapperIgnoreUnknown = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_EMPTY)
				.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
	}

	public <T> T getObjectFromJson(String json, Class<T> classOfType, DependencyLayer dependencyLayer) throws JsonParseException {
		T response = null;
		try {
			response = mapper.readValue(json, classOfType);
		} catch (Exception e) {
			throw new JsonParseException(dependencyLayer, ErrorType.MARSHALLING,
					MarshallingErrors.JSON_TO_OBJECT_FAILURE.getErrorCode(), e.getMessage());
		}
		return response;
	}

	public <T> T getObjectFromJsonWithIgnoreUnkown(String json, Class<T> classOfType, DependencyLayer dependencyLayer) throws JsonParseException {
		T response = null;
		try {
			response = mapperIgnoreUnknown.readValue(json, classOfType);
		} catch (Exception e) {
			throw new JsonParseException(dependencyLayer, ErrorType.MARSHALLING,
					MarshallingErrors.JSON_TO_OBJECT_FAILURE.getErrorCode(), e.getMessage());
		}
		return response;
	}

	public <T> String getJsonFromObject(T inpuTPojo, DependencyLayer dependencyLayer) throws JsonParseException {
		String json = null;
		try {
			json = mapper.writeValueAsString(inpuTPojo);
		} catch (Exception e) {
			throw new JsonParseException(dependencyLayer, ErrorType.MARSHALLING, 
					MarshallingErrors.OBJECT_TO_JSON_FAILURE.getErrorCode(), e.getMessage());
		}
		return json;
	}
	
	public <T> T getObjectFromJsonWithType(String jsonPacket, TypeReference<T> type, DependencyLayer dependencyLayer) throws JsonParseException { 
		T response = null;
		try {
			response = mapper.readValue(jsonPacket, type);
		} catch (Exception e) {
			throw new JsonParseException(dependencyLayer, ErrorType.MARSHALLING, 
					MarshallingErrors.OBJECT_TO_JSON_FAILURE.getErrorCode(), e.getMessage());
		}
		return response;
	}

	public <T> T getObjectFromJsonNode(JsonNode jsonNode, TypeReference<T> typeReference) {
		T object = null;
		try {
			object = mapper.convertValue(jsonNode, typeReference);
		} catch (Exception ex) {
			LOGGER.error("Exception in converting JsonNode to Object", ex);
		}
		return object;
	}

	public <T> String getJsonFromObjectWithView(T inpuTPojo, DependencyLayer dependencyLayer, Class view) throws JsonParseException {
		String json = null;
		try {
			json = mapper.writerWithView(view).writeValueAsString(inpuTPojo);
		} catch (Exception e) {
			throw new JsonParseException(dependencyLayer, ErrorType.MARSHALLING,
					MarshallingErrors.OBJECT_TO_JSON_FAILURE.getErrorCode(), e.getMessage());
		}
		return json;
	}
}
