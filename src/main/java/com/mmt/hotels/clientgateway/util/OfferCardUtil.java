package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.OfferCard;
import com.mmt.hotels.clientgateway.pms.DetailPageOfferCardsConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.Tariff;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionItem;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.Inclusion;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.TRUE;

@Component
public class OfferCardUtil {

    @Autowired
    private Utility utility;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MobConfigHelper mobConfigHelper;

    private static final Logger LOGGER = LoggerFactory.getLogger(OfferCard.class);

    public List<CardData> getDetailPageOfferCards(HotelRates hotelRates, SearchRoomsResponse searchRoomsResponse, CommonModifierResponse commonModifierResponse) {
        DetailPageOfferCardsConfig detailPageOfferCardsConfig = mobConfigHelper.getDetailPageOfferCardsConfig();
        List<CardData> detailPageOfferCards = new ArrayList<>();
        if (detailPageOfferCardsConfig == null || CollectionUtils.isEmpty(detailPageOfferCardsConfig.getCardDetails())) {
            return null;
        }
        for (CardData cardData: detailPageOfferCardsConfig.getCardDetails()) {
            if (cardData == null || cardData.getCardInfo() == null || StringUtils.isBlank(cardData.getCardInfo().getId())) {
                continue;
            }
            try {
                OfferCard offerCard;
                try {
                    offerCard = OfferCard.valueOf(cardData.getCardInfo().getId());
                } catch (IllegalArgumentException e) {
                    LOGGER.error("Error while fetching offer card id: {}", cardData.getCardInfo().getId());
                    continue;
                }
                switch (offerCard) {
                    case LONG_STAY_BENEFITS:
                        buildLongStayBenefitCard(hotelRates, cardData, detailPageOfferCards);
                        break;
                    case DAILY_STEAL_DEAL:
                        buildDailyStealDealCard(hotelRates, cardData, detailPageOfferCards);
                        break;
                    case SALE_CAMPAIGN:
                        buildSaleCampaignCard(hotelRates, cardData, detailPageOfferCards);
                        break;
                    case ELITE_PACKAGE:
                        buildElitePackageCard(searchRoomsResponse, cardData, detailPageOfferCards);
                        break;
                    case TAJ_GIFT_CARD:
                        buildTajGiftCard(searchRoomsResponse, hotelRates, cardData, detailPageOfferCards);
                        break;
                    case NEW_USER_DISCOUNT_CARD:
                        buildNewUserDiscountCard(cardData, detailPageOfferCards, commonModifierResponse);
                    default:
                        break;
                }
            } catch (Exception e) {
                LOGGER.error("Error while building offer card: {}", cardData.getCardInfo().getId(), e);
            }
        }
        return detailPageOfferCards;
    }

    private void buildElitePackageCard(SearchRoomsResponse searchRoomsResponse, CardData cardData,
                                       List<CardData> detailPageOfferCards) {
        if (searchRoomsResponse != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms())
                && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans())) {
            SelectRoomRatePlan packageRatePlan = searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0);
            if (CollectionUtils.isEmpty(packageRatePlan.getInclusionsList())) {
                return;
            }
            List<BookedInclusion> packageBenefitInclusions = packageRatePlan.getInclusionsList().stream()
                    .filter(BookedInclusion::isPackageBenefit)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageBenefitInclusions)) {
                return;
            }
            CardInfo cardInfo = cardData.getCardInfo();
            cardInfo.setSubTextList(packageBenefitInclusions.stream()
                    .map(BookedInclusion::getText)
                    .limit(cardInfo.getSubTextList().size())
                    .collect(Collectors.toList()));
            detailPageOfferCards.add(cardData);
        }
    }

    private void buildSaleCampaignCard(HotelRates hotelRates, CardData cardData, List<CardData> detailPageOfferCards) {
        if (utility.isB2CFunnel() && hotelRates.getCampaignPojo() != null
                && StringUtils.isNotBlank(hotelRates.getCampaignPojo().getHeading())) {
            CardInfo cardInfo = cardData.getCardInfo();
            cardInfo.setTitleText(hotelRates.getCampaignPojo().getHeading());
            cardInfo.setTitleTextColor(hotelRates.getCampaignPojo().getHeadingColor());
            cardInfo.setSubText(hotelRates.getCampaignPojo().getDescription());
            cardInfo.setLargeIconURL(hotelRates.getCampaignPojo().getIconUrl());
            cardInfo.setBgImageURL(hotelRates.getCampaignPojo().getBackgroundImage());
            detailPageOfferCards.add(cardData);
        }
    }

    private void buildNewUserDiscountCard(CardData cardData, List<CardData> detailPageOfferCards, CommonModifierResponse commonModifierResponse) {
        if(TRUE.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, ExperimentKeys.NEW_USER_COUPON_CALLOUT.getKey()))){
            detailPageOfferCards.add(cardData);
        }
    }

    private void buildTajGiftCard(SearchRoomsResponse searchRoomsResponse, HotelRates hotelRates,CardData cardData, List<CardData> detailPageOfferCards) {
        List<Tariff> tariffs = null;
        if (searchRoomsResponse !=null) {
            if(CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) &&
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs())) {
                tariffs = searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs();
            } else if(CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) &&
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRatePlans()) &&
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRatePlans().get(0).getTariffs())) {
                tariffs = searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRatePlans().get(0).getTariffs();
            }
        }
        if(CollectionUtils.isNotEmpty(tariffs) && Constants.HOTEL_TAJ_GIFT_NAME.equalsIgnoreCase(tariffs.get(0).getVoucherType())) {
            CardInfo cardInfo = cardData.getCardInfo();
            cardInfo.setSubText(tariffs.get(0).getVoucherCode());
            if (hotelRates.getHotelBenefitInfo() != null) {
                cardInfo.setIconURL(hotelRates.getHotelBenefitInfo().getIconUrl());
            }
            detailPageOfferCards.add(cardData);
        }
    }

    private void buildDailyStealDealCard(HotelRates hotelRates, CardData cardData, List<CardData> detailPageOfferCards) {
        if (hotelRates.getHotelPersuasions() == null) {
            return;
        }
        List<PersuasionItem> dailyStealDealPer = getDealPersuasion(
                hotelRates, Constants.DAILY_STEAL_DEAL_TEMPLATE_ID, Constants.DAILY_STEAL_DEAL_PERSUASION_ID);
        // DSD persuasion contains 3 persuasion items out of which 3rd item contains time remaining info.
        // Without time remaining info, card will not be shown.
        if (CollectionUtils.isNotEmpty(dailyStealDealPer) && dailyStealDealPer.size() == 3) {
            long timeRemaining;
            try {
                timeRemaining = Long.parseLong(dailyStealDealPer.get(2).getPersuasionText().trim());
            } catch (Exception e) {
                return;
            }
            if(cardData.getCardInfo().getCardPayload()!=null && cardData.getCardInfo().getCardPayload().getTimerCard()!=null) {
                TimerCard timerCard = cardData.getCardInfo().getCardPayload().getTimerCard();
                timerCard.setTimerRemainingLong(timeRemaining);
            }
            detailPageOfferCards.add(cardData);
        }
    }

    private List<PersuasionItem> getDealPersuasion(HotelRates hotelRates, Integer templateId, String persuasionId) {
        if (hotelRates.getHotelPersuasions() == null) {
            return null;
        }
        Map<String, List<Object>> persuasionMap;
        try {
            String persuasionJson = objectMapperUtil.getJsonFromObject(
                    hotelRates.getHotelPersuasions(), DependencyLayer.CLIENTGATEWAY);
            persuasionMap = objectMapperUtil.getObjectFromJson(persuasionJson, Map.class,
                    DependencyLayer.CLIENTGATEWAY);
        } catch (Exception e) {
            LOGGER.error("Error while parsing hotelPersuasions map:: ", e);
            return null;
        }
        if (persuasionMap != null && CollectionUtils.isNotEmpty(persuasionMap.get(Constants.DEALS))) {
            List<Object> dealPersuasions = persuasionMap.get(Constants.DEALS);
            List<PersuasionItem> persuasionItems = new ArrayList<>();
            for (Object dealPersuasion : dealPersuasions) {
                PersuasionItem persuasionItem;
                try {
                    persuasionItem = objectMapperUtil.getObjectFromJson(
                            objectMapperUtil.getJsonFromObject(dealPersuasion, DependencyLayer.CLIENTGATEWAY),
                            PersuasionItem.class, DependencyLayer.CLIENTGATEWAY);
                } catch (Exception e) {
                    LOGGER.error("Error while parsing hotelPersuasionItem:: ", e);
                    continue;
                }
                if (persuasionItem != null && templateId.equals(persuasionItem.getTemplateType())
                        && persuasionId.equals(persuasionItem.getPersuasionId())) {
                    persuasionItems.add(persuasionItem);
                }
            }
            return persuasionItems;
        }
        return null;
    }

    private void buildLongStayBenefitCard(HotelRates hotelRates, CardData cardData, List<CardData> detailPageOfferCards) {
        if (hotelRates != null && hotelRates.getLongStayBenefits() != null
                && CollectionUtils.isNotEmpty(hotelRates.getLongStayBenefits().getInclusionsList())) {
            CardInfo cardInfo = cardData.getCardInfo();
            cardInfo.setSubTextList(hotelRates.getLongStayBenefits().getInclusionsList().stream()
                    .map(Inclusion::getCode)
                    .limit(cardInfo.getSubTextList().size())
                    .collect(Collectors.toList()));
            CardPayloadData cardPayloadData = cardData.getCardInfo().getCardPayload();
            cardPayloadData.setGenericCardData(hotelRates.getLongStayBenefits().getInclusionsList().stream()
                    .map(inclusion -> {
                        GenericCardPayloadDataCG genericCardPayloadDataCG = new GenericCardPayloadDataCG();
                        genericCardPayloadDataCG.setTitleText(inclusion.getCode());
                        genericCardPayloadDataCG.setIconUrl(Constants.LONG_STAY_GENERIC_CARD_ICON_URL);
                        return genericCardPayloadDataCG;
                    }).collect(Collectors.toList()));
            detailPageOfferCards.add(cardData);
        }
    }
}
