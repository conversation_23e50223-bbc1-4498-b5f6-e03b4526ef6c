package com.mmt.hotels.clientgateway.util;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Locale;

@Component
public class DateUtil {

    /*
    HH:MM => 24 hour format
    hh::mm => 12 hour format
    a      => am/pm
    MMM    => Jan/Feb
    DD     -> Date(22 / 02)
    YYYY   => 2022
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String MMDDYYYY = "MMddyyyy";
    public static final String DD_MMM_HH_MM_a = "dd MMM,HH:MM a"; // 24 hour format (Capital HH:MM) (a for am/pm)
    public static final String DD_MMM_hh_mm_a = "dd MMM,hh:mm a"; // 12 hour format (Small case hh::mm)
    public static final String DD_MMM_HH_MM = "dd MMM,HH:MM";

    public static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD);

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public String getDateFormatted(String inputDate, String inputFormat, String outputFormat) {
        String formattedDate = null;
        try {
            formattedDate = format(parse(inputDate, inputFormat), outputFormat);
        } catch (Exception e) {
            LOGGER.error("Error while getting formatted date :", e);
        }

        return formattedDate;
    }

    public Date parse(String dateStr, String format) {
        Date date = null;
        try {
            date = fetchSimpleDateFormat(format).parse(dateStr);
        } catch (Exception e) {
            LOGGER.error("ERROR parsing date :: ", e);
        }

        return date;
    }

    public String format(Date inputDate, String dateFormat) {
        return fetchSimpleDateFormat(dateFormat).format(inputDate);
    }

    public SimpleDateFormat fetchSimpleDateFormat(String dateFormat) {
        return new SimpleDateFormat(dateFormat);
    }

    public int getDaysDiff(LocalDate start, LocalDate end) {
        return Math.toIntExact(Math.abs(ChronoUnit.DAYS.between(start, end)));
    }

    public int getDaysDiff(String start) throws ParseException {
        LocalDate checkInDate = simpleDateFormat.parse(start).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate now = LocalDate.now();
        return getDaysDiff(now, checkInDate);
    }

    public int getDaysDiff(String start, String end) throws ParseException {
        LocalDate checkInDate = simpleDateFormat.parse(start).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate checkOutDate = simpleDateFormat.parse(end).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return getDaysDiff(checkInDate, checkOutDate);
    }

    public String convertEpochToDateTime(long epoch, String pattern) {
        try {
            LOGGER.debug("Converting DateTime to epoch for epoch {} with pattern {}",epoch, pattern);
            DateTime tillDate = new DateTime(epoch);
            DateTimeFormatter formatter = DateTimeFormat.forPattern(pattern);
            String date =  tillDate.toString(formatter);
            LOGGER.debug("Date after converting from epoch {} ",date);
            return date;
        }  catch(Exception ex) {
            LOGGER.error("Error in converting epoch to DateTime {}",ex.getStackTrace());
            LOGGER.debug("Error in converting epoch to DateTime for {} and pattern {}",epoch,pattern);
        }
        return null;
    }

}
