package com.mmt.hotels.clientgateway.BusinessProcessor;

import com.gommt.hotels.content.proto.hotelsdatav2.HotelData;
import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Request;
import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Response;
import com.gommt.hotels.content.proto.mediaid.MediaIdRequest;
import com.gommt.hotels.content.proto.mediaid.MediaIdResponse;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaRequest;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaResponse;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.grpcexecutors.StaticContentGrpcExecutor;
import com.mmt.hotels.clientgateway.response.gi.mediabyid.TaggedMediaByIdResponse;
import com.mmt.hotels.clientgateway.transformer.request.GrpcContentRequestBuilder;
import com.mmt.hotels.clientgateway.transformer.response.GrpcContentResponseBuilder;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class GrpcContentBusinessProcessor {

    @Autowired
    GrpcContentRequestBuilder grpcContentRequestBuilder;

    @Autowired
    GrpcContentResponseBuilder grpcContentResponseBuilder;

    @Autowired
    StaticContentGrpcExecutor staticContentGrpcExecutor;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    private static final Logger LOGGER = LoggerFactory.getLogger(GrpcContentBusinessProcessor.class);

    public String processTaggedMedia(String correlationKey, String voyagerId, String mmtHotelId, String gi, boolean streetViewReq) {
        String strResponse = null;
        com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse response = null;
        long startTime=System.currentTimeMillis();
        try {
            if (StringUtils.isEmpty(mmtHotelId)) {
                mmtHotelId = getHotelIdMappingDetails(correlationKey, voyagerId);
                if (StringUtils.isEmpty(mmtHotelId)) {
                    response = new com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse();
                    response.setSuccess(false);
                    LOGGER.error("Hotel Id not received from HSC");
                    return objectMapperUtil.getJsonFromObjectWithView(response, DependencyLayer.HSC, com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse.class);
                }
            }
            TaggedMediaRequest taggedMediaRequest = grpcContentRequestBuilder.buildTaggedMediaRequest(correlationKey, mmtHotelId, gi, streetViewReq);
            LOGGER.debug("Tagged Media Request: {}", JsonFormat.printer().omittingInsignificantWhitespace().print((MessageOrBuilder)taggedMediaRequest));
            TaggedMediaResponse taggedMediaResponse = staticContentGrpcExecutor.getTaggedMedia(taggedMediaRequest,"");
            metricAspect.addToTime(new Date().getTime() - startTime, MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue())+"_HSC", "", "", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), "");
            LOGGER.debug("Tagged Media Response: {}", JsonFormat.printer().omittingInsignificantWhitespace().print((MessageOrBuilder)taggedMediaResponse));
            response = grpcContentResponseBuilder.buildTaggedMediaResponse(taggedMediaResponse);
            if (response != null) {
                strResponse = objectMapperUtil.getJsonFromObjectWithView(response, DependencyLayer.HSC, com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse.class);
            }
        } catch (Exception ex) {
            LOGGER.error("Error in processTaggedMedia : {}",ex.getMessage(), ex);
            metricAspect.addToTime(new Date().getTime() - startTime, MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()), "", "", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), "");
        }
        return strResponse;
    }

    public String processMediaIdDetails(String correlationKey, String voyagerId, String mmtHotelId, String tagIds, String gi, String offset, String limit) {
        String strResponse = null;
        TaggedMediaByIdResponse response = null;
        long startTime=System.currentTimeMillis();
        try {
            if (StringUtils.isEmpty(mmtHotelId)) {
                mmtHotelId = getHotelIdMappingDetails(correlationKey, voyagerId);
                if (StringUtils.isEmpty(mmtHotelId)) {
                    response = new TaggedMediaByIdResponse();
                    response.setSuccess(false);
                    LOGGER.error("Hotel Id not received from HSC");
                    return objectMapperUtil.getJsonFromObjectWithView(response, DependencyLayer.HSC, TaggedMediaByIdResponse.class);
                }
            }
            MediaIdRequest mediaIdRequest = grpcContentRequestBuilder.buildMediaIdDetailsRequest(correlationKey, mmtHotelId, tagIds, gi, offset, limit);
            LOGGER.debug("Tagged MediaById Request: {}", JsonFormat.printer().omittingInsignificantWhitespace().print((MessageOrBuilder)mediaIdRequest));
            MediaIdResponse mediaIdResponse = staticContentGrpcExecutor.getMediaId(mediaIdRequest,"");
            metricAspect.addToTime(System.currentTimeMillis() - startTime, MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue())+"_HSC", "", "", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), "");
            LOGGER.debug("Tagged MediaById Response: {}", JsonFormat.printer().omittingInsignificantWhitespace().print((MessageOrBuilder)mediaIdResponse));
            response = grpcContentResponseBuilder.buildTaggedMediaByIdResponse(mediaIdRequest, mediaIdResponse);
            if (response != null){
                strResponse = objectMapperUtil.getJsonFromObjectWithView(response, DependencyLayer.HSC, TaggedMediaByIdResponse.class);
            }
        } catch (Exception ex) {
            LOGGER.error("Error in processMediaIdDetails : {}",ex.getMessage(), ex);
            metricAspect.addToTime(System.currentTimeMillis() - startTime, MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()), "", "", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), "");
        }
        return strResponse;
    }

    private String getHotelIdMappingDetails(String correlationKey, String hotelId) {
        HotelsDataV2Response response = null;
        long startTime = System.currentTimeMillis();
        try {
            HotelsDataV2Request hotelIdMappingRequest = grpcContentRequestBuilder.getHotelIdMappingRequest(hotelId,correlationKey);
            LOGGER.debug("Hotel GI to MMT mapping static content for HotelId - {} : {}", hotelId, JsonFormat.printer().omittingInsignificantWhitespace().print((MessageOrBuilder)hotelIdMappingRequest));
            startTime = System.currentTimeMillis();
            response = staticContentGrpcExecutor.getHotelIdMapping(hotelIdMappingRequest, "");
            if (response != null) {
                if (MapUtils.isNotEmpty(response.getResponseMap())) {
                    HotelData hotelData = response.getResponseMap().get(hotelIdMappingRequest.getVoyIds(0));
                    return hotelData != null ? hotelData.getMmtHtlId() : "";
                }
            }
            metricAspect.addToTime(System.currentTimeMillis() - startTime, "cg/HotelsDataV2", "", "", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), "");
            LOGGER.debug("Hotel GI to MMT mapping static content for HotelId - {} : {}", hotelId, JsonFormat.printer().omittingInsignificantWhitespace().print((MessageOrBuilder)response));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred while getting HotelsDataV2 for brand:GI,HotelId:{} : {}", hotelId, ex.getMessage(), ex);
            metricAspect.addToTime(System.currentTimeMillis() - startTime, "cg/HotelsDataV2", "", "", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), "");
        }
        return "";
    }

}
