package com.mmt.hotels.clientgateway.pms;

import java.util.*;

import javax.annotation.PostConstruct;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.response.MobgenJsonBO;
import com.mmt.hotels.clientgateway.response.MobgenStringsBO;
import com.mmt.hotels.clientgateway.response.searchHotels.FilterConditions;
import com.mmt.hotels.clientgateway.restexecutors.PolyglotRestExecutor;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.propertymanager.config.PropertyManager;
import com.fasterxml.jackson.databind.JsonNode;

@Component
public class MobConfigHelper {

    @Autowired
    private PropertyManager propManager;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    PolyglotRestExecutor polyglotRestExecutor;

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    CacheManager cacheManager;

    @Autowired
    PolyglotHelper polyglotHelper;

    private Map<String, SourceRegionSpecificDataConfig> sourceRegionSpecificDataMapping;

	private Map<String,Integer> corpSectionListCount;
	
	private static final Logger logger = LoggerFactory.getLogger(MobConfigHelper.class);
	
    public enum HtlMobConfigError {
        PMS_VERSION_ERROR, PMS_CONFIG_READ_ERROR
    }
	
	@PostConstruct
	public void init() {
		try {
			MobConfigProps mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
			sourceRegionSpecificDataMapping = mobConfigProps.SourceRegionSpecificDataMapping();
			corpSectionListCount = mobConfigProps.corpSectionListCount();
		}catch (Exception e) {
			logger.error("Error Ocurred in initialising mob config: ", e);
		}
	}

    private String getConfigJson() {
        MobConfigProps mobGeneralConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
        return mobGeneralConfigProps.configJson();
    }

    public Map<String, Integer> getCorpSectionListCount() {
        return corpSectionListCount;
    }

    public Map<String, SourceRegionSpecificDataConfig> getSourceRegionSpecificDataMapping() {
        return sourceRegionSpecificDataMapping;
    }

    private HotelMobConfig getBaseHotelMobileConfig() {
        return new HotelMobConfig.Builder()
                .success(true)
                .responseErrors(null)
                .build();
    }

    public HotelMobConfig getHotelMobileConfigFromPMS() throws JsonParseException {

        String configJson = getConfigJson();
        JsonNode configNode = objectMapperUtil.getObjectFromJson(configJson, JsonNode.class, DependencyLayer.CLIENTGATEWAY);

        if (configNode == null) {
            return getErrorMobileConfig(HtlMobConfigError.PMS_CONFIG_READ_ERROR);
        }

        HotelMobConfig hotelMobileConfig = getBaseHotelMobileConfig();
        hotelMobileConfig.setConfigJson(configNode);

        return hotelMobileConfig;
    }

    public static JsonNode merge(JsonNode mainNode, JsonNode updateNode) {

        Iterator<String> fieldNames = updateNode.fieldNames();
        while (fieldNames.hasNext()) {

            String fieldName = fieldNames.next();
            JsonNode jsonNode = mainNode.get(fieldName);

                if (mainNode instanceof ObjectNode) {
                    // Overwrite field
                    JsonNode value = updateNode.get(fieldName);
                    ((ObjectNode) mainNode).put(fieldName, value);
                }


        }

        return mainNode;
    }

    
    public String getHotelMobConfigAsString() throws JsonParseException{
        HotelMobConfig configResponse;
        String responseString = null;
        configResponse = getHotelMobileConfigFromPMS();
        try {
            responseString = objectMapperUtil.getJsonFromObject(configResponse, DependencyLayer.CLIENTGATEWAY);
        } catch (JsonParseException e) {
            responseString = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
            logger.error(e.toString());
        }

        return responseString;
    }
    
    /**
     * Get source region specific data like currency, domain, language etc
     * @param configResponse, httpHeaderMap
     * @return
     */
    public void getSrcRegionSpecificData(HotelMobConfig configResponse, Map<String,String> httpHeaderMap) {
   	 try {
			Pair<String, String> countryAndCity = commonHelper.getCountryAndCityCodeFromHeader(httpHeaderMap);
			String countryCode = countryAndCity.getLeft();
			MobConfigProps mobGeneralConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
			if(StringUtils.isNotBlank(countryCode) && mobGeneralConfigProps != null && 
					MapUtils.isNotEmpty(mobGeneralConfigProps.SourceRegionSpecificDataMapping())) {
				SourceRegionSpecificDataConfig regionData = new SourceRegionSpecificDataConfig();
				if(mobGeneralConfigProps.SourceRegionSpecificDataMapping().containsKey(countryCode.toUpperCase())) {
					regionData = mobGeneralConfigProps.SourceRegionSpecificDataMapping().get(countryCode);
				}else {
					regionData = mobGeneralConfigProps.SourceRegionSpecificDataMapping().get(Constants.DEFAULT_DOMAIN);
				}
				configResponse.setCurrency(regionData.getCurrency());
				configResponse.setSiteDomain(regionData.getSiteDomain());
				configResponse.setLanguage(regionData.getLanguage());
			}else {
				configResponse.setCurrency(Constants.DEFAULT_CUR_INR);
				configResponse.setSiteDomain(Constants.DEFAULT_SITE_DOMAIN);
				configResponse.setLanguage(Constants.ENGLISH);
			}
   	 }catch(Exception e) {
		 	logger.error("Exception occured in getting Country specific data", e);
   	 }
    }
    
    /**
     * Function to get error responses for HotelMobileConfig
     *
     * @param errorCode the error code
     * @return the hotel mobile config with error response
     */
    private HotelMobConfig getErrorMobileConfig(HtlMobConfigError errorCode) {
        HotelMobConfig errorMobileConfig = new HotelMobConfig.Builder()
                .success(false)
                .build();

        ErrorResponse errorResponse = new ErrorResponse();
        GenericErrorEntity genericErrorEntity = new GenericErrorEntity();
        errorResponse.setErrorList(new ArrayList<>());
        errorResponse.getErrorList().add(genericErrorEntity);
        errorMobileConfig.setResponseErrors(errorResponse);
        //genericErrorEntity.setErrorAdditionalInfo("Please check Girgit for prop values immediately");

        switch (errorCode) {
            case PMS_VERSION_ERROR:
                genericErrorEntity.setErrorCode("4400");
                genericErrorEntity.setErrorMessage("Version in PMS is null or empty after read.");
                break;
            case PMS_CONFIG_READ_ERROR:
                genericErrorEntity.setErrorCode("4401");
                genericErrorEntity.setErrorMessage("Config in PMS is null or empty after read.");
                break;
            default:
                genericErrorEntity.setErrorCode("4402");
                genericErrorEntity.setErrorMessage("Something went wrong while trying to access PMS");
                break;
        }

        logger.error("Error in trying to read hotel config from PMS with error code " + genericErrorEntity.getErrorCode());
        return errorMobileConfig;
    }

    public FilterConditions getFilterConditions() {
        try {
            MobConfigProps mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
            return mobConfigProps.filterConditions();
        } catch (Exception e) {
            logger.error("Error Ocurred in getFilterConditions: ", e);
        }
        return null;
    }

    public Map<String, List<String>> getPersuasionPlaceHoldersToShow() {
        try {
            MobConfigProps mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
            return mobConfigProps.persuasionPlaceHoldersToShow();
        } catch (Exception e) {
            logger.error("Error Ocurred in getPersuasionPlaceHoldersToShow: ", e);
        }
        return null;
    }

    public JsonNode getTickTockConfig() {
        try {
            MobConfigProps mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
            return mobConfigProps.tickTockConfig();
        } catch (Exception e) {
            logger.error("Error Occured in getTickTockConfig");
        }
        return null;
    }

    public DetailPageOfferCardsConfig getDetailPageOfferCardsConfig() {
        MobConfigProps mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
        if(mobConfigProps!=null && StringUtils.isNotBlank(mobConfigProps.detailPageOfferCards())) {
            String detailPageOfferCardsJson = mobConfigProps.detailPageOfferCards();
            try {
                return objectMapperUtil.getObjectFromJson(detailPageOfferCardsJson, DetailPageOfferCardsConfig.class,
                        DependencyLayer.CLIENTGATEWAY);
            } catch (Exception e) {
                logger.error("Error occurred while serializing detail page offer cards json");
            }
        }
        return null;
    }

    public RoomUpsellConfig getRoomUpsellConfig() {
        MobConfigProps mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
        if(mobConfigProps!=null && StringUtils.isNotBlank(mobConfigProps.roomUpsellConfig())) {
            String roomUpsellConfigJson = mobConfigProps.roomUpsellConfig();
            try {
                return objectMapperUtil.getObjectFromJson(roomUpsellConfigJson, RoomUpsellConfig.class,
                        DependencyLayer.CLIENTGATEWAY);
            } catch (Exception e) {
                logger.error("Error occurred while serializing detail page offer cards json");
            }
        }
        return null;
    }

    public String getIncognitoUrl() {
        MobConfigProps mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
        if(mobConfigProps!=null) {
            return mobConfigProps.incognitoMessageBoxImgUrl();
        }
        return null;
    }
}

