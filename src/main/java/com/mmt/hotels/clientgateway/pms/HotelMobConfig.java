package com.mmt.hotels.clientgateway.pms;

import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;

public class HotelMobConfig {

    private boolean success;


    private JsonNode configJson;
    
    private String currency;
    
    private String siteDomain;
    
    private String language;

    private ErrorResponse responseErrors;

    private HotelMobConfig(Builder builder) {
        setSuccess(builder.success);
        setConfigJson(builder.configJson);
        setResponseErrors(builder.responseErrors);
    }

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }


    public JsonNode getConfigJson() {
        return configJson;
    }

    public void setConfigJson(JsonNode configJson) {
        this.configJson = configJson;
    }


	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getSiteDomain() {
		return siteDomain;
	}

	public void setSiteDomain(String siteDomain) {
		this.siteDomain = siteDomain;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public ErrorResponse getResponseErrors() {
		return responseErrors;
	}

	public void setResponseErrors(ErrorResponse responseErrors) {
		this.responseErrors = responseErrors;
	}

	public static final class Builder {
        private boolean success;
        private JsonNode configJson;
        private ErrorResponse responseErrors;

        public Builder() {
        }

        public Builder success(boolean val) {
            success = val;
            return this;
        }

        public Builder configJson(JsonNode val) {
            configJson = val;
            return this;
        }

        public Builder responseErrors(ErrorResponse val) {
            responseErrors = val;
            return this;
        }
        
        public HotelMobConfig build() {
            return new HotelMobConfig(this);
        }
    }
}
