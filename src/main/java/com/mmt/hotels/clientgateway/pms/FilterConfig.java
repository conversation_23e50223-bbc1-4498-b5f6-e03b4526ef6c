package com.mmt.hotels.clientgateway.pms;

import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.filter.FilterPricingOption;
import com.mmt.propertymanager.config.PropertyQualifier;
import com.mmt.propertymanager.util.JsonConvertor;
import org.aeonbits.owner.Config;
import org.aeonbits.owner.Mutable;
import org.aeonbits.owner.Reloadable;

import java.util.List;
import java.util.Map;

@Config.LoadPolicy(Config.LoadType.MERGE)
@Config.Sources({PMSUrls.GIRGIT_HOST + PMSUrls.GIRGIT_FILTER_URL})
@PropertyQualifier("cgGIFilterConfig")
public interface FilterConfig extends Reloadable, Mutable {


    @DefaultValue("{}")
    String baseFilterSettings();

    @DefaultValue("{}")
    String suggestedFilterSettings();

    @DefaultValue("{}")
    String desktopCorpFilterSettings();

    @DefaultValue("{}")
    String desktopB2CFilterSettings();

    @DefaultValue("{}")
    String pwaCorpFilterSettings();

    @DefaultValue("{}")
    String pwaB2CFilterSettings();

    @DefaultValue("{}")
    String androidCorpFilterSettings();

    @DefaultValue("{}")
    String androidB2CFilterSettings();

    @DefaultValue("{}")
    String iosCorpFilterSettings();

    @DefaultValue("{}")
    String iosB2CFilterSettings();

    @DefaultValue("{}")
    String defaultPriceHistogram();

    @DefaultValue("{}")
    String defaultPriceHistogramCorp();

    @DefaultValue("{}")
    String pwaMyPartnerFilterSettings();

    @DefaultValue("{}")
    String desktopMyPartnerFilterSettings();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    Map<String, List<Filter>> compositeFilterConfig();

    @DefaultValue("{}")
    String amenitiesCategoryConfig();

    @DefaultValue("{}")
    String amenitiesCategoryConfigPolyGlot();

    @ConverterClass(JsonConvertor.class)
    FilterPricingOption pricingOption();
}
