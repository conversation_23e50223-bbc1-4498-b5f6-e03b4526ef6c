package com.mmt.hotels.clientgateway.pms;

import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.response.searchHotels.FilterConditions;
import kafka.utils.Json;
import org.aeonbits.owner.Mutable;
import org.aeonbits.owner.Reloadable;

import java.util.List;
import java.util.Map;

import org.aeonbits.owner.Config.LoadPolicy;
import org.aeonbits.owner.Config.LoadType;
import org.aeonbits.owner.Config.Sources;

import com.mmt.propertymanager.config.PropertyQualifier;
import com.mmt.propertymanager.util.JsonConvertor;

@LoadPolicy(LoadType.MERGE)
@Sources({ PMSUrls.GIRGIT_HOST + PMSUrls.GIRGIT_MOB_CONFIG_URL })
@PropertyQualifier("mobConfigProps")
public interface MobConfigProps extends Reloadable, Mutable {


	public String version();

	public String configJson();

	public String detailPageOfferCards();

	public String roomUpsellConfig();


	@DefaultValue("100")
	public int guavaCacheDetailResponsesCapacity();

	@DefaultValue("200")
	public int hotelInfoCacheResponseCapacity();

	@DefaultValue("2")
	public int guavaCacheExpiryMinutes();

	@DefaultValue("100")
	public int guavaCacheLandingResponsesCapacity();

	@DefaultValue("2")
	public int guavaLandingCacheExpiryMinutes();

	@Key("DETAIL.API.PROPERTIES.MAP")
	@ConverterClass(JsonConvertor.class)
	public Map<String, ApiProperties> detailApiSettings();

	@Separator(",")
	@DefaultValue("500,500,500,500,500,500,500,500")
	public List<Integer> detailApiTimeOuts();

	@Key("LANDING.API.PROPERTIES.MAP")
	@ConverterClass(JsonConvertor.class)
	public Map<String, ApiProperties> landingApiSettings();

	@Separator(",")
	@DefaultValue("600,600,6000, 600")
	public List<Integer> landingApiTimeOuts();

	@ConverterClass(JsonConvertor.class)
	Map<String, Integer> seekConceptPriorities();

	@ConverterClass(JsonConvertor.class)
	Map<String, Integer> seekSubConceptPriorities();
	
	@ConverterClass(JsonConvertor.class)
	@DefaultValue("{\"US\":{\"currency\":\"USD\",\"siteDomain\":\"US\",\"language\":\"ENGLISH\"},\"AE\":{\"currency\":\"AED\",\"siteDomain\":\"AE\",\"language\":\"ENGLISH\"},\"DEFAULT\":{\"currency\":\"INR\",\"siteDomain\":\"IN\",\"language\":\"ENGLISH\"}}")
	Map<String, SourceRegionSpecificDataConfig> SourceRegionSpecificDataMapping();
	
	@Key("domainBasedPersuasionsFiltering")
	@ConverterClass(JsonConvertor.class)
	@DefaultValue("{\"AE\":[\"Near_Indian_Resturants\",\"North_Indian_Restaurants\"]}")
	Map<String, List<String>> FilterPersuasionsMapping();

	@ConverterClass(JsonConvertor.class)
	@DefaultValue("{ \"BOOKED_BY_COMPANY\": 5, \"LAST_BOOKED_HOTELS\": 5, \"MYBIZ_RECOMMENDED_HOTELS\": 5, \"PREFERRED_BY_COMPANY\": 5, \"RECENTLY_VIEWED_HOTELS\": 5 }")
	Map<String,Integer> corpSectionListCount();
		   
    @DefaultValue("500")
	int latchWaitTimeInMillSec();

	@DefaultValue("100")
	public int collectionApiTimeOut();


	@ConverterClass(JsonConvertor.class)
	@DefaultValue("{\"range\":{\"minValue\":10,\"maxValue\":3500},\"categoriesIncluded\":[\"MMT Value Stays\"],\"categoriesExcluded\":[]}")
	FilterConditions filterConditions();

	@ConverterClass(JsonConvertor.class)
	@DefaultValue("{\"SIMILAR_HOTELS\":[\"PLACEHOLDER_IMAGE_LEFT_TOP\",\"PLACEHOLDER_CARD_M4\",\"PLACEHOLDER_CARD_M1\"]}")
	Map<String, List<String>> persuasionPlaceHoldersToShow();

	JsonNode tickTockConfig();

	@DefaultValue("")
	String incognitoMessageBoxImgUrl();

}
