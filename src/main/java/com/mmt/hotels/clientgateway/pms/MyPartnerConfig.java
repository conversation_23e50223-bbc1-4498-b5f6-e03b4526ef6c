package com.mmt.hotels.clientgateway.pms;

import com.mmt.propertymanager.config.PropertyQualifier;
import com.mmt.propertymanager.util.JsonConvertor;
import org.aeonbits.owner.Config;
import org.aeonbits.owner.Mutable;
import org.aeonbits.owner.Reloadable;

import java.util.List;
import java.util.Map;

@Config.LoadPolicy(Config.LoadType.MERGE)
@Config.Sources({PMSUrls.GIRGIT_HOST + PMSUrls.GIRGIT_MYPARTNER_CONFIG_URL})
@PropertyQualifier("myPartnerConfig")
public interface MyPartnerConfig extends Reloadable, Mutable {

	@DefaultValue("{}")
	@ConverterClass(JsonConvertor.class)
	Map<String, Double> discountParameters();
}