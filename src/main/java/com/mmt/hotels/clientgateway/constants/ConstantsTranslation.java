package com.mmt.hotels.clientgateway.constants;

public class ConstantsTranslation {

    public static final String PRICE_PN_TITLE = "price_pn_title";
    public static final String PRICE_PNT_TITLE = "price_pnt_title";
    public static final String PRICE_PRN_TITLE = "price_prn_title";
    public static final String PRICE_PRNT_TITLE = "price_prnt_title";
    public static final String PRICE_TP_TITLE = "price_tp_title";
    public static final String PRICE_TPT_TITLE = "price_tpt_title";
    public static final String PRICE_PPPN_TITLE = "price_pppn_title";
    public static final String RECOMMENDED_HOTELS_HEADING = "recommended_hotels_heading";
    public static final String RECOMMENDED_HOTELS_HEADING_NEW = "recommended_hotels_heading_new";
    public static final String GETAWAYS_RECOMMENDED_HEADING = "NEARBY_GETAWAYS_HEADING";
    public  static final String PARTIAL_REFUNDABLE_CHECKIN_TEXT = "PARTIAL_REFUNDABLE_CHECKIN_TEXT";
    public  static final String PARTIAL_REFUNDABLE_TEXT = "PARTIAL_REFUNDABLE_TEXT";
    public static final String ACTION_VIEW_DETAILS = "ACTION_VIEW_DETAILS";
    public static final String ABO_BANNER_INFO_TEXT = "ABO_BANNER_INFO_TEXT";
    public static final String ACTION_CONTINUE = "ACTION_CONTINUE";
    public static final String FILTER_GROUP_SPLITTER_DPT = "#";
    public static final String FILTER_GROUP_VALUE_SPLITTER_DPT = "=";
    public static final String MATCHMAKER_AREA = "area";
    public static final String STREET_VIEW = "STREET_VIEW";
    public static final String STREET_VIEW_TEXT = "STREET_VIEW_TEXT";
    public static final String MATCHMAKER_POI = "poi";
    public static final String FILTER_VALUE_SPLITTER_DPT = "\\^";
    public static final String COUPLE_FRIENDLY_TITLE = "COUPLE_FRIENDLY";
    public static final String COUPLE_FRIENDLY_RULE_TITLE = "COUPLE_FRIENDLY_RULE_TITLE";
    public static final String COUPLE_FRIENDLY_RULE_DESC = "COUPLE_FRIENDLY_RULE_DESC";

    public  static final String PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT = "PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT";
    public static final String GIFT_CARD_TEXT = "GIFT_CARD_TEXT";
    public static final String GIFT_CARD_TEXT_GI = "GIFT_CARD_TEXT_GI";
    public static final String MOST_BOOKED_HEADING = "MOST_BOOKED_HEADING";
    public static final String TITLE_MISS = "TITLE_MISS";
    public static final String TITLE_MS = "TITLE_MS";
    public static final String TITLE_MRS = "TITLE_MRS";

    public static final String ENTIRE = "ENTIRE";
    public static final String BED_TEXT = "BED_TEXT";
    public static final String BEDS_TEXT = "BEDS_TEXT";
    public static final String ROOM_TEXT = "ROOM_TEXT";
    public static final String ROOMS_TEXT = "ROOMS_TEXT";

    public static final String COUPON_NA_BNPL0 = "COUPON_NA_BNPL0";
    public static final String COUPON_NA_BNPL0_GIFT_CARD = "COUPON_NA_BNPL0_GIFT_CARD";
    public static final String COUPON_NA_BNPL1 = "COUPON_NA_BNPL1";
    public static final String COUPON_NA_BNPL1_GIFT_CARD = "COUPON_NA_BNPL1_GIFT_CARD";
    public static final String COUPON_NA_GIFT_CARD = "COUPON_NA_GIFT_CARD";

    // avail rooms pricing constants
    public static final String PRICE_TYPE_SUM = "price_type_sum";
    public static final String PRICE_TYPE_DIFF = "price_type_diff";
    public static final String TOTAL_AMOUNT_LABEL = "total_amount_label";
    public static final String AMOUNT_YOU_PAYING_NOW_LABEL = "amount_you_paying_now_label";
    public static final String AMOUNT_YOU_PAYING_AT_HOTEL_LABEL = "amount_you_paying_at_hotel_label";
    public static final String NO_CARD_REQUIRED_SUBLINE = "no_card_required_subline";
    public static final String BASE_FARE_LABEL = "base_fare_label";
    public static final String BASE_FARE_WITH_TAX_LABEL = "base_fare_with_tax_label";
    public static final String INSURANCE_LABEL = "insurance_label";

    public static final String BEDROOMS = "BEDROOMS";

    public static final String BEDROOM = "BEDROOM";
    public static final String TOTAL_DISCOUNT_LABEL = "total_discount_label";
    public static final String HOTELIER_DISCOUNT_LABEL = "hotelier_discount_label";
    public static final String CDF_DISCOUNT_LABEL = "cdf_discount_label";
    public static final String BLACK_DISCOUNT_LABEL = "BLACK_DISCOUNT_LABEL";
    public static final String WALLET_LABEL = "wallet_label";
    public static final String PROMO_CASH = "promo_cash";
    public static final String PRICE_AFTER_DISCOUNT_LABEL = "price_after_discount_label";
    public static final String PAY_NOW_LABEL = "pay_now_label";
    public static final String TAXES_LABEL = "taxes_label";
    public static final String TAXES_ROOM_UPGRADE_LABEL = "TAXES_ROOM_UPGRADE_LABEL";
    public static final String GST_LABEL = "gst_label";
    public static final String HOTEL_TAX_LABEL = "hotel_tax_label";
    public static final String SERVICE_FEES_LABEL = "service_fees_label";
    public static final String SERVICE_CHARGE_LABEL = "service_charge_label";
    public static final String AFFILIATE_FEES_LABEL = "affiliate_fees_label";
    public static final String SERVICE_FEES_REVERSAL_LABLE = "service_fees_reversal_lable";
    public static final String EFFECTIVE_COUPON_APPLIED_LABLE = "effective_coupon_applied_lable";

    public static final String VIEW_360_IMAGE = "VIEW_360_IMAGE";
    public static final String VIEW_IMAGE = "VIEW_IMAGE";

    public static final String CAT_STATIC_POLICY = "cat_static_policy";
    public static final String CAT_HOUSE_RULES = "cat_house_rules";
    public static final String SUBCAT_COMMON_RULES = "subcat_common_rules";
    public static final String SUBCAT_EXTRA_BED = "subcat_extra_bed";
    public static final String CAT_MUST_READ = "cat_must_read";
    public static final String TITLE_MUST_READ = "title_must_read";
    /*
    thankyou constants
     */

    public static final String AMOUNT_BREAKUP_DEDUCTED = "amount_breakup_deducted";
    public static final String AMOUNT_BREAKUP_PENDING = "amount_breakup_pending";
    public static final String AMOUNT_BREAKUP_TOTAL = "amount_breakup_total";

    public static final String NON_REFUNDABLE_TEXT = "non_refundable_text";
    public static final String NON_REFUNDABLE_SUBTEXT = "non_refundable_subtext";
    public static final String FREE_CANCELL_BNPL_SUBTEXT = "free_cancell_bnpl_subtext";
    public static final String CANCEL_POLICY_BNPL_SUBTEXT = "CANCEL_POLICY_BNPL_SUBTEXT";
    public static final String CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT = "CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT";
    public static final String OK_GOT_IT_CTA_TEXT = "OK_GOT_IT_CTA_TEXT";
    public static final String LOS_TITLE_TEXT = "LOS_TITLE_TEXT";

    // for bnpl 0
    public static final String CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT = "CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT";

    public static final String UNRATED_SR = "unrated_sr";
    public static final String KID_FRIENDLY = "kid_friendly";
    public static final String COUPLE_FRIENDLY = "couple_friendly";
    public static final String STAYCATION = "staycation";
    public static final String GREATVALUE = "greatvalue";
    public static final String MyBiz_Assured = "MyBiz_Assured";
    public static final String MMT_Assured = "MMT_Assured";

    public static final String ADDITIONAL_FEE_SUBTEXT_ROOMS = "additional_fee_subtext_rooms";
    public static final String ADDITIONAL_FEE_SUBTEXT_ROOM = "additional_fee_subtext_room";
    public static final String ADDITIONAL_FEE_SUBTEXT_ADULT = "additional_fee_subtext_adult";
    public static final String ADDITIONAL_FEE_SUBTEXT_ADULTS = "additional_fee_subtext_adults";
    public static final String ADDITIONAL_FEE_SUBTEXT_CHILD = "additional_fee_subtext_child";
    public static final String ADDITIONAL_FEE_SUBTEXT_CHILDREN = "additional_fee_subtext_children";
    public static final String ADDITIONAL_FEE_SUBTEXT_NIGHT = "additional_fee_subtext_night";
    public static final String ADDITIONAL_FEE_SUBTEXT_NIGHTS = "additional_fee_subtext_nights";
    public static final String PG_CHARGES_TEXT = "PG_CHARGES_TEXT";
    public static final String ADDITIONAL_FEE_TRANSFERS_MSG = "additional_fee_transfers_msg";

    //Avail rooms properties.
    public static final String GSTN_NOT_AVAILABLE_TEXT = "availrooms_gstn_not_available_text";
    public static final String TIN_NO_NOT_AVAILABLE_TEXT = "TIN_NO_NOT_AVAILABLE_TEXT";
    public static final String NO_MEALS_INCLUDED_TEXT = "no_meals_included_text";
    public static final String PRICE_INCREASE_ALERT_TEXT = "alert_price_increase_text";
    public static final String PRICE_INCREASE_ALERT_SUB_TEXT = "alert_price_increase_subtext";
    public static final String PRICE_DECREASE_ALERT_TEXT = "alert_price_decrease_text";
    public static final String PRICE_DECREASE_ALERT_SUB_TEXT = "alert_price_decrease_subtext";
    public static final String CANCEL_POLICY_CHANGE_ALERT_TEXT = "alert_cancel_policy_change_text";
    public static final String MEAL_PLAN_CHANGE_ALERT_TEXT = "alert_mean_plan_change_text";
    public static final String CTRIP_NON_INSTANT_TEXT = "alert_confirmation_policy_text";
    public static final String CANCELLATION_POLICY_NR_NON_INSTANT_TEXT = "cancellation_policy_nr_non_instant_text";

    public static final String PRICE_CHANGE_ALERT_TEXT = "alert_price_change_text";
    public static final String PRICE_CHANGE_INCREASE_ALERT_SUB_TEXT = "alert_price_change_increase_subtext_based_upon_currency";
    public static final String PRICE_CHANGE_DECREASE_ALERT_SUB_TEXT = "alert_price_change_decrease_subtext_based_upon_currency";
    public static final String PRICE_CHANGE_DECREASE_OLD_VERSION_ALERT_SUB_TEXT = "alert_price_change_old_version_subtext";
    public static final String PRICE_CHANGE_INCREASE_ALERT_TEXT_OLD_VERSION = "alert_price_change_increase_old_version_text_based_upon_currency";
    public static final String PRICE_CHANGE_DECREASE_ALERT_TEXT_OLD_VERSION = "alert_price_change_decrease_old_version_text_based_upon_currency";

    public static final String PRICE_INCREASE = "INCREASE";
    public static final String PRICE_DECREASE = "DECREASE";
    public static final String PRICE_NO_CHANGE = "NO_CHANGE";
    //thank you
    public static final String PENDING_AMOUNT_BNPL_TEXT = "thankyou_remaining_amount_bnpl_text";
    public static final String PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT = "thankyou_remaining_amount_bnpl_new_variant_text";
    public static final String PENDING_AMOUNT_PAH_DEFAULT_TEXT = "thankyou_remaining_amount_pah_default_text";
    public static final String PENDING_AMOUNT_PAH_TEXT = "thankyou_remaining_amount_pah_text";
    public static final String FAILED_BOOKING_REFUND_DURATION = "thankyou.failed.booking.refund.duration";
    public static final String PENDING_BOOKING_CONFIRMATION_TIME = "thankyou.booking.pending.corfirmation.time";
    public static final String PENDING_CTATEXT = "thankyou_pending_booking_cta_text";
    public static final String BNPL_PAYMENT_BREAKUP_LABEL = "thankyou_bnpl_payment_persuasion_label";
    public static final String PAH_PAYMENT_BREAKUP_LABEL = "thankyou_pah_payment_persuasion_label";

    // common transfer
    public static final String NO_COUPON_AVAILABLE_TEXT = "no_coupon_available_text";
    public static final String EARLY_CHECKIN_TITLE = "early_checkin_title";
    public static final String MANDATORY_CHARGES_SECTION_TITLE = "mandatory_charges_section_title";
    public static final String MANDATORY_CHARGES_SECTION_DESC = "mandatory_charges_section_desc";
    public static final String MANDATORY_CHARGES_BREAKUP_SUBTITLE = "mandatory_charges_section_breakup_subtitle";
    public static final String MANDATORY_CHARGES_DEFAULT_PRICE_TEXT = "mandatory_charges_section_default_price_text";
    public static final String INTL_PAH_NON_REFUNDABLE_TEXT = "intl_pah_nr_policy_text";
    public static final String INTL_PAH_FREE_CANCELLATION_TEXT = "intl_pah_fc_policy_text";
    public static final String ROOM_BY_ROOM_PROPERTY_LAYOUT_TEXT = "ROOM_BY_ROOM_PROPERTY_LAYOUT_TEXT";
    public static final String ENTIRE_PROPERTY_LAYOUT_TEXT = "ENTIRE_PROPERTY_LAYOUT_TEXT";
    public static final String GI_MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT = "GI_MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT";
    public static final String INTL_PAH_FC_NON_INGO_POLICY_TEXT = "intl_pah_fc_non_ingo_policy_text";
    public static final String INTL_NR_SUPPLIER_SPECIFIC_TEXT = "intl_nr_supplier_specific_text";
    public static final String PROPERTY_RULES_DISCLAIMER_TEXT = "property_rules_disclaimer_text";
    public static final String SPECIAL_REQUEST_TEXT = "selected_special_request_text";
    public static final String SPECIAL_REQUEST_SUBTEXT = "selected_special_request_sub_text";

    public static final String HOTELIER_DISCOUNT_LABEL_CORP = "HOTELIER_DISCOUNT_LABEL_CORP";
    public static final String PRICE_TOOL_TIP = "PRICE_TOOL_TIP";
    public static final String DAYS = "{days}";
    public static final String AMOUNT = "{amount}";
    public static final String CURRENCY = "{currency}";
    public static final String PERCENTAGE = "{PERCENTAGE}";
    public static final String UPSELL_LINKED_VALUE = "{LINKED_VALUE}";
    public static final String MEAL_RATING = "{MEAL_RATING}";
    public static final String RESTAURANT_DISTANCE = "{RESTAURANT_DISTANCE}";

    public static final String GUESTS = "GUESTS";
    public static final String GUESTS_AND_ROOMS = "GUESTS_AND_ROOMS";
    public static final String GUESTS_AND_BEDS = "GUESTS_AND_BEDS";
    public static final String LONGSTAY_HIGHLIGHTED_TEXT = "LONGSTAY_HIGHLIGHTED_TEXT";
    public static final String ZERO_PAYMENT_NOW_WITH_CC = "ZERO_PAYMENT_NOW_WITH_CC";
    public static final String BNPL_NEW_VARIANT_TEXT = "BNPL_NEW_VARIANT_TEXT";

    public static final String BNPL_NEW_VARIANT_TEXT_GI = "BNPL_NEW_VARIANT_TEXT_GI";
    public static final String BNPL_GCC_TEXT = "BNPL_GCC_TEXT";
    public static final String BNPL_ZERO_VARIANT_TEXT = "BNPL_ZERO_VARIANT_TEXT";

    public static final String BNPL_ZERO_VARIANT_TEXT_GI = "BNPL_ZERO_VARIANT_TEXT_GI";
    public static final String BNPL_ZERO_VARIANT_TEXT_GI_AVAILABLE = "BNPL_ZERO_VARIANT_TEXT_GI_AVAILABLE";

    //RTB related texts
    public static final String RTB_CARD_TITLE = "RTB_CARD_TITLE";
    public static final String RTB_CARD_TEXT = "RTB_CARD_TEXT";
    public static final String RTB_TRIP_CARD_ACTION_TEXT = "RTB_TRIP_CARD_ACTION_TEXT";
    public static final String RTB_TRIP_CARD_HEADER_SUB_TEXT = "RTB_TRIP_CARD_HEADER_SUB_TEXT";
    public static final String RTB_TRIP_CARD_HEADER_TEXT = "RTB_TRIP_CARD_HEADER_TEXT";
    public static final String RTB_TRIP_CARD_TEXT1 = "RTB_TRIP_CARD_TEXT1";
    public static final String RTB_TRIP_CARD_TEXT2 = "RTB_TRIP_CARD_TEXT2";
    public static final String RTB_TRIP_CARD_TITLE = "RTB_TRIP_CARD_TITLE";
    public static final String RTB_CHAT_CARD_TITLE = "RTB_CHAT_CARD_TITLE";
    public static final String RTB_CHAT_CARD_SUBTITLE = "RTB_CHAT_CARD_SUBTITLE";
    public static final String RTB_CHAT_CARD_ACTION_TEXT = "RTB_CHAT_CARD_ACTION_TEXT";
    public static final String RTB_ALERT_TEXT = "RTB_ALERT_TEXT";
    public static final String RTB_CHANGE_ALERT_TEXT = "RTB_CHANGE_ALERT_TEXT";
    public static final String STAR_FACILITIES = "STAR_FACILITIES";
    public static final String STAR_FACILITIES_TITLE_GI = "STAR_FACILITIES_TITLE_GI";
    public static final String THANkYOU_INSURANCE_SUBTEXT = "THANKYOU_INSURANCE_CARD_SUB_TEXT";
    public static final String TOP_RATED = "TOP_RATED";
    public static final String RTB_TITLE = "RTB_TITLE";
    public static final String RTB_CARD_DESC = "RTB_CARD_DESC";
    public static final String RTB_PRE_APPROVED_TEXT = "RTB_PRE_APPROVED_TEXT";
    public static final String RTB_TEXT = "RTB_TEXT";
    public static final String COMPLETE_YOUR_BOOKING = "COMPLETE_YOUR_BOOKING";

    public static final String KNOW_MORE = "KNOW_MORE";

    public static final String FILTER_LUXE_PACKAGE_TEXT = "FILTER_LUXE_PACKAGE_TEXT";
    public static final String FILTER_NON_LUXE_PACKAGE_TEXT = "FILTER_NON_LUXE_PACKAGE_TEXT";
    public static final String PERSUASION_LUXE_PACKAGE_TEXT = "PERSUASION_LUXE_PACKAGE_TEXT";
    public static final String PERSUASION_NON_LUXE_PACKAGE_TEXT = "PERSUASION_NON_LUXE_PACKAGE_TEXT";

    //FAQ Properties
    public static final String FAQ_TITLE = "Frequently Asked Questions";
    public static final String FAQ_HINT = "Search";
    public static final String FAQ_ITEM_COUNT = "3";
    public static final String FAQ_EXTRA_TEXT = "All %d answered questions";

    public static final String SELECT_ROOM_1_AMENITIES_BANNER = "SELECT_ROOM_1_AMENITIES_BANNER";
    public static final String SELECT_ROOM_2_AMENITIES_BANNER = "SELECT_ROOM_2_AMENITIES_BANNER";
    public static final String SELECT_ROOM_3_AMENITIES_BANNER = "SELECT_ROOM_3_AMENITIES_BANNER";
    public static final String ROOM_NAME = "{ROOM_NAME}";
    public static final String AMENITY_1 = "{AMENITY_1}";
    public static final String AMENITY_2 = "{AMENITY_2}";
    public static final String AMENITY_3 = "{AMENITY_3}";

    public static final String DAY_USE = "DAY_USE";
    public static final String DAY_USE_ROOM_ALERT_TEXT = "DAY_USE_ROOM_ALERT_TEXT";
    public static final String DAY_USE_ROOM_LABEL = "DAY_USE_ROOM_LABEL";

    public static final String CORP_BOTTOM_SHEET_HEADING_MOBILE = "CORP_BOTTOM_SHEET_HEADING_MOBILE";
    public static final String CORP_BOTTOM_SHEET_SUBHEADING_MOBILE = "CORP_BOTTOM_SHEET_SUBHEADING_MOBILE";
    public static final String RATED_HIGH_BT_MOBILE = "RATED_HIGH_BT_MOBILE";
    public static final String MYBIZ_ASSURED_FILTER_CARD_CTA = "MYBIZ_ASSURED_FILTER_CARD_CTA";
    public static final String GST_INVOICE_ASSURANCE_TEXT = "GST_INVOICE_ASSURANCE_TEXT";
    public static final String BPG_TEXT = "BPG_TEXT";
    public static final String RATED_HIGH_BT = "RATED_HIGH_BT";
    public static final String HOMESTAY_DISPLAY_TEXT = "HOMESTAY_DISPLAY_TEXT";
    public static final String CONTEXTUAL_POPULAR_FILTER_TITLE = "CONTEXTUAL_POPULAR_FILTER_TITLE";
    public static final String BATCH_COLLECTIONS_TITLE = "BATCH_COLLECTIONS_TITLE";
    public static final String RESTRICTIONS = "RESTRICTIONS";
    public static final String EXTRA_BED_POLICY = "EXTRA_BED_POLICY";
    public static final String FOOD_AND_DINING = "FOOD_AND_DINING";
    public static final String ROOM_DETAILS_EXTRA_GUEST_INFO_HEADING = "ROOM_DETAILS_EXTRA_GUEST_INFO_HEADING";
    public static final String DEAL_ICON_URL = "DEAL_ICON_URL";
    public static final String EXTRA_GUEST_DEAL_TEXT_STYLE = "EXTRA_GUEST_DEAL_TEXT_STYLE";
    public static final String BOOK_NOW = "BOOK_NOW";
    public static final String QUICK_BOOK_DESKTOP_CTA = "QUICK_BOOK_DESKTOP_CTA";
    public static final String QUICK_BOOK_DESKTOP_TITLE = "QUICK_BOOK_DESKTOP_TITLE";
    public static final String QUICK_BOOK_DESKTOP_SUBTITLE = "QUICK_BOOK_DESKTOP_SUBTITLE";
    public static final String QUICK_BOOK_DESKTOP_MODAL_SUBTITLE = "QUICK_BOOK_DESKTOP_MODAL_SUBTITLE";

    public static final String LOCAL_CURRENCY_TEXT = "LOCAL_CURRENCY_TEXT";
    public static final String PAY_AT_HOTEL_POLICY_TEXT = "PAY_AT_HOTEL_POLICY_TEXT";
    public static final String PAY_AT_HOTEL_POLICY_TEXT_GENERIC = "PAY_AT_HOTEL_POLICY_TEXT_GENERIC";
    public static final String LOGIN_PERSUASION_TEXT = "LOGIN_PERSUASION_TEXT";
    public static final String LOGIN_PERSUASION_SUBTEXT = "LOGIN_PERSUASION_SUBTEXT";
    public static final String LOGIN_PERSUASION_TEXT_GCC = "LOGIN_PERSUASION_TEXT_GCC";
    public static final String LOGIN_PERSUASION_SUBTEXT_GCC = "LOGIN_PERSUASION_SUBTEXT_GCC";

    public static final String SPACE_OCCUPANCY_TEXT = "SPACE_OCCUPANCY_TEXT";
    public static final String SPACE_SINGLE_OCCUPANCY_TEXT = "SPACE_SINGLE_OCCUPANCY_TEXT";

    public static final String PAN_CARD_DETAILS_TITLE = "PAN_CARD_DETAILS_TITLE";
    public static final String PAN_CARD_DETAILS_DESCRIPTION = "PAN_CARD_DETAILS_DESCRIPTION";

    public static final String MYBIZ_DIRECT_HOTEL_APPS_DISTANCE_TEXT = "MYBIZ_DIRECT_HOTEL_APPS_DISTANCE_TEXT";
    public static final String MYBIZ_DIRECT_HOTEL_DESKTOP_DISTANCE_TEXT = "MYBIZ_DIRECT_HOTEL_DESKTOP_DISTANCE_TEXT";
    public static final String MYBIZ_DIRECT_HOTEL_TAG = "MYBIZ_DIRECT_HOTEL_TAG";
    public static final String MYBIZ_DIRECT_HOTEL_SUBHEADING = "MYBIZ_DIRECT_HOTEL_SUBHEADING";
    public static final String PAH_GCC_TEXT = "PAH_GCC_TEXT";
    public static final String PACKAGE_RATE_TEXT = "PACKAGE_RATE_TEXT";
    public static final String TC_CLAUSE_TEXT = "TC_CLAUSE_TEXT";
    public static final String TC_HEADING_TEXT = "TC_HEADING_TEXT";
    public static final String TC_SUBHEADING_TEXT = "TC_SUBHEADING_TEXT";

    //    TripMoney paylater texts
    public static final String PAY_LATER_TITLE = "PAY_LATER_TITLE";
    public static final String PAY_LATER_SUBTITLE = "PAY_LATER_SUBTITLE";
    public static final String PAY_LATER_CTA_TEXT = "PAY_LATER_CTA_TEXT";
    public static final String PAY_LATER_SUCCESS_TITLE = "PAY_LATER_SUCCESS_TITLE";
    public static final String PAY_LATER_FAILURE_TITLE = "PAY_LATER_FAILURE_TITLE";
    public static final String PAY_LATER_SUCCESS_SUBTITLE = "PAY_LATER_SUCCESS_SUBTITLE";
    public static final String PAY_LATER_FAILURE_SUBTITLE = "PAY_LATER_FAILURE_SUBTITLE";
    public static final String PAY_LATER_SUCCESS_CARD_TITLE = "PAY_LATER_SUCCESS_CARD_TITLE";
    public static final String PAY_LATER_DESCRIPTION = "PAY_LATER_DESCRIPTION";


    public static final String MMT_EXCLUSIVE = "MMT_EXCLUSIVE";
    public static final String LANDING_GB_MSG = "landingGBMsg";
    public static final String TIMESLOT_AM_AM = "TIMESLOT_AM_AM";
    public static final String TIMESLOT_AM_PM = "TIMESLOT_AM_PM";
    public static final String TIMESLOT_PM_AM = "TIMESLOT_PM_AM";
    public static final String TIMESLOT_PM_PM = "TIMESLOT_PM_PM";
    public static final String STAY_TIME_HOURS = "HOURS";

    public static final String DAYUSE_PER_NIGHT = "DAYUSE_PER_NIGHT";
    public static final String GI_LUCKY_TIMER_TEXT = "GI_LUCKY_TIMER_TEXT";

    public static final String DAYUSE_PER_NIGHT_TAX = "DAYUSE_PER_NIGHT_TAX";

    /*Group Booking Constants*/
    public static final String ADD_BREAKFAST = "ADD_BREAKFAST";
    public static final String ADD_BREAKFAST_LUNCH_AND_DINNER = "ADD_BREAKFAST_LUNCH_AND_DINNER";
    public static final String ADD_BREAKFAST_LUNCH_OR_DINNER = "ADD_BREAKFAST_LUNCH_OR_DINNER";
    public static final String ADD_BREAKFAST_AND_LUNCH = "ADD_BREAKFAST_AND_LUNCH";
    public static final String ADD_BREAKFAST_AND_DINNER = "ADD_BREAKFAST_AND_DINNER";
    public static final String GROUP_PRICE_TEXT = "GROUP_PRICE_TEXT";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT = "GROUP_PRICE_TEXT_ONE_NIGHT";
    public static final String GROUP_PRICE_TEXT_ONE_ROOM = "GROUP_PRICE_TEXT_ONE_ROOM";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM = "GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM";

    public static final String GROUP_PRICE_TEXT_WITH_TAX = "GROUP_PRICE_TEXT_WITH_TAX";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_WITH_TAX = "GROUP_PRICE_TEXT_ONE_NIGHT_WITH_TAX";
    public static final String GROUP_PRICE_TEXT_ONE_ROOM_WITH_TAX = "GROUP_PRICE_TEXT_ONE_ROOM_WITH_TAX";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_WITH_TAX = "GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_WITH_TAX";

    public static final String SAVING_PERC_TEXT = "SAVING_PERC_TEXT";
    public static final String PAYMENT_PLAN_PENALTY_TEXT = "PAYMENT_PLAN_PENALTY_TEXT";
    public static final String BEDS_SELLABLE_LABEL = "BEDS_SELLABLE_LABEL";
    public static final String ROOMS_SELLABLE_LABEL = "ROOMS_SELLABLE_LABEL";

    public static final String MYBIZ_QUICKPAY_TITLE = "MYBIZ_QUICKPAY_TITLE";

    public static final String MYBIZ_QUICKPAY_SUBTITLE = "MYBIZ_QUICKPAY_SUBTITLE";

    public static final String MYBIZ_QUICKPAY_TEXT = "MYBIZ_QUICKPAY_TEXT";

    public static final String MYBIZ_QUICKPAY_CTA_DESKTOP = "MYBIZ_QUICKPAY_CTA_DESKTOP";

    public static final String MYBIZ_QUICKPAY_CTA_APPS = "MYBIZ_QUICKPAY_CTA_APPS";

    public static final String STATIC_ROOM_TAG = "STATIC_ROOM_TAG";

    public static final String CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY = "CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY";

    public static final String CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY = "CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY";

    public static final String CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY = "CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY";

    public static final String CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY = "CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY";

    public static final String CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE = "CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE";

    public static final String CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE = "CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE";

    public static final String CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE = "CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE";

    public static final String CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE = "CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE";

    public static final String APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY = "APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY";

    public static final String APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY = "APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY";

    public static final String APP_CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY = "APP_CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY";

    public static final String APP_CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY = "APP_CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY";

    public static final String APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE = "APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE";

    public static final String APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE = "APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE";

    public static final String APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE = "APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE";

    public static final String APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE = "APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE";

    public static final String GENERIC_ERROR_TITLE = "GENERIC_ERROR_TITLE";

    public static final String GENERIC_ERROR_SUBTITLE = "GENERIC_ERROR_SUBTITLE";

    public static final String CORP_TXT_KEY_EXPIRED_CODE_TITLE = "CORP_TXT_KEY_EXPIRED_CODE_TITLE";

    public static final String CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE = "CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE";


    public static final String APP_CORP_TXT_KEY_EXPIRED_CODE_TITLE = "APP_CORP_TXT_KEY_EXPIRED_CODE_TITLE";

    public static final String APP_CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE = "APP_CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE";

    public static final String BOOK_NOW_PERSUASION_HOVER_TITLE = "BOOK_NOW_PERSUASION_HOVER_TITLE";
    public static final String BOOK_NOW_PERSUASION_HOVER_SUB_TITLE = "BOOK_NOW_PERSUASION_HOVER_SUB_TITLE";
    public static final String BOOK_NOW_PERSUASION_TITLE = "BOOK_NOW_PERSUASION_TITLE";
    public static final String BOOK_NOW_THANK_YOU_PAYMENT_HEADING = "BOOK_NOW_THANK_YOU_PAYMENT_HEADING";
    public static final String BOOK_NOW_THANK_YOU_HEADING = "BOOK_NOW_THANK_YOU_HEADING";
    public static final String BOOK_NOW_THANK_YOU_SUB_HEADING = "BOOK_NOW_THANK_YOU_SUB_HEADING";
    public static final String BOOK_NOW_THANK_YOU_CTA_TEXT = "BOOK_NOW_THANK_YOU_CTA_TEXT";
    public static final String ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT = "ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT";
    public static final String ROOM_DETAILS_BATHROOM_TEXT = "ROOM_DETAILS_BATHROOM_TEXT";
    public static final String ROOM_DETAILS_BATHROOMS_TEXT = "ROOM_DETAILS_BATHROOMS_TEXT";
    public static final String FILTER_CTA_TEXT = "FILTER_CTA_TEXT";
    public static final String FILTER_CTA_DEFAULT_TEXT = "FILTER_CTA_DEFAULT_TEXT";

    public static final String TAXES_EXCLUDED_TEXT_REVIEW_PAGE = "TAXES_EXCLUDED_TEXT_REVIEW_PAGE";

    public static final String LOYALTY_OFFER_TEXT = "LOYALTY_OFFER_TEXT";

    public static final String CASHBACK_OFFER_TEXT = "CASHBACK_OFFER_TEXT";

    public static final String CASHBACK_OFFER_TEXT_PWA = "CASHBACK_OFFER_TEXT_PWA";
    public static final String BEACHFRONT_CATEGORY_USP_DETAILS_TEXT = "BEACHFRONT_CATEGORY_USP_DETAILS_TEXT";
    public static final String FLYER_DESCRIPTION = "FLYER_DESCRIPTION";
    public static final String TCS_APPLICABLE_TEXT_IH = "TCS_TEXT_IH";
    public static final String TCS_APPLICABLE_CTA_IH = "TCS_CTA_IH";

    public static final String PAH_WITHOUT_CC_TEXT_GI = "PAH_WITHOUT_CC_TEXT_GI";

    public static final String PAH_WITH_CC_TEXT_GI = "PAH_WITH_CC_TEXT_GI";


    public static final String GI_NON_BNPL_COUPON_APPLIED_TEXT = "GI_NON_BNPL_COUPON_APPLIED_TEXT";

    public static final String GI_INSURANCE_APPLIED_TEXT = "GI_INSURANCE_APPLIED_TEXT";
    public static final String GI_GOCASH_APPLIED_TEXT = "GI_GOCASH_APPLIED_TEXT";
    public static final String GI_NON_BNPL_COUPON_GOCASH_APPLIED_TEXT = "GI_NON_BNPL_COUPON_GOCASH_APPLIED_TEXT";

    public static final String GI_NON_BNPL_COUPON_INSURANCE_APPLIED_TEXT = "GI_NON_BNPL_COUPON_INSURANCE_APPLIED_TEXT";
    public static final String GI_GOCASH_INSURANCE_APPLIED_TEXT = "GI_GOCASH_INSURANCE_APPLIED_TEXT";
    public static final String GI_NON_BNPL_COUPON_GOCASH_INSURANCE_APPLIED_TEXT = "GI_NON_BNPL_COUPON_GOCASH_INSURANCE_APPLIED_TEXT";

    public static final String GI_ACTIVE_BOOKINGS_THRESHOLD_TEXT = "GI_ACTIVE_BOOKINGS_THRESHOLD_TEXT";

    public static final String GI_BNPL_1_VARIANT_TEXT = "GI_BNPL_1_VARIANT_TEXT";
    public static final String GI_BNPL_0_VARIANT_TEXT = "GI_BNPL_0_VARIANT_TEXT";

    public static final String GI_BNPL_1_VARIANT_TEXT_IH = "GI_BNPL_1_VARIANT_TEXT_IH";
    public static final String GI_BNPL_0_VARIANT_TEXT_IH = "GI_BNPL_0_VARIANT_TEXT_IH";


    public static final String NON_BNPL_LOGGEDIN_PERSUASION = "NON_BNPL_LOGGED_IN_PERSUASION";
    public static final String BNPL_ZERO_LOGGEDIN_PERSUASION = "BNPL_ZERO_LOGGED_IN_PERSUASION";
    public static final String BNPL_ONE_LOGGEDIN_PERSUASION = "BNPL_ONE_LOGGED_IN_PERSUASION";

    // Soldout call out banner data.
    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_COLOUR = "SOLD_OUT_HANDLING_BANNER_TITLE_COLOUR";
    public static final String SOLD_OUT_HANDLING_BANNER_IMAGE_URL = "SOLD_OUT_HANDLING_BANNER_IMAGE_URL";

    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_ONLY_PRICE_INC = "SOLD_OUT_HANDLING_BANNER_TITLE_ONLY_PRICE_INC";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_ONLY_PRICE_INC = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_ONLY_PRICE_INC";
    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_ONLY_PRICE_INC = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_ONLY_PRICE_INC";

    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_ONLY_PRICE_DEC = "SOLD_OUT_HANDLING_BANNER_TITLE_ONLY_PRICE_DEC";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_ONLY_PRICE_DEC = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_ONLY_PRICE_DEC";
    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_ONLY_PRICE_DEC = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_ONLY_PRICE_DEC";

    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_RPCC = "SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_RPCC";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_WITH_RPCC = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_WITH_RPCC";
    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_WITH_RPCC = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_WITH_RPCC";
    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_WITH_RPCC = "SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_WITH_RPCC";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_WITH_RPCC = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_WITH_RPCC";

    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_WITH_RPCC = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_WITH_RPCC";
    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_RPCC_ROOMCODE = "SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_RPCC_ROOMCODE";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_WITH_RPCC_ROOMCODE = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_WITH_RPCC_ROOMCODE";
    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_WITH_RPCC_ROOMCODE = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_WITH_RPCC_ROOMCODE";
    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_WITH_RPCC_ROOMCODE = "SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_WITH_RPCC_ROOMCODE";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_WITH_RPCC_ROOMCODE = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_WITH_RPCC_ROOMCODE";
    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_WITH_RPCC_ROOMCODE = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_WITH_RPCC_ROOMCODE";
    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_ALL_OPTIONS = "SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_ALL_OPTIONS";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_ALL_OPTIONS = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_ALL_OPTIONS";
    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_ALL_OPTIONS = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_ALL_OPTIONS";
    public static final String SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_ALL_OPTIONS = "SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_ALL_OPTIONS";
    public static final String SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_ALL_OPTIONS = "SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_ALL_OPTIONS";
    public static final String SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_ALL_OPTIONS = "SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_ALL_OPTIONS";
    public static final String INLINE_PRICE_FILTER_TITLE = "INLINE_PRICE_FILTER_TITLE";

    public static final String  UPSELL_BREAKFAST_DETAILS_PAGE_TITLE = "UPSELL_BREAKFAST_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE =        "UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE =    "UPSELL_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE =    "UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE =   "UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_BREAKFAST_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE =   "UPSELL_BREAKFAST_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE =  "UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE =  "UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE =     "UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_DETAILS_PAGE_SUBTITLE =     "UPSELL_DETAILS_PAGE_SUBTITLE";
    public static final String  UPSELL_BREAKFAST_ADDED_DETAILS_PAGE_TITLE =   "UPSELL_BREAKFAST_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE =  "UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE  =  "UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE =  "UPSELL_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE  =  "UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_BREAKFAST_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE  =  "UPSELL_BREAKFAST_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE  =  "UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE  =  "UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE";
    public static final String  UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE =  "UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE";
    public static final String PER_NIGHT_TEXT = "PER_NIGHT_TEXT";
    public static final String ROOM_UPGRADED_TAG_TITLE = "ROOM_UPGRADED_TAG_TITLE";
    public static final String MEAL_UPGRADED_TAG_TITLE = "MEAL_UPGRADED_TAG_TITLE";
    public static final String ROOM_AND_MEAL_UPGRADED_TAG_TITLE = "ROOM_AND_MEAL_UPGRADED_TAG_TITLE";
    public static final String UPGRADED_TAG_TITLE_COLOR = "UPGRADED_TAG_TITLE_COLOR";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE = "UPGRADE_RATE_PLAN_POPUP_TITLE";
    public static final String ROOM_UPGRADE_POPUP_TITLE_GI = "ROOM_UPGRADE_POPUP_TITLE_GI";
    public static final String MEAL_UPGRADE_POPUP_TITLE_GI = "MEAL_UPGRADE_POPUP_TITLE_GI";
    public static final String ROOM_AND_MEAL_UPGRADE_POPUP_TITLE_GI = "ROOM_AND_MEAL_UPGRADE_POPUP_TITLE_GI";
    public static final String DEFAULT_UPGRADE_POPUP_TITLE_GI = "DEFAULT_UPGRADE_POPUP_TITLE_GI";
    public static final String UPGRADE_RATE_PLAN_POPUP_DISCLAIMER = "UPGRADE_RATE_PLAN_POPUP_DISCLAIMER";
    public static final String SELECTED_TITLE = "SELECTED_TITLE";
    public static final String UPGRADED_TITLE = "UPGRADED_TITLE";
    public static final String UPGRADED_ROOM_TITLE = "UPGRADED_ROOM_TITLE";
    public static final String UPGRADED_MEAL_TITLE = "UPGRADED_MEAL_TITLE";
    public static final String UPGRADED_ROOM_AND_MEAL_TITLE = "UPGRADED_ROOM_AND_MEAL_TITLE";
    public static final String GOTRIBE_BENEFITS_TITLE_DETAIL = "GOTRIBE_BENEFITS_TITLE_DETAIL";
    public static final String GOTRIBE_BENEFITS_TITLE_CTA_DETAIL = "GOTRIBE_BENEFITS_TITLE_CTA_DETAIL";
    public static final String GOTRIBE_BENEFITS_TITLE_REVIEW = "GOTRIBE_BENEFITS_TITLE_REVIEW";
    public static final String GOTRIBE_BENEFITS_SUB_TITLE_REVIEW = "GOTRIBE_BENEFITS_SUB_TITLE_REVIEW";
    public static final String GOTRIBE_BENEFITS_SUB_TITLE_REVIEW_POPUP = "GOTRIBE_BENEFITS_SUB_TITLE_REVIEW_POPUP";
    public static final String MEAL_UPGRADE_TEXT = "MEAL_UPGRADE_TEXT";
    public static final String UPGRADE_POSITIVE_CTA_TEXT = "UPGRADE_POSITIVE_CTA_TEXT";
    public static final String UPGRADED_NEGATIVE_CTA_TEXT = "UPGRADED_NEGATIVE_CTA_TEXT";
    public static final String GOTRIBE_OK_GOT_IT_CTA_TEXT = "GOTRIBE_OK_GOT_IT_CTA_TEXT";

    //Food & Dining Constants
    public static final String MEAL_FOR_KIDS = "MEAL_FOR_KIDS";   //Kid's meal can be prepared on request

    public static final String HOSTEL_TITLE = "HOSTEL_TITLE";

    public static final String HOSTEL_BEDS_AVAILABLE_TEXT = "HOSTEL_BEDS_AVAILABLE_TEXT";
    public static final String HOSTEL_ROOMS_AVAILABLE_TEXT = "HOSTEL_ROOMS_AVAILABLE_TEXT";
    public static final String PER_NIGHT_TITLE_TEXT = "PER_NIGHT_TITLE_TEXT";

    // package benefits constants
    public static final String PACKAGE_BENEFITS_TEXT_GI = "PACKAGE_BENEFITS_TEXT_GI";
    public static final String PACKAGE_BENEFITS_DEFAULT_TEXT_GI = "PACKAGE_BENEFITS_DEFAULT_TEXT_GI";
    public static final String PAY_ENTIRE_BNPL_APPLICABLE_TEXT = "PAY_ENTIRE_BNPL_APPLICABLE_TEXT";
    public static final String PAY_ENTIRE_BNPL_NOT_APPLICABLE_TEXT = "PAY_ENTIRE_BNPL_NOT_APPLICABLE_TEXT";
    public static final String TCS_AMOUNT_LABEL = "tcs_amount_label";

    // business identifcation constants
    public static final String BUSINESSIDENTIFICATION_CARD_TITLE_TEXT = "BUSINESSIDENTIFICATION_CARD_TITLE_TEXT";
    public static final String BUSINESSIDENTIFICATION_CARD_SUB_TEXT = "BUSINESSIDENTIFICATION_CARD_SUB_TEXT";
    public static final String BUSINESSIDENTIFICATION_CARD_SUB_TEXT_REVIEW_PAGE = "BUSINESSIDENTIFICATION_CARD_SUB_TEXT_REVIEW_PAGE";

    public static final String UPGRADE_RATE_PLAN_ORIGINAL_PRICE_GI = "UPGRADE_RATE_PLAN_ORIGINAL_PRICE_GI";

    // Loved by Indians constants
    public static final String INDIANESS_HOVER_TITLE = "INDIANESS_HOVER_TITLE";
    public static final String INDIANESS_HOVER_SUBTITLE = "INDIANESS_HOVER_SUBTITLE";
    public static final String INDIANNESS_URL = "https://promos.makemytrip.com/images/CDN_upload/LBI_GI.png";
    public static final String NEAR_ME_PILL_TITLE = "NEAR_ME_PILL_TITLE";

    // Guidelines constants
    public static final String REVIEW_TEXT_TITLE = "REVIEW_TEXT_TITLE";
    public static final String REVIEW_IMAGE_TITLE = "REVIEW_IMAGE_TITLE";
    public static final String BASE_TITLE = "BASE_TITLE";

    // OnBoardingData constants
    public static final String REVIEW_TITLE = "REVIEW_TITLE";
    public static final String REVIEW_SUBTITLE = "REVIEW_SUBTITLE";
    public static final String REVIEW_EDIT_TITLE = "REVIEW_EDIT_TITLE";
    public static final String REVIEW_EDIT_TEXT = "REVIEW_EDIT_TEXT";
    public static final String REVIEW_TEXT = "REVIEW_TEXT";
    public static final String REVIEW_DESKTOP_TEXT = "REVIEW_DESKTOP_TEXT";

    // Configs constants
    public static final String REVIEW_ESTIMATED_TIME = "REVIEW_ESTIMATED_TIME";
    public static final String REVIEW_EXIT_REVIEW_STRING = "REVIEW_EXIT_REVIEW_STRING";
    public static final String REVIEW_EDIT_EXIT_REVIEW_STRING = "REVIEW_EDIT_EXIT_REVIEW_STRING";
    public static final String REVIEW_EDIT_EXIT_QUESTION_CONFIG_STRING = "REVIEW_EDIT_EXIT_QUESTION_CONFIG_STRING";

    public static final String REVIEW_NONINCENTIVE_TITLE = "REVIEW_NONINCENTIVE_TITLE";
    public static final String REVIEW_NONINCENTIVE_TEXT = "REVIEW_NONINCENTIVE_TEXT";
    public static final String REVIEW_DESKTOP_NONINCENTIVE_TEXT = "REVIEW_DESKTOP_NONINCENTIVE_TEXT";
    public static final String REVIEW_EXIT_REVIEW_NONINCENTIVE_STRING = "REVIEW_EXIT_REVIEW_NONINCENTIVE_STRING";
    public static final String REVIEW_DISCAMOUNT_TITLE = "REVIEW_DISCAMOUNT_TITLE";
    public static final String REVIEW_DISCAMOUNT_TEXT = "REVIEW_DISCAMOUNT_TEXT";
    public static final String REVIEW_DESKTOP_DISCAMOUNT_TEXT = "REVIEW_DESKTOP_DISCAMOUNT_TEXT";
    public static final String REVIEW_EXIT_REVIEW_DISCAMOUNT_STRING = "REVIEW_EXIT_REVIEW_DISCAMOUNT_STRING";

    public static final String IN_PROGRESS_REVIEW = "IN_PROGRESS";
    public static final String REVIEW_COMPLETE = "REVIEW_COMPLETE";
    public static final String SEGMENT_ID = "SEGMENT_ID";
    public static final String LOCUS_TYPE = "LOCUS_TYPE";
    public static final String TEXT_ERROR = "Please add review to proceed";
    public static final String RATING_ERROR = "Please add rating to proceed";
    public static final String TERRIBLE_RATING = "Terrible";
    public static final String POOR_RATING = "Poor";
    public static final String AVERAGE_RATING = "Average";
    public static final String GOOD_RATING = "Good";
    public static final String EXCELLENT_RATING = "Excellent";
    public static final String LEVEL_1_COMPLETE_URL = "https://promos.makemytrip.com/gi/UGC/reward.png";
    public static final String LEVEL_2_COMPLETE_URL = "https://promos.makemytrip.com/gi/UGC/thumbs_up.png";
    public static final String LEVEL_2_SUBHEADER = "Earn discount of ₹%d more by answering a few more quesitons";
    public static final String LEVEL_HEADER_ICON = "https://promos.makemytrip.com/gi/UGC/star_badge.png";

    public static final String OCCASION_PACKAGE_BENEFITS_TEXT = "OCCASION_PACKAGE_BENEFITS_TEXT";

    // ugc question error key polyglot constants
    public static final String BOOKING_NOT_FOUND_MSG = "BOOKING_NOT_FOUND_MSG";
    public static final String BOOKING_NOT_FOUND_TITLE = "BOOKING_NOT_FOUND_TITLE";
    public static final String PROGRAM_LOADING_ISSUE_MSG = "PROGRAM_LOADING_ISSUE_MSG";
    public static final String PROGRAM_LOADING_ISSUE_TITLE = "PROGRAM_LOADING_ISSUE_TITLE";
    public static final String NO_DATA_FOUND_FOR_UUID = "NO_DATA_FOUND_FOR_UUID_MSG";
    public static final String NO_DATA_FOUND_FOR_UUID_TITLE = "NO_DATA_FOUND_FOR_UUID_TITLE";

    public static final String QUESTION_GENERIC_ERROR_MSG = "QUESTION_GENERIC_ERROR_MSG";

    public static final String QUESTION_GENERIC_ERROR_TITLE = "QUESTION_GENERIC_ERROR_TITLE";

    // cta text
    public static final String CTA_TEXT = "CTA_TEXT";
    public static final String CTA_DEEPLINK_URL = "CTA_DEEPLINK_URL";
    public static final String GI_LINKED_RATE_PLAN_BOTTOMSHEET_TITLE = "GI_LINKED_RATE_PLAN_BOTTOMSHEET_TITLE";
    public static final String GI_LINKED_RATE_PLAN_DISCOUNT_TEXT = "GI_LINKED_RATE_PLAN_DISCOUNT_TEXT";
    public static final String GI_LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT = "GI_LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT";
    public static final String GI_LINKED_RATE_PLAN_TITLE = "GI_LINKED_RATE_PLAN_TITLE";
    public static final String GI_LINKED_RATE_PLAN_DESCRIPTION = "GI_LINKED_RATE_PLAN_DESCRIPTION";
    public static final String GI_LINKED_RATE_PLAN_CTATEXT = "GI_LINKED_RATE_PLAN_CTATEXT";
    public static final String GI_LINKED_RATE_PLAN_BOTTOM_SHEET_FOOTER_TEXT = "GI_LINKED_RATE_PLAN_BOTTOM_SHEET_FOOTER_TEXT" ;

    // Select Room Revamp constant translations.
    public static final String GO_TRIBE_INCLUSION_TEXT = "GO_TRIBE_INCLUSION_TEXT";
    public static final String LONG_STAY_INCLUSION_TEXT = "LOS_INCLUSION_TEXT";
    public static final String BOOK_AT_ZERO_HTML_TEXT_GI = "BOOK_AT_ZERO_HTML_TEXT_GI";
    public static final String PAY_AT_HOTEL_HTML_TEXT_GI = "PAY_AT_HOTEL_HTML_TEXT_GI";
    public static final String NON_REFUNDABLE_SHORT_HTML_TEXT = "NON_REFUNDABLE_SHORT_HTML_TEXT";
    public static final String FREE_CANCELLATION_SHORT_HTML_TEXT = "FREE_CANCELLATION_SHORT_HTML_TEXT";

    public static final String HOTEL_UPSELL_MEAL_RACKRATE = "HOTEL_UPSELL_MEAL_RACKRATE";
    public static final String HOTEL_UPSELL_MEAL_CANCELLATION_TEXT = "HOTEL_UPSELL_MEAL_CANCELLATION_TEXT";
    public static final String HOTEL_UPSELL_CARD_TITLE = "HOTEL_UPSELL_CARD_TITLE";
    public static final String HOTEL_UPSELL_CARD_SUBTITLE = "HOTEL_UPSELL_CARD_SUBTITLE";
    public static final String MEAL_SHEET_TITLE = "MEAL_SHEET_TITLE";
    public static final String MEAL_SHEET_PRICE_LABEL = "MEAL_SHEET_PRICE_LABEL";
    public static final String HOTEL_UPSELL_MEAL_RATING = "HOTEL_UPSELL_MEAL_RATING";
    public static final String HOTEL_UPSELL_MEAL_RESTAURANT = "HOTEL_UPSELL_MEAL_RESTAURANT";

    public static final String MEAL_TITLE_AI = "MEAL_TITLE_AI";
    public static final String MEAL_TITLE_JP = "MEAL_TITLE_JP";
    public static final String MEAL_TITLE_AP = "MEAL_TITLE_AP";
    public static final String MEAL_TITLE_LD = "MEAL_TITLE_LD";
    public static final String MEAL_TITLE_SMAP = "MEAL_TITLE_SMAP";
    public static final String MEAL_TITLE_MAP = "MEAL_TITLE_MAP";
    public static final String MEAL_TITLE_TMAP = "MEAL_TITLE_TMAP";

    public static final String MEAL_TITLE_NON_REFUNDABLE = "MEAL_TITLE_NON_REFUNDABLE";
    public static final String MEAL_TITLE_EP = "MEAL_TITLE_EP";
    public static final String MEAL_TITLE_DN = "MEAL_TITLE_DN";
    public static final String MEAL_TITLE_LN = "MEAL_TITLE_LN";
    public static final String MEAL_TITLE_CP = "MEAL_TITLE_CP";
    public static final String MEAL_TITLE_LCP = "MEAL_TITLE_LCP";
    public static final String MEAL_TITLE_CB = "MEAL_TITLE_CB";
    public static final String HOTEL_UPSELL_MEAL_SHEET_TITLE = "HOTEL_UPSELL_MEAL_SHEET_TITLE";
    public static final String INCOGNITO_TOOLTIP_TEXT = "INCOGNITO_TOOLTIP_TEXT";
    public static final String INCOGNITO_DESCRIPTION_TEXT = "INCOGNITO_DESCRIPTION_TEXT";
    public static final String FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT = "FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT";
    public static final String TAX_AND_SERVICE_FEE = "TAX_AND_SERVICE_FEE";

    //LOCATIN CARD CONSTANTS
    public static final String LOCATION_RATING_SUBTITLE = "LOCATION_RATING_SUBTITLE";
    public static final String SORT_CRITERIA_ACCESS_POINT_TITLE = "SORT_CRITERIA_ACCESS_POINT_TITLE";
}

