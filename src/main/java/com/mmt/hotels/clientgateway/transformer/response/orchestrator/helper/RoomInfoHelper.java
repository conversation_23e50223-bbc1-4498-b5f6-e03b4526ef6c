package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfoExtension;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Tag;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.RoomHighlightType;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.SleepingArrangementRoomInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.rooms.StayDetail;
import com.mmt.hotels.clientgateway.response.rooms.StayTypeInfo;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.flyfish.TagData;
import com.mmt.hotels.model.response.pricing.ExtraGuestDetail;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.SleepingArrangement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOSTEL_BEDS_AVAILABLE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOSTEL_ROOMS_AVAILABLE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SPACE_OCCUPANCY_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT;
import static com.mmt.hotels.date.DateUtil.LOGGER;

@Component
public class RoomInfoHelper {

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private Utility utility;

    private Map<String, StayTypeInfo> actionInfoMap = new HashMap<>();

    /**
     * Extract sleeping arrangements from OrchV2 RoomInfo
     */
    public static List<SleepingArrangement> getSleepingArrangements(RoomInfo roomInfo) {
        List<SleepingArrangement> beds = null;
        if (roomInfo != null && MapUtils.isNotEmpty(roomInfo.getRoomArrangementMap()) && CollectionUtils.isNotEmpty(roomInfo.getRoomArrangementMap().get("BEDS"))) {
            beds = new ArrayList<>();
            for (ArrangementInfo arrangementInfo : roomInfo.getRoomArrangementMap().get("BEDS")) {
                SleepingArrangement bed = new SleepingArrangement();
                bed.setType(arrangementInfo.getType());
                bed.setCount(arrangementInfo.getCount());
                beds.add(bed);
            }
        }
        return beds;
    }

    /**
     * Transform OrchV2 RoomInfo to room highlights - Complete implementation adapted from legacy getRoomHighlights
     */
    public List<RoomHighlight> transformRoomHighlights(RoomInfo orchRoomInfo, ExtraGuestDetail extraGuestDetail,
                                                       boolean altAccoHotel, boolean pilgrimageBedInfoEnable) {
        List<RoomHighlight> roomHighlights = new ArrayList<>();

        if (orchRoomInfo == null) {
            return roomHighlights;
        }

        // Process bed and bathroom info from OrchV2 spaces data
        // Note: OrchV2 doesn't have external vendor bedroom info, so we skip that logic
        // and directly process bathroom and bed info from spaces

        if(StringUtils.isNotBlank(orchRoomInfo.getRoomSize())) {
            RoomHighlight roomHighlight = new RoomHighlight();

            roomHighlight.setIconUrl(IMAGE_URL_ROOM_SIZE);
            roomHighlight.setText(buildRoomSizeText(orchRoomInfo));
            roomHighlight.setDescription(roomHighlight.getText());
            roomHighlight.setIdentifier(RoomHighlightType.ROOM_SIZE.name());
            roomHighlight.setSelectRoomRevampOrder(3);
            roomHighlights.add(roomHighlight);
        }
        if(StringUtils.isNotBlank(orchRoomInfo.getRoomViewName())) {
            RoomHighlight roomHighlight = new RoomHighlight();
            roomHighlight.setIconUrl("https://gos3.ibcdn.com/roomViewIcon-1678093525.png");
            roomHighlight.setText(orchRoomInfo.getRoomViewName());
            roomHighlight.setDescription(orchRoomInfo.getRoomViewName());
            roomHighlight.setIdentifier(RoomHighlightType.ROOM_VIEW.name());
            roomHighlight.setSelectRoomRevampOrder(4);
            roomHighlights.add(roomHighlight);
        }
        // Bed info from OrchV2 spaces data
        addBedHighlightsFromOrchV2(orchRoomInfo, roomHighlights, extraGuestDetail,
                altAccoHotel, pilgrimageBedInfoEnable);

        // Bathroom info from OrchV2 spaces data
        addBathroomHighlightsFromOrchV2(orchRoomInfo, roomHighlights);

        if (orchRoomInfo.getMaxGuestCount() > 0) {
            RoomHighlight roomHighlight = new RoomHighlight();
            roomHighlight.setIconUrl("https://gos3.ibcdn.com/paxBlackIcon-1678093500.png");
            if (orchRoomInfo.getMaxGuestCount() == 1) {
                String countText = String.format("Max %s Guest",orchRoomInfo.getMaxGuestCount());
                roomHighlight.setText(countText);
                roomHighlight.setDescription(countText);
            }else {
                String countText = String.format("Max %s Guests",orchRoomInfo.getMaxGuestCount());
                roomHighlight.setText(countText);
                roomHighlight.setDescription(countText);
            }
            roomHighlight.setIdentifier(RoomHighlightType.GUEST_COUNT.name());
            roomHighlights.add(roomHighlight);
        }


        return roomHighlights;

    }

    private String buildRoomSizeText(RoomInfo roomInfo) {
        String roomSizeText = null;
        if (roomInfo != null && StringUtils.isNotBlank(roomInfo.getRoomSize()) && StringUtils.isNotBlank(roomInfo.getRoomSizeUnit())) {
            roomSizeText = roomInfo.getRoomSize() + SPACE + roomInfo.getRoomSizeUnit();
            if (SQUARE_FEET_V2.equalsIgnoreCase(roomInfo.getRoomSizeUnit())) {
                try {
                    double roomSize = Double.parseDouble(roomInfo.getRoomSize());
                    roomSize = roomSize * SQUARE_FEET_TO_SQUARE_METER_CONVERSION_FACTOR;
                    long roundedRoomSize = Math.round(roomSize); // Round off to the nearest integer
                    String roomSizeInMeterSquare = String.valueOf(roundedRoomSize);
                    roomSizeText = roomSizeText + SPACE + AMENITIES_OPEN_BRACE + roomSizeInMeterSquare + SPACE + SQUARE_METER + AMENITIES_CLOSING_BRACE;
                } catch (Exception e) {
                    LOGGER.error("Error while parsing room size text : {}", e.getMessage());
                }
            }
        }
        return roomSizeText;
    }

    /**
     * Add bathroom highlights from OrchV2 roomArrangementMap (equivalent to legacy roomInfo.getBathrooms())
     */
    private void addBathroomHighlightsFromOrchV2(RoomInfo orchRoomInfo, List<RoomHighlight> roomHighlights) {
        // Extract bathroom info from OrchV2 roomArrangementMap using "BATHROOM" key
        if (orchRoomInfo.getRoomArrangementMap() != null &&
                CollectionUtils.isNotEmpty(orchRoomInfo.getRoomArrangementMap().get("BATHROOM"))) {

            List<ArrangementInfo> bathrooms = orchRoomInfo.getRoomArrangementMap().get("BATHROOM");
            ArrangementInfo bathroomArrangement = bathrooms.get(0); // Follow legacy pattern - get first bathroom

            RoomHighlight roomHighlight = new RoomHighlight();
            roomHighlight.setIconUrl(IMAGE_URL_BATHROOM_TYPE);
            String bedTypeText;
            if (bathroomArrangement.getCount() > 1) {
                bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOMS_TEXT);
            } else {
                bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOM_TEXT);
            }
            roomHighlight.setText(bedTypeText);
            roomHighlight.setDescription(bedTypeText);
            roomHighlight.setSelectRoomRevampOrder(6);
            roomHighlight.setIdentifier(RoomHighlightType.BATHROOM_TYPE.name());
            roomHighlights.add(roomHighlight);
        }
    }

    /**
     * Add bed highlights from OrchV2 roomArrangementMap (equivalent to legacy roomInfo.getBeds())
     */
    private void addBedHighlightsFromOrchV2(RoomInfo orchRoomInfo, List<RoomHighlight> roomHighlights,
                                            ExtraGuestDetail extraGuestDetail, boolean altAccoHotel,
                                            boolean pilgrimageBedInfoEnable) {
        // Extract bed info from OrchV2 roomArrangementMap using "BEDS" and "ALTERNATE_BEDS" keys
        if (orchRoomInfo.getRoomArrangementMap() != null) {
            List<ArrangementInfo> beds = orchRoomInfo.getRoomArrangementMap().get("BEDS");
            List<ArrangementInfo> alternateBeds = orchRoomInfo.getRoomArrangementMap().get("ALTERNATE_BEDS");

            if (pilgrimageBedInfoEnable) {
                // Pilgrimage bed info logic - with alternate beds (following legacy pattern exactly)
                if (CollectionUtils.isNotEmpty(beds) || CollectionUtils.isNotEmpty(alternateBeds)) {
                    RoomHighlight roomHighlight = new RoomHighlight();
                    roomHighlight.setIconUrl(IMAGE_URL_ROOM_TYPE);
                    List<String> bedTypeList = new ArrayList<>();
                    List<String> alternateBedTypeList = new ArrayList<>();

                    if (CollectionUtils.isNotEmpty(beds)) {
                        beds.forEach(bedType -> {
                            String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
                            bedTypeList.add(bedTypeText);
                        });
                    }
                    if (CollectionUtils.isNotEmpty(alternateBeds)) {
                        alternateBeds.forEach(bedType -> {
                            String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
                            alternateBedTypeList.add(bedTypeText);
                        });
                    }
                    String bedTypeListString = "";
                    if (!bedTypeList.isEmpty()) {
                        bedTypeListString = String.join(COMMA_SPACE, bedTypeList);
                        if (!alternateBedTypeList.isEmpty()) {
                            bedTypeListString = bedTypeListString + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT) + SPACE + String.join(COMMA_SPACE, alternateBedTypeList);
                        }
                    } else {
                        bedTypeListString = String.join(COMMA_SPACE, alternateBedTypeList);
                    }

                    roomHighlight.setText(bedTypeListString);
                    roomHighlight.setDescription(bedTypeListString);
                    if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
                        roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
                    }
                    roomHighlight.setIdentifier(RoomHighlightType.BED_TYPE.name());
                    roomHighlight.setSelectRoomRevampOrder(5);
                    roomHighlights.add(roomHighlight);
                }
            } else {
                // Standard bed info logic (following legacy pattern exactly)
                if (CollectionUtils.isNotEmpty(beds)) {
                    RoomHighlight roomHighlight = new RoomHighlight();
                    roomHighlight.setIconUrl("https://gos3.ibcdn.com/bedBlackIcon-1678093474.png");
                    List<String> bedTypeList = new ArrayList<>();
                    beds.forEach(bedType -> {
                        String bedTypeText;
                        if (bedType.getCount() > 1) {
                            bedTypeText = bedType.getCount() + SPACE_X_SPACE + bedType.getType();
                        } else {
                            bedTypeText = bedType.getType();
                        }
                        bedTypeList.add(bedTypeText);
                    });
                    roomHighlight.setText(String.join(COMMA_SPACE, bedTypeList));
                    roomHighlight.setDescription(String.join(COMMA_SPACE, bedTypeList));
                    if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
                        roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
                    }
                    roomHighlight.setIdentifier(RoomHighlightType.BED_TYPE.name());
                    roomHighlight.setSelectRoomRevampOrder(5);
                    roomHighlights.add(roomHighlight);
                }
            }
        }
    }

    /**
     * Build stay details from OrchV2 RoomInfo spaces data
     */
    public void buildStayDetails(SearchRoomsResponse searchRoomsResponse,
                                 SleepingArrangementRoomInfo roomInfo, int sellableCombo, String propertyType,
                                 HotelDetails hotelDetails, boolean modifyStayDetailsForIH) {
        StayDetail stayDetail = null;
        int bedCount = 0;
        int bedRoomCount = 0;
        int maxGuest = 0;
        int extraBeds = 0;
        int baseOccupancy = 0;
        int maxCapacity = 0;
        int bathroomCount = 0;
        LinkedHashMap<String, Integer> bedInfoMap = new LinkedHashMap<>();

        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            RoomInfo orchRoomInfo = hotelDetails.getRooms().get(0).getRoomInfo();
            if (orchRoomInfo != null) {
                // Extract data from OrchV2 RoomInfo using spaces (similar to legacy privateSpaces logic)
                if (CollectionUtils.isNotEmpty(orchRoomInfo.getSpaces())) {
                    // Process spaces similar to legacy logic: iterate through SpaceData and then through individual spaces
                    int bathRoomMultiplier = 0;
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData spaceData : orchRoomInfo.getSpaces()) {
                        if (CollectionUtils.isNotEmpty(spaceData.getSpaces())) {
                            for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space : spaceData.getSpaces()) {
                                if (space.getSleepingDetails() != null) {
                                    // Count bedrooms/living rooms for bathroom multiplier (similar to legacy)
                                    if ((Constants.BEDROOM.equalsIgnoreCase(space.getSpaceType()) ||
                                            Constants.LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) && bathRoomMultiplier == 0) {
                                        bathRoomMultiplier = 1; // OrchV2 doesn't have multiple sleeping arrangements per space
                                    }

                                    // Extract sleeping details from each space
                                    bedCount += space.getSleepingDetails().getBedCount();
                                    bedRoomCount += space.getSleepingDetails().getBedRoomCount();
                                    baseOccupancy += space.getSleepingDetails().getMinOccupancy();
                                    maxGuest += space.getSleepingDetails().getMinOccupancy();
                                    maxCapacity += space.getSleepingDetails().getMaxOccupancy();
//                                    extraBeds += space.getSleepingDetails().getExtraBedCount();

                                    // Extract bed info from sleeping details (similar to legacy bedInfos)
                                    if (CollectionUtils.isNotEmpty(space.getSleepingDetails().getBedInfo())) {
                                        for (ArrangementInfo bedInfo : space.getSleepingDetails().getBedInfo()) {
                                            if (StringUtils.isNotBlank(bedInfo.getType()) && bedInfo.getCount() > 0) {
                                                bedInfoMap.put(bedInfo.getType(), bedInfoMap.getOrDefault(bedInfo.getType(), 0) + bedInfo.getCount());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Calculate bathroom count using multiplier (similar to legacy logic)
                    if (orchRoomInfo.getRoomInfoExtension() != null) {
                        bathroomCount = orchRoomInfo.getRoomInfoExtension().getBathroomCount() * bathRoomMultiplier;
                    }
                    stayDetail = new StayDetail();
                    stayDetail.setBedRoom(bedRoomCount);
                    stayDetail.setBed(bedCount);
                    stayDetail.setMaxGuests(maxGuest);
                    stayDetail.setBaseGuests(baseOccupancy);
                    stayDetail.setExtraBeds(extraBeds);
                    stayDetail.setBathroom(bathroomCount);
                    stayDetail.setMaxCapacity(maxCapacity);
                    stayDetail.setBedInfoMap(bedInfoMap);
                    stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
                }
                else
                    stayDetail = searchRoomsResponse.getExactRooms().get(0).getStayDetail();
            }

        } else if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
            List<Rooms> rooms = hotelDetails.getRoomCombos().get(0).getRooms();
            boolean zeroBedRoomCount = false;

            if (CollectionUtils.isNotEmpty(rooms)) {
                // Aggregate data from all rooms in the combo using OrchV2 RoomInfo
                for (Rooms room : rooms) {
                    RoomInfo orchRoomInfo = room.getRoomInfo();
                    if (orchRoomInfo != null) {
                        // Process spaces for each room in combo (similar to legacy logic)
                        if (CollectionUtils.isNotEmpty(orchRoomInfo.getSpaces())) {
                            int bathRoomMultiplier = 0;
                            for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData spaceData : orchRoomInfo.getSpaces()) {
                                if (CollectionUtils.isNotEmpty(spaceData.getSpaces())) {
                                    for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space : spaceData.getSpaces()) {
                                        if (space.getSleepingDetails() != null) {
                                            // Count bedrooms/living rooms for bathroom multiplier
                                            if ((Constants.BEDROOM.equalsIgnoreCase(space.getSpaceType()) ||
                                                    Constants.LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) && bathRoomMultiplier == 0) {
                                                bathRoomMultiplier = 1;
                                            }

                                            // Aggregate sleeping details from each space
                                            bedCount += space.getSleepingDetails().getBedCount();
                                            bedRoomCount += space.getSleepingDetails().getBedRoomCount();
                                            baseOccupancy += space.getSleepingDetails().getMinOccupancy();
                                            maxGuest += space.getSleepingDetails().getMinOccupancy();
                                            maxCapacity += space.getSleepingDetails().getMaxOccupancy();
//                                            extraBeds += space.getSleepingDetails().getExtraBedCount();

                                            // Aggregate bed info from sleeping details
                                            if (CollectionUtils.isNotEmpty(space.getSleepingDetails().getBedInfo())) {
                                                for (ArrangementInfo bedInfo : space.getSleepingDetails().getBedInfo()) {
                                                    if (StringUtils.isNotBlank(bedInfo.getType()) && bedInfo.getCount() > 0) {
                                                        bedInfoMap.put(bedInfo.getType(), bedInfoMap.getOrDefault(bedInfo.getType(), 0) + bedInfo.getCount());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // Add bathroom count for this room
                            if (orchRoomInfo.getRoomInfoExtension() != null) {
                                bathroomCount += orchRoomInfo.getRoomInfoExtension().getBathroomCount() * bathRoomMultiplier;
                            }
                        } else if (!modifyStayDetailsForIH && CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
                            // It's an Un-Migrated Property So read always from roomInfo Node;

                            List<RoomDetails> roomDetailsList = searchRoomsResponse.getRecommendedCombos().get(0).getRooms();
                            for (RoomDetails roomDetails : roomDetailsList) {
                                StayDetail roomStayDetail = roomDetails.getStayDetail();
                                if (roomStayDetail != null) {
                                    if (roomStayDetail.getBed() != null) {
                                        bedCount += roomStayDetail.getBed();
                                    }
                                    if (roomStayDetail.getBedRoom() != null && roomStayDetail.getBedRoom() > 0) {
                                        bedRoomCount += roomStayDetail.getBedRoom();
                                    }
                                    if (roomStayDetail.getMaxGuests() != null) {
                                        maxGuest += roomStayDetail.getMaxGuests();
                                    }
                                    if (roomStayDetail.getExtraBeds() != null) {
                                        extraBeds += roomStayDetail.getExtraBeds();
                                    }
                                    if (roomStayDetail.getBaseGuests() != null) {
                                        baseOccupancy += roomStayDetail.getBaseGuests();
                                    }
                                    if (roomStayDetail.getBathroom() != null) {
                                        bathroomCount += roomStayDetail.getBathroom();
                                    }
                                    if (roomStayDetail.getMaxCapacity() != null) {
                                        maxCapacity += roomStayDetail.getMaxCapacity();
                                    }
                                }

                            }
                        }
                    }
                }

                maxCapacity = maxGuest;

                stayDetail = new StayDetail();
                if (bedRoomCount > 0) {
                    stayDetail.setBedRoom(bedRoomCount);
                }
                stayDetail.setBed(bedCount);
                stayDetail.setMaxGuests(maxGuest);
                stayDetail.setBaseGuests(baseOccupancy);
                stayDetail.setExtraBeds(extraBeds);
                stayDetail.setMaxCapacity(maxCapacity);
                stayDetail.setBathroom(bathroomCount);
                stayDetail.setBedInfoMap(bedInfoMap);
                stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
            }
        }
        if (stayDetail!=null){
            stayDetail.setRoomCount(hotelDetails.getRoomCount());
        }
        // Apply property type specific logic
        if (stayDetail != null && StringUtils.isNotBlank(propertyType) &&
                (propertyType.equalsIgnoreCase(Constants.PROPERTY_TYPE_HOSTEL) || propertyType.equalsIgnoreCase(Constants.PROPERTY_TYPE_HOMESTAY)) &&
                (sellableCombo == 1 || sellableCombo == 3)) {
            stayDetail.setBedRoom(0);
        }

        // Set stay type info based on hotel details
        if (stayDetail != null) {
            StayTypeInfo stayTypeInfo = buildStayTypeInfo(hotelDetails.getSellableUnit(), propertyType);
            stayDetail.setStayTypeInfo(stayTypeInfo);
        }

        roomInfo.setStayDetail(stayDetail);
        roomInfo.setBedInfoText((stayDetail != null ? stayDetail.getBedInfoText() : ""));
    }

    /**
     * Transform OrchV2 SpaceData to CG SpaceData (V1)
     */
    public SpaceData getSpaceData(com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData orchSpaceData,
                                  CommonModifierResponse commonModifierResponse) {
        if (orchSpaceData == null) {
            return null;
        }

        SpaceData cgSpaceData = new SpaceData();
        int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;

        cgSpaceData.setDescriptive(orchSpaceData.getDescriptive());
        cgSpaceData.setSharedInfo(buildSharedInfo(orchSpaceData.getDisplayItem()));

        List<Space> spaceList = new ArrayList<>();

        // Build CG spaces
        if (CollectionUtils.isNotEmpty(orchSpaceData.getSpaces())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space orchSpace : orchSpaceData.getSpaces()) {
                Space cgSpace = new Space();
                cgSpace.setAreaText(orchSpace.getAreaText());
                cgSpace.setDescriptionText(orchSpace.getDescriptionText());
                cgSpace.setName(orchSpace.getName());
                String subText = orchSpace.getSubText();
                cgSpace.setSpaceId(orchSpace.getSpaceId());
                cgSpace.setSpaceType(orchSpace.getSpaceType());

                if (orchSpace.getSpaceType() != null &&
                        (orchSpace.getSpaceType().equalsIgnoreCase(BEDROOM) ||
                                orchSpace.getSpaceType().equalsIgnoreCase(LIVING_ROOM))) {

                    int finalOccupancy = orchSpace.getFinalOccupancy();
                    int occupancy = Math.max(finalOccupancy, orchSpace.getBaseOccupancy());
                    if (occupancy > 0) {
                        subText = (occupancy > 1) ?
                                polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) :
                                polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
                    } else {
                        subText = null;
                    }
                    if (subText != null) {
                        subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
                    }
                    totalBaseOccupancy += orchSpace.getBaseOccupancy();
                    totalMaxOccupancy += orchSpace.getMaxOccupancy();
                    extraBedCount += Math.max(0, finalOccupancy - orchSpace.getBaseOccupancy());
                }

                cgSpace.setSubText(subText);
                cgSpace.setOpenCardText(orchSpace.getOpenCardText());

                // Convert media from RoomEntity to MediaData
                if (CollectionUtils.isNotEmpty(orchSpace.getMedia())) {
                    List<MediaData> mediaDataList = new ArrayList<>();
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity roomEntity : orchSpace.getMedia()) {
                        MediaData cgMediaData = new MediaData();
                        cgMediaData.setMediaType(roomEntity.getMediaType());
                        cgMediaData.setUrl(roomEntity.getUrl());
                        mediaDataList.add(cgMediaData);
                    }
                    cgSpace.setMedia(mediaDataList);
                }
                spaceList.add(cgSpace);
            }
        }
        cgSpaceData.setSpaces(spaceList);

        cgSpaceData.setBaseGuests(totalBaseOccupancy);
        cgSpaceData.setExtraBeds(extraBedCount);
        cgSpaceData.setMaxGuests(totalMaxOccupancy);
        return cgSpaceData;
    }

    /**
     * Build SharedInfo from OrchV2 DisplayItem
     */
    private SharedInfo buildSharedInfo(com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem displayItem) {
        if (displayItem == null) {
            return null;
        }
        SharedInfo sharedInfo = new SharedInfo();
        sharedInfo.setIconUrl(displayItem.getIconUrl());
        sharedInfo.setInfoText(displayItem.getText());
        return sharedInfo;
    }


    /**
     * Build complete room info object from OrchV2 data
     */
    public SleepingArrangementRoomInfo buildRoomInfo(HotelDetails hotelDetails, SearchRoomsResponse searchRoomsResponse,
                                                     List<RoomStayCandidate> roomStayCandidates,
                                                     String countryCode, boolean isOHSExpEnable, Pair<Boolean, Boolean> bedAndRoomPresent, boolean isNewDetailPageTrue,
                                                     Map<String, String> expDataMap) {
        if (hotelDetails == null) {
            return null;
        }

        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        if (isOHSExpEnable) {
            roomInfo.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.HOSTEL_TITLE));
        } else {
            if (StringUtils.isNotBlank(hotelDetails.getStayTypeText())) {
                roomInfo.setTitle(hotelDetails.getStayTypeText());
            }
        }

        buildStayDetails(searchRoomsResponse, roomInfo, hotelDetails.getAdditionalDetails().getSellableCombo(), hotelDetails.getPropertyType(), hotelDetails, false);

        // Set basic room info properties
        if (roomInfo.getStayDetail() == null) {
            roomInfo.setStayDetail(new StayDetail());
        }

        // Set guest room key-value based on OrchV2 data
        updateGuestRoomDetails(hotelDetails, countryCode, expDataMap, roomInfo);

        // Set bedInfoText from stay detail
        roomInfo.setBedInfoText((roomInfo.getStayDetail() != null ? roomInfo.getStayDetail().getBedInfoText() : ""));

        String propertyInfoText = roomInfo.getTitle();
        try {
            // TODO : this hack is temporary, would be removed once we start getting property size from hotstore with space details
            roomInfo.setPropertyInfoText("");
            if(StringUtils.isNotBlank(propertyInfoText) && roomInfo.getStayDetail() != null && roomInfo.getStayDetail().getMaxGuests() != null){
                String[] splittedPropertyInfoText = propertyInfoText.split("\\(");
                if(splittedPropertyInfoText.length > 1) {
                    propertyInfoText = "(" + splittedPropertyInfoText[1];
                    propertyInfoText = propertyInfoText.replace(")", " | " + Constants.FITS + " " + roomInfo.getStayDetail().getMaxGuests() + ")");
                    propertyInfoText = propertyInfoText.split("\\)")[0] + ")";
                } else {
                    propertyInfoText = "";
                }
                roomInfo.setPropertyInfoText(propertyInfoText);
            }
        } catch (Exception e) {
            LOGGER.error("propertyInfoText could not be added due to : {} ", e.getMessage());
        }

        // Get additional data needed for room info
        String freeChildText = getFreeChildTextFromHotelDetails(hotelDetails);

        // Build sleep info text based on stay details
        if (roomInfo.getStayDetail() != null) {
            buildSleepInfoText(roomInfo.getStayDetail(), isOHSExpEnable, hotelDetails.getAdditionalDetails().getSellableCombo(),
                    bedAndRoomPresent, freeChildText, isNewDetailPageTrue);
        }

        return roomInfo;
    }

    /**
     * Update guest room details from OrchV2 data
     */
    public void updateGuestRoomDetails(HotelDetails hotelDetails, String countryCode, Map<String, String> expDataMap, SleepingArrangementRoomInfo roomInfo) {
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            Rooms firstRoom = hotelDetails.getRooms().get(0);
            if (firstRoom != null && CollectionUtils.isNotEmpty(firstRoom.getRatePlans())) {
                RatePlan firstRatePlan = firstRoom.getRatePlans().get(0);
                if (firstRatePlan != null && firstRatePlan.getAvailDetail() != null && firstRatePlan.getAvailDetail().getOccupancyDetails() != null) {
                    OccupancyDetails occupancyDetails = firstRatePlan.getAvailDetail().getOccupancyDetails();
                    Map<String, Integer> roomBedCount = new HashMap<>();
                    roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, occupancyDetails != null ? occupancyDetails.getNumberOfRooms() : 1);
                    // Note: bedCount is not available in OrchV2 occupancy details, using default value
                    roomBedCount.put(Constants.SELLABLE_BED_TYPE, occupancyDetails != null ? occupancyDetails.getBedCount() : 0);

                    Tuple<String, String> guestRoomKeyValue = utility.getGuestRoomKeyValue(roomBedCount, hotelDetails.getPropertyType(), hotelDetails.getListingType()
                    );
                    roomInfo.setGuestRoomKey(guestRoomKeyValue.getX());
                    roomInfo.setGuestRoomValue(guestRoomKeyValue.getY());
                }
            }
        }
    }

    /**
     * Get free child text from hotel details (simplified for OrchV2)
     */
    public String getFreeChildTextFromHotelDetails(HotelDetails hotelDetails) {
        if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
            for (RoomCombo roomCombo : hotelDetails.getRoomCombos()) {
                if (StringUtils.isNotEmpty(roomCombo.getFreeChildText())) {
                    return roomCombo.getFreeChildText();
                }
            }
        }
        return getFreeChildText(hotelDetails.getRooms());
    }

    private String getFreeChildText(List<Rooms> rooms) {
        return Optional.ofNullable(rooms)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(roomList -> roomList.stream()
                        .filter(room -> CollectionUtils.isNotEmpty(room.getRatePlans()))
                        .flatMap(room -> room.getRatePlans().stream())
                        .map(RatePlan::getFreeChildText)
                        .filter(StringUtils::isNotEmpty)
                        .findFirst())
                .orElse(null);
    }

    /**
     * Build sleep info text from stay details
     */
    private void buildSleepInfoText(StayDetail stayDetail, boolean isOHSExpEnable, int sellableCombo, Pair<Boolean, Boolean> bedAndRoomPresent, String freeChildText,boolean isNewDetailPageTrue) {
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        if (StringUtils.isNotBlank(client) && stayDetail != null && stayDetail.getMaxGuests() != null && stayDetail.getMaxGuests() > 0) {
            String polyglotSleepsText = stayDetail.getMaxGuests() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
            if (StringUtils.isNotBlank(polyglotSleepsText)) {
                String sleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(stayDetail.getMaxGuests()));
                if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) || isNewDetailPageTrue) {
                    stayDetail.setSleepInfoText(sleepsText);
                } else {
                    stayDetail.setSleepInfoText(OPEN_BOLD_TAG + sleepsText + CLOSE_BOLD_TAG);
                }

                if ((stayDetail.getMaxCapacity() != null && stayDetail.getMaxCapacity() > stayDetail.getMaxGuests()) || StringUtils.isNotEmpty(freeChildText)) {
                    int extraGuests = (stayDetail.getMaxCapacity()==null)?0:stayDetail.getMaxCapacity()  - stayDetail.getMaxGuests();
                    String polyglotExtraGuestsText = extraGuests == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_EXTRA_GUESTS);
                    if (StringUtils.isNotBlank(polyglotSleepsText)) {
                        String extraGuestsText = StringUtils.isNotEmpty(freeChildText) ? freeChildText : polyglotExtraGuestsText.replace(EXTRA_PARAMETER, String.valueOf(extraGuests));
                        if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                            stayDetail.setSleepInfoText(sleepsText);
                            stayDetail.setAdditionalSleepInfoText(extraGuestsText);
                        } else {
                            if(isNewDetailPageTrue && (Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client))) {
                                stayDetail.setSleepInfoText(sleepsText + COMMA + SPACE + extraGuestsText);
                            } else{
                                stayDetail.setSleepInfoText(OPEN_BOLD_TAG + sleepsText + CLOSE_BOLD_TAG + SPACE + BULLET_HTML + SPACE + extraGuestsText);
                            }
                        }
                    }
                }
                if(isOHSExpEnable){
                    if(sellableCombo==1){
                        // sellableType bed available in lowestRoomTypeCode, So Suggest private rooms
                        stayDetail.setBedInfoText(null);
                        if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                            stayDetail.setSleepInfoText(null);
                            stayDetail.setAdditionalSleepInfoText(null);
                            if(bedAndRoomPresent.getValue()){
                                stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
                            }
                        }else{
                            if(bedAndRoomPresent.getValue()){
                                stayDetail.setSleepInfoText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
                            }else{
                                stayDetail.setSleepInfoText(null);
                            }
                        }
                    }else if(sellableCombo==2){
                        // sellableType bedRoom available in lowestRoomTypeCode so suggest shared dorm
                        if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))){
                            if(bedAndRoomPresent.getKey()){
                                stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(HOSTEL_BEDS_AVAILABLE_TEXT));
                            }
                        }else{
                            stayDetail.setBedInfoText(sleepsText);
                            if(bedAndRoomPresent.getKey()){
                                stayDetail.setSleepInfoText(polyglotService.getTranslatedData(HOSTEL_BEDS_AVAILABLE_TEXT));
                            }else{
                                stayDetail.setSleepInfoText(null);
                            }
                        }
                    }else{
                        stayDetail.setSleepInfoText(null);
                        stayDetail.setAdditionalSleepInfoText(null);
                        stayDetail.setBedInfoText(null);
                    }
                }
            }
        }
    }

    /**
     * Build stay type info based on sellable unit and property type
     */
    private StayTypeInfo buildStayTypeInfo(String propertySellableUnit, String propertyType) {
        if (MapUtils.isEmpty(actionInfoMap) || StringUtils.isEmpty(propertySellableUnit)) {
            return null;
        }
        if (SELLABLE_UNIT_ENTIRE.equalsIgnoreCase(propertySellableUnit)) {
            return actionInfoMap.get(SELLABLE_UNIT_ENTIRE_PMS_KEY);
        } else if (SELLABLE_UNIT_ROOM.equalsIgnoreCase(propertySellableUnit) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType)) {
            return actionInfoMap.get(SELLABLE_UNIT_ROOM_PMS_KEY);
        }
        return null;
    }

    /**
     * Initialize actionInfoMap from configuration
     */
    public void initializeActionInfoMap(Map<String, StayTypeInfo> configActionInfoMap) {
        if (configActionInfoMap != null) {
            this.actionInfoMap = configActionInfoMap;
        }
    }

    public RoomSummary buildRoomSummary(RoomInfoExtension roomInfoExtension) {
        RoomSummary roomSummary = null;
        if (roomInfoExtension != null && roomInfoExtension.getRoomSummary() != null) {
            roomSummary = new RoomSummary();
            roomSummary.setTopRated(roomInfoExtension.getRoomSummary().isTopRated());
            roomSummary.setRatingCount(roomInfoExtension.getRoomSummary().getRatingCount());
            roomSummary.setReviewCount(roomInfoExtension.getRoomSummary().getReviewCount());
            roomSummary.setDisableLowRating(roomInfoExtension.getRoomSummary().isDisableLowRating());
            roomSummary.setTagData(buildTagData(roomInfoExtension.getRoomSummary().getTagData()));
        }
        return roomSummary;
    }

    private List<TagData> buildTagData(List<Tag> tagData) {
        List<TagData> tagDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tagData)) {
            tagData.forEach(tag -> tagDataList.add(convertTag(tag)));
        }
        return tagDataList;
    }

    private TagData convertTag(Tag tag) {
        TagData data = new TagData();
        data.setName(tag.getName());
        data.setId(tag.getId());
        data.setTagType(tag.getTagType());
        data.setSentiment(tag.getSentiment());
        return data;
    }

}
