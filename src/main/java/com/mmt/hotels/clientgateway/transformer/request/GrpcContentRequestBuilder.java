package com.mmt.hotels.clientgateway.transformer.request;

import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Request;
import com.gommt.hotels.content.proto.mediaid.MediaIdRequest;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class GrpcContentRequestBuilder {
    public TaggedMediaRequest buildTaggedMediaRequest(String correlationKey, String hotelId, String tenant, boolean streetViewReq) {
        TaggedMediaRequest request  = TaggedMediaRequest.newBuilder()
                .setCorrelationKey(correlationKey)
                .setHotelId(hotelId)
                .setTenant(tenant)
                .setStreetViewEnabled(streetViewReq)
                .build();
        return request;
    }

    public MediaIdRequest buildMediaIdDetailsRequest(String correlationKey, String hotelId, String tagIds, String tenant, String offset, String limit) {
        MediaIdRequest request = MediaIdRequest.newBuilder()
                .setCorrelationKey(correlationKey)
                .setHotelId(hotelId)
                .setTag(tagIds)
                .setId(tagIds)
                .setTenant(tenant)
                .setOffset(StringUtils.isNotEmpty(offset) ? Integer.parseInt(offset) : 0)
                .setLimit(StringUtils.isNotEmpty(limit) ? Integer.parseInt(limit) : 0)
                .build();
        return request;
    }

    public HotelsDataV2Request getHotelIdMappingRequest(String hotelId, String correlationKey) {
        HotelsDataV2Request.Builder hotelIdMappingRequest;
        if (StringUtils.isEmpty(hotelId)) {
            return null;
        }
        hotelIdMappingRequest = HotelsDataV2Request.newBuilder();
        hotelIdMappingRequest.addVoyIds(hotelId);
        hotelIdMappingRequest.setTenant("GI");
        hotelIdMappingRequest.setCorrelationKey(StringUtils.isNotEmpty(correlationKey) ? correlationKey : "");
        return hotelIdMappingRequest.build();
    }
}
