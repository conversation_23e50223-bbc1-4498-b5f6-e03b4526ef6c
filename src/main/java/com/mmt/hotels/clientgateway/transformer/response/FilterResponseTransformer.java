package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import java.lang.reflect.Type;

import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.response.filter.*;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.response.staticdata.TimeSlot;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.google.gson.reflect.TypeToken;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.ICV2;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FILTER_CTA_DEFAULT_TEXT;

@Component
public class FilterResponseTransformer {

    private static final Logger logger = LoggerFactory.getLogger(FilterResponseTransformer.class);

    @Autowired
    FilterHelper filterHelper;

    @Autowired
    PropertyManager propertyManager;
    @Autowired
    ObjectMapperUtil objectMapperUtil;
    private Map<String, List<Integer>> defaultPriceHistConfig;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    private Map<String, List<Integer>> defaultPriceHistConfigCorp;
    private Map<Integer, Set<String>> budgetHotelCityConfig;
    private Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfig;
    private Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfigPolyGlot;
    private FilterPricingOption pricingOptionConfigGCC;

    @Value("${filter.view.modification.config}")
    private String filterViewTypeModificationConfig;

    @Value("${filter.categories.remove.config}")
    private String filterCategoriesRemoveConfig;

    private Map<String, Map<String,String>> filterViewTypeModificationMap;

    private Map<String, List<String>> filterCategoriesRemoveMap;

    @Value("${filter.min.items.limit}")
    private int filterMinItemsLimit;

    @Value("${filter.min.items.show}")
    private int filterMinItemsToShow;

    @Value("${flexible.checkin.tag.url}")
    private String flexibleCheckinTagUrl;

    @Value("#{'${amenities.altacco.icon}'.split(',')}")
    private List<String> altAccoAmenities;

    @Value("${gi.rush.deal.ap.from.checkin}")
    private int rushDealApGi;

    @Value("${gi.rush.deal.ap.time.threshold}")
    private int rushDealTimeInHrGi;

    @PostConstruct
    public void init() {
        FilterConfig filterConfig = propertyManager.getProperty("cgGIFilterConfig", FilterConfig.class);
        try {
            defaultPriceHistConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogram(),
                    new TypeReference<Map<String, List<Integer>>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            defaultPriceHistConfigCorp = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogramCorp(),
                    new TypeReference<Map<String, List<Integer>>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            amenitiesCategoryConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfig(),
                    new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            amenitiesCategoryConfigPolyGlot = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfigPolyGlot(),
                    new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                    }, DependencyLayer.CLIENTGATEWAY);

            pricingOptionConfigGCC = filterConfig.pricingOption();
            filterViewTypeModificationMap = objectMapperUtil.getObjectFromJsonWithType(filterViewTypeModificationConfig, new TypeReference<Map<String, Map<String, String>>>() {
            }, DependencyLayer.CLIENTGATEWAY);

            filterCategoriesRemoveMap = objectMapperUtil.getObjectFromJsonWithType(filterCategoriesRemoveConfig, new TypeReference<Map<String, List<String>>>() {
            }, DependencyLayer.CLIENTGATEWAY);

        } catch (Exception ex) {
            logger.error("Error in fetching filter config ,:{}", ex.getMessage(), ex);
        }

        filterConfig.addPropertyChangeListener("defaultPriceHistogram", event -> {
            try {
                defaultPriceHistConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogram(),
                        new TypeReference<Map<String, List<Integer>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
            } catch (Exception ex) {
                logger.error("Error in updating filter config ,:{}", ex.getMessage(), ex);
            }
        });

        filterConfig.addPropertyChangeListener("defaultPriceHistogramCorp", event -> {
            try {
                defaultPriceHistConfigCorp = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogramCorp(),
                        new TypeReference<Map<String, List<Integer>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
            } catch (Exception ex) {
                logger.error("Error in updating filter config corp,:{}", ex.getMessage(), ex);
            }
        });

        CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
        budgetHotelCityConfig = commonConfig.budgetHotelCityConfig();
        commonConfig.addPropertyChangeListener("budgetHotelCityConfig", evt -> budgetHotelCityConfig = commonConfig.budgetHotelCityConfig());

        filterConfig.addPropertyChangeListener("amenitiesCategoryConfig", event -> {
            try {
                amenitiesCategoryConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfig(),
                        new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
            } catch (Exception ex) {
                logger.error("Error in updating amenities category config config corp,:{}", ex.getMessage(), ex);
            }
        });

        filterConfig.addPropertyChangeListener("amenitiesCategoryConfigPolyGlot", event -> {
            try {
                amenitiesCategoryConfigPolyGlot = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfigPolyGlot(),
                        new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
            } catch (Exception ex) {
                logger.error("Error in updating amenities category config config corp,:{}", ex.getMessage(), ex);
            }
        });

    }

    public FilterResponse convertFilterResponse(FilterSearchMetaDataResponse filterResponseHES, FilterConfiguration filterConfig, FilterCountRequest filterRequest, LinkedHashMap<String,String>expDataMap, CommonModifierResponse commonModifierResponse, FilterPillConfigurationWrapper filterPillConfigurationWrapper) {
        if (filterConfig == null || MapUtils.isEmpty(filterConfig.getFilters())) {
            return null;
        }

        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setTotalCount(filterResponseHES.getTotalHotelsCount());
        filterResponse.setFilteredCount(filterResponseHES.getTotalHotelsCountAfterFilterApplication());
        filterResponse.setPropertyExists(filterResponseHES.getTotalHotelsCountAfterFilterApplication() > 0);
        filterResponse.setMinPrice(filterResponseHES.getMinPrice());
        filterResponse.setLimited(filterResponseHES.isLimitedResponse());
        filterResponse.setFilterList(addFilterCategories(filterResponseHES, filterConfig, filterRequest,expDataMap, commonModifierResponse));
        filterResponse.setMatchmakerFilterList(buildMatchmakerSuggestedFilterList(filterResponseHES.getMatchmakerFilterList()));
        filterResponse.setContextDetails(buildContextDetails(filterResponseHES.getContextDetails()));
        filterResponse.setPricingOption(buildPricingOption(filterRequest, pricingOptionConfigGCC));
        filterResponse.setBannerIconUrl(filterConfig.getHomestayBannerIconUrl());

        /** will be configured as per experiment **/
        boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        if (MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(FILTER_PILL_EXP)) && !isMyPartnerRequest && filterPillConfigurationWrapper != null) {
            if (MapUtils.isNotEmpty(filterCategoriesRemoveMap) && filterCategoriesRemoveMap.containsKey(filterRequest.getClient())) {
                removeFilterCategoriesFromResponse(filterCategoriesRemoveMap.get(filterRequest.getClient().toUpperCase()), filterResponse);
            }
            filterResponse.setFilterPills(buildFilterPills(filterResponse, filterPillConfigurationWrapper.getFilterPillConfig(), filterResponseHES, commonModifierResponse.getExpDataMap(), filterRequest));
            translateSortPillData(filterPillConfigurationWrapper.getSortList());
            filterResponse.setSortList(filterPillConfigurationWrapper.getSortList());
            //FOR GI -> Filter Count is not needed as UI Component doesn't need Filter Count
            //For IOS Code -> They are reading filtered count, so need to set 0 for Ios Specifically.
            filterResponse.setCtaText(
                    filterRequest.getRequestDetails()!=null && Constants.FUNNEL_SOURCE_HOSTEL.equalsIgnoreCase(filterRequest.getRequestDetails().getFunnelSource())
                            ? polyglotService.getTranslatedData(FILTER_CTA_DEFAULT_TEXT) :buildFilterCtaText(filterResponseHES.getTotalHotelsCountAfterFilterApplication(),filterRequest)
            );
            if (MapUtils.isNotEmpty(filterViewTypeModificationMap) && filterViewTypeModificationMap.containsKey(filterRequest.getClient())) {
                modifyFilterCategoryViewType(filterViewTypeModificationMap.get(filterRequest.getClient().toUpperCase()), filterResponse);

            }
            filterResponse.setInlinePriceFitler(getPriceRangeFilter(filterResponse.getFilterList(), filterRequest));
            modifyShowImageUrlMergePropertyType(filterResponse.getFilterList());
        }
        filterResponse.setPreAppliedFilters(filterRequest.getFilterCriteria());
        if(commonModifierResponse!= null) {
            filterResponse.setExpData(commonModifierResponse.getExpDataMap());
            filterResponse.setVariantKey(commonModifierResponse.getVariantKey());
        }
        return filterResponse;
    }

    /**
     * this method modifies the showImageurl boolean to false for filter context experiment MERGE_PROPERTY_TYPE
     **/
    private void modifyShowImageUrlMergePropertyType(List<FilterCategory> filterList) {
        if (CollectionUtils.isNotEmpty(filterList)) {
            for (FilterCategory filterCategory : filterList) {
                if (filterCategory != null && MERGE_PROPERTY_TYPE.equalsIgnoreCase(filterCategory.getCategoryName())) {
                    filterCategory.setShowImageUrl(false);
                    break;
                }
            }
        }
    }

    /**
     * This method removes certain filter categories from filter response as per config
     **/
    private void removeFilterCategoriesFromResponse(List<String> filterCategoriesToRemove, FilterResponse filterResponse) {
        if (filterResponse != null && CollectionUtils.isNotEmpty(filterCategoriesToRemove) && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            List<FilterCategory> categories = new ArrayList<>();
            for (FilterCategory filterCategory : filterResponse.getFilterList()) {
                if (filterCategoriesToRemove.contains(filterCategory.getCategoryName())) {
                    categories.add(filterCategory);
                }
            }
            filterResponse.getFilterList().removeAll(categories);
        }
    }

    /**
     * This method modifies the view-type of filter category as per config, config is map with category name and corresponding target view type
     **/
    private void modifyFilterCategoryViewType(Map<String, String> viewTypeModificationMap, FilterResponse filterResponse) {

        if (filterResponse != null && MapUtils.isNotEmpty(viewTypeModificationMap) && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            for (FilterCategory filterCategory : filterResponse.getFilterList()) {
                if (StringUtils.isNotEmpty(filterCategory.getCategoryName()) && viewTypeModificationMap.containsKey(filterCategory.getCategoryName())) {
                    filterCategory.setViewType(viewTypeModificationMap.get(filterCategory.getCategoryName()));
                }
            }
        }

    }


    /**
     * this method returns a string having count of properties after filter application
     * eg: VIEW 45 PROPERTIES
     **/
    private String buildFilterCtaText(int totalHotelsCountAfterFilterApplication, FilterCountRequest filterRequest) {

        String ctaText = polyglotService.getTranslatedData(ConstantsTranslation.FILTER_CTA_TEXT);

        if (StringUtils.isNotEmpty(ctaText)) {
            if (filterRequest != null && (CollectionUtils.isNotEmpty(filterRequest.getFilterCriteria()) || (filterRequest.getMatchMakerDetails() != null && (CollectionUtils.isNotEmpty(filterRequest.getMatchMakerDetails().getSelectedTags()) || CollectionUtils.isNotEmpty(filterRequest.getMatchMakerDetails().getLatLng())))))
            {
                ctaText = (totalHotelsCountAfterFilterApplication >= 0) ? ctaText.replace("{filtered_hotel_count}", String.valueOf(totalHotelsCountAfterFilterApplication)) : ctaText.replace("{filtered_hotel_count}", EMPTY_STRING);

            } else {
                ctaText = ctaText.replace("{filtered_hotel_count}", EMPTY_STRING);
            }
        }

        return ctaText;

    }

    /** this method calls polyglot to translate te keys present in the SORT filter pill **/
    private void translateSortPillData(SortList sortList) {
        if (sortList != null) {
            if (StringUtils.isNotBlank(sortList.getTitle())) {
                sortList.setTitle(polyglotService.getTranslatedData(sortList.getTitle()));
            }
            if (CollectionUtils.isNotEmpty(sortList.getSortCriteria())) {
                for (SortCriteria sortCriteria : sortList.getSortCriteria()) {

                    if (!sortCriteria.isAccessPoint()) {
                        sortCriteria.setTitle(polyglotService.getTranslatedData(sortCriteria.getTitle()));
                        sortCriteria.setPillText(polyglotService.getTranslatedData(sortCriteria.getPillText()));
                    }

                }
            }
        }
    }

    /** this method uses filterList in filterResponse to build filter pills **/
    private List<FilterPill> buildFilterPills(FilterResponse filterResponse, FilterPillConfig filterPillConfig,FilterSearchMetaDataResponse filterResponseHES, LinkedHashMap<String,String>expDataMap, FilterCountRequest filterRequest) {

        List<FilterPill> filterPillList = null;

        /* List containing all the filter categories present in filterList. Eg: PRICE, POPULAR, STAR_CATEGORY */
        List<String> filterCategories = getCategoryNamesFromFilterResponse(filterResponse);

        if (filterPillConfig != null) {
            Map<String, Integer> pillSequence = filterPillConfig.getPillSequence();
            FilterPill[] filterPills = null;
            if (MapUtils.isNotEmpty(pillSequence)) {
                filterPills = new FilterPill[pillSequence.size()];
                for (String pill : pillSequence.keySet()) {
                    if (StringUtils.isNotBlank(pill) && pillSequence.containsKey(pill) && pillSequence.get(pill) != null) {
                        int index = pillSequence.get(pill);
                        if (index > 0) {
                            filterPills[index - 1] = buildPillCategories(filterCategories, pill, filterPillConfig, filterResponseHES, expDataMap, filterRequest);
                        }
                    }
                }
            }
            /* this is done to ensure filter pill list becomes modifiable and remove all method works */
            filterPillList = new ArrayList<>(Arrays.asList(filterPills));

            /* to ensure no pill out of config is added */
            filterPillList.removeAll(Collections.singleton(null));
        }

        return filterPillList;
    }

    private FilterCategory getPriceRangeFilter(List<FilterCategory> filtersList, FilterCountRequest filterRequest) {
        if(filtersList == null || filtersList.size() == 0 || isPriceFilterApplied(filterRequest))
            return null;
        for(FilterCategory filterCategory : filtersList) {
            if(filterCategory.getCategoryName().equalsIgnoreCase(FILTER_HOTEL_PRICE_BUCKET)) {
                FilterCategory priceIntent = new FilterCategory();
                priceIntent.setFilters(filterCategory.getFilters());
                priceIntent.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INLINE_PRICE_FILTER_TITLE));
                priceIntent.setViewType(filterCategory.getViewType());
                priceIntent.setShowMore(filterCategory.isShowMore());
                priceIntent.setMinItemsToShow(filterCategory.getMinItemsToShow());
                priceIntent.setVisible(filterCategory.isVisible());
                priceIntent.setCollapsed(filterCategory.isCollapsed());
                priceIntent.setShowCustomRange(filterCategory.isShowCustomRange());
                priceIntent.setCategoryName(filterCategory.getCategoryName());
                priceIntent.setSearchable(filterCategory.isSearchable());
                priceIntent.setShowImageUrl(filterCategory.isShowImageUrl());
                priceIntent.setSingleSelection(filterCategory.isSingleSelection());
                return priceIntent;
            }
        }
        return null;
    }

    private boolean isPriceFilterApplied(FilterCountRequest filterRequest) {
        // Note : When price range filter is applied then inlinePriceFilters won't be sent from BE.
        if(filterRequest != null && filterRequest.getFilterCriteria() != null) {
            for(com.mmt.hotels.clientgateway.request.Filter f : filterRequest.getFilterCriteria()) {
                if(f.getFilterGroup().toString().equalsIgnoreCase(FILTER_PRICE_BUCKET_HES)) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * method to get all category names from the filterList in filterResponse
     * Eg. PRICE, PRICE_BUCKET, POPULAR, STAR_CATEGORY
     **/
    private List<String> getCategoryNamesFromFilterResponse(FilterResponse filterResponse) {
        List<String> filterCategories = new ArrayList<>();

        if (filterResponse != null && filterResponse.getFilterList() != null) {
            for (FilterCategory filterCategory : filterResponse.getFilterList()) {
                if (CollectionUtils.isNotEmpty(filterCategory.getFilters())) {
                    filterCategories.add(filterCategory.getCategoryName());
                }
            }
        }
        return filterCategories;

    }

    /** this method adds filter categories to every pill basis whether those categories are present in filter list **/
    private FilterPill buildPillCategories(List<String> filterCategories, String pill, FilterPillConfig filterPillConfig, FilterSearchMetaDataResponse filterResponseHES, LinkedHashMap<String,String>expDataMap, FilterCountRequest filterRequest) {

        FilterPill filterPill = null;
        // GI brand.
        if (filterResponseHES != null && MapUtils.isNotEmpty(filterResponseHES.getFilterDataMap()))
        {
            for(String category : filterPillConfig.getFilterPills().get(pill).getCategories()) { // only one category will be present in the GI config.
                Optional<com.mmt.hotels.filter.Filter> f;

                try {
                    if (filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(category)) == null)
                        continue;
                } catch (Exception e) {
                    continue;
                }

                String bookAtZeroExp = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.BOOK_AT_ZERO):EMPTY_STRING);
                if (PAY_LATER.equalsIgnoreCase(category) && !isBnplApplicable(expDataMap, filterRequest.getDeviceDetails().getBookingDevice(), filterRequest.getSearchCriteria().getCheckIn())) {
                    continue;
                }
                if (FLEXIBLE_CHECKIN.equalsIgnoreCase(category) && !isFlexibleCheckinApplicable(expDataMap, filterRequest.getSearchCriteria().getCheckIn(), filterRequest.getSearchCriteria().getCheckOut())) {
                    continue;
                }
                if (HOTEL_CATEGORY_FILTER.equalsIgnoreCase(category) && COUPLE_FRIENDLY.equalsIgnoreCase(pill) && !isCoupleFriendlyFilterApplicable(filterRequest.getSearchCriteria())) {
                    continue;
                }

                if (FilterGroup.HOTELS_USP.name().equals(category) && !isLovedByIndiansApplicable(expDataMap)) {
                    continue;
                }
                if (RUSH_DEALS.equalsIgnoreCase(category) && !isRushDealsFilterApplicable(expDataMap,filterRequest.getSearchCriteria().getCheckIn())) {
                    continue;
                }

                if(BLACKDEALS.equalsIgnoreCase(category)) {
                    f = filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(category)).stream().filter(a -> String.format(CURRENT_USERTIER, filterResponseHES.getGIUserTierNumber()).equalsIgnoreCase(a.getFilterValue())).findFirst();
                } else {
                    f = filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(category)).stream().filter(a -> pill.equalsIgnoreCase(a.getFilterValue())).findFirst();
                }
                if(!f.isPresent())
                    continue;
                Filter filter = createFilter(null, f.get(), "B2C", "", null, filterRequest);
                if(filter.getFilterExists() == null || !filter.getFilterExists())
                    continue;
                filter.setTitle(filterPillConfig.getFilterPills().get(pill).getTitle());
                filterPill = new FilterPill();
                filterPill.setId(filterPillConfig.getFilterPills().get(pill).getId());
                if(pill.equalsIgnoreCase(PAY_LATER)){
                    if ("false".equalsIgnoreCase(bookAtZeroExp)) {
                        filter.setTitle(BookAtOne);
                    } else {
                        filter.setTitle(BookAtZero);
                    }
                    filterPill.setTitle(filter.getTitle());
                } else {
                    filterPill.setTitle(filterPillConfig.getFilterPills().get(pill).getTitle());
                }
                if (FLEXIBLE_CHECKIN.equalsIgnoreCase(category)
                        && MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(FLEXI_NEW) && TRUE.equalsIgnoreCase(expDataMap.get(FLEXI_NEW))) {
                    filter.setRightIconUrl(flexibleCheckinTagUrl);
                }
                filter.setIconList(filterPillConfig.getFilterPills().get(pill).getIconList());
                filter.setTooltip(filterPillConfig.getFilterPills().get(pill).getTooltip());
                filterPill.setType(filterPillConfig.getFilterPills().get(pill).getType());
                filterPill.setPillFilter(filter);
            }
        }
        return filterPill;

    }


    public FilterResponse convertBatchFilterResponse(FilterSearchMetaDataResponse filterResponseHES) {
        if (filterResponseHES == null) {
            return null;
        }
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.BATCH_COLLECTIONS_TITLE));
        filterResponse.setTotalCount(filterResponseHES.getTotalHotelsCount());
        filterResponse.setBatchFiltersList(buildBatchFilterList(filterResponseHES.getBatchFilters()));
        filterResponse.setFilteredCount(filterResponseHES.getTotalHotelsCountAfterFilterApplication());
        filterResponse.setPropertyExists(filterResponseHES.getTotalHotelsCountAfterFilterApplication() > 0);
        filterResponse.setContextDetails(buildContextDetails(filterResponseHES.getContextDetails()));
        return filterResponse;
    }

    public FilterPricingOption buildPricingOption(FilterCountRequest filterRequest, FilterPricingOption pricingOption){

        if(pricingOption == null)
            return null;

        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        String checkIn = filterRequest.getSearchCriteria().getCheckIn();
        String checkOut = filterRequest.getSearchCriteria().getCheckOut();
        int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn),LocalDate.parse(checkOut));

        if(Constants.AE.equalsIgnoreCase(region) && los>1) {

            FilterPricingOption pricingOptionNew = new FilterPricingOption();
            pricingOptionNew.setHeader(polyglotService.getTranslatedData(pricingOption.getHeader()));
            List<PricingOptionFilter> filterListNew = new ArrayList<PricingOptionFilter>();
            List<PricingOptionFilter> filterList = pricingOption.getFilters();

            for(PricingOptionFilter filter : filterList) {
                PricingOptionFilter newFilter = new PricingOptionFilter();
                newFilter.setTitle(polyglotService.getTranslatedData(filter.getTitle()));
                newFilter.setExpValue(filter.getExpValue());

                filterListNew.add(newFilter);
            }
            pricingOptionNew.setFilters(filterListNew);
            return pricingOptionNew;
        }

        return null;
    }
    private ContextDetails buildContextDetails(com.mmt.hotels.filter.ContextDetails contextDetails) {
        if(contextDetails == null){
            return null;
        }
        ContextDetails contextDetailsCG = new ContextDetails();
        contextDetailsCG.setContext(contextDetails.getContext());
        contextDetailsCG.setAltAccoIntent(contextDetails.isAltAccoIntent());
        return contextDetailsCG;
    }

    private List<Filter> buildBatchFilterList(List<com.mmt.hotels.filter.Filter> batchFilters) {
        if(CollectionUtils.isEmpty(batchFilters)){
            return null;
        }
        List<Filter> batchFiltersCG = new ArrayList<>();
        batchFilters.forEach(batchFilter -> batchFiltersCG.add(buildBatchFilter(batchFilter)));
        return batchFiltersCG;
    }

    private Filter buildBatchFilter(com.mmt.hotels.filter.Filter batchFilter) {
        Filter batchCG = new Filter();
        batchCG.setFilterValue(batchFilter.getFilterValue());
        batchCG.setCount(batchFilter.getCount());
        batchCG.setIconUrl(batchFilter.getIconUrl());
        batchCG.setTitle(batchFilter.getTitle());
        batchCG.setSubTitle(batchFilter.getSubTitle());
        batchCG.setStaticBatch(batchFilter.getStaticBatch());
        return batchCG;
    }

    private List<Filter> buildMatchmakerSuggestedFilterList(List<com.mmt.hotels.filter.Filter> matchmakerFilterList) {
        if(CollectionUtils.isEmpty(matchmakerFilterList)){
            return null;
        }
        List<Filter> matchmakerFilters = new ArrayList<>();
        matchmakerFilterList.forEach(matchmakerFilterHes -> matchmakerFilters.add(commonResponseTransformer.buildFilterCG(matchmakerFilterHes)));
        return matchmakerFilters;
    }

    private List<FilterCategory> addFilterCategories(FilterSearchMetaDataResponse filterResponseHES,FilterConfiguration filterConfig,FilterCountRequest filterRequest,LinkedHashMap<String,String>expDataMap, CommonModifierResponse commonModifierResponse){
        int maxFilterSize = filterConfig !=null && MapUtils.isNotEmpty(filterConfig.getRankOrder()) ? filterConfig.getRankOrder().values().stream().max(Integer::compareTo).get() : -1;
        List<FilterCategory> filterCategoryList = new ArrayList<>();
        List<FilterCategory> nonRankedFilterCategoryList = new ArrayList<>();
        FilterCategory[] fCategoryRankedArr = null;
        if(maxFilterSize > 0)
            fCategoryRankedArr = new FilterCategory[maxFilterSize];

        String funnelSource = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getFunnelSource())) ? filterRequest.getRequestDetails().getFunnelSource().toUpperCase() : "HOTELS";
        String idContext = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getIdContext())) ? filterRequest.getRequestDetails().getIdContext().toUpperCase() : "B2C";
        String siteDomain = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getSiteDomain())) ? filterRequest.getRequestDetails().getSiteDomain() : "IN";
        String locationId= (filterRequest.getSearchCriteria()!=null && StringUtils.isNotBlank(filterRequest.getSearchCriteria().getLocationId())) ? filterRequest.getSearchCriteria().getLocationId() : "";
        String locationType= (filterRequest.getSearchCriteria()!=null && StringUtils.isNotBlank(filterRequest.getSearchCriteria().getLocationType())) ? filterRequest.getSearchCriteria().getLocationType() : "";

        for(Map.Entry<String, FilterConfigCategory> entry: filterConfig.getFilters().entrySet()){
            FilterConfigCategory fConfigCat = entry.getValue();
            if(PROPERTY_TYPE.equalsIgnoreCase(entry.getKey()) && MapUtils.isNotEmpty(expDataMap)  && TRUE.equalsIgnoreCase(expDataMap.get(Constants.ENABLE_MERGED_PROPERTY_TYPE)))
                continue;
            if(MERGE_PROPERTY_TYPE.equalsIgnoreCase(entry.getKey()) && MapUtils.isNotEmpty(expDataMap)  && !TRUE.equalsIgnoreCase(expDataMap.get(Constants.ENABLE_MERGED_PROPERTY_TYPE)))
                continue;

            if (SPOTLIGHT_FILTER.equalsIgnoreCase(entry.getKey()) && MapUtils.isNotEmpty(expDataMap) && !TRUE.equalsIgnoreCase(expDataMap.get(Constants.ENABLE_SPOTLIGHT_FILTER)))
                continue;

            if (entry.getKey().equalsIgnoreCase("LUX") ){
//                    GIHTL_15565 Enable Luxury Filter Is always false.
//                    && MapUtils.isNotEmpty(expDataMap) && ((expDataMap.containsKey("enableLuxeFilter") && !expDataMap.get("enableLuxeFilter").equalsIgnoreCase("true")) || (!expDataMap.containsKey("enableLuxeFilter")))){
                continue;
            }
            if(PRIVATE_ROOM.equalsIgnoreCase(entry.getKey()) && !FUNNEL_SOURCE_HOSTEL.equalsIgnoreCase(funnelSource)) {
                continue;
            }
            if (FLEXIBLE_CHECKIN.equalsIgnoreCase(entry.getKey()) && !isFlexibleCheckinApplicable(expDataMap, filterRequest.getSearchCriteria().getCheckIn(), filterRequest.getSearchCriteria().getCheckOut()))
                continue;


            if(MapUtils.isNotEmpty(fConfigCat.getCondition())) {
                if (fConfigCat.getCondition().containsKey(Constants.FILTER_COND_FUNNEL) && StringUtils.isNotBlank(fConfigCat.getCondition().get(Constants.FILTER_COND_FUNNEL)) &&
                        !fConfigCat.getCondition().get(Constants.FILTER_COND_FUNNEL).contains(funnelSource))
                    continue;

                String country= null!=filterRequest.getSearchCriteria() && "IN".equalsIgnoreCase(filterRequest.getSearchCriteria().getCountryCode())? "DOM":"INTL";
                if (fConfigCat.getCondition().containsKey(Constants.COUNTRY) &&
                        !country.equalsIgnoreCase(fConfigCat.getCondition().get(Constants.COUNTRY))) {
                    continue;
                }

                if (entry.getKey().equalsIgnoreCase("STAR_CATEGORY") &&
                        (funnelSource.equalsIgnoreCase("HOMESTAY") || funnelSource.equalsIgnoreCase(Constants.FUNNEL_SOURCE_HOMESTAY_NEW) )&&
                        filterRequest.getSearchCriteria().getCountryCode().equalsIgnoreCase(Constants.DOM_COUNTRY))
                    continue;

                if (fConfigCat.getCondition().containsKey(Constants.SITE_DOMAIN) && !siteDomain.equalsIgnoreCase(fConfigCat.getCondition().get(Constants.SITE_DOMAIN))) {
                    continue;
                }

//                if(fConfigCat.getCondition().containsKey("isLuxeFilter")){
//                    if("YES".equalsIgnoreCase(fConfigCat.getCondition().get("isLuxeFilter")) &&  !Constants.LUXE_LOCATION_ID.equalsIgnoreCase(locationId))
//                        continue;
//                    else if("NO".equalsIgnoreCase(fConfigCat.getCondition().get("isLuxeFilter")) &&  Constants.LUXE_LOCATION_ID.equalsIgnoreCase(locationId))
//                        continue;
//                }

                if(fConfigCat.getCondition().containsKey(Constants.IS_LUXE_ONLY_FILTER)){
                    if(StringUtils.isBlank(fConfigCat.getCondition().get(Constants.IS_LUXE_ONLY_FILTER)) || ("YES".equalsIgnoreCase(fConfigCat.getCondition().get(Constants.IS_LUXE_ONLY_FILTER)) &&  !Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType)))
                        continue;
                }else{
                    if(Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType))
                        continue;
                }

            }else{
                if(Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType))
                    continue;
            }

            if(Constants.CLIENT_DESKTOP.equalsIgnoreCase(filterRequest.getClient())){
                if(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER))){
                    if (Constants.FILTER_HOTEL_PRICE.equalsIgnoreCase(entry.getKey())){
                        continue;
                    }
                } else {
                    if (Constants.FILTER_HOTEL_PRICE_BUCKET.equalsIgnoreCase(entry.getKey()) && !Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)){
                        continue;
                    }
                }
            }

            if(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)){
                if(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER))){
                    if (FilterGroup.PROPERTY_CATEGORY.name().equalsIgnoreCase(entry.getKey())){
                        continue;
                    }
                } else {
                    if (FilterGroup.PROPERTY_TYPE.name().equalsIgnoreCase(entry.getKey())){
                        continue;
                    }
                }
            }

            if((expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) == null || !Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER)))
                    && Constants.FILTER_SPACE.equalsIgnoreCase(entry.getKey()) && MapUtils.isNotEmpty(fConfigCat.getGroups())){
                fConfigCat.getGroups().remove(FilterGroup.BOOKING.name());
            }

            if((expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) == null || !Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER)))
                    && (FilterGroup.HOUSE_RULES.name().equalsIgnoreCase(entry.getKey()) || Constants.FILTER_ROOMS_AND_BEDS.equalsIgnoreCase(entry.getKey()))){
                continue;
            }

            if(entry.getKey().equalsIgnoreCase(Constants.LOCALITY_GROUP) && filterRequest.getFeatureFlags()!=null && !filterRequest.getFeatureFlags().isFilterRanking()){
                continue;
            }
            FilterCategory fRespCategory = new FilterCategory();
            fRespCategory.setCategoryName(entry.getKey());
            String title;
            // For GI there is no usecase of making the title null for price filters.
//            if (expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER)) &&
//                    Constants.FILTER_HOTEL_PRICE_BUCKET.equalsIgnoreCase(entry.getKey()) &&
//                    (Constants.ANDROID.equalsIgnoreCase(filterRequest.getClient()) || Constants.DEVICE_IOS.equalsIgnoreCase(filterRequest.getClient()))){
//                title = null; // Todo : If price range filter title is required. Remove the above condition.
//            } else{
//                title = updateCategoryTitle(entry.getKey(), fConfigCat, filterRequest, filterResponseHES, expDataMap);
//            }
            title = updateCategoryTitle(entry.getKey(), fConfigCat, filterRequest, filterResponseHES, expDataMap);
            fRespCategory.setTitle(title);

            fRespCategory.setShowMore(fConfigCat.isShowMore());
            fRespCategory.setMinItemsToShow(fConfigCat.getMinItemsToShow());
            fRespCategory.setShowImageUrl(fConfigCat.isShowImageUrl());
            fRespCategory.setViewType(fConfigCat.getViewType());
            fRespCategory.setSingleSelection(fConfigCat.isSingleSelection());
            fRespCategory.setShowCustomRange(fConfigCat.isShowCustomRange());
            fRespCategory.setCustomRangeTitle(fConfigCat.getCustomRangeTitle());
            fRespCategory.setVisible(fConfigCat.isVisible());
            fRespCategory.setIconUrl(fConfigCat.getIconUrl());
            if((entry.getKey().equalsIgnoreCase(LOCATION))) {
                fRespCategory.setSearchable(true);
            }
            if (FilterGroup.AMENITIES.name().equalsIgnoreCase(entry.getKey())){
                fRespCategory.setSearchable(true);
                fRespCategory.setViewType(Constants.VIEW_TYPE_FLEX);
                fRespCategory.setFilters(buildFilterList(entry.getValue(), filterResponseHES, filterRequest, filterConfig, expDataMap, entry.getKey(), idContext, commonModifierResponse));
            } else if (expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER)) && FilterGroup.AMENITIES.name().equalsIgnoreCase(entry.getKey())){
                List<Filter> filterList = buildFilterList(entry.getValue(), filterResponseHES, filterRequest, filterConfig, expDataMap, entry.getKey(), idContext, commonModifierResponse);
                fRespCategory.setFilterCollection(buildFilterCollection(filterList, funnelSource));
                fRespCategory.setSearchable(true);
                fRespCategory.setViewType(Constants.VIEW_TYPE_FLEX);
            } else {
                fRespCategory.setFilters(buildFilterList(entry.getValue(), filterResponseHES, filterRequest, filterConfig, expDataMap, entry.getKey(), idContext, commonModifierResponse));
            }
            updateMinItemsToShowAndShowMore(fRespCategory, fConfigCat);

            if ((entry.getKey().equalsIgnoreCase(Constants.LOCALITY_GROUP)) || CollectionUtils.isNotEmpty(fRespCategory.getFilters()) || CollectionUtils.isNotEmpty(fRespCategory.getFilterCollection()) || (entry.getKey().equalsIgnoreCase(LOCATION))) {
                int index = filterConfig.getRankOrder() != null && filterConfig.getRankOrder().containsKey(entry.getKey()) ? filterConfig.getRankOrder().get(entry.getKey()) : -1;
                if (index > 0) {
                    fCategoryRankedArr[index - 1] = fRespCategory;
                } else {
                    nonRankedFilterCategoryList.add(fRespCategory);
                }
            }


        }

        if (fCategoryRankedArr != null){
            filterCategoryList = new ArrayList<>(Arrays.asList(fCategoryRankedArr));
            // In case of country page, locality filter is moved to top so that the users can easily filter the hotels by
            // locality in a country before removing null filters
            if (COUNTRY.equalsIgnoreCase(locationType))
                moveLocalityFilterToTop(filterCategoryList, filterConfig);
            // remove null filters
            filterCategoryList = filterCategoryList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if(!Utility.isFilterAppliedRequest(filterRequest.getFilterCriteria(), filterRequest.getMatchMakerDetails())) { // Assuming this as First call.
            // Requirement is to:
            // 1. Remove filter which have count 0 for popular filters.
            // 2. All other filter categories, we need to push to the end.
            for (FilterCategory filterCat : filterCategoryList) {
                if (StringUtils.equalsIgnoreCase(filterCat.getCategoryName(), FILTER_POPULAR)) {
                    //filterCat.getFilters().stream().filter(filter -> LOCATION.equalsIgnoreCase(filter.getFilterGroup())).forEach(filter -> filter.setFilterExists(true)); // Setting filterExists as true for location filters in popular filters section. This is required for FE to support grey out logic.
                    // During first call when filter count is 0, we are removing the filter from popular filter section
                    filterCat.setFilters(filterCat.getFilters().stream().filter(obj -> obj.getFilterExists() != null && obj.getFilterExists()).collect(Collectors.toList()));
                } else {
                    // Filters whose count is non zero.
                    if(filterCat.getFilters() != null) {
                        List<Filter> filtersExistsList = filterCat.getFilters().stream().filter(obj -> obj.getFilterExists() != null && obj.getFilterExists()).collect(Collectors.toList());
                        // Filters whose count is zero.
                        List<Filter> filtersListWithCountZero = filterCat.getFilters().stream().filter(obj -> obj.getFilterExists() == null || !obj.getFilterExists()).collect(Collectors.toList());
                        filterCat.setFilters(filtersExistsList);
                        filterCat.getFilters().addAll(filtersListWithCountZero);
                    }
                }
            }
        }

        // Show already applied filters in expanded state. i,e showMore is always set to false. And minItemsToShow is not sent.
//        if(filterRequest.getFilterCriteria() != null) {
//            for (com.mmt.hotels.clientgateway.request.Filter filterApplied : filterRequest.getFilterCriteria()) {
//                for (FilterCategory filterCat : filterCategoryList) {
//                    for (Filter eachFilter : filterCat.getFilters()) {
//                        // Range filter
//                        // Regular filter
//                        if (BooleanUtils.isTrue(eachFilter.getRangeFilter()) && filterApplied.getFilterRange() != null) {
//                            // This condition is added because for price filters the range values won't remain same.
//                            if (StringUtils.equalsIgnoreCase(eachFilter.getFilterGroup(), filterApplied.getFilterGroup().toString()) && StringUtils.equalsIgnoreCase(FILTER_PRICE_BUCKET_HES, filterApplied.getFilterGroup().toString())) {
//                                filterCat.setShowMore(false);
//                                filterCat.setMinItemsToShow(null);
//                            } else if (StringUtils.equalsIgnoreCase(eachFilter.getFilterGroup(), filterApplied.getFilterGroup().toString()) && eachFilter.getFilterRange().getMinValue() == filterApplied.getFilterRange().getMinValue() && eachFilter.getFilterRange().getMaxValue() == filterApplied.getFilterRange().getMaxValue()) {
//                                filterCat.setShowMore(false);
//                                filterCat.setMinItemsToShow(null);
//                            }
//                        } else if (StringUtils.equalsIgnoreCase(eachFilter.getFilterGroup(), filterApplied.getFilterGroup().toString()) && StringUtils.equalsIgnoreCase(eachFilter.getFilterValue(), filterApplied.getFilterValue().toString()) && !StringUtils.equalsIgnoreCase(AMENITIES, filterApplied.getFilterGroup().toString())) { // Amenities is driven through separate screen.
//                            filterCat.setShowMore(false);
//                            filterCat.setMinItemsToShow(null);
//                        }
//                    }
//                }
//            }
//        }

       /* if(CollectionUtils.isNotEmpty(nonRankedFilterCategoryList))
            filterCategoryList.addAll(nonRankedFilterCategoryList);*/
        return filterCategoryList;
    }

    /**
     * Move the LOCALITY filter to the first position if its present in the ranked list
     * @param rankedFilterList a list of {@link FilterCategory}  ranked in order
     * @param filterConfig filter configuration taken from PMS
     */
    private void moveLocalityFilterToTop(List<FilterCategory> rankedFilterList, FilterConfiguration filterConfig) {
        if(CollectionUtils.isEmpty(rankedFilterList)) return;
        int localityIndex = filterConfig != null && filterConfig.getRankOrder() != null && filterConfig.getRankOrder().containsKey(LOCALITY_GROUP) ? filterConfig.getRankOrder().get(LOCALITY_GROUP) : -1;
        if (localityIndex > 0) {
            FilterCategory localityFilterCategory = rankedFilterList.remove(localityIndex - 1);
            rankedFilterList.add(0, localityFilterCategory);
        }
    }

    /**
     * HTL-38235
     * If size of filter values is greater than filterMinItemsLimit then :
     * -> showMore will be set as true
     * -> minItemsToShow will be filterMinItemsToShow
     * Eg. FilterList size = 10
     * filterMinItemsLimit = 7
     * filterMinItemsToShow = 5
     * 5 filters will be shown with 5 in show more
     **/
    private void updateMinItemsToShowAndShowMore(FilterCategory fRespCategory, FilterConfigCategory fConfigCat) {
        if (fRespCategory != null && CollectionUtils.isNotEmpty(fRespCategory.getFilters())) {
            if(fConfigCat != null && fConfigCat.getMinItemsToShow()!=null && fConfigCat.getMinItemsToShow() > 0) {
                if (fRespCategory.getFilters().size() > fConfigCat.getMinItemsToShow()) {
                    fRespCategory.setShowMore(true);
                    fRespCategory.setMinItemsToShow(fConfigCat.getMinItemsToShow());
                } else {
                    fRespCategory.setShowMore(false);
                    fRespCategory.setMinItemsToShow(null);
                }
            } else {
                if (fRespCategory.getFilters().size() > filterMinItemsLimit) {
                    fRespCategory.setShowMore(true);
                    fRespCategory.setMinItemsToShow(filterMinItemsToShow);
                } else {
                    fRespCategory.setShowMore(false);
                    fRespCategory.setMinItemsToShow(null);
                }
            }
        }
    }

    private Map<String, String> getExpDataMap(String expDataReq) {

        Map<String, String> existingExpData = new HashMap<>();
        if (!StringUtils.isEmpty(expDataReq)) {
            String experimentString = expDataReq.replaceAll("^\"|\"$", "");
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            //TODO
            existingExpData = new Gson().fromJson(experimentString, type);
        }
        return existingExpData;
    }

    private String updateCategoryTitle(String categoryName, FilterConfigCategory fConfigCat, FilterCountRequest filterCountRequest, FilterSearchMetaDataResponse filterRespHES, LinkedHashMap<String,String>expDataMap) {
        String contextualExpValue = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.EXP_CONTEXTUAL_FILTER):EMPTY_STRING);
        String categoryTitle = fConfigCat.getTitle();
        switch (categoryName) {
            case Constants.FILTER_HOTEL_PRICE:
                categoryTitle = filterHelper.fetchPriceTitle(expDataMap);
                break;
            case Constants.FILTER_HOTEL_PRICE_BUCKET:
                if(filterCountRequest.getRequestDetails() != null && StringUtils.isNotEmpty(filterCountRequest.getClient()) &&
                        Arrays.asList(DEVICE_OS_DESKTOP, DEVICE_OS_PWA).contains(filterCountRequest.getClient().toLowerCase()) && Utility.isGroupBookingFunnel(filterCountRequest.getRequestDetails().getFunnelSource())) {
                    categoryTitle = filterHelper.fetchPriceTitle(expDataMap);
                }
                break;
            case Constants.FILTER_POPULAR:
                // Title text in popular section is static for GI.
//                if(StringUtils.isNotBlank(contextualExpValue) && !StringUtils.equalsIgnoreCase(contextualExpValue, "A")) {
//                    categoryTitle = polyglotService.getTranslatedData(ConstantsTranslation.CONTEXTUAL_POPULAR_FILTER_TITLE);
//                    if(StringUtils.isEmpty(filterRespHES.getLocationName())) {
//                        categoryTitle = categoryTitle.replace("{filter_text}", EMPTY_STRING);
//                    }
//                }
//                // in {place_name}
//                String titleShown = polyglotService.getTranslatedData("FILTER_POPULAR_TITLE_IN");
//                if(StringUtils.isNotEmpty(filterRespHES.getLocationName())) {
//                    String appendText = (Constants.DOM_COUNTRY.equalsIgnoreCase(filterCountRequest.getSearchCriteria().getCountryCode()) ? titleShown.replace("{place_name}", filterRespHES.getLocationName()) : (Constants.DEFAULT_SITE_DOMAIN.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()))) ? polyglotService.getTranslatedData("AMONG_INDIANS") : titleShown.replace("{place_name}", filterRespHES.getLocationName()));
//                    categoryTitle = categoryTitle.replace("{filter_text}", appendText);
//                }
                break;
            case Constants.FILTER_PRICE_MANUAL:
                categoryTitle = filterRespHES.getFilterDataMap().containsKey(FilterGroup.HOTEL_PRICE_BUCKET) ? polyglotService.getTranslatedData("OR_ENTER_RANGE") : polyglotService.getTranslatedData("ENTER_PRICE_RANGE");
                break;
            case Constants.PRICE_CHECKBOX:
                categoryTitle = categoryTitle.replace("max_budget_price", ""+ getMaxBudgetPriceForCity(filterCountRequest));
            default:

        }
        return categoryTitle;

    }

    public int getMaxBudgetPriceForCity(FilterCountRequest filterCountRequest) {
        int defaultMaxBudget = 3000;
        if(filterCountRequest == null || filterCountRequest.getSearchCriteria() == null){
            return defaultMaxBudget;
        }
        String cityCode = filterCountRequest.getSearchCriteria().getCityCode();
        if(org.apache.commons.collections4.MapUtils.isNotEmpty(budgetHotelCityConfig)){
            AtomicReference<Integer> value = new AtomicReference<>(defaultMaxBudget);
            budgetHotelCityConfig.forEach((x,y) -> {
                if(y.contains(cityCode)){
                    value.set(x);
                }
            });
            return value.get();
        }
        return defaultMaxBudget;
    }

    private List<FilterCollection> buildFilterCollection(List<Filter> filterList, String funnelSource){
        List<FilterCollection> filterCollectionList = new ArrayList<>();
        String funnelKey = Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource) ? Constants.FUNNEL_SOURCE_HOMESTAY : Constants.FUNNEL_SOURCE_HOTELS;

//        if(MapUtils.isEmpty(amenitiesCategoryConfigPolyGlot) || MapUtils.isEmpty(amenitiesCategoryConfigPolyGlot.get(funnelKey))){
//            return filterCollectionList;
//        }

        Map<String, AmenityCategory> amenityCategoryMap = amenitiesCategoryConfigPolyGlot.get(funnelKey);
        for(Map.Entry<String, AmenityCategory> amenityCategoryEntry: amenityCategoryMap.entrySet()){
            FilterCollection filterCollection = new FilterCollection();
            filterCollection.setCollectionName(polyglotService.getTranslatedData(amenityCategoryEntry.getValue().getTitle()));
            List<Filter> finalFilters = new ArrayList<>();
            for (Filter filter: filterList){
                if(amenityCategoryEntry.getValue().getAmenities().contains(filter.getFilterValue())){
                    if(Constants.GUESTS_LOVE.equalsIgnoreCase(amenityCategoryEntry.getKey()))
                        filter.setIconType(Constants.ICON_TYPE_LIGHTNING);
                    finalFilters.add(filter);
                }
            }
            filterCollection.setFilters(finalFilters);
            filterCollectionList.add(filterCollection);
        }
        return filterCollectionList;
    }

    private List<Filter> buildFilterList(FilterConfigCategory fConfigCat, FilterSearchMetaDataResponse filterResponseHES,
                                         FilterCountRequest filterCountRequest, FilterConfiguration filterConfig, Map<String, String> expDataMap, String filterConfigKey, String idContext, CommonModifierResponse commonModifierResponse) {
        List<Filter> filterList = new ArrayList<>();
        if (filterConfigKey.equalsIgnoreCase(LOCATION)) {
            return filterList;
        }
        // Add a filter section directly from the filterCategoryMap if present.
        if (MapUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap())
                && CollectionUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap().get(filterConfigKey))) {
            return addAllFilterFromCategoryMap(filterResponseHES, idContext, filterConfigKey, expDataMap, filterCountRequest, filterConfig.getSuggestedFiltersThreshold());
        }
        if (MapUtils.isEmpty(fConfigCat.getGroups()))
            return null;

        for (Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> entry : fConfigCat.getGroups().entrySet()) {
            try {
                if (Constants.FILTER_PRICE_MANUAL.equalsIgnoreCase(entry.getKey())) {
                    Filter filter = new Filter();
                    filter.setFilterGroup(Constants.FILTER_PRICE_MANUAL);
                    filterList.add(filter);
                    return filterList;
                } else if (FilterGroup.BED.name().equalsIgnoreCase(entry.getKey()) || FilterGroup.BEDROOM.name().equalsIgnoreCase(entry.getKey())) {
                    filterList.add(getRoomOrBedFilter(entry.getKey(), entry.getValue().get(entry.getKey())));
                }
                // This condition is to merge a filter category in an already configured filter section.
                else if (HES_CATEGORY_FILTERS.contains(entry.getKey())) {
                    if (MapUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap()) && CollectionUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap().get(entry.getKey()))) {
                        List<Filter> filters = addAllFilterFromCategoryMap(filterResponseHES, idContext, entry.getKey(),
                                expDataMap, filterCountRequest, filterConfig.getSuggestedFiltersThreshold());
                        if (CollectionUtils.isNotEmpty(filters))
                            filterList.addAll(filters);
                        }
                }
                else if (filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(entry.getKey())) != null) {
                    if (MapUtils.isNotEmpty(entry.getValue())) {
                        List<Filter> filters = addSelectiveFilter(filterResponseHES, entry.getKey(), entry.getValue(), filterConfig, idContext, filterConfigKey, filterCountRequest.getRequestDetails(), filterCountRequest.getSearchCriteria(), filterCountRequest, expDataMap);
                        if (CollectionUtils.isNotEmpty(filters))
                            filterList.addAll(filters);
                    } else {
                        List<Filter> filters = addAllFilterInGroup(filterResponseHES, entry.getKey(), filterConfig, idContext, filterConfigKey, expDataMap, filterCountRequest);
                        if (CollectionUtils.isNotEmpty(filters))
                            filterList.addAll(filters);
                    }
                } else if (FilterGroup.valueOf(entry.getKey()) == FilterGroup.HOTEL_PRICE) {
                    filterList.addAll(createDefaultPriceHistogramBuckets(StringUtils.isNotBlank(filterCountRequest.getSearchCriteria().getCurrency()) ? filterCountRequest.getSearchCriteria().getCurrency().toUpperCase() : filterCountRequest.getSearchCriteria().getCurrency()));
                } else if (FilterGroup.valueOf(entry.getKey()) == FilterGroup.HOTEL_PRICE_BUCKET) {
                    filterList.addAll(createDefaultPriceHistogramCorpBuckets(StringUtils.isNotBlank(filterCountRequest.getSearchCriteria().getCurrency()) ? filterCountRequest.getSearchCriteria().getCurrency().toUpperCase() : filterCountRequest.getSearchCriteria().getCurrency()));
                }
            } catch (Exception ex) {
                logger.error("Error in configuring filter for :{} , exception message:{}", entry.getKey(), ex.getMessage(), ex);
            }
        }
        if (StringUtils.equalsIgnoreCase(filterConfigKey, FilterGroup.AMENITIES.name()))
            filterList = filterList.stream().sorted(Comparator.comparing(Filter::getTitle)).collect(Collectors.toList());
        if (isRepositionFiltersWithCountZero(commonModifierResponse)) {
            appendFilterWithCountZeroAtLast(filterList);
        }
        if (PROPERTY_TYPE.equalsIgnoreCase(filterConfigKey) || MERGE_PROPERTY_TYPE.equalsIgnoreCase(filterConfigKey))
            sortByPropertyCount(filterList);
        // Removing duplicate added filters. A filter is uniquely identified on FilterGroup and FilterValue.
        List<Filter> uniqueFilterList = new ArrayList<>();
        for (Filter f: filterList) {
            String key = getFilterKey(f);
            if (key==null || uniqueFilterList.stream().noneMatch(uniqueFilter -> getFilterKey(uniqueFilter).equals(key))) {
                uniqueFilterList.add(f);
            }
        }
        return uniqueFilterList;
    }

    private static String getFilterKey(Filter f) {
        if(StringUtils.isBlank(f.getFilterGroup()) || StringUtils.isBlank(f.getFilterValue()))
            return null;
        return f.getFilterGroup() + PIPE_SEPARATOR + f.getFilterValue();
    }

    private boolean isRepositionFiltersWithCountZero(CommonModifierResponse commonModifierResponse){
//        this function helps us to identify if we have to resposition the filters with count 0 at last HTL-37652
        return (DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) && commonModifierResponse!=null && commonModifierResponse.getExtendedUser()!=null &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
    }

    private void appendFilterWithCountZeroAtLast(List<Filter> filterList){
//        this function extracts the filters that has count 0 in filter list and then append them at last
        if(CollectionUtils.isNotEmpty(filterList)) {
            List<Filter> filtersWithCountZero = new ArrayList<>();
            filterList.forEach(e -> {
                if (StringUtils.isNotEmpty(e.getFilterGroup()) && !HOTEL_PRICE.equalsIgnoreCase(e.getFilterGroup()) && !FILTER_PRICE_BUCKET_HES.equalsIgnoreCase(e.getFilterGroup()) && e.getCount() != null && e.getCount() == 0) {
                    filtersWithCountZero.add(e);
                }
            });
            filtersWithCountZero.forEach(e -> {
                filterList.remove(e);
                filterList.add(e);
            });
        }
    }

    private void sortByPropertyCount(List<Filter> filterList) {
        try
        {
            if(CollectionUtils.isNotEmpty(filterList))
                filterList.sort(Comparator.comparing(Filter::getCount).reversed());
        }
        catch (Exception ex)
        {
            logger.error("Error while sorting filterList on the basis of PropertyCount",ex);
        }
    }

    public List<Filter> buildFilter(List<com.mmt.hotels.filter.Filter> filters) {
        List<Filter> filterList = new ArrayList<>();
        filters.forEach(filterHes -> {

          filterList.add(commonResponseTransformer.buildFilterCG(filterHes));
        });
        return filterList;
    }

    private Filter getRoomOrBedFilter(String filterGroup, FilterConfigDetail filterConfigDetail) {
        Filter filter = new Filter();
        filter.setQuantityFilter(true);
        filter.setTitle(filterConfigDetail.getTitle());
        filter.setFilterGroup(filterGroup);
        filter.setFilterValue("0");
        filter.setIconList(filterConfigDetail.getIconList());
        filter.setImageUrl(filterConfigDetail.getImageUrl());
        return filter;
    }

    private List<Filter> createDefaultPriceHistogramCorpBuckets(String currency) {
        List<Filter> filterList = null;

        if (defaultPriceHistConfigCorp.containsKey(currency)) {
            List<Integer> config = defaultPriceHistConfigCorp.get(currency);
            filterList = new ArrayList<>();
            int sequence = 0;
            for (int i = 0; i < config.get(0) + config.get(1); i += config.get(1)) {
                Filter filter = new Filter();
                filter.setFilterGroup(FilterGroup.HOTEL_PRICE_BUCKET.name());
                filter.setRangeFilter(true);
                filter.setSequence(sequence++);
                filter.setFilterRange(new FilterRange());
                filter.getFilterRange().setMinValue(i);
                filter.getFilterRange().setMaxValue((i + config.get(1)) > config.get(0) ? config.get(0) * 7 : (i + config.get(1)));
                if(i==0)
                    filter.setTitle("Under " + Currency.getCurrencyEnum(currency).getCurrencySymbol() + " " + filter.getFilterRange().getMaxValue() );
                else if(i>=config.get(0))
                    filter.setTitle(Currency.getCurrencyEnum(currency).getCurrencySymbol() + " " + filter.getFilterRange().getMinValue() + " & above");
                else
                    filter.setTitle(Currency.getCurrencyEnum(currency).getCurrencySymbol() + " " + filter.getFilterRange().getMinValue() + " - " + Currency.getCurrencyEnum(currency).getCurrencySymbol() + " " + filter.getFilterRange().getMaxValue());
                filter.setFilterExists(true); // filter Exists is being set to true here when price buckets are not coming from HF and default price buckets are picked.
                filterList.add(filter);
            }
        }

        return filterList;
    }

    public List<Filter> createDefaultPriceHistogramBuckets(String currency) {
        List<Filter> filterList = null;

        if (defaultPriceHistConfig.containsKey(currency)) {
            List<Integer> config = defaultPriceHistConfig.get(currency);
            filterList = new ArrayList<>();
            int sequence = 0;
            for (int i = 0; i < config.get(0); i += config.get(1)) {
                Filter filter = new Filter();
                filter.setFilterGroup(FilterGroup.HOTEL_PRICE.name());
                filter.setRangeFilter(true);
                filter.setSequence(sequence++);
                filter.setFilterRange(new FilterRange());
                filter.getFilterRange().setMinValue(i);
                filter.getFilterRange().setMaxValue((i + config.get(1)) > config.get(0) ? config.get(0) : (i + config.get(1)));
                filterList.add(filter);
            }
        }

        return filterList;
    }

    private boolean isValidUserTier(String filterValue, int userTier) {
        String currentUserTierValue = String.format(CURRENT_USERTIER, userTier);
        return filterValue.equalsIgnoreCase(currentUserTierValue);
    }

    private boolean isBnplApplicable(Map<String, String> expDataMap, String flavour, String checkIn) {
        String bnplApplicable = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.BNPLApplicable):EMPTY_STRING);
        String enableBnplV2 = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.enableBnplV2):EMPTY_STRING);
        String newBnplKt = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.newBnplKt):EMPTY_STRING);
        String bookatzero = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.BOOK_AT_ZERO):EMPTY_STRING);
        if(!isRnplEnabled) {
            return false;
        } else if (!StringUtils.equalsIgnoreCase(bnplApplicable, "2") && (!StringUtils.equalsIgnoreCase(enableBnplV2, "2"))) { // We are not adding any app version check here since filter-count is called on new apps anyway.
            // Irrespective of rnpl conditions we are returning false if pokus sends BNPL Applicable as false.
            return false;
        }
        if (StringUtils.equalsIgnoreCase(newBnplKt,"0") && StringUtils.isEmpty(bookatzero)) {
            return false;
        }


        // No flavour wise restriction present.

        // City wise there is no restriction.

        // No RnplBlackListed cities check present.

        // No User type check present.

        int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
        if(ap <= RNPL_BOOKING_CUTOFF_DAYS) { // checkIn != nil && (checkIn.Sub(time.Now()).Hours()/24) <= Constants.RNPL_BOOKING_CUTOFF_DAYS {
            return false;
        }
        return true;
    }

    public boolean isRushDealsFilterApplicable(LinkedHashMap<String, String> expDataMap, String checkIn) {
        String rushDealsInExpVal = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(rushDetailsExp):EMPTY_STRING);
        //For AP ==0 Filter to be shown post 3 pm.
        int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
        if("TRUE".equalsIgnoreCase(rushDealsInExpVal) && ap == rushDealApGi && hourOfDayIST()>=rushDealTimeInHrGi){
            return true;
        }
        return false;
    }


    // Get the current hour in IST
    public int hourOfDayIST() {
        DateTimeZone istZone = DateTimeZone.forID("Asia/Kolkata");
        LocalTime currentTime = new DateTime(istZone).toLocalTime();
        return currentTime.getHourOfDay();
    }

    private boolean isFlexibleCheckinApplicable(Map<String, String> expDataMap, String checkIn, String checkOut) {
        // 1. Experiment check.
        String flexibleCheckInExpVal = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(flexibleCheckin):EMPTY_STRING);
        // 2. Length of stay check.
        int lengthOfStay = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
        // 3. AP Window check. For AP ==0 Filter to be hidden post 2 pm.
        int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
        if(ap == 0 && hourOfDayIST()>=14){
            return false;
        }
        //CHECK 2 & 3 Have been removed as part of -> GIHTL-15003
        if(!"TRUE".equalsIgnoreCase(flexibleCheckInExpVal))
            return false;
        return true;
    }

    private boolean isLovedByIndiansApplicable(Map<String, String> expDataMap) {
        return MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.getOrDefault(ICV2, EMPTY_STRING));
    }

    private List<Filter> addAllFilterInGroup(FilterSearchMetaDataResponse filterResponseHES, String groupKey, FilterConfiguration filterConfig, String idContext, String filterConfigKey, Map<String, String> expDataMap, FilterCountRequest filterCountRequest) {
        List<Filter> filterList = null;
        if (filterResponseHES.getFilterDataMap().containsKey(FilterGroup.valueOf(groupKey))) {
            filterList = new ArrayList<>();
            int position = 0;
            for (com.mmt.hotels.filter.Filter filterHES : filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(groupKey))) {
                try {
                    //if(!requiredFilters.contains(filterHES.getFilterValue()))
                    //    continue;
                    if(PAY_LATER.equalsIgnoreCase(groupKey) && !isBnplApplicable(expDataMap, filterCountRequest.getDeviceDetails().getBookingDevice(), filterCountRequest.getSearchCriteria().getCheckIn())){
                        continue;
                    }
                    // This check is to allow only valid user tier data to flow to FE.
                    if(BLACKDEALS.equalsIgnoreCase(groupKey) && !isValidUserTier(filterHES.getFilterValue(), filterResponseHES.getGIUserTierNumber())) {
                        continue;
                    }
                    Filter filter = createFilter(null, filterHES, idContext, filterConfigKey, expDataMap, filterCountRequest);
                    if(FILTER_PRICE_BUCKET_HES.equalsIgnoreCase(groupKey)) {
                        String currency = StringUtils.isNotBlank(filterCountRequest.getSearchCriteria().getCurrency()) ? filterCountRequest.getSearchCriteria().getCurrency().toUpperCase() : filterCountRequest.getSearchCriteria().getCurrency();
                        if (filter.getFilterRange().getMaxValue() == Integer.MAX_VALUE) {
                            filter.setTitle(Currency.getCurrencyEnum(currency).getCurrencySymbol() + filter.getFilterRange().getMinValue() + "+");
                        } else {
                            filter.setTitle(Currency.getCurrencyEnum(currency).getCurrencySymbol() + filter.getFilterRange().getMinValue() + " to " + Currency.getCurrencyEnum(currency).getCurrencySymbol() + filter.getFilterRange().getMaxValue());
                        }
                    }
                    if(PAY_LATER.equalsIgnoreCase(groupKey)) {
                        String bookAtZeroExp = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.BOOK_AT_ZERO):EMPTY_STRING);
                        if ("false".equalsIgnoreCase(bookAtZeroExp)) {
                            filter.setTitle(BookAtOne);
                        } else {
                            filter.setTitle(BookAtZero);
                        }
                    }
                    filter.setSequence(position++);
                    filterList.add(filter);
                    if (CollectionUtils.isNotEmpty(filterHES.getSubGroup())) {
                        filter.setSubFilterCategory(createFilterSubGroup(filterHES.getSubGroup(), filterResponseHES, filterConfig, idContext, filterConfigKey, filterCountRequest));
                    }
                } catch (Exception ex) {
                    logger.error("Error in configuring filter for :{} , exception message:{}", groupKey, ex.getMessage(), ex);
                }
            }
        }
        return filterList;
    }

    public List<Filter> addAllFilterFromCategoryMap(FilterSearchMetaDataResponse filterResponseHES, String idContext,
                                                     String filterConfigKey, Map<String, String> expDataMap,
                                                     FilterCountRequest filterCountRequest, int suggestedFiltersThreshold) {
        List<Filter> filterList = new ArrayList<>();
        if (CollectionUtils.isEmpty(filterResponseHES.getFilterCategoryMap().get(filterConfigKey))) {
            return filterList;
        }
        int position = 0;
        for (com.mmt.hotels.filter.Filter filterHES : filterResponseHES.getFilterCategoryMap().get(filterConfigKey)) {
            try {
                if(suggestedFiltersThreshold != 0 && position >= suggestedFiltersThreshold) // added suggestedFiltersThreshold != 0 to prevent form breaking the for loop in case suggestedFiltersThreshold comes as 0 from PMS.
                    break;
                Filter filter = createFilter(null, filterHES, idContext, filterConfigKey,
                        expDataMap, filterCountRequest);
                filter.setSequence(position++);
                filterList.add(filter);
            } catch (Exception ex) {
                logger.error("Error in configuring category filter for category: {} , exception message:{}", filterConfigKey, ex.getMessage(), ex);
            }
        }
        return filterList;
    }

    private List<Filter> addSelectiveFilter(FilterSearchMetaDataResponse filterResponseHES, String groupKey, LinkedHashMap<String, FilterConfigDetail> filterConfigMap, FilterConfiguration filterConfig, String idContext,
                                            String filterConfigKey, RequestDetails requestDetails, SearchHotelsCriteria searchHotelsCriteria, FilterCountRequest filterRequest, Map<String, String> expDataMap) {
        List<Filter> filterList = new ArrayList<>();
        for (Map.Entry<String, FilterConfigDetail> fEntry : filterConfigMap.entrySet()) {
            try {
                Optional<com.mmt.hotels.filter.Filter> f;
                String[] filterString = fEntry.getKey().split(":");
                String filterVal = filterString[0];
                int position = filterList.size();
                if (filterString.length > 1)
                    position = Integer.parseInt(filterString[1]);
                if (filterVal.contains(Constants.FILTER_PREFERRED)) {
                    f = filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(groupKey)).stream().filter(a -> a.getFilterValue().contains(filterVal)).findFirst();
                }
                if(BOOK_NOW_AT_0.equalsIgnoreCase(filterVal) &&
                        (filterResponseHES.getMpFareHoldStatus()==null || !filterResponseHES.getMpFareHoldStatus().isHoldEligible() ||
                                filterResponseHES.getMpFareHoldStatus().getBookingAmount()!=0 ))
                    continue;
                if(BOOK_NOW_AT_1.equalsIgnoreCase(filterVal) &&
                        (filterResponseHES.getMpFareHoldStatus()==null || !filterResponseHES.getMpFareHoldStatus().isHoldEligible() ||
                                filterResponseHES.getMpFareHoldStatus().getBookingAmount()!=1 ))
                    continue;
                // This check is to allow only valid user tier data to flow to FE.
                if(BLACKDEALS.equalsIgnoreCase(groupKey) && !isValidUserTier(filterVal, filterResponseHES.getGIUserTierNumber())) {
                    continue;
                }
                if (FilterGroup.HOTELS_USP.name().equals(groupKey) && !isLovedByIndiansApplicable(expDataMap)) {
                    continue;
                }
                if (FLEXIBLE_CHECKIN.equalsIgnoreCase(groupKey) && !isFlexibleCheckinApplicable(expDataMap, filterRequest.getSearchCriteria().getCheckIn(), filterRequest.getSearchCriteria().getCheckOut()))
                    continue;

                    /* Group Deals filter should not come for funnels other than GROUP */
                else if (requestDetails != null && !StringUtils.equalsIgnoreCase(requestDetails.getFunnelSource(), FUNNEL_SOURCE_GROUP_BOOKING) && StringUtils.equalsIgnoreCase(filterVal, GROUP_DEALS)) {
                    continue;
                } else {
                    f = filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(groupKey)).stream().filter(a -> filterVal.equalsIgnoreCase(a.getFilterValue())).findFirst();
                }
                String country= null!=searchHotelsCriteria && "IN".equalsIgnoreCase(searchHotelsCriteria.getCountryCode())? "DOM":"INTL";
                // fEntry.getValue().getCountry() is empty if no conditions are required on a filter.
                // Else it will contain one of DOM or INTL.
                if (f.isPresent() && (StringUtils.isEmpty(fEntry.getValue().getCountry()) || country.equalsIgnoreCase(fEntry.getValue().getCountry()))) {
                    Filter filter = createFilter(fEntry.getValue(), f.get(), idContext, filterConfigKey, null, filterRequest);
                    if (FLEXIBLE_CHECKIN.equalsIgnoreCase(filterConfigKey)
                            && MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(FLEXI_NEW) && TRUE.equalsIgnoreCase(expDataMap.get(FLEXI_NEW))) {
                        filter.setRightIconUrl(flexibleCheckinTagUrl);
                    }
                    filter.setSequence(position);
                    filterList.add(filter);
                    if (CollectionUtils.isNotEmpty(f.get().getSubGroup())) {
                        filter.setSubFilterCategory(createFilterSubGroup(f.get().getSubGroup(), filterResponseHES, filterConfig, idContext, filterConfigKey, filterRequest));
                    }
                }

            } catch (Exception ex) {
                logger.error("Error in configuring filter for :{} , exception message:{}", fEntry.getKey(), ex.getMessage(), ex);
            }
        }
        return filterList;
    }

    private boolean isCoupleFriendlyFilterApplicable(SearchHotelsCriteria searchCriteria) {
        if (searchCriteria == null || CollectionUtils.isEmpty(searchCriteria.getRoomStayCandidates())) {
            return false;
        }
        int adultCount = 0, childCount = 0;
        List<RoomStayCandidate> roomStayCandidateList = searchCriteria.getRoomStayCandidates();
        if (CollectionUtils.isNotEmpty(roomStayCandidateList)) {
            for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidateList) {
                adultCount += roomStayCandidate.getAdultCount();
                if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                    childCount += roomStayCandidate.getChildAges().size();
                }
            }
        }
        return adultCount == 2 && childCount == 0;
    }

    public FilterCategory createFilterSubGroup(List<FilterGroup> filterHESSubGroupList, FilterSearchMetaDataResponse filterResponseHES, FilterConfiguration filterConfig, String idContext, String filterConfigKey, FilterCountRequest filterRequest) {
        FilterCategory filterCategory = null;
        if (CollectionUtils.isNotEmpty(filterHESSubGroupList) && filterResponseHES != null) {

            for (com.mmt.hotels.filter.FilterGroup filterHESSubGroup : filterHESSubGroupList) {
                List<com.mmt.hotels.filter.Filter> filterHESSubList = filterResponseHES.getFilterDataMap().get(filterHESSubGroup);
                Optional<FilterConfigCategory> filterConfigCategory = filterConfig.getFilters().values().stream().filter(a -> a.getGroups().containsKey(filterHESSubGroup.name())).findFirst();

                if (CollectionUtils.isNotEmpty(filterHESSubList) && filterConfigCategory.isPresent()) {
                    filterCategory = new FilterCategory();
                    List<Filter> subFilterList = new ArrayList<>();
                    for (com.mmt.hotels.filter.Filter filterHESSub : filterHESSubList) {
                        FilterConfigDetail filterConfigDetail = filterConfigCategory.get().getGroups().get(filterHESSubGroup.name()).get(filterHESSub.getFilterValue());
                        subFilterList.add(createFilter(filterConfigDetail, filterHESSub, idContext, filterConfigKey, null, filterRequest));
                    }

                    filterCategory.setViewType(filterConfigCategory.get().getViewType());
                    filterCategory.setShowCustomRange(filterConfigCategory.get().isShowCustomRange());
                    filterCategory.setCustomRangeTitle(filterConfigCategory.get().getCustomRangeTitle());
                    filterCategory.setVisible(filterConfigCategory.get().isVisible());
                    filterCategory.setFilters(subFilterList);
                    filterCategory.setTitle(filterConfigCategory.get().getTitle());
                    filterCategory.setMinItemsToShow(filterConfigCategory.get().getMinItemsToShow());
                    filterCategory.setShowImageUrl(filterConfigCategory.get().isShowImageUrl());
                    filterCategory.setShowMore(filterConfigCategory.get().isShowMore());
                    filterCategory.setIconUrl(filterConfigCategory.get().getIconUrl());
                }

            }
        }
        return filterCategory;
    }

    public Filter createFilter(FilterConfigDetail filterConfigDetail, com.mmt.hotels.filter.Filter filterHES, String idContext, String filterConfigKey, Map<String, String> expDataMap, FilterCountRequest filterRequest) {
        Filter filter = new Filter();
        if (filterConfigDetail != null) {
            filter.setSubTitle(filterConfigDetail.getSubTitle());
            filter.setTitle(filterConfigDetail.getTitle());
            filter.setDescription(filterConfigDetail.getDescription());
            filter.setImageUrl(filterConfigDetail.getImageUrl());
            filter.setIconList(filterConfigDetail.getIconList());
            filter.setAppliedTitle(filterConfigDetail.getAppliedTitle());
        } else if (filterHES != null) {
            filter.setTitle(filterHES.getTitle());
        }
        if (filterHES!=null) {
            if (filterHES.getCount()!=null)
                filter.setCount(filterHES.getCount());
            filter.setFilterGroup(filterHES.getFilterGroup().name());
            filter.setFilterValue(filterHES.getFilterValue());
            filter.setFilterExists(filterHES.getFilterExist());
            if (filterHES.getTooltip() != null) {
                filter.setTooltip(filterHES.getTooltip());
            }
            if (StringUtils.isNotBlank(filterHES.getSubTitle())) {
                filter.setSubTitle(filterHES.getSubTitle());
            }
            if(StringUtils.isBlank(filter.getTitle()))
                filter.setTitle(filterHES.getTitle());
            if(StringUtils.isBlank(filter.getTitle()))
                filter.setTitle(filterHES.getFilterValue());
            // client doesnt need unnecessary false values. so made wrapper Boolean and setting null for false
            filter.setRangeFilter(filterHES.isRangeFilter() ? filterHES.isRangeFilter() : null);
            if (filterHES.isRangeFilter()) {
                FilterRange fRange = new FilterRange();
                fRange.setMinValue(filterHES.getFilterRange().getMinValue());
                fRange.setMaxValue(filterHES.getFilterRange().getMaxValue());
                filter.setFilterRange(fRange);
            }
            if(filterHES.getFilterGroup().equals(FilterGroup.LOCATION)) {
                filter.setMatchmakerType(filterHES.getMatchmakerType());
                filter.setFilterExists(true); // setting filter exists as true by default for location filters [required for FE to support grey out logic.]
            }
            filter.setSuggestedFilters(commonResponseTransformer.buildSuggestedFilters(filterHES.getSuggestedFilters()));
        }
        if (filter.getFilterGroup().equalsIgnoreCase(FilterGroup.AMENITIES.name()) && altAccoAmenities.contains(filter.getFilterValue()) && !StringUtils.equalsIgnoreCase(Constants.CORP_ID_CONTEXT, idContext)
        && !filterConfigKey.equalsIgnoreCase(FILTER_SPACE)) {
            filter.setIconList(new ArrayList<>());
            filter.getIconList().add(Constants.HOMESTAY_ICON);
        }
        if (filterHES != null && StringUtils.isNotEmpty(filterHES.getFilterValue()) && filterHES.getFilterValue().equalsIgnoreCase(PAY_LATER) && isBnplApplicable(expDataMap, filterRequest.getDeviceDetails().getBookingDevice(), filterRequest.getSearchCriteria().getCheckIn())) {
            String bookAtZeroExp = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.BOOK_AT_ZERO):EMPTY_STRING);
            if ("false".equalsIgnoreCase(bookAtZeroExp)) {
                filter.setTitle(BookAtOne);
            } else {
                filter.setTitle(BookAtZero);
            }
        }
        return filter;
    }
}
