package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.PoliciesRequest;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.PoliciesResponse;
import com.mmt.hotels.clientgateway.response.Policy;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.Notices;
import com.mmt.hotels.model.response.pricing.PaymentMode;
import com.mmt.hotels.model.response.pricing.RatePolicy;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.model.response.txn.PersistedTariffInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class PoliciesResponseTransformer {

	@Autowired
	PolyglotService polyglotService;

    @Value("#{'${intl.nr.supplier.exclusion}'.split(',')}")
    private List<String> intlNrSupplierExclusionList;

	public PoliciesResponse transformPolicyResponse(PoliciesRequest policiesRequest,PersistanceMultiRoomResponseEntity txnDataEntity) {
		PoliciesResponse policiesResponse = new PoliciesResponse();
		List<Policy> policies = new ArrayList<>();
		
		PersistedMultiRoomData txnData = txnDataEntity.getPersistedData();
		
    	// 1. populate notices when available 
		
		
		//inputbo to create upfront policies
    	RequestInputBO inputBo = new RequestInputBO.Builder()
    			.buildCountryCode(txnData.getHotelList().get(0).getHotelInfo().getCountryCode())
    			.buildPropertyType(txnData.getHotelList().get(0).getHotelInfo().getPropertyType())
    			.buildMustReadRules(txnData.getHotelList().get(0).getHotelInfo().getMustReadRules())
    			.buildPah(PaymentMode.isPAHOnlyPaymodes(PaymentMode.findPaymentModeFromString(txnData.getAvailReqBody().getPayMode())) ? true : false)
    			.buildCancellationPolicyType(getCancellationPolicyType(txnData.getHotelList().get(0).getTariffInfoList()))
    			.buildCancellationDate(getCancellationDate(txnData.getHotelList().get(0).getTariffInfoList()))
    			.buildSupplierCode(txnData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails().getSupplierCode())
    			.buildCheckinPolicy(getCheckinPolicy(txnData.getHotelList().get(0).getTariffInfoList()))
    			.buildConfirmationPolicy(getConfirmationPolicy(txnData.getHotelList().get(0).getTariffInfoList()))
				.buildNotices(txnData.getHotelList().get(0).getHotelInfo().getNotices())
    			.build();    	
    	
    	// 2. populate must read policies shown upfront
		populateUpfrontMustReadPolicies(policies,inputBo);
		
		// 3. populate house rules
		HouseRules houseRules = txnData.getHotelList().get(0).getHotelInfo().getHouseRules();	
		if(houseRules != null){
			policies.addAll(buildHouseRules(houseRules));
		}

		// 4. populate static policies
		Map<String, List<String>> policiesMap = txnData.getHotelList().get(0).getHotelInfo().getPolicyToMessagesMap();
		populateStaticPolicies(policies, policiesMap);
		
		policiesResponse.setCorrelationKey(policiesRequest.getCorrelationKey());
		policiesResponse.setPolicies(policies);
		return policiesResponse;
	}

    private String getCancellationPolicyType(List<PersistedTariffInfo> tarrifList) {
    	CancelPenalty cancelPenalty = getCancelPenalty(tarrifList);
    	if(cancelPenalty !=null && cancelPenalty.getCancellationType() !=null) {
    		if(CancelPenalty.CancellationType.FREE_CANCELLATON.equals(cancelPenalty.getCancellationType()))
    			return BookedCancellationPolicyType.FC.name();
    		else
    			return BookedCancellationPolicyType.NR.name();
    	}
    	return null;
    }
    
    private String getCancellationDate(List<PersistedTariffInfo> tarrifList) {
    	CancelPenalty cancelPenalty = getCancelPenalty(tarrifList);
    	if(cancelPenalty !=null) {
    		return cancelPenalty.getTillDate();
    	}
    	return null;
    }
    
    private CancelPenalty getCancelPenalty(List<PersistedTariffInfo> tarrifList) {
    	CancelPenalty mostRestrictedCancelPenalty = null;
    	for(PersistedTariffInfo tarrifInfo: tarrifList){
			if(mostRestrictedCancelPenalty == null) {
				mostRestrictedCancelPenalty = tarrifInfo.getCancelPenaltyList().get(0);
				if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(mostRestrictedCancelPenalty.getMostRestrictive())) {
					return mostRestrictedCancelPenalty;
				}
			}
			Optional<CancelPenalty> cancelPenalty = tarrifInfo.getCancelPenaltyList().stream().filter(penalty -> Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(penalty.getMostRestrictive())).findFirst();
			if(cancelPenalty.isPresent()) {
				mostRestrictedCancelPenalty = cancelPenalty.get();
				return mostRestrictedCancelPenalty;
			}
    	}
    	return mostRestrictedCancelPenalty;
    }
	
	private void populateUpfrontMustReadPolicies(List<Policy> policies, RequestInputBO inputBo){

		Policy policy = new Policy();
		policy.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.CAT_MUST_READ));
		policy.setSubCategory("");
		policy.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.TITLE_MUST_READ));
		policy.setRules(new ArrayList<>());

		if(CollectionUtils.isNotEmpty(inputBo.getNotices())){
			for(Notices notice : inputBo.getNotices()){
				policy.getRules().add(notice.getDescription());
			}
		}

    	if(inputBo.getCheckinPolicy() !=null && StringUtils.isNotEmpty(inputBo.getCheckinPolicy().getDescription())) {
    		policy.getRules().add(inputBo.getCheckinPolicy().getDescription());
    	}
    	if(inputBo.getConfirmationPolicy() !=null && StringUtils.isNotEmpty(inputBo.getConfirmationPolicy().getDescription())) {
			if(!BookedCancellationPolicyType.FC.name().equals(inputBo.getCancellationPolicyType())){
				inputBo.getConfirmationPolicy().setDescription(inputBo.getConfirmationPolicy().getDescription() );
			}
			policy.getRules().add(inputBo.getConfirmationPolicy().getDescription());
		}
		
		// Intl + Pah FC/NR text
		if(!Constants.DOM_COUNTRY.equalsIgnoreCase(inputBo.getCountryCode()) && inputBo.isPah()) {
			if(StringUtils.isEmpty(inputBo.getCancellationDate()) || 
					BookedCancellationPolicyType.NR.name().equalsIgnoreCase(inputBo.getCancellationPolicyType())) {
				policy.getRules().add(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_NON_REFUNDABLE_TEXT));
			}else {
				policy.getRules().add(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_FREE_CANCELLATION_TEXT), inputBo.getCancellationDate()));
			}
		}
		
		// must read policies
		if(CollectionUtils.isNotEmpty(inputBo.getMustReadRules())) {
			for (String mustRead : inputBo.getMustReadRules()) {
				policy.getRules().add(mustRead);
			}
		}
		
		// Intl NR text for suppliers other than ingo, expedia
		if(!Constants.DOM_COUNTRY.equalsIgnoreCase(inputBo.getCountryCode()) && 
				BookedCancellationPolicyType.NR.name().equalsIgnoreCase(inputBo.getCancellationPolicyType())) {
			boolean excluded = isSupplierExcluded(inputBo.getSupplierCode());
			if(!excluded) {
				policy.getRules().add(polyglotService.getTranslatedData(ConstantsTranslation.INTL_NR_SUPPLIER_SPECIFIC_TEXT));
			}
		}
		
		if(CollectionUtils.isNotEmpty(policy.getRules())){
			policies.add(policy);
		}
	}
	
    private boolean isSupplierExcluded(String supplierCode) {
    	for (String supplier : intlNrSupplierExclusionList) {
			if(supplierCode.startsWith(supplier))
				return true;
		}
    	return false;
    }
    
    private RatePolicy getCheckinPolicy(List<PersistedTariffInfo> tarrifList) {
    	RatePolicy checkInPolicy = null;
    	for(PersistedTariffInfo tarrifInfo: tarrifList){
				RatePolicy ratePolicy = tarrifInfo.getCheckinPolicy();
				if(checkInPolicy == null && ratePolicy !=null) {
					checkInPolicy = ratePolicy;
					if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(checkInPolicy.getMostRestrictive())) {
						return checkInPolicy;
					}
				}
				else if(checkInPolicy !=null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
					checkInPolicy = ratePolicy;
					return checkInPolicy;
				}
			}
    	return checkInPolicy;
    }
    
    private RatePolicy getConfirmationPolicy(List<PersistedTariffInfo> tarrifList) {
    	RatePolicy ConfirmationPolicy = null;
		for(PersistedTariffInfo tarrifInfo: tarrifList){
			RatePolicy ratePolicy = tarrifInfo.getConfirmationPolicy();
			if(ConfirmationPolicy == null && ratePolicy !=null) {
				ConfirmationPolicy = ratePolicy;
				if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
					return ConfirmationPolicy;
				}
			}
			else if(ConfirmationPolicy !=null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
				ConfirmationPolicy = ratePolicy;
				return ConfirmationPolicy;
			}
		}
    	return ConfirmationPolicy;
    }
    
    
	public List<Policy> buildHouseRules(HouseRules houseRules) {
		List<Policy> policies = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(houseRules.getCommonRules())){
			for(CommonRules commonRule : houseRules.getCommonRules()){
				if(CollectionUtils.isNotEmpty(commonRule.getRules())){
					Policy policy = new Policy();
					policy.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.CAT_HOUSE_RULES));
					policy.setSubCategory(polyglotService.getTranslatedData(ConstantsTranslation.SUBCAT_COMMON_RULES));
					policy.setTitle(commonRule.getCategory());
					policy.setRules(new ArrayList<>());
					for(Rule rule : commonRule.getRules()){
						policy.getRules().add(rule.getText());
						policy.setValues(rule.getValues());
					}
					
					policies.add(policy);
				}
			}
		}
		
		if(CollectionUtils.isNotEmpty(houseRules.getExtraBedPolicyList())){
			for(ChildExtraBedPolicy extraBedPolicy : houseRules.getExtraBedPolicyList()){
				Policy policy = getPolicyForExtraBed(extraBedPolicy);
				if(policy != null){
					policies.add(policy);
				}
			}
		}

		//adding an extra check because child policy is being added twice as it is part of extraBedPolicy List as well
		if(CollectionUtils.isEmpty(houseRules.getExtraBedPolicyList()) && houseRules.getChildExtraBedPolicy() != null){
			Policy policy = getPolicyForExtraBed(houseRules.getChildExtraBedPolicy());
			if(policy != null){
				policies.add(policy);
			}
		}
	
		return policies;
	}

	private void populateStaticPolicies( List<Policy> policies,Map<String, List<String>>  policyToMessagesMap) {
		if(MapUtils.isNotEmpty(policyToMessagesMap)){
			for(String key : policyToMessagesMap.keySet()){
				List<String> plocyRules = policyToMessagesMap.get(key);
				plocyRules = removeEmptyString(plocyRules);
				if (CollectionUtils.isNotEmpty(plocyRules)) {
					Policy policy = new Policy();
					policy.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.CAT_STATIC_POLICY));
					policy.setTitle(key);
					policy.setRules(plocyRules);
					policies.add(policy);
				}
			}
		}
	}

	private List<String> removeEmptyString(List<String> plocyRules) {
		List<String> resp = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(plocyRules)) {
			for (String rule : plocyRules) {
				if (StringUtils.isNotEmpty(rule)) {
					resp.add(rule);
				}
			} 
		}
		return resp;
	}

	private Policy getPolicyForExtraBed(ChildExtraBedPolicy extraBedPolicy) {
		Policy policy = null;
		if(StringUtils.isNotEmpty(extraBedPolicy.getLabel()) && StringUtils.isNotEmpty(extraBedPolicy.getPolicyInfo())){
			policy = new Policy();
			policy.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.CAT_HOUSE_RULES));
			policy.setSubCategory(polyglotService.getTranslatedData(ConstantsTranslation.SUBCAT_EXTRA_BED));
			policy.setTitle(extraBedPolicy.getLabel());
			policy.setRules(new ArrayList<>());
			policy.getRules().add(extraBedPolicy.getPolicyInfo());
			
			if(CollectionUtils.isNotEmpty(extraBedPolicy.getPolicyRules())){
				for(PolicyRules policyRules :  extraBedPolicy.getPolicyRules()){
					if(CollectionUtils.isNotEmpty(policyRules.getExtraBedTerms())){
						for(ExtraBedRules extraBedRules : policyRules.getExtraBedTerms()){
							StringBuilder rule = new StringBuilder("");
							if(StringUtils.isNotEmpty(policyRules.getAgeGroup())){
								rule.append(policyRules.getAgeGroup());
								rule.append("-");										
							}
							if (StringUtils.isNotBlank(extraBedRules.getLabel())) {
								rule.append(extraBedRules.getLabel());
								rule.append("-");
							}
							rule.append(extraBedRules.getValue());
							policy.getRules().add(rule.toString());
						}
					}
				}
			}
		}
		return policy;
	}
}
