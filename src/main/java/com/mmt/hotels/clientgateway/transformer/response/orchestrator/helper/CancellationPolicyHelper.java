package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.CancellationTimeline;
import com.mmt.hotels.clientgateway.response.FCBenefit;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.pricing.CancelRulesDescription;
import com.mmt.hotels.model.response.pricing.CancellationRules;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Helper class for transforming OrchV2 CancellationPolicy directly to CG response format
 * without going through legacy model transformation.
 */
@Component
public class CancellationPolicyHelper {

    @Autowired
    private PolyglotService polyglotService;

    // FCBenefit type constants
    private static final String TYPE_FCZPN = "FCZPN";
    private static final String BNPL_DISABLED = "BNPL_DISABLED";
    private static final String DATE = "DATE";
    private static final int AP_LIMIT_FOR_INCLUSION_ICONS = 2;

    /**
     * Transform OrchV2 CancellationPolicy directly to BookedCancellationPolicy
     * with complete logic including icons, amendment policies, and advance purchase handling.
     * Assumes DETAIL_SEARCH_ROOMS controller context.
     */
    public BookedCancellationPolicy transformCancellationPolicy(CancellationPolicy policy, Integer ap) {

        if (policy == null) {
            return null;
        }

        BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
        String cancellationPolicyType = getCancellationPolicyType(policy);
        boolean isFC = Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(cancellationPolicyType);
        bookedCancellationPolicy.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/policy.png");
        if (isFC) {
            // Free Cancellation logic
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);

            // Set text from penalties if available
            if (CollectionUtils.isNotEmpty(policy.getPenalties())) {
                CancellationPenalty firstPenalty = policy.getPenalties().get(0);
                if (StringUtils.isNotBlank(firstPenalty.getFreeCancellationText())) {
                    bookedCancellationPolicy.setText(firstPenalty.getFreeCancellationText());
                } else {
                    bookedCancellationPolicy.setText(getFreeCancellationText());
                }
            } else {
                bookedCancellationPolicy.setText(getFreeCancellationText());
            }
            // TODO: Add BNPL logic when available in OrchV2
        } else {
            // Non-Refundable logic
            // Set icon type based on advance purchase for DETAIL_SEARCH_ROOMS controller
            if (ap != null && ap < AP_LIMIT_FOR_INCLUSION_ICONS) {
                bookedCancellationPolicy.setIconType(IconType.DEFAULT);
            } else {
                bookedCancellationPolicy.setIconType(IconType.BIGCROSS);
            }

            bookedCancellationPolicy.setText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT));
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.NR);
            bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT));
        }

        if (CollectionUtils.isNotEmpty(policy.getPenalties())) {
            CancellationPenalty firstPenalty = policy.getPenalties().get(0);
            bookedCancellationPolicy.setCancelRules(transformCancelRules(firstPenalty.getCancelRules()));
        }

        return bookedCancellationPolicy;
    }


    /**
     * Transform OrchV2 CancelRules to legacy CancellationRules format
     */
    private List<CancellationRules> transformCancelRules(List<CancelRules> orchCancelRules) {
        if (CollectionUtils.isEmpty(orchCancelRules)) {
            return null;
        }

        List<CancellationRules> cancellationRulesList = new ArrayList<>();
        for (CancelRules orchRule : orchCancelRules) {
            CancellationRules cancellationRules = new CancellationRules();
            cancellationRules.setText(orchRule.getText());

            // Transform descText from OrchV2 CancelRulesDesc to legacy CancelRulesDescription
            if (CollectionUtils.isNotEmpty(orchRule.getDescText())) {
                List<CancelRulesDescription> legacyDescText = new ArrayList<>();
                for (CancelRulesDesc orchDesc : orchRule.getDescText()) {
                    CancelRulesDescription cancelRulesDescription = new CancelRulesDescription();
                    cancelRulesDescription.setFeeText(orchDesc.getFeeText());
                    cancelRulesDescription.setDateText(orchDesc.getDateText());
                    legacyDescText.add(cancelRulesDescription);
                }
                cancellationRules.setDescText(legacyDescText);
            }
            cancellationRulesList.add(cancellationRules);
        }
        return cancellationRulesList;
    }

    /**
     * Get free cancellation text with fallback
     */
    private String getFreeCancellationText() {
        try {
            // Try to get translated text, fallback to default if not available
            String translatedText = polyglotService.getTranslatedData("FREE_CANCELLATION_TEXT");
            if (StringUtils.isNotBlank(translatedText) && !"FREE_CANCELLATION_TEXT".equals(translatedText)) {
                return translatedText;
            }
        } catch (Exception e) {
            // Ignore translation errors and use fallback
        }
        return "Free Cancellation";
    }

    /**
     * Build CancellationTimeline directly from orchestrator v2 CancellationTimelineDetails
     */
    public CancellationTimeline buildCancellationTimelineFromOrchV2(
            com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationTimelineDetails orchTimelineDetails) {

        if (orchTimelineDetails == null) {
            return null;
        }

        CancellationTimeline cancellationTimeline = new CancellationTimeline();

        // Map all fields directly from orchestrator v2 to CG response
        cancellationTimeline.setBookingDate(orchTimelineDetails.getBookingDate());
        cancellationTimeline.setCancellationDate(orchTimelineDetails.getCancellationDate());
        cancellationTimeline.setCancellationDateTime(orchTimelineDetails.getCancellationDateTime());
        cancellationTimeline.setCardChargeDate(orchTimelineDetails.getCardChargeDate());
        cancellationTimeline.setCardChargeDateTime(orchTimelineDetails.getCardChargeDateTime());
        cancellationTimeline.setDateFormat(orchTimelineDetails.getDateFormat());
        cancellationTimeline.setCardChargeText(orchTimelineDetails.getCardChargeText());
        cancellationTimeline.setBookingAmountText(orchTimelineDetails.getBookingAmountText());
        cancellationTimeline.setCheckInDate(orchTimelineDetails.getCheckInDate());
        cancellationTimeline.setCheckInDateTime(orchTimelineDetails.getCheckInDateTime());
        cancellationTimeline.setFreeCancellationText(orchTimelineDetails.getFreeCancellationText());
        cancellationTimeline.setSubTitle(orchTimelineDetails.getSubTitle());
        cancellationTimeline.setTitle(orchTimelineDetails.getTitle());
        cancellationTimeline.setFreeCancellationBenefits(buildFreeCancellationBenefits(orchTimelineDetails.getFreeCancellationBenefits()));

        return cancellationTimeline;
    }

    private List<FCBenefit> buildFreeCancellationBenefits(List<com.gommt.hotels.orchestrator.detail.model.response.pricing.FreeCancellationBenefitDetails> freeCancellationBenefits) {
        if (CollectionUtils.isEmpty(freeCancellationBenefits)) {
            return null;
        }
        List<FCBenefit> benefitsForCG = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.FreeCancellationBenefitDetails orchBenefit : freeCancellationBenefits) {
            FCBenefit benefitCG = new FCBenefit();
            benefitCG.setText(orchBenefit.getText());
            if (orchBenefit.getType().equalsIgnoreCase(TYPE_FCZPN)) {
                benefitCG.setIconType(IconType.DOUBLETICK);
            } else {
                benefitCG.setIconType(IconType.SINGLETICK);
            }
            benefitsForCG.add(benefitCG);
        }
        return benefitsForCG;
    }

    /**
     * Build CancellationPolicyTimeline directly from orchestrator v2 CancellationTimelineDetails
     */
    public CancellationPolicyTimeline buildCancellationPolicyTimelineFromOrchV2(
            com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationTimelineDetails orchTimelineDetails) {

        if (orchTimelineDetails == null || CollectionUtils.isEmpty(orchTimelineDetails.getCancellationPolicyTimelineList())) {
            return null;
        }

        CancellationPolicyTimeline cancellationPolicyTimeline = new CancellationPolicyTimeline();
        cancellationPolicyTimeline.setTimeline(buildCancellationTimelineList(orchTimelineDetails.getCancellationPolicyTimelineList()));
        cancellationPolicyTimeline.setCancellationDate(orchTimelineDetails.getCancellationDate());
        cancellationPolicyTimeline.setCancellationDateTime(orchTimelineDetails.getCancellationDateTime());
        cancellationPolicyTimeline.setCardChargeDate(orchTimelineDetails.getCardChargeDate());
        cancellationPolicyTimeline.setCardChargeDateTime(orchTimelineDetails.getCardChargeDateTime());
        cancellationPolicyTimeline.setDateFormat(orchTimelineDetails.getDateFormat());
        cancellationPolicyTimeline.setCardChargeText(orchTimelineDetails.getCardChargeText());
        cancellationPolicyTimeline.setCardChargeTextTitle(orchTimelineDetails.getCardChargeTextTitle());
        cancellationPolicyTimeline.setCardChargeTextMsg(orchTimelineDetails.getCardChargeTextMsg());
        cancellationPolicyTimeline.setBnplTitleText(orchTimelineDetails.getBnplTitleText());
        cancellationPolicyTimeline.setBookingAmountText(orchTimelineDetails.getBookingAmountText());
        cancellationPolicyTimeline.setTitle(polyglotService.getTranslatedData("RATEPLAN_CANCELLATION_POLICY"));

        // Transform free cancellation benefits
        if (CollectionUtils.isNotEmpty(orchTimelineDetails.getFreeCancellationBenefits())) {
            List<FCBenefit> fcBenefits = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.FreeCancellationBenefitDetails orchBenefit :
                    orchTimelineDetails.getFreeCancellationBenefits()) {
                FCBenefit benefit = new FCBenefit();
                benefit.setText(orchBenefit.getText());
                // Map type to IconType based on the logic from CommonResponseTransformer
                if (orchBenefit.getType() != null) {
                    if (orchBenefit.getType().equalsIgnoreCase(BNPL_DISABLED)) {
                        benefit.setIconType(IconType.DEFAULT);
                    } else if (orchBenefit.getType().equalsIgnoreCase(TYPE_FCZPN)) {
                        benefit.setIconType(IconType.DOUBLETICK);
                    } else {
                        benefit.setIconType(IconType.SINGLETICK);
                    }
                } else {
                    benefit.setIconType(IconType.SINGLETICK);
                }
                fcBenefits.add(benefit);
            }
            cancellationPolicyTimeline.setFreeCancellationBenefits(fcBenefits);
        }

        return cancellationPolicyTimeline;
    }

    private List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> buildCancellationTimelineList(
            List<CancellationPolicyTimelineDetails> orchPolicyTimelineList) {
        if (CollectionUtils.isEmpty(orchPolicyTimelineList)) {
            return null;
        }

        List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> timelineList = new ArrayList<>();
        for (CancellationPolicyTimelineDetails orchTimeline : orchPolicyTimelineList) {
            com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline timeline =
                    new com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline();

            timeline.setRefundable(orchTimeline.isRefundable());
            timeline.setText(orchTimeline.getText());
            timeline.setStartDate(orchTimeline.getStartDate());
            timeline.setEndDate(orchTimeline.getEndDate());
            timeline.setEndDateTime(orchTimeline.getEndDateTime());

            // TODO: Add flexi cancellation details transformation when available

            timelineList.add(timeline);
        }
        return timelineList;
    }

    public String getCancellationPolicyType(CancellationPolicy cancellationPolicy) {
        String penaltyType = CollectionUtils.isNotEmpty(cancellationPolicy.getPenalties()) ? cancellationPolicy.getPenalties().get(0).getType() : "";
        String cancellationType = Constants.CANCELLATION_TYPE_NR;
        if (StringUtils.isNotBlank(penaltyType)) {
            switch (penaltyType.toUpperCase()) {
                case "FC":
                case "FREE_CANCELLATON":
                case "FREE_CANCELLATION":
                    cancellationType = Constants.CANCELLATION_TYPE_FC;
                    break;
                case "PR":
                case "PARTIAL_REFUNDABLE":
                    cancellationType = Constants.CANCELLATION_TYPE_FCZPN;
                    break;
                case "NR":
                case "NON_REFUNDABLE":
                default:
                    cancellationType = Constants.CANCELLATION_TYPE_NR;
                    break;
            }
        }
        return cancellationType;
    }

}
