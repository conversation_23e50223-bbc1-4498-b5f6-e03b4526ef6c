package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Brand;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.filter.FilterRange;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.GuestRecommendationEnabledReqBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_RECOMMENDATION;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;


@Component
public abstract class SearchRoomsRequestTransformer extends BaseRoomRequestTransformer{
	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private Utility utility;

	public PriceByHotelsRequestBody convertSearchRoomsRequest(SearchRoomsRequest searchRoomsRequest, CommonModifierResponse commonModifierResponse) {
		PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
		long startTime = System.currentTimeMillis();
		try {
			super.buildDeviceDetails(priceByHotelsRequestBody, searchRoomsRequest.getDeviceDetails());
			buildSearchCriteria(priceByHotelsRequestBody, searchRoomsRequest.getSearchCriteria(), commonModifierResponse);
			buildRequestDetails(priceByHotelsRequestBody, searchRoomsRequest.getRequestDetails(),commonModifierResponse);

			priceByHotelsRequestBody.setResponseFilterFlags(super.buildResponseFilterFlags(priceByHotelsRequestBody, searchRoomsRequest, commonModifierResponse));
			priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
			priceByHotelsRequestBody.setMobile(commonModifierResponse.getMobile());
			priceByHotelsRequestBody.setExperimentData(searchRoomsRequest.getExpData());
			priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
			priceByHotelsRequestBody.setMtKey(searchRoomsRequest.getSearchCriteria().getMtKey());
			priceByHotelsRequestBody.setAppliedFilterMap(buildAppliedFilterMap(searchRoomsRequest.getFilterCriteria()));
			setOtherDetails(priceByHotelsRequestBody);
			priceByHotelsRequestBody.setChannel(searchRoomsRequest.getRequestDetails().getChannel());
			priceByHotelsRequestBody.setCdfContextId(commonModifierResponse.getCdfContextId());
			priceByHotelsRequestBody.setAffiliateId(commonModifierResponse.getAffiliateId());
			priceByHotelsRequestBody.setCorrelationKey(searchRoomsRequest.getCorrelationKey());
			priceByHotelsRequestBody.setRequestIdentifier(utility.buildRequestIdentifier(searchRoomsRequest.getRequestDetails()));
			if (commonModifierResponse.getExtendedUser() != null) {
				priceByHotelsRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
				priceByHotelsRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
				priceByHotelsRequestBody.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
				priceByHotelsRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());

			}
			if (CollectionUtils.isNotEmpty(searchRoomsRequest.getSearchCriteria().getTravellerEmailID())) {
				List<TravelerDetail> travelerDetailsList = new ArrayList<>();
				for (String email : searchRoomsRequest.getSearchCriteria().getTravellerEmailID()) {
					TravelerDetail travelerDetail = new TravelerDetail();
					try {
						ScramblerClient sc = ScramblerClient.getInstance();
						String commEmail = sc.encode(email, HashType.F);
						travelerDetail.setEmailCommId(commEmail);
						travelerDetailsList.add(travelerDetail);
					} catch (ScramblerClientException e) {
						//Do nothing
					}
				}
				priceByHotelsRequestBody.setTravelerDetailsList(travelerDetailsList);
			}
			priceByHotelsRequestBody.setSiteDomain(searchRoomsRequest.getRequestDetails().getSiteDomain());
			priceByHotelsRequestBody.setZcpHash(searchRoomsRequest.getRequestDetails().getZcp());
			priceByHotelsRequestBody.setLoggedIn(searchRoomsRequest.getRequestDetails().isLoggedIn());
			if (commonModifierResponse.getHydraResponse() != null) {
				priceByHotelsRequestBody.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
				priceByHotelsRequestBody.setFlightBooker(commonModifierResponse.getHydraResponse().isFlightBooker());
			}
			priceByHotelsRequestBody.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
			priceByHotelsRequestBody.setBrand(Brand.GI.name());
			priceByHotelsRequestBody.setGiHotelId(searchRoomsRequest.getSearchCriteria().getGiHotelId());
			priceByHotelsRequestBody.setGiHotelIds(Collections.singletonList(searchRoomsRequest.getSearchCriteria().getGiHotelId()));
			priceByHotelsRequestBody.setVcId(searchRoomsRequest.getSearchCriteria().getVcId());
			priceByHotelsRequestBody.setUserLocation(commonModifierResponse.getUserLocation());
			priceByHotelsRequestBody.setRoomPreferenceEnabled(utility.checkIfFilterValueExistsInAppliedFilterMap(searchRoomsRequest.getFilterCriteria()));
			if (utility.isBusinessIdentifyEnableExperimentOn(commonModifierResponse.getExpDataMap())
					&& commonModifierResponse.getExtendedUser() != null) {
				priceByHotelsRequestBody.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
			}
			priceByHotelsRequestBody.setUserPreferredCouponCode(searchRoomsRequest.getRequestDetails().getUserPreferredCouponCode());
		}finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
		}
        
		return priceByHotelsRequestBody;
	}
	
	private Map<FilterGroup, Set<Filter>> buildAppliedFilterMap(List<com.mmt.hotels.clientgateway.request.Filter> filters){
		Map<FilterGroup,Set<Filter>> appliedFilterMap = null;
		if(CollectionUtils.isNotEmpty(filters)){
			appliedFilterMap = new HashMap<>();
			for(com.mmt.hotels.clientgateway.request.Filter filter : filters){
				if(filter.getFilterGroup() != null) {
					if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filter.getFilterGroup().name())){
						continue;
					}
				}
				FilterGroup filterGroup = FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name());
				Filter oldFilter = getOldFilterFromNewFilter(filter, filterGroup);
				if(appliedFilterMap.containsKey(filterGroup)){
					appliedFilterMap.get(filterGroup).add(oldFilter);
				}else{
					Set<Filter> filterSet = new HashSet<>();
					filterSet.add(oldFilter);
					appliedFilterMap.put(filterGroup,filterSet);
				}
			}
		}
		return appliedFilterMap;
	}

	private Filter getOldFilterFromNewFilter(com.mmt.hotels.clientgateway.request.Filter filter, FilterGroup filterGroup) {
		Filter oldFilter = new Filter();
		oldFilter.setFilterGroup(filterGroup);
		oldFilter.setFilterValue(filter.getFilterValue());
		oldFilter.setRangeFilter(filter.isRangeFilter());
		if(null != filter.getFilterRange()) {
			FilterRange filterRange = new FilterRange();
			filterRange.setMaxValue(filter.getFilterRange().getMaxValue());
			filterRange.setMinValue(filter.getFilterRange().getMinValue());
			oldFilter.setFilterRange(filterRange);
		}
		return oldFilter;
	}


	private void setOtherDetails(PriceByHotelsRequestBody priceByHotelsRequestBody) {
		priceByHotelsRequestBody.setChannel("B2Cweb");
		priceByHotelsRequestBody.setPageContext("DETAIL");
		priceByHotelsRequestBody.setRequestType("B2CAgent");
		priceByHotelsRequestBody.setDomain("B2C");

	}

	private void buildRequestDetails(PriceByHotelsRequestBody priceByHotelsRequestBody, RequestDetails requestDetails,CommonModifierResponse commonModifierResponse) {
		priceByHotelsRequestBody.setFunnelSource(requestDetails.getFunnelSource());
		priceByHotelsRequestBody.setIdContext(requestDetails.getIdContext());
		priceByHotelsRequestBody.setVisitorId(requestDetails.getVisitorId());
		priceByHotelsRequestBody.setVisitNumber(requestDetails.getVisitNumber() != null?
				String.valueOf(requestDetails.getVisitNumber()): "");

		priceByHotelsRequestBody.setPaymentChannel(requestDetails.getChannel());
		priceByHotelsRequestBody.setFirstTimeUserState(requestDetails.getFirstTimeUserState());
        if(null != requestDetails.getTrafficSource()) {
            priceByHotelsRequestBody.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource()));
        }
		priceByHotelsRequestBody.setCouponCount(requestDetails.getCouponCount() != null ? requestDetails.getCouponCount() : 0);
		priceByHotelsRequestBody.setSrCon(requestDetails.getSrCon());
        priceByHotelsRequestBody.setSrCty(requestDetails.getSrCty());
		if (commonModifierResponse.getUserLocation() != null) {
			priceByHotelsRequestBody.setSrCty(commonModifierResponse.getUserLocation().getCity());
			priceByHotelsRequestBody.setSrCon(commonModifierResponse.getUserLocation().getCountry());
			priceByHotelsRequestBody.setSrcState(commonModifierResponse.getUserLocation().getState());
		}
		priceByHotelsRequestBody.setCouponCount(requestDetails.getCouponCount() != null? requestDetails.getCouponCount(): 0);
		priceByHotelsRequestBody.setSeoCorp(requestDetails.isSeoCorp());
		priceByHotelsRequestBody.setExtendedPackageCall(requestDetails.isExtendedPackageCall());
		priceByHotelsRequestBody.setRequisitionID(requestDetails.getRequisitionID());
		priceByHotelsRequestBody.setMyBizFlowIdentifier(requestDetails.getMyBizFlowIdentifier());
		priceByHotelsRequestBody.setRequestIdentifier(utility.buildRequestIdentifier(requestDetails));

	}

    private com.mmt.hotels.model.request.TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {

        com.mmt.hotels.model.request.TrafficSource trafficSourceCB = new com.mmt.hotels.model.request.TrafficSource();
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        return trafficSourceCB;

    }
    
	private void buildSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody,
			SearchRoomsCriteria searchCriteria, CommonModifierResponse commonModifierResponse) {
		List<String> hotelIds = null;
		if (StringUtils.isNotEmpty(searchCriteria.getHotelId())) {
			hotelIds = new ArrayList<>();
			hotelIds.add(searchCriteria.getHotelId());
		}
		super.populateSearchCriteria(priceByHotelsRequestBody,searchCriteria,hotelIds, commonModifierResponse);
		priceByHotelsRequestBody.setRoomStayCandidates(buildRoomStayCandidates(searchCriteria.getRoomStayCandidates()));
	}


	private GuestRecommendationEnabledReqBody buildGuestRecommendationEnabled() {
		GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
		guestRecommendationEnabledReqBody.setMaxRecommendations("1");
		guestRecommendationEnabledReqBody.setText("true");
		return guestRecommendationEnabledReqBody;
	}
	
	private List<RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        
        if(roomStayCandidates==null)
            return null;
        
        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();
        
        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates){
        	RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }
        
        return roomStayCandidateList;
    }



    private List<GuestCount> buildGuestCounts(
			com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG) {
		List<GuestCount> guestCounts = new ArrayList<>();
		GuestCount guestCount = new GuestCount();
		guestCount.setAgeQualifyingCode("10");
		guestCount.setAges(roomStayCandidateCG.getChildAges());
		guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
		guestCounts.add(guestCount);
		return guestCounts;
	}
}
