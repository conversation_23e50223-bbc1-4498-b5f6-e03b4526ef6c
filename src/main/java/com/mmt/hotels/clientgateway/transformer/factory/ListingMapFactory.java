package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.ListingMapRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.ListingMapRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.ListingMapRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.ListingMapRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.ListingMapRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.ListingMapResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.ListingMapResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.ListingMapResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.ListingMapResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ListingMapResponseTransformerPWA;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ListingMapFactory {

    @Autowired
    private ListingMapRequestTransformerPWA listingMapRequestTransformerPWA;

    @Autowired
    private ListingMapResponseTransformerPWA listingMapResponseTransformerPWA;
    
    @Autowired
    private ListingMapRequestTransformerAndroid listingMapRequestTransformerAndroid;

    @Autowired
    private ListingMapResponseTransformerAndroid listingMapResponseTransformerAndroid;
    
    @Autowired
    private ListingMapRequestTransformerDesktop listingMapRequestTransformerDesktop;

    @Autowired
    private ListingMapResponseTransformerDesktop listingMapResponseTransformerDesktop;
    
    @Autowired
    private ListingMapRequestTransformerIOS listingMapRequestTransformerIOS;

    @Autowired
    private ListingMapResponseTransformerIOS listingMapResponseTransformerIOS;

    public ListingMapRequestTransformer getRequestService(String client) {
    	if (StringUtils.isEmpty(client))
			return listingMapRequestTransformerDesktop;
    	
        switch(client) {
            case "PWA":
            case "MSITE":
                return listingMapRequestTransformerPWA;
            case "DESKTOP": return listingMapRequestTransformerDesktop;
            case "ANDROID": return listingMapRequestTransformerAndroid;
            case "IOS": return listingMapRequestTransformerIOS;
        }
        return listingMapRequestTransformerDesktop;
    }

    public ListingMapResponseTransformer getResponseService(String client) {
    	if (StringUtils.isEmpty(client))
			return listingMapResponseTransformerDesktop;
    	
        switch(client){
            case "PWA":
            case "MSITE":
                return listingMapResponseTransformerPWA;
            case "DESKTOP": return listingMapResponseTransformerDesktop;
            case "ANDROID": return listingMapResponseTransformerAndroid;
            case "IOS": return listingMapResponseTransformerIOS;
        }
        return listingMapResponseTransformerDesktop;
    }


}
