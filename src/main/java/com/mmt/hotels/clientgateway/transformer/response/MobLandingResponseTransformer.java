package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.MobLandingRequest;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.staticdata.BbLatLong;
import com.mmt.hotels.pojo.landing.metadata.HotelsMetadata;
import com.mmt.hotels.pojo.matchmaker.SavedLocations;
import com.mmt.hotels.pojo.matchmaker.WikiQuestion;
import com.mmt.hotels.pojo.matchmaker.WikiResponse;
import com.mmt.hotels.pojo.response.landing.HotelLandingWrapperResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

public abstract class MobLandingResponseTransformer  {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    private static final Logger logger = LoggerFactory.getLogger(MobLandingResponseTransformer.class);
    public MobLandingResponse convertMobLandingResponse(HotelLandingWrapperResponse hotelLandingWrapperResponse, String client, LinkedHashMap<String,String> expDataMap, Object pageMakerOfferResponse, Object smartEngageResponse, Object pageMakerRenderedTemplateResponse, CommonModifierResponse commonModifierResponse, MobLandingRequest mobLandingRequest){

        MobLandingResponse mobLandingResponse = new MobLandingResponse();
        mobLandingResponse.setCompletedRequests(buildCompletedRequests(hotelLandingWrapperResponse.getCompletedRequests()));
        mobLandingResponse.setCurrentTimeStamp(hotelLandingWrapperResponse.getCurrentTimeStamp());
        mobLandingResponse.setUuids(buildUuids(hotelLandingWrapperResponse.getUuids()));
        mobLandingResponse.setMatchMakerResponse(buildMatchMakerResponse(hotelLandingWrapperResponse.getMatchmakerResponse()));
        mobLandingResponse.setMetaData(buildMetaData(hotelLandingWrapperResponse.getMetadata()));
        mobLandingResponse.setListPersonalizationResponse(commonResponseTransformer.buildListPersonalizationResponse(
                hotelLandingWrapperResponse.getListPersonalizationResponse(), client, expDataMap, mobLandingRequest.getRequestDetails().getIdContext(), mobLandingRequest.getRequestDetails().getFunnelSource(), commonModifierResponse));
        mobLandingResponse.setSmartEngageResponse(smartEngageResponse);
        mobLandingResponse.setPagemakerResponse(buildpageMakerResponse(pageMakerOfferResponse,pageMakerRenderedTemplateResponse));
        buildLocationPersuasion(mobLandingResponse);
        if(commonModifierResponse != null) {
            mobLandingResponse.setExpData(commonModifierResponse.getExpDataMap());
            mobLandingResponse.setVariantKey(commonModifierResponse.getVariantKey());
        }
        mobLandingResponse.setPreAppliedFilters(mobLandingRequest.getFilterCriteria());
        return mobLandingResponse;
    }

    public PagemakerResponse buildpageMakerResponse(Object pageMakerOfferResponse, Object pageMakerRenderedTemplateResponse){
        PagemakerResponse pagemakerResponse = new PagemakerResponse();
        pagemakerResponse.setOffersResponse(pageMakerOfferResponse);
        pagemakerResponse.setRenderedTemplateResponse(pageMakerRenderedTemplateResponse);
        return pagemakerResponse;
    }

    public void buildLocationPersuasion(MobLandingResponse mobLandingResponse) {
        if (mobLandingResponse != null && mobLandingResponse.getListPersonalizationResponse() != null) {
            if (CollectionUtils.isNotEmpty(mobLandingResponse.getListPersonalizationResponse().getCardData())) {
                Optional<CardData> valueStaysCard = mobLandingResponse.getListPersonalizationResponse().getCardData().stream()
                        .filter(cardData -> cardData != null && cardData.getCardInfo() != null && MMT_VALUESTAYS_SUBTYPE.equalsIgnoreCase(cardData.getCardInfo().getSubType()))
                        .findFirst();
                valueStaysCard.ifPresent(cardData -> {
                    if (cardData.getCardInfo() != null && cardData.getCardInfo().getCardPayload() != null
                            && CollectionUtils.isNotEmpty(cardData.getCardInfo().getCardPayload().getHotelList())) {
                        cardData.getCardInfo().getCardPayload().getHotelList().forEach(
                                this::addLocationPersuasionToHotelPersuasions
                        );
                    }
                });
            }
        }
    }

    /**
     * New method to build Hidden Gem and HomeStay Persuasions for Mob-Landing listing card.
     * If Hidden Gem Card is present in the Mob landing response, this will build all the required persuasions.
     */
    public void buildHiddenGemPersuasion(MobLandingResponse mobLandingResponse) {
        if (mobLandingResponse != null && mobLandingResponse.getListPersonalizationResponse() != null && CollectionUtils.isNotEmpty(mobLandingResponse.getListPersonalizationResponse().getCardData())) {
            Optional<CardData> hiddenGemCard = mobLandingResponse.getListPersonalizationResponse().getCardData().stream()
                    .filter(cardData -> cardData != null && cardData.getCardInfo() != null && Constants.HIDDEN_GEM_CARD.equalsIgnoreCase(cardData.getCardInfo().getSubType()))
                    .findFirst();

            hiddenGemCard.ifPresent(cardData -> {
                if (cardData.getCardInfo() != null && cardData.getCardInfo().getCardPayload() != null && CollectionUtils.isNotEmpty(cardData.getCardInfo().getCardPayload().getHotelList())) {
                    cardData.getCardInfo().getCardPayload().getHotelList().forEach(this::addPersuasionsForHiddenGemCard);
                }
            });
        }
    }

    public abstract void addLocationPersuasionToHotelPersuasions(SearchWrapperHotelEntity hotelEntity);

    public abstract void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity);

    private MetaData buildMetaData(HotelsMetadata metadata) {

        if (metadata == null) {
            return null;
        }

        MetaData metaDataCG = new MetaData();
        Map<String, Object> metaObject = (metadata.getAdditionalProperties());

        String bBoxLocationDetailsString =null;
        try {
            bBoxLocationDetailsString = objectMapperUtil.getJsonFromObject(metaObject.get("bBoxLocationDetails"), DependencyLayer.CLIENTBACKEND);
        } catch (JsonParseException e1) {
            logger.error("error occured in serializing bBoxLocationDetails");
        }
        BBoxLocationDetails bBoxLocationDetails = null;
        try {
            bBoxLocationDetails = objectMapperUtil.getObjectFromJson(bBoxLocationDetailsString, BBoxLocationDetails.class, DependencyLayer.CLIENTGATEWAY);
        } catch (JsonParseException e) {
            logger.error("error occured in de-serializing bBoxLocationDetailsString");
        }

        List<PropertyType> propertyTypeCGList = (List<PropertyType>) metaObject.get("propertytypes");
        String lng = (String) metaObject.get("lng");
        List<HotelCategory> hotelcategories = (List<HotelCategory>) metaObject.get("hotelcategories");
        String lat = (String) metaObject.get("lat");

        metaDataCG.setHotelcategories(hotelcategories);
        metaDataCG.setBBoxLocationDetails(bBoxLocationDetails);
        metaDataCG.setLat(lat);
        metaDataCG.setLng(lng);
        metaDataCG.setPropertyTypes(propertyTypeCGList);
        return metaDataCG;
    }

    private MatchMakerResponseCG buildMatchMakerResponse(WikiResponse matchmakerResponse) {
        if (matchmakerResponse == null) {
            return null;
        }
        MatchMakerResponseCG matchMakerResponseCG = new MatchMakerResponseCG();
        matchMakerResponseCG.setName(matchmakerResponse.getName());
        matchMakerResponseCG.setCityCode(matchmakerResponse.getCityCode());
        matchMakerResponseCG.setCityImageUrl(matchmakerResponse.getCityImageUrl());
        matchMakerResponseCG.setCountryCode(matchmakerResponse.getCountryCode());
        matchMakerResponseCG.setDefaultSearchText(matchmakerResponse.getDefaultSearchText());
        matchMakerResponseCG.setErrorString(matchmakerResponse.getErrorString());
        matchMakerResponseCG.setPreferIcon(matchmakerResponse.getPreferIcon());
        matchMakerResponseCG.setPreferText(matchmakerResponse.getPreferText());
        matchMakerResponseCG.setQuestions(buildQuestions(matchmakerResponse.getQuestions()));
        matchMakerResponseCG.setResponseError(buildResponseError(matchmakerResponse.getResponseError()));
        matchMakerResponseCG.setSavedLocations(buildSavedLocations(matchmakerResponse.getSavedLocations()));
        return matchMakerResponseCG;
    }

    private List<SavedLocation> buildSavedLocations(List<SavedLocations> savedLocations) {
        if (savedLocations == null) {
            return null;
        }
        List<SavedLocation> savedLocationList = new ArrayList<>();
        for (SavedLocations savedLocationsCB : savedLocations){
            SavedLocation savedLocation = new SavedLocation();
            savedLocation.setDesc(savedLocationsCB.getDesc());
            savedLocation.setLatitude(savedLocationsCB.getLatitude());
            savedLocation.setLongitude(savedLocationsCB.getLongitude());
            savedLocation.setPlaceId(savedLocationsCB.getPlaceId());
            savedLocation.setType(savedLocationsCB.getType());
            savedLocationList.add(savedLocation);
        }
        return savedLocationList;
    }

    private com.mmt.hotels.clientgateway.response.moblanding.ErrorResponse buildResponseError(com.mmt.hotels.pojo.response.ErrorResponse errorResponse) {
        if (errorResponse == null) {
            return null;
        }
        com.mmt.hotels.clientgateway.response.moblanding.ErrorResponse errorResponseCG = new com.mmt.hotels.clientgateway.response.moblanding.ErrorResponse();
        errorResponseCG.setErrorList(buildError(errorResponse.getErrorList()));
        return errorResponseCG;
    }

    private List<GenericErrorEntity> buildError(List<com.mmt.hotels.pojo.response.GenericErrorEntity> errorList) {
        if (CollectionUtils.isEmpty(errorList)) {
            return null;
        }
        List<GenericErrorEntity> genericErrorEntityList = new ArrayList<>();
        for (com.mmt.hotels.pojo.response.GenericErrorEntity genericErrorEntityCB : errorList){
            GenericErrorEntity genericErrorEntityCG = new GenericErrorEntity();
            genericErrorEntityCG.setErrorAdditionalInfo(genericErrorEntityCB.getErrorAdditionalInfo());
            genericErrorEntityCG.setErrorCode(genericErrorEntityCB.getErrorCode());
            genericErrorEntityCG.setErrorMessage(genericErrorEntityCB.getErrorMessage());
            genericErrorEntityList.add(genericErrorEntityCG);
        }
        return genericErrorEntityList;
    }

    private List<QuestionCG> buildQuestions(List<WikiQuestion> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return null;
        }
        List<QuestionCG> questionCGList = new ArrayList<>();
        for (WikiQuestion wikiQuestionCB : questions){
            QuestionCG questionCG = new QuestionCG();
            questionCG.setCategory(buildCategory(wikiQuestionCB.getCategory()));
            questionCG.setDesc(wikiQuestionCB.getDesc());
            questionCG.setId(wikiQuestionCB.getId());
            questionCG.setName(wikiQuestionCB.getName());
            questionCG.setOrder(wikiQuestionCB.getOrder());
            questionCG.setType(wikiQuestionCB.getType());
            questionCG.setBbox(buildBBox(wikiQuestionCB.getBbox()));
            questionCGList.add(questionCG);
        }
        return questionCGList;
    }

    private List<WikiQuestionCategory> buildCategory(List<com.mmt.hotels.pojo.matchmaker.WikiQuestionCategory> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<WikiQuestionCategory> wikiQuestionCategoryListCG = new ArrayList<>();
        for (com.mmt.hotels.pojo.matchmaker.WikiQuestionCategory wikiQuestionCategoryCB : list){
            WikiQuestionCategory wikiQuestionCategory = new WikiQuestionCategory();
            wikiQuestionCategory.setDesc(wikiQuestionCategoryCB.getDesc());
            wikiQuestionCategory.setDisplayType(wikiQuestionCategoryCB.getDisplayType());
            wikiQuestionCategory.setId(wikiQuestionCategoryCB.getId());
            wikiQuestionCategory.setGroupid(wikiQuestionCategoryCB.getGroupid());
            wikiQuestionCategory.setOrder(wikiQuestionCategoryCB.getOrder());
            wikiQuestionCategory.setType(wikiQuestionCategoryCB.getType());
            wikiQuestionCategory.setTags(buildTags(wikiQuestionCategoryCB.getTags()));
            wikiQuestionCategory.setCity(wikiQuestionCategoryCB.getIsCity());
            wikiQuestionCategory.setIsPivot(wikiQuestionCategoryCB.getPivot());
            wikiQuestionCategoryListCG.add(wikiQuestionCategory);
        }
        return wikiQuestionCategoryListCG;
    }

    private String buildDisplayType(String type) {
        if (POI.equalsIgnoreCase(type)) {
            return PLACE;
        } else {
            return type;
        }
    }

    private List<com.mmt.hotels.clientgateway.response.moblanding.MatchmakerTag> buildTags(List<com.mmt.hotels.pojo.matchmaker.MatchmakerTag> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<com.mmt.hotels.clientgateway.response.moblanding.MatchmakerTag> matchmakerTagCGList = new ArrayList<>();
        for (com.mmt.hotels.pojo.matchmaker.MatchmakerTag matchmakerTagCB : list){
            com.mmt.hotels.clientgateway.response.moblanding.MatchmakerTag matchmakerTagCG = new com.mmt.hotels.clientgateway.response.moblanding.MatchmakerTag();
            matchmakerTagCG.setAltImgUrl(matchmakerTagCB.getAltImgUrl());
            matchmakerTagCG.setAreaId(matchmakerTagCB.getAreaId());
            matchmakerTagCG.setAreaIdStr(matchmakerTagCB.getAreaIdStr());
            matchmakerTagCG.setCityName(matchmakerTagCB.getCityName());
            matchmakerTagCG.setDesc(matchmakerTagCB.getDesc());
            matchmakerTagCG.setLocId(matchmakerTagCB.getLocId());
            matchmakerTagCG.setLabel(matchmakerTagCB.getLabel());
            matchmakerTagCG.setId(matchmakerTagCB.getId());
            matchmakerTagCG.setLocType(matchmakerTagCB.getLocType());
            matchmakerTagCG.setBbox(buildBBox(matchmakerTagCB.getBbox()));
            matchmakerTagCG.setTagType(matchmakerTagCB.getTagType());
            matchmakerTagCG.setPriority(matchmakerTagCB.getPriority());
            matchmakerTagCG.setMatchMakerTagLatLngObject(buildMatchMakerLatLngObject(matchmakerTagCB.getMatchMakerTagLatLngObject()));
            matchmakerTagCG.setShowableEntities(matchmakerTagCB.getShowableEntities());
            matchmakerTagCG.setTypeId(matchmakerTagCB.getTypeId());
            matchmakerTagCG.setWiki(buildWiki(matchmakerTagCB.getWiki()));
            matchmakerTagCG.setType(matchmakerTagCB.getType());
            matchmakerTagCG.setDisplayType(buildDisplayType(matchmakerTagCB.getType()));
            matchmakerTagCG.setPoi(matchmakerTagCB.isPoi());
            matchmakerTagCG.setCity(matchmakerTagCB.isCity());
            matchmakerTagCG.setPropCount(matchmakerTagCB.getPropCount());
            matchmakerTagCG.setQuestions(buildQuestions(matchmakerTagCB.getQuestions()));
            matchmakerTagCG.setDistanceText(matchmakerTagCB.getDistanceText());
            matchmakerTagCG.setCityTagline(matchmakerTagCB.getCityTagline());
            matchmakerTagCG.setIsPivot(matchmakerTagCB.getIsPivot());
            matchmakerTagCG.setPoiCategory(matchmakerTagCB.getPoiCategory());
            matchmakerTagCGList.add(matchmakerTagCG);
        }
        return matchmakerTagCGList;
    }

    private LatLong buildBBox(BbLatLong bBox){
        LatLong bBoxCG = null;
        if (bBox == null || (bBox.getNe()==null && bBox.getSw()==null) ) {
            bBoxCG = null;
        } else {
            bBoxCG = new LatLong();
            if (bBox.getNe()!=null) {
                LatLongObject NELatLongObject = new LatLongObject();
                NELatLongObject.setLat(bBox.getNe().getLat());
                NELatLongObject.setLng(bBox.getNe().getLng());
                bBoxCG.setNe(NELatLongObject);
            }
            if (bBox.getSw()!=null) {
                LatLongObject SWLatLongObject = new LatLongObject();
                SWLatLongObject.setLat(bBox.getSw().getLat());
                SWLatLongObject.setLng(bBox.getSw().getLng());
                bBoxCG.setSw(SWLatLongObject);
            }
        }
        return bBoxCG;
    }

    private WikiDetail buildWiki(com.mmt.hotels.pojo.matchmaker.WikiDetail wiki) {
        if (wiki == null) {
            return null;
        }
        WikiDetail wikiDetail = new WikiDetail();
        wikiDetail.setFirstPara(wiki.getFirstPara());
        wikiDetail.setId(wiki.getId());
        wikiDetail.setImgUrl(wiki.getImgUrl());
        wikiDetail.setPoiTags(buildPoiTags(wiki.getPoiTags()));
        wikiDetail.setReasonToStays(buildReasonToStays(wiki.getReasonToStays()));
        wikiDetail.setSecondPara(wiki.getSecondPara());
        wikiDetail.setTags(wiki.getTags());
        wikiDetail.setTransitTags(buildTransitTags(wiki.getTransitTags()));
        wikiDetail.setUrl(wiki.getUrl());
        wikiDetail.setWikiMetaInfo(buildWikiMeta(wiki.getWikiMetaInfo()));
        return wikiDetail;
    }

    private WikiMetaInfo buildWikiMeta(com.mmt.hotels.pojo.matchmaker.WikiMetaInfo wikiMetaInfo) {
        if (wikiMetaInfo == null) {
            return null;
        }
        WikiMetaInfo wikiMetaInfoCG = new WikiMetaInfo();
        wikiMetaInfoCG.setBestForText(wikiMetaInfo.getBestForText());
        wikiMetaInfoCG.setBudget(wikiMetaInfo.getBudget());
        wikiMetaInfoCG.setCaution(buildCaution(wikiMetaInfo.getCaution()));
        wikiMetaInfoCG.setPersuasionText(wikiMetaInfo.getPersuasionText());
        wikiMetaInfoCG.setRelatedTags(wikiMetaInfo.getRelatedTags());
        return wikiMetaInfoCG;
    }

    private WikiInfo buildCaution(com.mmt.hotels.pojo.matchmaker.WikiInfo caution) {
        if (caution == null) {
            return null;
        }
        WikiInfo wikiInfo = new WikiInfo();
        wikiInfo.setId(caution.getId());
        wikiInfo.setInfo(caution.getInfo());
        wikiInfo.setHeader(caution.getHeader());
        return wikiInfo;
    }

    private List<WikiTransitTag> buildTransitTags(List<com.mmt.hotels.pojo.matchmaker.WikiTransitTag> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<WikiTransitTag> wikiTransitTagList = new ArrayList<>();
        for (com.mmt.hotels.pojo.matchmaker.WikiTransitTag wikiTransitTagCB : list){
            WikiTransitTag wikiTransitTagCG = new WikiTransitTag();
            wikiTransitTagCG.setId(wikiTransitTagCB.getId());
            wikiTransitTagCG.setName(wikiTransitTagCB.getName());
            wikiTransitTagCG.setOrder(wikiTransitTagCB.getOrder());
            wikiTransitTagCG.setTime(wikiTransitTagCB.getTime());
            wikiTransitTagList.add(wikiTransitTagCG);
        }
        return wikiTransitTagList;
    }

    private List<WikiInfo> buildReasonToStays(List<com.mmt.hotels.pojo.matchmaker.WikiInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<WikiInfo> wikiInfoList = new ArrayList<>();
        for (com.mmt.hotels.pojo.matchmaker.WikiInfo wikiInfoCB : list){
            WikiInfo wikiInfoCG = new WikiInfo();
            wikiInfoCG.setHeader(wikiInfoCB.getHeader());
            wikiInfoCG.setInfo(wikiInfoCB.getInfo());
            wikiInfoCG.setId(wikiInfoCB.getId());
            wikiInfoList.add(wikiInfoCG);
        }
        return wikiInfoList;
    }

    private List<WikiPoi> buildPoiTags(List<com.mmt.hotels.pojo.matchmaker.WikiPoi> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<WikiPoi> wikiPoiList = new ArrayList<>();
        for (com.mmt.hotels.pojo.matchmaker.WikiPoi wikiPoiCB : list){
            WikiPoi wikiPoiCG = new WikiPoi();
            wikiPoiCG.setId(wikiPoiCB.getId());
            wikiPoiCG.setImages(wikiPoiCB.getImages());
            wikiPoiCG.setInfo(wikiPoiCB.getInfo());
            wikiPoiCG.setLat(wikiPoiCB.getLat());
            wikiPoiCG.setLng(wikiPoiCB.getLng());
            wikiPoiCG.setName(wikiPoiCB.getName());
            wikiPoiCG.setOrder(wikiPoiCB.getOrder());
            wikiPoiList.add(wikiPoiCG);
        }
        return wikiPoiList;
    }

    private com.mmt.hotels.clientgateway.response.moblanding.LatLongAndBounds buildMatchMakerLatLngObject(com.mmt.hotels.pojo.matchmaker.LatLongAndBounds matchMakerTagLatLngObject) {
        if (matchMakerTagLatLngObject == null) {
            return null;
        }
        com.mmt.hotels.clientgateway.response.moblanding.LatLongAndBounds latLongAndBounds = new com.mmt.hotels.clientgateway.response.moblanding.LatLongAndBounds();
        latLongAndBounds.setLat(matchMakerTagLatLngObject.getLat());
        latLongAndBounds.setLng(matchMakerTagLatLngObject.getLng());
        latLongAndBounds.setNELat(matchMakerTagLatLngObject.getNELat());
        latLongAndBounds.setNELng(matchMakerTagLatLngObject.getNELng());
        latLongAndBounds.setSWLat(matchMakerTagLatLngObject.getSWLat());
        latLongAndBounds.setSWLng(matchMakerTagLatLngObject.getSWLng());
        return latLongAndBounds;
    }

    private List<String> buildUuids(Set<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return null;
        }
        List<String> uuidList = new ArrayList<>();
        for (String uuid: uuids){
            uuidList.add(uuid);
        }
        return uuidList;
    }

    private List<String> buildCompletedRequests(Set<String> completedRequests) {
        if (CollectionUtils.isEmpty(completedRequests)) {
            return null;
        }
        List<String> requestsList = new ArrayList<>();
        for (String request : completedRequests) {
            requestsList.add(request);
        }
        return requestsList;
    }
}
