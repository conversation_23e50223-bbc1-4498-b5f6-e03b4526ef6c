package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.transformer.response.InitiateApprovalResponseTransformer;
import org.springframework.stereotype.Component;

@Component
public class InitiateApprovalResponseTransformerDesktop extends InitiateApprovalResponseTransformer {

    @Override
    public InitApprovalResponse processResponse(CGServerResponse initAppResponseHES) {
        return super.processResponse(initAppResponseHES);
    }
}
