package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.gi.hotels.model.response.staticdata.HotelMedia;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.Address;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.ReviewSummary;
import com.mmt.hotels.clientgateway.response.flyfish.AltAccoPer;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.ChildExtraBedPolicy;
import com.mmt.hotels.clientgateway.response.staticdetail.CommonRules;
import com.mmt.hotels.clientgateway.response.staticdetail.ExtraBedRules;
import com.mmt.hotels.clientgateway.response.staticdetail.GovtPolicies;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRules;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRulesV2;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.PolicyRules;
import com.mmt.hotels.clientgateway.response.staticdetail.Rule;
import com.mmt.hotels.clientgateway.response.staticdetail.*;
import com.mmt.hotels.clientgateway.response.staticdetail.StreetViewInfo;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.model.response.flyfish.*;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.pojo.FoodAndDining.FoodAndDiningEnums;
import com.mmt.hotels.pojo.request.detail.mob.CBFlyFishSummaryResponse;
import com.mmt.hotels.pojo.request.detail.mob.CBPlatformSummaryResponse;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import com.mmt.model.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.IMAGES_EXP_ENABLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;
import static java.lang.Math.min;

public abstract class StaticDetailResponseTransformer {

	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	protected CommonResponseTransformer commonResponseTransformer;

	@Value("${value.stay.icon}")
	private String iconUrl;

	@Value("${value.stay.icon.gcc}")
	private String iconUrlGcc;

	@Value("${food.dining.min.count.config}")
	private int foodDiningMinCountConfig;

	@Value("${food.menu.position.config}")
	private int foodMenuPosition;

	@Autowired
	private PolyglotService polyglotService;

	@Autowired
	private Utility utility;

	@Autowired
	private SearchHotelsFactory searchHotelsFactory;

	@Autowired
	private MetricAspect metricAspect;

	@Value("#{'${suppressed.houseRules.list}'.split(',')}")
	private List<Integer> supressedHouseRulesList;

	@Value("#{'${traveller.image_order}'.split(',')}")
	private List<String> travellerImageOrder;

	public StaticDetailResponse convertStaticDetailResponse(HotelDetailWrapperResponse hotelDetailWrapperResponse, String client, StaticDetailRequest staticDetailRequest, CommonModifierResponse commonModifierResponse) {
		StaticDetailResponse staticDetailResponse = new StaticDetailResponse();
		boolean isInternational = utility.isInternationalProperty(hotelDetailWrapperResponse.getHotelResult(), staticDetailRequest.getSearchCriteria());
		Map<String, String> expDataMap = utility.getExpDataMap(staticDetailRequest.getExpData());
		staticDetailRequest.setExpDataMap(expDataMap);
		staticDetailResponse.setHotelDetails(
				getHotelResult(hotelDetailWrapperResponse.getHotelResult(), hotelDetailWrapperResponse.getContext(),
						expDataMap, isCorp(staticDetailRequest),
						utility.isGroupBookingFunnel(staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : null),
						staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : null,
						isInternational,staticDetailRequest.getSearchCriteria(), staticDetailRequest.getDeviceDetails()));
		staticDetailResponse.setReviewSummary(getReviewSummary(hotelDetailWrapperResponse.getFlyfishSummaryResponse(), client, staticDetailRequest.getDeviceDetails(), staticDetailRequest.getSearchCriteria() != null ? staticDetailRequest.getSearchCriteria().getCountryCode() : null, commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null));
		if(MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(UGCV2) && UGCV2_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(UGCV2))) {
			staticDetailResponse.setUgcSummary(buildUgcSummary(hotelDetailWrapperResponse.getPlatformSummaryResponse(), commonModifierResponse, staticDetailRequest.getSearchCriteria().getCountryCode()));
			if(null!=hotelDetailWrapperResponse.getHotelResult() && null!=hotelDetailWrapperResponse.getHotelResult().getGiStaticData()) {
				staticDetailResponse.setAmenitiesGI(hotelDetailWrapperResponse.getHotelResult().getGiStaticData().getAmenities());
			}
			if(expDataMap.containsKey(amenitiesGiV2) && TRUE.equalsIgnoreCase(expDataMap.get(amenitiesGiV2)) && hotelDetailWrapperResponse.getHotelResult()!=null
					&& hotelDetailWrapperResponse.getHotelResult().getAmenities()!=null) {
				//categorizedV2 node will contain the amenities data for GI
				utility.updateAmenitiesGI(staticDetailResponse, hotelDetailWrapperResponse.getHotelResult().getAmenities());
			}

		}
		else {
			staticDetailResponse.setReviewSummaryGI(hotelDetailWrapperResponse.getReviewSummaryGI() != null ? hotelDetailWrapperResponse.getReviewSummaryGI().getGiSummary() : null);
		}
		if(null!=hotelDetailWrapperResponse.getHotelCompareResponse()) {
			staticDetailResponse.setHotelCompareResponse(buildComparatorResponse(hotelDetailWrapperResponse.getHotelCompareResponse(), null != staticDetailRequest.getRequiredApis() ? staticDetailRequest.getRequiredApis().isComparatorV2Required() : false, client, staticDetailRequest.getExpData()));
		}
		staticDetailResponse.setBhfPersuasions(commonResponseTransformer.getBhfPersuasions(hotelDetailWrapperResponse.getBhfPersuasions()));

		staticDetailResponse.setPersuasionDetail(hotelDetailWrapperResponse.getPersuasionDetail());
		staticDetailResponse.setComparatorResponse(hotelDetailWrapperResponse.getComparatorResponse());
		staticDetailResponse.setWeaverResponse(hotelDetailWrapperResponse.getWeaverResponse());
		staticDetailResponse.setDetailPersuasionCards(commonResponseTransformer.buildListPersonalizationResponse(hotelDetailWrapperResponse.getDetailPersuasionCards(), client,new LinkedHashMap<>(), "", "", commonModifierResponse));
		staticDetailResponse.setRoomInfoMap(buildRoomInfoMap(hotelDetailWrapperResponse));
		staticDetailResponse.setUuids(hotelDetailWrapperResponse.getUuids());
		staticDetailResponse.setCompletedRequests(hotelDetailWrapperResponse.getCompletedRequests());

		// GIHTL-16776: Show Same Loc Card as MMT on GI
		String locationCardV2 = Utility.getExperimentValue(commonModifierResponse, Constants.LOCATION_CARD_V2);

		if (TRUE.equalsIgnoreCase(locationCardV2)) {
			staticDetailResponse.setPlacesResponse(hotelDetailWrapperResponse.getPlacesResponse());
			staticDetailResponse.setPlacesResponseV2(utility.mapToPlacesResponseCG(hotelDetailWrapperResponse.getPlacesResponse()));
		} else {
			utility.updateStaticDetailResponseGI(staticDetailResponse, hotelDetailWrapperResponse);
		}

		boolean isLuxe = hotelDetailWrapperResponse.getHotelResult() != null && hotelDetailWrapperResponse.getHotelResult().getLuxe() != null ? hotelDetailWrapperResponse.getHotelResult().getLuxe() : false;
		boolean isImageExpEnable = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(IMAGES_EXP_ENABLE) && TRUE.equalsIgnoreCase(expDataMap.get(IMAGES_EXP_ENABLE));
		if(MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(MEDIAV2) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(MEDIAV2))){
			staticDetailResponse.setMediaV2(buildMediaV2(hotelDetailWrapperResponse.getHotelImage(), hotelDetailWrapperResponse.getHotelResult() != null ? hotelDetailWrapperResponse.getHotelResult().getListingType() : null, hotelDetailWrapperResponse.getHotelResult() != null ? hotelDetailWrapperResponse.getHotelResult().getProfessionalImageTagsOrder() : null,isLuxe,isImageExpEnable,client));
		}else{
			staticDetailResponse.setMedia(buildMediaImageGallery(hotelDetailWrapperResponse.getHotelImage(),hotelDetailWrapperResponse.getHotelResult()));
		}
		if(commonModifierResponse != null) {
			staticDetailResponse.setExpData(commonModifierResponse.getExpDataMap());
			staticDetailResponse.setVariantKey(commonModifierResponse.getVariantKey());
		}
		return staticDetailResponse;
	}

	public MediaV2 buildMediaV2(HotelImage hotelImage, String listingType, LinkedHashSet<String> imageTagsOrder,boolean isLuxe, boolean isImageExpEnable,String client) {
		MediaV2 mediaV2 = null;

		if (hotelImage != null && hotelImage.getImageDetails() != null ) {

			/* Traveller images building START */
			if (MapUtils.isNotEmpty(hotelImage.getImageDetails().getTraveller())) {

				Map<String, List<TravellerImageEntity>> travelerImageMapV2 = hotelImage.getImageDetails().getTraveller();
				List<TravellerImageEntity> travelerList = travelerImageMapV2.get("H");

				// Getting the space Name to Image Entity Map
				Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap = spaceNameToImageEntityMap(travelerList);

				// Converting the Image Details Map to Client format
				List<Tag> tags = createTagsFromTravellerImages(spaceNameToImageEntityMap);
				// make constant  as LinkedHashSet list

				LinkedHashSet<String> imageTagsOrderSet = new LinkedHashSet<>(travellerImageOrder);


				//sorting traveller tags based upon ImageTags order from hotstore
				if(CollectionUtils.isNotEmpty(imageTagsOrderSet) && CollectionUtils.isNotEmpty(tags)){
					sortTravellerTagsBasedImageTagsOrder(tags, new ArrayList<>(imageTagsOrderSet));
				}

				TravellerImages travellerImages = new TravellerImages();
				travellerImages.setTags(tags);
				mediaV2 = new MediaV2();
				mediaV2.setTraveller(travellerImages);

			}
			/* Traveller images building END */

			/* Professional images V2 building START */
			if(MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessionalV2())){
				Map<String, List<Tag>> professionalImageMapV2 = hotelImage.getImageDetails().getProfessionalV2();
				if(professionalImageMapV2.containsKey("H")){
					HotelImages hotelImages = new HotelImages();
					//  set the  name street view at top and rest as in order
					List<Tag> tags = professionalImageMapV2.get("H");
					if(CollectionUtils.isNotEmpty(tags)){
						List<Tag> sortedTags = tags.stream().sorted(Comparator.comparing(tag -> {
							if (tag.getName().equalsIgnoreCase("Street View")) {
								return -1;
							}
							return 1;
						})).collect(Collectors.toList());
						hotelImages.setTags(sortedTags);
					}
					if(mediaV2 == null){
						mediaV2 = new MediaV2();
					}
					mediaV2.setHotel(hotelImages);
				}
			}
			/* Professional images V2 building END */

			/* Grid images building START */
			int mediaLimit = isImageExpEnable && Utility.isAppRequest() ? listingMediaLimitExp : detailGridImageLimit;
			if(MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())) {
				List<MediaInfo> professionalMediaInfos = buildProfessionalImagesFromContentResponse(hotelImage, listingType);

				if (CollectionUtils.isNotEmpty(professionalMediaInfos)) {
					int numOfImages = min(mediaLimit, professionalMediaInfos.size());
					ProfessionalImages grid = new ProfessionalImages();
					grid.setImages(professionalMediaInfos.subList(0, numOfImages));
					if (mediaV2 == null) {
						mediaV2 = new MediaV2();
					}
					mediaV2.setGrid(grid);
				}
			}
			/* Grid images building END */

		}

		return mediaV2;
	}
	private Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap(List<TravellerImageEntity> travelerList){
		Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap = new HashMap<>();

		if (CollectionUtils.isNotEmpty(travelerList)) {
			for (TravellerImageEntity travellerImageEntity : travelerList) {
				if (spaceNameToImageEntityMap.containsKey(travellerImageEntity.getImageFilterInfo())) {
					spaceNameToImageEntityMap.get(travellerImageEntity.getImageFilterInfo()).add(travellerImageEntity);
				} else {
					spaceNameToImageEntityMap.put(travellerImageEntity.getImageFilterInfo(), new ArrayList<TravellerImageEntity>() {{
						add(travellerImageEntity);
					}});
				}
			}
		}
		return spaceNameToImageEntityMap;
	}
	private List<Tag> createTagsFromTravellerImages(Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap){
		List<Tag> tags = new ArrayList<>();
		for (Map.Entry<String, List<TravellerImageEntity>> entry : spaceNameToImageEntityMap.entrySet()){
			List<TravellerImageEntity> travelerList = entry.getValue();
			Tag tag = new Tag();
			tag.setName(entry.getKey());
			Subtag subtag = new Subtag();
			subtag.setName(entry.getKey());
			List<ImageData> data = new ArrayList<>();
			for (TravellerImageEntity travellerImageEntity : travelerList) {
				ImageData imageData = new ImageData();
				imageData.setDate(travellerImageEntity.getDate());
				imageData.setTravelerName(travellerImageEntity.getTravellerName());
				imageData.setMediaType(travellerImageEntity.getMediaType());
				imageData.setUrl(travellerImageEntity.getUrl());
				// write all setter methods that are present in ImageData class
				imageData.setRoomCode(travellerImageEntity.getRoomCode());
				imageData.setRoomName(travellerImageEntity.getRoomName());
				imageData.setTravelerRating(travellerImageEntity.getTravelerRating());
				imageData.setTravellerImage(travellerImageEntity.getTravellerImage());
				imageData.setUserReview(travellerImageEntity.getUserReview());
				imageData.setReviewCount(travellerImageEntity.getReviewCount());
				imageData.setPreviewUrl(travellerImageEntity.getPreviewUrl());
				imageData.setThumbnailURL(travellerImageEntity.getThumbnailURL());
				data.add(imageData);
			}
			subtag.setData(data);
			tag.setSubtags(new ArrayList<Subtag>(){{add(subtag);}});
			tags.add(tag);
		}
		return tags;
	}
	private List<MediaInfo> buildProfessionalImagesFromContentResponse(HotelImage hotelImage, String listingType){

		List<MediaInfo> professionalMediaInfos = new ArrayList<>();

		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())) {
			Map<String, List<ProfessionalImageEntity>> professional = hotelImage.getImageDetails().getProfessional();
			List<ProfessionalImageEntity> professionalList = professional.get("H");
			if (CollectionUtils.isNotEmpty(professionalList)) {
				for (ProfessionalImageEntity professionalImageEntity : professionalList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setFilterInfo(professionalImageEntity.getImageFilterInfo());
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTags(professionalImageEntity.getSeekTags());
					mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailURL());
					mediaInfo.setTitle(professionalImageEntity.getTitle());
					mediaInfo.setUrl(professionalImageEntity.getUrl());
					professionalMediaInfos.add(mediaInfo);
				}
			}
			professionalList = professional.get("R");
			//HTL-41751 Not Merging RoomImages in GalleryImages when listingType is Entire Property
			//as same set of images were coming twice, once in gallery images and second time in room images.
			if (CollectionUtils.isNotEmpty(professionalList) && !LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)) {
				for (ProfessionalImageEntity professionalImageEntity : professionalList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTags(professionalImageEntity.getSeekTags());
					String roomTag = polyglotService.getTranslatedData(STATIC_ROOM_TAG);
					if (mediaInfo.getTags() != null) {
						mediaInfo.getTags().add(roomTag);
					} else {
						List<String> tags = new ArrayList<String>();
						tags.add(roomTag);
						mediaInfo.setTags(tags);
					}
					mediaInfo.setFilterInfo(roomTag);
					mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailURL());
					mediaInfo.setTitle(professionalImageEntity.getTitle());
					mediaInfo.setUrl(professionalImageEntity.getUrl());
					mediaInfo.setRoomCode(professionalImageEntity.getCatCode());
					professionalMediaInfos.add(mediaInfo);
				}
			}
		}

		return professionalMediaInfos;
	}
	private void sortTravellerTagsBasedImageTagsOrder(List<Tag> tags, List<String> imageTagsOrderList){

		if(CollectionUtils.isEmpty(imageTagsOrderList) || CollectionUtils.isEmpty(tags)){
			return;
		}
		tags.sort(new Comparator<Tag>() {
			@Override
			public int compare(Tag tag1, Tag tag2) {

				//product requirement keep the "others" tag at the last
				if(OTHERS.equalsIgnoreCase(tag1.getName())) {
					return 1;
				} else if(OTHERS.equalsIgnoreCase(tag2.getName())){
					return -1;
				}

				int index1 = imageTagsOrderList.indexOf(tag1.getName());
				int index2 = imageTagsOrderList.indexOf(tag2.getName());

				if(index2 == -1)
					return -1;
				else if(index1 == -1)
					return 1;
				else
					return Integer.compare(index1,index2);
			}
		});
	}

	private  ComparatorResponse buildComparatorResponse(UpsellHotelDetailResponse hotelCompareResponse, boolean isComparatorV2Required, String client, String expData){
		ComparatorResponse comparatorResponse = new ComparatorResponse();
		comparatorResponse.setComparisonHeadings(hotelCompareResponse.getComparisonHeadings());
		comparatorResponse.setCtaMap(hotelCompareResponse.getCtaMap());
		comparatorResponse.setDeeplink(hotelCompareResponse.getDeeplink());
		comparatorResponse.setHotelDisplayMap(hotelCompareResponse.getHotelDisplayMap());
		comparatorResponse.setResponseErrors(hotelCompareResponse.getResponseErrors());
		comparatorResponse.setTitle(hotelCompareResponse.getTitle());
		if(isComparatorV2Required && null!=hotelCompareResponse.getHotelSearchResponse()){
			if(CollectionUtils.isNotEmpty(hotelCompareResponse.getHotelSearchResponse().getHotelList())) {
				comparatorResponse.setHotelList(searchHotelsFactory.getResponseService(client)
						.buildPersonalizedHotels(hotelCompareResponse.getHotelSearchResponse().getHotelList(), expData,
								null, null,null));
				if (null != hotelCompareResponse.getHotelSearchResponse().getAugurConfig()) {
					comparatorResponse.setRankingAlgo(getHotelsRankingAlgo(hotelCompareResponse.getHotelSearchResponse().getAugurConfig()));
				}
			}

		}else{
			comparatorResponse.setHotelSearchResponse(hotelCompareResponse.getHotelSearchResponse());
		}
		return comparatorResponse;
	}

	private String getHotelsRankingAlgo(JsonNode augurConfig) {
		List<String> rankingAlgo = new ArrayList<>();
		if (augurConfig.has(AUGUR_CONFIG_EXP) && StringUtils.isNotBlank(augurConfig.get(AUGUR_CONFIG_EXP).asText())) {
			rankingAlgo.add(augurConfig.get(AUGUR_CONFIG_EXP).asText());
		}
		if (augurConfig.has(AUGUR_CONFIG_OPTION) && StringUtils.isNotEmpty(augurConfig.get(AUGUR_CONFIG_OPTION).asText())) {
			rankingAlgo.add(augurConfig.get(AUGUR_CONFIG_OPTION).asText());
		}
		if (augurConfig.has(AUGUR_CONFIG_CONFIG) && StringUtils.isNotEmpty(augurConfig.get(AUGUR_CONFIG_CONFIG).asText())) {
			rankingAlgo.add(augurConfig.get(AUGUR_CONFIG_CONFIG).asText());
		}
		return CollectionUtils.isNotEmpty(rankingAlgo) ? StringUtils.join(rankingAlgo, PIPE_SYMBOL) : null;
	}

	/*Deprecated this method because not required in GI response.
	* use buildMedia functionality if required
	*/
	@Deprecated
	private Media buildMedia(HotelImage hotelImage, com.mmt.hotels.model.response.staticdata.HotelResult hotelResult) {
		List<MediaInfo> professionalMediaInfos = new ArrayList<>();
		List<MediaInfo> travelerMediaInfos = new ArrayList<>();
		HotelMedia imageGallery = null;
		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())) {
			Map<String, List<ProfessionalImageEntity>> professional = hotelImage.getImageDetails().getProfessional();
			List<ProfessionalImageEntity> professionalList = professional.get("H");
			if (CollectionUtils.isNotEmpty(professionalList)) {
				for (ProfessionalImageEntity professionalImageEntity : professionalList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setFilterInfo(professionalImageEntity.getImageFilterInfo());
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTags(professionalImageEntity.getSeekTags());
					mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailURL());
					mediaInfo.setTitle(professionalImageEntity.getTitle());
					mediaInfo.setUrl(professionalImageEntity.getUrl());
					professionalMediaInfos.add(mediaInfo);
				}
			}
			professionalList = professional.get("R");
			if (CollectionUtils.isNotEmpty(professionalList)) {
				for (ProfessionalImageEntity professionalImageEntity : professionalList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTags(professionalImageEntity.getSeekTags());
					String roomTag = polyglotService.getTranslatedData(STATIC_ROOM_TAG);
					if(mediaInfo.getTags() != null){
						mediaInfo.getTags().add(roomTag);
					}
					else{
						List<String> tags = new ArrayList<String>();
						tags.add(roomTag);
						mediaInfo.setTags(tags);
					}
					mediaInfo.setFilterInfo(roomTag);
					mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailURL());
					mediaInfo.setTitle(professionalImageEntity.getTitle());
					mediaInfo.setUrl(professionalImageEntity.getUrl());
					mediaInfo.setRoomCode(professionalImageEntity.getCatCode());
					professionalMediaInfos.add(mediaInfo);
				}
			}
		}

		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getTraveller())) {
			Map<String, List<TravellerImageEntity>> traveler = hotelImage.getImageDetails().getTraveller();
			List<TravellerImageEntity> travelerList = traveler.get("H");
			if (CollectionUtils.isNotEmpty(travelerList)) {
				for (TravellerImageEntity travellerImageEntity : travelerList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setDate(travellerImageEntity.getDate());
					mediaInfo.setFilterInfo(travellerImageEntity.getImageFilterInfo());
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTitle(travellerImageEntity.getTitle());
					mediaInfo.setTravelerName(travellerImageEntity.getTravellerName());
					mediaInfo.setUrl(travellerImageEntity.getUrl());
					travelerMediaInfos.add(mediaInfo);
				}
			}
		}

		if (hotelResult != null) {
			List<VideoInfo> videos = hotelResult.getHotelVideos();
			if (CollectionUtils.isNotEmpty(videos)) {
				List<MediaInfo> videoList = new ArrayList<>();
				for (VideoInfo video : videos) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setUrl(video.getUrl());
					mediaInfo.setThumbnailURL(video.getThumbnailUrl());
					mediaInfo.setTags(video.getTags());
					mediaInfo.setTitle(video.getTitle());
					mediaInfo.setMediaType("VIDEO");
					mediaInfo.setText(video.getText());
					mediaInfo.setFilterInfo(video.getVideoFilterInfo());
					videoList.add(mediaInfo);
				}
				if (CollectionUtils.isNotEmpty(videoList))
					professionalMediaInfos.addAll(0,videoList);
			}
			if (Objects.nonNull(hotelResult.getGiStaticData()) && Objects.nonNull(hotelResult.getGiStaticData().getImageGallery())){
				imageGallery = hotelResult.getGiStaticData().getImageGallery();
			}
		}
		Media media = null;
		if (CollectionUtils.isNotEmpty(professionalMediaInfos)
				|| CollectionUtils.isNotEmpty(travelerMediaInfos) ||
				imageGallery != null) {
			media = new Media();
			media.setProfessional(professionalMediaInfos);
			media.setTraveller(travelerMediaInfos);
			media.setImageGallery(imageGallery);
		}
		return media;
	}

	private Media buildMediaImageGallery(HotelImage hotelImage, com.mmt.hotels.model.response.staticdata.HotelResult hotelResult) {
		HotelMedia imageGallery = null;
		if (hotelResult != null) {
			if (Objects.nonNull(hotelResult.getGiStaticData()) && Objects.nonNull(hotelResult.getGiStaticData().getImageGallery())){
				imageGallery = hotelResult.getGiStaticData().getImageGallery();
			}
		}
		Media media = null;
		if (imageGallery != null) {
			media = new Media();
			media.setImageGallery(imageGallery);
		}
		return media;
	}

	private Map<String, ReviewSummary> getReviewSummary(CBFlyFishSummaryResponse flyfishSummaryResponse, String client, DeviceDetails deviceDetails, String countryCode, Map<String, String> expDataMap) {
		if (flyfishSummaryResponse == null || MapUtils.isEmpty(flyfishSummaryResponse.getSummary()))
			return null;
		Map<String,ReviewSummary> map = new HashMap<>();

		boolean isValidAppVersion=true;

		//Added temporarily for backward compatibility . Not needed once Android/Ios version 8.5.9/8.5.6 are rolled out 100%
		if(deviceDetails!=null && ("ANDROID".equalsIgnoreCase(deviceDetails.getBookingDevice()) || "IOS".equalsIgnoreCase(deviceDetails.getBookingDevice()))){
			isValidAppVersion=utility.isValidAppVersion(deviceDetails.getAppVersion(),"ANDROID".equalsIgnoreCase(deviceDetails.getBookingDevice())?"8.5.9":"8.5.6");
        }
		//Clean Up as part of GIHTL-15565 for SHOW_MMT_RATING_EXP experiment
//		boolean isMMTRatingExp = MapUtils.isNotEmpty(expDataMap) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.SHOW_MMT_RATING_EXP));
		OTA internationalPreferredOTA = utility.getPreferredOtaFromExp(flyfishSummaryResponse.getSummary(), false);

		for (OTA ota: flyfishSummaryResponse.getSummary().keySet()) {

			ReviewSummary reviewSummary = new ReviewSummary();
			JsonNode ratingSummary = flyfishSummaryResponse.getSummary().get(ota);
			reviewSummary.setSource(ota.getValue());
			if (ratingSummary.get("cumulativeRating") != null) {
				reviewSummary.setCumulativeRating(ratingSummary.get("cumulativeRating").floatValue());
			}
//			this will set recent 10 ratings for property into review section on detail page.
			if (ratingSummary.get("recentRatings") != null) {
				reviewSummary.setRecentRatings(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("recentRatings"), new TypeReference<List<Object>>() {
				}));
			}
			if (ratingSummary.get("manualPersuasion") != null) {
				reviewSummary.setManualPersuasion(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("manualPersuasion"), new TypeReference<com.mmt.hotels.clientgateway.response.ManualPersuasion>() {}));
			}
			if (ratingSummary.get("totalRatingCount") != null) {
				reviewSummary.setTotalRatingCount(ratingSummary.get("totalRatingCount").intValue());
			}
			if (ratingSummary.get("totalReviewsCount") != null) {
				reviewSummary.setTotalReviewCount(ratingSummary.get("totalReviewsCount").intValue());
			}
			if (ratingSummary.get("disableLowRating") != null) {
				reviewSummary.setDisableLowRating(ratingSummary.get("disableLowRating").booleanValue());
			}
			if (ratingSummary.get("preferredOTA") != null) {
				// using same value as flyFish if country code is India or there is only one summary
				// implementation for experiment.
				if (Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) || flyfishSummaryResponse.getSummary().size()==1) {
					reviewSummary.setPreferredOTA(ratingSummary.get("preferredOTA").booleanValue());
				}
				else {
					reviewSummary.setPreferredOTA(internationalPreferredOTA.equals(ota));
				}
			}
			if (ratingSummary.get("ratingText") != null) {
				reviewSummary.setRatingText(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("ratingText"), new TypeReference<String>() {}));
			}
			if (ratingSummary.get("crawledData") != null) {
				reviewSummary.setCrawledData(ratingSummary.get("crawledData").booleanValue());
			}
			if (ratingSummary.get("bestReviewTitle") != null) {
				reviewSummary.setBestReviewTitle((objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("bestReviewTitle"), new TypeReference<String>() {})));
			}
			if (ratingSummary.get("travelTypeList") != null) {
				reviewSummary.setTravelTypeList(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("travelTypeList"), new TypeReference<List<TravelType>>() {
				}));
			}
			if (ratingSummary.get("sortingCriterionList") != null) {
				reviewSummary.setSortingCriterionList(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("sortingCriterionList"), new TypeReference<List<CriteriaType>>() {
				}));
			}
			reviewSummary.setTravelTypes(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("travelTypes"), new TypeReference<List<String>>() {}));
			reviewSummary.setSortingCriterion(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("sortingCriterion"), new TypeReference<List<String>>() {}));
			reviewSummary.setImageTypes(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("imageTypes"), new TypeReference<List<String>>() {}));
			reviewSummary.setRatingBreakup(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("ratingBreakup"), new TypeReference<Map<String, Integer>>() {}));
			reviewSummary.setReviewBreakup(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("reviewBreakup"), new TypeReference<Map<String, Integer>>() {}));
			reviewSummary.setBest(getBestReview(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("best"), new TypeReference<List<ReviewDescriptionDTO>>() {})));
			reviewSummary.setAltAccoPer(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("altAccoPer"), new TypeReference<List<AltAccoPer>>() {}));
			if(ratingSummary.get("selectedCategory") != null) {
				reviewSummary.setSelectedCategory(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("selectedCategory"), new TypeReference<String>() {}));
			}
			if (ratingSummary.get("additionalInfo") != null) {
				reviewSummary.setAdditionalInfo(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("additionalInfo"), new TypeReference<FlyfishReviewAdditionalInfo>() {}));
			}
			JsonNode travellerRatingSummary = ratingSummary.get("travellerRatingSummary");
			if(travellerRatingSummary != null){
				reviewSummary.setHotelRatingSummary(getHotelRatingSummary(objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary,  new TypeReference<TravellerRatingSummaryDTO>(){})));
			}
			if(travellerRatingSummary != null){
				reviewSummary.setAltAccoRatingSummary(getAltAccoRatingSummary(objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary, new TypeReference<TravellerRatingSummaryDTO>() {})));
			}
			if(ota.name().equals("MMT")) {
				updateIconUrl(flyfishSummaryResponse.getContextualizedReviews(), client);
				reviewSummary.setContextualizedReviews(flyfishSummaryResponse.getContextualizedReviews());
			}
			if(ota == OTA.EXT){
				if(!isValidAppVersion)
					return null;
				reviewSummary.setDisclaimer(ratingSummary.get("disclaimer"));
				reviewSummary.setReviewHighlights(ratingSummary.get("reviewHighlights"));
				reviewSummary.setReviewHighlightTitle(ratingSummary.get("reviewHighlightTitle"));
			}
			map.put(ota.name(), reviewSummary);
		}
		return map;
	}

	private List<ConceptSummary> getAltAccoRatingSummary(TravellerRatingSummaryDTO travellerRatingSummaryDTO) {
		if (travellerRatingSummaryDTO == null || CollectionUtils.isEmpty(travellerRatingSummaryDTO.getAltAccoSummary())){
			return null;
		}
		List<ConceptSummary> altAccoRatingSummary = new ArrayList<>();

		for(ConceptSummaryDTO conceptSummaryDTO : travellerRatingSummaryDTO.getAltAccoSummary()) {
			ConceptSummary conceptSummary = new ConceptSummary();
			conceptSummary.setConcept(conceptSummaryDTO.getConcept());
			conceptSummary.setDisplayText(conceptSummaryDTO.getDisplayText());
			conceptSummary.setValue(conceptSummaryDTO.getValue());
			conceptSummary.setReviewCount(conceptSummaryDTO.getReviewCount());
			altAccoRatingSummary.add(conceptSummary);
		}
		return altAccoRatingSummary;
	}

	private void updateIconUrl(ContextualizeReviews contextualizedReviews, String client){
		if(contextualizedReviews == null){
			return;
		}
		String clientAppend = UNDERSCORE + client.toLowerCase() + DOT;
		if(CollectionUtils.isNotEmpty(contextualizedReviews.getHighlighted())) {
			for (ContextualReviewData highlighted : contextualizedReviews.highlighted) {
				if (StringUtils.isNotBlank(highlighted.iconUrl)) {
					highlighted.iconUrl = highlighted.iconUrl.replaceAll("\\.(?!.*\\.)", clientAppend);
				}
			}
		}
		if(CollectionUtils.isNotEmpty(contextualizedReviews.getCommon())) {
			for (ContextualReviewData common : contextualizedReviews.common) {
				if (StringUtils.isNotBlank(common.iconUrl)) {
					common.iconUrl = common.iconUrl.replaceAll("\\.(?!.*\\.)", clientAppend);
				}
			}
		}
	}


	private List<ConceptSummary> getHotelRatingSummary(TravellerRatingSummaryDTO travellerRatingSummaryDTO) {
		if (travellerRatingSummaryDTO == null || CollectionUtils.isEmpty(travellerRatingSummaryDTO.getHotelSummary())){
			return null;
		}

		List<ConceptSummary> hotelratingSummary = new ArrayList<>();

		for (ConceptSummaryDTO conceptSummaryDTO : travellerRatingSummaryDTO.getHotelSummary()){
			ConceptSummary conceptSummary = new ConceptSummary();
			conceptSummary.setConcept(conceptSummaryDTO.getConcept());
			conceptSummary.setDisplayText(conceptSummaryDTO.getDisplayText());
			conceptSummary.setHeroTag(conceptSummaryDTO.isHeroTag());
			conceptSummary.setReviewCount(conceptSummaryDTO.getReviewCount());
			conceptSummary.setShow(conceptSummaryDTO.getShow());
			conceptSummary.setSubConcepts(buildSubConcepts(conceptSummaryDTO.getSubConcepts()));
			conceptSummary.setValue(conceptSummaryDTO.getValue());

			hotelratingSummary.add(conceptSummary );
		}

		return hotelratingSummary;
	}

	private List<SubConcept> buildSubConcepts(List<SubConceptDTO> subConceptDTOList) {
		List<SubConcept> subConcepts = null;
		if (CollectionUtils.isNotEmpty(subConceptDTOList)){
			subConcepts = new ArrayList<>();
			for (SubConceptDTO subConceptDTO : subConceptDTOList){
				SubConcept subConcept = new SubConcept();
				subConcept.setPriorityScore(subConceptDTO.getPriorityScore());
				subConcept.setRelatedReviewCount(subConceptDTO.getRelatedReviewCount());
				subConcept.setSentiment(subConceptDTO.getSentiment());
				subConcept.setSubConcept(subConceptDTO.getSubConcept());
				subConcept.setDisplayText(subConceptDTO.getDisplayText());
				subConcept.setTagType(subConceptDTO.getTagType());

				subConcepts.add(subConcept );
			}
		}
		return subConcepts;
	}

	private List<ReviewObject> getBestReview(List<ReviewDescriptionDTO> best) {
		if (CollectionUtils.isEmpty(best))
			return null;
		List<ReviewObject> bestReviews = new ArrayList<>();
		for (ReviewDescriptionDTO reviewDTO: best) {
			ReviewObject reviewObject = new ReviewObject();
			reviewObject.setCheckinDate(reviewDTO.getCheckinDate());
			reviewObject.setCheckoutDate(reviewDTO.getCheckoutDate());
			reviewObject.setId(reviewDTO.getId());
			reviewObject.setPublishDate(reviewDTO.getPublishDate());
			reviewObject.setRating(reviewDTO.getRating());
			reviewObject.setReviewText(reviewDTO.getReviewText());
			reviewObject.setTitle(reviewDTO.getTitle());
			reviewObject.setTravellerName(reviewDTO.getTravellerName());
			reviewObject.setTravelType(reviewDTO.getTravelType());
			reviewObject.setUpvoted(reviewDTO.isUpvoted());
			bestReviews.add(reviewObject);
		}
		return bestReviews;
	}

	private HotelResult getHotelResult(com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB,
									   String context, Map<String, String> expData, boolean isCorp,
									   boolean isGroupBookingFunnel, String funnel, boolean isInternational, StaticDetailCriteria staticDetailCriteria, DeviceDetails deviceDetails) {
		if (null == hotelResultCB) {
			return null;
		}
		HotelResult hotelResult = new HotelResult();
		hotelResult.setGroupBookingHotel(hotelResultCB.isGroupBookingHotel());
		hotelResult.setAddress(getAddress(hotelResultCB.getAddr1(), hotelResultCB.getAddr2()));
		hotelResult.setPrimaryArea(hotelResultCB.getPrimaryArea());
		hotelResult.setAltAcco(hotelResultCB.isAltAcco());
		hotelResult.setCategories(new ArrayList<>(hotelResultCB.getCategories()));
		hotelResult.setCheckinTime(hotelResultCB.getCheckIntime());
		hotelResult.setCheckoutTime(hotelResultCB.getCheckOutTime());
		if (!hotelResultCB.getCheckIntime().equalsIgnoreCase(hotelResultCB.getCheckInTimeRange()) || !hotelResultCB.getCheckOutTime().equalsIgnoreCase(hotelResultCB.getCheckOutTimeRange())) {
			hotelResult.setCheckinTimeRange(hotelResultCB.getCheckInTimeRange());
			hotelResult.setCheckoutTimeRange(hotelResultCB.getCheckOutTimeRange());
		}
		hotelResult.setEmail(hotelResultCB.getEmail());
		hotelResult.setFreeWifi(hotelResultCB.isFreeWifi());
		hotelResult.setHotelIcon(hotelResultCB.getHotelIcon());
		hotelResult.setId(hotelResultCB.getId());
		hotelResult.setLat(hotelResultCB.getLatitude());
		hotelResult.setLng(hotelResultCB.getLongitude());
		hotelResult.setLocationDetail(buildLocationDetail(hotelResultCB.getCityCode(), hotelResultCB.getCityName(),
				hotelResultCB.getCityCtyCode(), hotelResultCB.getCountry()));
		hotelResult.setLongDesc(hotelResultCB.getLongDescription());
		hotelResult.setMobile(hotelResultCB.getMobile());
		hotelResult.setName(hotelResultCB.getName());
		hotelResult.setPinCode(hotelResultCB.getPinCode());
		hotelResult.setPropertyType(hotelResultCB.getPropertyType());
		hotelResult.setPropertyLabel(hotelResultCB.getPropertyLabel());
		hotelResult.setShortDesc(hotelResultCB.getShortDescription());
		hotelResult.setShortDescSeo(hotelResultCB.getShortDescSeo());
		hotelResult.setStarRating(hotelResultCB.getStarRating());
        hotelResult.setStayType(hotelResultCB.getStayType());
        hotelResult.setHostInfo(buildHostInfo(hotelResultCB.getUsers()));
		hotelResult.setHostInfoV2(hotelResultCB.getHostInfoV2());
		hotelResult.setStaffInfo(convertStaffInfo(hotelResultCB.getStaffInfo()));
		hotelResult.setAmenities(commonResponseTransformer.getAmenities(hotelResultCB.getAmenities()));
		// the lines below will be set and sent to the client only if it is a case of Available but sold out
		hotelResult.setListingDeeplinkUrl(hotelResultCB.getListingDeeplinkUrl());
		hotelResult.setPropertyUnavailableImg(hotelResultCB.getPropertyUnavailableImg());

        if (CollectionUtils.isNotEmpty(hotelResultCB.getLongStayAmenities()) && utility.shouldDisplayOfferDiscountBreakup(expData)) {
			SelectRoomAmenities longStayAmenity = commonResponseTransformer.getAmenities(hotelResultCB.getLongStayAmenities()).get(0);
			longStayAmenity.setType("LONG_STAY");
			longStayAmenity.setHighlightedText(polyglotService.getTranslatedData(ConstantsTranslation.LONGSTAY_HIGHLIGHTED_TEXT));
			longStayAmenity.setFocus(true);
        	hotelResult.getAmenities().add(0, longStayAmenity);
		}
		if (isGroupBookingFunnel) {
			hotelResult.setHighlightedAmenities(hotelResultCB.getGroupBookingAmenities());
		} else {
			hotelResult.setHighlightedAmenities(commonResponseTransformer.getHighlightedAmenities(hotelResultCB.getHighlightedAmenities()));
		}
		if (hotelResultCB.getStreetViewInfo() != null) {
			StreetViewInfo streetViewInfoInfo = new StreetViewInfo();
			BeanUtils.copyProperties(hotelResultCB.getStreetViewInfo(), streetViewInfoInfo);
			hotelResult.setStreetViewInfo(streetViewInfoInfo);
		}
		hotelResult.setStreetViewDataAvailable(hotelResultCB.isStreetViewDataAvailable());
		if (utility.isExperimentOn(expData,EXP_PL_GI) && hotelResultCB.getAltAccoRoomInfo() != null && !hotelResultCB.getAltAccoRoomInfo().isEmpty()) {
			hotelResult.setGiSharedSpacesV2(utility.getSpaceDataV2(hotelResultCB.getAltAccoRoomInfo(), false));
			Set<SpaceData> giPrivateSpaceV2Data = utility.getSpaceDataV2(hotelResultCB.getAltAccoRoomInfo(), true);
			//GIHTL-15674 Remove property layout section where only 1 room type available as private space and no shared space available.
			//But for entire property case,it should be shown
			if (!(CollectionUtils.isEmpty(hotelResult.getGiSharedSpacesV2()) && giPrivateSpaceV2Data!=null && !giPrivateSpaceV2Data.isEmpty() && giPrivateSpaceV2Data.size()==1) || Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelResultCB.getListingType())) {
				hotelResult.setGiPrivateSpacesV2(giPrivateSpaceV2Data);
			}
			if(Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelResultCB.getListingType())) {
				hotelResult.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(ENTIRE_PROPERTY_LAYOUT_TEXT),hotelResultCB.getPropertyType()));
			} else {
				hotelResult.setPropertyLayoutTitleText(polyglotService.getTranslatedData(ROOM_BY_ROOM_PROPERTY_LAYOUT_TEXT));
			}
		}
        hotelResult.setLongStayAmenities(commonResponseTransformer.getHighlightedAmenities(hotelResultCB.getLongStayAmenities()));
        hotelResult.setHouseRules(buildHouseRules(hotelResultCB.getHouseRules(), hotelResultCB.getMustReadRules()));
		hotelResult.setHouseRulesV2(buildHouseRulesV2(hotelResult.getHouseRules(), hotelResultCB.getFoodAndDiningRules()));
		/* GIHTL-13828 - Based on this flag, Clients will supress showing HouseRules on DetailPage UI by showing a disclaimer text
		 * (Scope is only for international and non AltAcco hotels)
		 * Inorder not to break old apps, we will still sends houseRulesV2 so that old apps can show the rules.
		 */
		boolean supressHouseRules = isInternational && !hotelResultCB.isAltAcco();
		hotelResult.setSupressHouseRules(supressHouseRules);
		if(hotelResultCB!=null && CollectionUtils.isNotEmpty(hotelResultCB.getFoodDining())){
			hotelResult.setFoodDining(buildFoodDining(hotelResultCB.getFoodDining(), hotelResultCB.isFoodDiningHighlight(), staticDetailCriteria, deviceDetails, true));
		}

		if(FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnel))
			suppressFewHouseRules(hotelResult.getHouseRulesV2());

		hotelResult.setFaqData(buildFaqData(hotelResultCB.getFAQs()));

		if (StringUtils.isNotBlank(hotelResultCB.getStayType()) && StringUtils.startsWith(hotelResultCB.getStayType().toUpperCase(), "ENTIRE")) {
            hotelResult.setEntireProperty(true);
        }
        hotelResult.setTotalGuestCount(hotelResultCB.getTotalGuestCount());
        hotelResult.setSharingUrl(hotelResultCB.getSharingUrl());
        if (StringUtils.isNotBlank(hotelResultCB.getDetailDeeplinkUrl())) {
            hotelResult.setDetailDeeplinkUrl(hotelResultCB.getDetailDeeplinkUrl());
        }
		hotelResult.setLocationPersuasion(hotelResultCB.getLocationPersuasion());
        hotelResult.setIngoId(hotelResultCB.getGdsHotelCode());
		hotelResult.setLocationPersuasion(hotelResultCB.getLocationPersuasion());
		hotelResult.setContextType(hotelResultCB.getContextType());
		hotelResult.setAlternateDatesAvailable(hotelResultCB.isAlternateDatesAvailable());
		if (!isCorp && CollectionUtils.isNotEmpty(hotelResult.getCategories()) && hotelResult.getCategories().contains(Constants.MMT_VALUE_STAYS)) {
			hotelResult.setTitleIcon(Utility.isGCC() ? iconUrlGcc : iconUrl);
			addTitleData(hotelResult, hotelResultCB.getOldCountryCode());
		}
		hotelResult.setContext(context);
		if(hotelResultCB.getLuxe() != null && hotelResultCB.getLuxe()) {
			hotelResult.setLuxeIcon(getLuxeIcon());
		}
		hotelResult.setMmtHotelCategory(hotelResultCB.getMmtHotelCategory());
		hotelResult.setMmtHotelText(hotelResultCB.getMmtHotelText());
		if(CollectionUtils.isNotEmpty(hotelResultCB.getSignatureAmenities())) {
			hotelResult.setSignatureAmenities(hotelResultCB.getSignatureAmenities());
		}
		hotelResult.setHighlightedAmenitiesTag(hotelResultCB.getHighlightedAmenitiesTag());
		hotelResult.setCardTitleMap(buildCardTitleMap());
		hotelResult.setHeroImage(hotelResultCB.getHeroImage());
		if(hotelResult.getHostInfo() == null || !hotelResult.getHostInfo().isChatEnabled()){
			hotelResult.setGroupBookingQueryEnabled(hotelResultCB.isGroupBookingAllowed());
			hotelResult.setGroupBookingWebUrl(hotelResultCB.getGroupBookingWebUrl());
		}
		hotelResult.setFlexibleCheckin(hotelResultCB.getFlexibleCheckinRules());
		hotelResult.setFlexibleCheckinInfo(hotelResultCB.getFlexibleCheckinInfo());
		hotelResult.setWishListed(hotelResultCB.isWishListed());
		hotelResult.setCalendarCriteria(buildCalendarCriteria(hotelResultCB.getCalendarCriteria()));
		hotelResult.setCategoryIcon(hotelResultCB.getCategoryIcon());
		buildGovtPolies(hotelResult, hotelResultCB);
		if(StringUtils.isNotEmpty(hotelResultCB.getCategoryUsp())) {
			hotelResult.setCategoryUspDetailsText(polyglotService.getTranslatedData(BEACHFRONT_CATEGORY_USP_DETAILS_TEXT));
		}
		hotelResult.setPremiumUsp(hotelResultCB.getPremiumUsp());
		utility.updateHotelResultGI(hotelResult, hotelResultCB, deviceDetails);
		if(MapUtils.isNotEmpty(expData) && expData.containsKey(UGCV2) && UGCV2_TRUE_VALUE.equalsIgnoreCase(expData.get(UGCV2))) {
			hotelResult.setAmenitiesGI(null);
		}
        return hotelResult;
    }

	/**
	 * @param hotelResult CG hotel static details object
	 * @param hotel HES hotel static details object.
	 * if govt policies are present in HES hotel object it will set in CG response
	 * else govt policies in CG hotel object will be null
	 */
	private void buildGovtPolies(HotelResult hotelResult, com.mmt.hotels.model.response.staticdata.HotelResult hotel) {
		if(CollectionUtils.isNotEmpty(hotel.getGovtPolicies())) {
			List<GovtPolicies> govtPolicies = new ArrayList<>();
			hotel.getGovtPolicies().forEach(govtPolicyFromHES -> {
				if(govtPolicyFromHES != null) {
					GovtPolicies govPolicy = new GovtPolicies();
					govPolicy.setNidhiId(govtPolicyFromHES.getNidhiId());
					govPolicy.setLogoUrl(govtPolicyFromHES.getLogoUrl());
					govPolicy.setTitle(govtPolicyFromHES.getTitle());
					govPolicy.setSubTitle(govtPolicyFromHES.getSubTitle());
					govPolicy.setValidTill(govtPolicyFromHES.getValidTill());
					govtPolicies.add(govPolicy);
				}
			});
			hotelResult.setGovtPolicies(govtPolicies);
		}
	}

	// merge all house rule
	private HouseRulesV2 buildHouseRulesV2(HouseRules houseRules, List<com.mmt.hotels.model.response.staticdata.CommonRules> foodAndDiningRule) {
		if(houseRules == null){
			return null;
		}
		HouseRulesV2 houseRulesV2 = new HouseRulesV2();
		houseRulesV2.setContextRules(houseRules.getContextRules());

		List<CommonRules> allRules = new ArrayList<>();
		if(houseRules.getMustReadRules() != null){
			allRules.add(convertMustReadRule(houseRules.getMustReadRules()));
		}

		List<CommonRules> foodAndDining = buildCommonRules(foodAndDiningRule);
		if (CollectionUtils.isNotEmpty(foodAndDining)) {
			CommonRules commonRule = new CommonRules();
			commonRule.setCategory(polyglotService.getTranslatedData(FOOD_AND_DINING));
			commonRule.setId(FOOD_AND_DINING.toLowerCase());
			commonRule.setShowInDetailHome(true);
			commonRule.setSubCategories(foodAndDining);
			allRules.add(commonRule);
		}

		if (CollectionUtils.isNotEmpty(houseRules.getCommonRules())) {
			for(CommonRules commonRules: houseRules.getCommonRules()){
				if(Constants.GUEST_PROFILE.equalsIgnoreCase(commonRules.getId()) || Constants.SAFETY_AND_HYGIENE.equalsIgnoreCase(commonRules.getId())){
					commonRules.setShowInDetailHome(true);
				}
				allRules.add(commonRules);
			}
		}

		List<Rule> rules = new ArrayList<>();
		List<ChildExtraBedPolicy> extraBedPolicy = houseRules.getExtraBedPolicyList();
		if (CollectionUtils.isNotEmpty(extraBedPolicy)) {
			for(ChildExtraBedPolicy childExtraBedPolicy : extraBedPolicy){
				rules.add(new Rule(childExtraBedPolicy.getPolicyInfo()));
				if (CollectionUtils.isNotEmpty(childExtraBedPolicy.getPolicyRules())) {
					for(PolicyRules policyRules : childExtraBedPolicy.getPolicyRules()){
						if (CollectionUtils.isNotEmpty(policyRules.getExtraBedTerms())) {
							for (ExtraBedRules extraBedRules : policyRules.getExtraBedTerms()){
								if(extraBedRules.getValue() != null) {
									rules.add(new Rule(extraBedRules.getValue()));
								}
							}
						}
					}
				}
			}
			CommonRules commonRules = new CommonRules();
			commonRules.setCategory(polyglotService.getTranslatedData(EXTRA_BED_POLICY));
			commonRules.setId(EXTRA_BED_POLICY.toLowerCase());
			commonRules.setRules(rules);
			allRules.add(commonRules);
		}

		houseRulesV2.setAllRules(allRules);
		return houseRulesV2;
	}

	private HouseRulesV2 buildFoodDining(List<com.mmt.hotels.model.response.staticdata.CommonRules> foodDiningRule, boolean foodDiningHighlight, StaticDetailCriteria staticDetailRequest, DeviceDetails deviceDetails, boolean foodAndDiningV2) {
		HouseRulesV2 foodDining = new HouseRulesV2();
		foodDining.setTitle(polyglotService.getTranslatedData(FOOD_AND_DINING));
		foodDining.setTag(foodDiningHighlight);

		List<CommonRules> allRulesList = new ArrayList<>();
		List<String> summaryList = new ArrayList<>();
		Map<String, com.mmt.hotels.model.response.staticdata.CommonRules> sectionToRuleMap = buildSectionToRuleMap(foodDiningRule);


		if (MapUtils.isNotEmpty(sectionToRuleMap)) {
			for (Map.Entry<String, com.mmt.hotels.model.response.staticdata.CommonRules> sectionToRuleEntry : sectionToRuleMap.entrySet()) {
				if (sectionToRuleEntry.getKey().equalsIgnoreCase(FoodAndDiningEnums.FoodMenu.getName())) {
					continue;
				}
				CommonRules commonRules = new CommonRules();
				commonRules.setCategory(sectionToRuleEntry.getValue().getCategory());
				commonRules.setDescription(sectionToRuleEntry.getValue().getHostCatHeading());

				if (sectionToRuleEntry.getValue().getCategory().equalsIgnoreCase(FoodAndDiningEnums.Meals.getName()) && sectionToRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())) {
					commonRules.setRules(buildMealRuleList(sectionToRuleMap, deviceDetails));
				} else if (CollectionUtils.isNotEmpty(sectionToRuleEntry.getValue().getRules())) {
					commonRules.setRules(buildRuleList(sectionToRuleEntry.getValue()));
				}

				if(!(foodAndDiningV2 & ADDITIONAL_INFORMATION.equalsIgnoreCase(sectionToRuleEntry.getValue().getCategory()))){
					commonRules.setShowInDetailHome(true);
				}
				commonRules.setImages(sectionToRuleEntry.getValue().getImages());
				allRulesList.add(commonRules);

				if (!ADDITIONAL_INFORMATION.equalsIgnoreCase(sectionToRuleEntry.getValue().getCategory())
						&& !Utility.isBookingDeviceDesktop(deviceDetails) ){
					if(foodAndDiningV2 && StringUtils.isNotEmpty(sectionToRuleEntry.getValue().getSummaryText())){
						summaryList.add(sectionToRuleEntry.getValue().getSummaryText());
					}
					else if(StringUtils.isNotEmpty(sectionToRuleEntry.getValue().getHostCatHeading())){
						summaryList.add(sectionToRuleEntry.getValue().getHostCatHeading());
					}
				}
			}
		}

		foodDining.setAllRules(CollectionUtils.isNotEmpty(allRulesList) ? allRulesList : null);

		//if there is only one section then put ruleText from ruleList into summary list
		if (CollectionUtils.isNotEmpty(foodDiningRule) && foodDiningRule.size() == 1 && ADDITIONAL_INFORMATION.equalsIgnoreCase(foodDiningRule.get(0).getCategory())) {
			handleOneFoodAndDiningSection(foodDiningRule.get(0), summaryList, allRulesList);
		}

		// these are L1 overview pointers
		if (CollectionUtils.isNotEmpty(summaryList)) {
			// if childContext (child count > 0) is there in request, add node "Special meal for Kids is available on request"
			if (MapUtils.isNotEmpty(sectionToRuleMap) && sectionToRuleMap.containsKey(FoodAndDiningEnums.Cook.getName())) {
				Integer childCount = utility.getTotalChildrenFromRequest(staticDetailRequest.getRoomStayCandidates());
				if (childCount != null && childCount > 0) {
					String mealForKids = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_FOR_KIDS);
					if (StringUtils.isNotEmpty(mealForKids) && !Constants.NULL_STRING.equalsIgnoreCase(mealForKids)) {
						summaryList.add(mealForKids);
					}
				}
			}

			foodDining.setSummary(summaryList);
		}
		return foodDining;
	}

	private Map<String, com.mmt.hotels.model.response.staticdata.CommonRules> buildSectionToRuleMap(List<com.mmt.hotels.model.response.staticdata.CommonRules> foodDiningRule) {
		Map<String, com.mmt.hotels.model.response.staticdata.CommonRules> sectionToRuleMap = new LinkedHashMap<>();

		if (CollectionUtils.isNotEmpty(foodDiningRule)) {
			for (com.mmt.hotels.model.response.staticdata.CommonRules commonRule : foodDiningRule) {
				if (StringUtils.isNotEmpty(commonRule.getCategory())) {
					sectionToRuleMap.put(commonRule.getCategory(), commonRule);
				}
			}
		}
		return sectionToRuleMap;
	}

	private List<Rule> buildMealRuleList(Map<String, com.mmt.hotels.model.response.staticdata.CommonRules> sectionToCommonRuleMap, DeviceDetails deviceDetails) {
		Rule foodMenuRule = null;
		if (sectionToCommonRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())
				&& CollectionUtils.isNotEmpty(sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).getRules())
				&& null != sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).getRules().get(0)) {
			foodMenuRule = new Rule();
			foodMenuRule.setText(sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).getRules().get(0).getText());
			foodMenuRule.setImages(sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).getRules().get(0).getImages());
			foodMenuRule.setImageCategory(sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).getRules().get(0).getImageCategory());
		}

		List<Rule> ruleList = buildRuleList(sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()));
		if (deviceDetails != null && Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice())) {
			if (foodMenuRule != null && sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()).getRules().size() > foodMenuPosition) {
				ruleList.add(foodMenuPosition, foodMenuRule);
			}
		} else {
			ruleList.add(foodMenuRule);
		}
		return ruleList;
	}

	private List<Rule> buildRuleList(com.mmt.hotels.model.response.staticdata.CommonRules commonRule){
		List<Rule> allRuleCg = new ArrayList<>();
		for (com.mmt.hotels.model.response.staticdata.Rule rule : commonRule.getRules()){
			Rule ruleCg = new Rule();
			ruleCg.setText(rule.getText());
			allRuleCg.add(ruleCg);
		}
		return allRuleCg;
	}

	private void handleOneFoodAndDiningSection(com.mmt.hotels.model.response.staticdata.CommonRules commonRule, List<String> summaryList, List<CommonRules> allRulesList){
		summaryList.clear();
		if(CollectionUtils.isNotEmpty(commonRule.getRules())){
			if(commonRule.getRules().size() <= foodDiningMinCountConfig){
				for(com.mmt.hotels.model.response.staticdata.Rule rule : commonRule.getRules()){
					summaryList.add(rule.getText());
				}
				allRulesList.clear();
			}
			else {
				for(int i=0; i<foodDiningMinCountConfig; i++){
					summaryList.add(commonRule.getRules().get(i).getText());
				}
			}
		}
	}

	private CommonRules convertMustReadRule(CommonRules mustReadRules){
		if (CollectionUtils.isNotEmpty(mustReadRules.getRulesList())) {
			List<Rule> rules = mustReadRules.getRulesList().stream().map(Rule::new).collect(Collectors.toList());
			mustReadRules.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.RESTRICTIONS));
			mustReadRules.setId(RESTRICTIONS.toLowerCase());
			mustReadRules.setShowInDetailHome(true);
			mustReadRules.setExpandRules(true);
			mustReadRules.setRules(rules);
			mustReadRules.setRulesList(null);
		}
		return mustReadRules;
	}


	protected abstract Map<String, String> buildCardTitleMap();

	protected abstract void addTitleData(HotelResult hotelResult, String countryCode);

	protected abstract String getLuxeIcon();

	private HouseRules buildHouseRules(com.mmt.hotels.model.response.staticdata.HouseRules houseRules, List<String> mustReadRules) {
		HouseRules houseRulesCG = null;
		if (houseRules != null ) {
			houseRulesCG = new HouseRules();
			houseRulesCG.setChildExtraBedPolicy(buildChildExtraBedPolicy(houseRules.getChildExtraBedPolicy()));
			houseRulesCG.setCommonRules(buildCommonRules(houseRules.getCommonRules()));
			houseRulesCG.setExtraBedPolicyList(buildExtraBedPolicyList(houseRules.getExtraBedPolicyList()));
			houseRulesCG.setOtherInfo(buildCommonRules(houseRules.getOtherInfo()));
			houseRulesCG.setMustReadRules(buildMustReadRules(mustReadRules));
			houseRulesCG.setContextRules(houseRules.getContextRules());
		}
		return houseRulesCG;
	}

	private FaqData buildFaqData( List<Faqs> faqs) {
		FaqData faqData = null;
		if(faqs != null)
		{
			faqData = new FaqData();
			faqData.setFaqs(faqs);
			faqData.setTitle(FAQ_TITLE);
			faqData.setHint(FAQ_HINT);
			faqData.setItemCountForCard(Integer.valueOf(FAQ_ITEM_COUNT));
			faqData.setExtraItemText(FAQ_EXTRA_TEXT.replace("%d", ""+faqs.size()));
		}
		return faqData;
	}

	private CommonRules buildMustReadRules(List<String> mustReadRules) {
		if(CollectionUtils.isEmpty(mustReadRules)) {
			return null;
		}
		CommonRules mustReadRulesCG = new CommonRules();
		mustReadRulesCG.setCategory("must read");
		mustReadRulesCG.setRulesList(mustReadRules);
		return mustReadRulesCG;
	}

	private List<ChildExtraBedPolicy> buildExtraBedPolicyList(List<com.mmt.hotels.model.response.staticdata.ChildExtraBedPolicy> extraBedPolicyList) {
		List<ChildExtraBedPolicy> listCG = null;
		if (CollectionUtils.isNotEmpty(extraBedPolicyList)){
			listCG = new ArrayList<>();
			for(com.mmt.hotels.model.response.staticdata.ChildExtraBedPolicy policy : extraBedPolicyList){
				listCG.add(buildChildExtraBedPolicy(policy));
			}
		}
		return listCG;
	}

	private List<CommonRules> buildCommonRules(List<com.mmt.hotels.model.response.staticdata.CommonRules> commonRules) {
		List<CommonRules> commonRulesCG = null;
		if (CollectionUtils.isNotEmpty(commonRules)){
			commonRulesCG = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.CommonRules commonRule : commonRules){
				CommonRules commonRuleCG = new CommonRules();
				commonRuleCG.setCategory(commonRule.getCategory());
				commonRuleCG.setCategoryId(commonRule.getCategoryId());
				commonRuleCG.setId(commonRule.getId());
				commonRuleCG.setRules(buildRules(commonRule.getRules()));
				commonRuleCG.setHostCatHeading(commonRule.getHostCatHeading());
				commonRuleCG.setShowInHost(commonRule.isShowInHost());
				commonRuleCG.setShowInDetailHome(commonRule.isShowInDetailHome());
				commonRuleCG.setExpandRules(commonRule.isExpandRules());
				commonRuleCG.setImages(commonRule.getImages());
				commonRulesCG.add(commonRuleCG );
			}
		}
		return commonRulesCG;
	}

	private List<Rule> buildRules(List<com.mmt.hotels.model.response.staticdata.Rule> rules) {
		List<Rule> rulesCG = null;
		if (CollectionUtils.isNotEmpty(rules)){
			rulesCG = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.Rule rule : rules){
				Rule ruleCG = new Rule();
				ruleCG.setDisplay(rule.getDisplay());
				ruleCG.setDisplayRank(rule.getDisplayRank());
				ruleCG.setSentiment(rule.getSentiment());
				ruleCG.setTemplateText(rule.getTemplateText());
				ruleCG.setText(rule.getText());
				ruleCG.setRequired(rule.getRequired());
				ruleCG.setValues(rule.getValues());
				rulesCG.add(ruleCG );
			}
		}
		return rulesCG;
	}

	private ChildExtraBedPolicy buildChildExtraBedPolicy(
			com.mmt.hotels.model.response.staticdata.ChildExtraBedPolicy childExtraBedPolicy) {
		if (null == childExtraBedPolicy){
			return null;
		}
		ChildExtraBedPolicy childExtraBedPolicyCG = new ChildExtraBedPolicy();
		childExtraBedPolicyCG.setId(childExtraBedPolicy.getId());
		childExtraBedPolicyCG.setLabel(childExtraBedPolicy.getLabel());
		childExtraBedPolicyCG.setPaid(childExtraBedPolicy.getPaid());
		childExtraBedPolicyCG.setPolicyInfo(childExtraBedPolicy.getPolicyInfo());
		childExtraBedPolicyCG.setPolicyRules(buildPolicyRules(childExtraBedPolicy.getPolicyRules()));
		return childExtraBedPolicyCG;
	}

	private List<PolicyRules> buildPolicyRules(List<com.mmt.hotels.model.response.staticdata.PolicyRules> policyRules) {
		List<PolicyRules> policyRulesCG = null;
		if (CollectionUtils.isNotEmpty(policyRules)){
			policyRulesCG = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.PolicyRules policyRule : policyRules){
				PolicyRules PolicyRulesCG = new PolicyRules();
				PolicyRulesCG.setAgeGroup(policyRule.getAgeGroup());
				PolicyRulesCG.setExtraBedTerms(buildExtraBedTerms(policyRule.getExtraBedTerms()));
				policyRulesCG.add(PolicyRulesCG);
			}
		}
		return policyRulesCG;
	}

	private Set<ExtraBedRules> buildExtraBedTerms(Set<com.mmt.hotels.model.response.staticdata.ExtraBedRules> extraBedTerms) {
		Set<ExtraBedRules> extraBedRulesCG = null;
		if (CollectionUtils.isNotEmpty(extraBedTerms)){
			extraBedRulesCG = new HashSet<>();
			for (com.mmt.hotels.model.response.staticdata.ExtraBedRules extraBedTerm : extraBedTerms){
				ExtraBedRules extraBedRuleCG = new ExtraBedRules();
				extraBedRuleCG.setLabel(extraBedTerm.getLabel());
				extraBedRuleCG.setValue(extraBedTerm.getValue());
				extraBedRulesCG.add(extraBedRuleCG);
			}
		}
		return extraBedRulesCG;
	}

	private HostInfo buildHostInfo(Map<String, ArrayList<Type>> users) {
		HostInfo hostInfo = null;
		if (MapUtils.isNotEmpty(users)){
			List<Type> types = users.get("hotel");
			if (CollectionUtils.isNotEmpty(types)){
				Type type = types.get(0);
				hostInfo = new HostInfo();
				hostInfo.setAbout(type.getAbout());
				hostInfo.setEmail(type.getEmail());
				hostInfo.setHobbies(type.getHobbies());
				hostInfo.setHostImage(type.getPicture());
				hostInfo.setHostingExperienceTotal(type.getHostingExperienceTotal());
				hostInfo.setMobile(type.getMobile());
				hostInfo.setName(type.getName());
				hostInfo.setStarHostDescription(type.getStarHostDescription());
				hostInfo.setStarHostHeading(type.getStarHostHeading());
				hostInfo.setStarHostImageUrl(type.getStarHostImageUrlNew());
				hostInfo.setStarHostIcon(type.getStarHostImageIcon());
				hostInfo.setChatEnabled(type.isChatEnabled());
				hostInfo.setStarHostReasons(type.getStarHostReasons());
				hostInfo.setTimeSinceHostingOnMmt(type.getTimeSinceHostingOnMmt());
				hostInfo.setResponseTime(type.getResponseTime());
				hostInfo.setResponseRate(type.getResponseRate());
				hostInfo.setLanguage(type.getLanguage());
				hostInfo.setEducation(type.getEducation());
				hostInfo.setGender(type.getGender());
				hostInfo.setPicture(type.getPicture());

			}
		}
		return hostInfo;
	}

	private Address getAddress(String addr1, String addr2) {
		Address address = new Address();
		address.setLine1(addr1);
		address.setLine2(addr2);
		return address;
	}

	private LocationDetail buildLocationDetail(String cityCode, String cityName, String cityCtyCode, String country) {
		LocationDetail locationDetail = new LocationDetail();
		locationDetail.setId(cityCode);
		locationDetail.setName(cityName);
		locationDetail.setType("city");
		locationDetail.setCountryId(cityCtyCode);
		locationDetail.setCountryName(country);
		return locationDetail;
	}

	private Map<String,RoomDetails> buildRoomInfoMap(HotelDetailWrapperResponse hotelDetailWrapperResponse) {
		if (hotelDetailWrapperResponse.getRoomInfoData()==null || MapUtils.isEmpty(hotelDetailWrapperResponse.getRoomInfoData().getRoomInfoMap()))
			return null;

		Map<String,RoomDetails> map = new HashMap<>();
		hotelDetailWrapperResponse.getRoomInfoData().getRoomInfoMap().forEach((key,value)->map.put(key,buildRoomInfo(value)));

		if (MapUtils.isEmpty(map)) {
			return null;
		} else if (hotelDetailWrapperResponse.getHotelImage()!=null && hotelDetailWrapperResponse.getHotelImage().getImageDetails()!=null
					&& hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional()!=null
					&& hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().containsKey("R")) {
			/* Map only Room images : parse professional (only Room, not Hotel) images
			*  and set them in the RoomDetails object for each matching room. */
			List<ProfessionalImageEntity> professionalImageEntityList = hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().get("R");
			professionalImageEntityList.forEach(
					professionalImageEntity -> {
						String roomCode = professionalImageEntity.getCatCode();
						if (StringUtils.isNotBlank(roomCode) && map.containsKey(roomCode)) {
							if (CollectionUtils.isEmpty(map.get(roomCode).getImages())) {
								map.get(roomCode).setImages(new ArrayList<>());
							}
							map.get(roomCode).getImages().add(professionalImageEntity.getUrl().startsWith("http") ? professionalImageEntity.getUrl() : "https:" + professionalImageEntity.getUrl());
						}
					}
			);
		}
		return map;
	}

	private RoomDetails buildRoomInfo(RoomInfo roomInfo) {
		if (roomInfo == null) {
			return null;
		}
		RoomDetails roomDetails = new RoomDetails();
		roomDetails.setRoomCode(roomInfo.getRoomCode());
		roomDetails.setRoomName(roomInfo.getRoomName());
		roomDetails.setRoomSize(roomInfo.getRoomSize());
		roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
		roomDetails.setRoomViewName(roomInfo.getRoomViewName());
		roomDetails.setBeds(roomInfo.getBeds());
		roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
		roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
		roomDetails.setMaxChild(roomInfo.getMaxChildCount());
		roomDetails.setMaster(roomInfo.isMaster());
		if (StringUtils.isNotBlank(roomInfo.getBedRoomCount())) {
			roomDetails.setBedroomCount(NumberUtils.toInt(roomInfo.getBedRoomCount(),0));
		}
		roomDetails.setBedCount(roomInfo.getBedCount());
		roomDetails.setAmenities(commonResponseTransformer.buildAmenities(roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities()));
		roomDetails.setHighlightedAmenities(commonResponseTransformer.buildHighlightedAmenities(roomInfo.getFacilityHighlights()));
		return roomDetails;
	}

	private boolean isCorp(StaticDetailRequest staticDetailRequest) {
		if (staticDetailRequest!=null && staticDetailRequest.getRequestDetails()!=null
			&& StringUtils.isNotBlank(staticDetailRequest.getRequestDetails().getIdContext())) {
			return Constants.CORP_ID_CONTEXT.equalsIgnoreCase(staticDetailRequest.getRequestDetails().getIdContext());
		}
		return false;
	}

	public abstract StaffInfo convertStaffInfo(StaffInfo staffInfo);

	public void removeIcon(StaffInfo staffInfo){
		if(staffInfo == null){
			return;
		}
		removeIcon(staffInfo.getHost());
		removeIcon(staffInfo.getCook());
		removeIcon(staffInfo.getCaretaker());
	}

	private void removeIcon(Staff staff){
		if(staff == null){
			return;
		}
		for(StaffData staffData : staff.getData()){
			if (CollectionUtils.isNotEmpty(staffData.getGeneralInfo())) {
				staffData.getGeneralInfo().forEach(x -> x.setIconUrl(null));
			}
		}
	}

	public WishListedHotelsDetailResponse convertWishListedStaticDetailResponse(HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse, FlyfishReviewWrapperResponse flyfishReviewWrapperResponse,
																				String client, WishListedHotelsDetailRequest wishListedHotelsDetailRequest, CommonModifierResponse commonModifierResponse){
		WishListedHotelsDetailResponse wishListedHotelsDetailResponse = new WishListedHotelsDetailResponse();
		long startTime = System.currentTimeMillis();
		try {
			if(hotStoreHotelsWrapperResponse != null && CollectionUtils.isNotEmpty(hotStoreHotelsWrapperResponse.getHotelResults())){
				String countryCode = wishListedHotelsDetailRequest.getSearchCriteria() != null ? wishListedHotelsDetailRequest.getSearchCriteria().getCountryCode() : Constants.DOM_COUNTRY;
				List<Hotel> hotels = new ArrayList<>();
				hotStoreHotelsWrapperResponse.getHotelResults()
						.forEach(hotelResult -> {
							Hotel hotel = new Hotel();
							hotel.setId(hotelResult.getId());
							hotel.setName(hotelResult.getName());
							hotel.setPropertyType(hotelResult.getPropertyType());
							hotel.setPropertyLabel(hotelResult.getPropertyLabel());
							hotel.setStayType(hotelResult.getStayType());
							hotel.setStarRating(hotelResult.getStarRating());
							hotel.setAlternateDates(hotelResult.isAlternateDatesAvailable());
							hotel.setIsAltAcco(hotelResult.isAltAcco());
							hotel.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelResult));
							hotel.setLocationPersuasion(hotelResult.getLocationPersuasion());
							List<String> images = getImagesForHotel(hotelResult.getId(), hotStoreHotelsWrapperResponse, wishListedHotelsDetailRequest);
							hotel.setMedia(commonResponseTransformer.buildMedia(images, hotelResult.getHotelVideos(), null));
							Map<OTA, JsonNode> reviewSummaryForHotel = getReviewSummaryForHotel(hotelResult.getId(), flyfishReviewWrapperResponse);
							if (wishListedHotelsDetailRequest.getDeviceDetails() != null) {
								hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(countryCode, reviewSummaryForHotel, wishListedHotelsDetailRequest.getDeviceDetails(), commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null));
							} else {
								hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(countryCode, reviewSummaryForHotel, null, commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null));
							}
							hotel.setLocationDetail(buildLocationDetail(hotelResult.getCityCode(), hotelResult.getCityName(), hotelResult.getCityCtyCode(), hotelResult.getCountry()));
							hotel.setCategories(hotelResult.getCategories());
							hotel.setDetailDeeplinkUrl(hotelResult.getDetailDeeplinkUrl());
							hotel.setHeroImage(hotelResult.getHeroImage());
							hotel.setMmtHotelCategory(hotelResult.getMmtHotelCategory());
							hotel.setWishListed(hotelResult.isWishListed());
							hotels.add(hotel);
						});
				wishListedHotelsDetailResponse.setHotels(hotels);
			}
		}finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
		}

		return wishListedHotelsDetailResponse;
	}

	private List<String> getImagesForHotel(String hotelId, HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse, WishListedHotelsDetailRequest wishListedHotelsDetailRequest) {
		List<String> images = new ArrayList<>();
		if (wishListedHotelsDetailRequest.getImageDetails() != null && CollectionUtils.isNotEmpty(wishListedHotelsDetailRequest.getImageDetails().getTypes())
				&& wishListedHotelsDetailRequest.getImageDetails().getTypes().contains(Constants.PROFESSIONAL)) {
			if (hotStoreHotelsWrapperResponse != null && MapUtils.isNotEmpty(hotStoreHotelsWrapperResponse.getHotelIdToHotelImageMap())) {
				if (hotStoreHotelsWrapperResponse.getHotelIdToHotelImageMap().containsKey(hotelId)) {
					ImageType imageDetails = hotStoreHotelsWrapperResponse.getHotelIdToHotelImageMap().get(hotelId).getImageDetails();
					if (imageDetails != null) {
						if (MapUtils.isNotEmpty(imageDetails.getProfessional())) {
							List<String> imageUrlList = new ArrayList<>();
							for (Map.Entry<String, List<ProfessionalImageEntity>> professionalImageMap : imageDetails.getProfessional().entrySet()) {
								List<ProfessionalImageEntity> professionalImageList = professionalImageMap.getValue();
								if (CollectionUtils.isNotEmpty(professionalImageList)) {
									for (ProfessionalImageEntity imageEntity : professionalImageList) {
										imageUrlList.add(imageEntity.getUrl());
									}
								}
							}
							int imagesCount = imageUrlList.size();
							if (wishListedHotelsDetailRequest.getImageDetails() != null && CollectionUtils.isNotEmpty(wishListedHotelsDetailRequest.getImageDetails().getCategories())) {
								Integer suppliedCount = wishListedHotelsDetailRequest.getImageDetails().getCategories().get(0).getCount();
								imagesCount = Math.min(suppliedCount, imageUrlList.size());
							}
							imageUrlList = imageUrlList.subList(0, imagesCount);
							images.addAll(imageUrlList);
						}
					}
				}
			}
		}
		return images;
	}

	protected Map<OTA, JsonNode> getReviewSummaryForHotel(String hotelId, FlyfishReviewWrapperResponse flyfishReviewWrapperResponse) {
		Map<OTA, JsonNode> reviewSummary = null;
		if (StringUtils.isNotBlank(hotelId) && flyfishReviewWrapperResponse != null && flyfishReviewWrapperResponse.getFlyFishReviewSummary() != null
				&& MapUtils.isNotEmpty(flyfishReviewWrapperResponse.getFlyFishReviewSummary().getSummary())
				&& flyfishReviewWrapperResponse.getFlyFishReviewSummary().getSummary().containsKey(hotelId)) {
			reviewSummary = flyfishReviewWrapperResponse.getFlyFishReviewSummary().getSummary().get(hotelId);
		}
		return reviewSummary;
	}

	private CalendarCriteria buildCalendarCriteria(com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria calendarCriteriaHES) {
		if(calendarCriteriaHES == null)
			return null;
		CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
		calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getAdvanceDays());
		calendarCriteriaCG.setAvailable(calendarCriteriaHES.isAvailable());
		calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getMaxDate());
		calendarCriteriaCG.setMlos(calendarCriteriaHES.getMlos());
		return calendarCriteriaCG;
	}

	private void suppressFewHouseRules(HouseRulesV2 houseRulesV2) {
		if (null == houseRulesV2 || CollectionUtils.isEmpty(houseRulesV2.getAllRules())) return;
		List<CommonRules> resultCommonRulesList = houseRulesV2.getAllRules().stream().filter(rule -> !supressedHouseRulesList.contains(rule.getCategoryId()) && (null == rule.getId() || !rule.getId().equalsIgnoreCase(EXTRA_BED_POLICY_TO_BE_REMOVED))).collect(Collectors.toList());
		houseRulesV2.setAllRules(resultCommonRulesList);
	}

	protected UGCSummary buildUgcSummary(CBPlatformSummaryResponse data, CommonModifierResponse commonModifierResponse, String countryCode) {
		try {
			UGCSummary ugcSummary = null;
			if (data != null && data.getSummary() != null) {
				ugcSummary = new UGCSummary();
				UGCPlatformReviewSummaryDTO summary = objectMapperUtil.getObjectFromJsonNode(data.getSummary().get("data"), new TypeReference<UGCPlatformReviewSummaryDTO>() {
				});
				ugcSummary.setData(summary);
				ugcSummary.setCardTitle(REVIEW_RATING_TITLE);


				if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, MMT_RATINGS_ON_GI), TRUE)) {
					if (summary != null && summary.getAvailableOTAs() != null) {
						if (summary.getAvailableOTAs().contains(OTA.MMT)) {
							clearRatingSummaryAndReviews(summary);
						}
					}
				}

				// Below Change made for Combined OTA Flow [GIHTL-16802]
				if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, COMBINED_OTA_FLOW), FALSE)) {
					changeUGCDataSource(summary);
				}

				if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, COMBINED_OTA_FLOW), TRUE)) {
					hideRatingBreakUpForIH(summary, countryCode);
				}

				if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, TRAVELLER_IMPRESSIONS_EXP), FALSE)) {
					hideSeekTagDetailsInfo(summary);
				}
			}

			return ugcSummary;
		} catch (Exception e) {
			return null;
		}
	}

	protected void clearRatingSummaryAndReviews(UGCPlatformReviewSummaryDTO summary) {
		summary.setRatingSummaryGI(null);
		summary.setReviewCount(null);
		summary.setRatingHighlight(null);
	}

	protected void changeUGCDataSource(UGCPlatformReviewSummaryDTO data) {
		if (StringUtils.equalsIgnoreCase(data.getSource().getValue(), OTA.GI_EXP.getValue()) || StringUtils.equalsIgnoreCase(data.getSource().getValue(), OTA.GI_BKG.getValue())) {
			data.setSource(OTA.GI);
		}
	}

	protected void hideRatingBreakUpForIH(UGCPlatformReviewSummaryDTO data, String countryCode) {
		if (StringUtils.isNotBlank(countryCode) && !StringUtils.equalsIgnoreCase(Constants.DOM_COUNTRY, countryCode)) {
			data.setRatingBreakup(null);
		}
	}

	protected void hideSeekTagDetailsInfo(UGCPlatformReviewSummaryDTO summaryDTO) {
		if (summaryDTO != null) {
			summaryDTO.setSeekTagDetails(null);
		}
	}

}
