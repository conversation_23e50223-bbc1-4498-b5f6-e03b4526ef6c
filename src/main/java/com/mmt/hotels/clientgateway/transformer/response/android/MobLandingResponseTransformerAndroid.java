package com.mmt.hotels.clientgateway.transformer.response.android;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.MobLandingResponseTransformer;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MobLandingResponseTransformerAndroid extends MobLandingResponseTransformer {

    @Autowired
    PersuasionUtil persuasionUtil;
    private static final Logger logger = LoggerFactory.getLogger(MobLandingResponseTransformerAndroid.class);

    private static final String DEVICE_TYPE = "Apps";

    @Override
    public void addLocationPersuasionToHotelPersuasions(SearchWrapperHotelEntity hotelEntity) {
        if (hotelEntity != null && CollectionUtils.isNotEmpty(hotelEntity.getLocationPersuasion())) {
            List<String> locationPersuasion = hotelEntity.getLocationPersuasion();
            if (hotelEntity.getHotelPersuasions() == null)
                hotelEntity.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject locPers = new PersuasionObject();
            locPers.setData(new ArrayList<>());
            locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP);
            locPers.setTemplate("IMAGE_TEXT_H");
            locPers.setPlaceholder("SINGLE");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setHasAction(false);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");

            locPers.getData().add(locPersuasionData);
            if (locationPersuasion.size() == 1) {
                locPersuasionData.setText(locationPersuasion.get(0));
            } else if (locationPersuasion.size() >= 2) {
                locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
            }
            try {
                ((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP, locPers);
            } catch (ClassCastException e) {
                logger.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
            } catch (Exception e) {
                logger.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }

        }
    }

    /**
     * This is an encapsulated method to build all the required persuasions for Hidden Gem card.
     */
    @Override
    public void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity) {
        addLocationPersuasionToHotelPersuasions(hotelEntity);
        buildHiddenGemPersuasion(hotelEntity);
        buildHiddenGemIconPersuasion(hotelEntity);
        buildHomeStaysPersuasion(hotelEntity);
    }

    /**
     * Method to build Hidden Gem Persuasion, this method calls persuasionUtil to build Hidden Gem persuasion.
     * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
     */
    public void buildHiddenGemPersuasion(SearchWrapperHotelEntity hotelEntity) {
        PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemPersuasion(hotelEntity, DEVICE_TYPE);
        if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
            hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps());
            MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps(), hiddenGemPersuasion);
        }
    }

    /**
     * Method to build Hidden Gem Icon Persuasion, this method calls persuasionUtil to build Hidden Gem Icon persuasion.
     * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
     */
    public void buildHiddenGemIconPersuasion(SearchWrapperHotelEntity hotelEntity) {
        PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemIconPersuasion(hotelEntity, DEVICE_TYPE);
        if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
            hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps());
            MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps(), hiddenGemPersuasion);
        }
    }

    /**
     * Method to build Home Stays Persuasions, this method calls persuasionUtil to build Homestay persuasions.
     * If the util method return a non-empty Persuasion List for homestay title, this method will add that persuasion in Hotel Persuasion object.
     * And, if util method return a non-empty Persuasion List for homestay title and sub-title, this method will add both the persuasions in Hotel Persuasion object, on the same placeholder
     */
    public void buildHomeStaysPersuasion(SearchWrapperHotelEntity hotelEntity) {
        PersuasionObject homeStaysTitlePersuasion = persuasionUtil.buildHomeStaysTitlePersuasion(hotelEntity, DEVICE_TYPE);
        if (homeStaysTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysTitlePersuasion.getData()) && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
            homeStaysTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps());
            MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps(), homeStaysTitlePersuasion);

            PersuasionObject homeStaysSubTitlePersuasion = persuasionUtil.buildHomeStaysSubTitlePersuasion(hotelEntity, DEVICE_TYPE);
            if (homeStaysSubTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysSubTitlePersuasion.getData())) {
                homeStaysSubTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps());
                homeStaysTitlePersuasion.getData().add(homeStaysSubTitlePersuasion.getData().get(0));
                MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps(), homeStaysTitlePersuasion);
            }
        }
    }

}
