package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.BNPLDisabledReason;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.response.availrooms.SuccessData;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelReviewIncognito;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payment.SpecialRequestCategory;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions;
import com.mmt.hotels.clientgateway.response.availrooms.*;
import com.mmt.hotels.clientgateway.response.corporate.CorpRateTags;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.clm.ClmPersuasion;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.request.addon.LOB;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.mmtprime.BlackInfo;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.model.response.staticdata.ProfessionalImageEntity;
import com.mmt.hotels.model.response.txn.LoyaltyMessageResponse;
import com.mmt.hotels.model.response.txn.UserCard;
import com.mmt.hotels.pojo.request.detail.mob.CBPlatformSummaryResponse;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import com.mmt.model.RoomInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;
import static java.lang.Math.min;

@Component
public abstract class AvailRoomsResponseTransformer {

	@Autowired
	private MobConfigHelper mobConfigHelper;

	@Value("${thankyou.max.inclusions}")
    private int maxInclusionsThankyou;

	@Value("#{'${corp.segments}'.split(',')}")
	private Set<String> corpSegments;

	@Value("#{'${group.booking.review.page.cards}'.split(',')}")
	private List<String> groupBookingCardKeys;

	@Value("${trip.money.dark.logo.apps}")
	private String tripMoneyDarkLogoApps;

	@Value("${trip.money.dark.logo.web}")
	private String tripMoneyDarkLogoWeb;

	@Value("${trip.money.white.logo.apps}")
	private String tripMoneyWhiteLogoApps;

	@Value("${trip.money.white.logo.web}")
	private String tripMoneyWhiteLogoWeb;

	@Value("${trip.money.icon.apps}")
	private String tripMoneyIconApps;

	@Value("${trip.money.icon.web}")
	private String tripMoneyIconWeb;

	@Value("${tcs.info.review.url}")
	private String tcsInfoCardWebUrl;

	@Value("${bnpl.persuasion.gift.logo.web}")
	private String bnplLoggedOutIconUrl;

	@Value("${elite.package.icon.url}")
	private String elitePackageIconUrl;

	@Value("${elite.package.type}")
	private String elitePackageType;

	@Autowired
	private CommonResponseTransformer commonResponseTransformer;

	private Map<String,String> mealPlanMapPolyglot;
	private Map<String,Map<String,List<String>>> supplierToRateSegmentMapping;
	@Autowired
	PropertyManager propManager;

	@Autowired
	private Utility utility;

	@Autowired
	private DateUtil dateUtil;

	@Autowired
	protected PolyglotService polyglotService;

	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	MetricAspect metricAspect;

	Map<String, String> rtbCardConfigs;

	private boolean enablePanCardCheck;

	List<String> sameDayRoomNames;

	double payLaterCardLimit;

	private Map<String, CardData> reviewPageCards;

	@Value("${bnpl.active.booking.threshold}")
	private int bnplActiveBookingThreshold;

	private static final Logger LOGGER = LoggerFactory.getLogger(AvailRoomsResponseTransformer.class);

	protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);

	@PostConstruct
	public  void init(){
		CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
		mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
		commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
		rtbCardConfigs = commonConfig.rtbCardConfigs();
		commonConfig.addPropertyChangeListener("rtbCardConfigs", evt -> rtbCardConfigs = commonConfig.rtbCardConfigs());
		enablePanCardCheck = commonConfig.enablePanCardCheck();
		commonConfig.addPropertyChangeListener("enablePanCardCheck", evt -> enablePanCardCheck = commonConfig.enablePanCardCheck());
		sameDayRoomNames = commonConfig.sameDayRoomNames();
		commonConfig.addPropertyChangeListener("sameDayRoomNames", evt -> sameDayRoomNames = commonConfig.sameDayRoomNames());
		supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping();
		commonConfig.addPropertyChangeListener("supplierToRateSegmentMapping", event -> supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping());
		payLaterCardLimit = commonConfig.payLaterCardLimit();
		commonConfig.addPropertyChangeListener("payLaterCardLimit", evt -> payLaterCardLimit = commonConfig.payLaterCardLimit());
		reviewPageCards = commonConfig.reviewPageCards();
		commonConfig.addPropertyChangeListener("reviewPageCards", evt -> reviewPageCards = commonConfig.reviewPageCards());
	}

	public AvailRoomsResponse convertAvailRoomsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														String siteDomain, String checkIn, String checkOut, Map<String, String> expData, boolean allInclusions, String funnelSource,
														CommonModifierResponse commonModifierResponse, boolean showBnplCard, HotelImage hotelImage,boolean isCheckUpgrade, String selectedRoomCode) {
		return convertAvailRoomsResponse(roomDetailsResponse,hotelsRoomInfoResponseEntity, siteDomain, checkIn, checkOut, expData, allInclusions, funnelSource, commonModifierResponse, showBnplCard, hotelImage, null,false, isCheckUpgrade, selectedRoomCode, null);
	}

	public AvailRoomsResponse convertAvailRoomsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														String siteDomain, String checkIn, String checkOut, Map<String, String> expData, boolean allInclusions, String funnelSource,
														CommonModifierResponse commonModifierResponse, boolean showBnplCard, HotelImage hotelImage,
														String giHotelId,boolean showLuckyTimer, boolean isCheckUpgrade, String selectedRoomCode, String selectedRoomType) {
		AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
		long startTime = System.currentTimeMillis();
		try {
			Integer ap = StringUtils.isNotBlank(checkIn) ? dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn)) : null;
			if (roomDetailsResponse != null && CollectionUtils.isNotEmpty(roomDetailsResponse.getHotelRates())) {
				Map<String, Integer> roomBedCount = new HashMap<>();
				boolean blackRevamp = utility.isExperimentTrue(expData, GOTRIBE_REVAMP_POKUS_EXP_KEY);
				int los = StringUtils.isNotEmpty(checkIn) && StringUtils.isNotEmpty(checkOut) ? dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut)) : 1;
				boolean showTransfersFeeTxt  = false;
				roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, 0);
				roomBedCount.put(Constants.SELLABLE_BED_TYPE, 0);
				HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
				availRoomsResponse.setFeatureFlags(getFeatureFlags(hotelRates, expData, roomDetailsResponse.getCorpData() != null ? roomDetailsResponse.getCorpData() : null));
				availRoomsResponse.setHotelInfo(buildHotelInfo(hotelRates, checkIn, funnelSource, giHotelId));
				availRoomsResponse.setUpsellOptions(buildUpsellOptions(hotelRates));
				 if (LINKED_RATE_EXP_TWO_VARIANT.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, Constants.LINKED_RATE_EXPERIMENT_NR))) {
					availRoomsResponse.setDownsellOptions(buildDownsellOptions(hotelRates));
				 }
				availRoomsResponse.setDownsellOptionsAvailable(checkForAnyDownsellOptions(hotelRates));
				availRoomsResponse.setCancellationTimeline(hotelRates.getRoomTypeDetails() != null ?
						commonResponseTransformer.buildCancellationTimeline(hotelRates.getRoomTypeDetails().getCancellationTimeline()) : null);
				availRoomsResponse.setCancellationPolicyTimeline(hotelRates.getRoomTypeDetails() != null ?
						commonResponseTransformer.buildCancellationPolicyTimeline(hotelRates.getRoomTypeDetails().getCancellationTimeline()) : null);
				availRoomsResponse.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(hotelRates.getRoomTypeDetails().getPaymentPlan()));
				availRoomsResponse.setTcsWidgetInfo(hotelRates.getTcsWidgetInfo());
				availRoomsResponse.setTotalpricing(commonResponseTransformer.getTotalPricing(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown(), hotelRates.getCountryCode(),
						availRoomsResponse.getFeatureFlags().getPayMode(),
						isCorp(availRoomsResponse, hotelRates),
						commonResponseTransformer.getCorporateSegmentId(hotelRates.getRoomTypeDetails()),
						expData, Utility.isGroupBookingFunnel(funnelSource),hotelRates.isCbrAvailable(), hotelRates.getLoyaltyMessage(), hotelRates.getClmPersuasion(), null, null, 0.0,0.0));
				String pahText = polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_POLICY_TEXT_GENERIC);
				if (availRoomsResponse.getFeatureFlags() != null && availRoomsResponse.getFeatureFlags().getPayMode() != null)
					Utility.updatePayAtHotelText(availRoomsResponse.getTotalpricing(), availRoomsResponse.getFeatureFlags().getPayMode(), pahText);
				updateHotelierCurrencyPricingDetails(availRoomsResponse.getTotalpricing(), hotelRates.getRoomTypeDetails(), hotelRates.getRoomTypeDetails().getTotalDisplayFare().getConversionFactor(), hotelRates.getCurrencyCode(), availRoomsResponse.getFeatureFlags().getPayMode());
				populateOtherCoupons(hotelRates.getRoomTypeDetails(), availRoomsResponse);
				availRoomsResponse.getTotalpricing().setCurrency(hotelRates.getCurrencyCode());
				if(roomDetailsResponse.getUserCards() != null && roomDetailsResponse.getUserCards().size() > 0) {
					int tierNumber = hotelRates.getBlackInfo() != null && StringUtils.isNotBlank(hotelRates.getBlackInfo().getTierNumber()) ? Integer.parseInt(hotelRates.getBlackInfo().getTierNumber()) : 0;
					UserCard card = commonResponseTransformer.GetUserCardById(roomDetailsResponse.getUserCards(), COMMONS_CARD_TEMPLATE_ID, tierNumber);
					if(card != null)
						availRoomsResponse.setReviewCommonsCardData(card);
				}
				BNPLVariant bnplVariant = hotelRates.getRoomTypeDetails().getTotalDisplayFare().getBnplVariant();
				LOGGER.warn("BNPL Variant received from HES: {}", bnplVariant);
				if(bnplVariant == null || bnplVariant.equals(BNPLVariant.BNPL_NOT_APPLICABLE)) {
					if (commonModifierResponse != null && commonModifierResponse.getExpDataMap() != null && commonModifierResponse.getExpDataMap().get(Constants.EXP_BNPL_ZERO_VARIANT) != null && Constants.TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(EXP_BNPL_ZERO_VARIANT))) {
						bnplVariant = BNPLVariant.BNPL_AT_0;
					}
					else if (commonModifierResponse != null && commonModifierResponse.getExpDataMap() != null && commonModifierResponse.getExpDataMap().get(Constants.EXP_BNPL_NEW_VARIANT) != null && Constants.TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.EXP_BNPL_NEW_VARIANT))){
						bnplVariant = BNPLVariant.BNPL_AT_1;
					}
					else {
						bnplVariant = BNPLVariant.BNPL_NOT_APPLICABLE;
					}
					LOGGER.warn("BNPL Variant through CG: {}", bnplVariant);
				}
				boolean bnplDisabledDueToNonBnplCouponApplied  = commonResponseTransformer.checkIfInvalidCoupon(availRoomsResponse.getTotalpricing());

				BNPLDisabledReason bnplDisabledReason = null;
				if (commonModifierResponse != null && hotelRates.getIsBNPLAvailable()) {
					//Insurance/Gocash not applied in avail price
					bnplDisabledReason = commonResponseTransformer.getBNPLDisabledReason(roomDetailsResponse.isUserLevelBnplDisabled(),
							bnplDisabledDueToNonBnplCouponApplied, false, false);
				}
				String dormTypeStr = processDormTypes(hotelRates.getRoomTypeDetails());
				if(dormTypeStr != null && !dormTypeStr.isEmpty()){
					DormType dormType = new DormType();
					dormType.setDormTypePop(dormTypeStr);
					availRoomsResponse.setDormType(dormType);
				}

				//availRoomsResponse.setBnplDetails(getBnplDetails(hotelRates.getRoomTypeDetails(), showBnplCard, bnplDisabledReason,hotelRates.getCountryCode(),bnplVariant));
				availRoomsResponse.setRateplanlist(getRatePlanDetails(hotelRates.getRoomTypeDetails(), availRoomsResponse.getBnplDetails(),
						roomBedCount, ap, hotelRates.isUserGCCAndMmtExclusive(), expData,buildRoomImageMap(hotelImage), hotelRates.getListingType(),
						hotelRates.getLoyaltyMessage(), hotelRates.getClmPersuasion(), hotelRates.getBlackInfo(), hotelRates.getCountryCode(), hotelRates.getPropertyType()));
				if (!StringUtils.isNotEmpty(commonModifierResponse.getMmtAuth())) {
					 if (commonModifierResponse.getExpDataMap() != null &&
							commonModifierResponse.getExpDataMap().getOrDefault("disableBNPL", "").equalsIgnoreCase( FALSE)){
						 if (commonModifierResponse.getExpDataMap().getOrDefault(LOGGEDOUT_BNPL_WEB_EXP, "").equalsIgnoreCase(TRUE)) {
							 availRoomsResponse.setBnplDetails(getBnplDetails(hotelRates.getRoomTypeDetails(), showBnplCard, bnplDisabledReason,hotelRates.getCountryCode(),bnplVariant));
							 if (availRoomsResponse.getBnplDetails() != null)
								 availRoomsResponse.getBnplDetails().setLoggedOutBnplWebExp(true);
						 } else {
							 availRoomsResponse.setBnplDetails(getLoggedOutBnplDetails(hotelRates.getRoomTypeDetails()));
						 }
					 }
				}else{
					availRoomsResponse.setBnplDetails(getBnplDetails(hotelRates.getRoomTypeDetails(), showBnplCard, bnplDisabledReason,hotelRates.getCountryCode(),bnplVariant));
				}

				if (LINKED_RATE_EXP_TWO_VARIANT.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, Constants.LINKED_RATE_EXPERIMENT_NR)) && DOWNSELL.equalsIgnoreCase(selectedRoomType)) {
					if (availRoomsResponse.getRateplanlist() != null) {
						availRoomsResponse.getRateplanlist().stream()
								.filter(plan -> plan != null && plan.getCancellationPolicy() != null && BookedCancellationPolicyType.NR.equals(plan.getCancellationPolicy().getType()))
								.forEach(plan -> plan.getCancellationPolicy().setText("<s>Free Cancellation</s>"));
					}
				}

				if (hotelRates.getRoomTypeDetails() != null && availRoomsResponse.getBnplDetails() != null) {
					availRoomsResponse.getBnplDetails().setDetails(
							commonResponseTransformer.getPricingDetails(
									hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown(), hotelRates.getCountryCode(),
									availRoomsResponse.getFeatureFlags().getPayMode(), isCorp(availRoomsResponse, hotelRates),
									commonResponseTransformer.getCorporateSegmentId(hotelRates.getRoomTypeDetails()), expData,
									Utility.isGroupBookingFunnel(funnelSource), hotelRates.isCbrAvailable(), hotelRates.getLoyaltyMessage(),
									hotelRates.getClmPersuasion(), null, null, hotelRates.getRoomTypeDetails().getBnplExtraFees()
							,0.0)
					);
				}

				if(hotelRates.getRoomTypeDetails()!=null && hotelRates.getRoomTypeDetails().getFullPayment()!=null) {
					availRoomsResponse.setFullPayment(utility.buildFullPayment(hotelRates.getRoomTypeDetails().getFullPayment(),
							hotelRates.getRoomTypeDetails().getBnplExtraFees(), 0d));
				}

				availRoomsResponse.setAddons(commonResponseTransformer.getAddons(hotelRates.getAddOns()));
				availRoomsResponse.setSpecialrequests(buildSpecialRequests(hotelRates.getSpecialRequestAvailable()));
				availRoomsResponse.setGstInfo(getGstInfo(hotelRates.getGstin(), hotelRates.getCountryCode(), siteDomain, hotelRates.getGstStateCode(), hotelRates.getRoomTypeDetails()));
				availRoomsResponse.setTcsInfo(getTcsInfoCard(siteDomain, hotelRates.getCountryCode(), hotelRates.isPanCardRequired(), hotelRates.getUserEligiblePayMode()));
				availRoomsResponse.setPanInfo(getPanInfo(hotelRates.isPanCardRequired(), hotelRates.isPnAvlbl()));
				availRoomsResponse.setEmiDetails(commonResponseTransformer.getEmiAbridgeDetails(hotelRates.getEmiDetails()));
				availRoomsResponse.setDoubleBlackInfo(commonResponseTransformer.getDoubleBlackInfo(hotelRates.getDoubleBlackInfo()));
				availRoomsResponse.setMsmeCorpCard(hotelRates.getMsmeCorpCard());
				availRoomsResponse.setBlackInfo(commonResponseTransformer.buildBlackInfo(hotelRates.getBlackInfo()));
				// soldOutCallOut is sent instread of alerts in all new Apps.
				if(!isAlertsNodeEnabled(Constants.DOM_COUNTRY.equalsIgnoreCase(hotelRates.getCountryCode()), commonModifierResponse.getFlavour(), commonModifierResponse.getAppVersionIntGi()))
					availRoomsResponse.setSoldOutCallOut(buildSoldOutCallOutInfo(hotelRates, availRoomsResponse));
				availRoomsResponse.setTxnKey(roomDetailsResponse.getTxnKey());
				availRoomsResponse.setSafetyPersuasionMap(commonResponseTransformer.buildSafetyPersuasionList(availRoomsResponse.getHotelInfo().getCategories()));
				availRoomsResponse.getTotalpricing().setAffiliateFeeDetails(commonResponseTransformer.buildAffiliateFeeDetails(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getAffiliateFeeOptions()));
				buildPropertyRules(availRoomsResponse, hotelRates);
				availRoomsResponse.setIntlRoamingInfo(hotelRates.getIntlRoamingInfo());
				//flag to decide whether to show transfer fee text or not
				if (MapUtils.isNotEmpty(expData) && expData.containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
					showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expData.get(Constants.TRANSFERS_FEE_TEXT_KEY));
				}
				buildAdditionalCharges(availRoomsResponse, hotelRates, showTransfersFeeTxt);
				//AlertTypeVariant - AlertMessage will be updated basis Pokus experiment key alert_type_variant.
//				boolean alertTypeVariant =  MapUtils.isNotEmpty(expData) && StringUtils.isNotEmpty(expData.get(ALERT_TYPE_VARIANT)) && Constants.TRUE.equalsIgnoreCase(expData.get(ALERT_TYPE_VARIANT));
				//GIHTL-15565 Clean Up For Alert Type Variant
				if(isAlertsNodeEnabled(Constants.DOM_COUNTRY.equalsIgnoreCase(hotelRates.getCountryCode()), commonModifierResponse.getFlavour(), commonModifierResponse.getAppVersionIntGi())) { // For international we are blocking alerts to stop showing alerts in price summary section.
					buildAlerts(availRoomsResponse, hotelRates, false);
				}
				updateCampaingAlert(availRoomsResponse, hotelRates);
				availRoomsResponse.setPriceChangeInfo(getPriceChangeInfo(hotelRates));
				if ("android".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || "ios".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
					availRoomsResponse.getHotelInfo().setApplicableHotelCategoryData(commonResponseTransformer.buildHotelCategoryDataMap(availRoomsResponse.getHotelInfo().getCategories()));
				} else {
					availRoomsResponse.getHotelInfo().setApplicableHotelCategoryDataWeb(commonResponseTransformer.buildHotelCategoryDataWeb(availRoomsResponse.getHotelInfo().getCategories()));
				}

				updateInclusions(availRoomsResponse, allInclusions, expData);
				buildCorpApprovalInfo(availRoomsResponse, hotelRates);
				buildApprovingManagers(availRoomsResponse, hotelRates);
				if (availRoomsResponse != null && availRoomsResponse.getCorpApprovalInfo() != null && availRoomsResponse.getCorpApprovalInfo().isWalletQuickPayAllowed()
						&& hotelRates != null && hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null
						&& hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
					availRoomsResponse.setMyBizQuickPayConfig(commonResponseTransformer.buildMyBizQuickPayConfig(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown()));
				}

				int totalNumOfRooms = 0;
				if (availRoomsResponse != null && availRoomsResponse.getRateplanlist() != null) {
					totalNumOfRooms = availRoomsResponse.getRateplanlist().stream()
							.filter(Objects::nonNull) // Check for null ratePlan
							.filter(ratePlan -> ratePlan.getOccupancydetails() != null)
							.filter(ratePlan -> ratePlan.getOccupancydetails().getNumOfRooms() != null)
							.mapToInt(ratePlan -> ratePlan.getOccupancydetails().getNumOfRooms())
							.sum();
				}

				Tuple<String, String> guestKeyValueTuple = utility.getGuestRoomKeyValue(roomBedCount, availRoomsResponse.getHotelInfo().getPropertyLabel(), availRoomsResponse.getHotelInfo().getListingType(), roomDetailsResponse.isServiceApartment(), totalNumOfRooms);
				availRoomsResponse.getHotelInfo().setGuestRoomKey(guestKeyValueTuple.getX());
				Boolean entireProperty = hotelRates.isEntireProperty();
				if (Boolean.TRUE.equals(entireProperty)) {
					availRoomsResponse.getHotelInfo().setGuestRoomValue(totalNumOfRooms > 1 && hotelRates.isEntireProperty() ? String.valueOf(totalNumOfRooms) + " " + guestKeyValueTuple.getY() : guestKeyValueTuple.getY());
				}
				if (hotelRates.isRequestToBook() && !hotelRates.isRtbPreApproved()) {
					TripDetailsCard tripDetailsCard = new TripDetailsCard();
					tripDetailsCard.setActionText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_ACTION_TEXT));
					tripDetailsCard.setHeaderSubText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_HEADER_SUB_TEXT));
					tripDetailsCard.setHeaderText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_HEADER_TEXT));
					tripDetailsCard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_TITLE));
					List<String> texts = new ArrayList<>();
					texts.add(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_TEXT1));
					texts.add(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_TEXT2));
					tripDetailsCard.setTexts(texts);
					tripDetailsCard.setActionUrl(rtbCardConfigs.get("rtbTripDetailsActionUrl"));
					availRoomsResponse.setTripDetailsCard(tripDetailsCard);
				}
				availRoomsResponse.setHydraSegments(roomDetailsResponse.getHydraSegments());
				availRoomsResponse.setFlexibleCheckinInfo(roomDetailsResponse.getFlexibleCheckinInfo());
				availRoomsResponse.setTcClauseDetails(buiildTCClauseDetails(hotelRates.getRoomTypeDetails()));
				if(roomDetailsResponse.getCorpData() != null){
					availRoomsResponse.setCorpData(buildCorpData(roomDetailsResponse.getCorpData().isCorpCapturePersonalBookingGstn()));
				}
				if(null!=availRoomsResponse.getFeatureFlags() && null!=availRoomsResponse.getFeatureFlags().getPayLaterCard() && availRoomsResponse.getFeatureFlags().getPayLaterCard()){
					availRoomsResponse.setPayLaterCard(buildPayLaterCard());
				}
				if(roomDetailsResponse.getHotelRates().get(0).getUserLoyaltyStatus()!=null)
					availRoomsResponse.setUserLoyaltyStatus(roomDetailsResponse.getHotelRates().get(0).getUserLoyaltyStatus());
				if(CollectionUtils.isNotEmpty(roomDetailsResponse.getPaxDetailsList())){
					availRoomsResponse.setPaxDetails(roomDetailsResponse.getPaxDetailsList());
				}
				availRoomsResponse.setCardData(buildReviewPageCardData(funnelSource, hotelRates,commonModifierResponse, roomDetailsResponse.getAffiliateId()));

				Long mpFareHoldExpiry = getMpFareHoldMinExpiry(hotelRates);
				if(mpFareHoldExpiry!=null) {
					int mpFareHoldBookingAmount =
							(int)(hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().get().
									getRatePlanList().values().stream().findFirst().get().getMpFareHoldStatus().getBookingAmount());
					availRoomsResponse.setBookNowDetails(getBookNowDetails(mpFareHoldBookingAmount,mpFareHoldExpiry));
				}
				availRoomsResponse.setHotelPersuasions(buildHotelPersuasions(hotelRates,mpFareHoldExpiry!=null,commonModifierResponse));
				if(UGCV2_TRUE_VALUE.equalsIgnoreCase(expData.get(UGCV2))){
					availRoomsResponse.setUgcSummary(buildUgcSummary(roomDetailsResponse.getHotelRates().get(0).getPlatformSummaryResponse()));
				}
				availRoomsResponse.setReviewSummaryGI(roomDetailsResponse.getHotelRates().get(0).getReviewSummaryGI()!=null ? roomDetailsResponse.getHotelRates().get(0).getReviewSummaryGI().getGiSummary() : null);
				if (utility.checkIfGoStaysProperty(roomDetailsResponse) && commonModifierResponse.getStreaksUserInfoResponse() != null && (roomDetailsResponse.getLuckyUserContext() ==null || roomDetailsResponse.getLuckyUserContext() == LuckyUserContext.UNLUCKY)) {
					availRoomsResponse.setUserStreaksInfoResponse(utility.fetchReviewDetailsFromStreaksResp(commonModifierResponse.getStreaksUserInfoResponse()));
					availRoomsResponse.getHotelInfo().setGoStay(true);
				}
				if(!Utility.gocashVariant(expData, StringUtils.equalsIgnoreCase(hotelRates.getCountryCode(), DOM_COUNTRY))){
					availRoomsResponse.setClmPersuasion(roomDetailsResponse.getHotelRates().get(0).getClmPersuasion());
				}

				if (utility.isSPKGExperimentOn(expData) && anyPackageRatePlanAvailable(availRoomsResponse.getRateplanlist()) && availRoomsResponse.getHotelInfo() != null) {
					availRoomsResponse.getHotelInfo().setPackageTagUrl(elitePackageIconUrl);
				}

				availRoomsResponse.setGoCashDetails(roomDetailsResponse.getHotelRates().get(0).getGoCashDetails());
				availRoomsResponse.setLoyaltyMessage(roomDetailsResponse.getHotelRates().get(0).getLoyaltyMessage());

				availRoomsResponse.setFooterStrip(commonResponseTransformer.buildFooterStrip(roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails(),
						roomDetailsResponse.getHotelRates().get(0).getLoyaltyMessage(), roomDetailsResponse.getHotelRates().get(0).getClmPersuasion(), roomDetailsResponse.getHydraSegments(), expData, roomDetailsResponse.getHotelRates().get(0).getBlackInfo(), StringUtils.equalsIgnoreCase(hotelRates.getCountryCode(), DOM_COUNTRY)));

				if(showLuckyTimer && roomDetailsResponse.getLuckyCouponStartTime() != null
						&& roomDetailsResponse.getLuckyCouponEndTime() != null) {
					LuckyUserDetails luckyUserDetails = new LuckyUserDetails();
					luckyUserDetails.setCurrent(roomDetailsResponse.getLuckyCouponStartTime());
					luckyUserDetails.setEnd(roomDetailsResponse.getLuckyCouponEndTime());
					availRoomsResponse.setLuckyUserDetails(luckyUserDetails);
					/*Setting Lucky Data V2*/
					LuckyData luckyDataV2 = new LuckyData();
					luckyDataV2.setLuckyProperty(true);
					luckyDataV2.setLuckyPersuasion(polyglotService.getTranslatedData(GI_LUCKY_TIMER_TEXT));
					luckyDataV2.setLuckyUserDetails(availRoomsResponse.getLuckyUserDetails());
					availRoomsResponse.setLuckyDataV2(luckyDataV2);
				}
				//Black Upgrade Info
				if (blackRevamp && hotelRates.getBlackInfo() != null) {
					BlackBenefits blackBenefits = getBlackBenefits(hotelRates);
					if (isCheckUpgrade && hotelRates.getUpgradeInfo() != null) availRoomsResponse.setRateplansUpgrade(commonResponseTransformer.prepareUpgradeInfo(hotelRates, availRoomsResponse.getTotalpricing(), los, blackBenefits, selectedRoomCode));
					availRoomsResponse.getRateplanlist().forEach(ratePlan -> ratePlan.setTagInfo(commonResponseTransformer.prepareTagInfo(blackBenefits)));
				}
				if(commonModifierResponse != null) {
					availRoomsResponse.setExpData(commonModifierResponse.getExpDataMap());
					availRoomsResponse.setVariantKey(commonModifierResponse.getVariantKey());
				}

				// Sale Campaign Persuasion.
				if (utility.isB2CFunnel()) {
					PersuasionResponse salePersuasion = commonResponseTransformer.getSaleCampaignPersuasion(
							hotelRates.getCampaignPojo());
					if (null != salePersuasion) {
						Map<String, PersuasionResponse> persuasionMap = null != availRoomsResponse.getHotelPersuasions()
								? availRoomsResponse.getHotelPersuasions() : new HashMap<>();
						persuasionMap.put(SALE_CAMPAIGN, salePersuasion);
						availRoomsResponse.setHotelPersuasions(persuasionMap);
					}
				}
				updateCouponsAvailabilityText(availRoomsResponse);
			}
		}finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_REVIEW_RESPONSE_PROCESS, REVIEW_AVAIL_ROOMS, System.currentTimeMillis() - startTime);
		}
    	return availRoomsResponse;
    }

	private void updateCouponsAvailabilityText(AvailRoomsResponse availRoomsResponse) {
		BNPLDetails bnplDetails = availRoomsResponse.getBnplDetails();
		HCouponApplicableTextCreator couponApplicableTextCreator = new HCouponApplicableTextCreator();
		TotalPricing totalPricing = availRoomsResponse.getTotalpricing();
		List<Coupon> couponsList = totalPricing.getCoupons();
		if (couponsList != null && !couponsList.isEmpty()) {
			couponsList.forEach(coupon -> {
				String couponApplicabilityText = couponApplicableTextCreator.getText(bnplDetails, coupon.isGiftCardAllowed(), coupon.isBnplAllowed(), polyglotService);
				coupon.setCouponApplicabilityText(couponApplicabilityText);
			});
		}
	}

	private boolean checkForAnyDownsellOptions(HotelRates hotelRates) {
		if (hotelRates == null || hotelRates.getRoomTypeDetails() == null || MapUtils.isEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			return false;
		}

		return hotelRates.getRoomTypeDetails().getRoomType().values().stream()
				.findFirst()
				.map(roomType -> roomType.getRatePlanList().values().stream()
						.filter(ratePlan -> ratePlan.getUpsellOptions() != null)
						.flatMap(ratePlan -> ratePlan.getUpsellOptions().stream())
						.anyMatch(upsellOptions -> DOWNSELL.equalsIgnoreCase(upsellOptions.getRatePlanType())))
				.orElse(false);
	}

	private boolean anyPackageRatePlanAvailable(List<RatePlan> ratePlanList) {
		if(CollectionUtils.isNotEmpty(ratePlanList)) {
			for(RatePlan ratePlan:ratePlanList) {
				if(ratePlan.isPackageRateAvailable()) {
					return true;
				}
			}
		}
		return false;
	}

	private BlackBenefits getBlackBenefits(HotelRates hotelRates) {
		BlackBenefits blackBenefits = null;
		if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			RoomType roomType = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().orElse(new RoomType());
			if (MapUtils.isNotEmpty(roomType.getRatePlanList())) {
				blackBenefits = roomType.getRatePlanList().values().stream().findFirst().orElse(new com.mmt.hotels.model.response.pricing.RatePlan()).getBlackBenefits();
			}
		}
		return blackBenefits;
	}

	private boolean isAlertsNodeEnabled(boolean isDomesticRequest, String flavour, String appVersionGI) {
		if(isDomesticRequest)
			return true;
		// For Web platforms we are not sending alerts node. SoldOutCallOut is only used.
		if(!(flavour.equalsIgnoreCase(DEVICE_IOS) || flavour.equalsIgnoreCase(ANDROID)))
			return false;
		// App version check here.
		int flavourIntValue = Integer.parseInt(appVersionGI);
		if((flavour.equalsIgnoreCase(DEVICE_IOS) && flavourIntValue < Constants.IOS_APP_VER_FOR_SOLDOUT_CALLOUT) || (flavour.equalsIgnoreCase(ANDROID) && flavourIntValue < Constants.ANDROID_APP_VER_FOR_SOLDOUT_CALLOUT))
			return true;
		return false;
	}
	/**
	 * This method will return the TCS card data basis country is international and region is India
	 *
	 * @param domain
	 * @param countryCode
	 * @param panCardRequired
	 * @param userEligiblePayMode
	 * @return
	 */
	private TcsInfo getTcsInfoCard(String domain, String countryCode, boolean panCardRequired, PaymentMode userEligiblePayMode) {
		TcsInfo tcsInfo = null;
		String tcsApplicableInfoTextReview = polyglotService.getTranslatedData(ConstantsTranslation.TCS_APPLICABLE_TEXT_IH);
		if (StringUtils.isNotEmpty(tcsApplicableInfoTextReview) && utility.isIHFunnel(countryCode, domain)
				&& panCardRequired && userEligiblePayMode == PaymentMode.PAS) {
			tcsInfo = new TcsInfo();
			tcsInfo.setMessage(tcsApplicableInfoTextReview);
			tcsInfo.setWebUrl(tcsInfoCardWebUrl);
			tcsInfo.setCta(polyglotService.getTranslatedData(ConstantsTranslation.TCS_APPLICABLE_CTA_IH));
			tcsInfo.setTitle("Learn more about TCS");
		}
		return tcsInfo;
	}

	private Map<String,PersuasionResponse> buildHotelPersuasions(HotelRates hotelRates, boolean isMpFareHoldEligible,CommonModifierResponse commonModifierResponse) {

		Map<String,PersuasionResponse> persuasionMap = new HashMap<>();
		if(isMpFareHoldEligible) {
			int bookingValue = (int)(
					hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().get().
							getRatePlanList().values().stream().findFirst().get().getMpFareHoldStatus().getBookingAmount());

			//handles all null check to evaluate isMpFareHoldEligible
			Long expiry = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().get().
					getRatePlanList().values().stream().findFirst().get().getMpFareHoldStatus().getExpiry();

			PersuasionResponse fareHoldPersuasion = new PersuasionResponse();
			fareHoldPersuasion.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_TITLE),
					String.valueOf(bookingValue)));
			Hover hover = new Hover();
			if(expiry!=null) {
				hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
						dateUtil.convertEpochToDateTime(expiry, dateUtil.DD_MMM_hh_mm_a)));
			}
			hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));
			fareHoldPersuasion.setHover(hover);
			persuasionMap.put(Constants.BOOK_NOW_PERSUASION_KEY, fareHoldPersuasion);
		}
		if (!StringUtils.isNotEmpty(commonModifierResponse.getMmtAuth()) && commonModifierResponse.getExpDataMap() != null
				&& commonModifierResponse.getExpDataMap().getOrDefault("disableBNPL", "").equalsIgnoreCase(FALSE)) {
			PersuasionResponse loggedInBnplPersuasion = new PersuasionResponse();
			loggedInBnplPersuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.NON_BNPL_LOGGEDIN_PERSUASION));
			loggedInBnplPersuasion.setIconUrl(bnplLoggedOutIconUrl);
			Style style = new Style();
			style.setBgColor("#e9f6ea");
			style.setTextColor("#2274e0");
			loggedInBnplPersuasion.setTemplate("2");
			loggedInBnplPersuasion.setStyle(style);
			loggedInBnplPersuasion.setPlaceholderId("loginPer");
			if (!commonModifierResponse.getExpDataMap().getOrDefault(LOGGEDOUT_BNPL_WEB_EXP, "").equalsIgnoreCase(TRUE)) {
				if (hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare().getIsBNPLApplicable()) {
					loggedInBnplPersuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.BNPL_ONE_LOGGEDIN_PERSUASION));

					if (BNPLVariant.BNPL_AT_0.equals(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getBnplVariant())) {
						loggedInBnplPersuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.BNPL_ZERO_LOGGEDIN_PERSUASION));
					}
				}
			}
			persuasionMap.put(LOG_IN_PERSUASION_KEY, loggedInBnplPersuasion);

		}

		//build cashback offer persuasion/Hero offer persuasion for review page myPartner funnel
		boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		if(isMyPartnerRequest) {
			if (null != hotelRates.getRoomTypeDetails() && null != hotelRates.getRoomTypeDetails().getTotalDisplayFare() && null !=  hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() && null != hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()) {
				BestCoupon coupon = hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo();
				//If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
				LOGGER.debug("Manthan HybridDiscounts {}",coupon.getHybridDiscounts());
				boolean isCashbackAmtAvailable= org.apache.commons.collections.MapUtils.isNotEmpty(coupon.getHybridDiscounts()) && coupon.getHybridDiscounts().containsKey("CTW");
				if (StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable) {
					buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
				}
			}
		}
		//GIHTL-15995 build taj gift card or hotel credit persuasion for review page
		boolean isTajGiftCardOrHotelCreditAvailable = hotelRates.getHotelBenefitInfo()!=null && (GIFT_CARD_BENEFIT_TYPE.equalsIgnoreCase(hotelRates.getHotelBenefitInfo().getBenefitType()) ||
				HOTEL_CREDIT_BENEFIT_TYPE.equalsIgnoreCase(hotelRates.getHotelBenefitInfo().getBenefitType()));
		if(isTajGiftCardOrHotelCreditAvailable) {
			PersuasionResponse tajGiftCardPersuasion = commonResponseTransformer.buildTajGiftCardOrHotelCreditPersuasion(hotelRates.getHotelBenefitInfo());
			persuasionMap.put(TAJ_CAMPAIGN_PERSUASION_KEY, tajGiftCardPersuasion);
		}
		return persuasionMap;
	}
	private BookNowDetails getBookNowDetails(int bookingValue,Long mpFareHoldExpiry) {

		if(mpFareHoldExpiry!=null) {
			BookNowDetails details = new BookNowDetails();
			details.setBookNowAmount(bookingValue);
			details.setExpiry(mpFareHoldExpiry);
			return details;
		}
		return null;
	}

	/**
	 *
	 * @param hotelRates
	 *
	 * if we have multi-room flow we need to check expiry value for each rateplan
	 * and need to return restrictive expiry date
	 *
	 * Return null for non-refundable policy
	 * Return epoch Long value for refundable policy
	 *
	 * if any ratePlan is non-refundable we need to return null
	 * If all ratePlans are refundable we need to return min expiry value
	 * @return Restrictive Expiry value
	 */
	private Long getMpFareHoldMinExpiry(HotelRates hotelRates ) {

		Long mpFareHoldExpiry = null;
		if(hotelRates!=null && hotelRates.getRoomTypeDetails()!=null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			for(RoomType roomtype: hotelRates.getRoomTypeDetails().getRoomType().values()) {
				if(MapUtils.isNotEmpty(roomtype.getRatePlanList())) {
					for(com.mmt.hotels.model.response.pricing.RatePlan ratePlan: roomtype.getRatePlanList().values()) {
						if (ratePlan.getMpFareHoldStatus() != null) {
							if (ratePlan.getMpFareHoldStatus().getExpiry() == null || (!ratePlan.getMpFareHoldStatus().isHoldEligible())) {
								return null;
							}
							if(mpFareHoldExpiry==null) {
								//If any ratePlan has null value for Expiry we should return null as BookNow@0/1 flow is not eligible
								mpFareHoldExpiry = ratePlan.getMpFareHoldStatus().getExpiry();
							} else {
								//Get minValue(restrictive Value) (minDate/ minEpoch) to return
								mpFareHoldExpiry = min(mpFareHoldExpiry, ratePlan.getMpFareHoldStatus().getExpiry());
							}
						} else {
							//If one of rate-plan is non-refundable return null as BookNow@0/1 flow is not eligible
							return null;
						}
					}
				}
			}
		}
		return mpFareHoldExpiry;
	}
	private PayLaterCard buildPayLaterCard() {
		PayLaterCard payLaterCard = new PayLaterCard();
		payLaterCard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_TITLE));
		payLaterCard.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUBTITLE));
		payLaterCard.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_CTA_TEXT));
		if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
			payLaterCard.setLogo(tripMoneyWhiteLogoWeb);
			payLaterCard.setTitleIcon(tripMoneyIconWeb);
		}else{
			payLaterCard.setLogo(tripMoneyWhiteLogoApps);
			payLaterCard.setTitleIcon(tripMoneyIconApps);
		}
		return payLaterCard;
	}

	private List<CardData> buildReviewPageCardData(String funnelSource, HotelRates hotelRates, CommonModifierResponse commonModifierResponse,
												   String affiliateId) {
		List<CardData> reviewCards = new ArrayList<>();
		if (MapUtils.isNotEmpty(reviewPageCards)) {
			addGroupBookingCards(funnelSource, reviewCards, hotelRates, commonModifierResponse);
		}
		CardData businessIdentificationCard = commonResponseTransformer.buildBusinessIdentificationCards(hotelRates, affiliateId);
		if (businessIdentificationCard != null) {
			reviewCards.add(businessIdentificationCard);
		}
		return reviewCards;
	}

	/**
	 * * This method is used to make Group booking Cards In this one POST_BOOKING_CARD is not build in the case of MyPartner and Group Funnel
	 * @param funnelSource
	 * @param reviewCards
	 * @param hotelRates
	 * @param commonModifierResponse
	 */
	private void addGroupBookingCards(String funnelSource, List<CardData> reviewCards, HotelRates hotelRates,CommonModifierResponse commonModifierResponse ){
		boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		if (utility.isGroupBookingFunnel(funnelSource) && hotelRates != null && hotelRates.isGroupBookingHotel()) {
			groupBookingCardKeys.forEach(groupBookingCardKey -> {
				if(reviewPageCards.get(groupBookingCardKey) != null && isMyPartnerRequest  && !Constants.POST_BOOKING_CARD.equalsIgnoreCase(groupBookingCardKey)){
						reviewCards.add(reviewPageCards.get(groupBookingCardKey));
					}
				else if(reviewPageCards.get(groupBookingCardKey) != null && !isMyPartnerRequest) {
					reviewCards.add(reviewPageCards.get(groupBookingCardKey));
				}
			});
		}
	}


	private TCClauseDetails buiildTCClauseDetails(RoomTypeDetails roomTypeDetails)
	{
		SupplierDetails supplierDetails = getSupplierDetails(roomTypeDetails);
		String segmentId = getSegmentId(roomTypeDetails);
		String supplierCode = supplierDetails.getSupplierCode();

		if(supplierToRateSegmentMapping!=null && supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY)!=null &&
				supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).get(supplierCode)!=null
				&& (supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).get(supplierCode).isEmpty()
				|| supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).get(supplierCode).contains(segmentId))) {

			TCClauseDetails tcclauseDetails = new TCClauseDetails();
			tcclauseDetails.setClause(polyglotService.getTranslatedData(ConstantsTranslation.TC_CLAUSE_TEXT));
			tcclauseDetails.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.TC_HEADING_TEXT));
			tcclauseDetails.setSubHeading(polyglotService.getTranslatedData(ConstantsTranslation.TC_SUBHEADING_TEXT));

			return tcclauseDetails;
		}

		return null;
	}

	private void updateCampaingAlert(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates) {
		boolean setCampaignAlert = true;
		String campaignText = null;
		for(RatePlan ratePlan : availRoomsResponse.getRateplanlist()){
			String roomCode = ratePlan.getRoomCode();
			String ratePlanCode = ratePlan.getCode();
			if(null != ratePlan.getCancellationPolicy() && BookedCancellationPolicyType.FC == ratePlan.getCancellationPolicy().getType()) {
				campaignText = hotelRates.getRoomTypeDetails().getRoomType().get(roomCode).getRatePlanList().get(ratePlanCode).getCampaingText();
			}else{
				setCampaignAlert = false;
				break;
			}
		}
		if(setCampaignAlert && StringUtils.isNotBlank(campaignText)){
			Alert alert = new Alert();
			alert.setText(campaignText);
			alert.setType(AlertType.FREE_CANC_CAMPAIGN);
			availRoomsResponse.setCampaignAlert(alert);
		}
	}


	private boolean isCorp(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates){
		return availRoomsResponse!=null && hotelRates.getRoomTypeDetails()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCorpMetaData()!=null;
	}

	private void updateInclusions(AvailRoomsResponse availRoomsResponse , boolean isAllInclusion, Map<String, String> expData){
		if(availRoomsResponse != null && CollectionUtils.isNotEmpty(availRoomsResponse.getRateplanlist()) && !isAllInclusion) {
			for(RatePlan ratePlan : availRoomsResponse.getRateplanlist()) {
				List<BookedInclusion> inclusions = ratePlan.getInclusionsList();
				if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusions) && inclusions.size() > maxInclusionsThankyou) {
					inclusions.sort(Comparator.comparing(i -> i.getSegmentIdentifier(), Comparator.nullsLast(Comparator.naturalOrder())));
					inclusions = inclusions.subList(0, maxInclusionsThankyou);
					ratePlan.setInclusionsList(inclusions);
				}
				if (StringUtils.isNotBlank(ratePlan.getMealCode())) {
					/* This will duplicate strings with mealPlan code : but added on Product's request (HTL-29300) */
					BookedInclusion i = new BookedInclusion();
					i.setText(utility.getRatePlanName(getMealPlanList(ratePlan.getMealCode()),ratePlan.getCancellationPolicy(),ratePlan.getSellableType(),availRoomsResponse.getHotelInfo().getListingType(),expData));
					i.setCode(i.getText());
					i.setIconType(IconType.DEFAULT);
					if (CollectionUtils.isEmpty(ratePlan.getInclusionsList())) {
						ratePlan.setInclusionsList(new ArrayList<>());
					}
					if(ratePlan.getInclusionsList().size() > 1)
						ratePlan.getInclusionsList().add(1,i);
					else
						ratePlan.getInclusionsList().add(0,i);
				}
			}
		}
	}

	private void buildApprovingManagers(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates) {

		if (availRoomsResponse!=null && hotelRates.getApprovingManagers()!=null && CollectionUtils.isNotEmpty(hotelRates.getApprovingManagers())){
			availRoomsResponse.setApprovingManagers(commonResponseTransformer.buildManagers(hotelRates.getApprovingManagers()));
		}
	}

	private void buildCorpApprovalInfo(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates) {

		if (availRoomsResponse!=null && hotelRates.getRoomTypeDetails()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCorpMetaData()!=null){
			availRoomsResponse.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCorpMetaData()));
		}
	}

	private void populateOtherCoupons(RoomTypeDetails roomTypeDetails, AvailRoomsResponse availRoomsResponse) {
		String payMode = getRatePlanPayMode(roomTypeDetails);
		if (availRoomsResponse.getTotalpricing() != null) {
			if (roomTypeDetails.getTotalDisplayFare() != null &&
					MapUtils.isNotEmpty(roomTypeDetails.getTotalDisplayFare().getOtherCouponByPaymode()) &&
					roomTypeDetails.getTotalDisplayFare().getOtherCouponByPaymode().containsKey(payMode)) {
				List<BestCoupon> otherCoupons = roomTypeDetails.getTotalDisplayFare().getOtherCouponByPaymode().get(payMode);
				if (CollectionUtils.isNotEmpty(otherCoupons)) {
					otherCoupons.stream().forEach(coupon -> {
						Coupon otherCoupon = new Coupon();
						otherCoupon.setCode(coupon.getCouponCode());
						otherCoupon.setCouponAmount(coupon.getDiscountAmount() != null ? coupon.getDiscountAmount() : 0.0);
						otherCoupon.setDescription(coupon.getDescription());
						otherCoupon.setBnplAllowed(coupon.getBnplAllowed() != null ? coupon.getBnplAllowed() : false);
						otherCoupon.setTncUrl(coupon.getTncUrl());
						otherCoupon.setAutoApplicable(coupon.isAutoApplicable());
						otherCoupon.setDisabled(coupon.isDisabled());
						otherCoupon.setPromoIconLink(coupon.getPromoIconLink());
						otherCoupon.setSuccessMessage(coupon.getSuccessMessage());
						otherCoupon.setGiftCardAllowed(coupon.getGiftCardAllowed());
						if (CollectionUtils.isEmpty(availRoomsResponse.getTotalpricing().getCoupons()))
							availRoomsResponse.getTotalpricing().setCoupons(new ArrayList<Coupon>());
						availRoomsResponse.getTotalpricing().getCoupons().add(otherCoupon);
					});
				}
				if (CollectionUtils.isNotEmpty(availRoomsResponse.getTotalpricing().getCoupons())) {
					availRoomsResponse.getTotalpricing().setNoCouponText(null);
				}
			}
    	}
    }

	private HotelResult buildHotelInfo(HotelRates hotelRates, String checkIn, String funnelSource, String giHotelId) {
		HotelResult hotelInfo = new HotelResult();
		hotelInfo.setHotelIcon(hotelRates.getHotelIcon());
		hotelInfo.setAltAcco(hotelRates.isAltAcco());
		hotelInfo.setName(hotelRates.getName());
		hotelInfo.setStarRating(hotelRates.getStarRating());
		hotelInfo.setPropertyType(hotelRates.getPropertyType());
		hotelInfo.setPropertyLabel(hotelRates.getPropertyLabel());
		hotelInfo.setLat(hotelRates.getLat());
		hotelInfo.setLng(hotelRates.getLng());
		hotelInfo.setCityName(hotelRates.getCityName());
		//BedInfoText, It is added to summarize the bed types and count for property Layout
		Map<String, RoomInfo> roomInfoMap = hotelRates.getRoomInfo();
		List<RoomInfo> roomInfoList = MapUtils.isNotEmpty(roomInfoMap) ? roomInfoMap.values().stream()
				.filter(roomInfo -> StringUtils.isNotBlank(roomInfo.getBedInfoText())).collect(Collectors.toList()) : new ArrayList<>();
		Boolean entireProperty = hotelRates.isEntireProperty();
		if (Boolean.TRUE.equals(entireProperty)) {
			if(StringUtils.isNotBlank(hotelRates.getBedInfoText())){
				hotelInfo.setBedInfoText(hotelRates.getBedInfoText());
			}else if(CollectionUtils.isNotEmpty(roomInfoList) && roomInfoList.get(0).getBedRoomCount()!=null) {
				String s = (roomInfoList.get(0).getBedRoomCount().equals("1")) ? roomInfoList.get(0).getBedRoomCount() + " "+polyglotService.getTranslatedData(ConstantsTranslation.BEDROOM) : roomInfoList.get(0).getBedRoomCount() + " "+polyglotService.getTranslatedData(ConstantsTranslation.BEDROOMS);
				hotelInfo.setBedInfoText(s + " | " + roomInfoList.get(0).getBedInfoText());
			}
			else if(CollectionUtils.isNotEmpty(roomInfoList)){
				hotelInfo.setBedInfoText(roomInfoList.get(0).getBedInfoText());
			}
		}
		hotelInfo.setCountryName(hotelRates.getCountryName());
		hotelInfo.setAddress(getAddress(hotelRates.getAddressLines()));
		hotelInfo.setCheckinTime(hotelRates.getCheckInTime());
		hotelInfo.setCheckoutTime(hotelRates.getCheckOutTime());
		if (!hotelRates.getCheckInTime().equalsIgnoreCase(hotelRates.getCheckInTimeRange()) || !hotelRates.getCheckOutTime().equalsIgnoreCase(hotelRates.getCheckOutTimeRange())) {
			hotelInfo.setCheckinTimeRange(hotelRates.getCheckInTimeRange());
			hotelInfo.setCheckoutTimeRange(hotelRates.getCheckOutTimeRange());
		}
		hotelInfo.setListingType(hotelRates.getListingType());
		hotelInfo.setHotelId(hotelRates.getId());
		hotelInfo.setCategories(commonResponseTransformer.getHotelCategories(hotelRates.getCategories(), hotelRates.isAltAcco()));
		if (MapUtils.isNotEmpty(hotelRates.getCategoryDetails())) {
			hotelInfo.setCategoryDetails(hotelRates.getCategoryDetails());
		}
		hotelInfo.setHotelTags(buildHotelTags(hotelRates.getHotelTags()));
		String idContext = MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue());
		//Hidden Gem will have high priority than Value Stays [HTL-38253]
		if(hotelRates.isBudgetHotel() && StringUtils.isNotBlank(idContext) && !Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext) && !(CollectionUtils.isNotEmpty(hotelRates.getCategories()) && hotelRates.getCategories().contains(HIDDEN_GEM))) {
			hotelInfo.setHotelTags(commonResponseTransformer.buildValueStaysHotelTag(Constants.VALUE_STAY_TAG_TITLE_REVIEW, hotelInfo.getHotelTags()));
		}
		hotelInfo.setMmtHotelCategory(hotelRates.getMmtHotelCategory());
		hotelInfo.setDayUseInfo(buildDayUseInfo(hotelRates));
		if (hotelInfo.getDayUseInfo()!=null) {
			hotelInfo.setCheckinTime(hotelInfo.getDayUseInfo().getCheckinDate());
			hotelInfo.setCheckoutTime(hotelInfo.getDayUseInfo().getCheckoutDate());
			hotelInfo.getDayUseInfo().setCheckinDate(checkIn);
			hotelInfo.getDayUseInfo().setCheckoutDate(checkIn);
		}
		hotelInfo.setQuickBookSubTitle(hotelRates.getQuickBookSubtitle());
		hotelInfo.setEntireProperty(hotelRates.isEntireProperty());

		HotelReviewIncognito incognitoModel = buildIncognitoModel(hotelRates);
		if(incognitoModel != null){
			hotelInfo.setIncognitoModel(incognitoModel);

		}

		hotelInfo.setSecrecyOption(hotelRates.isSecrecyOptionEnabled());
		hotelInfo.setStayType(hotelRates.getStayType());
		hotelInfo.setGiHotelId(giHotelId);
		setGroupBookingParamsInHotelInfo(hotelRates, funnelSource, hotelInfo);
		return hotelInfo;
	}

	private HotelReviewIncognito buildIncognitoModel(HotelRates hotelRates) {
		if (hotelRates.isIncognitoOption()) {
			HotelReviewIncognito incognitoModel =  new HotelReviewIncognito();
			incognitoModel.setEnabled(hotelRates.isIncognitoOption());
			incognitoModel.setDescriptionText(polyglotService.getSafeTranslatedData("INCOGNITO_DESCRIPTION_TEXT"));
			incognitoModel.setToolTipText(polyglotService.getSafeTranslatedData("INCOGNITO_TOOLTIP_TEXT"));
			String incognitoImageUrl = mobConfigHelper.getIncognitoUrl();
			if(incognitoImageUrl != null){
				incognitoModel.setMessageBoxImgUrl(incognitoImageUrl);
			}
			return incognitoModel;
		}
		return null;
	}

	private void setGroupBookingParamsInHotelInfo(HotelRates hotelRates, String funnelSource, HotelResult hotelInfo) {
		if (utility.isGroupBookingFunnel(funnelSource)) {
			hotelInfo.setGroupBookingHotel(hotelRates.isGroupBookingHotel());
			hotelInfo.setGroupBookingPrice(hotelRates.isGroupBookingPrice());
			hotelInfo.setMaskedPrice(hotelRates.isMaskedPrice());
			hotelInfo.setRoomText(buildRoomText(hotelRates.getRoomTypeDetails()));
		}
	}

	private String buildRoomText(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails != null && roomTypeDetails.getTotalDisplayFare() != null && MapUtils.isNotEmpty(roomTypeDetails.getRoomType()) &&
				roomTypeDetails.getRoomType().entrySet().stream().anyMatch((roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()))) {
			String baseRoomName = roomTypeDetails.getRoomType().entrySet().stream().filter(roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()).findFirst().get().getValue().getRoomTypeName();
			return roomTypeDetails.getTotalDisplayFare().getTotalRoomCount() + SPACE_X_SPACE + baseRoomName;
		}
		return null;
	}

	private Map<String, HotelTag> buildHotelTags(Map<String, com.mmt.hotels.model.response.persuasion.HotelTag> hotelTags) {
		if (MapUtils.isEmpty(hotelTags)) {
			return null;
		}

		Map<String, HotelTag> hotelTagMap = new HashMap<>();

		for (Map.Entry<String, com.mmt.hotels.model.response.persuasion.HotelTag> hotelTag : hotelTags.entrySet()) {
			hotelTagMap.put(hotelTag.getKey(),
					buildHotelTag(hotelTag.getValue()));
		}

		return hotelTagMap;
	}

	private HotelTag buildHotelTag(com.mmt.hotels.model.response.persuasion.HotelTag responseHotelTag) {
		if(responseHotelTag == null){
			return null;
		}
		HotelTag hotelTag = new HotelTag();
		hotelTag.setBackground(responseHotelTag.background);
		hotelTag.setTitle(responseHotelTag.title);
		hotelTag.setIcon(responseHotelTag.icon);
		hotelTag.setTitleColor(responseHotelTag.titleColor);
		hotelTag.setIconUrl(responseHotelTag.iconUrl);
		if(responseHotelTag.type != null) {
			hotelTag.setType(responseHotelTag.type.getValue());
		}
		return hotelTag;
	}

	private BNPLDetails getBnplDetails(RoomTypeDetails roomTypeDetails, boolean showBnplCard, BNPLDisabledReason bnplDisabledReason, String countryCode, BNPLVariant bnplVariant) {
		if (roomTypeDetails.getTotalDisplayFare() != null) {
			boolean hotelOriginalBNPL = roomTypeDetails.getTotalDisplayFare().isOriginalBNPL();
			boolean isBNPL = roomTypeDetails.getTotalDisplayFare().getIsBNPLApplicable();
			String bnplPerMsg = roomTypeDetails.getCancellationTimeline() != null && StringUtils.isNotEmpty(roomTypeDetails.getCancellationTimeline().getFreeCancellationText()) ?
					roomTypeDetails.getCancellationTimeline().getFreeCancellationText() : StringUtils.isNotBlank(roomTypeDetails.getBnplPersuasionMsg()) ? roomTypeDetails.getBnplPersuasionMsg() : roomTypeDetails.getTotalDisplayFare().getBnplPersuasionMsg();
			String bnplPolicyText = StringUtils.isNotBlank(roomTypeDetails.getBnplPolicyText()) ? roomTypeDetails.getBnplPolicyText() : roomTypeDetails.getTotalDisplayFare().getBnplPolicyText();
			String bnplText = BNPLVariant.BNPL_AT_0.equals(bnplVariant) ? polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT_GI) : BNPLVariant.BNPL_AT_1.equals(bnplVariant) ? polyglotService.getTranslatedData(BNPL_NEW_VARIANT_TEXT_GI) : roomTypeDetails.getBnplNewVariantText();
			String bnplSubText = StringUtils.isNotBlank(roomTypeDetails.getBnplNewVariantSubText()) ? roomTypeDetails.getBnplNewVariantSubText() : null;
			return commonResponseTransformer.buildBNPLDetails(isBNPL, bnplPerMsg, bnplPolicyText, bnplText, bnplSubText, hotelOriginalBNPL, showBnplCard, bnplVariant, bnplDisabledReason, countryCode, roomTypeDetails.getBnplExtraFees());
		}
		return null;
	}
	private BNPLDetails getLoggedOutBnplDetails(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails.getTotalDisplayFare() != null) {
			BNPLDetails bnplDetails = new BNPLDetails();
			bnplDetails.setBnplVariant(roomTypeDetails.getTotalDisplayFare().getBnplVariant() != null ?
					(roomTypeDetails.getTotalDisplayFare().getBnplVariant().toString()) : BNPLVariant.BNPL_NOT_APPLICABLE.toString());
			return  bnplDetails;
		}
		return null;
	}


	private Address getAddress(List<String> addressLines) {
    	if(CollectionUtils.isNotEmpty(addressLines)) {
    		Address address = new Address();
    		address.setLine1(addressLines.get(0));
    		address.setLine2(addressLines.size() > 1 ? addressLines.get(1) : null);
    		return address;
    	}
    	return null;
    }

    private void buildPropertyRules(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates) {
    	RequestInputBO inputBo = new RequestInputBO.Builder()
				.buildCountryCode(hotelRates.getCountryCode())
				.buildPropertyType(hotelRates.getPropertyType())
				.buildHouseRules(hotelRates.getHouseRules())
				.buildMustReadRules(hotelRates.getMustReadRules())
				.buildPah(Utility.isPahOnlyPaymode(availRoomsResponse.getFeatureFlags().getPayMode()))
				.buildPahWithCC(Utility.isPahWithCCPaymode(availRoomsResponse.getFeatureFlags().getPayMode()))
    			.buildCancellationPolicyType(getCancellationPolicyType(hotelRates.getRoomTypeDetails()))
    			.buildCancellationDate(getCancellationDate(hotelRates.getRoomTypeDetails()))
    			.buildSupplierCode(getSupplierCode(hotelRates))
    			.buildCheckinPolicy(getCheckinPolicy(hotelRates.getRoomTypeDetails()))
    			.buildConfirmationPolicy(commonResponseTransformer.getConfirmationPolicy(hotelRates.getRoomTypeDetails()))
				.buildNotices(hotelRates.getNotices())
    			.build();

    	availRoomsResponse.setPropertyRules(commonResponseTransformer.getImportantInfoSection(inputBo));
    }

    private CancelPenalty getCancelPenalty(RoomTypeDetails roomTypeDetails) {
    	CancelPenalty mostRestrictedCancelPenalty = null;
    	for(Map.Entry<String,RoomType> roomType: roomTypeDetails.getRoomType().entrySet()){
			for(Map.Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlan : roomType.getValue().getRatePlanList().entrySet()){
				if(mostRestrictedCancelPenalty == null) {
					mostRestrictedCancelPenalty = ratePlan.getValue().getCancelPenaltyList().get(0);
					if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(mostRestrictedCancelPenalty.getMostRestrictive())) {
						return mostRestrictedCancelPenalty;
					}
				}
				Optional<CancelPenalty> cancelPenalty = ratePlan.getValue().getCancelPenaltyList().stream().filter(penalty -> Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(penalty.getMostRestrictive())).findFirst();
				if(cancelPenalty.isPresent()) {
					mostRestrictedCancelPenalty = cancelPenalty.get();
					return mostRestrictedCancelPenalty;
				}
			}
    	}
    	return mostRestrictedCancelPenalty;
    }

    private RatePolicy getCheckinPolicy(RoomTypeDetails roomTypeDetails) {
    	RatePolicy checkInPolicy = null;
    	for(Map.Entry<String,RoomType> roomType: roomTypeDetails.getRoomType().entrySet()){
			for(Map.Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlan : roomType.getValue().getRatePlanList().entrySet()){
				RatePolicy ratePolicy = ratePlan.getValue().getCheckinPolicy();
				if(checkInPolicy == null && ratePolicy !=null) {
					checkInPolicy = ratePolicy;
					if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(checkInPolicy.getMostRestrictive())) {
						return checkInPolicy;
					}
				}
				else if(checkInPolicy !=null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
					checkInPolicy = ratePolicy;
					return checkInPolicy;
				}
			}
    	}
    	return checkInPolicy;
    }



	private void buildAdditionalCharges(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates, boolean showTransfersFeeTxt) {
		SupplierDetails supplierDetails = getSupplierDetails(hotelRates.getRoomTypeDetails());
		AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
				.buildUserCurrency(hotelRates.getCurrencyCode())
				.buildHotelierCurrency(supplierDetails != null ? supplierDetails.getHotelierCurrencyCode() : "INR")
				.buildPropertyType(hotelRates.getPropertyType())
				.buildAdditionalFees(hotelRates.getMandatoryCharges())
				.buildConversionFactor(hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null ? hotelRates.getRoomTypeDetails().getTotalDisplayFare().getConversionFactor() : 1.0)
				.buildBookingAmount(hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null ? hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice() : 0.0)
				.buildCityCode(hotelRates.getCityCode())
				.build();
		availRoomsResponse.setAdditionalFees(commonResponseTransformer.buildAdditionalCharges(additionalChargesBO, showTransfersFeeTxt));
	}

	private void buildAlerts(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates, boolean alertTypeVariant) {

		List<Alert> hotelLevelAlerts = new ArrayList<>();
		if(hotelRates.isRequestToBook()){
			if(hotelRates.isPreApprovalExpired()){
				Alert alert = new Alert();
				alert.setType(AlertType.RTB_CHANGE);
				alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_CHANGE_ALERT_TEXT));
				hotelLevelAlerts.add(alert);
			}else if(hotelRates.isRTBRatePlanPreApproved()) {
				Alert alert = new Alert();
				alert.setType(AlertType.RTB_PRE_APPROVED);
				alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_PRE_APPROVED_TEXT));
				hotelLevelAlerts.add(alert);
			}else if(!hotelRates.isRtbPreApproved()) {
				Alert alert = new Alert();
				alert.setType(AlertType.RTB);
				alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_ALERT_TEXT));
				hotelLevelAlerts.add(alert);

			}
		}
		RatePolicy checkinPolicy = getCheckinPolicy(hotelRates.getRoomTypeDetails());
		if (null != checkinPolicy && StringUtils.isNotBlank(checkinPolicy.getShortDescription())) {
			Alert alert = new Alert();
			alert.setType(AlertType.CHECKIN_POLICY);
			alert.setText(checkinPolicy.getShortDescription());
			hotelLevelAlerts.add(alert);
		}

		RatePolicy confirmationPolicy = commonResponseTransformer.getConfirmationPolicy(hotelRates.getRoomTypeDetails());
		if (null != confirmationPolicy && StringUtils.isNotBlank(confirmationPolicy.getShortDescription())) {
			Alert alert = new Alert();
			alert.setType(AlertType.CONFIRMATION_POLICY);
			alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.CTRIP_NON_INSTANT_TEXT));
			hotelLevelAlerts.add(alert);
		}

		if (CollectionUtils.isNotEmpty(hotelRates.getAlerts())) {
			//build price alert
			Optional<AlertInfo> priceAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.PRICE.equals(al.getMismatchType())).findAny();
			//HTL-41292 For IH need to Change Alert Message if we get any alertType other than PRICE from pricer.
			boolean isIHNonPriceAlert = priceAlert.isPresent() && hotelRates.getAlerts().size()>1 && !DOM_COUNTRY.equalsIgnoreCase(hotelRates.getCountryCode()); //true if AlertType from pricer response list contains PRICE and any other AlertType also
			if (priceAlert.isPresent()) {
				Alert alert = new Alert();
				AlertInfo alertInfo = priceAlert.get();
				Double amount = Utility.round(getAlertAmount(alertInfo), 0);

				// Get currency symbol based on user selected currency.
				String currencyType = availRoomsResponse.getTotalpricing().getCurrency();
				String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currencyType) ? currencyType : DEFAULT_CUR_INR).getCurrencySymbol();
				if (priceIncreaseAlert(alertInfo)) {
					alert.setType(AlertType.PRICE_INCREASE);
					if (isIHNonPriceAlert && alertTypeVariant) {
						alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_ALERT_TEXT));
						alert.setSubText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_INCREASE_ALERT_SUB_TEXT), currencySymbol, amount));
					} else if (!alertTypeVariant) {
						alert.setText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_INCREASE_ALERT_TEXT_OLD_VERSION), currencySymbol, amount));
						alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_OLD_VERSION_ALERT_SUB_TEXT));
					} else {
						alert.setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_INCREASE_ALERT_TEXT), hotelRates.getPropertyType(), amount));
						alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_INCREASE_ALERT_SUB_TEXT));
					}
				} else {
					alert.setType(AlertType.PRICE_DECREASE);
					if (isIHNonPriceAlert && alertTypeVariant) {
						alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_ALERT_TEXT));
						alert.setSubText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_ALERT_SUB_TEXT), currencySymbol, amount));
					} else if (!alertTypeVariant) {
						alert.setText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_ALERT_TEXT_OLD_VERSION), currencySymbol, amount));
						alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_OLD_VERSION_ALERT_SUB_TEXT));
					} else {
						alert.setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_DECREASE_ALERT_TEXT), amount));
						alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_DECREASE_ALERT_SUB_TEXT));
					}
				}
				//adding Alert Reasons list parsed from Pricer Mismatch error messages to client for tracking purpose.
				alert.setReasons(hotelRates.getAlerts().stream().map(AlertInfo::getMismatchType).collect(Collectors.toList()));
				alert.setCurrency(hotelRates.getCurrencyCode());
				alert.setAmount(amount);
				hotelLevelAlerts.add(alert);
			}

			//build rate plan specific alerts - cancelpolicy and meal plan
			for (RatePlan ratePlan : availRoomsResponse.getRateplanlist()) {
				List<AlertInfo> ratePlanAlerts = hotelRates.getAlerts().stream()
						.filter(al -> ratePlan.getCode().equals(al.getRpcc()) && !AlertInfo.AlertType.PRICE.equals(al.getMismatchType()))
						.collect(Collectors.toList());
				for (AlertInfo alertInfo : ratePlanAlerts) {
					Alert alert = new Alert();
					if (AlertInfo.AlertType.CANCELLATION.equals(alertInfo.getMismatchType())) {
						alert.setType(AlertType.CANCEL_POLICY);
						alert.setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_CHANGE_ALERT_TEXT), hotelRates.getPropertyType().toLowerCase()));
					} else if(AlertInfo.AlertType.MEALPLAN.equals(alertInfo.getMismatchType())){
						alert.setType(AlertType.MEAL_PLAN);
						alert.setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.MEAL_PLAN_CHANGE_ALERT_TEXT), hotelRates.getPropertyType().toLowerCase()));
					}
					if (CollectionUtils.isEmpty(ratePlan.getAlerts()))
						ratePlan.setAlerts(new ArrayList<>());
					ratePlan.getAlerts().add(alert);
				}
			}
		}
		availRoomsResponse.setAlerts(hotelLevelAlerts);
	}

	private Double getAlertAmount(AlertInfo alertInfo) {
		return Math.abs(Double.valueOf(alertInfo.getActualValue()) - Double.valueOf(alertInfo.getExpectedValue()));
	}

	private boolean priceIncreaseAlert(AlertInfo alertInfo) {
		return Double.valueOf(alertInfo.getActualValue()) > Double.valueOf(alertInfo.getExpectedValue());
	}

	private String getSupplierCode(HotelRates hotelRates) {
    	SupplierDetails supplierDetails = getSupplierDetails(hotelRates.getRoomTypeDetails());
		if(supplierDetails !=null) {
			return supplierDetails.getSupplierCode();
		}
    	return null;
    }

    private String getCancellationPolicyType(RoomTypeDetails roomTypeDetails) {
    	CancelPenalty cancelPenalty = getCancelPenalty(roomTypeDetails);
    	if(cancelPenalty !=null && cancelPenalty.getCancellationType() !=null) {
			if (CancelPenalty.CancellationType.FREE_CANCELLATON.equals(cancelPenalty.getCancellationType()))
				return BookedCancellationPolicyType.FC.name();
			else
				return BookedCancellationPolicyType.NR.name();
		}
    	else
    		return BookedCancellationPolicyType.NR.name();


    }

    private String getCancellationDate(RoomTypeDetails roomTypeDetails) {
    	CancelPenalty cancelPenalty = getCancelPenalty(roomTypeDetails);
    	if(cancelPenalty !=null) {
    		return cancelPenalty.getTillDate();
    	}
    	return null;
    }

    private void updateHotelierCurrencyPricingDetails(TotalPricing totalpricing, RoomTypeDetails roomTypeDetails, double convFactor, String userCurrency, String payMode) {
    	// update only if paymode is PAH
    	if(Utility.isPahOnlyPaymode(payMode)) {
			SupplierDetails supplierDetails = getSupplierDetails(roomTypeDetails);
			if(supplierDetails !=null) {
				commonResponseTransformer.updateTotalAmountInHotelierCurrency(totalpricing.getDetails(), payMode, userCurrency, supplierDetails.getHotelierCurrencyCode(), convFactor);
			}
    	}
    }

	private String getSegmentId(RoomTypeDetails roomTypeDetails)
	{
		RoomType roomType = null;
		String segmentId = null;
		for (String roomCode: roomTypeDetails.getRoomType().keySet()) {
			roomType = roomTypeDetails.getRoomType().get(roomCode);
			break;
		}

		if(roomType !=null) {
			Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanMap = roomType.getRatePlanList();
			for (String code: ratePlanMap.keySet()) {
				com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = ratePlanMap.get(code);
				segmentId = ratePlanCB.getSegmentId();
				break;
			}
		}

		return segmentId;
	}
    private SupplierDetails getSupplierDetails(RoomTypeDetails roomTypeDetails) {
    	RoomType roomType = null;
    	SupplierDetails supplierDetails = null;
		for (String roomCode: roomTypeDetails.getRoomType().keySet()) {
			roomType = roomTypeDetails.getRoomType().get(roomCode);
			break;
		}
		if(roomType !=null) {
			Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanMap = roomType.getRatePlanList();
			for (String code: ratePlanMap.keySet()) {
				com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = ratePlanMap.get(code);
				supplierDetails = ratePlanCB.getSupplierDetails();
				break;
			}
		}
		return supplierDetails;
    }

    private com.mmt.hotels.clientgateway.request.payment.SpecialRequest buildSpecialRequests(SpecialRequest spclReq){
    	if(spclReq !=null) {
    		com.mmt.hotels.clientgateway.request.payment.SpecialRequest specialRequest = new com.mmt.hotels.clientgateway.request.payment.SpecialRequest();
    		specialRequest.setDisclaimer(spclReq.getDisclaimer());
    		if(CollectionUtils.isNotEmpty(spclReq.getCategories())) {
    			List<SpecialRequestCategory> spclCatList = new ArrayList<>();
    			for (com.mmt.hotels.model.response.pricing.SpecialRequestCategory reqCategory : spclReq.getCategories()) {
    				SpecialRequestCategory spclCat = new SpecialRequestCategory();
    				spclCat.setCode(reqCategory.getCode());
    				spclCat.setName(reqCategory.getName());
    				spclCat.setType(reqCategory.getType());
    				if(CollectionUtils.isNotEmpty(reqCategory.getSubCategories()))
    					buildSpecialReqSubCategories(reqCategory.getSubCategories(), spclCat);
    				spclCatList.add(spclCat);
				}
    			specialRequest.setCategories(spclCatList);
    		}
    		return specialRequest;
    	}
    	return null;
    }

    private void buildSpecialReqSubCategories(List<com.mmt.hotels.model.response.pricing.SpecialRequestCategory> subCategories, SpecialRequestCategory spclCat){
    	List<SpecialRequestCategory> spclSubCategories = new ArrayList<>();
    	for (com.mmt.hotels.model.response.pricing.SpecialRequestCategory subCat : subCategories) {
    		SpecialRequestCategory spclSubCat = new SpecialRequestCategory();
    		if(Constants.SPCL_CAT_TYPE_LABEL.equalsIgnoreCase(subCat.getType())) {
    			spclCat.setLabel(subCat.getName());
    		}else {
    			spclSubCat.setCode(subCat.getCode());
    			spclSubCat.setName(subCat.getName());
    			spclSubCat.setType(subCat.getType());
    			spclSubCat.setValues(subCat.getValues());
    			spclSubCat.setValuesMap(buildValueMap(subCat.getValues()));
    			spclSubCategories.add(spclSubCat);
    		}
		}
    	spclCat.setSubCategories(spclSubCategories);
    }

	private Map<String, String> buildValueMap(String[] values) {
		if (ArrayUtils.isEmpty(values)) {
			return null;
		}
		Map<String, String> valuesMap = new LinkedHashMap<>();
		for (String value : values) {
			if ("ARA".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue())) && (StringUtils.containsIgnoreCase(value, "AM") || StringUtils.containsIgnoreCase(value, "PM"))) {
				String[] time = value.split("\\s");
				String translatedValue = polyglotService.getTranslatedData(time[1]) + " " + time[0];
				valuesMap.put(value, translatedValue);
			} else {
				valuesMap.put(value, value);
			}
		}
		return valuesMap;
	}

    private PanInfo getPanInfo(boolean panRequired, boolean panAvailable) {
    	PanInfo panInfo = new PanInfo();
		if(enablePanCardCheck){
			panInfo.setPanCardRequired(panRequired);
		}else {
			panInfo.setPanCardRequired(false);
		}
    	panInfo.setPanAvailable(panAvailable);
    	return panInfo;
    }

	private GstInfo getGstInfo(String gstIn, String countryCode, String siteDomain, String gstStateCode, RoomTypeDetails roomTypeDetails) {
		SupplierDetails supplierDetails = null;
		if(roomTypeDetails != null) {
			supplierDetails = getSupplierDetails(roomTypeDetails);
		}
		if ("AE".equalsIgnoreCase(siteDomain)) {
			if(supplierDetails != null && StringUtils.isNotBlank(supplierDetails.getSupplierCode()) && Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierDetails.getSupplierCode())) {
				GstInfo gstInfo = new GstInfo();
				if (StringUtils.isNotBlank(gstStateCode)) {
					gstInfo.setGstStateCode(gstStateCode);
				}
				if (countryCode.equalsIgnoreCase("UNI")) {
					gstInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.TIN_NO_NOT_AVAILABLE_TEXT));
				} else {
					return null;
				}
				return gstInfo;
			}
		} else if (Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
			GstInfo gstInfo = new GstInfo();
			if (StringUtils.isNotBlank(gstStateCode)) {
				gstInfo.setGstStateCode(gstStateCode);
			}
			if (StringUtils.isNotBlank(gstIn)) {
				gstInfo.setGstin(gstIn);
			} else {
				gstInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.GSTN_NOT_AVAILABLE_TEXT));
			}
			return gstInfo;
		}
		return null;
	}

    private FeatureFlags getFeatureFlags(HotelRates hotelRates, Map<String, String> expDataMap, com.mmt.model.CorpData corpData) {
    	FeatureFlags featureFlags = new FeatureFlags();
    	featureFlags.setPahWalletApplicable(hotelRates.isPahWalletApplicable());
    	featureFlags.setSoldOut(hotelRates.getSoldOut()!=null? hotelRates.getSoldOut(): false);
    	featureFlags.setShowFCBanner(hotelRates.isShowFcBanner());
    	featureFlags.setBlackEligible(hotelRates.isBlackEligible());
    	featureFlags.setPayMode(getRatePlanPayMode(hotelRates.getRoomTypeDetails()));
    	featureFlags.setForeignTravel(hotelRates.isForeignTravel());
    	featureFlags.setLeadPassengerMandatoryPerRoom(hotelRates.isLeadPassengerMandatoryPerRoom());
		featureFlags.setRequestToBook(hotelRates.isRequestToBook());
		featureFlags.setRtbPreApproved(hotelRates.isRtbPreApproved());
		featureFlags.setRtbAutoCharge(hotelRates.isRtbAutoCharge());
		if(hotelRates.getBlackInfo() != null)
			featureFlags.setIsGoTribe3_0(hotelRates.getBlackInfo().getIsNewLoyaltyProgramForGI());
    	if(hotelRates.getUserWalletDetails() !=null) {
    		if((hotelRates.getUserWalletDetails().getRealAmount() !=null && hotelRates.getUserWalletDetails().getRealAmount() > 0.0d) ||
    				hotelRates.getUserWalletDetails().getPlusAmount() !=null && hotelRates.getUserWalletDetails().getPlusAmount() > 0.0d) {
    			featureFlags.setRealWalletAvailable(true);
    		}
    	}
		boolean paylaterPriceValidated = null!=hotelRates.getRoomTypeDetails()
				&& null!=hotelRates.getRoomTypeDetails().getTotalDisplayFare()
				&& null!=hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown()
				&& payLaterCardLimit >= hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice();
         // 	Clean Up for GIHTL-15565 - PayLater Card
		//		featureFlags.setPayLaterCard(paylaterPriceValidated && MapUtils.isNotEmpty(expDataMap) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.PAY_LATER_CARD_EXP)));
    	featureFlags.setPayLaterCard(false);
		featureFlags.setShowScrolldown(false);
		if(corpData != null){
			featureFlags.setCaptureAllPaxDetailsHotel(corpData.isCaptureAllPaxDetailsHotel());
		}
    	return featureFlags;
    }

    public TotalPriceResponse convertTotalPricingResponse(TotalPricingResponse totalPricingResponseOld, TotalPricingRequest getTotalPriceRequest, String countryCode){
    	TotalPriceResponse totalPriceResponse = new TotalPriceResponse();
		Map<String, String > expDataMap = utility.getExpDataMap(getTotalPriceRequest.getExpData());
		if(expDataMap == null)
			expDataMap = new HashMap<>();
		if (expDataMap != null){
			if(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))
				expDataMap.put("wallet_exp",totalPricingResponseOld != null && totalPricingResponseOld.getWalletVariant() != null ? totalPricingResponseOld.getWalletVariant().toString() : "0");
			else
				expDataMap.put(WalletExpIH,totalPricingResponseOld != null && totalPricingResponseOld.getWalletVariant() != null ? totalPricingResponseOld.getWalletVariant().toString() : "0");
			expDataMap.put("goTribe3", (totalPricingResponseOld != null && totalPricingResponseOld.isGoTribe3Exp()) ? "true" : "false");
		}
    	totalPriceResponse.setTotalPricing(getTotalAmountDetails(totalPricingResponseOld, countryCode, expDataMap));
    	totalPriceResponse.setAddon(getAddOnPricing(totalPricingResponseOld, countryCode, expDataMap));
    	if(totalPricingResponseOld != null && totalPricingResponseOld.getPriceBreakdown() !=null) {
    		com.mmt.hotels.model.response.pricing.TotalPricing priceBreakdown = totalPricingResponseOld.getPriceBreakdown();
    		totalPriceResponse.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(priceBreakdown.getCancellationTimeline()));
    		totalPriceResponse.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(priceBreakdown.getCancellationTimeline()));
			totalPriceResponse.setEmiDetails(commonResponseTransformer.getEmiAbridgeDetails(priceBreakdown.getEmiDetails()));
			boolean showBnplFlags = Utility.isShowBnplCard(getTotalPriceRequest.getFeatureFlags());
			BNPLVariant bnplVariant = priceBreakdown.getBnplVariant();

			boolean bnplDisabledDueToNonBnplCouponApplied = commonResponseTransformer.checkIfInvalidCoupon(totalPriceResponse.getTotalPricing());
			boolean insuranceAddonSelected = priceBreakdown.getLobWisePricing() != null && priceBreakdown.getLobWisePricing().get(LOB.INSURANCE) != null;
			boolean goCashApplied = totalPricingResponseOld.getGoCashDetails() !=null &&  totalPricingResponseOld.getGoCashDetails().isGoCashApplied();
			Integer activeBnplBookingCount = totalPricingResponseOld.isUserLevelBnplDisabled() ? totalPricingResponseOld.getActiveBnplBookingCount() : null;

			BNPLDisabledReason bnplDisabledReason = null;
			if (totalPricingResponseOld.isShowDisabledBnplDetails()) {
				bnplDisabledReason = commonResponseTransformer.getBNPLDisabledReason(totalPricingResponseOld.isUserLevelBnplDisabled(), bnplDisabledDueToNonBnplCouponApplied, insuranceAddonSelected, goCashApplied);
			}
			if (CollectionUtils.isNotEmpty(totalPricingResponseOld.getHydraSegments()) || (CollectionUtils.isEmpty(totalPricingResponseOld.getHydraSegments()) && StringUtils.isNotBlank(getTotalPriceRequest.getClient()) &&
					!getTotalPriceRequest.getClient().equalsIgnoreCase(Constants.DEVICE_IOS) && !getTotalPriceRequest.getClient().equalsIgnoreCase(Constants.DEVICE_OS_ANDROID))){
				totalPriceResponse.setBnplDetails(
						commonResponseTransformer.buildBNPLDetails(
								priceBreakdown.isBnplApplicable(), priceBreakdown.getBnplPersuasionMsg(), priceBreakdown.getBnplPolicyText(),
								priceBreakdown.getBnplNewVariantText(), priceBreakdown.getBnplNewVariantSubText(), priceBreakdown.isOriginalBNPL(),
								showBnplFlags, bnplVariant, bnplDisabledReason, countryCode, priceBreakdown.getBnplExtraFees()
						)
				);
				Map<String, PricingDetails> pricingDetailsMap = new HashMap<>();
				if (totalPriceResponse.getBnplDetails() != null) {
					totalPriceResponse.getBnplDetails().setDetails(
							commonResponseTransformer.getPricingDetails(
									totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.HOTEL), countryCode,
									totalPricingResponseOld.getPriceBreakdown().getPayMode(), false, "", expDataMap,
									false, totalPricingResponseOld.getPriceBreakdown().isCbrAvailable(),
									totalPricingResponseOld.getLoyaltyMessage(), totalPricingResponseOld.getClmPersuasion(),
									null, null, priceBreakdown.getBnplExtraFees(),0.0
							)
					);
					if(CollectionUtils.isNotEmpty(totalPriceResponse.getBnplDetails().getDetails())) {
						pricingDetailsMap = getPricingDetailKeyToPricingDetailObjectMap(totalPriceResponse.getBnplDetails().getDetails());
						updateTotalAmountIncludingAddon(pricingDetailsMap, priceBreakdown.getDisplayPrice() + priceBreakdown.getBnplExtraFees());
						if (totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.INSURANCE)!=null) {
							commonResponseTransformer.buildInsuranceBreakup(totalPriceResponse.getBnplDetails().getDetails(), totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.INSURANCE));
						}
						commonResponseTransformer.updateTotalAmountInHotelierCurrency(totalPriceResponse.getBnplDetails().getDetails()
								,totalPricingResponseOld.getPriceBreakdown().getPayMode(), totalPricingResponseOld.getPriceBreakdown().getCurrency()
								, totalPricingResponseOld.getPriceBreakdown().getHotelierCurrency(), totalPricingResponseOld.getPriceBreakdown().getHotelierCurrencyConvFactor());
					}
				}

				if (priceBreakdown.getFullPayment() != null) {
					double tcsAmount = MapUtils.isNotEmpty(pricingDetailsMap) && pricingDetailsMap.containsKey(TCS_AMOUNT_KEY) ?
							pricingDetailsMap.get(TCS_AMOUNT_KEY).getAmount() : 0d;
					totalPriceResponse.setFullPayment(utility.buildFullPayment(priceBreakdown.getFullPayment(), priceBreakdown.getBnplExtraFees(), tcsAmount));
				}
			}

			if (totalPricingResponseOld.getPriceBreakdown().getCorpMetaData()!=null){
				totalPriceResponse.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(totalPricingResponseOld.getPriceBreakdown().getCorpMetaData()));
			}
        	totalPriceResponse.setRateplanlist(commonResponseTransformer.buildRateplanList(priceBreakdown.getRoomTypes(), totalPriceResponse.getBnplDetails(), bnplVariant,
					(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing() != null && totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().containsKey(LOB.HOTEL) ?
					totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.HOTEL) : null),
					expDataMap, totalPricingResponseOld.getLoyaltyMessage(), totalPricingResponseOld.getClmPersuasion(),countryCode));
			totalPriceResponse.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(totalPricingResponseOld.getPaymentPlan()));
    	}

		// Set the BNPL unavailable message when BNPL is removed due to addition of insurance addon
		if (totalPriceResponse.getTotalPricing() != null && totalPricingResponseOld.getPriceBreakdown() != null
				&& StringUtils.isNotBlank(totalPricingResponseOld.getPriceBreakdown().getBnplUnavailableMsg())) {
			totalPriceResponse.getTotalPricing().setBnplUnavailableMsg(totalPricingResponseOld.getPriceBreakdown().getBnplUnavailableMsg());
		}
		if(!Utility.gocashVariant(expDataMap, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY))){
			totalPriceResponse.setClmPersuasion(totalPricingResponseOld.getClmPersuasion());
		}

		totalPriceResponse.setGoCashDetails(totalPricingResponseOld.getGoCashDetails());
		totalPriceResponse.setLoyaltyMessage(totalPricingResponseOld.getLoyaltyMessage());
		if(totalPricingResponseOld.getUserCards() != null && totalPricingResponseOld.getUserCards().size() > 0) {
			int tierNumber = totalPricingResponseOld.getBlackInfo() != null && StringUtils.isNotBlank(totalPricingResponseOld.getBlackInfo().getTierNumber()) ? Integer.parseInt(totalPricingResponseOld.getBlackInfo().getTierNumber()) : 0;
			UserCard card = commonResponseTransformer.GetUserCardById(totalPricingResponseOld.getUserCards(), COMMONS_CARD_TEMPLATE_ID, tierNumber);
			if(card != null)
				totalPriceResponse.setReviewCommonsCardData(card);
		}

		if (totalPricingResponseOld.getPriceBreakdown().getLobWisePricing() != null &&
				totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().containsKey(LOB.HOTEL)) {
		totalPriceResponse.setFooterStrip(commonResponseTransformer.buildFooterStripValidateCoupon(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.HOTEL),
				totalPricingResponseOld.getLoyaltyMessage(), totalPricingResponseOld.getClmPersuasion(), totalPricingResponseOld.getHydraSegments(), expDataMap, totalPricingResponseOld.getBlackInfo(), countryCode));
		}
		// Adding charity amount if present, into the PricingDetails list
		setCharityInPricingDetails(totalPriceResponse, getTotalPriceRequest);
		//GIHTL-15995 build taj gift card or hotel credit persuasion for review page
		setHotelPerusasions(totalPriceResponse, totalPricingResponseOld);
		return totalPriceResponse;
    }

	private void setHotelPerusasions(TotalPriceResponse totalPriceResponse, TotalPricingResponse totalPricingResponseOld) {
		if(totalPricingResponseOld!=null && totalPricingResponseOld.getHotelBenefitInfo()!=null) {
			Map<String,PersuasionResponse> persuasionMap = new HashMap<>();
			PersuasionResponse tajGiftCardPersuasion = commonResponseTransformer.buildTajGiftCardOrHotelCreditPersuasion(totalPricingResponseOld.getHotelBenefitInfo());
			persuasionMap.put(TAJ_CAMPAIGN_PERSUASION_KEY, tajGiftCardPersuasion);
			totalPriceResponse.setHotelPersuasions(persuasionMap);
		}
	}

	private void setCharityInPricingDetails(TotalPriceResponse totalPriceResponse, TotalPricingRequest getTotalPriceRequest) {
		try {
			if (CollectionUtils.isNotEmpty(totalPriceResponse.getAddon()) && CollectionUtils.isNotEmpty(getTotalPriceRequest.getAddOnSelected())
					&& (getTotalPriceRequest.getAddOnSelected().stream().anyMatch(c -> StringUtils.equalsIgnoreCase(c.getId(), CHARITY_ID)) || getTotalPriceRequest.getAddOnSelected().stream().anyMatch(c -> StringUtils.equalsIgnoreCase(c.getId(), CHARITY_ID_V2)))
					&& totalPriceResponse.getTotalPricing() != null && CollectionUtils.isNotEmpty(totalPriceResponse.getTotalPricing().getDetails())) {

				boolean isCharityV2 = getTotalPriceRequest.getAddOnSelected().stream().anyMatch(c -> StringUtils.equalsIgnoreCase(c.getId(), CHARITY_ID_V2));
				PricingDetails pricingDetails = totalPriceResponse.getAddon().get(0);
				if (pricingDetails != null && pricingDetails.getAmount() > 0.0d) {
					PricingDetails charity = new PricingDetails();
					charity.setAmount(pricingDetails.getAmount());
					charity.setKey(isCharityV2 ? CHARITY_KEY_V2:CHARITY_KEY);
					charity.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
					charity.setLabel(CHARITY_LABEL);
					totalPriceResponse.getTotalPricing().getDetails().add(charity);
					// adding charity node in bnplDetail breakup
					if (totalPriceResponse.getBnplDetails() != null && CollectionUtils.isNotEmpty(totalPriceResponse.getBnplDetails().getDetails())) {
						totalPriceResponse.getBnplDetails().getDetails().add(charity);
					}
				}
				//Adding Charity Amount in After Cashback price in case of Wallet GoCash
				if (pricingDetails != null && pricingDetails.getAmount() > 0.0d){
					for(PricingDetails pricingNode : totalPriceResponse.getTotalPricing().getDetails()) {
						if (pricingNode.getKey() == AFTER_CASHBACK_PRICE_KEY) {
							pricingNode.setAmount(pricingNode.getAmount() + pricingDetails.getAmount());
						}
					}
				}
			}
		} catch (Exception ex) {
			LOGGER.warn("Failed to set Charity in PricingDetails : {}", ex.getMessage(), ex);
		}
	}

	private TotalPricing getTotalAmountDetails(TotalPricingResponse totalPricingResponseOld, String countryCode, Map<String, String> expData){
    	if(totalPricingResponseOld !=null && totalPricingResponseOld.getPriceBreakdown() !=null) {
    		TotalPricing totalPrice = null;
    		if(MapUtils.isNotEmpty(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing()) &&
    				totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().containsKey(LOB.HOTEL)) {
				totalPrice = commonResponseTransformer.getTotalPricing(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.HOTEL), countryCode,
						totalPricingResponseOld.getPriceBreakdown().getPayMode(),
						false, "",expData, false,totalPricingResponseOld.getPriceBreakdown().isCbrAvailable(), totalPricingResponseOld.getLoyaltyMessage(), totalPricingResponseOld.getClmPersuasion(), null, null, 0.0,0.0);
			}
    		if(totalPrice !=null && CollectionUtils.isNotEmpty(totalPrice.getDetails())) {
    			List<PricingDetails> priceDetails = totalPrice.getDetails();
				Map<String, PricingDetails> pricingDetailsMap = getPricingDetailKeyToPricingDetailObjectMap(priceDetails);
				updateTotalAmountIncludingAddon(pricingDetailsMap, totalPricingResponseOld.getPriceBreakdown().getDisplayPrice());
    			if (totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.INSURANCE)!=null) {
					commonResponseTransformer.buildInsuranceBreakup(priceDetails, totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.INSURANCE));
				}
    			totalPrice.setDetails(priceDetails);
				totalPrice.setPinCodeMandatory(totalPricingResponseOld.getPriceBreakdown().isPinCodeMandatory());
    			commonResponseTransformer.updateTotalAmountInHotelierCurrency(priceDetails
    					,totalPricingResponseOld.getPriceBreakdown().getPayMode(), totalPricingResponseOld.getPriceBreakdown().getCurrency()
    					, totalPricingResponseOld.getPriceBreakdown().getHotelierCurrency(), totalPricingResponseOld.getPriceBreakdown().getHotelierCurrencyConvFactor());
    		}
			String pahText = polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_POLICY_TEXT_GENERIC);
			if (totalPricingResponseOld.getPriceBreakdown().getPayMode() != null)
				Utility.updatePayAtHotelText(totalPrice, totalPricingResponseOld.getPriceBreakdown().getPayMode(), pahText);
			if(totalPrice != null){
				totalPrice.setCouponSubtext(polyglotService.getSafeTranslatedData(GIFT_CARD_TEXT_GI));
			}
    		return totalPrice;
    	}
    	return null;
    }

	private void updateTotalAmountIncludingAddon(Map<String, PricingDetails> priceDetails, double totalAmount) {
		if (MapUtils.isNotEmpty(priceDetails) && priceDetails.containsKey(Constants.TOTAL_AMOUNT_KEY)) {
			if (priceDetails.containsKey(TCS_AMOUNT_KEY)) {
				priceDetails.get(Constants.TOTAL_AMOUNT_KEY).setAmount(totalAmount + priceDetails.get(TCS_AMOUNT_KEY).getAmount());
			} else {
				priceDetails.get(Constants.TOTAL_AMOUNT_KEY).setAmount(totalAmount);
			}
		}
	}

	private Map<String, PricingDetails> getPricingDetailKeyToPricingDetailObjectMap(List<PricingDetails> priceDetails) {
		if (CollectionUtils.isNotEmpty(priceDetails)) {
			return priceDetails.stream().collect(Collectors.toMap(PricingDetails::getKey, priceDetail -> priceDetail));
		}
		return null;
	}

    private List<PricingDetails> getAddOnPricing(TotalPricingResponse totalPricingResponseOld, String countryCode, Map<String, String> expData){
    	if(totalPricingResponseOld !=null && totalPricingResponseOld.getPriceBreakdown() !=null &&
    			MapUtils.isNotEmpty(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing())) {
    		List<PricingDetails> details = new ArrayList<>();
    		for (Map.Entry<LOB, DisplayPriceBreakDown> entry : totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().entrySet()) {
    			if (!LOB.HOTEL.name().equalsIgnoreCase(entry.getKey().name()) && !LOB.INSURANCE.name().equalsIgnoreCase(entry.getKey().name())) {
    				details.addAll(commonResponseTransformer.getPricingDetails(entry.getValue(), countryCode, null, false, "",expData, false,false, null, null, null, null, 0.0,0.0));
    			}
			}
    		return details;
    	}
    	return null;
    }
	private String processDormTypes(RoomTypeDetails roomTypeDetails) {
		Set<String> dormTypeSet = new HashSet<>();

		//collect dormType
		if (roomTypeDetails != null && roomTypeDetails.getRoomType() != null) {
			for (Map.Entry<String, RoomType> entry : roomTypeDetails.getRoomType().entrySet()) {
				RoomType roomType = entry.getValue();

				if (roomType.getDormType() != null) {
					dormTypeSet.add(roomType.getDormType().toLowerCase());
				}
			}
		}

		// determine the dorm types
		if (!dormTypeSet.isEmpty()) {
			boolean isMaleDormType = false;
			boolean isFemaleDormType = false;

			// Find male and female dorm types
			for (String dormType : dormTypeSet) {

				if (MALE_GENDER.equalsIgnoreCase(dormType)) {
					isMaleDormType = true;
				}
				if (FEMALE_GENDER.equalsIgnoreCase(dormType)) {
					isFemaleDormType = true;
				}
			}

			if (isMaleDormType && isFemaleDormType) {
				return polyglotService.getTranslatedData("MIXED_DORM_POPUP_TEXT");
			} else if (isMaleDormType) {
				return polyglotService.getTranslatedData("MALE_DORM_POPUP_TEXT");
			} else if (isFemaleDormType) {
				return polyglotService.getTranslatedData("FEMALE_DORM_POPUP_TEXT");
			}
		}
		return null;
	}


    private List<RatePlan> getRatePlanDetails(RoomTypeDetails roomTypeDetails, BNPLDetails bnplDetails, Map<String, Integer> roomBedCount, Integer ap, boolean isUserGccAndMmtExclusive, Map<String, String > expDataMap, Map<String, List<String>> roomImageMap,
											  String listingType, LoyaltyMessageResponse loyaltyData, ClmPersuasion clmData, BlackInfo blackInfo, String countryCode, String propertyType) {
        List<RatePlan> ratePlansList = new ArrayList<>();
        boolean bnplApplicable = (bnplDetails != null) && bnplDetails.isBnplApplicable();
        RatePolicy confirmationPolicy = commonResponseTransformer.getConfirmationPolicy(roomTypeDetails);
        String confirmationPolicyType = (confirmationPolicy != null) ? confirmationPolicy.getValue() : null;
        String baseRoomCode = null;
        if (roomTypeDetails.getRoomType().entrySet().stream().anyMatch((roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()))) {
            baseRoomCode = roomTypeDetails.getRoomType().entrySet().stream().filter(roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()).findFirst().get().getValue().getRoomTypeCode();
        }
        for (String roomCode : roomTypeDetails.getRoomType().keySet()) {
            RoomType roomType = roomTypeDetails.getRoomType().get(roomCode);
            ratePlansList.addAll(getRatePlansList(roomType.getRatePlanList(), roomType.getRoomTypeName(), roomType.getDormType() , roomCode, bnplApplicable,confirmationPolicyType,roomType.getSellableType(),roomBedCount,ap, isUserGccAndMmtExclusive, expDataMap,  StringUtils.equalsIgnoreCase(roomCode, baseRoomCode), roomImageMap, listingType, blackInfo, countryCode, roomTypeDetails.getOccassionDetails(), propertyType));
		}
		if (CollectionUtils.isNotEmpty(ratePlansList) && ratePlansList.size() > 0) {
			ratePlansList.get(0).setOffersInclusionsList(commonResponseTransformer.getOffersInclusionList(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown(), expDataMap, loyaltyData, clmData, countryCode));
		}
		return ratePlansList;
    }

    private List<RatePlan> getRatePlansList(Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanList,
											String roomName, String dormType, String roomCode, boolean bnplApplicable,
											String confirmationPolicyType, String sellableType, Map<String, Integer> roomBedCount,
											Integer ap, boolean isUserGccAndMmtExclusive, Map<String, String > expDataMap, boolean baseRoom, Map<String, List<String>> roomImageMap,
											String listingType, BlackInfo blackInfo, String countryCode, OccassionDetails occassionDetails, String propertyType) {
        List<RatePlan> ratePlans = new ArrayList<>();
		boolean blackRevamp = utility.isExperimentTrue(expDataMap, GOTRIBE_REVAMP_POKUS_EXP_KEY);
        for (String code: ratePlanList.keySet()) {
            RatePlan ratePlan = new RatePlan();
            com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = ratePlanList.get(code);
			ratePlan.setRoomName(roomName);
			ratePlan.setType(ratePlanCB.getRatePlanType());
			ratePlan.setCode(code);
			ratePlan.setDormType(dormType);

			ratePlan.setName(utility.getRatePlanName(ratePlanCB.getMealPlans(), ratePlan.getCancellationPolicy(), ratePlan.getSellableType(), listingType, expDataMap));
            ratePlan.setRoomCode(roomCode);
            ratePlan.setSegmentId(ratePlanCB.getSegmentId());
            ratePlan.setDescription(ratePlanCB.getRatePlanDesc());
            ratePlan.setSuppliercode(ratePlanCB.getSupplierDetails() !=null ? ratePlanCB.getSupplierDetails().getSupplierCode() : null);
            ratePlan.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(ratePlanCB.getCancellationTimeline()));
            ratePlan.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(ratePlanCB.getCancellationTimeline()));
			BNPLVariant bnplVariant = ratePlanCB.getBnplVariant();
			boolean isRecommendation = false;
			if(ratePlanCB.getAvailDetails() != null)
				isRecommendation = ratePlanCB.getAvailDetails().getNumOfRooms() > 1;
			String freeChildText = ratePlanCB.getFreeChildCount() > 0 && StringUtils.isNotEmpty(ratePlanCB.getFreeChildText()) && !isRecommendation ? ratePlanCB.getFreeChildText() : null;
            ratePlan.setCancellationPolicy(utility.transformCancellationPolicy(ratePlanCB.getCancelPenaltyList(), bnplApplicable, bnplVariant, confirmationPolicyType, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap, ratePlanCB.getCancellationTimeline()));
            commonResponseTransformer.updateCancelPolicyDescription(ratePlan, ratePlanCB.getCancelPenaltyList());
			int manthanGocash = 0;
			if (Utility.gocashVariant(expDataMap, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) && CommonResponseTransformer.getManthanGocashValueValidateCoupon(ratePlanCB.getDisplayFare().getDisplayPriceBreakDown()) > 0){
				manthanGocash = ratePlanCB.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY).intValue();
			}
            ratePlan.setInclusionsList(utility.transformInclusions(ratePlanCB.getMealPlans(), ratePlanCB.getInclusions(), mealPlanMapPolyglot,
					ratePlanCB.getSupplierDetails()!= null ? ratePlanCB.getSupplierDetails().getSupplierCode() :"",ap, ratePlanCB.getExtraGuestDetail(), expDataMap,ratePlanCB.getCancelPenaltyList(),ratePlanCB.isBnplApplicable(),ratePlanCB.getBnplVariant(),ratePlanCB.getPaymentDetails(), freeChildText, manthanGocash));

			if (CollectionUtils.isNotEmpty(ratePlan.getInclusionsList()) && blackRevamp && blackInfo != null)
				utility.restructureBlackInclusions(ratePlan, blackInfo, expDataMap != null && (expDataMap.containsKey("goTribe3") && "true".equalsIgnoreCase(expDataMap.get("goTribe3"))));

            if (CollectionUtils.isNotEmpty(ratePlanCB.getMealPlans())) {
            	ratePlan.setMealCode(ratePlanCB.getMealPlans().get(0).getCode());
            }
            ratePlan.setOccupancydetails(getOccupancyDetails(ratePlanCB.getAvailDetails()));
            ratePlan.setRoomTariff(getRoomTariffInfo(ratePlanCB.getRoomTariff(),roomBedCount, sellableType, propertyType));
            ratePlan.setSellableType(sellableType);
			if (utility.isShowOccassionPackagesPlanExperimentOn(expDataMap) && occassionDetails != null) {
				ratePlan.setHighlightImage(occassionDetails.getPersuasionImageUrl());
				ratePlan.setType(occassionDetails.getOccassionType());
			}
			if(utility.isSPKGExperimentOn(expDataMap) && ratePlanCB.getPackageRoomRatePlan()) {
				ratePlan.setPackageRateAvailable(true);
				ratePlan.setType(elitePackageType);
				ratePlan.setHighlightImage(elitePackageIconUrl);
				utility.transformInclusionsForPackageRatePlan(ratePlan.getInclusionsList());
			}
            // update alerts later
			ratePlan.setCorpRateTags(buildCorpRateTags(ratePlanCB.getCorpMetaData()));

			ratePlan.setRatePlanPersuasions(buildRatePlanPersuasion(ratePlanCB));
			//Creating Node For MmtExclusive detail Page
			if(isUserGccAndMmtExclusive) {
				Map<String, MmtExclusive> card = new HashMap<>();
				card.put(Constants.GCC_EXCLUSIVE, utility.buildMmtExclusiveNode(ratePlanCB.getInclusions()));
				ratePlan.setCards(card);
			}
			if(MapUtils.isNotEmpty(roomImageMap)){
				ratePlan.setImages(roomImageMap.get(roomCode));
			}
			ratePlans.add(ratePlan);
		}

		if(baseRoom && CollectionUtils.isNotEmpty(ratePlans)) {
			ratePlans.get(0).setBasePlan(true);
		}
        return ratePlans;
    }

	private Map<String, PersuasionResponse> buildRatePlanPersuasion(com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB)
	{

		Map<String, PersuasionResponse> ratePlanPersuasions = new HashMap<>();
		if(ratePlanCB!=null && ratePlanCB.getSupplierDetails()!=null && supplierToRateSegmentMapping!=null && supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY)!=null &&
				supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanCB.getSupplierDetails().getSupplierCode())!=null
				&& (supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanCB.getSupplierDetails().getSupplierCode()).isEmpty()
		|| supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanCB.getSupplierDetails().getSupplierCode()).contains(ratePlanCB.getSegmentId()))) {

			ratePlanPersuasions.put(Constants.MYPARTNER_EXPEDIA_PKG_RATE,buildExpediaPackageRatePersuasion());
		}
		return ratePlanPersuasions.isEmpty() ? null:ratePlanPersuasions;
	}

	private PersuasionResponse buildExpediaPackageRatePersuasion(){

		PersuasionResponse persuasion = new PersuasionResponse();
		persuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_RATE_TEXT));
		persuasion.setId(Constants.MYPARTNER_EXPEDIA_PKG_RATE);
		return persuasion;
	}
	private List<CorpRateTags> buildCorpRateTags(CorpMetaInfo corpMetaData) {
		if (corpMetaData!=null && CollectionUtils.isNotEmpty(corpMetaData.getTags())){
			List<CorpRateTags> corpRateTagsList = commonResponseTransformer.buildTags(corpMetaData.getTags());
			return corpRateTagsList;
		}
		return null;
	}


    private String getRatePlanPayMode(RoomTypeDetails roomTypeDetails) {
    	for (Map.Entry<String, RoomType> entry : roomTypeDetails.getRoomType().entrySet()) {
			List<com.mmt.hotels.model.response.pricing.RatePlan> ratePlanlist = entry.getValue().getRatePlanList().entrySet().stream().map(ratePlan -> ratePlan.getValue()).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(ratePlanlist) && ratePlanlist.get(0).getPaymentDetails() !=null &&
					ratePlanlist.get(0).getPaymentDetails().getPaymentMode() !=null) {
				return ratePlanlist.get(0).getPaymentDetails().getPaymentMode().name();
			}
		}
    	return null;
    }

    private boolean childAgesSame(List<Integer> currentTariffChildAges, List<Integer> nextTariffChildAges) {
    	if(CollectionUtils.isEmpty(currentTariffChildAges) && CollectionUtils.isEmpty(nextTariffChildAges))
    		return true;
    	if(CollectionUtils.isEmpty(currentTariffChildAges) || CollectionUtils.isEmpty(nextTariffChildAges))
    		return false;
    	if(currentTariffChildAges.size() == nextTariffChildAges.size()) {
			Collections.sort(currentTariffChildAges);
			Collections.sort(nextTariffChildAges);
			int index = 0;
			for (; index < currentTariffChildAges.size(); index++) {
				if (!currentTariffChildAges.get(index).equals(nextTariffChildAges.get(index)))
					return false;
			}
			return index == currentTariffChildAges.size();
		}
    	return false;
    }

    private boolean isSameRoomTariff(com.mmt.hotels.model.response.pricing.RoomTariff currentTariff,
    		com.mmt.hotels.model.response.pricing.RoomTariff nextTariff) {
    	if(currentTariff !=null && nextTariff !=null) {
			return currentTariff.getNumberOfAdults() == nextTariff.getNumberOfAdults() &&
					currentTariff.getNumberOfChildren() == nextTariff.getNumberOfChildren() &&
					childAgesSame(currentTariff.getChildAges(), nextTariff.getChildAges());
		}
    	return false;
    }

    private List<RoomTariff> getRoomTariffInfo(List<com.mmt.hotels.model.response.pricing.RoomTariff> roomTariffs, Map<String, Integer> roomBedCount, String sellableType, String propertyType){
    	if(CollectionUtils.isNotEmpty(roomTariffs)) {
    		List<RoomTariff> roomTariffList = new ArrayList<>();
    		List<Integer> removeIndexes = new ArrayList<>();
    		for (com.mmt.hotels.model.response.pricing.RoomTariff roomTariff : roomTariffs) {
    			int index = roomTariffs.indexOf(roomTariff);
    			if(!removeIndexes.contains(index)) {
        			RoomTariff tariff = new RoomTariff();
        			tariff.setNumberOfAdults(roomTariff.getNumberOfAdults());
        			tariff.setNumberOfChildren(roomTariff.getNumberOfChildren());
        			tariff.setChildAges(roomTariff.getChildAges());
        			tariff.setRoomCount(1);
        			int nextIndex = index + 1;
    				while(nextIndex < roomTariffs.size() && !removeIndexes.contains(nextIndex)) {
    					com.mmt.hotels.model.response.pricing.RoomTariff nextRoomTariff = roomTariffs.get(nextIndex);
    					if(isSameRoomTariff(roomTariff, nextRoomTariff)) {
    						removeIndexes.add(nextIndex);
    						tariff.setRoomCount(tariff.getRoomCount() + 1);
    					}
    					nextIndex++;
    				}
					int count = tariff.getRoomCount();

					if ("hostel".equalsIgnoreCase(propertyType)) {
						roomBedCount.put(Constants.SELLABLE_BED_TYPE, roomBedCount.get(Constants.SELLABLE_BED_TYPE) + count);

					} else {
						if (Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(sellableType)) {
							roomBedCount.put(Constants.SELLABLE_BED_TYPE, roomBedCount.get(Constants.SELLABLE_BED_TYPE) + count);
						} else {
							roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, roomBedCount.get(Constants.SELLABLE_ROOM_TYPE) + count);
						}
					}

    				roomTariffList.add(tariff);
    			}
			}
    		return roomTariffList;
    	}
    	return null;
    }

    private OccupancyDetail getOccupancyDetails(AvailDetails availDetails) {
        if (availDetails == null || availDetails.getOccupancyDetails() == null)
            return null;
        OccupancyDetail occupancyDetail = new OccupancyDetail();
        occupancyDetail.setNumOfRooms(availDetails.getNumOfRooms());
        occupancyDetail.setAdult(availDetails.getOccupancyDetails().getAdult());
        occupancyDetail.setChild(availDetails.getOccupancyDetails().getChild());
        return occupancyDetail;
    }

	private List<MealPlan> getMealPlanList(String mealCode) {
		if (StringUtils.isBlank(mealCode))
			return null;
		MealPlan mealPlan = new MealPlan();
		mealPlan.setCode(mealCode);
		return Collections.singletonList(mealPlan);
	}

	private List<UpsellOptions> buildUpsellOptions(HotelRates hotelRates) {
		if (hotelRates!=null && hotelRates.getRoomTypeDetails()!=null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			Optional<RoomType> roomTypeEntry = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst();
			List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
			roomTypeEntry.ifPresent(roomType -> {
				List<com.mmt.hotels.model.response.pricing.RatePlan> list = roomType.getRatePlanList().values().stream().filter(e->e.getUpsellOptions()!=null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(list)) {
					list.forEach(ratePlan -> {
						upsellOptionsList.addAll(makeUpsellOptions(ratePlan.getUpsellOptions()));
					});
				}
			});
			if (CollectionUtils.isNotEmpty(upsellOptionsList))
				return upsellOptionsList;
		}
		return null;
	}

	private List<UpsellOptions> buildDownsellOptions(HotelRates hotelRates) {
		if (hotelRates != null && hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			Optional<RoomType> roomTypeEntry = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst();
			List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> downsellOptionsList = new ArrayList<>();
			roomTypeEntry.ifPresent(roomType -> {
				List<com.mmt.hotels.model.response.pricing.RatePlan> list = roomType.getRatePlanList().values().stream().filter(e -> e.getUpsellOptions() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(list)) {
					list.forEach(ratePlan -> {
						downsellOptionsList.addAll(makeDownsellOptions(ratePlan.getUpsellOptions()));
					});
				}
			});
			if (CollectionUtils.isNotEmpty(downsellOptionsList))
				return downsellOptionsList;
		}
		return null;
	}

	private List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> makeUpsellOptions(List<com.mmt.hotels.model.response.pricing.UpsellOptions> list) {
		List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
		list.forEach(u -> {
			if (UPSELL.equalsIgnoreCase(u.getRatePlanType())) {
				UpsellOptions upsellOptionsCG = new UpsellOptions();
				upsellOptionsCG.setMtKey(u.getMtKey());
				upsellOptionsCG.setRoomCode(u.getRoomCode());
				upsellOptionsCG.setRatePlanType(u.getRatePlanType());
				upsellOptionsCG.setDiscountLabel(u.getDiscountLabel());
				upsellOptionsCG.setRatePlanCode(u.getRatePlanCode());
				upsellOptionsCG.setDisplayText(u.getDisplayText());
				upsellOptionsCG.setSuccessDisplayText(u.getSuccessDisplayText());
				upsellOptionsCG.setFailureDisplayText(u.getFailureDisplayText());
				upsellOptionsCG.setDescriptionText(u.getDescriptionText());
				upsellOptionsList.add(upsellOptionsCG);
			}
		});
		return upsellOptionsList;
	}


	private List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> makeDownsellOptions(List<com.mmt.hotels.model.response.pricing.UpsellOptions> list) {
		List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
		list.forEach(u -> {
			if (DOWNSELL.equalsIgnoreCase(u.getRatePlanType())) {
				UpsellOptions upsellOptionsCG = new UpsellOptions();
				upsellOptionsCG.setMtKey(u.getMtKey());
				upsellOptionsCG.setRoomCode(u.getRoomCode());
				upsellOptionsCG.setRatePlanType(u.getRatePlanType());
				upsellOptionsCG.setRatePlanCode(u.getRatePlanCode());
				upsellOptionsCG.setDiscountLabel(u.getDiscountLabel());
				upsellOptionsCG.setDisplayText(u.getDisplayText());
				upsellOptionsCG.setSuccessDisplayText(u.getSuccessDisplayText());
				upsellOptionsCG.setFailureDisplayText(u.getFailureDisplayText());
				upsellOptionsCG.setDescriptionText(u.getDescriptionText());
				upsellOptionsCG.setSuccessData(buildSuccessData(u.getSuccessData()));
				upsellOptionsList.add(upsellOptionsCG);
			}
		});
		return upsellOptionsList;
	}

	private SuccessData buildSuccessData(com.mmt.hotels.model.response.pricing.SuccessData successData) {
		SuccessData successDataCG = new SuccessData();
		successDataCG.setText(successData.getText());
		successDataCG.setDescription(successData.getDescription());
		return successDataCG;
	}

	private DayUseInfo buildDayUseInfo(HotelRates hotelRates) {
		boolean allRoomsDayUse = true;
		String roomName = StringUtils.EMPTY;
		if (hotelRates.getRoomTypeDetails()!=null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			for (RoomType roomType : hotelRates.getRoomTypeDetails().getRoomType().values()) {
				boolean isRoomDayUse = false;
				if (CollectionUtils.isNotEmpty(sameDayRoomNames)) {
					roomName = roomType.getRoomTypeName();
					isRoomDayUse = sameDayRoomNames.stream()
							.anyMatch(e -> roomType.getRoomTypeName().toLowerCase().contains(e.toLowerCase()));
				}
				allRoomsDayUse = allRoomsDayUse && isRoomDayUse;
			}
		}
		if (allRoomsDayUse) {
			DayUseInfo dayUseInfo = new DayUseInfo();
			dayUseInfo.setAlertText(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE_ROOM_ALERT_TEXT));
			dayUseInfo.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE_ROOM_LABEL));
			if (StringUtils.isNotBlank(roomName)) {
				Tuple<String,String> timmings = Utility.getCheckinAndCheckoutForDailyUse(roomName);
				if (timmings!=null) {
					dayUseInfo.setCheckinDate(timmings.getX());
					dayUseInfo.setCheckoutDate(timmings.getY());
				}
			}
			if (StringUtils.isBlank(dayUseInfo.getCheckinDate())||StringUtils.isBlank(dayUseInfo.getCheckoutDate())) {
				dayUseInfo.setCheckoutDate(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE));
				dayUseInfo.setCheckinDate(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE));
			}
			return dayUseInfo;
		}
		return null;
	}

	private CorpData buildCorpData(boolean corpCapturePersonalBookingGstn) {
		CorpData corpData = new CorpData();
		corpData.setCorpCapturePersonalBookingGstn(corpCapturePersonalBookingGstn);
		return corpData;
	}

	private SoldOutCallOut buildSoldOutCallOutInfo(HotelRates hotelRates, AvailRoomsResponse availRoomsResponse) {
		if(!Constants.DOM_COUNTRY.equalsIgnoreCase(hotelRates.getCountryCode())) {
			Optional<AlertInfo> priceAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.PRICE.equals(al.getMismatchType())).findAny();
			Optional<AlertInfo> cancellationAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.CANCELLATION.equals(al.getMismatchType())).findAny();
			Optional<AlertInfo> mealPlanAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.MEALPLAN.equals(al.getMismatchType())).findAny();
			Optional<AlertInfo> rpccAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.RPCC.equals(al.getMismatchType())).findAny();
			Optional<AlertInfo> roomCodeAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.ROOM_CODE.equals(al.getMismatchType())).findAny();

			if(!priceAlert.isPresent()) { // If any case where there is no change in price then no banner is shown.
				return null;
			}
			AlertInfo alertInfo = priceAlert.get();
			Double amount = Utility.round(getAlertAmount(alertInfo), 0);
			String priceText = "";

			// Get currency symbol based on user selected currency.
			String currencyType = availRoomsResponse.getTotalpricing().getCurrency();
			String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currencyType) ? currencyType : DEFAULT_CUR_INR).getCurrencySymbol();
			List<AlertInfo.AlertType> reasons = hotelRates.getAlerts().stream().map(AlertInfo::getMismatchType).collect(Collectors.toList());

			SoldOutCallOut SoldOutCallOutInfo = new SoldOutCallOut();
			if(hotelRates.isBackupVendorRate()) {
				// THREE cases are possible.
				// 1. price, rpcc.
				// 2. price, rpcc, room_code.
				// 3. price, rpcc, room_code, one or both of (cancellation, mealplan)
				if(priceAlert.isPresent() && rpccAlert.isPresent() && roomCodeAlert.isPresent()) {
					if(cancellationAlert.isPresent() || mealPlanAlert.isPresent()) { // Case where any mismatch comes from pricer. [i,e Differnet room code is chosen as back up with different cancellation & meal plan policy]
						if (priceIncreaseAlert(alertInfo)) {
							SoldOutCallOutInfo.setPriceChange(PRICE_INCREASE);
							priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_ALL_OPTIONS);
							SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_ALL_OPTIONS));
							SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_ALL_OPTIONS));
						} else {
							SoldOutCallOutInfo.setPriceChange(PRICE_DECREASE);
							priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_ALL_OPTIONS);
							SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_ALL_OPTIONS));
							SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_ALL_OPTIONS));
						}
					} else { // Case where only price, rpcc and room code mismatch comes from pricer. [i,e Differnet room code is chosen as back up with same cancellation & meal plan policy]
						if (priceIncreaseAlert(alertInfo)) {
							SoldOutCallOutInfo.setPriceChange(PRICE_INCREASE);
							priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_WITH_RPCC_ROOMCODE);
							SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_RPCC_ROOMCODE));
							SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_WITH_RPCC_ROOMCODE));
						} else {
							SoldOutCallOutInfo.setPriceChange(PRICE_DECREASE);
							priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_WITH_RPCC_ROOMCODE);
							SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_WITH_RPCC_ROOMCODE));
							SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_WITH_RPCC_ROOMCODE));
						}
					}
				} else if (priceAlert.isPresent() && rpccAlert.isPresent()) { // Case where only price, rpcc mismatch comes from pricer. [i,e Same SubVendor's back up rate is chosen]
					if (priceIncreaseAlert(alertInfo)) {
						SoldOutCallOutInfo.setPriceChange(PRICE_INCREASE);
						priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_WITH_RPCC);
						SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_RPCC));
						SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_WITH_RPCC));
					} else {
						SoldOutCallOutInfo.setPriceChange(PRICE_DECREASE);
						priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_WITH_RPCC);
						SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_WITH_RPCC));
						SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_WITH_RPCC));
					}
				}
			} else {
				if (priceIncreaseAlert(alertInfo)) {
					SoldOutCallOutInfo.setPriceChange(PRICE_INCREASE);
					if(priceAlert.isPresent()) { // Only price mismatched occurred without any backup rate.
						priceText = polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_SUB_TITLE_ONLY_PRICE_INC);
						SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_TITLE_ONLY_PRICE_INC));
						SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_BG_COLOUR_ONLY_PRICE_INC));
					}
				} else {
					SoldOutCallOutInfo.setPriceChange(PRICE_DECREASE);
					if(priceAlert.isPresent()) { // Only price mismatched has occurred without any back up rate.
						priceText = polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_SUB_TITLE_ONLY_PRICE_DEC);
						SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_TITLE_ONLY_PRICE_DEC));
						SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_BG_COLOUR_ONLY_PRICE_DEC));
					}
				}
			}
			//Added default handling :: Currently some mismatch types coming from pricer are not as per table shared in jira. Adding this as per product request.
			if(hotelRates.isBackupVendorRate() && priceText.isEmpty()) {
				if (priceIncreaseAlert(alertInfo)) {
					SoldOutCallOutInfo.setPriceChange(PRICE_INCREASE);
					priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_INC_WITH_RPCC);
					SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_INC_WITH_RPCC));
					SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_INC_WITH_RPCC));
				} else {
					SoldOutCallOutInfo.setPriceChange(PRICE_DECREASE);
					priceText = polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_SUB_TITLE_PRICE_DEC_WITH_RPCC);
					SoldOutCallOutInfo.setTitle(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_TITLE_PRICE_DEC_WITH_RPCC));
					SoldOutCallOutInfo.setBgColor(polyglotService.getTranslatedData(SOLD_OUT_HANDLING_BANNER_BG_COLOUR_PRICE_DEC_WITH_RPCC));
				}
			}

			if (StringUtils.isNotEmpty(priceText)) {
				priceText = priceText.replace(Constants.CURRENCY_SYMBOL, currencySymbol);
				priceText = priceText.replace(Constants.AMOUNT, amount.toString());
				SoldOutCallOutInfo.setSubTitles(new String[]{priceText});
			}
			SoldOutCallOutInfo.setTitleColor((polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_TITLE_COLOUR)));
			SoldOutCallOutInfo.setIconUrl((polyglotService.getTranslatedData(ConstantsTranslation.SOLD_OUT_HANDLING_BANNER_IMAGE_URL)));
			SoldOutCallOutInfo.setReasons(reasons);
			return SoldOutCallOutInfo;
		}
		return null;
	}

	public PayLaterEligibilityResponse convertPayLaterEligibilityResponse(String client, com.mmt.hotels.model.response.PayLaterEligibilityResponse hesResponse,boolean isMemoize) {
		PayLaterEligibilityResponse response = new PayLaterEligibilityResponse();
		PayLaterCard card = new PayLaterCard();
		if(isMemoize && hesResponse!=null && !hesResponse.isEligible())
		{
			response.setEligible(hesResponse.isEligible());
			response.setPayLaterCard(buildPayLaterCard());
			response.setCheckEligibility(true);
			return response;
		}
		String titleIcon = tripMoneyIconApps;
		String failureLogo = tripMoneyWhiteLogoApps;
		String successLogo = tripMoneyDarkLogoApps;
		if(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)){
			titleIcon = tripMoneyIconWeb;
			failureLogo = tripMoneyWhiteLogoWeb;
			successLogo = tripMoneyDarkLogoWeb;
		}
		if (hesResponse.isEligible()) {
			card.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUCCESS_TITLE));
			card.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUCCESS_SUBTITLE));
			card.setDesc(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_DESCRIPTION));
			card.setSuccessCard(buildSuccessCard(Utility.round(hesResponse.getAmount(),0), successLogo));
		} else {
			card.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_FAILURE_TITLE));
			card.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_FAILURE_SUBTITLE));
			card.setTitleIcon(titleIcon);
			card.setLogo(failureLogo);
		}
		response.setEligible(hesResponse.isEligible());
		response.setPayLaterCard(card);
		return response;
	}

	private PayLaterSuccessCard buildSuccessCard(double amount, String successLogo) {
		PayLaterSuccessCard card = new PayLaterSuccessCard();
		card.setAmount(amount);
		card.setCurrency(Constants.DEFAULT_CUR_INR);
		card.setLogo(successLogo);
		card.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUCCESS_CARD_TITLE));
		return card;
	}

	private Map<String, List<String>> buildRoomImageMap(HotelImage hotelImage) {
		/* START <RoomCode,ImageURLsList> map */
		Map<String, List<String>> roomImageMap = new HashMap<>();
		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())
				&& hotelImage.getImageDetails().getProfessional().containsKey("R")) {
			List<ProfessionalImageEntity> images = hotelImage.getImageDetails().getProfessional().get("R");
			if (CollectionUtils.isNotEmpty(images)) {
				images.forEach(image -> {
					if (StringUtils.isNotBlank(image.getUrl())) {
						roomImageMap.computeIfAbsent(image.getCatCode(), k -> new ArrayList<>());
						roomImageMap.get(image.getCatCode()).add(image.getUrl().startsWith("http") ? image.getUrl() : "https:" + image.getUrl());
					}
				});
			}
		}
		return roomImageMap;
	}

	private PriceChangeInfo getPriceChangeInfo(HotelRates hotelRates) {
		if (CollectionUtils.isNotEmpty(hotelRates.getAlerts())) {
			//build price change info
			Optional<AlertInfo> priceAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.PRICE.equals(al.getMismatchType()))
					.findAny();
			if (priceAlert.isPresent()) {
				return priceIncreaseAlert(priceAlert.get()) ? PriceChangeInfo.PRICE_INCREASE : PriceChangeInfo.PRICE_DECREASE;
			}
		}
		return PriceChangeInfo.PRICE_NO_CHANGE;
	}

	private UGCSummary buildUgcSummary(CBPlatformSummaryResponse data) {
		try {
			UGCSummary ugcSummary = null;
			if (data != null && data.getSummary() != null) {
				ugcSummary = new UGCSummary();
				UGCPlatformReviewSummaryDTO summary = objectMapperUtil.getObjectFromJsonNode(data.getSummary().get("data"), new TypeReference<UGCPlatformReviewSummaryDTO>() {
				});
				ugcSummary.setData(summary);
				ugcSummary.setCardTitle(REVIEW_RATING_TITLE);
			}
			return ugcSummary;
		}
		catch (Exception e) {
			LOGGER.warn("Failed to build ugcSummary : {}", e.getMessage(), e);
			return null;
		}
	}
}