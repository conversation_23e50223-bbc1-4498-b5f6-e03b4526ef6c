package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.gi.hotels.model.response.staticdata.RoomAdvisory;
import com.gommt.hotels.orchestrator.detail.enums.BNPLVariant;
import com.gommt.hotels.orchestrator.detail.enums.RoomType;
import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.BenefitType;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.PackageDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.OfferCard;
import com.mmt.hotels.clientgateway.enums.SelectRoomCardFilterType;
import com.mmt.hotels.clientgateway.pms.*;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.TrailingCtaBottomSheet;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.enums.CardTemplateId;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.LinkedRate;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.PaymentModeDescriptionPolicy;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature;
import com.mmt.hotels.clientgateway.response.staticdetail.AllInclusiveCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.InjectHermesResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.SelectRoomV2ResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.*;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.model.LocusData;
import com.mmt.model.SleepingArrangement;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.roomUpsell;
import static com.mmt.hotels.clientgateway.util.Utility.getExperimentValue;

@Component
public abstract class OrchSearchRoomsResponseTransformer {

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private Utility utility;

    private static final Logger logger = LoggerFactory.getLogger(OrchSearchRoomsResponseTransformer.class);

    @Autowired
    PropertyManager propManager;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private DayUseUtil dayUseUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Value("${searchrooms.rateplan.name.config}")
    private String ratePlanNameConfig;

    @Value("${pah.without.cc.text}")
    private String pahwithoutccText;

    @Value("${pah.with.cc.text}")
    private String pahwithccText;

    @Value("${pah.gcc.text}")
    private String pahGccText;

    @Value("#{'${mypat_exclusive_rate_segmentId.list}'.split(',')}")
    private Set<String> mypatExclusiveRateSegmentIdList;

    @Value("${flyer.persuasion.color.detail}")
    private String flyerPersuasionColorDetail;

    @Value("${flyer.persuasion.image.url.detail}")
    private String flyerPersuasionImageUrlDetail;

    @Value("#{'${combo.title.meal.plan.code}'.split(',')}")
    private List<String> mealPlanCodeList;

    @Value("${searchrooms.rateplan.name.config}")
    private String ratePlanNameConfigProperty;

    @Value("${searchrooms.rateplan.redesign}")
    private String ratePlanNameConfigRedesign;

    @Autowired
    private ReArchUtility reArchUtility;

    private List<String> translateEnabledSupplierCodes = new ArrayList<>();

    private int apLimitForInclusionIcons = 2;

    private Map<String, String> mealPlanMapPolyglot;

    private int ratePlanMoreOptionsLimit = 1;
    private boolean mealplanFilterEnable;
    private boolean partnerExclusiveFilterEnable;
    private Map<String, Map<String, List<String>>> supplierToRateSegmentMapping;

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private SearchRoomsMediaHelper searchRoomsMediaHelper;

    @Autowired
    private CancellationPolicyHelper cancellationPolicyHelper;

    @Autowired
    private SearchRoomsPriceHelper searchRoomsPriceHelper;

    @Autowired
    private RoomAmentiesHelper roomAmentiesHelper;

    @Autowired
    DeepLinkHelper deepLinkHelper;


    @Autowired
    private InjectHermesResponseTransformer injectHermesResponseTransformer;

    private Map<String, Map<String, Map<String, String>>> ratePlanNameMap;

    private  Map<String, Map<String, Map<String, String>>> ratePlanNameMapRedesign;

    private static final Gson gson = new Gson();
    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchRoomsResponseTransformer.class);

    private Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic;
    Map<String, String> rtbCardConfigs;

    String mandatoryChargesAlert;

    AllInclusiveCard allInclusiveCard;

    private MissingSlotDetail missingSlotDetails = null;

    private Map<String, DayUsePersuasion> dayUseFunnelPersuasions = null;

    private Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap;

    @Value("${group.filters.GI}")
    private String groupFilterMap;

    private int thresholdForSlashedAndDefaultHourPrice = 0;

    private Map<String, StayTypeInfo> actionInfoMap = new HashMap<>();

    @Autowired
    private MobConfigHelper mobConfigHelper;

    @Value("${elite.package.icon.url}")
    private String elitePackageIconUrl;

    @Value("${elite.package.icon.url.updated}")
    private String elitePackageIconUrlUpdated;

    @Value("${elite.package.type}")
    private String elitePackageType;

    @Autowired
    SelectRoomV2ResponseTransformer selectRoomV2ResponseTransformer;

    @Autowired
    SearchRoomsPersuasionHelper searchRoomsPersuasionHelper;

    @Autowired
    private RoomInfoHelper roomInfoHelper;

    @PostConstruct
    public void init() {
        CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
        thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
        ratePlanNameMap = gson.fromJson(ratePlanNameConfig, new com.google.gson.reflect.TypeToken<Map<String,Map<String,Map<String,String>>> >() {
        }.getType());
        ratePlanNameMapRedesign = gson.fromJson(ratePlanNameConfigRedesign, new com.google.gson.reflect.TypeToken<Map<String,Map<String,Map<String,String>>> >() {
        }.getType());
        mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
        commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
        ratePlanMoreOptionsLimit  = commonConfig.ratePlanMoreOptionsLimit();
        commonConfig.addPropertyChangeListener("ratePlanMoreOptionsLimit", evt -> ratePlanMoreOptionsLimit = commonConfig.ratePlanMoreOptionsLimit());
        ratePlanDisplayLogic = commonConfig.ratePlanDisplayLogic();
        commonConfig.addPropertyChangeListener("ratePlanDisplayLogic",evt->ratePlanDisplayLogic = commonConfig.ratePlanDisplayLogic());
        apLimitForInclusionIcons = commonConfig.apLimitForInclusionIcons();
        mealplanFilterEnable = commonConfig.mealplanFilterEnable();
        commonConfig.addPropertyChangeListener("mealplanFilterEnable", event -> mealplanFilterEnable = commonConfig.mealplanFilterEnable());
        partnerExclusiveFilterEnable = commonConfig.partnerExclusiveFilterEnable();
        commonConfig.addPropertyChangeListener("partnerExclusiveFilterEnable", event -> partnerExclusiveFilterEnable = commonConfig.partnerExclusiveFilterEnable());
        rtbCardConfigs = commonConfig.rtbCardConfigs();
        commonConfig.addPropertyChangeListener("rtbCardConfigs", event -> rtbCardConfigs = commonConfig.rtbCardConfigs());
        mandatoryChargesAlert = commonConfig.mandatoryChargesAlert();
        commonConfig.addPropertyChangeListener("mandatoryChargesAlert", event -> mandatoryChargesAlert = commonConfig.mandatoryChargesAlert());
        allInclusiveCard = commonConfig.allInclusiveCard();
        commonConfig.addPropertyChangeListener("allInclusiveCard", event -> allInclusiveCard = commonConfig.allInclusiveCard());
        supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping();
        commonConfig.addPropertyChangeListener("supplierToRateSegmentMapping", event -> supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping());
        //Added missing slot and persuasion details of dayUse detail page
        missingSlotDetails = commonConfig.missingSlotDetails();
        commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
        dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions();
        commonConfig.addPropertyChangeListener("dayUseFunnelPersuasions", event -> dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions());
        groupRatePlanFilterConfMap = gson.fromJson(groupFilterMap, new TypeToken<Map<String, GroupRatePlanFilter>>() {
        }.getType());

        try {
            HotelMobConfig hotelMobileConfigFromPMS = mobConfigHelper.getHotelMobileConfigFromPMS();
            JsonNode actionInfoJsonNode = hotelMobileConfigFromPMS.getConfigJson().get("actionInfo");
            actionInfoMap = objectMapperUtil.getObjectFromJsonNode(actionInfoJsonNode, new TypeReference<Map<String, StayTypeInfo>>() {
            });
        } catch (Exception e) {
            // Swallow the exception
        }

        // Initialize actionInfoMap in RoomInfoHelper
        roomInfoHelper.initializeActionInfoMap(actionInfoMap);
    }

    public SearchRoomsResponse convertSearchRoomsResponse(SearchRoomsRequest searchRoomsRequest, HotelDetailsResponse hotelDetailsResponse, Map<String, String> expDataMap, List<RoomStayCandidate> roomStayCandidates, SearchCriteria searchRoomsCriteria,
                                                          List<Filter> filterCriteria, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        // Check if we have valid hotel details
        if (hotelDetailsResponse == null || hotelDetailsResponse.getHotelDetails() == null) {
            return searchRoomsResponse;
        }

        HotelDetails hotelDetails = hotelDetailsResponse.getHotelDetails();


        long startTime = System.currentTimeMillis();
        try {
            String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
            String askedCurrency = StringUtils.isNotEmpty(searchRoomsCriteria.getCurrency()) ? searchRoomsCriteria.getCurrency() : "INR";

            // Check if we have rooms or room combos
            if (CollectionUtils.isEmpty(hotelDetails.getRooms()) && CollectionUtils.isEmpty(hotelDetails.getRoomCombos())) {
                return searchRoomsResponse;
            }

            // Map basic hotel information
            boolean isLuxeHotel = utility.isLuxeHotel(hotelDetails.getCategories());
            boolean isAltAccoHotel = hotelDetails.getHotelRateFlags().isAltAcco();
            boolean isBlackRevamp = utility.isExperimentTrue(expDataMap, GOTRIBE_REVAMP_POKUS_EXP_KEY);
            String checkIn = searchRoomsCriteria.getCheckIn();
            String checkOut = searchRoomsCriteria.getCheckOut();
            int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
            int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
            boolean isBlockPAH = expDataMap != null && expDataMap.containsKey("blockPAH") && StringUtils.isNotBlank(expDataMap.get("blockPAH")) && Boolean.parseBoolean(expDataMap.get("blockPAH"));
            searchRoomsResponse.setSpotlightApplicable(hotelDetails.getHotelRateFlags().isSpotlightApplicable());

            //searchRoomsResponse.setMsmeCorpCard(null); // TODO: Map msmeCorpCard from hotelDetails - B2B implementation

            searchRoomsResponse.setUserLoyaltyStatus(hotelDetailsResponse.getUserLoyaltyStatus());
            searchRoomsResponse.setMustReadRules(hotelDetails.getMustReadRules());
            searchRoomsResponse.setBlackInfo(extractAndBuildBlackInfo(hotelDetails.getDealBenefits()));
            if (isBlackRevamp) searchRoomsResponse.setGoTribeInfo(buildGoTribeInfo(extractAndBuildBlackInfo(hotelDetails.getDealBenefits()),  polyglotService.getTranslatedData(GOTRIBE_BENEFITS_TITLE_DETAIL), null));

            List<String> hydraSegment=null;
            if(commonModifierResponse != null && commonModifierResponse.getHydraResponse()!=null && commonModifierResponse.getHydraResponse().getHydraMatchedSegment()!=null)
                hydraSegment = new ArrayList<String>(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
            searchRoomsResponse.setHydraSegments(hydraSegment);

            // Transform rooms
            Map<String, String> ratePlanCodeAndNameMap = new HashMap<>();
            boolean packageRoomPresent = false;

            // Handle single rooms vs room combos
            if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
                // Filter out package rooms from regular rooms
                List<Rooms> regularRooms = hotelDetails.getRooms().stream()
                        .filter(room -> RoomType.EXACT.name().equalsIgnoreCase(room.getType()))
                        .collect(Collectors.toList());

                // Transform individual rooms to exact rooms
                List<RoomDetails> exactRooms = transformRoomsToRoomDetails(
                        regularRooms, hotelDetails, expDataMap,
                        askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                        commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel
                );
                searchRoomsResponse.setExactRooms(exactRooms);

                // TODO: Set extra guest detail persuasion
                // searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(...));
            } else if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
                // This block is now only for cases where we have ONLY room combos and NO individual rooms
                // Most of the logic has been moved outside the if/else-if blocks
            }

            // Set occupancy rooms (legacy: hotelRates.getOccupencyLessRoomTypeDetails -> OrchV2: hotelDetails.getRoomCombos with ComboType.OCCUPANCY_ROOM)
            // This should be set regardless of whether we have individual rooms or room combos, matching legacy behavior
            if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
                List<RoomCombo> occupancyRoomCombos = hotelDetails.getRoomCombos().stream()
                        .filter(combo -> combo.getComboType() == ComboType.OCCUPANCY_ROOM)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(occupancyRoomCombos)) {
                    // Occupancy rooms are essentially single room combos transformed to RoomDetails
                    List<RoomDetails> occupancyRooms = transformOccupancyRoomCombos(
                            occupancyRoomCombos, hotelDetails, expDataMap,
                            askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                            commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel
                    );
                    searchRoomsResponse.setOccupancyRooms(occupancyRooms);
                }
            }

            // Set recommended combos (legacy: hotelRates.getRecommendedRoomTypeDetails + hotelRates.getOtherRecommendedRooms)
            // This should be set regardless of whether we have individual rooms or room combos, matching legacy behavior
            if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
                List<RoomCombo> recommendedRoomCombos = hotelDetails.getRoomCombos().stream()
                        .filter(combo -> combo.getComboType() == ComboType.RECOMMENDED_ROOM || combo.getComboType() == ComboType.OTHER_RECOMMENDED_ROOM)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(recommendedRoomCombos)) {
                    List<RecommendedCombo> recommendedCombos = transformRoomCombos(
                            recommendedRoomCombos, hotelDetails.getMedia(), hotelDetails, expDataMap, askedCurrency,
                            requestDetails.getFunnelSource(), los, ap, isBlockPAH, commonModifierResponse,
                            ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel
                    );
                    searchRoomsResponse.setRecommendedCombos(recommendedCombos);
                }
            }

            // Handle recommended rooms logic (outside if/else-if block as per legacy code)
            // Legacy: hotelRates.getRecommendedRooms[] -> OrchV2: hotelDetails.getRoomCombos with ComboType.RECOMMENDED_ROOM
            if (commonModifierResponse != null) {
                // Handle occasion package room details first (legacy: hotelRates.getOccassionPackageRoomDetails)
                // In OrchV2: Single entry in hotelDetails.getRooms[] with type "OCCASION_PACKAGE"
                List<Rooms> occasionPackageRooms = hotelDetails.getRooms().stream()
                        .filter(room -> RoomType.OCCASION_PACKAGE.name().equalsIgnoreCase(room.getType()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(occasionPackageRooms)) {
                    List<RoomDetails> occasionPackageRoomDetails = transformRoomsToRoomDetails(
                            occasionPackageRooms, hotelDetails, expDataMap,
                            askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                            commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel
                    );
                    searchRoomsResponse.setRecommendedRooms(occasionPackageRoomDetails);
                    packageRoomPresent = true;
                } else {
                    // Handle package room details (legacy: hotelRates.getPackageRoomDetails)
                    // In OrchV2: Single entry in hotelDetails.getRooms[] with type "PACKAGE"
                    List<Rooms> packageRooms = hotelDetails.getRooms().stream()
                            .filter(room -> RoomType.SUPER_PACKAGE.name().equalsIgnoreCase(room.getType()))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(packageRooms)) {
                        List<RoomDetails> packageRoomDetails = transformRoomsToRoomDetails(
                                packageRooms, hotelDetails, expDataMap,
                                askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                                commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel
                        );
                        searchRoomsResponse.setRecommendedRooms(packageRoomDetails);
                        packageRoomPresent = true;
                    }
                }
            }

            // Handle package rooms separately
            // Legacy: hotelRates.getPackageRoomDetails -> OrchV2: Single entry in hotelDetails.getRooms[] with type "PACKAGE"
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
                List<Rooms> packageRooms = hotelDetails.getRooms().stream()
                        .filter(room -> RoomType.SUPER_PACKAGE.name().equalsIgnoreCase(room.getType()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(packageRooms)) {
                    List<RoomDetails> packageRoomDetails = transformRoomsToRoomDetails(
                            packageRooms, hotelDetails, expDataMap,
                            askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                            commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel
                    );
                    searchRoomsResponse.setPackageRooms(packageRoomDetails);
                    // TODO: Update package inclusion base rate plan name when method is available
                    // updatePackageInclusionBaseRatePlanName(ratePlanCodeAndNameMap, searchRoomsResponse.getPackageRooms());
                }
            }

            // Set property layout title text
            if (Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelDetails.getListingType())) {
                Optional<RoomDetails> optionalRoomDetails = Optional.ofNullable(searchRoomsResponse.getExactRooms())
                        .flatMap(rooms -> rooms.stream().findFirst());
                if (optionalRoomDetails.isPresent()) {
                    RoomDetails roomDetails = optionalRoomDetails.get();
                    roomDetails.getRatePlans().stream().findFirst().ifPresent(ratePlan -> {
                        if (ratePlan.getTariffs() != null && ratePlan.getTariffs().get(0) != null && ratePlan.getTariffs().get(0).getOccupancydetails() != null) {
                            int roomCount = ratePlan.getTariffs().get(0).getOccupancydetails().getRoomCount();
                            if (roomCount > 1) {
                                searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(GI_MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT), roomCount, hotelDetails.getPropertyType(), hotelDetails.getPropertyType()));
                            } else {
                                searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(ENTIRE_PROPERTY_LAYOUT_TEXT), hotelDetails.getPropertyType()));
                            }
                        }
                    });
                } else {
                    searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(ENTIRE_PROPERTY_LAYOUT_TEXT), hotelDetails.getPropertyType()));
                }
            } else {
                searchRoomsResponse.setPropertyLayoutTitleText(polyglotService.getTranslatedData(ROOM_BY_ROOM_PROPERTY_LAYOUT_TEXT));
            }

            // Set additional fields
            searchRoomsResponse.setAddons(null); // TODO: Transform AddOns - hotelDetails doesn't have addons field

            // Initialize missing variables for filters
            BNPLVariant bnplVariant = hotelDetails.getBnplVariant();
            searchRoomsResponse.setGroupFilters(getFilters(searchRoomsResponse.getExactRooms(), searchRoomsResponse.getOccupancyRooms(),
                    filterCriteria, ap, isBlockPAH, bnplVariant, commonModifierResponse, isLuxeHotel, groupRatePlanFilterConfMap, hotelDetails.getPropertyType()));

            //Desciption of the card filter
            List<GroupRatePlanFilter> cardFilters = getCardFilters(searchRoomsResponse.getExactRooms());
            CardFilter cardFilter = new CardFilter();
            cardFilter.setIsEligibleForCardFilter(!cardFilters.isEmpty());
            if (utility.isExperimentTrue(expDataMap, roomUpsell.getKey())) {
                cardFilter.setCardFilters(cardFilters);
            }
            searchRoomsResponse.setCardFilter(cardFilter);
            String detailDeepLink = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest);
//            searchRoomsResponse.setDetailDeeplinkUrl(detailDeepLink);
            searchRoomsResponse.setSearchRoomDeeplinkUrl(deepLinkHelper.buildSearchRoomsDeepLinkUrl(hotelDetails, searchRoomsRequest));
            searchRoomsResponse.setRecentDeepLink(deepLinkHelper.buildRecentDeepLink(hotelDetails, searchRoomsRequest));
            searchRoomsResponse.setHotelDetails(buildHotelDetails(hotelDetails, requestDetails.getFunnelSource(), detailDeepLink, client, expDataMap, hotelDetails.getLocation(), packageRoomPresent));

            // Set selected rate plan code
            if (hotelDetails.getAdditionalDetails() != null) {
                searchRoomsResponse.setSelectedRatePlanCode(hotelDetails.getAdditionalDetails().getSelectedRatePlanCode());
            }

            searchRoomsResponse.setCardData(setDetailPageCards(hotelDetails, requestDetails, searchRoomsResponse, expDataMap, commonModifierResponse));

            // Map spaceIdToSleepingInfoArrMap data to rooms using HES logic
            mappingSpaceIdToEachRoomCode(searchRoomsResponse, hotelDetails, searchRoomsRequest);

            // Handle space data transformation - Shared Spaces at Response Level
            // Condition: Check if space details are required and available
            if (CollectionUtils.isNotEmpty(hotelDetails.getRooms()) &&
                    hotelDetails.getRooms().get(0).getRoomInfo() != null &&
                    CollectionUtils.isNotEmpty(hotelDetails.getRooms().get(0).getRoomInfo().getSpaces())) {

                // Get shared spaces from the first room's room info (following legacy pattern)
                com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData sharedSpaceData =
                        hotelDetails.getRooms().get(0).getRoomInfo().getSpaces().stream()
                                .filter(space -> space.getType() == com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData.Type.SHARED)
                                .findFirst()
                                .orElse(null);

                if (sharedSpaceData != null) {
                    searchRoomsResponse.setSharedSpaces(roomInfoHelper.getSpaceData(sharedSpaceData, commonModifierResponse));
                }
            }

            if (!requestDetails.isLoggedIn()) {
                searchRoomsResponse.setLoginPersuasion(buildLoginPersuasion());
            }

            // Build banner for non-desktop and non-corp contexts
//            if (!DEVICE_OS_DESKTOP.equalsIgnoreCase(client) && !CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext())) {
//                searchRoomsResponse.setBanner(searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails));
//            } // not used by client

            // TODO: Set specialFareInfo for negotiated rates
            // if (hotelDetails.isNegotiatedRateFlag()) {
            //     buildInstantFareInfo(corpAlias, searchRoomsResponse, currency, isMyBizNewDetailsPage);
            // }

            // TODO: Set addOnErrorMessage if present
            // searchRoomsResponse.setAddOnErrorMessage(hotelDetails.getAddOnErrorMessage());

            // TODO: Set RTB persuasion card
            // searchRoomsResponse.setRtbPersuasionCard(buildRtbPersuasionCard(hotelDetails));

            // TODO: Set payment card
            // searchRoomsResponse.setPaymentCard(buildPaymentCard(requestDetails, hotelDetails));

            // TODO: Set primary offer based on various deal types
            // - Check vistaraDealAvailable
            // - Check exclusiveFlyerRateAvailable
            // - Check busExclusiveRateAvailable
            // - Check trainExclusiveRateAvailable
            // - Check campaignPojo for sale campaigns
            // - Check supplier deals
            // - Check no cost EMI
//            if (hotelDetails.getPrimaryOffer() != null) {
//                com.mmt.hotels.clientgateway.response.staticdetail.PrimaryOffer primaryOffer = getPrimaryOfferForSaleCampaign(hotelDetails.getPrimaryOffer());
//                searchRoomsResponse.setPrimaryOffer(primaryOffer);
//            }

            // TODO: Set extra bed policy from house rules
            // searchRoomsResponse.setExtraBedPolicy(buildExtraBedPolicy(houseRules));

            // TODO: Handle alternate dates persuasion
            // commonResponseTransformer.buildAltDatesPersuasionAndBottomsheet(...)

            // TODO: Set luckyUserContext
            // searchRoomsResponse.setLuckyUserContext(luckyUserContext);

            // TODO: Set hotel cloud data
            // searchRoomsResponse.setHotelCloudData(hotelCloudData);

            // TODO: Set support details for high value calls
            // searchRoomsResponse.setSupportDetails(supportDetails);
            // searchRoomsResponse.setRequestCallbackData(requestCallbackData);

            // TODO: Set guest house availability and response for corp users
            // searchRoomsResponse.setGuestHouseAvailable(guestHouseAvailable);
            // searchRoomsResponse.setAvailableGuestHouse(guestHouseResponse);
            // Build feature flags
            searchRoomsResponse.setFeatureFlags(getFeatureFlags(hotelDetails, searchRoomsResponse.getExactRooms(), (commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null), commonModifierResponse));

            searchRoomsResponse.setContextDetails(getContextDetails(hotelDetails));
            // TODO: Implement impInfo (important info) for combo rooms
            // searchRoomsResponse.setImpInfo(getImpInfo(recommendedCombos, roomStayCandidates));

            // Build property sellable type
            searchRoomsResponse.setPropertySellableType(buildPropertySellableType(hotelDetails));

            // Set selected rate plan code
            if (hotelDetails.getAdditionalDetails() != null) {
                searchRoomsResponse.setSelectedRatePlanCode(hotelDetails.getAdditionalDetails().getSelectedRatePlanCode());
            }

            Pair<Boolean, Boolean> bedAndRoomPresent = new ImmutablePair<>(false,false);

            boolean isNewDetailPageTrue = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE);
            boolean isOHSExpEnable = Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType()); //Turning it on All Hostels.
            if(isOHSExpEnable){
                bedAndRoomPresent = addSellableLabelFromSellableType(searchRoomsResponse);
            }

            // Build room info
            SleepingArrangementRoomInfo roomInfo = roomInfoHelper.buildRoomInfo(hotelDetails, searchRoomsResponse, roomStayCandidates,
                    searchRoomsCriteria.getCountryCode(), isOHSExpEnable, bedAndRoomPresent, isNewDetailPageTrue, expDataMap);
            searchRoomsResponse.setRoomInfo(roomInfo);

            if (!requestDetails.isLoggedIn()){
                searchRoomsResponse.setLoginPersuasion(buildLoginPersuasion());
            }

            if (commonModifierResponse!=null)
            {
                sortBySellableType(searchRoomsResponse,hotelDetails.getPropertyType(),commonModifierResponse.getExpDataMap());
                addSellableLabelFromSellableType(searchRoomsResponse);
            }
            searchRoomsResponse.setHotelPersuasions(hotelDetails.getPersuasions());

            // Set room advisory from rooms' RoomInfo
            RoomAdvisory roomAdvisory = buildRoomAdvisoryFromRooms(hotelDetails.getRooms());
            if (roomAdvisory != null) {
                searchRoomsResponse.setRoomAdvisory(roomAdvisory);
            }

//            /*Setting Lucky Data*/      // todo
//            LuckyData luckyData = new LuckyData();
//            if(hotelDetails.getLuckyUserContext() != null ){
//                LuckyUserDetails luckyUserDetails = new LuckyUserDetails();
//                luckyUserDetails.setLuckyUserContext(hotelDetails.getLuckyUserContext());
//                luckyData.setLuckyUserDetails(luckyUserDetails);
//            }
//            searchRoomsResponse.setLuckyData(luckyData);
//            /*Setting Lucky Data V2*/
//            LuckyData luckyDataV2 = new LuckyData();
//            if (hotelDetails.getLuckyCouponTimings() != null){
//                luckyDataV2.setLuckyPersuasion(polyglotService.getTranslatedData(GI_LUCKY_TIMER_TEXT));
//                LuckyUserDetails luckyUserDetails = new LuckyUserDetails();
//                luckyUserDetails.setCurrent(luckyCouponTimings.getStartTime());
//                luckyUserDetails.setEnd(luckyCouponTimings.getEndTime());
//                luckyDataV2.setLuckyUserDetails(luckyUserDetails);
//            }
//            searchRoomsResponse.setLuckyDataV2(luckyDataV2);
            if(commonModifierResponse != null) {
                searchRoomsResponse.setExpData(commonModifierResponse.getExpDataMap());
                searchRoomsResponse.setVariantKey(commonModifierResponse.getVariantKey());
            }
            removeDuplicateData(searchRoomsResponse);
    } finally {
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
        }

        return searchRoomsResponse;
    }

    private List<OfferDetail> transformOffers(List<com.gommt.hotels.orchestrator.detail.model.response.common.RangePrice> offers) {
        if (CollectionUtils.isEmpty(offers)) {
            return null;
        }

        List<OfferDetail> offerDetails = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.common.RangePrice offer : offers) {
            OfferDetail offerDetail = new OfferDetail();
            offerDetail.setLongText(offer.getLongText());
            offerDetail.setOfferType(offer.getOfferType());
            offerDetail.setPriority(offer.getPriority());
            offerDetail.setShortText(offer.getShortText());
            offerDetail.setTncLink(offer.getTncLink());
            offerDetail.setIconUrl(offer.getIconUrl());
            offerDetails.add(offerDetail);
        }
        return offerDetails;
    }

    private List<CardData> setDetailPageCards(HotelDetails hotelDetails, RequestDetails requestDetails,
                                              SearchRoomsResponse searchRoomsResponse, Map<String, String> expData, CommonModifierResponse commonModifierResponse) {
        if (hotelDetails == null || requestDetails == null) {
            return null;
        }
        List<CardData> detailPageCards = new ArrayList<>();
        
        // Build business identification card using orchestrator data
//        CardData businessIdentificationCard = buildBusinessIdentificationCard(hotelDetails, requestDetails);
//        if (businessIdentificationCard != null) {
//            detailPageCards.add(businessIdentificationCard);
//        }
        
        // Build offer cards using orchestrator data
        if (utility.isExperimentTrue(expData, LOS_BENEFITS_EXP_KEY) || utility.isExperimentTrue(expData, TAJ_GIFT_CARD_EXP_KEY)) {
            List<CardData> offerCards = buildOfferCards(hotelDetails, searchRoomsResponse, commonModifierResponse);
            if (CollectionUtils.isNotEmpty(offerCards)) {
                detailPageCards.addAll(offerCards);
            }
        }
        
        return detailPageCards.isEmpty() ? new ArrayList<>() : detailPageCards;
    }

    /**
     * Build offer cards using orchestrator HotelDetails
     */
    private List<CardData> buildOfferCards(HotelDetails hotelDetails, SearchRoomsResponse searchRoomsResponse, CommonModifierResponse commonModifierResponse) {
        DetailPageOfferCardsConfig detailPageOfferCardsConfig = mobConfigHelper.getDetailPageOfferCardsConfig();
        List<CardData> detailPageOfferCards = new ArrayList<>();
        
        if (detailPageOfferCardsConfig == null || CollectionUtils.isEmpty(detailPageOfferCardsConfig.getCardDetails())) {
            return null;
        }
        
        for (CardData cardData : detailPageOfferCardsConfig.getCardDetails()) {
            if (cardData == null || cardData.getCardInfo() == null || StringUtils.isBlank(cardData.getCardInfo().getId())) {
                continue;
            }
            
            try {
                OfferCard offerCard;
                try {
                    offerCard = OfferCard.valueOf(cardData.getCardInfo().getId());
                } catch (IllegalArgumentException e) {
                    LOGGER.error("Error while fetching offer card id: {}", cardData.getCardInfo().getId());
                    continue;
                }
                
                switch (offerCard) {
                    case SALE_CAMPAIGN:
                        buildSaleCampaignCardFromOrchestrator(hotelDetails, cardData, detailPageOfferCards);
                        break;
                    case ELITE_PACKAGE:
                        buildElitePackageCardFromOrchestrator(searchRoomsResponse, cardData, detailPageOfferCards);
                        break;
                    case TAJ_GIFT_CARD:
                        buildTajGiftCardFromOrchestrator(searchRoomsResponse, hotelDetails, cardData, detailPageOfferCards);
                        break;
                    case NEW_USER_DISCOUNT_CARD:
                        buildNewUserDiscountCard(cardData, detailPageOfferCards, commonModifierResponse);
                        break;
                    default:
                        // For other offer types, we might need additional mapping logic
                        break;
                }
            } catch (Exception e) {
                LOGGER.error("Error while building offer card: {}", cardData.getCardInfo().getId(), e);
            }
        }
        
        return detailPageOfferCards;
    }

    /**
     * Get the lowest rate segment ID from HotelDetails
     */
    private String getLowestRateSegmentId(HotelDetails hotelDetails) {
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms room : hotelDetails.getRooms()) {
                if (CollectionUtils.isNotEmpty(room.getRatePlans())) {
                    com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan ratePlan = room.getRatePlans().get(0);
                    if (StringUtils.isNotEmpty(ratePlan.getSegmentId())) {
                        return ratePlan.getSegmentId();
                    }
                }
            }
        }
        return null;
    }

    /**
     * Get all available segments from all rooms and rate plans
     */
    private Set<String> getAllAvailableSegments(HotelDetails hotelDetails) {
        Set<String> segments = new HashSet<>();
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms room : hotelDetails.getRooms()) {
                if (CollectionUtils.isNotEmpty(room.getRatePlans())) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan ratePlan : room.getRatePlans()) {
                        if (StringUtils.isNotEmpty(ratePlan.getSegmentId())) {
                            segments.add(ratePlan.getSegmentId());
                        }
                    }
                }
            }
        }
        return segments;
    }

    /**
     * Calculate saving percentage from HotelDetails pricing information
     */
    private int calculateSavingPercentage(HotelDetails hotelDetails) {
        try {
            if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
                com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms firstRoom = hotelDetails.getRooms().get(0);
                if (CollectionUtils.isNotEmpty(firstRoom.getRatePlans())) {
                    com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan ratePlan = firstRoom.getRatePlans().get(0);
                    if (ratePlan.getPrice() != null && ratePlan.getPrice().getDiscount() != null) {
                        // Calculate percentage from total discount and display price
                        double totalDiscount = ratePlan.getPrice().getTotalDiscount();
                        double displayPrice = ratePlan.getPrice().getDisplayPrice();
                        if (displayPrice > 0) {
                            return (int) ((totalDiscount / displayPrice) * 100);
                        }
                    }
                }
                // Fallback to room level pricing if available
                if (firstRoom.getPrice() != null && firstRoom.getPrice().getDiscount() != null) {
                    double totalDiscount = firstRoom.getPrice().getTotalDiscount();
                    double displayPrice = firstRoom.getPrice().getDisplayPrice();
                    if (displayPrice > 0) {
                        return (int) ((totalDiscount / displayPrice) * 100);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error calculating saving percentage: {}", e.getMessage());
        }
        return 0;
    }

    /**
     * Build sale campaign card from orchestrator PrimaryOffer
     */
    private void buildSaleCampaignCardFromOrchestrator(HotelDetails hotelDetails, CardData cardData, List<CardData> detailPageOfferCards) {
        if (utility.isB2CFunnel() && hotelDetails.getPrimaryOffer() != null
                && StringUtils.isNotBlank(hotelDetails.getPrimaryOffer().getDesc())) {
            CardInfo cardInfo = cardData.getCardInfo();
            cardInfo.setTitleText(hotelDetails.getPrimaryOffer().getDesc());
            cardInfo.setTitleTextColor(hotelDetails.getPrimaryOffer().getHeadingColor());
            cardInfo.setSubText(hotelDetails.getPrimaryOffer().getDesc());
            cardInfo.setLargeIconURL(hotelDetails.getPrimaryOffer().getIconUrl());
            // Note: backgroundImage might need additional mapping if available in orchestrator
            detailPageOfferCards.add(cardData);
        }
    }

    /**
     * Build elite package card - reuse existing logic with search response
     */
    private void buildElitePackageCardFromOrchestrator(SearchRoomsResponse searchRoomsResponse, CardData cardData, List<CardData> detailPageOfferCards) {
        // This logic remains the same as it uses SearchRoomsResponse which is available
        if (searchRoomsResponse != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms())
                && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans())) {
            SelectRoomRatePlan packageRatePlan = searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0);
            if (CollectionUtils.isEmpty(packageRatePlan.getInclusionsList())) {
                return;
            }
            List<BookedInclusion> packageBenefitInclusions = packageRatePlan.getInclusionsList().stream()
                    .filter(BookedInclusion::isPackageBenefit)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageBenefitInclusions)) {
                return;
            }
            CardInfo cardInfo = cardData.getCardInfo();
            cardInfo.setSubTextList(packageBenefitInclusions.stream()
                    .map(BookedInclusion::getText)
                    .limit(cardInfo.getSubTextList().size())
                    .collect(Collectors.toList()));
            detailPageOfferCards.add(cardData);
        }
    }

    /**
     * Build Taj gift card - adapted for orchestrator
     */
    private void buildTajGiftCardFromOrchestrator(SearchRoomsResponse searchRoomsResponse, HotelDetails hotelDetails, CardData cardData, List<CardData> detailPageOfferCards) {
        List<Tariff> tariffs = null;
        if (searchRoomsResponse != null) {
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) &&
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans()) && 
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs())) {
                tariffs = searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs();
            } else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) &&
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) && 
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRatePlans()) &&
                    CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRatePlans().get(0).getTariffs())) {
                tariffs = searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRatePlans().get(0).getTariffs();
            }
        }
        if (CollectionUtils.isNotEmpty(tariffs) && Constants.HOTEL_TAJ_GIFT_NAME.equalsIgnoreCase(tariffs.get(0).getVoucherType())) {
            CardInfo cardInfo = cardData.getCardInfo();
            cardInfo.setSubText(tariffs.get(0).getVoucherCode());
            // Use offers from HotelDetails if available for icon URL
            if (CollectionUtils.isNotEmpty(hotelDetails.getOffers())) {
                cardInfo.setIconURL(hotelDetails.getOffers().get(0).getIconUrl());
            }
            detailPageOfferCards.add(cardData);
        }
    }

    /**
     * Add business identification card - reuse existing utility method
     */
    private CardData addBusinessIdentificationCard(int savingPerc, boolean isReviewPageApi) {
        CardData cardData = new CardData();
        CardInfo cardInfo = new CardInfo();
        cardInfo.setTemplateId(CardTemplateId.BUSINESSIDENTIFICATION_CARD.name());
        cardInfo.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.BUSINESSIDENTIFICATION_CARD_TITLE_TEXT));
        cardInfo.setTitleTextColor(BUSINESSIDENTIFICATION_CARD_TITLE_TEXT_COLOR);
        if (isReviewPageApi) {
            cardInfo.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BUSINESSIDENTIFICATION_CARD_SUB_TEXT_REVIEW_PAGE));
        } else {
            cardInfo.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BUSINESSIDENTIFICATION_CARD_SUB_TEXT).replace(SAVING_PERC, String.valueOf(savingPerc)));
        }
        cardInfo.setIconURL(BUSINESSIDENTIFICATION_CARD_ICON_URL);
        cardInfo.setBgGradientStart(BUSINESSIDENTIFICATION_BG_GRADIENT_START);
        cardInfo.setBgGradientEnd(BUSINESSIDENTIFICATION_BG_GRADIENT_END);
        cardData.setCardInfo(cardInfo);
        return cardData;
    }

    /**
     * Build new user discount card - reuse existing logic
     */
    private void buildNewUserDiscountCard(CardData cardData, List<CardData> detailPageOfferCards, CommonModifierResponse commonModifierResponse) {
        if (TRUE.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, ExperimentKeys.NEW_USER_COUPON_CALLOUT.getKey()))) {
            detailPageOfferCards.add(cardData);
        }
    }

    private BlackInfo extractAndBuildBlackInfo(List<com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits> dealBenefits) {
        if (CollectionUtils.isEmpty(dealBenefits)) {
            return null;
        }
        // Find the DealBenefits with BLACK_BENEFITS type
        com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits blackDeal = dealBenefits.stream()
                .filter(deal -> deal != null && BenefitType.BLACK_BENEFITS.equals(deal.getBenefitType()))
                .findFirst()
                .orElse(null);

        if (blackDeal == null || blackDeal.getLoyaltyDetails() == null) {
            return null;
        }

        // Directly create CG BlackInfo from orchestrator v2 loyalty details
        BlackInfo blackInfo = new BlackInfo();
        com.gommt.hotels.orchestrator.detail.model.request.prime.BlackInfo loyaltyDetails = blackDeal.getLoyaltyDetails();

        // Map basic fields from loyalty details
        blackInfo.setTierName(loyaltyDetails.getTierName());
        blackInfo.setTierNumber(loyaltyDetails.getTierNumber());
        blackInfo.setIconUrl(loyaltyDetails.getIconUrl());
        blackInfo.setMsg(blackDeal.getSubTitle()); // Using subtitle from deal benefits
        blackInfo.setBorderColor(loyaltyDetails.getBorderColour());
        blackInfo.setCtaUrl(loyaltyDetails.getCtaLink());
        blackInfo.setCtaText(loyaltyDetails.getCta());
        blackInfo.setCurrencyIcon(loyaltyDetails.getCurrencyIcon());
        blackInfo.setTitle(blackDeal.getTitle()); // Using title from deal benefits
        blackInfo.setCardId(blackDeal.getCardId());
        blackInfo.setBgImageUrl(loyaltyDetails.getBgImageUrl());
        blackInfo.setCampaignEndTime(loyaltyDetails.getCampaignEndTime());
        blackInfo.setMmtSelectPrivilige(loyaltyDetails.isMmtSelectPrivilige());
        blackInfo.setTitleImageUrl(loyaltyDetails.getTitleImageUrl());

        // Map inclusions if available
        if (CollectionUtils.isNotEmpty(blackDeal.getInclusionsList())) {
            List<Inclusion> inclusionsList = blackDeal.getInclusionsList().stream()
                    .map(this::transformDealInclusionToBlackInclusion)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            blackInfo.setInclusionsList(inclusionsList);
        }

        // Map border gradient if available
        if (blackDeal.getBorderGradient() != null) {
            BorderGradient bg = new BorderGradient();
            bg.setAngle(blackDeal.getBorderGradient().getAngle());
            bg.setEnd(blackDeal.getBorderGradient().getEnd());
            bg.setStart(blackDeal.getBorderGradient().getStart());
            // Direction might not be available in CG BorderGradient
            blackInfo.setBorderGradient(bg);
        }

        return blackInfo;
    }

    private Inclusion transformDealInclusionToBlackInclusion(com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion orchInclusion) {
        if (orchInclusion == null) {
            return null;
        }

        Inclusion inclusion = new Inclusion();
        inclusion.setValue(orchInclusion.getValue());
        inclusion.setCode(orchInclusion.getValue()); // Using value as code
        inclusion.setInclusionType(orchInclusion.getInclusionType());
        inclusion.setId(orchInclusion.getId());
        inclusion.setImageURL(orchInclusion.getIconUrl());

        return inclusion;
    }

    public GoTribeInfo buildGoTribeInfo(BlackInfo bInfo, String title, String subTitle) {
        if (bInfo != null && CollectionUtils.isNotEmpty(bInfo.getInclusionsList())) {
            GoTribeInfo goTribeInfo = new GoTribeInfo();
            goTribeInfo.setTierName(bInfo.getTierName());
            goTribeInfo.setTierNumber(bInfo.getTierNumber());
            goTribeInfo.setGoTribeIconUrlV2(bInfo.getIconUrl());
            goTribeInfo.setBenefitsTitle(bInfo.getBenefitsTitle());
            goTribeInfo.setLineBgColour(bInfo.getLineBgColor());
            goTribeInfo.setTitle(title);
            goTribeInfo.setSubTitle(subTitle);
            goTribeInfo.setBenefits(bInfo.getInclusionsList());
            if (CollectionUtils.isNotEmpty(goTribeInfo.getBenefits()))
                goTribeInfo.setBenefits(utility.reorderBlackBenefits(goTribeInfo.getBenefits()));
            return goTribeInfo;
        }
        return null;
    }

    private String buildPropertySellableType(HotelDetails hotelDetails) {
        String propertySellableType = "Stay";
        if (isPropertyHotelOrResort(hotelDetails)) {
            propertySellableType = "Room";
        }
        return propertySellableType;
    }

    public static boolean isPropertyHotelOrResort(HotelDetails hotelDetails){
        if (hotelDetails == null) {
            return false;
        }
        return "hotel".equalsIgnoreCase(hotelDetails.getPropertyType()) || "resort".equalsIgnoreCase(hotelDetails.getPropertyType());
    }

    private List<String> amenitiesFilter(AmenityGroup amenityGroup) {
        RoomUpsellConfig roomUpsellConfig = mobConfigHelper.getRoomUpsellConfig();
        List<String> amenitiesFilter = new ArrayList<>();
        if (amenityGroup != null && roomUpsellConfig != null && roomUpsellConfig.roomViewsFilterToggle != null) {
            for (Amenity amenity : amenityGroup.getAmenities()) {
                if (amenity.getAttributeName() != null) {
                    if (roomUpsellConfig.roomViewsFilterToggle.stream().anyMatch(roomViewFilterConfig ->
                            roomViewFilterConfig.getFilterCode().equalsIgnoreCase(Utility.convertToUnderscoreCaps(amenity.getAttributeName())))) {
                        amenitiesFilter.add(Utility.convertToUnderscoreCaps(amenity.getAttributeName()));
                    }
                }
            }
        }
        return amenitiesFilter;
    }

    private List<RoomDetails> transformRoomsToRoomDetails(List<Rooms> rooms,
                                                          HotelDetails hotelDetails, Map<String, String> expData,
                                                          String askedCurrency, String funnelSource, int los, int ap,
                                                          boolean isBlockPAH, CommonModifierResponse commonModifierResponse,
                                                          Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel,
                                                          boolean isAltAccoHotel) {
        List<RoomDetails> roomDetailsList = new ArrayList<>();
        String linkedRateExperimentValue = getExperimentValue(commonModifierResponse, Constants.LINKED_RATE_EXPERIMENT_NR);
//        boolean amendRoomHighlights = utility.showHighlightsForRoomAmenities(countryCode, funnelSource);
        if (CollectionUtils.isEmpty(rooms)) {
            return roomDetailsList;
        }

        for (Rooms room : rooms) {
            String sellableType = SELLABLE_ROOM_TYPE;
            // Determine room type flags based on OrchV2 data
            boolean isPackageRoom = isSuperPackageRoom(room) || isRecommendedRoom(room) || isOccasionRoom(room);
            RoomInfo roomInfo = room.getRoomInfo();
            String roomViewType = null;
            if (roomInfo != null &&  null != roomInfo.getRoomViewName())
                roomViewType = roomInfo.getRoomViewName();
            List<String> amenitiesFilterCodes = new ArrayList<>();
            if (roomInfo != null &&  null != roomInfo.getHighlightedAmenities() && !roomInfo.getHighlightedAmenities().isEmpty()) {
                amenitiesFilterCodes = amenitiesFilter(roomInfo.getHighlightedAmenities().get(0));
            }
            // Create appropriate RoomDetails type based on flags (following legacy pattern)
            RoomDetails roomDetails = new RoomDetails();
            if (isPackageRoom) {
                roomDetails = new PackageRoomDetails();
                if (isOccasionRoom(room)) {
                    ((PackageRoomDetails) roomDetails).setType(room.getType());
                }
            }
            // TODO: Add meal plan logic similar to legacy
            if (roomInfo != null) sellableType = StringUtils.isNotEmpty(roomInfo.getSellableType()) ? roomInfo.getSellableType() : sellableType;
            // Transform rate plans
            if (CollectionUtils.isNotEmpty(room.getRatePlans())) {
                List<SelectRoomRatePlan> ratePlans = transformRatePlans(room.getRatePlans(), sellableType, expData, room, hotelDetails,
                        askedCurrency, funnelSource, los, ap, isBlockPAH, commonModifierResponse,
                        ratePlanCodeAndNameMap, isLuxeHotel, roomViewType, amenitiesFilterCodes);
                if(LINKED_RATE_EXP_ONE_VARIANT.equalsIgnoreCase(linkedRateExperimentValue)){
                    linkRatePlans(ratePlans);
                }
                roomDetails.setRatePlans(ratePlans);
            }

            roomDetails.setRoomCode(room.getCode());
            roomDetails.setRoomName(room.getName());
            roomDetails.setDescription(room.getDesc());
            roomDetails.setRoomCategoryText(LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelDetails.getListingType())
                    ? (hotelDetails.getPropertyType() != null ? hotelDetails.getPropertyType() : SELLABLE_ROOM_TYPE)
                    : (roomInfo != null && roomInfo.getSellableType() != null ? roomInfo.getSellableType() : SELLABLE_ROOM_TYPE));

            // Set room images from OrchV2 Media (equivalent to legacy HotelImage logic)
            List<String> roomImages = searchRoomsMediaHelper.extractRoomImagesFromMedia(hotelDetails.getMedia(), room.getCode());
            roomDetails.setImages(roomImages);

            // Set 360 images from OrchV2 Media (equivalent to legacy HotelImage.getImages360 logic)
//            roomDetails.setView360(searchRoomsMediaHelper.extract360ImagesFromMedia(hotelDetails.getMedia(), room.getCode()));

            // Set media data from OrchV2 Media and room images (simplified - no video support in OrchV2)
            roomDetails.setMedia(searchRoomsMediaHelper.populateMedia(null, roomImages, room.getCode()));


            // Set missing fields for recommended rooms based on HES Detail logic
            setRecommendedRoomFields(roomDetails, room, expData);

            if (roomInfo != null) {
                // Set room highlights - adapted for OrchV2 (some parameters not available)
                roomDetails.setRoomHighlights(roomInfoHelper.transformRoomHighlights(roomInfo, null, isAltAccoHotel, utility.isExperimentTrue(expData, ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey())));
                roomDetails.setRoomSize(roomInfo.getRoomSize() != null ? roomInfo.getRoomSize() : "");
                roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
                roomDetails.setMaxGuestCount(roomInfo.getMaxGuestCount());
                roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
                roomDetails.setMaxAdultCount(roomInfo.getMaxAdultCount());
                roomDetails.setMaxChild(roomInfo.getMaxChildCount());
                roomDetails.setMaxChildCount(roomInfo.getMaxChildCount());
                roomDetails.setExtraBedCount(roomInfo.getExtraBedCount());
                roomDetails.setBathroomCount(roomInfo.getRoomInfoExtension() != null ? roomInfo.getRoomInfoExtension().getBathroomCount() : 0);
                roomDetails.setBedCount(roomInfo.getBedCount());
                roomDetails.setBedroomCount(roomInfo.getBedRoomCount());
                roomDetails.setMaster(roomInfo.getRoomFlags().isMasterRoom());
                roomDetails.setParentRoomCode("");
                roomDetails.setBaseRoom(room.isBaseRoom());
                roomDetails.setBeds(RoomInfoHelper.getSleepingArrangements(roomInfo));
                roomDetails.setRoomViewName(roomInfo.getRoomViewName());
//                roomDetails.setBedInfoText(buildBedInfoText(roomInfo)); // not used by client
//                roomDetails.setUsp(roomInfo.getUsp()); ////confirm from client whether we need this for CG, not needed in MMT

                // Handle private space data transformation - Room Level
                // Condition: Check if space details are required
                if (CollectionUtils.isNotEmpty(roomInfo.getSpaces())) {

                    // Get private spaces from room info (following legacy pattern)
                    com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData privateSpaceData =
                            roomInfo.getSpaces().stream()
                                    .filter(space -> space.getType() == com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData.Type.PRIVATE)
                                    .findFirst()
                                    .orElse(null);

                    if (privateSpaceData != null) {

                            roomDetails.setPrivateSpaces(roomInfoHelper.getSpaceData(privateSpaceData, commonModifierResponse));
                    }
                }
                roomDetails.setAmenities(roomAmentiesHelper.buildAmenities(roomInfo));
                roomDetails.setHighlightedAmenities(roomAmentiesHelper.buildFacilityHighlights(roomInfo.getHighlightedAmenities()));
                roomDetails.setBaseGuest(roomDetails.getPrivateSpaces() != null ? roomDetails.getPrivateSpaces().getBaseGuests() : 0);
                roomDetails.setRoomSummary(roomInfoHelper.buildRoomSummary(roomInfo.getRoomInfoExtension()));
                buildStayDetails(roomDetails);
            }

            roomDetails.setClientViewType(getTariffViewType(roomDetails, roomDetailsList.isEmpty()));
            roomDetails.setRoomPersuasions(room.getPersuasions());
            if (utility.isExperimentOn(expData, SELECT_ROOM_REVAMP_EXP_KEY)) {
                selectRoomV2ResponseTransformer.alterRoomDetailsForSelectRoomRevamp(roomDetails, hotelDetails.getPropertyType());
            }
            // Set package room specific information (following legacy pattern)
            if (isPackageRoom) {
                setPackageRoomSpecificInfo((PackageRoomDetails) roomDetails, room, askedCurrency, room.getSubType());
            }

            roomDetailsList.add(roomDetails);
        }

        // Conditions for enabling upsellDetails are mentioned below:
        // 1. Should be Alt Acco property
        // 2. Experiment must be on.
        // 3. For Regular Pax :: 1 room, 3 rateplans only. More than 3 then we should show select room page.
        // 4. Multi Pax :: Only for 1 room type, 1 rateplan. Then only skip select room should be enaled.
        // 5. All null pointers
        boolean isUpSellExperimentEnalbed = MapUtils.isNotEmpty(expData) && expData.get(ALT_ACCO_SELECT_ROOM_EXP) != null && StringUtils.equalsIgnoreCase(expData.get(ALT_ACCO_SELECT_ROOM_EXP),"true");
        boolean doesPropertyContainOnlyOneRoomType = roomDetailsList.size() == 1;
        boolean doesNumOfRatePlansExceedProvidedThreshold = CollectionUtils.isNotEmpty(roomDetailsList) && 
                CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans()) && 
                roomDetailsList.get(0).getRatePlans().size()-1 > SKIP_SELECT_ROOM_THRESHOLD;
        
        // Determine if this is a recommendation flow - for OrchV2, check if any room has recommendation type
        boolean isRecommendation = rooms.stream().anyMatch(room -> 
                isRecommendedRoom(room) || isOccasionRoom(room));
        
        // Check if any room is a package room - for OrchV2, check room types
        boolean isPackageRoom = rooms.stream().anyMatch(room -> isSuperPackageRoom(room));
        
        // Check if any room is an occasion room - for OrchV2, check room types  
        boolean isOccassion = rooms.stream().anyMatch(room -> isOccasionRoom(room));
        
        // Possible rooms, rateplan combinations below:
        // 1. 1 room, x rateplans = skip select rooms can be true if x <= 3.
        // 2. n rooms, x rateplans = skip select room is false.
        // Combo cases
        // 3. 1 room, made into n rooms, 1 rateplan = skip select room can be true.
        // 4. 1 room, made into n rooms, x rateplans = skip select room is false.
        // 5. x rooms, made into n rooms, x rateplans = skip select room is false.
        if (isRecommendation && CollectionUtils.isNotEmpty(roomDetailsList) && 
                CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans()) && 
                roomDetailsList.get(0).getRatePlans().size() == 1 && 
                isUpSellExperimentEnalbed && isAltAccoHotel) {
            // In this case only flag is set. UpSellDetails node is not sent from BE.
            if (commonModifierResponse != null) {
                commonModifierResponse.setSkipRoomSelectEnabled(true);
            }
        } else {
            if (commonModifierResponse != null) {
                commonModifierResponse.setSkipRoomSelectEnabled(false);
            }
        }
        
        if(isUpSellExperimentEnalbed && isAltAccoHotel && doesPropertyContainOnlyOneRoomType && 
                !doesNumOfRatePlansExceedProvidedThreshold && !isRecommendation && 
                commonModifierResponse != null) {
            updateUpSellOptions(roomDetailsList.get(0).getRatePlans(), commonModifierResponse);
        }
        
        if(CollectionUtils.isNotEmpty(roomDetailsList) && 
                CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans()) && 
                roomDetailsList.get(0).getRatePlans().size() == 1 && 
                !isPackageRoom && !isOccassion && doesPropertyContainOnlyOneRoomType && 
                !isRecommendation && commonModifierResponse != null) {
            commonModifierResponse.setSkipRoomSelectEnabled(true);
        }

        // As a part of select room revamp, skip select room logic is changed.
        if (utility.isExperimentOn(expData, SELECT_ROOM_V2_SKIP_EXP_KEY) && commonModifierResponse != null) {
            commonModifierResponse.setSkipRoomSelectEnabled(selectRoomV2ResponseTransformer.shouldSkipSelectRoom(
                    roomDetailsList, hotelDetails.getListingType(), isRecommendation, expData
            ));
        }
        
        // In case of entire properties with a single room type, we need to hide the room details.
        if (utility.isExperimentOn(expData, SELECT_ROOM_REVAMP_EXP_KEY)) {
            // In case of entire properties having exact rooms cases, hide room details if roomSize is 1
            // and ratePlans are multiple
            if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelDetails.getListingType()) && !isRecommendation
                    && roomDetailsList.size() == 1 && 
                    CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans()) && 
                    roomDetailsList.get(0).getRatePlans().size() > 1) {
                roomDetailsList.get(0).setHideRoomDetails(true);
            }
        }

        return roomDetailsList;
    }

    public String buildBedInfoText(RoomInfo roomInfo) {
        if (roomInfo == null) {
            return null;
        }
        
        LinkedHashMap<String, Integer> bedInfoMap = new LinkedHashMap<>();
        
        // Extract bed information from spaces (similar to RoomInfoHelper.buildStayDetails)
        if (CollectionUtils.isNotEmpty(roomInfo.getSpaces())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData spaceData : roomInfo.getSpaces()) {
                if (CollectionUtils.isNotEmpty(spaceData.getSpaces())) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space : spaceData.getSpaces()) {
                        if (space.getSleepingDetails() != null && CollectionUtils.isNotEmpty(space.getSleepingDetails().getBedInfo())) {
                            for (ArrangementInfo bedInfo : space.getSleepingDetails().getBedInfo()) {
                                if (StringUtils.isNotBlank(bedInfo.getType()) && bedInfo.getCount() > 0) {
                                    bedInfoMap.put(bedInfo.getType(), bedInfoMap.getOrDefault(bedInfo.getType(), 0) + bedInfo.getCount());
                                }
                            }
                        }
                    }
                }
            }
        } 
        // Fallback to roomArrangementMap if spaces data is not available
        else if (roomInfo.getRoomArrangementMap() != null && 
                 CollectionUtils.isNotEmpty(roomInfo.getRoomArrangementMap().get("BEDS"))) {
            for (ArrangementInfo arrangementInfo : roomInfo.getRoomArrangementMap().get("BEDS")) {
                if (arrangementInfo != null && StringUtils.isNotBlank(arrangementInfo.getType()) && arrangementInfo.getCount() > 0) {
                    bedInfoMap.put(arrangementInfo.getType(), bedInfoMap.getOrDefault(arrangementInfo.getType(), 0) + arrangementInfo.getCount());
                }
            }
        }
        // Final fallback to direct fields
        else if (StringUtils.isNotBlank(roomInfo.getBedType()) && roomInfo.getBedCount() > 0) {
            bedInfoMap.put(roomInfo.getBedType(), roomInfo.getBedCount());
        }
        
        // If no bed info found, return null
        if (MapUtils.isEmpty(bedInfoMap)) {
            return null;
        }
        
        return utility.createBedInfoTextFromBedInfoMap(bedInfoMap);
    }

    public CalendarAvailabilityResponse convertCalendarAvailabilityResponse(ConsolidatedCalendarAvailabilityResponse calendarAvailabilityResponseHES, String currency) {
        CalendarAvailabilityResponse calendarAvailabilityResponseCG = new CalendarAvailabilityResponse();
        Map<String, CalendarBO> dates = new LinkedHashMap<>();
        if(MapUtils.isNotEmpty(calendarAvailabilityResponseHES.getDates())) {
            calendarAvailabilityResponseHES.getDates().forEach((date, calendarBOHES) -> {
                if(calendarBOHES!=null) {
                    CalendarBO calendarBO = new CalendarBO();
                    calendarBO.setStatus(calendarBOHES.getStatus() != null ? calendarBOHES.getStatus().name() : EMPTY_STRING);
                    calendarBO.setPrice(calendarBOHES.getPrice());
                    calendarBO.setPriceColor(reArchUtility.getPriceColorForPriceDrop(calendarBOHES.getPriceVariationType()));
                    dates.put(date, calendarBO);
                }
            });
        }
        calendarAvailabilityResponseCG.setDates(dates);
        calendarAvailabilityResponseCG.setCurrency(currency);
        return calendarAvailabilityResponseCG;
    }

    private void buildStayDetails(RoomDetails roomDetails) {
        int roomCount = 1;
        if (CollectionUtils.isNotEmpty(roomDetails.getRatePlans()) && CollectionUtils.isNotEmpty(roomDetails.getRatePlans().get(0).getTariffs())) {
            Tariff tariff = roomDetails.getRatePlans().get(0).getTariffs().get(0);
            if (tariff.getOccupancydetails() != null && tariff.getOccupancydetails().getRoomCount() != null) {
                roomCount = tariff.getOccupancydetails().getRoomCount();
            }
        }
        StayDetail stayDetail = new StayDetail();
        if (roomDetails.getBedCount() != null) {
            stayDetail.setBed(roomCount * roomDetails.getBedCount());
        }
        if (roomDetails.getMaxGuest() != null) {
            stayDetail.setMaxGuests(roomCount * roomDetails.getMaxGuest());
        }
        if (roomDetails.getBedroomCount() != null) {
            stayDetail.setBedRoom(roomCount * roomDetails.getBedroomCount());
        }
        if(roomDetails.getExtraBedCount() != null) {
            stayDetail.setExtraBeds(roomCount * roomDetails.getExtraBedCount());
        }
        if(roomDetails.getBaseGuest() != null) {
            stayDetail.setBaseGuests(roomCount * roomDetails.getBaseGuest());
        }
        roomDetails.setStayDetail(stayDetail);
    }

    private void updateUpSellOptions(List<SelectRoomRatePlan> ratePlans, CommonModifierResponse commonModifierResponse) {
        SelectRoomRatePlan lowestRate = null;
        double baseRatePlanSellingPrice = 0;
        for (SelectRoomRatePlan ratePlan : ratePlans) {
            if(lowestRate == null) {
                lowestRate = ratePlan;
                if(CollectionUtils.isNotEmpty(ratePlan.getTariffs()) && ratePlan.getTariffs().get(0).getPriceMap() != null) {
                    for (Map.Entry<String, TotalPricing> entry : ratePlan.getTariffs().get(0).getPriceMap().entrySet()) {
                        // 2 cases are possible here.
                        // 1. When no coupon is present. We pick the price of the default.
                        // 2. When one or more coupons are there, we pick the auto applicable coupon which Manthan sends.
                        if (CollectionUtils.isNotEmpty(entry.getValue().getDetails()) && (entry.getValue().getCoupons() == null || entry.getValue().getCoupons().get(0).isAutoApplicable())) {
                            Optional<com.mmt.hotels.clientgateway.response.PricingDetails> totalPrice = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TOTAL_AMOUNT_KEY)).findFirst();
                            Optional<com.mmt.hotels.clientgateway.response.PricingDetails> taxesAndFees = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TAXES_KEY)).findFirst();
                            if(totalPrice.isPresent() && taxesAndFees.isPresent())
                                baseRatePlanSellingPrice = totalPrice.get().getAmount() + taxesAndFees.get().getAmount();
                        }
                    }
                }
                continue;
            }

            if(CollectionUtils.isNotEmpty(ratePlan.getTariffs()) && ratePlan.getTariffs().get(0).getPriceMap() != null) {
                for (Map.Entry<String, TotalPricing> entry : ratePlan.getTariffs().get(0).getPriceMap().entrySet()) {
                    if (CollectionUtils.isNotEmpty(entry.getValue().getDetails()) && (CollectionUtils.isEmpty(entry.getValue().getCoupons()) || entry.getValue().getCoupons().get(0).isAutoApplicable())) {
                        Optional<com.mmt.hotels.clientgateway.response.PricingDetails> totalPrice = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TOTAL_AMOUNT_KEY)).findFirst();
                        Optional<com.mmt.hotels.clientgateway.response.PricingDetails> taxesAndFees = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TAXES_KEY)).findFirst();
                        double currentRatePlanSellingPrice = 0;
                        if(totalPrice.isPresent() && taxesAndFees.isPresent())
                            currentRatePlanSellingPrice = totalPrice.get().getAmount() + taxesAndFees.get().getAmount();

                        String mealPlanCheapest = lowestRate.getMealPlanCode();
                        boolean cancellationIncludedCheapest = Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(lowestRate.getCancellationPolicy().getType().name()) || Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(lowestRate.getCancellationPolicy().getType().name());
                        String mealPlanCurrent = ratePlan.getMealPlanCode();
                        boolean cancellationIncludedCurrent = Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name()) || Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name());
                        if(StringUtils.isNotBlank(mealPlanCheapest) && StringUtils.isNotBlank(mealPlanCurrent)) {
                            UpSellDetails upSellData = buildUpSellDetails(mealPlanCheapest, cancellationIncludedCheapest, mealPlanCurrent, cancellationIncludedCurrent, (currentRatePlanSellingPrice - baseRatePlanSellingPrice));
                            if(upSellData != null && StringUtils.isNotEmpty(upSellData.getTitle())) {
                                ratePlan.getTariffs().get(0).setUpSellDetails(upSellData);
                                commonModifierResponse.setSkipRoomSelectEnabled(true);
                            }
                        }
                    }
                }
            }
        }
    }

    public UpSellDetails buildUpSellDetails(String cheapestRatePlanMealPlanCode, boolean cheapestRatePlanFreeCancellationIncluded, String currentRatePlanMealPlanCode, boolean currentRatePlanFreeCancellationIncluded, double diffAmount) {
        if(diffAmount == 0 || StringUtils.isEmpty(cheapestRatePlanMealPlanCode) || StringUtils.isEmpty(currentRatePlanMealPlanCode) )
            return null;
        UpSellDetails upSellData = new UpSellDetails();
        int amount = (int)Math.round(diffAmount);
        if(cheapestRatePlanFreeCancellationIncluded) { // No need to upsell free cancellation in this case.
            if(cheapestRatePlanMealPlanCode.equalsIgnoreCase("EP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("AO") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("BD") ) {
                if (currentRatePlanMealPlanCode.equalsIgnoreCase("CP") || currentRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_ADDED_DETAILS_PAGE_TITLE));
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP")  || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP") ) {
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
                }
            } else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("CP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
                if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
                }
            } else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("MAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
                if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
                }
            } else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                // Nothing to upsell.
            }
        } else { // Need to upsell free cancellation as well in this case.
            if(cheapestRatePlanMealPlanCode.equalsIgnoreCase("EP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("AO") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("BD") ) {
                if ((currentRatePlanMealPlanCode.equalsIgnoreCase("EP") || currentRatePlanMealPlanCode.equalsIgnoreCase("AO") || currentRatePlanMealPlanCode.equalsIgnoreCase("BD") ) && currentRatePlanFreeCancellationIncluded) {
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("CP") || currentRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
                    if(currentRatePlanFreeCancellationIncluded) {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                    } else {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_ADDED_DETAILS_PAGE_TITLE));
                    }
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
                    if(currentRatePlanFreeCancellationIncluded) {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                    } else {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
                    }
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                    if(currentRatePlanFreeCancellationIncluded) {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                    } else {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
                    }
                }
            } else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("CP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
                if ((currentRatePlanMealPlanCode.equalsIgnoreCase("CP") || currentRatePlanMealPlanCode.equalsIgnoreCase("CB")) && currentRatePlanFreeCancellationIncluded) {
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
                    if(currentRatePlanFreeCancellationIncluded) {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                    } else {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
                    }
                } else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                    if(currentRatePlanFreeCancellationIncluded) {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                    } else {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
                    }
                }
            } else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("MAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
                if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") && currentRatePlanFreeCancellationIncluded) {
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                }else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                    if(currentRatePlanFreeCancellationIncluded) {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                    } else {
                        upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                        upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                        upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
                    }
                }
            } else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("AP")){
                if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP") && currentRatePlanFreeCancellationIncluded) {
                    upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
                    upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
                    upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
                }
            }
        }
        return upSellData;
    }

    private void setFilterDetails(PackageRoomDetails packageGenerated) {
        FilterDetailsClient filterDetails = new FilterDetailsClient();
        filterDetails.setTitle("Show All Packages");
        filterDetails.setFilterCode(new ArrayList<>());
        filterDetails.getFilterCode().add("PACKAGE_RATE");
        packageGenerated.setFilterDetails(filterDetails);
    }

    /**
     * Determines if a room is a package room based on OrchV2 data.
     * Following legacy pattern: check for package attributes in room.
     */
    private boolean isSuperPackageRoom(Rooms room) {
        RoomType roomTypeEnum  = RoomType.fromValue(room.getType());
        return RoomType.SUPER_PACKAGE.equals(roomTypeEnum);
    }

    /**
     * Determines if a room is a recommended room based on OrchV2 data.
     * Following legacy pattern: check for meal upsell or recommendation attributes.
     */
    private boolean isRecommendedRoom(Rooms room) {
        RoomType roomTypeEnum  = RoomType.fromValue(room.getType());
        return RoomType.MMT_RECOMMEND.equals(roomTypeEnum) || RoomType.MEAL_UPGRADE.equals(roomTypeEnum);
    }

    /**
     * Determines if a room is an occasion room based on OrchV2 data.
     * Following legacy pattern: check for occasion type.
     */
    private boolean isOccasionRoom(Rooms room) {
        RoomType roomTypeEnum  = RoomType.fromValue(room.getType());
        return RoomType.OCCASION_PACKAGE.equals(roomTypeEnum);
    }

    private void setRecommendedRoomFields(RoomDetails roomDetails, Rooms room, Map<String, String> expData) {
        try {
            // Based on HES Detail PricingRequestWorkFlowExecutor.setPackageRoomInfo() logic
            // For recommended rooms, we use the "unlocked" case from setPackageRoomInfo

            // TODO: MISSING FIELDS IN ORCHV2 RoomDetails CLASS
            // The following fields are missing from OrchV2 RoomDetails class but are required for recommended rooms:
            // - title: Should be set from polyglot UNLOCKED_TITLE or NON_LUXE_UNLOCKED_TITLE
            // - recommendText: Should be set from polyglot RECOMMEND_TEXT ("MMT Recommends")
            // - ctaText: Should be set from polyglot RECOMMENDED_CTA_TEXT or CTA_TEXT ("Book Now")
            // - type: Should be set from room.getType() or room.getSellableType()

            // These fields need to be added to the RoomDetails class in ClientGateway
            // Based on HES Detail PricingRequestWorkFlowExecutor.setPackageRoomInfo() logic:

            // roomDetails.setTitle(room.getName()); // MISSING METHOD
            // roomDetails.setRecommendText("MMT Recommends"); // MISSING METHOD
            // roomDetails.setCtaText("Book Now"); // MISSING METHOD
            // roomDetails.setType(room.getType() != null ? room.getType() : room.getSellableType()); // MISSING METHOD

            LOGGER.info("Missing fields for recommended rooms: title, recommendText, ctaText, type - need to be added to RoomDetails class");

        } catch (Exception e) {
            LOGGER.warn("Error setting recommended room fields: " + e.getMessage(), e);
        }
    }

    private static List<SleepingArrangement> getSleepingArrangements(RoomInfo roomInfo) {
        List<SleepingArrangement> beds = new ArrayList<>();
        for (List<ArrangementInfo> arrangementInfos : roomInfo.getRoomArrangementMap().values()) {
            for (ArrangementInfo arrangementInfo : arrangementInfos) {
                SleepingArrangement bed = new SleepingArrangement();
                bed.setType(arrangementInfo.getType());
                bed.setCount(arrangementInfo.getCount());
                beds.add(bed);
            }
        }
        return beds;
    }

    private List<SelectRoomRatePlan> transformRatePlans(List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan> ratePlans, String sellableType, Map<String, String> expData, Rooms room, HotelDetails hotelDetails,
                                                        String askedCurrency, String funnelSource, int los, int ap,
                                                        boolean isBlockPAH, CommonModifierResponse commonModifierResponse,
                                                        Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, String roomViewType,List<String> amenitiesFilterCodes) {
        List<SelectRoomRatePlan> selectRatePlans = new ArrayList<>();
        
        // Get linked rate experiment value for processing
        String linkedRateExperimentValue = getExperimentValue(commonModifierResponse, Constants.LINKED_RATE_EXPERIMENT_NR);
        
        // Create RPC rate plan map for linked rate processing (similar to SearchRoomsResponseTransformer)
        Map<String, com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan> rpcRatePlanMap = new LinkedHashMap<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan ratePlan : ratePlans) {
            rpcRatePlanMap.put(ratePlan.getCode(), ratePlan);
        }

        for (RatePlan ratePlan : ratePlans) {
            SelectRoomRatePlan selectRatePlan = new SelectRoomRatePlan();

            // Basic rate plan information
            selectRatePlan.setSupplierCode(ratePlan.getTrackingInfo().getSupplierCode());
            selectRatePlan.setRpc(ratePlan.getCode()); // OrchV2 has already set this as RPC key
            selectRatePlan.setVendorRatePlanCode(ratePlan.getRpcc());
            selectRatePlan.setName(buildRatePlanName(ratePlan, sellableType, hotelDetails.getListingType(), expData));
            selectRatePlan.setFilterCode(new ArrayList<>());
            // TODO: setDescription not available in SelectRoomRatePlan
            // selectRatePlan.setDescription(ratePlan.getDescription());

            // Store rate plan name in map
            ratePlanCodeAndNameMap.put(ratePlan.getCode(), selectRatePlan.getName());

            // Set payment mode
            if (ratePlan.getPaymentMode() != null) {
                selectRatePlan.setPayMode(ratePlan.getPaymentMode().name());
            }

//            selectRatePlan.setRatePlanType(ratePlan.getRateType());
            selectRatePlan.setSellableType(sellableType);

            // Transform inclusions with system-generated inclusions
            selectRatePlan.setInclusionsList(transformInclusions(ratePlan.getInclusions(), ratePlan, expData, ap, isBlockPAH));

            // Transform cancellation policy
            if (ratePlan.getCancellationPolicy() != null) {
                selectRatePlan.setCancellationPolicy(cancellationPolicyHelper.transformCancellationPolicy(ratePlan.getCancellationPolicy(), ap));
                // Set cancellation timelines directly from orchestrator v2
                if (ratePlan.getCancellationPolicy().getCancellationTimeline() != null) {
                    // Build cancellation timeline directly from orchestrator v2
                    selectRatePlan.setCancellationTimeline(cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(ratePlan.getCancellationPolicy().getCancellationTimeline()));
                    selectRatePlan.setCancellationPolicyTimeline(cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(ratePlan.getCancellationPolicy().getCancellationTimeline()));
                }
            }

            // Create multiple tariffs from pricePerOccupancy data (OrchV2 has grouped the rate plans)
            List<Tariff> tariffs = buildTariffs(ratePlan, room, askedCurrency, expData, sellableType, los, funnelSource, commonModifierResponse, hotelDetails);
            selectRatePlan.setTariffs(tariffs);

            // Set review deeplink URL
            if (StringUtils.isNotEmpty(ratePlan.getReviewDeeplinkUrl())) {
                selectRatePlan.setReviewDeeplinkUrl(ratePlan.getReviewDeeplinkUrl());
            }
            selectRatePlan.setTariffPersuasion(ratePlan.getPersuasions());
            selectRatePlan.setInstantConfirmation(ratePlan.getInstantConfirmation());
            if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans())) {
                selectRatePlan.setMealPlanCode(ratePlan.getMealPlans().get(0).getCode());
            }

            // Set persuasions
            selectRatePlan.setPersuasions(searchRoomsPersuasionHelper.getRatePlanPersuasion(selectRatePlan, ratePlan, funnelSource, commonModifierResponse, isLuxeHotel));
            selectRatePlan.setRatePlanPersuasionsMap(searchRoomsPersuasionHelper.buildRatePlanPersuasionsMap(ratePlan, commonModifierResponse));
            selectRatePlan.setFilterCode(getFilterCodes(ratePlan, isBlockPAH, ap, commonModifierResponse, sellableType, roomViewType, amenitiesFilterCodes));
            selectRatePlan.setSegmentId(ratePlan.getSegmentId());
//            selectRatePlan.setPackageRateAvailable(ratePlan.getRatePlanFlags().getPackageRatePlan());
            if(utility.isSPKGExperimentOn(expData) && ratePlan.getRatePlanFlags().isPackageRatePlan()) {
                selectRatePlan.setPackageRateAvailable(true);
                selectRatePlan.setType(elitePackageType);
                selectRatePlan.setHighlightImage(elitePackageIconUrl);
//                utility.transformInclusionsForPackageRatePlan(ratePlan.getInclusionsList());
            }

            // ===== LINKED RATE PROCESSING SECTION (NEW) =====
            // Process parent linked rates if experiment is enabled
            if(LINKED_RATE_EXP_ONE_VARIANT.equalsIgnoreCase(linkedRateExperimentValue) && CollectionUtils.isNotEmpty(ratePlan.getLinkedRates())){
                String parent = ratePlan.getLinkedRates().get(0).getPricingKey();
                if(rpcRatePlanMap.containsKey(parent)){
                    com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan parentRatePlan = rpcRatePlanMap.get(parent);
                    if(Objects.nonNull(parentRatePlan)){
                        // Build parent linked rate name and data
                        String linkedRatePlanName = buildRatePlanName(parentRatePlan, sellableType, hotelDetails.getListingType(), expData);
                        selectRatePlan.setLinkedRatePlanName(utility.replaceWithFreeCancellation(linkedRatePlanName));
                        buildParentLinkedRates(selectRatePlan, ratePlan.getLinkedRates());
                    }
                }
            }

            // Process UPSELL rate plans from linked rates
            List<com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate> linkedRates = ratePlan.getLinkedRates();
            if (linkedRates != null && linkedRates.size() > 0) {
                if ("UPSELL".equalsIgnoreCase(linkedRates.get(0).getType())) {
                    // TODO: Build UpsellRatePlan (Task 4)
                    UpsellRatePlan upsellRatePlan = buildUpsellRatePlan(linkedRates, rpcRatePlanMap, sellableType, hotelDetails.getListingType(), expData, ap);
                    selectRatePlan.setUpsellRatePlan(upsellRatePlan);
                }
            }

            // Set child linked rates availability flag
            selectRatePlan.setLinkedRatePlanAvailable(CollectionUtils.isNotEmpty(ratePlan.getChildLinkedRates()));

            // Process child linked rates if experiment is enabled
            if(LINKED_RATE_EXP_ONE_VARIANT.equalsIgnoreCase(linkedRateExperimentValue) && CollectionUtils.isNotEmpty(ratePlan.getChildLinkedRates())){
                buildChildLinkedRates(selectRatePlan, ratePlan.getChildLinkedRates());
            }
            // ===== END LINKED RATE PROCESSING SECTION =====

            setCanTranslateFlag(selectRatePlan, translateEnabledSupplierCodes, ratePlan.getTrackingInfo().getSupplierCode());

            if (ratePlan.getPrice() != null && ratePlan.getPrice().getDiscount() != null && ratePlan.getPrice().getDiscount().getHotel() > 0
                    && StringUtils.isNotEmpty(ratePlan.getPrice().getDiscount().getHCPEncrypted())) {
                selectRatePlan.setHCP(ratePlan.getPrice().getDiscount().getHCPEncrypted());
            }
            
            // Handle flywheel-specific additional fees with enhanced functionality
            if (commonModifierResponse != null
                    && commonModifierResponse.getTrafficSource() != null
                    && Constants.TRAFFIC_SOURCE_FLYWHEEL.equalsIgnoreCase(commonModifierResponse.getTrafficSource())) {
                boolean showTransfersFeeTxt = false;
                if (MapUtils.isNotEmpty(expData) && expData.containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
                    showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expData.get(Constants.TRANSFERS_FEE_TEXT_KEY));
                }
                selectRatePlan.setAdditionalFees(buildAdditionalChargesForRatePlan(ratePlan, hotelDetails, showTransfersFeeTxt));
            }

            // Set all inclusive rate flag
            selectRatePlan.setAllInclusiveRate(ratePlan.getRatePlanFlags().isAllInclusiveRate());

            // Build cancel policy description for paymode (GI specific)
            buildCancelPolicyDescriptionPaymod(selectRatePlan);

            // TODO: Handle QuickBook functionality when fields are available in orchestrator model
            // TODO: Handle HCP encrypted (Hotelier Coupon) when fields are available in orchestrator model
            // TODO: Handle MMT Exclusive/GCC functionality when proper fields are available
            // TODO: Handle package room specific functionality when fields are available
            // TODO: Handle occasion room persuasion when fields are available

            selectRatePlans.add(selectRatePlan);
        }

        return selectRatePlans;
    }

    private AdditionalMandatoryCharges transformAdditionalFees(HotelDetails hotelDetails, CommonModifierResponse commonModifierResponse) {
        // Extract room type information from orchestrator model
        Map<String, Rooms> roomsMap = null;
        if (hotelDetails.getRooms() != null && !hotelDetails.getRooms().isEmpty()) {
            roomsMap = hotelDetails.getRooms().stream()
                    .collect(Collectors.toMap(Rooms::getCode, Function.identity(), (existing, replacement) -> existing));
        }

        // Extract supplier details and conversion factor from the first available rate plan
        com.mmt.hotels.model.response.pricing.SupplierDetails supplierDetails = null;
        double conversionFactor = 1.0;

        if (MapUtils.isNotEmpty(roomsMap)) {
            for (Map.Entry<String, Rooms> roomEntry : roomsMap.entrySet()) {
                Rooms room = roomEntry.getValue();
                if (room.getRatePlans() != null && !room.getRatePlans().isEmpty()) {
                    for (RatePlan ratePlan : room.getRatePlans()) {
                        if (ratePlan.getPrice() != null
                                && ratePlan.getPrice().getCurrencyConvertor() > 0) {
                            // Map orchestrator supplier details to old model
                            supplierDetails = new com.mmt.hotels.model.response.pricing.SupplierDetails();
                            //TODO add SupplierDetails in Orchestrator response
                            //supplierDetails.setSupplierCode(ratePlan.getSupplier().getCode());
                            //supplierDetails.setHotelierCurrencyCode(ratePlan.getSupplier().getCurrencyCode());

                            conversionFactor = ratePlan.getPrice().getCurrencyConvertor();
                            break;
                        }
                    }
                    //if (supplierDetails != null) break;
                }
            }
        }

        // Check if transfers fee text should be shown
        boolean showTransfersFeeTxt = false;
        if (commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
                && commonModifierResponse.getExpDataMap().containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
            showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.TRANSFERS_FEE_TEXT_KEY));
        }

        // Transform orchestrator AdditionalFees to old model AdditionalFees
        List<com.mmt.hotels.model.response.pricing.AdditionalFees> oldAdditionalFees = transformMandatoryCharges(hotelDetails.getMandatoryCharges());

        // Build AdditionalChargesBO object
        AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
                .buildUserCurrency(hotelDetails.getCurrencyCode()) //TODO read from user currency
                .buildHotelierCurrency(hotelDetails.getCurrencyCode())
                .buildPropertyType(hotelDetails.getPropertyType())
                .buildAdditionalFees(oldAdditionalFees)
                .buildConversionFactor(conversionFactor)
                .buildCityCode(hotelDetails.getLocation() != null && hotelDetails.getLocation().getCityId() != null ? hotelDetails.getLocation().getCityId() : null)
                .build();

        return commonResponseTransformer.buildAdditionalCharges(additionalChargesBO, showTransfersFeeTxt);
    }

    private List<com.mmt.hotels.model.response.pricing.AdditionalFees> transformMandatoryCharges(
            List<com.gommt.hotels.orchestrator.detail.model.response.pricing.AdditionalFees> orchestratorFees) {

        if (CollectionUtils.isEmpty(orchestratorFees)) {
            return new ArrayList<>();
        }

        List<com.mmt.hotels.model.response.pricing.AdditionalFees> additionalFeesList = new ArrayList<>();

        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.AdditionalFees orchFee : orchestratorFees) {
            com.mmt.hotels.model.response.pricing.AdditionalFees additionalFees = new com.mmt.hotels.model.response.pricing.AdditionalFees();

            // Map ALL basic fields from orchestrator model
            additionalFees.setAmount(orchFee.getAmount());
            additionalFees.setCategory(orchFee.getCategory());
            additionalFees.setLeafCategory(orchFee.getCategory());
            additionalFees.setDescription(orchFee.getDescription());
            additionalFees.setMandatory(orchFee.isMandatory());
            additionalFees.setCurrency(orchFee.getCurrency());

            // Map name if available
            if (StringUtils.isNotBlank(orchFee.getName())) {
                additionalFees.setName(orchFee.getName());
            }

            // Map asked currency amount - this is the display amount in user's currency
            if (orchFee.getAskedCurrencyAmount() > 0) {
                additionalFees.setAskedCurrencyAmount(orchFee.getAskedCurrencyAmount());
            }

            // Map total counts
            additionalFees.setTotalAdults(orchFee.getTotalAdults());
            additionalFees.setTotalChild(orchFee.getTotalChild());
            additionalFees.setTotalRooms(orchFee.getTotalRooms());
            additionalFees.setApplicableDaysCount(orchFee.getApplicableDaysCount());

            // Map property types
            if (CollectionUtils.isNotEmpty(orchFee.getPropertyType())) {
                additionalFees.setPropertyType(new ArrayList<>(orchFee.getPropertyType()));
            }

            if (CollectionUtils.isNotEmpty(orchFee.getPropertySubType())) {
                additionalFees.setPropertySubType(new ArrayList<>(orchFee.getPropertySubType()));
            }

            // Map price breakdown if available
            if (orchFee.getPrice() != null) {
                AdditionalFeesPrice additionalFeesPrice = new AdditionalFeesPrice();

                // Map all price fields
                additionalFeesPrice.setDefaultPrice(orchFee.getPrice().getDefaultPrice());
                additionalFeesPrice.setPerStayRoom(orchFee.getPrice().getPerStayRoom());
                additionalFeesPrice.setPerStayAdult(orchFee.getPrice().getPerStayAdult());
                additionalFeesPrice.setPerStayChild(orchFee.getPrice().getPerStayChild());
                additionalFeesPrice.setPerStayInfant(orchFee.getPrice().getPerStayInfant());
                additionalFeesPrice.setPerNightRoom(orchFee.getPrice().getPerNightRoom());
                additionalFeesPrice.setPerNightAdult(orchFee.getPrice().getPerNightAdult());
                additionalFeesPrice.setPerNightChild(orchFee.getPrice().getPerNightChild());
                additionalFeesPrice.setPerNightInfant(orchFee.getPrice().getPerNightInfant());

                // Set the price breakdown on the AdditionalFees object
                // Note: If setPriceBreakdown doesn't exist, we may need to set individual price fields
                // or store it in a different way based on the old model structure
                additionalFees.setPrice(additionalFeesPrice);
            }

            additionalFeesList.add(additionalFees);
        }

        return additionalFeesList;
    }

    private List<String> getFilterCodes(com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan ratePlan , boolean isBlockPAH , int ap,
                                        CommonModifierResponse commonModifierResponse, String sellableType, String roomViewType, List<String> amenitiesFilterCode) {
        List<String> filterCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
                && ! (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
            filterCodes.add(Constants.FREE_BREAKFAST);
        }
        if (mealplanFilterEnable &&  CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
                &&  (Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
            filterCodes.add(Constants.TWO_MEAL_AVAIL);
        }
        if (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
                &&  (Constants.MEAL_PLAN_CODE_ALL_MEALS_AI.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_ALL_MEALS.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
            filterCodes.add(Constants.ALL_MEAL_AVAIL);
        }
        if (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
                &&  (Constants.MEAL_PLAN_CODE_ALL_MEALS_AI.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
            filterCodes.add(Constants.ALL_INCLUSIVE);
        }
        boolean freeCancellation =  ratePlan.getCancellationPolicy() != null && CollectionUtils.isNotEmpty(ratePlan.getCancellationPolicy().getPenalties()) && com.mmt.hotels.model.response.pricing.CancelPenalty.CancellationType.FREE_CANCELLATON.name().equalsIgnoreCase(ratePlan.getCancellationPolicy().getPenalties().get(0).getType());
        if (freeCancellation) {
            filterCodes.add(Constants.FREE_CANCELLATION);
        }
        if (freeCancellation && ratePlan.getBnplDetails() != null && ratePlan.getBnplDetails().isEligibleForHoldBooking() && ratePlan.getBnplDetails().getExpiry() != null && ratePlan.getBnplDetails().getBookingAmount() == 0f) {
            filterCodes.add(BOOK_NOW_AT_0);
        }

        if (freeCancellation && ratePlan.getBnplDetails() != null && ratePlan.getBnplDetails().isEligibleForHoldBooking() && ratePlan.getBnplDetails().getExpiry() != null && ratePlan.getBnplDetails().getBookingAmount() == 1f) {
            filterCodes.add(BOOK_NOW_AT_1);
        }
        if (!(Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId()))) {
            if (null != ratePlan.getPaymentMode()
                    && !ratePlan.getPaymentMode().getMappedPayMode().equalsIgnoreCase(com.mmt.hotels.model.response.pricing.PaymentMode.PAS.getMappedPayMode())) {
                filterCodes.add(isBlockPAH && ap < 5 ? "FCZPN" : "PAH");
            }
            if (ratePlan.getBnplDetails() != null && ratePlan.getBnplDetails().isBnplApplicable()) {
                filterCodes.add("FCZPN");
            }
            if (CollectionUtils.isNotEmpty(ratePlan.getInclusions()) && ratePlan.getInclusions().stream().anyMatch(a -> ("Packages".equalsIgnoreCase(a.getCategory()) || "Packages1".equalsIgnoreCase(a.getCategory())
                    || "Packages2".equalsIgnoreCase(a.getCategory()) || "Packages3".equalsIgnoreCase(a.getCategory())
                    || "MMTBLACK".equalsIgnoreCase(a.getCategory())))) {
                filterCodes.add("SPECIALDEALS");
            }

            if(ratePlan.getRatePlanFlags().isStaycationDeal()
                    && (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
                    &&  (Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                    || Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                    || Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))
                    || Constants.MEAL_PLAN_CODE_ALL_MEALS.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
                filterCodes.add("STAYCATION");
            }
        }

        if (ratePlan.getRatePlanFlags().isPackageRatePlan()) {
            filterCodes.add(PACKAGE_RATE);
        }
        if (partnerExclusiveFilterEnable && mypatExclusiveRateSegmentIdList.contains(ratePlan.getSegmentId()))
            filterCodes.add("CTA_RATES_AVAIL");
        if (SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableType)) {
            filterCodes.add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
        }
        if (roomViewType != null && !roomViewType.isEmpty()) {
            filterCodes.add(Utility.convertToUnderscoreCaps(roomViewType));
        }
        if (amenitiesFilterCode != null && !amenitiesFilterCode.isEmpty()) {
            filterCodes.addAll(amenitiesFilterCode);
        }
        return filterCodes;
    }

    private TariffViewType getTariffViewType(RoomDetails roomDetails, boolean isFirstRoom) {
        int totalRatePlans = roomDetails.getRatePlans().size();

        TariffViewType clientViewType = new TariffViewType();
        clientViewType.setTotalTariffs(roomDetails.getRatePlans().size());
        clientViewType.setBaseTariffText(polyglotService.getTranslatedData("STARTING_PRICE_AT"));
        if (totalRatePlans == 1) {
            clientViewType.setInitialVisible(1);
            return clientViewType;
        }
        if (MapUtils.isNotEmpty(ratePlanDisplayLogic)) {
            Map<String,Integer> map;
            map = ratePlanDisplayLogic.get("BUDGET").get("DEFAULT");
            if (isFirstRoom) {
                clientViewType.setInitialVisible(map.get("FIRST_ROOM_RPC_COUNT"));
            } else {
                clientViewType.setInitialVisible(map.get("OTHER_ROOM_RPC_COUNT"));
            }
        } else {
            clientViewType.setInitialVisible(ratePlanMoreOptionsLimit);
        }
        if (clientViewType.getInitialVisible()>clientViewType.getTotalTariffs())
            clientViewType.setInitialVisible(clientViewType.getTotalTariffs());

        return clientViewType;
    }

    private List<RoomDetails> transformOccupancyRoomCombos(List<RoomCombo> occupancyRoomCombos,
                                                           HotelDetails hotelDetails,
                                                           Map<String, String> expData, String askedCurrency,
                                                           String funnelSource, int los, int ap,
                                                           boolean isBlockPAH,
                                                           CommonModifierResponse commonModifierResponse,
                                                           Map<String, String> ratePlanCodeAndNameMap,
                                                           boolean isLuxeHotel, boolean isAltAccoHotel) {
        List<RoomDetails> occupancyRoomDetails = new ArrayList<>();

        if (CollectionUtils.isEmpty(occupancyRoomCombos)) {
            return occupancyRoomDetails;
        }

        // Each occupancy room combo should contain rooms with less restrictive occupancy
        for (RoomCombo roomCombo : occupancyRoomCombos) {
            if (CollectionUtils.isNotEmpty(roomCombo.getRooms())) {
                // Transform each room in the combo to RoomDetails
                List<RoomDetails> transformedRooms = transformRoomsToRoomDetails(
                        roomCombo.getRooms(), hotelDetails, expData,
                        askedCurrency, funnelSource, los, ap, isBlockPAH, commonModifierResponse,
                        ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel
                );
                occupancyRoomDetails.addAll(transformedRooms);
            }
        }

        return occupancyRoomDetails;
    }

    private List<RecommendedCombo> transformRoomCombos(List<RoomCombo> roomCombos,
                                                       Media media, HotelDetails hotelDetails, Map<String, String> expData, String askedCurrency,
                                                       String funnelSource, int los, int ap, boolean isBlockPAH, CommonModifierResponse commonModifierResponse,
                                                       Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean isAltAccoHotel) {
        if (CollectionUtils.isEmpty(roomCombos)) {
            return new ArrayList<>();
        }

        List<RecommendedCombo> recommendedCombos = new ArrayList<>();

        // Common variables
        boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        boolean newPropertyOfferApplicable = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) &&
                utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey());
        NoCostEmiDetails noCostEmiDetailForRootLevel = new NoCostEmiDetails();
        double baseComboFare = 0.0;

        /* Case 1 : Make Combo from RecommendedRoomTypeDetails */
        // Find the primary recommended combo (comboType = RECOMMENDED_ROOM)
        List<RoomCombo> recommendedRoomCombos = roomCombos.stream()
                .filter(combo -> ComboType.RECOMMENDED_ROOM.equals(combo.getComboType())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(recommendedRoomCombos)) {
            for (RoomCombo recommendedRoomCombo : recommendedRoomCombos) {
                // Transform rooms in the combo to RoomDetails
                List<RoomDetails> roomDetailsList = transformRoomsToRoomDetails(
                        recommendedRoomCombo.getRooms(), hotelDetails,
                        expData, askedCurrency, funnelSource, los, ap, isBlockPAH,
                        commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel,
                        isAltAccoHotel
                );

                // Get sellable type from first room's first rate plan
                String sellableType = Constants.SELLABLE_ROOM_TYPE;
                if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                    sellableType = roomDetailsList.get(0).getRatePlans().get(0).getSellableType();
                }

                // Get staycation deal flag from first room
                boolean staycationDeal = false;
                if (CollectionUtils.isNotEmpty(recommendedRoomCombo.getRooms())) {
                    staycationDeal = recommendedRoomCombo.getRooms().get(0).getStaycationDeal();
                }

                // Build the recommended combo
                RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailsList, utility.getComboName(recommendedRoomCombo.getComboMealPlan()),
                        staycationDeal, true, // This is the base combo
                        funnelSource, recommendedRoomCombo.getOccupancyDetails(), recommendedRoomCombo.getComboMealPlan());

                // Initialize combo tariff
                if (recommendedCombo.getComboTariff() == null) {
                    recommendedCombo.setComboTariff(new Tariff());
                }

                // Set corporate approval info if available
                // TODO: corpMetaData not available in orchestrator v2 PriceDetail

                // Build price map from combo pricing
                if (recommendedRoomCombo.getPriceDetail() != null) {
                    PriceDetail priceDetail = recommendedRoomCombo.getPriceDetail();
                    // Get occupancy details
                    OccupancyDetails occupancyDetails = recommendedRoomCombo.getOccupancyDetails();
                    if (occupancyDetails == null) {
                        occupancyDetails = CollectionUtils.isNotEmpty(priceDetail.getPricePerOccupancy()) ?
                                priceDetail.getPricePerOccupancy().get(0) : null;
                    }


                    recommendedCombo.getComboTariff().setPriceMap(searchRoomsPriceHelper.getPriceMap(priceDetail, expData, recommendedRoomCombo.getOccupancyDetails(), askedCurrency, sellableType, los, false, "",
                            utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource),
                            false, // groupBookingPrice - TODO: check from hotelDetails
                            myPartner, hotelDetails.getListingType()));


                    // Set default price key
                    String defaultPriceKey = "DEFAULT";
                    if (StringUtils.isNotBlank(recommendedRoomCombo.getPriceDetail().getCouponCode())) {
                        defaultPriceKey = recommendedRoomCombo.getPriceDetail().getCouponCode();
                    }
                    recommendedCombo.getComboTariff().setDefaultPriceKey(defaultPriceKey);

                    // Set occupancy details in tariff
                    if (occupancyDetails != null) {
                        RoomTariff roomTariff = new RoomTariff();
                        roomTariff.setNumberOfAdults(occupancyDetails.getAdult());
                        roomTariff.setNumberOfChildren(occupancyDetails.getChild());
                        if (CollectionUtils.isNotEmpty(occupancyDetails.getChildAges())) {
                            roomTariff.setChildAges(occupancyDetails.getChildAges());
                        }
                        roomTariff.setRoomCount(occupancyDetails.getNumberOfRooms());
                        recommendedCombo.getComboTariff().setOccupancydetails(roomTariff);
                    }
                }

                // Set payment plan if available
                // TODO: paymentPlan not available in RoomCombo

                // Group booking specific logic
                if (Utility.isGroupBookingFunnel(funnelSource)) {
                    recommendedCombo.setBaseCombo(true);
                    if (recommendedRoomCombo.getPriceDetail() != null && recommendedRoomCombo.getPriceDetail().getDisplayPrice() > 0.0d) {
                        baseComboFare = recommendedRoomCombo.getPriceDetail().getDisplayPrice();
                    }
                    recommendedCombo.setComboText("<b>" + recommendedCombo.getComboName() + "</b>");
                }

                // Free stay for X children - child occupancy message
                // TODO: freeChildCount and freeChildText not available in RoomCombo

                // Flexi cancel add-on details
                // TODO: flexiCancelRoomDetail not available in RoomCombo

                recommendedCombos.add(recommendedCombo);

            }
        }

        /* Case 2 : Make Combo from OtherRecommendedRooms */
        // Find all other recommended combos (comboType = OTHER_RECOMMENDED_ROOM)
        List<RoomCombo> otherRecommendedCombos = roomCombos.stream()
                .filter(combo -> ComboType.OTHER_RECOMMENDED_ROOM.equals(combo.getComboType()))
                .collect(Collectors.toList());

        for (RoomCombo otherRecommendedCombo : otherRecommendedCombos) {
            // Transform rooms in the combo to RoomDetails
            List<RoomDetails> roomDetailsList = transformRoomsToRoomDetails(
                    otherRecommendedCombo.getRooms(), hotelDetails,
                    expData, askedCurrency, funnelSource, los, ap, isBlockPAH,
                    commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel,
                    isAltAccoHotel
            );

            // Get sellable type from first room's first rate plan
            String sellableType = Constants.SELLABLE_ROOM_TYPE;
            if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                sellableType = roomDetailsList.get(0).getRatePlans().get(0).getSellableType();
            }

            // Get staycation deal flag from first room
            boolean staycationDeal = false;
            if (CollectionUtils.isNotEmpty(otherRecommendedCombo.getRooms())) {
                staycationDeal = otherRecommendedCombo.getRooms().get(0).getStaycationDeal();
            }

            // Build the recommended combo
            RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailsList, utility.getComboName(otherRecommendedCombo.getComboMealPlan()),
                    staycationDeal,
                    false, // This is not the base combo
                    funnelSource, otherRecommendedCombo.getOccupancyDetails(), otherRecommendedCombo.getComboMealPlan());

            // Initialize combo tariff
            if (recommendedCombo.getComboTariff() == null) {
                recommendedCombo.setComboTariff(new Tariff());
            }

            // Build price map from combo pricing
            if (otherRecommendedCombo.getPriceDetail() != null) {
                PriceDetail priceDetail = otherRecommendedCombo.getPriceDetail();
                // Get occupancy details
                OccupancyDetails occupancyDetails = CollectionUtils.isNotEmpty(priceDetail.getPricePerOccupancy()) ?
                        priceDetail.getPricePerOccupancy().get(0) : null;

                recommendedCombo.getComboTariff().setPriceMap(searchRoomsPriceHelper.getPriceMap(priceDetail, expData, otherRecommendedCombo.getOccupancyDetails(), askedCurrency, sellableType, los, false, "",
                        utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource),
                        false, // groupBookingPrice - TODO: check from hotelDetails
                        myPartner, hotelDetails.getListingType()));

                // Set default price key
                String defaultPriceKey = "DEFAULT";
                if (StringUtils.isNotBlank(otherRecommendedCombo.getPriceDetail().getCouponCode())) {
                    defaultPriceKey = otherRecommendedCombo.getPriceDetail().getCouponCode();
                }
                recommendedCombo.getComboTariff().setDefaultPriceKey(defaultPriceKey);

                // Set occupancy details in tariff
                if (occupancyDetails != null) {
                    RoomTariff roomTariff = new RoomTariff();
                    roomTariff.setNumberOfAdults(occupancyDetails.getAdult());
                    roomTariff.setNumberOfChildren(occupancyDetails.getChild());
                    if (occupancyDetails.getChild() > 0) {
                        // TODO: Handle child ages when available in RoomCombo
                    }
                    roomTariff.setRoomCount(occupancyDetails.getNumberOfRooms());
                    recommendedCombo.getComboTariff().setOccupancydetails(roomTariff);
                }
            }

            // Group booking specific logic
            if (Utility.isGroupBookingFunnel(funnelSource)) {
                if (otherRecommendedCombo.getPriceDetail() != null && otherRecommendedCombo.getPriceDetail().getDisplayPrice() > 0.0d) {
                    double otherRecommendationDisplayPrice = otherRecommendedCombo.getPriceDetail().getDisplayPrice();
                    long differenceInPriceFromBaseCombo = (long) (otherRecommendationDisplayPrice - baseComboFare);
                    if (differenceInPriceFromBaseCombo != 0) {
                        recommendedCombo.setComboText(getComboText(otherRecommendedCombo.getComboMealPlan(), differenceInPriceFromBaseCombo));
                    }
                }
            }

            // Free stay for X children - child occupancy message
            // TODO: freeChildCount and freeChildText not available in RoomCombo

            // Flexi cancel add-on details
            // TODO: flexiCancelRoomDetail not available in RoomCombo

            recommendedCombos.add(recommendedCombo);
        }

        /* Case 3 : Make recommended combos out of the exact matched Rooms */
        // Use hotelDetails.getRooms() for exact matches instead of creating a new combo type
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            // Transform rooms to RoomDetails
            List<RoomDetails> roomDetailsList = transformRoomsToRoomDetails(
                    hotelDetails.getRooms(), hotelDetails,
                    expData, askedCurrency, funnelSource, los, ap, isBlockPAH,
                    commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel,
                    isAltAccoHotel
            );

            // Get sellable type from first room's first rate plan
            String sellableType = Constants.SELLABLE_ROOM_TYPE;
            if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                sellableType = roomDetailsList.get(0).getRatePlans().get(0).getSellableType();
            }

            // Get staycation deal flag from first room
            boolean staycationDeal = false;
            if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
                staycationDeal = hotelDetails.getRooms().get(0).getStaycationDeal();
            }

            // Build the recommended combo
            RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailsList, null, // No meal plan for exact matches
                    staycationDeal,
                    false, // This is not the base combo
                    funnelSource, null, null // No occupancy details for exact matches
            );

            // Initialize combo tariff
            if (recommendedCombo.getComboTariff() == null) {
                recommendedCombo.setComboTariff(new Tariff());
            }

            // Build price map from room pricing using rate plans
            if (CollectionUtils.isNotEmpty(roomDetailsList)) {
                RoomDetails firstRoom = roomDetailsList.get(0);
                if (CollectionUtils.isNotEmpty(firstRoom.getRatePlans())) {
                    SelectRoomRatePlan firstRatePlan = firstRoom.getRatePlans().get(0);
                    if (firstRatePlan.getTariffs() != null && !firstRatePlan.getTariffs().isEmpty()) {
                        Tariff firstTariff = firstRatePlan.getTariffs().get(0);
                        recommendedCombo.getComboTariff().setPriceMap(firstTariff.getPriceMap());
                        recommendedCombo.getComboTariff().setEmiPlanDetail(firstTariff.getEmiPlanDetail());
                        recommendedCombo.getComboTariff().setDefaultPriceKey(firstTariff.getDefaultPriceKey());

                        // Set occupancy details in tariff
                        if (firstTariff.getOccupancydetails() != null) {
                            recommendedCombo.getComboTariff().setOccupancydetails(firstTariff.getOccupancydetails());
                        }
                    }
                }
            }

            // Group booking specific logic
            if (Utility.isGroupBookingFunnel(funnelSource)) {
                if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                    SelectRoomRatePlan firstRatePlan = roomDetailsList.get(0).getRatePlans().get(0);
                    if (firstRatePlan.getTariffs() != null && !firstRatePlan.getTariffs().isEmpty() &&
                            firstRatePlan.getTariffs().get(0).getPriceMap() != null) {
                        Map<String, TotalPricing> priceMap = firstRatePlan.getTariffs().get(0).getPriceMap();
                        TotalPricing defaultPricing = priceMap.get("DEFAULT");
                        if (defaultPricing != null && defaultPricing.getDetails() != null) {
                            // Find the total amount pricing detail
                            Optional<PricingDetails> totalAmount = defaultPricing.getDetails().stream()
                                    .filter(detail -> Constants.TOTAL_AMOUNT_KEY.equals(detail.getKey()))
                                    .findFirst();
                            if (totalAmount.isPresent()) {
                                double amount = totalAmount.get().getAmount();
                                if (amount > 0) {
                                    long differenceInPriceFromBaseCombo = (long) (amount - baseComboFare);
                                    if (differenceInPriceFromBaseCombo != 0) {
                                        recommendedCombo.setComboText(getComboText(null, differenceInPriceFromBaseCombo));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Free stay for X children - child occupancy message
            // TODO: freeChildCount and freeChildText not available in RoomCombo

            // Flexi cancel add-on details
            // TODO: flexiCancelRoomDetail not available in RoomCombo

            recommendedCombos.add(recommendedCombo);
        }

        return recommendedCombos;
    }

    private RecommendedCombo buildBasicRecommendedCombo(List<RoomDetails> roomDetailsList, String comboName,
                                                        boolean isStaycationDeal, boolean baseCombo,
                                                        String funnelSource, OccupancyDetails occupancyDetails, String comboMealPlan) {
        RecommendedCombo recommendedCombo = new RecommendedCombo();
        recommendedCombo.setComboId(UUID.randomUUID().toString());
        recommendedCombo.setComboName(comboName);
        recommendedCombo.setRooms(roomDetailsList);
        recommendedCombo.setStaycationDeal(isStaycationDeal);
        recommendedCombo.setComboMealPlan(comboMealPlan);

        // Set room persuasions from first room
        if (CollectionUtils.isNotEmpty(roomDetailsList) && roomDetailsList.get(0).getRoomPersuasions() != null) {
            recommendedCombo.setRoomPersuasions(roomDetailsList.get(0).getRoomPersuasions());
        }

        // Build combo title based on rooms
        String comboTitle = buildComboTitle(recommendedCombo, funnelSource);
        recommendedCombo.setComboTitle(comboTitle);

        // Handle group booking specific text
        if (Utility.isGroupBookingFunnel(funnelSource) && occupancyDetails != null) {
            buildGroupBookingComboText(roomDetailsList, recommendedCombo, baseCombo, funnelSource, occupancyDetails);
        }

        return recommendedCombo;
    }

    private String buildComboTitle(RecommendedCombo recommendedCombo, String funnelSource) {
        // Variables for combo title generation
        boolean isSameCancellationPolicy = true;
        String cancellationType = null;
        int totalTariffs = 0;
        Set<String> mealInclusionCodeList = new HashSet<>();
        String roomCategoryText = Constants.SELLABLE_ROOM_TYPE;
        String mealTypeCodeText = "";

        // Analyze rooms to build combo title
        for (RoomDetails roomDetails : recommendedCombo.getRooms()) {
            if (CollectionUtils.isNotEmpty(roomDetails.getRatePlans())) {
                SelectRoomRatePlan firstRatePlan = roomDetails.getRatePlans().get(0);

                // Get cancellation type from first rate plan
                if (cancellationType == null && firstRatePlan.getCancellationPolicy() != null) {
                    cancellationType = firstRatePlan.getCancellationPolicy().getType().name();
                }

                // Check if all rate plans have same cancellation policy
                for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
                    if (ratePlan.getCancellationPolicy() != null) {
                        isSameCancellationPolicy = isSameCancellationPolicy && cancellationType != null && cancellationType.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name());
                    }

                    // Count tariffs
                    if (CollectionUtils.isNotEmpty(ratePlan.getTariffs())) {
                        totalTariffs += ratePlan.getTariffs().stream()
                                .mapToInt(tariff -> CollectionUtils.isNotEmpty(tariff.getRoomTariffs()) ? tariff.getRoomTariffs().size() : 0)
                                .sum();
                    }

                    // Collect meal inclusions
                    if (CollectionUtils.isNotEmpty(ratePlan.getInclusionsList())) {
                        for (BookedInclusion inclusion : ratePlan.getInclusionsList()) {
                            if (inclusion != null && "MEAL".equalsIgnoreCase(inclusion.getCategory()) &&
                                    StringUtils.isNotEmpty(inclusion.getInclusionCode())) {
                                mealInclusionCodeList.add(inclusion.getInclusionCode());
                            }
                        }
                    }

                    // Extract meal type from rate plan name
                    if (StringUtils.isNotEmpty(ratePlan.getName()) && StringUtils.isEmpty(mealTypeCodeText)) {
                        String[] arr = ratePlan.getName().split("\\|");
                        if (arr.length > 1) {
                            mealTypeCodeText = arr[1];
                        }
                        if (StringUtils.isEmpty(mealTypeCodeText)) {
                            arr = ratePlan.getName().split(WITH);
                            if (arr.length > 1) {
                                mealTypeCodeText = arr[1];
                            }
                        }
                    }
                }

                // Get sellable type
                if (StringUtils.isNotEmpty(roomDetails.getRoomCategoryText())) {
                    roomCategoryText = roomDetails.getRoomCategoryText();
                }
            }
        }

        // Build combo title
        return getComboTitle(isSameCancellationPolicy, cancellationType, totalTariffs,
                mealInclusionCodeList, "", roomCategoryText, mealTypeCodeText);
    }

    private String getComboTitle(boolean isSameCancellationPolicyInAllRatePlans, String cancellationType,
                                 int totalTariffs, Set<String> mealInclusionCodeList, String mealTypeCode,
                                 String roomCategoryText, String mealTypeCodeText) {
        try {
            // Creation of Polyglot key
            String comboTitle = "ROOMS_COMBO";
            if (totalTariffs == 1) {
                comboTitle = "SINGLE_" + comboTitle;
            } else {
                comboTitle = "PLURAL_" + comboTitle;
            }

            comboTitle += (isSameCancellationPolicyInAllRatePlans &&
                    "FC".equalsIgnoreCase(cancellationType)) ? "_FC" : "_NR";

            String roomTextFromPolyglot = polyglotService.getTranslatedData(comboTitle);
            comboTitle = roomTextFromPolyglot != null ? roomTextFromPolyglot : "";
            comboTitle = comboTitle.replace("{total_tarrifs}", String.valueOf(totalTariffs));
            comboTitle = comboTitle.replace("{sellable_type}", StringUtils.capitalize(roomCategoryText));

            mealTypeCode = mealInclusionCodeList.size() == 1 ?
                    mealInclusionCodeList.stream().findFirst().get() : "";
            mealTypeCode = (CollectionUtils.isNotEmpty(mealPlanCodeList) &&
                    mealPlanCodeList.contains(mealTypeCode)) ?
                    "COMBO_TITLE_" + mealTypeCode : mealTypeCode;

            String mealTextFromPolyglot = polyglotService.getTranslatedData(mealTypeCode);
            comboTitle += (StringUtils.isNotEmpty(mealTextFromPolyglot)) ?
                    " | " + mealTextFromPolyglot : "";

            String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
            if (!Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
                comboTitle += (StringUtils.isNotEmpty(mealTypeCodeText)) ?
                        " | " + mealTypeCodeText : "";
            }

            return comboTitle;
        } catch (Exception e) {
            LOGGER.warn("Error occurred in building Combo Title", e);
            return "Combo";
        }
    }

    private void buildGroupBookingComboText(List<RoomDetails> roomDetailsList, RecommendedCombo recommendedCombo,
                                            boolean baseCombo, String funnelSource, OccupancyDetails occupancyDetails) {
        // Group booking specific logic for combo text
        if (!Utility.isGroupBookingFunnel(funnelSource)) {
            return;
        }

        if (CollectionUtils.isNotEmpty(roomDetailsList)) {
            for (RoomDetails roomDetails : roomDetailsList) {
                if (roomDetails.getRoomSummary() != null && roomDetails.getRoomSummary().isTopRated()) {
                    // Add top rated persuasion if needed
                }
            }
        }
    }

    private String getComboText(String mealPlan, long priceDifference) {
        StringBuilder comboText = new StringBuilder();
        if (StringUtils.isNotBlank(mealPlan)) {
            comboText.append(mealPlan);
        }
        if (priceDifference != 0) {
            if (comboText.length() > 0) {
                comboText.append(" | ");
            }
            if (priceDifference > 0) {
                comboText.append("+");
            }
            comboText.append(priceDifference);
        }
        return comboText.toString();
    }

    protected abstract LoginPersuasion buildLoginPersuasion();

    protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);

    public GroupRatePlanFilter buildStaycationFilter(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation) {
        if (staycation) {
            GroupRatePlanFilter groupStaycationRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "GetawayDeals");
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "STAYCATION_DEALS".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(groupStaycationRatePlanFilter.getText(), "STAYCATION", "", selected);
            groupStaycationRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            return groupStaycationRatePlanFilter;
        }
        return null;
    }

    protected abstract PersuasionObject createTopRatedPersuasion();

    protected PersuasionObject createTopRatedPersuasionForMoblie() {
        PersuasionObject persuasionObject = new PersuasionObject();
        persuasionObject.setData(new ArrayList<>());
        persuasionObject.setPlaceholder(Constants.PLACEHOLDER_SELECT_TOP_R1);
        persuasionObject.setTemplate("IMAGE_TEXT_H");
        com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionData = new PersuasionData();
        PersuasionStyle style = new PersuasionStyle();
        style.setFontSize("SMALL");
        style.setTextColor("#b8860b");
        style.setFontType("B");
        style.setBorderColor("#b8860b");
        persuasionData.setStyle(style);
        persuasionData.setPersuasionType("PEITHO");
        persuasionData.setText(polyglotService.getTranslatedData(ConstantsTranslation.TOP_RATED));
        persuasionObject.setData(Arrays.asList(persuasionData));
        return persuasionObject;
    }

    private void sortRooms(List<RoomDetails> rooms)
    {
        if(CollectionUtils.isNotEmpty(rooms))
            rooms.sort((room1, room2) -> {
                if (isSellableTypePresent(room1) && isSellableTypePresent(room2) && !room1.getRatePlans().get(0).getSellableType().equalsIgnoreCase(room2.getRatePlans().get(0).getSellableType()))
                    return (room1.getRatePlans().get(0).getSellableType()).compareTo(room2.getRatePlans().get(0).getSellableType());
                return getAmountFromLabel(room1, Constants.TOTAL_AMOUNT_KEY) - getAmountFromLabel(room2, Constants.TOTAL_AMOUNT_KEY);
            });
    }

    private int getAmountFromLabel(RoomDetails roomDetails, String amountLabelKey) {
        try {
            String defaultPriceKey = roomDetails.getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey();
            return (int) roomDetails.getRatePlans().get(0).getTariffs().get(0).getPriceMap()
                    .get(defaultPriceKey).getDetails().stream().filter(e -> amountLabelKey.equalsIgnoreCase(e.getKey())).findFirst().get().getAmount();
        } catch (Exception ex) {
            LOGGER.error("error while sorting SearchResponse on the basis of sellableType", ex);
        }
        return 0;
    }

    private boolean isSellableTypePresent(RoomDetails room)
    {
        return CollectionUtils.isNotEmpty(room.getRatePlans()) && StringUtils.isNotBlank(room.getRatePlans().get(0).getSellableType());
    }

    public void sortBySellableType(SearchRoomsResponse searchRoomsResponse, String propertyType,Map<String,String>expDataMap) {
        try
        {
            /*CLEAN UP PART OF 15565*/
            if (MapUtils.isNotEmpty(expDataMap) && Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType)) {
                sortRooms(searchRoomsResponse.getOccupancyRooms());
                sortRooms(searchRoomsResponse.getExactRooms());
            }

        }
        catch (Exception ex) {
            LOGGER.error("error while sorting SearchResponse on the basis of sellableType", ex);
        }


    }

    private List<BookedInclusion> transformInclusions(List<com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion> inclusions,
                                                      RatePlan ratePlan, Map<String, String> expData, int ap, boolean isBlockPAH) {
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());

        List<BookedInclusion> bookedInclusions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inclusions)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion inclusion : inclusions) {
                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setCode(inclusion.getCode());
                bookedInclusion.setText(inclusion.getCode());
                bookedInclusion.setType(inclusion.getType());
                bookedInclusion.setSubText(inclusion.getValue());
                bookedInclusion.setAmount(inclusion.getAmount());
                bookedInclusion.setCategory(inclusion.getCategory());
                bookedInclusion.setIconUrl(INCLUSIONS_DEFAULT_DEFAULT_DOT_ICON_URL);
                bookedInclusion.setIUrl(INCLUSIONS_DEFAULT_DEFAULT_DOT_ICON_URL);
                if (StringUtils.isNotEmpty(inclusion.getIconType())) {
                    IconType iconType = IconType.valueOf(inclusion.getIconType());
                    bookedInclusion.setIconType(iconType);
                }
                bookedInclusion.setTrailingCtaText(inclusion.getTrailingCtaText());
                bookedInclusion.setTrailingCtaBottomSheet(buildTrailingCtaBottomSheet(inclusion.getTrailingCtaBottomSheet()));
                bookedInclusions.add(bookedInclusion);
            }
        }
        return bookedInclusions;//inclusionHelper.transformInclusions(inclusions, experimentDataMap, region);
    }

    private TrailingCtaBottomSheet buildTrailingCtaBottomSheet(com.gommt.hotels.orchestrator.detail.model.response.persuasion.TrailingCtaBottomSheet trailingCtaBottomSheet) {
        if (trailingCtaBottomSheet == null)
            return null;
        TrailingCtaBottomSheet bottomSheet = new TrailingCtaBottomSheet();
        bottomSheet.setHeading(trailingCtaBottomSheet.getHeading());
        bottomSheet.setSubHeading(trailingCtaBottomSheet.getSubHeading());
        List<SectionFeature> features = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.persuasion.SectionFeature sectionFeature : trailingCtaBottomSheet.getSectionFeatures()) {
            SectionFeature feature = new SectionFeature();
            feature.setText(sectionFeature.getText());
            feature.setIconUrl(sectionFeature.getIconUrl());
            features.add(feature);
        }
        bottomSheet.setSectionFeatures(features);
        return bottomSheet;
    }

    private void removeDuplicateData(SearchRoomsResponse searchRoomsResponse) {
        // Sale Campaign Details has been moved and now becomes a part of the cardData.
        // This code block is just for supporting sale campaign in older versions.
        boolean isSaleCampaignCardPresent = false;
        if (CollectionUtils.isNotEmpty(searchRoomsResponse.getCardData())) {
            isSaleCampaignCardPresent = searchRoomsResponse.getCardData().stream()
                    .anyMatch(cardData -> null != cardData && null != cardData.getCardInfo()
                            && OfferCard.SALE_CAMPAIGN.name().equalsIgnoreCase(cardData.getCardInfo().getId()));
        }
        if (isSaleCampaignCardPresent && MapUtils.isNotEmpty(searchRoomsResponse.getTemplateData())) {
            searchRoomsResponse.getTemplateData().remove(SALE_CAMPAIGN);
        }
    }

    private List<Tariff> buildTariffs(RatePlan ratePlan, Rooms room, String askedCurrency, Map<String, String> expData, String sellableType,
                                      int los, String funnelSource, CommonModifierResponse commonModifierResponse, HotelDetails hotelDetails) {

        List<Tariff> tariffs = new ArrayList<>();
        boolean groupBookingPrice = false; // TODO: Get from request context
        boolean myPartner = commonModifierResponse != null && commonModifierResponse.getExtendedUser() != null &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(),
                        commonModifierResponse.getExtendedUser().getAffiliateId());
        MarkUpDetails markUpDetails = null; // TODO: Get markup details if available
        boolean newPropertyOfferApplicable = commonModifierResponse != null &&
                utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey());

        com.gommt.hotels.orchestrator.detail.model.response.pricing.NoCostEmiDetails noCostEmiDetailForRootLevel = new com.gommt.hotels.orchestrator.detail.model.response.pricing.NoCostEmiDetails();

        if (ratePlan.getPrice() != null && CollectionUtils.isNotEmpty(ratePlan.getPrice().getPricePerOccupancy())) {
            // Check if this is an occupancy room (similar to OrchV2 logic)
            boolean isOccupancyRoom = isOccupancyRoomType(room);
            if (isOccupancyRoom) {
                // OCCUPANCY ROOMS: Create one tariff for each occupancy detail (current logic - CORRECT)
                for (com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails occupancyDetail : ratePlan.getPrice().getPricePerOccupancy()) {
                    List<OccupancyDetails> roomOccupancyDetail = new ArrayList();
                    roomOccupancyDetail.add(occupancyDetail);
                    Tariff tariff = getTariff(ratePlan, askedCurrency, expData, sellableType, los, funnelSource, hotelDetails, occupancyDetail, roomOccupancyDetail,
                            groupBookingPrice, myPartner, markUpDetails, noCostEmiDetailForRootLevel, newPropertyOfferApplicable);
                    tariffs.add(tariff);
                }
            } else {
                // NON-OCCUPANCY ROOMS (Exact, Recommended, Package): Create single tariff with multiple RoomTariff objects
                Tariff tariff = getTariff(ratePlan, askedCurrency, expData, sellableType, los, funnelSource, hotelDetails, ratePlan.getAvailDetail().getOccupancyDetails(), ratePlan.getPrice().getPricePerOccupancy(),
                        groupBookingPrice, myPartner, markUpDetails, noCostEmiDetailForRootLevel, newPropertyOfferApplicable);
                tariffs.add(tariff);
            }
        }

        return tariffs;
    }

    private Tariff getTariff(RatePlan ratePlan, String askedCurrency, Map<String, String> expData, String sellableType, int los, String funnelSource, HotelDetails hotelDetails,
                             OccupancyDetails occupancyDetail, List<OccupancyDetails> roomOccupancyDetails, boolean groupBookingPrice, boolean myPartner,
                             MarkUpDetails markUpDetails, com.gommt.hotels.orchestrator.detail.model.response.pricing.NoCostEmiDetails noCostEmiDetailForRootLevel, boolean newPropertyOfferApplicable) {
        Tariff tariff = new Tariff();

        // Set basic tariff properties
        tariff.setTariffCode(StringUtils.isNotEmpty(occupancyDetail.getRatePlanCode()) ? occupancyDetail.getRatePlanCode() : ratePlan.getCode());
        tariff.setMtKey(StringUtils.isNotEmpty(occupancyDetail.getRatePlanCode()) ? occupancyDetail.getRatePlanCode() : ratePlan.getCode());

        // Set occupancy details from the current occupancy detail
        RoomTariff occupancyDetails = new RoomTariff();
        occupancyDetails.setNumberOfAdults(occupancyDetail.getAdult());
        occupancyDetails.setNumberOfChildren(occupancyDetail.getChild());
        occupancyDetails.setRoomCount(occupancyDetail.getNumberOfRooms());

        // Set child ages if children present
        if (occupancyDetail.getChild() > 0 && CollectionUtils.isNotEmpty(occupancyDetail.getChildAges())) {
            occupancyDetails.setChildAges(occupancyDetail.getChildAges());
        }
        tariff.setOccupancydetails(occupancyDetails);

        // Set availability count
        if (ratePlan.getAvailDetail() != null) {
            tariff.setAvailCount(ratePlan.getAvailDetail().getCount());
        }

        // Set BNPL fields
        if (ratePlan.getBnplDetails() != null) {
            tariff.setBnplApplicable(ratePlan.getBnplDetails().isBnplApplicable());
            tariff.setBnplPersuasionMsg(StringUtils.isEmpty(ratePlan.getBnplDetails().getBnplPersuasionMsg()) ? null : ratePlan.getBnplDetails().getBnplPersuasionMsg());
        }


        // Create multiple RoomTariff objects from all occupancy details
        List<RoomTariff> roomTariffs = new ArrayList<>();
        for (OccupancyDetails roomOccupancy : roomOccupancyDetails) {
            RoomTariff roomTariff = new RoomTariff();
            roomTariffs.add(roomTariff);
            roomTariff.setNumberOfAdults(roomOccupancy.getAdult());
            roomTariff.setNumberOfChildren(roomOccupancy.getChild());
            roomTariff.setRoomCount(roomOccupancy.getNumberOfRooms() > 0 ? roomOccupancy.getNumberOfRooms() : null);
            if (roomOccupancy.getChild()>0) roomTariff.setChildAges(roomOccupancy.getChildAges());
            roomTariff.setDisplayPrice(roomOccupancy.getAmount());
        }
        tariff.setRoomTariffs(roomTariffs);

        // Use existing CommonResponseTransformer.getPriceMap method
        if (ratePlan.getPrice() != null) {
            boolean isCorp = ratePlan.getCorpMetaData() != null;
            String segmentId = ratePlan.getSegmentId() != null ? ratePlan.getSegmentId() : "";

            boolean isAltAccoHotel = hotelDetails.getHotelRateFlags().isAltAcco();
            boolean isHighSellingAltAcco = hotelDetails.getHotelRateFlags().isHighSellingAltAcco();
            Map<String, TotalPricing> priceMap = searchRoomsPriceHelper.getPriceMap(ratePlan.getPrice(), expData, occupancyDetail, askedCurrency, sellableType, los,
                    isCorp, segmentId, utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource), groupBookingPrice, myPartner, null);
            tariff.setPriceMap(priceMap);

            // Set default price key based on coupon code or default
            if (StringUtils.isNotBlank(ratePlan.getPrice().getCouponCode())) {
                tariff.setDefaultPriceKey(ratePlan.getPrice().getCouponCode());
            } else {
                tariff.setDefaultPriceKey("DEFAULT");
            }
        }
//        tariff.setVoucherCode();
//        tariff.setVoucherType(ratePlan.getDescription());
//        if(ratePlan.get  ().getDisplayPriceBreakDown() != null && ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null &&
//                CollectionUtils.isNotEmpty(ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList())) {
//            List<Voucher> voucherList = ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList();
//            tariff.setVoucherCode(voucherList.get(0).getVoucherCode());
//            tariff.setVoucherType(voucherList.get(0).getVoucherType());
//        }
        return tariff;
    }

    private List<PersuasionResponse> buildRatePlanPersuasions(SelectRoomRatePlan selectRatePlan, RatePlan ratePlan, String funnelSource, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel) {
        // Use the helper to build rate plan persuasions
        return searchRoomsPersuasionHelper.getRatePlanPersuasion(selectRatePlan, ratePlan, funnelSource, commonModifierResponse, isLuxeHotel);
    }

    private void setPackageRoomSpecificInfo(PackageRoomDetails roomDetails, Rooms packageRoom, String askedCurrency, String occasionType) {
        if (packageRoom == null) {
            return;
        }
        RoomType roomType  = RoomType.fromValue(packageRoom.getType());

        // Map OrchV2 PackageDetails to legacy PackageRoomType fields
        if (packageRoom.getPackageDetails() != null) {
            PackageDetails packageDetails = packageRoom.getPackageDetails();

            // Map available fields from OrchV2 PackageDetails to legacy fields
            if (StringUtils.isNotEmpty(packageDetails.getType())) {
                roomDetails.setType(packageDetails.getType());
            }

            if (StringUtils.isNotEmpty(packageDetails.getSpecialCardText())) {
                roomDetails.setCtaText(packageDetails.getSpecialCardText());
            }

            if (StringUtils.isNotEmpty(packageDetails.getPersuasionText())) {
                roomDetails.setRecommendText(packageDetails.getPersuasionText());
            }

            if (StringUtils.isNotEmpty(packageDetails.getText())) {
                roomDetails.setTitle(packageDetails.getText());
            }

            // Set description from available text fields
            if (StringUtils.isNotEmpty(packageDetails.getPersuasionText())) {
                roomDetails.setDescriptionText(packageDetails.getPersuasionText());
            }

            roomDetails.setPackageBenefits(buildPackageBenefitsText(packageDetails, askedCurrency, occasionType)); //TODO isNewDetailPageDesktop fix it
            roomDetails.setBorderGradient(getBorderGradient(roomType));
            roomDetails.setBgStyle(getBgStyleForPackage(roomType));
            //TODO Extract a method
            if (isSuperPackageRoom(packageRoom)) {
                roomDetails.setIconUrl(elitePackageIconUrlUpdated);
            } else if (isRecommendedRoom(packageRoom)) {
//                roomDetails.setIconUrl("https://promos.makemytrip.com/Growth/Images/B2C/MMT_Recommend_New.png");
                roomDetails.setIconUrl(packageDetails.getRecommendedRoomsIconUrl());
            }
            // Set header from available text fields
            if (StringUtils.isNotEmpty(packageDetails.getText())) {
                roomDetails.setHeader(packageDetails.getText());
            }
        } else {
            // Fallback to default values when PackageDetails is not available
            // Use polyglot service for translated text similar to legacy implementation
            roomDetails.setTitle(polyglotService.getTranslatedData("UNLOCKED_TITLE"));
            roomDetails.setRecommendText(polyglotService.getTranslatedData("RECOMMEND_TEXT"));
            roomDetails.setCtaText(polyglotService.getTranslatedData("RECOMMENDED_CTA_TEXT"));
            roomDetails.setType("SUPER_PACKAGE");
        }

        // Set filter details for package rooms
        setFilterDetails(roomDetails);
    }

    private String buildPackageBenefitsText(PackageDetails packageInclusionDetails, String askedCurrency, String occasionType) {
        String packageBenefitsText = polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_TEXT_GI);
        if(packageInclusionDetails!= null && StringUtils.isNotEmpty(packageInclusionDetails.getPackageBenefitsSlashedPrice()) && StringUtils.isNotEmpty(packageInclusionDetails.getBenefitsPrice())) {
            packageBenefitsText = packageBenefitsText.replace("{1}", String.valueOf((int)Double.parseDouble(packageInclusionDetails.getPackageBenefitsSlashedPrice())));
            packageBenefitsText = packageBenefitsText.replace("{2}", String.valueOf((int)Double.parseDouble(packageInclusionDetails.getBenefitsPrice())));
            return packageBenefitsText;
        }
        if(StringUtils.isNotBlank(occasionType)) {
            return polyglotService.getTranslatedData(occasionType.concat(UNDERSCORE).concat(ConstantsTranslation.OCCASION_PACKAGE_BENEFITS_TEXT));
        }
        return polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_DEFAULT_TEXT_GI);
    }

    private BorderGradient getBorderGradient(RoomType roomType) {
        BorderGradient borderGradient = new BorderGradient();
        if (RoomType.OCCASION_PACKAGE.equals(roomType)) {
            borderGradient.setStart("#d8d8d8");
            borderGradient.setEnd("#d8d8d8");
            borderGradient.setColor(new ArrayList<>());
            borderGradient.setDirection("horizontal");
        } else if (RoomType.SUPER_PACKAGE.equals(roomType)) {
            borderGradient.setStart("#8c671c");
            borderGradient.setEnd("#8c671c");
            borderGradient.setColor(Arrays.asList("#d3aa53", "#ffefcf", "#d3aa53"));
            borderGradient.setDirection("horizontal");
        } else if (RoomType.MMT_RECOMMEND.equals(roomType)) {
            /*borderGradient.setStart("#8c671c");
            borderGradient.setEnd("#8c671c");
            borderGradient.setColor(Arrays.asList("#d3aa53", "#ffefcf", "#d3aa53"));
            borderGradient.setDirection("horizontal");*/
        }
        return borderGradient;
    }

    private BgStyle getBgStyleForPackage(RoomType roomType) {
        BgStyle bgStyle = new BgStyle();
        if (RoomType.OCCASION_PACKAGE.equals(roomType)) {
            BorderGradient bgGradient = getBorderGradient(roomType);
            bgStyle.setStart(bgGradient.getStart());
            bgStyle.setCenter(bgGradient.getCenter());
            bgStyle.setEnd(bgGradient.getEnd());
            bgStyle.setDirection("diagonalBottom");
            bgStyle.setAngle("210deg");
        }
        return bgStyle;
    }

    private BgStyle getBgStyle(String start, String end, String direction) {
        BgStyle bgStyle = new BgStyle();
        bgStyle.setStart(start);
        bgStyle.setEnd(end);
        bgStyle.setDirection(direction);
        return bgStyle;
    }

    private String buildRatePlanName(RatePlan ratePlan, String sellableType, String listingType, Map<String, String> expData) {
        // Implement the same logic as utility.getRatePlanName but for OrchV2 models directly
        String ratePlanName = StringUtils.EMPTY;
        // Get meal code from OrchV2 meal plans
        String mealCode = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans()) && ratePlan.getMealPlans().get(0) != null) {
            mealCode = ratePlan.getMealPlans().get(0).getCode();
        }

        // Get cancellation policy type from OrchV2 cancellation policy
        String cancellationPolicyString = cancellationPolicyHelper.getCancellationPolicyType(ratePlan.getCancellationPolicy());

        // Adjust sellable type based on listing type
        if (Constants.SELLABLE_ENTIRE_TYPE.equalsIgnoreCase(listingType)) {
            sellableType = Constants.SELLABLE_ENTIRE_TYPE;
        } else {
            sellableType = StringUtils.isBlank(sellableType) ?
                    Constants.SELLABLE_ROOM_TYPE.toUpperCase() : sellableType.toUpperCase();
        }

        Map<String, Map<String, Map<String, String>>> ratePlanNameMapTemp;
        // we have created a different configuration for name and it is only used when we get RATE_PLAN_REDESIGN as true form pokus
        if(utility.isRatePlanRedesign(expData)){
            ratePlanNameMapTemp = ratePlanNameMapRedesign;
        }else {
            ratePlanNameMapTemp = ratePlanNameMap;
        }
        if(MapUtils.isNotEmpty(ratePlanNameMapTemp)){
            if (!ratePlanNameMapTemp.containsKey(mealCode)) {
                if (ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            } else {
                if (ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            }
        }

        LOGGER.debug("Built rate plan name: {}", ratePlanName);
        return utility.getTranslationFromPolyglot(ratePlanName);
    }

    private List<GroupRatePlanFilter> getFilters(List<RoomDetails> exactRooms, List<RoomDetails> occupancyRooms,
                                                 List<Filter> filterCriteria, int ap, boolean isblockPAH,
                                                 BNPLVariant bnplVariant, CommonModifierResponse commonModifierResponse,
                                                 boolean isLuxeHotel, Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap,
                                                 String propertyType) {
        if (CollectionUtils.isEmpty(exactRooms) && CollectionUtils.isEmpty(occupancyRooms)) {
            return null;
        }
        List<RoomDetails> rooms = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(exactRooms)) {
            rooms.addAll(exactRooms);
        }
        if (CollectionUtils.isNotEmpty(occupancyRooms)) {
            rooms.addAll(occupancyRooms);
        }
        List<RatePlanFilter> ratePlanFilters = new ArrayList<>();
        boolean pahFilter = false, freeCancellationFilter = false, freeBreakfastFilter = false, fczpnFilter = false, specialDeals = false, staycation = false;
        boolean twomealsFilter = false, allmealsFilter = false, partnerExclusiveFilter = false, packageRoomFilter = false;
        boolean mpBookNowFilterFor0 = false;
        boolean mpBookNowFilterFor1 = false;
        boolean allInclusiveFilter = false;
        int packageRatePlans = 0;
        boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        boolean isSellableTypeBedPresent = false, isSellableTypeRoomPresent = false;
        for (RoomDetails roomDetails : rooms) {
            for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
                if (CollectionUtils.isNotEmpty(ratePlan.getFilterCode())) {
                    if (ratePlan.getFilterCode().contains("FREE_CANCELLATION") || ratePlan.getFilterCode().contains("FREE_CANCELLATON")) {
                        freeCancellationFilter = true;
                    }
                    if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_0)) {
                        mpBookNowFilterFor0 = true;
                    }
                    if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_1)) {
                        mpBookNowFilterFor1 = true;
                    }
                    if (ratePlan.getFilterCode().contains("FREE_BREAKFAST"))
                        freeBreakfastFilter = true;
                    if (!myPartner) {
                        if (ratePlan.getFilterCode().contains("PAH")) {
                            if (isblockPAH && ap < 5) {
                                fczpnFilter = true;
                            } else {
                                pahFilter = true;
                            }
                        }
                        if (ratePlan.getFilterCode().contains("FCZPN"))
                            fczpnFilter = true;
                        if (ratePlan.getFilterCode().contains("SPECIALDEALS"))
                            specialDeals = true;
                        if (ratePlan.getFilterCode().contains("STAYCATION"))
                            staycation = true;
                    }
                    if (ratePlan.getFilterCode().contains("TWO_MEAL_AVAIL"))
                        twomealsFilter = true;
                    if (ratePlan.getFilterCode().contains("ALL_MEAL_AVAIL"))
                        allmealsFilter = true;
                    if (ratePlan.getFilterCode().contains(PACKAGE_RATE)) {
                        packageRoomFilter = true;
                        packageRatePlans++;
                    }
                    if (ratePlan.getFilterCode().contains("CTA_RATES_AVAIL"))
                        partnerExclusiveFilter = true;
                    if (ratePlan.getFilterCode().contains(ALL_INCLUSIVE))
                        allInclusiveFilter = true;
                }
                if (Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType) && SELLABLE_ROOM_TYPE.equalsIgnoreCase(ratePlan.getSellableType())) {
                    isSellableTypeRoomPresent = true;
                }
                if (PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType) && SELLABLE_BED_TYPE.equalsIgnoreCase(ratePlan.getSellableType())) {
                    isSellableTypeBedPresent = true;
                }
            }
        }

        List<GroupRatePlanFilter> groupRatePlanFilterList = new ArrayList<>();

        if (isSellableTypeRoomPresent && isSellableTypeBedPresent && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
            GroupRatePlanFilter groupFCRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "PrivateRoom");
            //Dynamic Private Room filter is available in listing page and will be a continuity filter.
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> PRIVATE_ROOM.equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("PRIVATE_ROOM_FILTER"), "PRIVATE_ROOMS", "", selected);
            groupFCRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            groupRatePlanFilterList.add(groupFCRatePlanFilter);
        }

        if (utility.isSPKGExperimentOn(commonModifierResponse.getExpDataMap()) && packageRatePlans > 1 && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
            GroupRatePlanFilter groupElitePackageRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "ElitePackage");
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> Constants.PACKAGE_RATE.equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter("Elite Package", Constants.PACKAGE_RATE, "", selected);
            groupElitePackageRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            groupRatePlanFilterList.add(groupElitePackageRatePlanFilter);

        }

        if (partnerExclusiveFilter) {
            boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a -> "CTA_RATES_AVAIL".equalsIgnoreCase(a.getFilterValue()) );
            RatePlanFilter ratePlanFilter = new RatePlanFilter("Partner Exclusive Rate", "CTA_RATES_AVAIL", "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }

        if (allInclusiveFilter && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
            GroupRatePlanFilter groupFCRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "AllInclusive");
            boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"ALL_INCLUSIVE".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter("All Inclusive Rates", "ALL_INCLUSIVE", "", selected);
            groupFCRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            groupRatePlanFilterList.add(groupFCRatePlanFilter);
        }

        if (freeCancellationFilter && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
            GroupRatePlanFilter groupFCRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "FreeCancellation");
            boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FREE_CANCELLATION_FILTER"), "FREE_CANCELLATION", "", selected);
            groupFCRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            groupRatePlanFilterList.add(groupFCRatePlanFilter);
        }
        if(mpBookNowFilterFor0) {
            boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_0), BOOK_NOW_AT_0, "", selected);
            ratePlanFilters.add(mpBookNowRatePlanFilter);
        }
        if(mpBookNowFilterFor1) {
            boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_1), BOOK_NOW_AT_1, "", selected);
            ratePlanFilters.add(mpBookNowRatePlanFilter);
        }

        if ((freeBreakfastFilter || twomealsFilter || allmealsFilter) && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
            GroupRatePlanFilter groupMealRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "MealOptions");
            if (freeBreakfastFilter) {
                boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "BREAKFAST_AVAIL".equalsIgnoreCase(a.getFilterValue()) || "BREAKFAST".equalsIgnoreCase(a.getFilterValue()));
                RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("BREAKFAST_INCLUDED_FILTER_GI"), "FREE_BREAKFAST", "", selected);
                groupMealRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            }
            if (twomealsFilter) {
                boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "TWO_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()));
                RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner ? "BUSINESS_TWO_MEAL_AVAIL_TITLE_MYPARTNER" : "BREAKFAST_LUNCH_DINNER_INCLUDED")), "TWO_MEAL_AVAIL", "", selected);
                groupMealRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            }
            if (allmealsFilter) {
                boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "ALL_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()));
                RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner ? "BUSINESS_ALL_MEAL_AVAIL_TITLE_MYPARTNER" : "ALL_MEALS_INCLUDED")), "ALL_MEAL_AVAIL", "", selected);
                groupMealRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
            }
            groupRatePlanFilterList.add(groupMealRatePlanFilter);
        }
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        if(!myPartner) {
            if ((pahFilter || fczpnFilter) && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
                GroupRatePlanFilter groupPayModeRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "PayOptions");
                if (pahFilter) {
                    boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "PAH".equalsIgnoreCase(a.getFilterValue()) || "PAH_AVAIL".equalsIgnoreCase(a.getFilterValue()));
                    RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("PAY_AT_HOTEL_FILTER"), "PAH", "", selected);
                    groupPayModeRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
                }
                if (fczpnFilter) {
                    boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "PAY_LATER".equalsIgnoreCase(a.getFilterValue()));
                    RatePlanFilter ratePlanFilter;
                    if (isblockPAH) {
                        ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("PAY_LATER_FILTER"), "FCZPN", "", selected);
                    } else {
                        //GCC -> Free Cancellation Zero Payment Now
                        if (AE.equalsIgnoreCase(region)) {
                            ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER"), "FCZPN", "", selected);
                        }
                        //BNPL_AT_1 -> Free Cancellation - Book @ ₹ 1
                        else if (BNPLVariant.BNPL_AT_1.equals(bnplVariant)) {
                            ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER_BNPL_NEW_VARIANT"), "FCZPN", "", selected);
                        }
                        //BNPL_AT_0 -> Book with 0 Payment
                        else if (BNPLVariant.BNPL_AT_0.equals(bnplVariant)) {
                            ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER_BNPL_ZERO_VARIANT_GI"), "FCZPN", "", selected);
                        } else {
                            ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER"), "FCZPN", "", selected);
                        }
                    }
                    groupPayModeRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
                }
                groupRatePlanFilterList.add(groupPayModeRatePlanFilter);
            }
            if (specialDeals) {
                RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("SPECIAL_DEALS_FILTER"), "SPECIALDEALS", "", false);
                ratePlanFilters.add(ratePlanFilter);
            }

            GroupRatePlanFilter filterForDevice = buildGroupFilterForDevice(groupRatePlanFilterConfMap, filterCriteria, staycation);
            if (staycation && filterForDevice != null) {
                groupRatePlanFilterList.add(filterForDevice);
            }
        }
//Remove MMT PACKAGE and MMT LUXE PACKAGE node value and set to PACKAGE_RATE, confirm with the client LUXE_PACKAGE and NON_LUXE_PACKAGE not required
        if (packageRoomFilter) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(
                    a -> Constants.PACKAGE_RATE.equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter;
            if (isLuxeHotel) {
                ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_LUXE_PACKAGE_TEXT),
                        Constants.PACKAGE_RATE, "", selected);
            } else {
                ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_NON_LUXE_PACKAGE_TEXT),
                        Constants.PACKAGE_RATE, "", selected);
            }
            ratePlanFilter.setOrder(1);
            ratePlanFilters.add(ratePlanFilter);
        }
        if (CollectionUtils.isEmpty(groupRatePlanFilterList))
            return null;
        for (GroupRatePlanFilter groupRatePlanFilter : groupRatePlanFilterList) {
            for (RatePlanFilter filter : groupRatePlanFilter.getRatePlanFilterList()) {
                if(!"PRIVATE_ROOMS".equalsIgnoreCase(filter.getCode())) {
                    if (CollectionUtils.isNotEmpty(occupancyRooms)) {
                        filter.setSelected(false);
                    }
                }
            }
            // If there is only a single ratePlan in group, don't show it as a group
            // rather than show it as a single filter.
            if (CollectionUtils.isNotEmpty(groupRatePlanFilter.getRatePlanFilterList())
                    && groupRatePlanFilter.getRatePlanFilterList().size() == 1) {
                groupRatePlanFilter.setGroupApplicable(false);
                groupRatePlanFilter.setText(groupRatePlanFilter.getRatePlanFilterList().get(0).getTitle());
            }
        }

        return groupRatePlanFilterList;
    }

    public GroupRatePlanFilter getGroupRatePlanFilter(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, String key) {
        GroupRatePlanFilter groupMealRatePlanFilter = new GroupRatePlanFilter();
        groupMealRatePlanFilter.setRatePlanFilterList(new ArrayList<>());
        groupMealRatePlanFilter.setText(groupRatePlanFilterConfMap.get(key).getText());
        groupMealRatePlanFilter.setImage(groupRatePlanFilterConfMap.get(key).getImage());
        groupMealRatePlanFilter.setGroupApplicable(groupRatePlanFilterConfMap.get(key).isGroupApplicable());
        return groupMealRatePlanFilter;
    }

    private List<GroupRatePlanFilter> getCardFilters(List<RoomDetails> exactRooms) {
        List<RoomDetails> rooms = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(exactRooms)) {
            rooms.addAll(exactRooms);
        }
        List<GroupRatePlanFilter> groupRatePlanFilters = new ArrayList<>();
        RoomUpsellConfig roomUpsellConfig = mobConfigHelper.getRoomUpsellConfig();

        if (roomUpsellConfig != null && roomUpsellConfig.roomViewsFilterToggle != null) {
            for(RoomViewFilterConfig roomViewFilterConfig: roomUpsellConfig.getRoomViewsFilterToggle()) {

                if (SelectRoomCardFilterType.AMENITIES.getName().equalsIgnoreCase(roomViewFilterConfig.getType())) {
                    Optional<RoomDetails> filteredRoomOptional = rooms.stream().filter( roomItemDetail -> isAmenityAvailable(roomItemDetail, roomViewFilterConfig)).findFirst();
                    if (filteredRoomOptional.isPresent()) {
                        RoomDetails roomDetails = filteredRoomOptional.get();
                        groupRatePlanFilters.add(getRoomUpsellGroupFilter(roomDetails, roomViewFilterConfig));
                        break;
                    }

                } else if (SelectRoomCardFilterType.ROOM_VIEW.getName().equalsIgnoreCase(roomViewFilterConfig.getType())) {
                    Optional<RoomDetails> filteredRoomOptional = rooms.stream().filter( roomItemDetail -> Utility.convertToUnderscoreCaps(roomItemDetail.getRoomViewName()).equalsIgnoreCase(roomViewFilterConfig.getFilterCode())).findFirst();
                    if (filteredRoomOptional.isPresent()) {
                        RoomDetails roomDetails = filteredRoomOptional.get();
                        groupRatePlanFilters.add(getRoomUpsellGroupFilter(roomDetails, roomViewFilterConfig));
                        break;
                    }
                }
            }
        }


        return groupRatePlanFilters;
    }

    private GroupRatePlanFilter getRoomUpsellGroupFilter(RoomDetails roomDetails, RoomViewFilterConfig roomViewFilterConfig) {
        GroupRatePlanFilter roomUpsellGroupFilter = new GroupRatePlanFilter();
        RatePlanFilter ratePlanFilter = new RatePlanFilter(roomDetails.getRoomViewName(), roomViewFilterConfig.getFilterCode(), roomViewFilterConfig.getIcon_URL(), false);
        List<RatePlanFilter> ratePlanFilterList = new ArrayList<>();
        ratePlanFilterList.add(ratePlanFilter);
        roomUpsellGroupFilter.setRatePlanFilterList(ratePlanFilterList);
        roomUpsellGroupFilter.setText(roomViewFilterConfig.getTitle());//roomViewsFilterToggle.getFilterText()
        roomUpsellGroupFilter.setImage(roomViewFilterConfig.getIcon_URL());
        roomUpsellGroupFilter.setGroupApplicable(false);
        return roomUpsellGroupFilter;
    }

    private Boolean isAmenityAvailable(RoomDetails roomDetails, RoomViewFilterConfig roomViewFilterConfig) {
        if (roomDetails.getAmenities() == null) {
            return false;
        }
        Optional<FacilityGroup> facilityGroupOptional = roomDetails.getAmenities().stream().filter(amenityItem -> {
            return Objects.equals(amenityItem.getId(), "SIGNATURE_AMENITIES");
        }).findFirst();
        if (facilityGroupOptional.isPresent()) {
            FacilityGroup facilityGroup = facilityGroupOptional.get();
            return facilityGroup.getFacilities().stream().anyMatch(facilityItem -> Utility.convertToUnderscoreCaps(facilityItem.getAttributeName()).equalsIgnoreCase(roomViewFilterConfig.getFilterCode()));
        }

        return false;
    }

    protected abstract GroupRatePlanFilter buildGroupFilterForDevice(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation);

    public Pair<Boolean, Boolean> addSellableLabelFromSellableType(SearchRoomsResponse searchRoomsResponse) {
        if (searchRoomsResponse == null) {
            return new ImmutablePair<>(false, false);
        }
        try {
            boolean bedAvailable = false, roomAvailable = false;
            Pair<Boolean, Boolean> isBedAndRoomAvailable = null;
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
                for (RecommendedCombo recommendedCombo : searchRoomsResponse.getRecommendedCombos()) {
                    if (recommendedCombo != null) {
                        isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(recommendedCombo.getRooms());
                        bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
                        roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
                    }
                }
            }
            isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getOccupancyRooms());
            bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
            roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
            isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getExactRooms());
            bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
            roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
            return new ImmutablePair<>(bedAvailable, roomAvailable);
        } catch (Exception ex) {
            LOGGER.error("Error while adding sellableLabel in Occupancy and exact Rooms", ex);
        }
        return new ImmutablePair<>(false, false);

    }


    private Pair<Boolean, Boolean> addSellableLabelAndRoomTypeFilterCode(List<RoomDetails> rooms) {
        boolean sellableTypeBed = false, sellableTypeRoom = false;
        if (CollectionUtils.isNotEmpty(rooms))
            for (RoomDetails room : rooms) {
                List<String> filterCode = new ArrayList<>();
                String sellableType = (CollectionUtils.isNotEmpty(room.getRatePlans()) ? room.getRatePlans().get(0).getSellableType() : null);
                if (Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(sellableType)) {
                    room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.BEDS_SELLABLE_LABEL));
                    filterCode.add(BEDS_SELLABLE_TYPE_FILTER_CODE);
                    sellableTypeBed = true;
                } else if (Constants.SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableType)) {
                    room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_SELLABLE_LABEL));
                    filterCode.add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
                    //Add private room filter code
                    List<SelectRoomRatePlan> roomWiseRatePlans = room.getRatePlans();
                    if (CollectionUtils.isNotEmpty(roomWiseRatePlans)) {
                        roomWiseRatePlans.stream()
                                .filter(Objects::nonNull)
                                .forEach(ratePlan -> {
                                    if(ratePlan.getFilterCode() == null) {
                                        ratePlan.setFilterCode(new ArrayList<>());
                                    }
                                    if(!ratePlan.getFilterCode().contains(ROOMS_SELLABLE_TYPE_FILTER_CODE)) {
                                        ratePlan.getFilterCode().add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
                                    }
                                });
                    }
                    sellableTypeRoom = true;
                }
                room.setFilterCode(filterCode);
            }
        return new ImmutablePair<>(sellableTypeBed, sellableTypeRoom);
    }

    private void linkRatePlans(List<SelectRoomRatePlan> ratePlans) {
        try {

            List<SelectRoomRatePlan> ratePlansCopy = new ArrayList<>(ratePlans);

            for (SelectRoomRatePlan ratePlan : ratePlansCopy) {
                List<LinkedRatePlan> linkedRatePlans = new ArrayList<>();
                UpsellRatePlan upsellRatePlan = ratePlan.getUpsellRatePlan();
                if (CollectionUtils.isNotEmpty(ratePlan.getChildLinkedRates())) {
                    for (LinkedRate childLinkedRate : ratePlan.getChildLinkedRates()) {
                        if (LINKEDRATE_FCNR.equalsIgnoreCase(childLinkedRate.getType())) {
                            Iterator<SelectRoomRatePlan> iterator = ratePlans.iterator();
                            while (iterator.hasNext()) {
                                SelectRoomRatePlan selectRoomRatePlan = iterator.next();
                                if (selectRoomRatePlan.getRpc().equalsIgnoreCase(childLinkedRate.getPricingKey())) {
                                    LinkedRatePlan linkedRatePlan = new LinkedRatePlan();
                                    linkedRatePlan.setRatePlan(selectRoomRatePlan);
                                    RatePlanData ratePlanData = createRatePlanData();
                                    ratePlanData.setRatePlanName(selectRoomRatePlan.getLinkedRatePlanName());
                                    linkedRatePlan.setData(ratePlanData);
                                    linkedRatePlans.add(linkedRatePlan);
                                    iterator.remove();
                                }
                            }
                        }
                    }
                }
                ratePlan.setUpsellRatePlan(upsellRatePlan);
                ratePlan.setLinkedRatePlans(linkedRatePlans);
            }
        } catch (Exception ex){
            LOGGER.error("Error in linking rate plans", ex);
        }
    }

    private RatePlanData createRatePlanData() {
        RatePlanData ratePlanData = new RatePlanData();
        ratePlanData.setTitle(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_TITLE));
        ratePlanData.setDescription(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_DESCRIPTION));
        ratePlanData.setCtaText(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_CTATEXT));
        ratePlanData.setBottomSheetFooterText(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_BOTTOM_SHEET_FOOTER_TEXT));
        return ratePlanData;
    }

    /**
     * Builds parent linked rates from orchestrator V2 LinkedRate model to client gateway LinkedRate model
     */
    private void buildParentLinkedRates(SelectRoomRatePlan ratePlan, List<com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate> linkedRates) {
        if(CollectionUtils.isNotEmpty(linkedRates)){
            List<LinkedRate> parentLinkedRates = new ArrayList<>();
            for(com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate orchLinkedRate : linkedRates){
                LinkedRate cgLinkedRate = new LinkedRate();
                cgLinkedRate.setType(orchLinkedRate.getType());
                cgLinkedRate.setPricingKey(orchLinkedRate.getPricingKey());
                parentLinkedRates.add(cgLinkedRate);
            }
            ratePlan.setParentLinkedRates(parentLinkedRates);
        }
    }

    /**
     * Builds child linked rates from orchestrator V2 LinkedRate model to client gateway LinkedRate model
     */
    private void buildChildLinkedRates(SelectRoomRatePlan ratePlan, List<com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate> linkedRates) {
        if(CollectionUtils.isNotEmpty(linkedRates)){
            List<LinkedRate> childLinkedRates = new ArrayList<>();
            for(com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate orchLinkedRate : linkedRates){
                LinkedRate cgLinkedRate = new LinkedRate();
                cgLinkedRate.setType(orchLinkedRate.getType());
                cgLinkedRate.setPricingKey(orchLinkedRate.getPricingKey());
                childLinkedRates.add(cgLinkedRate);
            }
            ratePlan.setChildLinkedRates(childLinkedRates);
        }
    }

    /**
     * Sets the canTranslate flag based on supplier code configuration
     */
    public static void setCanTranslateFlag(SelectRoomRatePlan ratePlan, List<String> translateEnabledSupplierCodes, String supplierCode) {
        if (ratePlan == null) {
            return;
        }

        if (CollectionUtils.isNotEmpty(translateEnabledSupplierCodes) &&
                StringUtils.isNotEmpty(supplierCode) &&
                translateEnabledSupplierCodes.contains(supplierCode)) {
            ratePlan.setCanTranslate(true);
        } else {
            ratePlan.setCanTranslate(false);
        }
    }

    private boolean isOccupancyRoomType(Rooms room) {
        // In CG layer, we need to identify occupancy rooms
        return ComboType.OCCUPANCY_ROOM.name().equalsIgnoreCase(room.getType());
    }

    private com.mmt.hotels.clientgateway.response.rooms.HotelDetails buildHotelDetails(com.gommt.hotels.orchestrator.detail.model.response.HotelDetails hotelDetails, String funnelSource, String detailDeepLinkUrl, String client, Map<String, String>expDataMap, LocationDetails locusData, boolean isPackageRoomPresent) {
        if (hotelDetails != null) {
            com.mmt.hotels.clientgateway.response.rooms.HotelDetails cgHotelDetails = new com.mmt.hotels.clientgateway.response.rooms.HotelDetails();
            if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)) {
                cgHotelDetails.setHotelName(hotelDetails.getName());
                cgHotelDetails.setHotelIcon(hotelDetails.getIcon());

                if(detailDeepLinkUrl!=null){

                    String[] parts = detailDeepLinkUrl.split(Constants.URL_PARAM_BASE_SPLITTER);
                    if (parts.length == 2) {
                        String base = parts[0];
                        String params = parts[1];

                        detailDeepLinkUrl =  base + Constants.QUE_MARK + Stream.of(params.split(Constants.AMP))
                                .map(p -> p.split(Constants.EQUI))
                                .filter(p -> !p[0].equals(Constants.CHECK_AVAILBILITY_PARAM))
                                .map(p -> String.join(Constants.EQUI, p))
                                .collect(Collectors.joining(Constants.AMP));

                        if(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)){
                            detailDeepLinkUrl+= Constants.AMP+ Constants.FUNNEL_SOURCE_HOMESTAY.toLowerCase()+  Constants.EQUI + "true";
                        }
                    }
                }
//                cgHotelDetails.setUrl(detailDeepLinkUrl); // not used by client
            }
            cgHotelDetails.setHotelId(hotelDetails.getId());
            cgHotelDetails.setSupplierId("v15");
//            cgHotelDetails.setVoyagerCityId(hotelDetails.getVoyagerCityId());
//            cgHotelDetails.setLmr(hotelDetails.isLmr());
            cgHotelDetails.setMergedPropType(hotelDetails.getPropertyType());
            if (locusData != null){
                LocusData locusDataRes = new LocusData();
                locusDataRes.setLocusId(locusData.getCityId());
                locusDataRes.setLocusType(locusData.getType());
                locusDataRes.setLocusName(locusData.getCityName());
                cgHotelDetails.setLocusData(locusDataRes);
            }
            cgHotelDetails.setCountryCode(hotelDetails.getLocation() != null ? hotelDetails.getLocation().getCountryId() : "");
            if (utility.isSPKGExperimentOn(expDataMap) && isPackageRoomPresent) {
                cgHotelDetails.setPackageTagUrl(elitePackageIconUrl);
            }
            cgHotelDetails.setEntireProperty(LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelDetails.getListingType()));
            cgHotelDetails.setAltAcco(hotelDetails.getHotelRateFlags().isAltAcco());

            return cgHotelDetails;
        }
        return null;
    }

    private FeatureFlags getFeatureFlags(HotelDetails hotelDetails, List<RoomDetails> rooms, LinkedHashMap<String, String> expDataMap, CommonModifierResponse commonModifierResponse) {
        if (hotelDetails == null)
            return null;
        HotelRateFlags hotelRateFlags = hotelDetails.getHotelRateFlags();
        FeatureFlags featureFlags = new FeatureFlags();
        featureFlags.setBestPriceGuaranteed(hotelRateFlags.isBestPriceGuaranteed());
        featureFlags.setBnpl(hotelRateFlags.isBnplAvailable());
        featureFlags.setBnplBaseAmount(hotelDetails.getBnplBaseAmount());
        featureFlags.setFirstTimeUser(hotelRateFlags.isFirstTimeUser());
        featureFlags.setFreeCancellation(hotelRateFlags.isFreeCancellationAvailable());
        featureFlags.setPahAvailable(hotelRateFlags.isPahAvailable());
        featureFlags.setPahTariffAvailable(hotelRateFlags.isPahAvailable());
        if(commonModifierResponse != null)
            featureFlags.setSkipSelectRoom(commonModifierResponse.isSkipRoomSelectEnabled());
        featureFlags.setGroupBookingPrice(hotelRateFlags.isGroupBookingPrice());
        featureFlags.setMaskedPrice(hotelRateFlags.isMaskedPrice());
        featureFlags.setOptimisedSelection(MapUtils.isNotEmpty(expDataMap) && Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType()) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP)));
        if (Constants.DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
            featureFlags.setPwaDetailSelectMerge(hotelRateFlags.isDetailSelectMerge());
        }
        featureFlags.setPahWalletApplicable(hotelRateFlags.isPahWalletApplicable());
        featureFlags.setRequestToBook(hotelRateFlags.isRequestToBook());
        featureFlags.setRtbPreApproved(hotelRateFlags.isRtbPreApproved());
        featureFlags.setRtbAutoCharge(hotelRateFlags.isRtbAutoCharge());
        com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits blackDeal = hotelDetails.getDealBenefits().stream()
                .filter(deal -> deal != null && BenefitType.BLACK_BENEFITS.equals(deal.getBenefitType()))
                .findFirst()
                .orElse(null);
        if (blackDeal != null && blackDeal.getLoyaltyDetails() != null) {
            featureFlags.setIsGoTribe3_0(blackDeal.getLoyaltyDetails().isGoTribe3());
        }
        Set<String> payModes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(rooms)) {
            for (RoomDetails roomDetail : rooms) {
                if (CollectionUtils.isNotEmpty(roomDetail.getRatePlans())) {
                    for (SelectRoomRatePlan ratePlan : roomDetail.getRatePlans()) {
                        payModes.add(ratePlan.getPayMode());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(payModes))
                featureFlags.setPayModes(payModes);
        }
//        if (!utility.isExperimentTrue(expDataMap, Constants.GROUP_FUNNEL_ENHANCEMENT_EXP))
//            featureFlags.setGroupBookingPrice(hotelRateFlags.isGroupBookingPrice());
//        else
//            featureFlags.setGroupBookingPrice(false); // Explicit marking this as false, In case of experiment is groupFunnelEnhancement enabled
//        featureFlags.setMaskedPrice(hotelRateFlags.isMaskedPrice());
//        featureFlags.setOptimisedSelection(utility.isOHSExpEnable(hotelDetails.getPropertyType(), expDataMap));
//        featureFlags.setMyPartnerMoveToTdsTaxStructure(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE)));
        /*
        //TODO Add for mypartner
        if(hotelDetails.getAdditionalDetails().getBnplBaseAmount()!=null) {
            featureFlags.setHotelFareHold(true);
        }*/
        return featureFlags;
    }

    private ResponseContextDetail getContextDetails(HotelDetails hotelDetails) {
        ResponseContextDetail responseContextDetail = new ResponseContextDetail();
        responseContextDetail.setCurrency(hotelDetails.getCurrencyCode());
        responseContextDetail.setMmtHotelCategory(hotelDetails.getHotelCategory());
        return responseContextDetail;
    }

    /**
     * Build RoomAdvisory from rooms' RoomInfo containing bedAdvisoryText
     * Similar to the legacy buildRoomAdvisoryInfo method but extracting data from OrchV2 rooms
     */
    private RoomAdvisory buildRoomAdvisoryFromRooms(List<Rooms> rooms) {
        if (CollectionUtils.isEmpty(rooms)) {
            return null;
        }

        RoomAdvisory roomAdvisory = new RoomAdvisory();
        List<String> roomCodeIds = new ArrayList<>();
        String bedAdvisoryText = null;

        // Iterate through rooms to extract bedAdvisoryText from RoomInfo
        for (Rooms room : rooms) {
            if (room.getRoomInfo() != null) {
                RoomInfo roomInfo = room.getRoomInfo();
                
                // Check if room has bedAdvisoryText - assuming it's a method on RoomInfo
                if (StringUtils.isNotEmpty(roomInfo.getBedAdvisoryText())) {
                    bedAdvisoryText = roomInfo.getBedAdvisoryText();
                    roomCodeIds.add(room.getCode());
                }
            }
        }

        // Only create RoomAdvisory if we have bedAdvisoryText
        if (StringUtils.isNotEmpty(bedAdvisoryText)) {
            roomAdvisory.setBedAdvisoryText(bedAdvisoryText);
            if (CollectionUtils.isNotEmpty(roomCodeIds)) {
//                roomAdvisory.setRoomCodeIds(roomCodeIds); //TODO need to add check for EXP, only add roomcodeids then
                roomAdvisory.setRoomCodeIds(null);
            }
            return roomAdvisory;
        }

        return null;
    }

    /**
     * Builds UpsellRatePlan from orchestrator V2 LinkedRate model
     */
    private UpsellRatePlan buildUpsellRatePlan(List<com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate> linkedRates,
                                             Map<String, com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan> rpcRatePlanMap,
                                             String sellableType, String listingType, Map<String, String> expData, int ap) {
        if (CollectionUtils.isEmpty(linkedRates)) {
            return null;
        }

        UpsellRatePlan upsellRatePlan = new UpsellRatePlan();
        upsellRatePlan.setHeading(polyglotService.getSafeTranslatedData(ConstantsTranslation.HOTEL_UPSELL_CARD_TITLE));
        upsellRatePlan.setSubHeading(polyglotService.getSafeTranslatedData(ConstantsTranslation.HOTEL_UPSELL_CARD_SUBTITLE));

        List<UpsellRatePlanOptions> upsellOptions = new ArrayList<>();

        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate linkedRate : linkedRates) {
            List<com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRateDetail> linkedRateDetails = linkedRate.getLinkedRateDetail();
            if (linkedRateDetails == null) continue;
            
            UpsellRatePlanOptions upsellRatePlanOptions = new UpsellRatePlanOptions();
            StringBuilder titleStrBuilder = new StringBuilder();
            StringBuilder subTitleBuilder = new StringBuilder();

            for (int i = 0; i < linkedRateDetails.size(); i++) {
                com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRateDetail linkedRateDetail = linkedRateDetails.get(i);

                // Build title from LinkedRateValue (simplified version for now)
                String titleDefaultString = "";
                if (linkedRateDetail.getLinkedRateValue() != null) {
                    titleDefaultString = buildTitleMealTitleDefaultStringForOrch(linkedRateDetail.getLinkedRateValue());
                }
                
                // Build subtitle from LinkedRateSubType (simplified version for now)
                String subtitleDefaultString = buildUpSellSubTitleForOrch(linkedRateDetail, rpcRatePlanMap, linkedRate.getPricingKey(), ap);

                if (StringUtils.isNotEmpty(titleDefaultString)) {
                    if (StringUtils.isNotEmpty(titleStrBuilder.toString())) {
                        titleStrBuilder.append("|");
                    }
                    titleStrBuilder.append(titleDefaultString);
                }

                if (StringUtils.isNotEmpty(subtitleDefaultString)) {
                    if (StringUtils.isNotEmpty(subTitleBuilder.toString())) {
                        subTitleBuilder.append("|");
                    }
                    subTitleBuilder.append(subtitleDefaultString);
                }
                
                // Set sheet title (simplified version for now)
                if (linkedRateDetail.getLinkedRateValue() != null) {
                    upsellRatePlanOptions.setSheetTitle(buildUpsellSheetTitleForOrch(linkedRateDetail.getLinkedRateValue()));
                }
            }

            upsellRatePlanOptions.setRpc(linkedRate.getPricingKey());
            upsellRatePlanOptions.setPriceLabel(polyglotService.getSafeTranslatedData(ConstantsTranslation.MEAL_SHEET_PRICE_LABEL));
            upsellRatePlanOptions.setSubTitle(subTitleBuilder.toString());
            upsellRatePlanOptions.setTitle(titleStrBuilder.toString());
            upsellOptions.add(upsellRatePlanOptions);
        }
        
        upsellRatePlan.setUpsellOptions(upsellOptions);
        return upsellRatePlan;
    }

    /**
     * Helper method to build meal title for orchestrator V2 LinkedRateValue
     */
    private String buildTitleMealTitleDefaultStringForOrch(com.gommt.hotels.orchestrator.detail.enums.LinkedRateValue linkedRateValue) {
        // Convert orchestrator V2 enum to string and use similar logic as SearchRoomsResponseTransformer
        switch(linkedRateValue.name()){
            case "DN":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_DN);
            case "EP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_EP);
            case "CB":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_CB);
            case "LCP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_LCP);
            case "LN":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_LN);
            case "LD":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_LD);
            case "TMAP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_TMAP);
            case "JP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_JP);
            case "AP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_AP);
            case "CP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_CP);
            case "MAP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_MAP);
            case "SMAP":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_SMAP);
            case "AI":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_AI);
            case "FREE_CANCELLATION":
                return polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_CANCELLATION_TEXT);
            case "NON_REFUNDABLE":
                return polyglotService.getSafeTranslatedData(MEAL_TITLE_NON_REFUNDABLE);
            default:
                return "";
        }
    }

    /**
     * Helper method to build upsell subtitle for orchestrator V2 LinkedRateDetail
     */
    private String buildUpSellSubTitleForOrch(com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRateDetail linkedRateDetail,
                                            Map<String, com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan> rpcRatePlanMap,
                                            String pricingKey, int ap) {
        if (linkedRateDetail.getSubType() == null) {
            return "";
        }
        
        // Convert orchestrator V2 enum to string and use similar logic as SearchRoomsResponseTransformer
        switch (linkedRateDetail.getSubType().name()) {
            case "CANCELLATION_FC_NR":
                return "";
            case "MEAL_DEFAULT":
                return "";
            case "MEAL_RACKRATE":
                return linkedRateDetail.getRackRateSavingPct() != null ? 
                    polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_RACKRATE).replace(ConstantsTranslation.PERCENTAGE.toUpperCase(), String.format("%.1f", linkedRateDetail.getRackRateSavingPct())) : "";
            case "MEAL_RATING":
                return linkedRateDetail.getFoodRating() != null ? 
                    polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_RATING).replace(ConstantsTranslation.MEAL_RATING.toUpperCase(), String.valueOf(linkedRateDetail.getFoodRating())) : "";
            case "MEAL_RESTAURANT":
                return linkedRateDetail.getEateriesDistance() != null ? 
                    polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_RESTAURANT).replace(ConstantsTranslation.RESTAURANT_DISTANCE.toUpperCase(), String.valueOf(linkedRateDetail.getEateriesDistance())) : "";
            case "CANCELLATION_DEFAULT":
                // For orchestrator V2, we need to build cancellation policy from parent rate plan
                if (rpcRatePlanMap.containsKey(pricingKey)) {
                    com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan parentRatePlan = rpcRatePlanMap.get(pricingKey);
                    if (Objects.nonNull(parentRatePlan) && parentRatePlan.getCancellationPolicy() != null) {
                        // Transform cancellation policy and get text (simplified)
                        // TODO: This could be expanded to use the cancellationPolicyHelper
                        return "Free Cancellation Available"; // Simplified text for now
                    }
                }
                return "";
            default:
                return "";
        }
    }

    /**
     * Helper method to build upsell sheet title for orchestrator V2 LinkedRateValue
     */
    private String buildUpsellSheetTitleForOrch(com.gommt.hotels.orchestrator.detail.enums.LinkedRateValue linkedRateValue) {
        String sheetTitleElement = buildTitleMealTitleDefaultStringForOrch(linkedRateValue);
        return polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_SHEET_TITLE).replace(ConstantsTranslation.UPSELL_LINKED_VALUE.toUpperCase(),sheetTitleElement);
    }

    /**
     * Builds additional charges for rate plan specific to flywheel traffic
     */
    private AdditionalMandatoryCharges buildAdditionalChargesForRatePlan(com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan ratePlan, 
                                                                        HotelDetails hotelDetails, boolean showTransfersFeeTxt) {
        // Extract supplier details and conversion factor from rate plan
        String hotelierCurrencyCode = hotelDetails.getCurrencyCode(); // Default to hotel currency
        double conversionFactor = ratePlan.getPrice() != null ? ratePlan.getPrice().getCurrencyConvertor() : 1.0;
        
        // Transform orchestrator AdditionalFees to old model AdditionalFees
        List<com.mmt.hotels.model.response.pricing.AdditionalFees> oldAdditionalFees = transformMandatoryCharges(hotelDetails.getMandatoryCharges());

        // Build AdditionalChargesBO object
        AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
                .buildUserCurrency(hotelDetails.getCurrencyCode())
                .buildHotelierCurrency(hotelierCurrencyCode)
                .buildPropertyType(hotelDetails.getPropertyType())
                .buildAdditionalFees(oldAdditionalFees)
                .buildConversionFactor(conversionFactor)
                .buildCityCode(hotelDetails.getLocation() != null && hotelDetails.getLocation().getCityId() != null ? hotelDetails.getLocation().getCityId() : null)
                .build();

        return commonResponseTransformer.buildAdditionalCharges(additionalChargesBO, showTransfersFeeTxt);
    }

    /**
     * TODO: Transforms QuickBook from orchestrator model to client gateway model
     * This method is commented out until orchestrator QuickBook model is available
     */
    // private QuickBook getQuickBook(com.gommt.hotels.orchestrator.detail.model.response.pricing.QuickBook orchQuickBook) {
    //     if (orchQuickBook == null) {
    //         return null;
    //     }
    //     
    //     QuickBook quickBook = new QuickBook();
    //     quickBook.setIsQuickBookAvailable(orchQuickBook.isQuickBookAvailable());
    //     quickBook.setQuickBookText(orchQuickBook.getQuickBookText());
    //     
    //     return quickBook;
    // }

    /**
     * Builds cancel policy description for payment mode (GI specific)
     */
    private void buildCancelPolicyDescriptionPaymod(SelectRoomRatePlan ratePlan) {
        if (StringUtils.isEmpty(ratePlan.getPayMode())){
            return;
        }
        PaymentModeDescriptionPolicy paymentModeDescriptionPolicy = null;
        switch (ratePlan.getPayMode()) {
            case "PAH_WITH_CC":
                paymentModeDescriptionPolicy = new PaymentModeDescriptionPolicy();
                paymentModeDescriptionPolicy.setTitleText("Pay at Hotel(CC) | Available on this plan");
                paymentModeDescriptionPolicy.setTitleMsg("Pay to the hotel - Credit card required");
                paymentModeDescriptionPolicy.setSubMsgText1("No Payment needed at the time of booking");
                paymentModeDescriptionPolicy.setSubMsgText2("Booking amount will be charged to your card by the hotel at any time before check-in");
                break;
            case "PAS":
                break;
            default:
                paymentModeDescriptionPolicy = new PaymentModeDescriptionPolicy();
                paymentModeDescriptionPolicy.setTitleText("Pay at Hotel | Available on this plan");
                paymentModeDescriptionPolicy.setTitleMsg("Pay at the hotel using cash/card");
                paymentModeDescriptionPolicy.setSubMsgText1("No Payment needed at the time of booking");
                paymentModeDescriptionPolicy.setSubMsgText2("Pay the booking amount directly at the hotel");
        }
        ratePlan.setPayModePolicy(paymentModeDescriptionPolicy);
    }

    /**
     * TODO: Sets package room specific rate plan information for orchestrator model
     * This method is commented out until orchestrator PackageDetails model is available
     */
    // private void setPackageRoomSpecificRatePlanInfo(PackageSelectRoomRatePlan ratePlan, 
    //                                                com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan orchRatePlan, 
    //                                                String askedCurrency) {
    //     if (orchRatePlan.getPackageDetails() == null) {
    //         return;
    //     }
    //     
    //     // Map orchestrator package details to client gateway model
    //     PackageDetails packageDetails = orchRatePlan.getPackageDetails();
    //     
    //     // Set extended check-in/out dates if available
    //     if (packageDetails.getExtendedCheckInDate() != null) {
    //         ratePlan.setExtendedCheckInDate(packageDetails.getExtendedCheckInDate());
    //     }
    //     if (packageDetails.getExtendedCheckOutDate() != null) {
    //         ratePlan.setExtendedCheckOutDate(packageDetails.getExtendedCheckOutDate());
    //     }
    //     
    //     // Convert package inclusion details
    //     if (packageDetails.getInclusions() != null && !packageDetails.getInclusions().isEmpty()) {
    //         PackageInclusionDetails packageInclusionDetails = new PackageInclusionDetails();
    //         // Map basic fields
    //         packageInclusionDetails.setBenefitsList(packageDetails.getInclusions());
    //         ratePlan.setPackageInclusionDetails(packageInclusionDetails);
    //     }
    // }

        /**
     * Maps sleeping info arrangement data to room details using HES logic.
     * Implements the same round-robin allocation algorithm as HotelUtil.buildSleepingInfoArrangement().
     * 
     * @param searchRoomsResponse The search rooms response containing room details
     * @param hotelDetails The hotel details from orchestrator containing room and space data
     * @param searchRoomsRequest The search request containing room stay candidates for pax count
     */
    private void mappingSpaceIdToEachRoomCode(SearchRoomsResponse searchRoomsResponse, 
                                              HotelDetails hotelDetails, 
                                              SearchRoomsRequest searchRoomsRequest) {
        
        if (hotelDetails == null || CollectionUtils.isEmpty(hotelDetails.getRooms()) || 
            searchRoomsRequest == null || searchRoomsRequest.getSearchCriteria() == null || 
            CollectionUtils.isEmpty(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates())) {
            return;
        }
        
        // Calculate total search pax
        int totalSearchPax = getTotalPaxCount(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());
        
        // Build sleeping arrangement data using HES logic
        LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> spaceIdToSleepingInfoArrMap = 
            buildSleepingInfoArrangement(searchRoomsResponse, hotelDetails, totalSearchPax);
        
        if (MapUtils.isEmpty(spaceIdToSleepingInfoArrMap)) {
            return;
        }
        
        // Process exact rooms
        if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
            List<RoomDetails> roomDetailsList = searchRoomsResponse.getExactRooms();
            if (CollectionUtils.isNotEmpty(roomDetailsList)) {
                utility.addSleepingInfoArrangementIntoRoomDetails(roomDetailsList, spaceIdToSleepingInfoArrMap);
            }
        }
        
        // Process recommended combos
        if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
            List<RoomDetails> roomDetailsList = searchRoomsResponse.getRecommendedCombos().get(0).getRooms();
            if (CollectionUtils.isNotEmpty(roomDetailsList)) {
                utility.addSleepingInfoArrangementIntoRoomDetails(roomDetailsList, spaceIdToSleepingInfoArrMap);
            }
        }
        
        // Process occupancy rooms
        if (CollectionUtils.isNotEmpty(searchRoomsResponse.getOccupancyRooms())) {
            List<RoomDetails> roomDetailsList = searchRoomsResponse.getOccupancyRooms();
            if (CollectionUtils.isNotEmpty(roomDetailsList)) {
                utility.addSleepingInfoArrangementIntoOccupancyRooms(roomDetailsList);
            }
        }
    }
    
    /**
     * Calculates total pax count from room stay candidates.
     * 
     * @param roomStayCandidates List of room stay candidates
     * @return Total pax count (adults + children)
     */
    private int getTotalPaxCount(List<RoomStayCandidate> roomStayCandidates) {
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return 0;
        }
        
        int totalPax = 0;
        for (RoomStayCandidate candidate : roomStayCandidates) {
            totalPax += candidate.getAdultCount();
            if (CollectionUtils.isNotEmpty(candidate.getChildAges())) {
                totalPax += candidate.getChildAges().size();
            }
        }
        return totalPax;
    }
    
    /**
     * Builds sleeping info arrangement using HES logic - implements round-robin allocation.
     * Replicates HotelUtil.buildSleepingInfoArrangement() and extractSleepingInfoArrFromPrivateSpace().
     * 
     * @param searchRoomsResponse The search rooms response to determine room type structure
     * @param hotelDetails The hotel details containing room and space data
     * @param totalSearchPax Total search pax count
     * @return Map of spaceId to sleeping arrangements
     */
    private LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> buildSleepingInfoArrangement(
            SearchRoomsResponse searchRoomsResponse, HotelDetails hotelDetails, int totalSearchPax) {
        
        // Determine if we have exact rooms or combos (following HES logic)
        boolean hasExactRooms = CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) && 
                               CollectionUtils.isEmpty(searchRoomsResponse.getOccupancyRooms());
        
        boolean comboCase = !hasExactRooms; // true for recommended combos, false for exact rooms
        
        // Extract sleeping info from private spaces (following HES extractSleepingInfoArrFromPrivateSpace logic)
        return extractSleepingInfoArrFromPrivateSpace(hotelDetails.getRooms(), comboCase, totalSearchPax);
    }
    
    /**
     * Extracts sleeping info arrangement from private spaces using HES logic.
     * Replicates HotelUtil.extractSleepingInfoArrFromPrivateSpace().
     * 
     * @param rooms List of rooms from orchestrator
     * @param comboCase Whether this is for combo case (true) or exact rooms (false)
     * @param totalSearchPax Total search pax count
     * @return Map of spaceId to sleeping arrangements
     */
    private LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> extractSleepingInfoArrFromPrivateSpace(
            List<com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms> rooms, boolean comboCase, int totalSearchPax) {
        
        LinkedHashMap<String, com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails> spaceIdToSleepingDetailsMap = new LinkedHashMap<>();
        LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> spaceIdToSleepingInfoArr = new LinkedHashMap<>();
        LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> tempSpaceIdToSleepingInfoArr = new LinkedHashMap<>();
        
        if (CollectionUtils.isEmpty(rooms)) {
            return spaceIdToSleepingInfoArr;
        }
        
        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms room : rooms) {
            if (room.getRoomInfo() == null || CollectionUtils.isEmpty(room.getRoomInfo().getSpaces())) {
                continue;
            }
            
            // Get room multiplier from rate plans (following HES logic)
            int roomMultiplier = getRoomMultiplier(room);
            
            // Clear maps for non-combo case (following HES logic)
            if (!comboCase) {
                spaceIdToSleepingDetailsMap.clear();
                tempSpaceIdToSleepingInfoArr.clear();
            }
            
            // Process private spaces only
            for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData spaceData : room.getRoomInfo().getSpaces()) {
                if (spaceData.getType() == com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData.Type.PRIVATE) {
                    processPrivateSpaces(spaceData.getSpaces(), spaceIdToSleepingDetailsMap, tempSpaceIdToSleepingInfoArr, roomMultiplier);
                }
            }
            
            // Apply round-robin for non-combo case
            if (!comboCase) {
                if (applyRoundRobinOnGuestCountAndBedCount(spaceIdToSleepingDetailsMap, tempSpaceIdToSleepingInfoArr, totalSearchPax)) {
                    // Copy temp map to final map
                    for (String spaceId : tempSpaceIdToSleepingInfoArr.keySet()) {
                        spaceIdToSleepingInfoArr.put(spaceId, tempSpaceIdToSleepingInfoArr.get(spaceId));
                    }
                } else {
                    return new LinkedHashMap<>(); // Return empty if round-robin fails
                }
            }
        }
        
        // Apply round-robin for combo case
        if (comboCase) {
            if (applyRoundRobinOnGuestCountAndBedCount(spaceIdToSleepingDetailsMap, tempSpaceIdToSleepingInfoArr, totalSearchPax)) {
                // Copy temp map to final map
                for (String spaceId : tempSpaceIdToSleepingInfoArr.keySet()) {
                    spaceIdToSleepingInfoArr.put(spaceId, tempSpaceIdToSleepingInfoArr.get(spaceId));
                }
            } else {
                return new LinkedHashMap<>(); // Return empty if round-robin fails
            }
        }
        
        return spaceIdToSleepingInfoArr;
    }
    
    /**
     * Gets room multiplier from rate plans (following HES logic).
     * 
     * @param room The room from orchestrator
     * @return Room multiplier (number of rooms)
     */
    private int getRoomMultiplier(com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms room) {
        // Simplified implementation - default to 1 room
        // TODO: Enhance to extract actual room multiplier from rate plans once correct field names are confirmed
        return 1;
    }
    
    /**
     * Processes private spaces and builds sleeping info arrangements (following HES logic).
     * 
     * @param spaces List of spaces to process
     * @param spaceIdToSleepingDetailsMap Map to store sleeping details by space ID
     * @param tempSpaceIdToSleepingInfoArr Temporary map to store sleeping arrangements
     * @param roomMultiplier Room multiplier for this room type
     */
    private void processPrivateSpaces(
            List<com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space> spaces,
            LinkedHashMap<String, com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails> spaceIdToSleepingDetailsMap,
            LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> tempSpaceIdToSleepingInfoArr,
            int roomMultiplier) {
        
        if (CollectionUtils.isEmpty(spaces)) {
            return;
        }
        
        for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space : spaces) {
            // Process only bedroom and living room spaces (following HES logic)
            if (Constants.BEDROOM.equalsIgnoreCase(space.getSpaceType()) || 
                Constants.LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) {
                
                if (space.getSleepingDetails() != null && StringUtils.isNotBlank(space.getSpaceId())) {
                    spaceIdToSleepingDetailsMap.put(space.getSpaceId(), space.getSleepingDetails());
                    
                    // Create sleeping arrangements for each room instance (following HES logic)
                    List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement> sleepingInfoArrangements = new ArrayList<>();
                    
                    int spaceMultiplier = roomMultiplier;
                    while (spaceMultiplier > 0) {
                        com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement sleepingInfoArrangement = 
                            new com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement();
                        
                        // Set basic data from sleeping details
                        sleepingInfoArrangement.setBedRoom(space.getSleepingDetails().getBedRoomCount());
                        sleepingInfoArrangement.setMaxCapacity(space.getSleepingDetails().getMaxOccupancy());
                        sleepingInfoArrangement.setSpaceType(space.getSpaceType());
                        
                        sleepingInfoArrangements.add(sleepingInfoArrangement);
                        spaceMultiplier--;
                    }
                    
                    tempSpaceIdToSleepingInfoArr.put(space.getSpaceId(), sleepingInfoArrangements);
                }
            }
        }
    }
    
    /**
     * Applies round-robin allocation algorithm (following HES logic).
     * Replicates HotelUtil.applyRoundRobinOnGuestCountAndBedCount().
     * 
     * @param spaceIdToSleepingDetailsMap Map of space ID to sleeping details
     * @param spaceIdToSleepingInfoArr Map of space ID to sleeping arrangements
     * @param totalSearchPax Total search pax to allocate
     * @return True if round-robin allocation succeeded, false otherwise
     */
    private boolean applyRoundRobinOnGuestCountAndBedCount(
            LinkedHashMap<String, com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails> spaceIdToSleepingDetailsMap,
            LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> spaceIdToSleepingInfoArr,
            int totalSearchPax) {
        
        if (MapUtils.isEmpty(spaceIdToSleepingDetailsMap) || MapUtils.isEmpty(spaceIdToSleepingInfoArr) ||
            spaceIdToSleepingDetailsMap.size() != spaceIdToSleepingInfoArr.size()) {
            return false;
        }
        
        // Calculate total max capacity (following HES logic)
        int totalMaxCapacity = 0;
        for (String spaceId : spaceIdToSleepingDetailsMap.keySet()) {
            int multiplier = spaceIdToSleepingInfoArr.get(spaceId).size();
            totalMaxCapacity += spaceIdToSleepingDetailsMap.get(spaceId).getMaxOccupancy() * multiplier;
        }
        
        if (totalMaxCapacity < totalSearchPax) {
            return false; // Not enough capacity
        }
        
        try {
            // Apply round-robin allocation (following HES logic)
            fillGuestCountAndBedCountUsingRoundRobin(spaceIdToSleepingDetailsMap, spaceIdToSleepingInfoArr, totalSearchPax);
            
            // Build bed info for each sleeping arrangement (following HES logic)
            buildBedInfoMapForEachSleepingInfoArrangement(spaceIdToSleepingInfoArr, spaceIdToSleepingDetailsMap);
            
        } catch (Exception ex) {
            logger.error("Error while applying round-robin allocation", ex);
            return false;
        }
        
        return true;
    }
    
    /**
     * Fills guest count and bed count using round-robin allocation (following HES logic).
     * Replicates HotelUtil.fillGuestCountAndBedCountUsingRoundRobin().
     * 
     * @param spaceIdToSleepingDetailsMap Map of space ID to sleeping details
     * @param spaceIdToSleepingInfoArr Map of space ID to sleeping arrangements
     * @param totalSearchPax Total search pax to allocate
     */
    private void fillGuestCountAndBedCountUsingRoundRobin(
            LinkedHashMap<String, com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails> spaceIdToSleepingDetailsMap,
            LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> spaceIdToSleepingInfoArr,
            int totalSearchPax) {
        
        // 1. Fill base occupancy for bedrooms first (following HES logic)
        totalSearchPax = fillingBaseOccupancy(spaceIdToSleepingInfoArr, spaceIdToSleepingDetailsMap, Constants.BEDROOM, totalSearchPax);
        
        // 2. Fill base occupancy for living rooms (following HES logic)
        totalSearchPax = fillingBaseOccupancy(spaceIdToSleepingInfoArr, spaceIdToSleepingDetailsMap, Constants.LIVING_ROOM, totalSearchPax);
        
        // 3. Fill remaining pax using extra capacity (following HES logic)
        boolean isPossible = true;
        while (totalSearchPax > 0 && isPossible) {
            isPossible = false;
            
            // Fill bedrooms first, then living rooms (following HES logic)
            totalSearchPax = fillingExtraOccupancy(spaceIdToSleepingInfoArr, spaceIdToSleepingDetailsMap, Constants.BEDROOM, totalSearchPax, isPossible);
            if (totalSearchPax == 0) break;
            
            int beforeLivingRoom = totalSearchPax;
            totalSearchPax = fillingExtraOccupancy(spaceIdToSleepingInfoArr, spaceIdToSleepingDetailsMap, Constants.LIVING_ROOM, totalSearchPax, isPossible);
            
            // Check if we made progress
            isPossible = (totalSearchPax < beforeLivingRoom);
        }
    }
    
    /**
     * Fills base occupancy for spaces of given type (following HES logic).
     * 
     * @param spaceIdToSleepingInfoArr Map of space ID to sleeping arrangements
     * @param spaceIdToSleepingDetailsMap Map of space ID to sleeping details
     * @param spaceType Space type to process (BEDROOM or LIVING_ROOM)
     * @param remainingPax Remaining pax to allocate
     * @return Updated remaining pax count
     */
    private int fillingBaseOccupancy(
            LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> spaceIdToSleepingInfoArr,
            LinkedHashMap<String, com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails> spaceIdToSleepingDetailsMap,
            String spaceType, int remainingPax) {
        
        for (String spaceId : spaceIdToSleepingInfoArr.keySet()) {
            List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement> arrangements = spaceIdToSleepingInfoArr.get(spaceId);
            com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails sleepingDetails = spaceIdToSleepingDetailsMap.get(spaceId);
            
            if (CollectionUtils.isNotEmpty(arrangements) && sleepingDetails != null) {
                for (com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement arrangement : arrangements) {
                    if (spaceType.equalsIgnoreCase(arrangement.getSpaceType()) && remainingPax > 0) {
                        int baseOccupancy = sleepingDetails.getMinOccupancy();
                        int allocatedGuests = Math.min(baseOccupancy, remainingPax);
                        
                        arrangement.setGuest(allocatedGuests);
                        arrangement.setBed(sleepingDetails.getBedCount());
                        arrangement.setBathRoom(0); // Default, can be enhanced with actual bathroom data
                        
                        remainingPax -= allocatedGuests;
                    }
                }
            }
        }
        
        return remainingPax;
    }
    
    /**
     * Fills extra occupancy for spaces of given type (following HES logic).
     * 
     * @param spaceIdToSleepingInfoArr Map of space ID to sleeping arrangements
     * @param spaceIdToSleepingDetailsMap Map of space ID to sleeping details
     * @param spaceType Space type to process (BEDROOM or LIVING_ROOM)
     * @param remainingPax Remaining pax to allocate
     * @param isPossible Whether allocation is possible
     * @return Updated remaining pax count
     */
    private int fillingExtraOccupancy(
            LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> spaceIdToSleepingInfoArr,
            LinkedHashMap<String, com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails> spaceIdToSleepingDetailsMap,
            String spaceType, int remainingPax, boolean isPossible) {
        
        for (String spaceId : spaceIdToSleepingInfoArr.keySet()) {
            List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement> arrangements = spaceIdToSleepingInfoArr.get(spaceId);
            com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails sleepingDetails = spaceIdToSleepingDetailsMap.get(spaceId);
            
            if (CollectionUtils.isNotEmpty(arrangements) && sleepingDetails != null && remainingPax > 0) {
                for (com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement arrangement : arrangements) {
                    if (spaceType.equalsIgnoreCase(arrangement.getSpaceType()) && 
                        arrangement.getGuest() < sleepingDetails.getMaxOccupancy() && remainingPax > 0) {
                        
                        // Add one more guest if space allows
                        arrangement.setGuest(arrangement.getGuest() + 1);
                        remainingPax--;
                        isPossible = true; // Mark that we made progress
                        
                        if (remainingPax == 0) break;
                    }
                }
            }
            if (remainingPax == 0) break;
        }
        
        return remainingPax;
    }
    
    /**
     * Builds bed info map for each sleeping arrangement (following HES logic).
     * Replicates HotelUtil.buildingBedInfoMapForEachSleepingInfoArrangement().
     * 
     * @param spaceIdToSleepingInfoArr Map of space ID to sleeping arrangements
     * @param spaceIdToSleepingDetailsMap Map of space ID to sleeping details
     */
    private void buildBedInfoMapForEachSleepingInfoArrangement(
            LinkedHashMap<String, List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement>> spaceIdToSleepingInfoArr,
            LinkedHashMap<String, com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails> spaceIdToSleepingDetailsMap) {
        
        for (String spaceId : spaceIdToSleepingInfoArr.keySet()) {
            List<com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement> arrangements = spaceIdToSleepingInfoArr.get(spaceId);
            com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails sleepingDetails = spaceIdToSleepingDetailsMap.get(spaceId);
            
            if (CollectionUtils.isNotEmpty(arrangements) && sleepingDetails != null) {
                for (com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement arrangement : arrangements) {
                    // Build bed info from sleeping details
                    LinkedHashMap<String, Integer> bedInfos = new LinkedHashMap<>();
                    
                    if (CollectionUtils.isNotEmpty(sleepingDetails.getBedInfo())) {
                        for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo bedInfo : sleepingDetails.getBedInfo()) {
                            if (StringUtils.isNotBlank(bedInfo.getType())) {
                                bedInfos.put(bedInfo.getType(), bedInfo.getCount());
                            }
                        }
                    }
                    arrangement.setBedInfos(bedInfos);
                    
                    // Set sub text
                    int guestCount = arrangement.getGuest();
                    arrangement.setSubText("Sleeps " + guestCount + " guest" + (guestCount > 1 ? "s" : ""));
                    
                    // Set max capacity
                    arrangement.setMaxCapacity(sleepingDetails.getMaxOccupancy());
                }
            }
        }
    }


}
