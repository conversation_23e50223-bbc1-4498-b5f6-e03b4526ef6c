package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.mmt.hotels.model.response.searchwrapper.CategoryDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails;
import com.gommt.hotels.orchestrator.model.response.listing.*;
import com.gommt.hotels.orchestrator.model.response.listing.ExclusiveFilterHotels;
import com.google.common.reflect.TypeToken;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.base.FailureReason;
import com.mmt.hotels.model.response.flyfish.ReviewSummary;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.model.LocusData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformer.gson;

@Component
public class OrchSearchHotelsResponseTransformerSCION extends OrchSearchHotelsResponseTransformerDesktop {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformerSCION.class);

    private Map<String, CategoryDetails> categoryDetailsMap;

    ObjectMapper objectMapper = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);

    @Value("${category.details.Map}")
    private String categoryDetailsText;

    @Value("${deeplink.listing.url}")
    private String deepLink;

    @Autowired
    Utility utility;

    @PostConstruct
    public void init() {
        categoryDetailsMap = gson.fromJson(categoryDetailsText, new TypeToken<Map<String, CategoryDetails>>() {
        }.getType());

    }

    public String convertSearchHotelsResponse(SearchHotelsRequest searchHotelsRequest, ListingResponse listingResponse) {
        if (listingResponse == null) {
            LOGGER.error("Listing response is null");
            return null;
        }

        if (listingResponse.getPersonalizedSections() == null) {
            LOGGER.error("Personalized sections are null");
            return null;
        }

        List<HotelDetails> hotelDetailsList = new ArrayList<>();
        Set<String> commonCategories = new HashSet<>();
        Map<String, CategoryDetails> commonCategoryDetails = new HashMap<>();

        int totalHotelCount =0;
        for (PersonalizedSectionDetails personalizedSection : listingResponse.getPersonalizedSections()) {
            totalHotelCount+=personalizedSection.getHotelCount();
            if(personalizedSection.getHotels() != null) {
                hotelDetailsList.addAll(personalizedSection.getHotels());
            }
        }

        for (HotelDetails hotelEntity : hotelDetailsList) {
            if (CollectionUtils.isNotEmpty(hotelEntity.getCategories())) {
                if (commonCategories.isEmpty())
                    commonCategories.addAll(hotelEntity.getCategories());
                else
                    commonCategories.retainAll(hotelEntity.getCategories());
            }
            Map<String,CategoryDetails> categoryDetails = buildCategoryDetails(hotelEntity.getCategories());
            if (MapUtils.isNotEmpty(categoryDetails)) {
                if (MapUtils.isEmpty(commonCategoryDetails)) {
                    commonCategoryDetails = categoryDetails;
                } else {
                    commonCategoryDetails.keySet().retainAll(categoryDetails.keySet());
                }
            }
        }
        LocusData locusData = new LocusData();
        if (listingResponse.getLocation() != null) {
            locusData.setLocusId(listingResponse.getLocation().getId());
            locusData.setLocusType(listingResponse.getLocation().getType());
            locusData.setLocusName(listingResponse.getLocation().getCityName());
            locusData.setCountryCode(listingResponse.getLocation().getCountryId());
        }
        FailureReason failureReason = new FailureReason();
        if(listingResponse.getError() != null) {
            failureReason.setErrorCode(listingResponse.getError().getCode());
            failureReason.setMsg(listingResponse.getError().getMessage());
            //TODO: set status code
            //failureReason.setStatusCode()
        }

        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> filterToHotelMapCg = new HashMap<>();
        for (PersonalizedSectionDetails personalizedSection : listingResponse.getPersonalizedSections()) {
            if (EXCLUSIVE_HOTELS.equalsIgnoreCase(personalizedSection.getName())) {
                filterToHotelMapCg = buildFilterToHotelMap(personalizedSection.getFilterToHotelMap(), listingResponse, searchHotelsRequest);
                break;
            }
        }
        List<SearchWrapperHotelEntity> hotelListCg = convertHesResponse(hotelDetailsList, listingResponse, searchHotelsRequest);


        boolean noMoreHotels = (listingResponse.getLastPage() != null && listingResponse.getLastPage()) || StringUtils.isEmpty(listingResponse.getLastHotelId());
        String appUrl = getAppDeeplinkCrossSell(searchHotelsRequest);
        SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = new SearchWrapperResponseBO.Builder()
                .buildTotalHotelsCount(totalHotelCount)
                .buildNoMoreAvailableHotels(noMoreHotels)
                .buildTrackingMap(listingResponse.getTrackingMap())
                .buildHotelsList(hotelListCg)
                .buildFilterToHotelMap(filterToHotelMapCg)//Have to check //processCardEngineFilterResponse
                .buildCityName(listingResponse.getLocation() != null ? listingResponse.getLocation().getCityName() : null)
                .buildCountryName(listingResponse.getLocation() != null ? listingResponse.getLocation().getCountryName() : null)
                .buildCountryCode(listingResponse.getLocation() != null ? listingResponse.getLocation().getCountryId() : null)
                .buildCityCode(listingResponse.getLocation() != null ? listingResponse.getLocation().getId() : null)
                .buildFailureReason(failureReason)
                .buildCorrelationKey(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()))
                .buildCurrency(listingResponse.getCurrency())
                .buildCurrencyConvFactorINR(1.0) //TO DO: check
                .buildLocusData(locusData)
                .buildListingDeepLinkUrl(prepareTabDetailsLink(searchHotelsRequest,appUrl ,true, true, null, listingResponse.getLocation()))
                .buildCommonHotelCategoriesDetails(commonCategoryDetails)
                .buildCommonHotelCategories(commonCategories)
                .build();
        searchWrapperResponseBO.setSharingUrl(prepareListingSharingUrl(searchHotelsRequest,rootLevelSharingUrl ,true, true, listingResponse.getLocation()));
        searchWrapperResponseBO.setExclusiveOfferTs(getCurrentTimestampInMilli());
        return toJson(searchWrapperResponseBO);
    }

    private String toJson(SearchWrapperResponseBO searchWrapperResponseBO) {
        try {
            String searchWrapperResponseBOString = objectMapper.writeValueAsString(searchWrapperResponseBO);
            return searchWrapperResponseBOString;
        } catch (Exception e) {
            LOGGER.error("Error converting SearchWrapperResponseBO to JSON", e);
        }
        return null;
    }

    public long getCurrentTimestampInMilli() {
        ZoneId zoneId_Dubai = ZoneId.of( "Asia/Dubai" );
        ZonedDateTime dubaiDT = Instant.now().atZone(zoneId_Dubai);
        long epochMilli = dubaiDT.toInstant().toEpochMilli();
        return epochMilli;
    }


    private Map<String,CategoryDetails> buildCategoryDetails(Set<String> categories){
        Map<String,CategoryDetails> categoryDtlsMap =new HashMap<>();
        if(CollectionUtils.isEmpty(categories)){
            return categoryDtlsMap;
        }
        translateCategoryMap(categoryDetailsMap);
        for(String category : categories){
            if(MapUtils.isNotEmpty(categoryDetailsMap) && categoryDetailsMap.containsKey(category)){
                CategoryDetails catDetails = new CategoryDetails();
                BeanUtils.copyProperties(categoryDetailsMap.get(category), catDetails);
                categoryDtlsMap.put(category,catDetails);
            }
        }
        return categoryDtlsMap;
    }

    public void translateCategoryMap(Map<String, CategoryDetails> categoryDetailsMap) {
        if (MapUtils.isNotEmpty(categoryDetailsMap)){
            for (Map.Entry<String, CategoryDetails> categoryDetailsEntry : categoryDetailsMap.entrySet()){
                CategoryDetails categoryDetails = categoryDetailsEntry.getValue();
                List<String> categoryDetailsList =  categoryDetails.getData();
                List<String> categoryListNew = new ArrayList<>();
                for (String category : categoryDetailsList){
                    categoryListNew.add(polyglotService.getTranslatedData(category));
                }
                categoryDetails.setData(categoryListNew);
                categoryDetails.setTitle(polyglotService.getTranslatedData(categoryDetails.getTitle()));
            }
        }
    }

    public Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> buildFilterToHotelMap(Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap, ListingResponse listingResponse, SearchHotelsRequest searchHotelsRequest) {
        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> filterToHotelMapCg = new HashMap<>();
        if (MapUtils.isEmpty(filterToHotelMap)) {
            return filterToHotelMapCg;
        }
        for (Map.Entry<String, ExclusiveFilterHotels<HotelDetails>> entry : filterToHotelMap.entrySet()) {
            String filterName = entry.getKey();
            ExclusiveFilterHotels<HotelDetails> exclusiveFilterHotels = entry.getValue();
            com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity> exclusiveFilterHotelsCg = new com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<>();
            List<HotelDetails> hotelEntities = exclusiveFilterHotels.getHotels();
            if (CollectionUtils.isNotEmpty(hotelEntities)) {
                List<SearchWrapperHotelEntity> searchWrapperHotelEntities = convertHesResponse(hotelEntities, listingResponse, searchHotelsRequest);
                exclusiveFilterHotelsCg.setHotels(searchWrapperHotelEntities);
            }
            exclusiveFilterHotelsCg.setTabDetails(convertTabDetails(exclusiveFilterHotels.getTabDetails(), searchHotelsRequest));
            filterToHotelMapCg.put(filterName, exclusiveFilterHotelsCg);
        }
        return filterToHotelMapCg;

    }

    protected com.mmt.hotels.model.response.searchwrapper.TabDetails convertTabDetails(
            com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails, SearchHotelsRequest searchHotelsRequest) {
        if (sourceTabDetails == null) {
            return null;
        }
        com.mmt.hotels.model.response.searchwrapper.TabDetails targetTabDetails = new com.mmt.hotels.model.response.searchwrapper.TabDetails();
        targetTabDetails.setId(sourceTabDetails.getId());
        targetTabDetails.setTitle(sourceTabDetails.getTitle());
        targetTabDetails.setSelected(sourceTabDetails.isSelected());
        targetTabDetails.setIconUrl(sourceTabDetails.getIconUrl());
        targetTabDetails.setFilterGroup(sourceTabDetails.getFilterGroup());
        targetTabDetails.setFilterValue(sourceTabDetails.getFilterValue());
        targetTabDetails.setDeeplink(prepareTabDetailsLink(searchHotelsRequest,deepLink, true, false, sourceTabDetails, null));
        return targetTabDetails;
    }

    public String prepareTabDetailsLink(SearchHotelsRequest searchHotelsRequest, String deeplink, boolean appendFilter, boolean checkAvailability, TabDetails tabDetails, LocationDetails locationDetails) {
        SearchHotelsCriteria searchCriteria = searchHotelsRequest.getSearchCriteria();
        String roomStayParam = "";
        if (searchCriteria != null && searchCriteria.getRoomStayCandidates() != null) {
//            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(searchCriteria.getRoomStayCandidates());
            roomStayParam = buildRoomStayCandidateFromSearchWrapper(searchCriteria.getRoomStayCandidates());
        }

        // Prepare basic sharing URL
        String deepLink = prepareBasicSharingUrl(
                roomStayParam,
                searchCriteria != null ? searchCriteria.getCheckIn() : "",
                searchCriteria != null ? searchCriteria.getCheckOut() : "",
                searchCriteria != null && searchCriteria.getCityName()!=null ? searchCriteria.getCityName() : locationDetails != null ? locationDetails.getCityName() : "",
                searchCriteria != null && searchCriteria.getLocationId() != null? searchCriteria.getLocationId() : locationDetails != null ? locationDetails.getId() : "",
                searchCriteria != null && searchCriteria.getCountryCode() != null? searchCriteria.getCountryCode() : locationDetails != null ? locationDetails.getCountryId() : "",
                deeplink,
                searchCriteria != null &&  searchCriteria.getLocationType() != null ? searchCriteria.getLocationType() : locationDetails != null ? locationDetails.getType() : "",
                searchCriteria != null ? searchCriteria.getCurrency() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getSiteDomain() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getPageContext() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getFunnelSource() : "",
                searchCriteria != null ? searchCriteria.getParentLocationId() : "",
                searchCriteria != null ? searchCriteria.getParentLocationType() : "",
                checkAvailability
        );
        if(tabDetails != null) {
            List<Filter> filters = new ArrayList<>();
            Filter filter = new Filter();
            filter.setFilterGroup(tabDetails.getFilterGroup() != null ? FilterGroup.valueOf(tabDetails.getFilterGroup()) : null);
            filter.setFilterValue(tabDetails.getFilterValue());
            filters.add(filter);
            deepLink = appendFiltersDataToDeepLink(filters, deepLink);
        }
        // Return the final deepLink
        return deepLink;
    }

    public List<SearchWrapperHotelEntity> convertHesResponse(List<HotelDetails> hotelEntities, ListingResponse listingResponse, SearchHotelsRequest searchHotelsRequest) {
        if (CollectionUtils.isEmpty(hotelEntities)) {
            return null;
        }
        List<SearchWrapperHotelEntity> hotelList = new ArrayList<>();
        for (HotelDetails hotelEntity : hotelEntities) {
            SearchWrapperHotelEntity searchWrapperHotelEntity = convertHesResponse(hotelEntity, listingResponse, searchHotelsRequest);
            hotelList.add(searchWrapperHotelEntity);
        }
        return hotelList;
    }


    public SearchWrapperHotelEntity convertHesResponse(com.gommt.hotels.orchestrator.model.response.listing.HotelDetails hotelEntity, ListingResponse listingResponse, SearchHotelsRequest searchHotelsRequest) {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        searchWrapperHotelEntity.setId(hotelEntity.getId());
        searchWrapperHotelEntity.setGiHotelId(hotelEntity.getGiId());
        searchWrapperHotelEntity.setName(hotelEntity.getName());
        searchWrapperHotelEntity.setPropertyType(hotelEntity.getPropertyType());
        searchWrapperHotelEntity.setStarRating(hotelEntity.getStarRating());
        searchWrapperHotelEntity.setCategories(hotelEntity.getCategories());
        searchWrapperHotelEntity.setAltAcco(hotelEntity.isAltAcco());
        searchWrapperHotelEntity.setMmtHotelCategory(hotelEntity.getHotelCategory());
        searchWrapperHotelEntity.setHotelPersuasions(hotelEntity.getHotelPersuasions());
        if(hotelEntity.getLocation() != null) {
            searchWrapperHotelEntity.setCityCode(hotelEntity.getLocation().getId());
            searchWrapperHotelEntity.setCountryCode(hotelEntity.getLocation().getCountryId());
            searchWrapperHotelEntity.setCountryName(hotelEntity.getLocation().getCountryName());
            searchWrapperHotelEntity.setCityName(hotelEntity.getLocation().getCityName());
        }

        searchWrapperHotelEntity.setFacilityHighlights(hotelEntity.getFacilityHighlights());
        LocusData locusData = new LocusData();
        if(hotelEntity.getLocation() != null) {
            locusData.setCountryCode(hotelEntity.getLocation().getCountryId());
            locusData.setLocusId(hotelEntity.getLocation().getId());
            locusData.setLocusName(hotelEntity.getLocation().getCityName());
            locusData.setLocusType(hotelEntity.getLocation().getType());
            locusData.setName(hotelEntity.getLocation().getCityName());
        }
        searchWrapperHotelEntity.setLocusData(locusData);

        com.mmt.hotels.model.response.searchwrapper.GeoLocation geoLocation = new com.mmt.hotels.model.response.searchwrapper.GeoLocation();
        if(hotelEntity.getLocation() != null && hotelEntity.getLocation().getGeo() != null) {
            geoLocation.setLatitude(hotelEntity.getLocation().getGeo().getLatitude());
            geoLocation.setLongitude(hotelEntity.getLocation().getGeo().getLongitude());
        }
        //geoLocation.setDistanceMeter(); // Have to check

        searchWrapperHotelEntity.setGeoLocation(geoLocation);
        buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        searchWrapperHotelEntity.setLocationPersuasion(hotelEntity.getLocationPersuasions());
        searchWrapperHotelEntity.setAddress(convertAddress(hotelEntity.getAddress()));
        if (hotelEntity.getMedia() != null && CollectionUtils.isNotEmpty(hotelEntity.getMedia().getImages())) {
            searchWrapperHotelEntity.setMainImages(
                    getMainImages(hotelEntity.getMedia().getImages(), searchHotelsRequest));
        }
        searchWrapperHotelEntity.setCategories(hotelEntity.getCategories());
        searchWrapperHotelEntity.setBreakFastAvailable(hotelEntity.isBreakFastAvailable());
        searchWrapperHotelEntity.setIsFreeCancellation(hotelEntity.isFreeCancellation());
        searchWrapperHotelEntity.setAltAcco(hotelEntity.isAltAcco());
        CurrencyCode currencyCode = new CurrencyCode();
        currencyCode.setId(listingResponse.getCurrency());
        currencyCode.setValue(listingResponse.getCurrency());
        searchWrapperHotelEntity.setCurrencyCode(currencyCode);
        searchWrapperHotelEntity.setReviewSummary(buildReviewSummary(hotelEntity));
        searchWrapperHotelEntity.setFlyfishReviewSummary(buildFlyfishReviewSummary(hotelEntity));
        if (hotelEntity.getAltAccoRoomInfo() != null) {
            searchWrapperHotelEntity.setAltAccoRoomInfo(
                    hotelEntity.getAltAccoRoomInfo().entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey, // Keep the key as is
                                    entry -> {
                                        com.mmt.model.RoomInfo roomInfo = new com.mmt.model.RoomInfo();
                                        com.gommt.hotels.orchestrator.model.response.content.roomInfo.RoomInfo sourceRoomInfo = entry.getValue();

                                        // Map fields from sourceRoomInfo to roomInfo
                                        roomInfo.setBedCount(sourceRoomInfo.getBedCount());
                                        roomInfo.setBedRoomCount(String.valueOf(sourceRoomInfo.getBedRoomCount()));
                                        roomInfo.setParentRoomCode(sourceRoomInfo.getParentRoomCode());
                                        // Map other fields as needed

                                        return roomInfo;
                                    }
                            ))
            );
        }

        //RSC VALUE FOR DEEP-LINKS
        List<RoomStayCandidate> distributedRoomStayCandidateList = Collections.emptyList();
        String rscValue = EMPTY_STRING;

        String detailUrl = getDetailDeeplink(searchHotelsRequest);
        searchWrapperHotelEntity.setAppDeeplink(buildHotelLevelAppDeepLink(searchHotelsRequest, null, rscValue, distributedRoomStayCandidateList, hotelEntity, listingResponse.getLocation(), detailUrl));
        if(listingResponse.getLocation() != null) {
            searchWrapperHotelEntity.setFromCity(listingResponse.getLocation().getParentCity());
        }
        return searchWrapperHotelEntity;
    }

    protected List<String> getMainImages(List<ImageDetails> imageUrlList, SearchHotelsRequest searchHotelsRequest) {
        List<String> urlList = new ArrayList<>();
        if(imageUrlList == null) {
            return urlList;
        }
        String secureUrl = "";
        int imageCount = 1;
        if(searchHotelsRequest.getAdditionalProperties() != null) {
            secureUrl = searchHotelsRequest.getAdditionalProperties().getOrDefault("secureUrl", "");
            imageCount = Integer.parseInt(searchHotelsRequest.getAdditionalProperties().getOrDefault("imageCount","1"));

        }
        for(ImageDetails imageDetails : imageUrlList) {
            urlList.add(imageDetails.getUrl());
        }
        if(isScionRequest(searchHotelsRequest) && CollectionUtils.isNotEmpty(urlList) && imageCount >0) {
            if (imageUrlList.size() > imageCount) {
                urlList = urlList.subList(0, imageCount);
            }
            if (secureUrl != null && secureUrl.length() != 0) {
                for (int i = 0; i < urlList.size(); i++) {
                    urlList.set(i, secureUrl + ":" + imageUrlList.get(i).getUrl());
                }
            }

        }
        return urlList;
    }

    protected com.mmt.hotels.model.response.staticdata.Address convertAddress(
            com.gommt.hotels.orchestrator.model.response.content.Address sourceAddress) {
        if (sourceAddress == null) {
            return null;
        }
        com.mmt.hotels.model.response.staticdata.Address targetAddress = new com.mmt.hotels.model.response.staticdata.Address();
        targetAddress.setArea(sourceAddress.getArea());
        targetAddress.setLine1(sourceAddress.getLine1());
        targetAddress.setLine2(sourceAddress.getLine2());
        // Map other fields as needed
        return targetAddress;
    }

    protected Map<OTA, JsonNode> buildFlyfishReviewSummary(HotelDetails hotelEntity) {
        Map<OTA, JsonNode> flyfishReviewSummary = new HashMap<>();
        if (hotelEntity != null && hotelEntity.getReviewDetails() != null && hotelEntity.getReviewDetails().getOta() != null) {
            flyfishReviewSummary.put(OTA.valueOf(hotelEntity.getReviewDetails().getOta().toUpperCase()), buildReviewSummaryFlyFish(hotelEntity));
        }
        return flyfishReviewSummary;
    }

    private JsonNode buildReviewSummaryFlyFish(HotelDetails hotelEntity) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode reviewSummary = objectMapper.createObjectNode();
        if (hotelEntity != null && hotelEntity.getReviewDetails() != null) {
            ((ObjectNode) reviewSummary).put("cumulativeRating", hotelEntity.getReviewDetails().getRating());
            ((ObjectNode) reviewSummary).put("totalReviewsCount", hotelEntity.getReviewDetails().getTotalReviewCount());
            ((ObjectNode) reviewSummary).put("totalRatingCount", hotelEntity.getReviewDetails().getTotalRatingCount());
        }
        return reviewSummary;
    }

    protected ReviewSummary buildReviewSummary(HotelDetails hotelEntity) {
        ReviewSummary reviewSummary = new ReviewSummary();
        if (hotelEntity == null) {
            return reviewSummary;
        }
        if (hotelEntity.getReviewDetails() != null) {
            reviewSummary.setOta(hotelEntity.getReviewDetails().getOta());
            reviewSummary.setHotelRating(hotelEntity.getReviewDetails().getRating());
            reviewSummary.setReviewCount(hotelEntity.getReviewDetails().getTotalReviewCount());
            reviewSummary.setRatingCount(hotelEntity.getReviewDetails().getTotalRatingCount());
        }
        return reviewSummary;
    }


    protected void buildDisplayFarePrice(SearchWrapperHotelEntity searchWrapperHotelEntity, com.gommt.hotels.orchestrator.model.response.listing.HotelDetails hotelEntity) {
        if (hotelEntity == null || hotelEntity.getRooms() == null || hotelEntity.getRooms() == null || hotelEntity.getRooms().isEmpty() || hotelEntity.getRooms().get(0).getRatePlans() == null || hotelEntity.getRooms().get(0).getRatePlans().isEmpty()) {
            return;
        }
        ResponseRatePlan responseRatePlan = hotelEntity.getRooms().get(0).getRatePlans().get(0);

        com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown displayPriceBreakDown = new com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown();
        if(responseRatePlan.getPrice() != null) {
            displayPriceBreakDown.setDisplayPrice(responseRatePlan.getPrice().getDisplayPrice());
            displayPriceBreakDown.setSavingPerc(responseRatePlan.getPrice().getSavingPerc());
            displayPriceBreakDown.setBlackDiscount(responseRatePlan.getPrice().getBlackDiscount());
            displayPriceBreakDown.setNonDiscountedPrice(responseRatePlan.getPrice().getBasePrice());
            if(CollectionUtils.isNotEmpty(responseRatePlan.getPrice().getApplicableCoupons())) {
                PriceCouponInfo priceCouponInfo = responseRatePlan.getPrice().getApplicableCoupons().get(0);
                BestCoupon bestCoupon = new BestCoupon();
                bestCoupon.setCouponCode(priceCouponInfo.getCouponCode());
                bestCoupon.setDescription(priceCouponInfo.getDescription());
                bestCoupon.setDiscountAmount(priceCouponInfo.getDiscount());
                bestCoupon.setSpecialPromoCoupon(priceCouponInfo.isSpecialPromoCoupon());
                bestCoupon.setType(priceCouponInfo.getType());
                displayPriceBreakDown.setCouponInfo(bestCoupon);
                displayPriceBreakDown.setCdfDiscount(priceCouponInfo.getDiscount());
            }

            DisplayFare displayFare = new DisplayFare();
            displayFare.setTotalRoomCount(responseRatePlan.getPrice().getTotalRoomCount());
            displayFare.setDisplayPriceBreakDown(displayPriceBreakDown);
            if(CollectionUtils.isNotEmpty(responseRatePlan.getMealPlans()) && responseRatePlan.getMealPlans().get(0) != null) {
                ExtraMeal mealPlanIncluded = new ExtraMeal();
                mealPlanIncluded.setCode(responseRatePlan.getMealPlans().get(0).getCode());
                mealPlanIncluded.setDesc(responseRatePlan.getMealPlans().get(0).getValue());
                searchWrapperHotelEntity.setMealPlanIncluded(mealPlanIncluded);
            }
            searchWrapperHotelEntity.setDisplayFare(displayFare);
        }
    }
}
