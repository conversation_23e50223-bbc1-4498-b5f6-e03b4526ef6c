package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.SortCriteria;
import com.mmt.hotels.clientgateway.response.filter.ContextDetails;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class FilterRequestTransformer extends BaseSearchRequestTransformer {

    public SearchWrapperInputRequest convertSearchRequest(FilterCountRequest filterCountRequest, CommonModifierResponse commonModifierResponse) {

        SearchWrapperInputRequest searchWrapperInputRequest =  super.convertSearchRequest(filterCountRequest, commonModifierResponse);

        if (filterCountRequest.getFeatureFlags() == null)
            filterCountRequest.setFeatureFlags(new FeatureFlags());
        buildResponseFilterFlags(searchWrapperInputRequest, filterCountRequest.getFeatureFlags());
        searchWrapperInputRequest.setContextDetails(buildContextDetails(filterCountRequest.getContextDetails()));
        //searchWrapperInputRequest.setSortCriteria(buildSortCriteria(filterCountRequest.getSortCriteria()));
        //searchWrapperInputRequest.setAppliedFilterMap(buildAppliedFilterMap(filterCountRequest.getFilterCriteria()));
        searchWrapperInputRequest.setBrand("GI");
        return searchWrapperInputRequest;
    }

    private com.mmt.hotels.filter.ContextDetails buildContextDetails(ContextDetails contextDetails) {
        if(contextDetails == null) {
            return null;
        }
        com.mmt.hotels.filter.ContextDetails contextDetailsHES = new com.mmt.hotels.filter.ContextDetails();
        contextDetailsHES.setContext(contextDetails.getContext());
        contextDetailsHES.setAltAccoIntent(contextDetails.isAltAccoIntent());
        return contextDetailsHES;
    }

    private ResponseFilterFlags buildResponseFilterFlags(SearchWrapperInputRequest searchWrapperInputRequest, FeatureFlags featureFlags) {
        ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
        responseFilterFlags.setStaticData(featureFlags.isStaticData());
        responseFilterFlags.setFlyfishSummaryRequired(featureFlags.isReviewSummaryRequired());
        responseFilterFlags.setWalletRequired(featureFlags.isWalletRequired());
        responseFilterFlags.setShortlistRequired(featureFlags.isShortlistingRequired());
        searchWrapperInputRequest.setNumberOfAddons(featureFlags.getNoOfAddons());
        searchWrapperInputRequest.setNumberOfCoupons(featureFlags.getNoOfCoupons());
        searchWrapperInputRequest.setNoOfPersuasions(featureFlags.getNoOfPersuasions());
        searchWrapperInputRequest.setNumberOfSoldOuts(featureFlags.getNoOfSoldouts());
        return responseFilterFlags;
    }


}
