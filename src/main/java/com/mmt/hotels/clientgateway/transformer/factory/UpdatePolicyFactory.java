package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.UpdatePolicyRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.UpdatePolicyRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.UpdatePolicyRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.UpdatePolicyRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatePolicyResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.ThankYouResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.android.UpdatePolicyResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.ThankYouResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.desktop.UpdatePolicyResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.ThankYouResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.ios.UpdatePolicyResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ThankYouResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpdatePolicyFactory {

    @Autowired
    private UpdatePolicyResponseTransformerPWA updatePolicyResponseTransformerPWA;

    @Autowired
    private UpdatePolicyResponseTransformerAndroid updatePolicyResponseTransformerAndroid;

    @Autowired
    private UpdatePolicyResponseTransformerDesktop updatePolicyResponseTransformerDesktop;

    @Autowired
    private UpdatePolicyResponseTransformerIOS updatePolicyResponseTransformerIOS;

    @Autowired
    private UpdatePolicyRequestTransformerPWA updatePolicyRequestTransformerPWA;

    @Autowired
    private UpdatePolicyRequestTransformerAndroid updatePolicyRequestTransformerAndroid;

    @Autowired
    private UpdatePolicyRequestTransformerDesktop updatePolicyRequestTransformerDesktop;

    @Autowired
    private UpdatePolicyRequestTransformerIOS updatePolicyRequestTransformerIOS;

    public UpdatePolicyResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return updatePolicyResponseTransformerDesktop;
        switch (client) {
            case "PWA":
            case "MSITE":
                return updatePolicyResponseTransformerPWA;
            case "DESKTOP":
                return updatePolicyResponseTransformerDesktop;
            case "ANDROID":
                return updatePolicyResponseTransformerAndroid;
            case "IOS":
                return updatePolicyResponseTransformerIOS;
        }
        return updatePolicyResponseTransformerDesktop;
    }

    public UpdatePolicyRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return updatePolicyRequestTransformerDesktop;
        switch (client) {
            case "PWA":
            case "MSITE":
                return updatePolicyRequestTransformerPWA;
            case "DESKTOP":
                return updatePolicyRequestTransformerDesktop;
            case "ANDROID":
                return updatePolicyRequestTransformerAndroid;
            case "IOS":
                return updatePolicyRequestTransformerIOS;
        }
        return updatePolicyRequestTransformerDesktop;
    }

}
