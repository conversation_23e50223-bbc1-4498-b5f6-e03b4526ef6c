package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.TitleData;
import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_DESKTOP;

@Component
public class StaticDetailResponseTransformerDesktop extends StaticDetailResponseTransformer {

    @Autowired
    private PropertyManager propertyManager;
    @Autowired
    private PolyglotHelper polyglotHelper;
    private ValueStaysTooltip valueStaysTooltipDom;
    private ValueStaysTooltip valueStaysTooltipIntl;
    private static final Logger logger = LoggerFactory.getLogger(StaticDetailResponseTransformerDesktop.class);

    @Value("${value.stays.title.icon}")
    private String valueStatysTitleIcon;
    @Value("${value.stays.title.icon.gcc}")
    private String valueStatysTitleIconGcc;
    @Value("${star.host.icon.desktop}")
    private String starHostIconDesktop;

    @Value("${mmt.value.stays.category.icon.url.desktop}")
    private String mmtValueStaysCategoryIconUrlDesktop;

    @PostConstruct
    public void init() {
        try {
            CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
            valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom();
            valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl();
            commonConfig.addPropertyChangeListener("mmtValueStaysTooltipDom", evt -> valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom());
            commonConfig.addPropertyChangeListener("mmtValueStaysTooltipIntl", evt -> valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl());
        } catch (Exception ex) {
            logger.error("error in fetching commonConfig pms properties", ex);
        }
    }

    @Override
    protected Map<String, String> buildCardTitleMap() {
        return null;
    }

    @Override
    protected void addTitleData(HotelResult hotelResult, String countryCode) {
        if (hotelResult == null) {
            return;
        }
        TitleData titleData = new TitleData();
        titleData.setTitleIcon(Utility.isGCC() ? valueStatysTitleIconGcc : valueStatysTitleIcon);
        ValueStaysTooltip valueStaysTooltip;
        if (StringUtils.isBlank(countryCode) || "IN".equalsIgnoreCase(countryCode)) {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipDom);
        } else {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipIntl);
        }
        polyglotHelper.translateValueStaysTooltip(valueStaysTooltip);
        titleData.setTooltip(valueStaysTooltip);
        hotelResult.setTitleData(titleData);
    }

    @Override
    protected String getLuxeIcon() {
        return LUXE_ICON_DESKTOP;
    }

    @Override
    public StaffInfo convertStaffInfo(StaffInfo staffInfo) {
        if(staffInfo != null && BooleanUtils.isTrue(staffInfo.getIsStarHost())){
            staffInfo.setStarHostIconUrl(starHostIconDesktop);
        }
        return staffInfo;
    }

    @Override
    public StaticDetailResponse convertStaticDetailResponse(HotelDetailWrapperResponse hotelDetailWrapperResponse, String client, StaticDetailRequest staticDetailRequest, CommonModifierResponse commonModifierResponse) {
        StaticDetailResponse staticDetailResponse = super.convertStaticDetailResponse(hotelDetailWrapperResponse, client, staticDetailRequest, commonModifierResponse);
        if (staticDetailResponse != null && staticDetailResponse.getHotelDetails() != null
                && StringUtils.isNotBlank(staticDetailResponse.getHotelDetails().getCategoryIcon())
                && StringUtils.isNotBlank(mmtValueStaysCategoryIconUrlDesktop) && isNotAHiddenGemIcon(staticDetailResponse)) {
            staticDetailResponse.getHotelDetails().setCategoryIcon(mmtValueStaysCategoryIconUrlDesktop);
        }
        return staticDetailResponse;
    }

    //If a property is a Hidden_Gem, Hidden Gem Icon need to be shown
    private boolean isNotAHiddenGemIcon(StaticDetailResponse staticDetailResponse) {
        if(staticDetailResponse !=null && staticDetailResponse.getHotelDetails() !=null && staticDetailResponse.getHotelDetails().getCategories()!=null) {
            return !staticDetailResponse.getHotelDetails().getCategories().contains(Constants.HIDDEN_GEM);
        }
        return true;
    }
}
