package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.MobLandingRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.MobLandingRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.MobLandingRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.MobLandingRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.MobLandingRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.MobLandingResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.MobLandingResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.MobLandingResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.MobLandingResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.MobLandingResponseTransformerPWA;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MobLandingFactory {

    @Autowired
    private MobLandingRequestTransformerPWA mobLandingRequestTransformerPWA;

    @Autowired
    private MobLandingResponseTransformerPWA mobLandingResponseTransformerPWA;
    
    @Autowired
    private MobLandingRequestTransformerDesktop mobLandingRequestTransformerDesktop;

    @Autowired
    private MobLandingResponseTransformerDesktop mobLandingResponseTransformerDesktop;
    
    @Autowired
    private MobLandingRequestTransformerAndroid mobLandingRequestTransformerAndroid;

    @Autowired
    private MobLandingResponseTransformerAndroid mobLandingResponseTransformerAndroid;
    
    @Autowired
    private MobLandingRequestTransformerIOS mobLandingRequestTransformerIOS;

    @Autowired
    private MobLandingResponseTransformerIOS mobLandingResponseTransformerIOS;

    public MobLandingRequestTransformer getRequestService(String client) {
    	if (StringUtils.isEmpty(client))
			return mobLandingRequestTransformerDesktop;
        switch(client) {
            case "PWA":
            case "MSITE":
                return mobLandingRequestTransformerPWA;
            case "DESKTOP": return mobLandingRequestTransformerDesktop;
            case "ANDROID": return mobLandingRequestTransformerAndroid;
            case "IOS": return mobLandingRequestTransformerIOS;
        }
        return mobLandingRequestTransformerDesktop;
    }

    public MobLandingResponseTransformer getResponseService(String client) {
    	if (StringUtils.isEmpty(client))
			return mobLandingResponseTransformerDesktop;
        switch(client){
            case "PWA":
            case "MSITE":
                return mobLandingResponseTransformerPWA;
            case "DESKTOP": return mobLandingResponseTransformerDesktop;
            case "ANDROID": return mobLandingResponseTransformerAndroid;
            case "IOS": return mobLandingResponseTransformerIOS;
        }
        return mobLandingResponseTransformerDesktop;
    }
}
