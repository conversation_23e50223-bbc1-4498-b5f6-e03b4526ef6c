package com.mmt.hotels.clientgateway.transformer.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.request.AddonsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AddonsReponseTransformer;

@Component
public class AddonsServiceFactory {

	@Autowired
	private AddonsReponseTransformer addonsReponseTransformer;
	
	@Autowired
	private AddonsRequestTransformer addonsRequestTransformer;
	
	public AddonsRequestTransformer getRequestService(String client){
		return addonsRequestTransformer;
	}
	
	public AddonsReponseTransformer getResponseService(String client){
		return addonsReponseTransformer;
	}

}
