package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.OrchUpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.OrchUpdatedPriceResponseTransformer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrchUpdatedPriceFactory {

    @Autowired
    private OrchUpdatedPriceRequestTransformer orchUpdatedPriceRequestTransformer;

    @Autowired
    private OrchUpdatedPriceResponseTransformer orchUpdatedPriceResponseTransformer;

    public OrchUpdatedPriceRequestTransformer getOrchRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return orchUpdatedPriceRequestTransformer;
        switch(client) {
            case "PWA": return orchUpdatedPriceRequestTransformer;
            case "DESKTOP": return orchUpdatedPriceRequestTransformer;
            case "ANDROID": return orchUpdatedPriceRequestTransformer;
            case "IOS": return orchUpdatedPriceRequestTransformer;
        }
        return orchUpdatedPriceRequestTransformer;
    }

    public OrchUpdatedPriceResponseTransformer getOrchResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return orchUpdatedPriceResponseTransformer;
        switch(client) {
            case "PWA": return orchUpdatedPriceResponseTransformer;
            case "DESKTOP": return orchUpdatedPriceResponseTransformer;
            case "ANDROID": return orchUpdatedPriceResponseTransformer;
            case "IOS": return orchUpdatedPriceResponseTransformer;
        }
        return orchUpdatedPriceResponseTransformer;
    }
}
