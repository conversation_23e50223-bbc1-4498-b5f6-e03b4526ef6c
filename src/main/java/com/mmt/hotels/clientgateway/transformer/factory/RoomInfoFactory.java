package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.RoomInfoResponseTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RoomInfoFactory {
    @Autowired
    private RoomInfoResponseTransformer roomInfoResponseTransformer;

    public RoomInfoResponseTransformer getResponseService(String client){
        return roomInfoResponseTransformer;
    }
}
