package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.model.request.TrafficSource;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.response.emi.EmiDetailsRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class UpdatedPriceRequestTransformer {


    public PriceByHotelsRequestBody convertUpdatedPriceRequest(UpdatePriceRequest updatedPriceRequest, CommonModifierResponse commonModifierResponse){

        if (updatedPriceRequest == null)
            return null;

        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();

        buildDeviceDetails(priceByHotelsRequestBody, updatedPriceRequest.getDeviceDetails());
        buildSearchCriteria(priceByHotelsRequestBody, updatedPriceRequest.getSearchCriteria());
        buildRequestDetails(priceByHotelsRequestBody, updatedPriceRequest.getRequestDetails());
        priceByHotelsRequestBody.setMobile(commonModifierResponse.getMobile());
        priceByHotelsRequestBody.setCdfContextId(commonModifierResponse.getCdfContextId());
        priceByHotelsRequestBody.setDomain(commonModifierResponse.getDomain());
        priceByHotelsRequestBody.setChannel(updatedPriceRequest.getRequestDetails().getChannel());
        priceByHotelsRequestBody.setPageContext("DETAIL");
        priceByHotelsRequestBody.setSiteDomain(updatedPriceRequest.getRequestDetails().getSiteDomain());

        priceByHotelsRequestBody.setEmiDetails(buildEmiDetail(updatedPriceRequest.getEmiDetail()));
        priceByHotelsRequestBody.setExperimentData(updatedPriceRequest.getExpData());
        if(commonModifierResponse.getExtendedUser() != null){
            priceByHotelsRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
            priceByHotelsRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
            priceByHotelsRequestBody.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
            priceByHotelsRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
        }
        priceByHotelsRequestBody.setBrand(commonModifierResponse.getBrand());
        priceByHotelsRequestBody.setGiHotelId(updatedPriceRequest.getSearchCriteria().getGiHotelId());
        priceByHotelsRequestBody.setVcId(updatedPriceRequest.getSearchCriteria().getVcId());

        return priceByHotelsRequestBody;

    }

    private EmiDetailsRequest buildEmiDetail(EMIDetail emiDetail) {

        if (emiDetail == null)
            return null;

        EmiDetailsRequest emiDetailCB = new EmiDetailsRequest();
        emiDetailCB.setBankId(emiDetail.getBankId());
        emiDetailCB.setBankName(emiDetail.getBankName());
        emiDetailCB.setPayOption(emiDetail.getPayOption());
        emiDetailCB.setTenure(emiDetail.getTenure());
        return emiDetailCB;

    }

    private void buildRequestDetails(PriceByHotelsRequestBody priceByHotelsRequestBody, RequestDetails requestDetails) {
        priceByHotelsRequestBody.setFunnelSource(requestDetails.getFunnelSource());
        priceByHotelsRequestBody.setIdContext(requestDetails.getIdContext());
        priceByHotelsRequestBody.setVisitorId(requestDetails.getVisitorId());
        priceByHotelsRequestBody.setVisitNumber(requestDetails.getVisitNumber() != null?
                String.valueOf(requestDetails.getVisitNumber()): "");
        priceByHotelsRequestBody.setLoggedIn(requestDetails.isLoggedIn());
        priceByHotelsRequestBody.setNotifCoupon(requestDetails.getNotifCoupon());
        priceByHotelsRequestBody.setPayMode(requestDetails.getPayMode());
        priceByHotelsRequestBody.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource()));


    }

    private TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {

    	if (trafficSource==null)
    		return null;
        TrafficSource trafficSourceCB = new TrafficSource();
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        return trafficSourceCB;

    }

    private void buildSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody, UpdatedPriceCriteria searchCriteria) {

        List<String> hotelIds = new ArrayList<String>();
        hotelIds.add(searchCriteria.getHotelId());
        priceByHotelsRequestBody.setHotelIds(hotelIds);
        priceByHotelsRequestBody.setCheckin(searchCriteria.getCheckIn());
        priceByHotelsRequestBody.setCheckout(searchCriteria.getCheckOut());
        priceByHotelsRequestBody.setCityCode(searchCriteria.getCityCode());
        priceByHotelsRequestBody.setCountryCode(searchCriteria.getCountryCode());
        priceByHotelsRequestBody.setLocationId(searchCriteria.getLocationId());
        priceByHotelsRequestBody.setLocationType(searchCriteria.getLocationType());
        priceByHotelsRequestBody.setCurrency(searchCriteria.getCurrency());
        priceByHotelsRequestBody.setRoomCriteria(buildRoomCriteria(searchCriteria.getRoomCriteria()));
        priceByHotelsRequestBody.setExtraInfo(buildExtraInfo(searchCriteria.getSearchType()));
        priceByHotelsRequestBody.setPersonalCorpBooking(searchCriteria.isPersonalCorpBooking());

    }

    private ExtraInfo buildExtraInfo(String searchType) {

        if (searchType == null)
            return null;

        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setSearchType(searchType);
        return extraInfo;
    }

    private List<RoomCriterion> buildRoomCriteria(List<UpdatedPriceRoomCriteria> roomCriteria) {

        if (CollectionUtils.isNotEmpty(roomCriteria)){
            List<RoomCriterion> roomCriterionList = new ArrayList<>();

            for (UpdatedPriceRoomCriteria roomCriteriaCG : roomCriteria){
                RoomCriterion roomCriterion = new RoomCriterion();
                roomCriterion.setMtKey(roomCriteriaCG.getMtKey());
                roomCriterion.setPricingKey(roomCriteriaCG.getPricingKey());
                roomCriterion.setRatePlanCode(roomCriteriaCG.getRatePlanCode());
                roomCriterion.setRoomCode(roomCriteriaCG.getRoomCode());
                roomCriterion.setRoomStayCandidates(buildRoomStayCandidates(roomCriteriaCG.getRoomStayCandidates()));
                roomCriterion.setSupplierCode(roomCriteriaCG.getSupplierCode());
                roomCriterionList.add(roomCriterion);
            }
            return roomCriterionList;
        }

        return null;


    }

    private List<com.mmt.hotels.model.request.RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {




        if(roomStayCandidates==null)
            return null;

        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates){
            com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;

    }

    private List<GuestCount> buildGuestCounts(com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG) {



        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidateCG.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;


    }

    private void buildDeviceDetails(PriceByHotelsRequestBody priceByHotelsRequestBody, DeviceDetails deviceDetails) {

        priceByHotelsRequestBody.setAppVersion(deviceDetails.getAppVersion());
        priceByHotelsRequestBody.setBookingDevice(deviceDetails.getBookingDevice());
        priceByHotelsRequestBody.setDeviceId(deviceDetails.getDeviceId());
        priceByHotelsRequestBody.setDeviceType(deviceDetails.getDeviceType());
        priceByHotelsRequestBody.setDeviceName(deviceDetails.getDeviceName());
    }
}
