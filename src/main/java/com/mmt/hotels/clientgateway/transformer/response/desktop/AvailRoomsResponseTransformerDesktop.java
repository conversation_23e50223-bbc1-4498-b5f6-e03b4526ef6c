package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class AvailRoomsResponseTransformerDesktop extends AvailRoomsResponseTransformer {
    private static final Logger LOGGER = LoggerFactory.getLogger(AvailRoomsResponseTransformerDesktop.class);

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        PersuasionResponse persuasion = new PersuasionResponse();
        StringBuilder persuasionAppliedText=new StringBuilder();
        if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ) {
            LOGGER.debug("loyalty_offer_message: {}", coupon.getLoyaltyOfferMessage());
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT), coupon.getLoyaltyOfferMessage()));
        }
        else{
            LOGGER.debug("Promo_Cash_Amount: {}",coupon.getHybridDiscounts().get("CTW"));
            int cashbackDiscountAmtRounded = (int) Math.round(coupon.getHybridDiscounts().get("CTW"));
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT),cashbackDiscountAmtRounded));
        }
        String iconType= StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage())? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
        persuasion.setPersuasionText(persuasionAppliedText.toString());
        persuasion.setHtml(true);
        persuasion.setIconType(iconType);
        persuasionMap.put (CASHBACK_HERO_OFFER_PERSUASION_NODE, persuasion);
    }
}
