package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;

import com.mmt.hotels.model.request.SearchWrapperInputRequest;

public abstract class ListingMapRequestTransformer extends BaseSearchRequestTransformer{

    public com.mmt.hotels.model.request.SearchWrapperInputRequest convertListingMapRequest(ListingMapRequest listingMapRequest, CommonModifierResponse commonModifierResponse) {
    	
    	SearchWrapperInputRequest listingMapRequestHES =  super.convertSearchRequest(listingMapRequest, commonModifierResponse);

    	buildMapDetails(listingMapRequestHES, listingMapRequest.getMapDetails());

        return listingMapRequestHES;
    }
    
    private void buildMapDetails(SearchWrapperInputRequest searchWrapperInputRequest, MapDetails mapDetails) {
		if (mapDetails == null)
			return;
		searchWrapperInputRequest.setLatSegments(mapDetails.getLatSegments());
		searchWrapperInputRequest.setLongSegments(mapDetails.getLngSegments());
		com.mmt.hotels.model.request.LatLngBounds latLngBoundsCB = new com.mmt.hotels.model.request.LatLngBounds();
		LatLngBounds latLngBounds = mapDetails.getLatLngBounds();
		latLngBoundsCB.setNELat(String.valueOf(latLngBounds.getNeLat()));
		latLngBoundsCB.setNELng(String.valueOf(latLngBounds.getNeLng()));
		latLngBoundsCB.setRadius(String.valueOf(mapDetails.getRadius()));
		latLngBoundsCB.setSWLat(String.valueOf(latLngBounds.getSwLat()));
		latLngBoundsCB.setSWLng(String.valueOf(latLngBounds.getSwLng()));
		searchWrapperInputRequest.setLatLngBounds(latLngBoundsCB);

		searchWrapperInputRequest.setMapRequestWithoutFilter(mapDetails.isMapRequestWithoutFilter());
		searchWrapperInputRequest.setLastPeekedOnMapHotelIds(mapDetails.getLastPeekedOnMapHotelIds());
	}
}
