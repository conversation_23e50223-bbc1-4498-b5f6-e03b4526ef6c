package com.mmt.hotels.clientgateway.transformer.response.android;

import com.mmt.hotels.clientgateway.response.corporate.UpdatePolicyResponse;
import com.mmt.hotels.clientgateway.transformer.response.UpdatePolicyResponseTransformer;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import org.springframework.stereotype.Component;

@Component
public class UpdatePolicyResponseTransformerAndroid extends UpdatePolicyResponseTransformer {

    @Override
    public UpdatePolicyResponse convertUpdatePolicyResponse(CorpPolicyUpdateResponse respHES) {
        return super.convertUpdatePolicyResponse(respHES);
    }
}
