package com.mmt.hotels.clientgateway.transformer.response;

import com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse;
import com.gommt.hotels.orchestrator.model.response.da.OccupancyDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.UpdatedPriceRoomCriteria;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPriceHelper;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class OrchUpdatedPriceResponseTransformer {

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private SearchRoomsPriceHelper searchRoomsPriceHelper;

    @Autowired
    private Utility utility;

    @Autowired
    private DateUtil dateUtil;

    public com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse convertUpdatePriceResponse(UpdatePriceRequest updatePriceRequest,
                                                                                                      com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse response,
                                                                                                      String expData,
                                                                                                      Object requestDetails,
                                                                                                      CommonModifierResponse commonModifierResponse) {

        if (response == null) {
            return null;
        }

        boolean myPartner = Objects.nonNull(commonModifierResponse) &&
                Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(),
                        commonModifierResponse.getExtendedUser().getAffiliateId());

        String askedCurrency = updatePriceRequest.getSearchCriteria().getCurrency();
        List<UpdatedPriceRoomCriteria> updatedPriceRoomCriteria = updatePriceRequest.getSearchCriteria().getRoomCriteria();

        String sellableType = CollectionUtils.isNotEmpty(updatedPriceRoomCriteria) ?
                updatedPriceRoomCriteria.get(0).getSellableType() : null;

        Integer roomCount = 1;
        if (CollectionUtils.isNotEmpty(updatedPriceRoomCriteria)) {
            roomCount = 0;
            for (UpdatedPriceRoomCriteria rc : updatedPriceRoomCriteria) {
                roomCount += rc.getRoomStayCandidates().size();
            }
        }

        boolean newPropertyOfferApplicable = commonModifierResponse != null &&
                MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ?
                utility.isExperimentOn(commonModifierResponse.getExpDataMap(),
                        ExperimentKeys.NEW_PROPERTY_OFFER.getKey()) : false;

        String checkIn = updatePriceRequest.getSearchCriteria().getCheckIn();
        String checkOut = updatePriceRequest.getSearchCriteria().getCheckOut();
        int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));

        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse updatePriceResponse = new com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse();

        // Convert orchestrator's CorpMetaInfo to corp approval info
//        updatePriceResponse.setCorpApprovalInfo(buildCorpApprovalInfo(response.getCorpMetaInfo(),
//                                                                     utility.isTCSV2FlowEnabled(commonModifierResponse.getExpDataMap())));

        // Convert orchestrator's PriceDetail to price map
        updatePriceResponse.setPriceMap(buildPriceMap(response, updatePriceRequest, expData, roomCount,
                askedCurrency, sellableType, los, myPartner,
                newPropertyOfferApplicable));

        if (MapUtils.isNotEmpty(updatePriceResponse.getPriceMap())) {
            updatePriceResponse.setDefaultPriceKey(getDefaultPriceKey(updatePriceResponse.getPriceMap()));
        }

        updatePriceResponse.setCurrency(response.getCurrency());

        return updatePriceResponse;
    }

//    private CorpApprovalInfo buildCorpApprovalInfo(UpdatePriceResponse.CorpMetaInfo corpMetaInfo, boolean isTCSV2FlowEnabled) {
//        if (corpMetaInfo == null) {
//            return null;
//        }
//
//        // TODO: Implement conversion from orchestrator CorpMetaInfo to corp approval info
//        // This should mirror the commonResponseTransformer.buildCorpApprovalInfo logic
//        CorpMetaInfo entityCorpMetaInfo = convertToEntityCorpMetaInfo(corpMetaInfo);
//        if (entityCorpMetaInfo == null) {
//            return null;
//        }
//        return commonResponseTransformer.buildCorpApprovalInfo(entityCorpMetaInfo, isTCSV2FlowEnabled);
//    }

//    private CorpMetaInfo convertToEntityCorpMetaInfo(UpdatePriceResponse.CorpMetaInfo orchCorpMetaInfo) {
//        // TODO: Convert orchestrator CorpMetaInfo to entity service CorpMetaInfo
//        // This is a temporary placeholder - needs proper conversion logic
//        if (orchCorpMetaInfo == null) {
//            return null;
//        }
//        // Return null for now - this needs proper implementation
//        return null;
//    }

    private Map<String, TotalPricing> buildPriceMap(UpdatePriceResponse response,
                                                    UpdatePriceRequest updatePriceRequest,
                                                    String expData,
                                                    Integer roomCount,
                                                    String askedCurrency,
                                                    String sellableType,
                                                    int los,
                                                    boolean myPartner,
                                                    boolean newPropertyOfferApplicable) {

        // TODO: Convert orchestrator's PriceDetail and PriceDetailList to price map
        // This should mirror the commonResponseTransformer.getPriceMap logic but work with orchestrator's PriceDetail structure

        // For now, return empty map - this needs proper implementation
        // The conversion should transform:
        // - response.getPriceDetail() -> main price entry
        // - response.getPriceDetailList() -> alternative price entries
        // - Handle response.getMarkUpDetails() for partner markup
        // - Handle response.isAltAcco() for alternative accommodation

        DisplayPriceBreakDown displayPriceBreakDown = convertToEntityDisplayPriceBreakDown(response.getPriceDetail());
        List<DisplayPriceBreakDown> displayPriceBreakDownList = convertToEntityDisplayPriceBreakDownList(response.getPriceDetailList());
        String funnelSource = updatePriceRequest.getRequestDetails().getFunnelSource();
        Map<String,String> expDataMap = utility.getExpDataMap(expData);
//        MarkUpDetails markUpDetails = convertToEntityMarkUpDetails(response.getMarkUpDetails());
        com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails occupancyDetails = new com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails();
        occupancyDetails.setNumberOfRooms(roomCount);
        occupancyDetails.setPricingKey(response.getPriceDetail().getPricingKey());

        return searchRoomsPriceHelper.getPriceMap(response.getPriceDetail(), expDataMap, occupancyDetails, askedCurrency, sellableType, los, false, "",
                utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource),
                false, // groupBookingPrice - TODO: check from hotelDetails
                myPartner, Constants.EMPTY_STRING);
    }

    private DisplayPriceBreakDown convertToEntityDisplayPriceBreakDown(Object priceDetail) {
        // TODO: Convert orchestrator PriceDetail to entity DisplayPriceBreakDown
        // This is a placeholder - needs proper conversion logic
        return null;
    }

    private List<DisplayPriceBreakDown> convertToEntityDisplayPriceBreakDownList(Object priceDetailList) {
        // TODO: Convert orchestrator List<PriceDetail> to entity List<DisplayPriceBreakDown>
        // This is a placeholder - needs proper conversion logic
        return null;
    }

//    private MarkUpDetails convertToEntityMarkUpDetails(PriceBreakDownResponse.MarkUpDetails markUpDetails) {
//        // TODO: Convert orchestrator MarkUpDetails to entity MarkUpDetails
//        // This is a placeholder - needs proper conversion logic
//        return null;
//    }

    private String getDefaultPriceKey(Map<String, TotalPricing> priceMap) {
        for (String key : priceMap.keySet()) {
            return key;
        }
        return null;
    }
}
