package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.request.SearchRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchRoomsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.SearchRoomsRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.SearchRoomsRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SearchRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchRoomsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchRoomsResponseTransformerPWA;

@Component
public class SearchRoomsFactory {
	
	@Autowired
	private SearchRoomsRequestTransformerPWA searchRoomsRequestTransformerPWA;
	
	@Autowired
	private SearchRoomsResponseTransformerPWA searchRoomsResponseTransformerPWA;
	
	@Autowired
	private SearchRoomsRequestTransformerDesktop searchRoomsRequestTransformerDesktop;
	
	@Autowired
	private SearchRoomsResponseTransformerDesktop searchRoomsResponseTransformerDesktop;
	
	@Autowired
	private SearchRoomsRequestTransformerAndroid searchRoomsRequestTransformerAndroid;
	
	@Autowired
	private SearchRoomsResponseTransformerAndroid searchRoomsResponseTransformerAndroid;
	
	@Autowired
	private SearchRoomsRequestTransformerIOS searchRoomsRequestTransformerIOS;
	
	@Autowired
	private SearchRoomsResponseTransformerIOS searchRoomsResponseTransformerIOS;

	@Autowired
	private OrchSearchRoomsResponseTransformerIOS orchSearchRoomsResponseTransformerIOS;

	@Autowired
	private OrchSearchRoomsResponseTransformerAndroid orchSearchRoomsResponseTransformerAndroid;

	@Autowired
	private OrchSearchRoomsResponseTransformerDesktop orchSearchRoomsResponseTransformerDesktop;

	@Autowired
	private OrchSearchRoomsResponseTransformerPWA orchSearchRoomsResponseTransformerPWA;

	public SearchRoomsRequestTransformer getRequestService(String client) {
		if (StringUtils.isEmpty(client))
			return searchRoomsRequestTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return searchRoomsRequestTransformerPWA;
			case "DESKTOP": return searchRoomsRequestTransformerDesktop;
			case "ANDROID": return searchRoomsRequestTransformerAndroid;
			case "IOS": return searchRoomsRequestTransformerIOS;
		}
		return searchRoomsRequestTransformerDesktop;
	}

	public SearchRoomsResponseTransformer getResponseService(String client) {
		if (StringUtils.isEmpty(client))
			return searchRoomsResponseTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return searchRoomsResponseTransformerPWA;
			case "DESKTOP": return searchRoomsResponseTransformerDesktop;
			case "ANDROID": return searchRoomsResponseTransformerAndroid;
			case "IOS": return searchRoomsResponseTransformerIOS;	
		}
		return searchRoomsResponseTransformerDesktop;
	}

	public OrchSearchRoomsResponseTransformer getSearchRoomsResponseService(String client) {
		if (StringUtils.isEmpty(client))
			return orchSearchRoomsResponseTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return orchSearchRoomsResponseTransformerPWA;
			case "DESKTOP": return orchSearchRoomsResponseTransformerDesktop;
			case "ANDROID": return orchSearchRoomsResponseTransformerAndroid;
			case "IOS": return orchSearchRoomsResponseTransformerIOS;
		}
		return orchSearchRoomsResponseTransformerDesktop;
	}
}
