package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class OrchSearchRoomsResponseTransformerAndroid extends OrchSearchRoomsResponseTransformer {

    @Autowired
    private Utility utility;
    @Override
    public SearchRoomsResponse convertSearchRoomsResponse(SearchRoomsRequest searchRoomsRequest, HotelDetailsResponse hotelDetailsResponse, Map<String, String> expData, List<RoomStayCandidate> roomStayCandidates, SearchCriteria searchRoomsCriteria,
                                                          List<Filter> filterCriteria, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {
        SearchRoomsResponse searchRoomsResponse = super.convertSearchRoomsResponse(searchRoomsRequest, hotelDetailsResponse, expData, roomStayCandidates, searchRoomsCriteria,
                filterCriteria, requestDetails, commonModifierResponse);
        removeImpInfo(searchRoomsResponse);
        return searchRoomsResponse;
    }

    private void removeImpInfo(SearchRoomsResponse searchRoomsResponse) {
        if (searchRoomsResponse!=null && searchRoomsResponse.getImpInfo()!=null)
            searchRoomsResponse.setImpInfo(null);
    }

    @Override
    protected PersuasionObject createTopRatedPersuasion() {
        return createTopRatedPersuasionForMoblie();
    }
    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    protected GroupRatePlanFilter buildGroupFilterForDevice(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation) {
        return buildStaycationFilter(groupRatePlanFilterConfMap, filterCriteria, staycation);
    }
} 