package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gi.hotels.model.response.staticdata.Categorized;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.TitleData;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class OrchStaticDetailsResponseTransformerDesktop extends OrchStaticDetailResponseTransformer {

    private static final Logger logger = LoggerFactory.getLogger(OrchStaticDetailsResponseTransformerDesktop.class);

    @Autowired
    private PropertyManager propertyManager;
    @Autowired
    private PolyglotHelper polyglotHelper;
    private ValueStaysTooltip valueStaysTooltipDom;
    private ValueStaysTooltip valueStaysTooltipIntl;

    @Value("${value.stays.title.icon}")
    private String valueStatysTitleIcon;
    @Value("${value.stays.title.icon.gcc}")
    private String valueStatysTitleIconGcc;
    @Value("${star.host.icon.desktop}")
    private String starHostIconDesktop;

    @Value("${mmt.value.stays.category.icon.url.desktop}")
    private String mmtValueStaysCategoryIconUrlDesktop;

    @PostConstruct
    public void init() {
        try {
            CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
            valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom();
            valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl();
            commonConfig.addPropertyChangeListener("mmtValueStaysTooltipDom", evt -> valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom());
            commonConfig.addPropertyChangeListener("mmtValueStaysTooltipIntl", evt -> valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl());
        } catch (Exception ex) {
            logger.error("error in fetching commonConfig pms properties", ex);
        }
    }

    @Override
    public Map<String, String> buildCardTitleMap() {
        return null;
    }

    @Override
    public void addTitleData(HotelResult hotelResult, String countryCode) {
        if (hotelResult == null) {
            return;
        }
        TitleData titleData = new TitleData();
        titleData.setTitleIcon(Utility.isGCC() ? valueStatysTitleIconGcc : valueStatysTitleIcon);
        ValueStaysTooltip valueStaysTooltip;
        if (StringUtils.isBlank(countryCode) || "IN".equalsIgnoreCase(countryCode)) {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipDom);
        } else {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipIntl);
        }
        polyglotHelper.translateValueStaysTooltip(valueStaysTooltip);
        titleData.setTooltip(valueStaysTooltip);
        hotelResult.setTitleData(titleData);
    }

    @Override
    public String getLuxeIcon() {
        return LUXE_ICON_DESKTOP;
    }

    @Override
    public StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if (staffInfo != null && BooleanUtils.isTrue(staffInfo.getIsStarHost())) {
            staffInfo.setStarHostIconUrl(starHostIconDesktop);
        }
        StaffInfo staffInfoCg = super.convertStaffInfo(staffInfo);
        return staffInfoCg;
    }

    /**
     * Convert HotelStaticContentResponse to StaticDetailResponse with desktop-specific modifications
     * This method is adapted for orchestrator pattern using HotelStaticContentResponse
     */
    @Override
    public StaticDetailResponse convertStaticDetailResponse(StaticDetailRequest staticDetailRequest, com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse hotelStaticContentResponse,
                                                           CommonModifierResponse commonModifierResponse) {
        // Use the parent's main mapping method
        StaticDetailResponse staticDetailResponse = super.convertStaticDetailResponse(
                staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        if (staticDetailResponse != null && staticDetailResponse.getHotelDetails() != null
                && StringUtils.isNotBlank(staticDetailResponse.getHotelDetails().getCategoryIcon())
                && StringUtils.isNotBlank(mmtValueStaysCategoryIconUrlDesktop) && isNotAHiddenGemIcon(staticDetailResponse)) {
            staticDetailResponse.getHotelDetails().setCategoryIcon(mmtValueStaysCategoryIconUrlDesktop);
        }
        //prependHighlightedAmenitiesForDesktop(staticDetailResponse.getHotelDetails());
        //setPopularAmenitiesNull(staticDetailResponse.getHotelDetails());
        return staticDetailResponse;
    }

    /**
     * If a property is a Hidden_Gem, Hidden Gem Icon need to be shown
     */
    private boolean isNotAHiddenGemIcon(StaticDetailResponse staticDetailResponse) {
        if (staticDetailResponse != null && staticDetailResponse.getHotelDetails() != null 
                && staticDetailResponse.getHotelDetails().getCategories() != null) {
            return !staticDetailResponse.getHotelDetails().getCategories().contains(Constants.HIDDEN_GEM);
        }
        return true;
    }


    private void prependHighlightedAmenitiesForDesktop(HotelResult hotelResult) {
        if (hotelResult.getAmenitiesGI() != null
                && CollectionUtils.isNotEmpty(hotelResult.getAmenitiesGI().getCategorized())
                && CollectionUtils.isNotEmpty(hotelResult.getHighlightedAmenities())) {
            Categorized highlightedAmenitiesCategory = new Categorized();
            highlightedAmenitiesCategory.setLabel(HIGHLIGHTED_AMENITIES_TITLE);
            highlightedAmenitiesCategory.setData(hotelResult.getHighlightedAmenities());
            hotelResult.getAmenitiesGI().getCategorized().add(0, highlightedAmenitiesCategory);
        }
    }

    private void setPopularAmenitiesNull(HotelResult hotelResult) {
        if (hotelResult.getAmenitiesGI() != null) {
            hotelResult.getAmenitiesGI().setPopular(null);
        }

    }


}
