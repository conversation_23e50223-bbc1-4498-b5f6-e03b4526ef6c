package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.AvailRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.BookingModRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.BookingModResponseTransformer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BookingModificationFactory {

    @Autowired
    BookingModRequestTransformer bookingModRequestTransformer;

    @Autowired
    BookingModResponseTransformer bookingModResponseTransformer;

    public BookingModRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return bookingModRequestTransformer;
        switch(client.toUpperCase()) {
            case "PWA":
            case "DESKTOP":
            case "ANDROID":
            case "IOS":
            default:
                return bookingModRequestTransformer;
        }
    }

    public BookingModResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return bookingModResponseTransformer;
        switch(client){
            case "PWA":
            case "DESKTOP":
            case "ANDROID":
            case "IOS":
            default:
                return bookingModResponseTransformer;
        }
    }
}
