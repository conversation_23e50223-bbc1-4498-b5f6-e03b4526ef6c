package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.EMIRequestTransformer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EMIFactory {
    @Autowired
    private EMIRequestTransformer emiRequestTransformer;

    public EMIRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return emiRequestTransformer;
        switch (client) {
            case "PWA":
                return emiRequestTransformer;
            case "DESKTOP":
                return emiRequestTransformer;
            case "ANDROID":
                return emiRequestTransformer;
            case "IOS":
                return emiRequestTransformer;
        }
        return emiRequestTransformer;
    }

}
