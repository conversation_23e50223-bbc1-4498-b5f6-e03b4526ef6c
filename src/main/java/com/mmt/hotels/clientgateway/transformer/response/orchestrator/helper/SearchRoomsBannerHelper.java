package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomBanner;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.AMENITY_1;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.AMENITY_2;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.AMENITY_3;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.ROOM_NAME;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SELECT_ROOM_1_AMENITIES_BANNER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SELECT_ROOM_2_AMENITIES_BANNER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SELECT_ROOM_3_AMENITIES_BANNER;

@Component
public class SearchRoomsBannerHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsBannerHelper.class);

    @Autowired
    private PolyglotService polyglotService;

    public SelectRoomBanner buildBanner(SearchRoomsResponse searchRoomsResponse, HotelDetails hotelDetails) {
        if (hotelDetails != null && searchRoomsResponse != null) {
            SelectRoomBanner banner = new SelectRoomBanner();
            
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) && 
                CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
                
                Optional<Rooms> maxRoom = findRoomWithMostAmenities(hotelDetails.getRooms(), 
                        searchRoomsResponse.getExactRooms().get(0).getRoomCode());
                        
                if (maxRoom.isPresent()) {
                    makeBanner(banner, maxRoom.get());
                    return banner;
                }
            } 
            else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getOccupancyRooms()) && 
                     CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
                
                Optional<Rooms> maxRoom = findRoomWithMostAmenities(hotelDetails.getRooms(), 
                        searchRoomsResponse.getOccupancyRooms().get(0).getRoomCode());
                        
                if (maxRoom.isPresent()) {
                    makeBanner(banner, maxRoom.get());
                    return banner;
                }
            } 
            else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) && 
                     CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
                
                Optional<Rooms> maxRoom = findRoomWithMostAmenitiesFromCombos(hotelDetails.getRoomCombos(), 
                        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRoomCode());
                        
                if (maxRoom.isPresent() && 
                    searchRoomsResponse.getRecommendedCombos().get(0).getRooms().stream()
                            .noneMatch(e -> e.getRoomCode().equalsIgnoreCase(maxRoom.get().getCode()))) {
                    makeBanner(banner, maxRoom.get());
                    return banner;
                }
            }
        }
        return null;
    }

    private Optional<Rooms> findRoomWithMostAmenities(List<Rooms> rooms, String excludeRoomCode) {
        return rooms.stream()
                .filter(Objects::nonNull)
                .filter(room -> room.getRoomInfo() != null &&
                        CollectionUtils.isNotEmpty(room.getRoomInfo().getAmenities()) &&
                        room.getCode() != null &&
                        !room.getCode().equalsIgnoreCase(excludeRoomCode))
                .max(Comparator.comparingInt(room -> 
                        room.getRoomInfo().getAmenities() == null ? 0 : room.getRoomInfo().getAmenities().size()));
    }

    private Optional<Rooms> findRoomWithMostAmenitiesFromCombos(List<RoomCombo> roomCombos, String excludeRoomCode) {
        return roomCombos.stream()
                .filter(Objects::nonNull)
                .filter(combo -> combo.getRooms() != null)
                .flatMap(combo -> combo.getRooms().stream())
                .filter(Objects::nonNull)
                .filter(room -> room.getRoomInfo() != null &&
                        CollectionUtils.isNotEmpty(room.getRoomInfo().getAmenities()) &&
                        room.getCode() != null &&
                        !room.getCode().equalsIgnoreCase(excludeRoomCode))
                .max(Comparator.comparingInt(room -> 
                        room.getRoomInfo().getAmenities() == null ? 0 : room.getRoomInfo().getAmenities().size()));
    }

    private void makeBanner(SelectRoomBanner banner, Rooms roomDetails) {
        String text = StringUtils.EMPTY;
        try {
            List<AmenityGroup> amenities = roomDetails.getRoomInfo().getAmenities();
            if (CollectionUtils.isNotEmpty(amenities)) {
                if (amenities.size() == 1) {
                    text = polyglotService.getTranslatedData(SELECT_ROOM_1_AMENITIES_BANNER)
                            .replace(ROOM_NAME, getRoomName(roomDetails))
                            .replace(AMENITY_1, amenities.get(0).getName());
                } else if (amenities.size() == 2) {
                    text = polyglotService.getTranslatedData(SELECT_ROOM_2_AMENITIES_BANNER)
                            .replace(ROOM_NAME, getRoomName(roomDetails))
                            .replace(AMENITY_1, amenities.get(0).getName())
                            .replace(AMENITY_2, amenities.get(1).getName());
                } else if (amenities.size() >= 3) {
                    text = polyglotService.getTranslatedData(SELECT_ROOM_3_AMENITIES_BANNER)
                            .replace(ROOM_NAME, getRoomName(roomDetails))
                            .replace(AMENITY_1, amenities.get(0).getName())
                            .replace(AMENITY_2, amenities.get(1).getName())
                            .replace(AMENITY_3, amenities.get(2).getName());
                }
            }
        } catch (Exception e) {
            LOGGER.error("Search-rooms banner could not be made", e);
        }
        
        if (StringUtils.isNotBlank(text)) {
            banner.setTitle(text);
            banner.setRedirectType(Constants.SELECT_ROOM_BANNER_TYPE);
            banner.setRedirectLink(roomDetails.getCode());
            banner.setIconUrl(Constants.SELECT_ROOM_BANNER_ICON_URL);
            banner.setBgColor(Constants.SELECT_ROOM_BANNER_BG_COLOR);
        }
    }

    private String getRoomName(Rooms roomDetails) {
        if (roomDetails.getRoomInfo() != null && 
            StringUtils.isNotBlank(roomDetails.getRoomInfo().getRoomName())) {
            return roomDetails.getRoomInfo().getRoomName();
        }
        return "Room";
    }
} 