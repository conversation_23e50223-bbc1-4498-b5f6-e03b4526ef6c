package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.AffiliateResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.AffiliateResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.AffiliateResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.AffiliateResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.AffiliateResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AffiliateFactory {

    @Autowired
    private AffiliateResponseTransformerPWA affiliateResponseTransformerPWA;

    @Autowired
    private AffiliateResponseTransformerAndroid affiliateResponseTransformerAndroid;

    @Autowired
    private AffiliateResponseTransformerDesktop affiliateResponseTransformerDesktop;

    @Autowired
    private AffiliateResponseTransformerIOS affiliateResponseTransformerIOS;

    public AffiliateResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return affiliateResponseTransformerDesktop;
        switch (client) {
            case "PWA":
            case "MSITE":
                return affiliateResponseTransformerPWA;
            case "DESKTOP":
                return affiliateResponseTransformerDesktop;
            case "ANDROID":
                return affiliateResponseTransformerAndroid;
            case "IOS":
                return affiliateResponseTransformerIOS;
        }
        return affiliateResponseTransformerDesktop;
    }
}
