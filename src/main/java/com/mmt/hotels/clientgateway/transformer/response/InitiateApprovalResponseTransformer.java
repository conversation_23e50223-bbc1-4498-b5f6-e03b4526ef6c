package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InitiateApprovalResponseTransformer {

    public InitApprovalResponse processResponse(CGServerResponse initAppResponseHES){

        InitApprovalResponse initApprovalResponse = new InitApprovalResponse();
        if(initAppResponseHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(initAppResponseHES.getResponseErrors().getErrorList())){
            Error error  = new Error(initAppResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(), initAppResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            initApprovalResponse.setError(error);
        }else if (initAppResponseHES.getAdditionalProperties().get("error") != null){
            Error error  = new Error((String) initAppResponseHES.getAdditionalProperties().get("status").toString(), (String) initAppResponseHES.getAdditionalProperties().get("message"));
            initApprovalResponse.setError(error);
        }else {
            initApprovalResponse = new Gson().fromJson(new Gson().toJson(initAppResponseHES.getAdditionalProperties()), InitApprovalResponse.class);
        }

        return  initApprovalResponse;
    }



}
