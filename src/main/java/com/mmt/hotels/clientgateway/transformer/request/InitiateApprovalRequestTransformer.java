package com.mmt.hotels.clientgateway.transformer.request;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.payment.SpecialRequestCategory;
import com.mmt.hotels.clientgateway.request.payment.TravellerDetail;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.model.request.corporate.ApprovalInfo;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.request.payment.TripTag;
import com.mmt.hotels.model.request.payment.WorkflowRequest;
import com.mmt.hotels.model.response.pricing.SpecialRequest;

import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class InitiateApprovalRequestTransformer {

    @Autowired
    CommonHelper commonHelper;

    public InitApprovalRequest convertInitApprovalRequest(com.mmt.hotels.clientgateway.request.InitApprovalRequest initApprovalRequest,
                                                          Map<String, String> headers, String correlationKey) {


        InitApprovalRequest hesInitApprovalRequest = new InitApprovalRequest();

        hesInitApprovalRequest.setTxnKey(initApprovalRequest.getTxnKey());
        hesInitApprovalRequest.setMyBizFlowIdentifier(initApprovalRequest.getMyBizFlowIdentifier());
        hesInitApprovalRequest.setRequisitionID(initApprovalRequest.getRequisitionID());
        populateTripTag(initApprovalRequest, hesInitApprovalRequest);
        populateTravellerDetails(initApprovalRequest, hesInitApprovalRequest);
        populateSpecialRequest(initApprovalRequest, hesInitApprovalRequest);
        populateWorkflowData(initApprovalRequest, hesInitApprovalRequest);
        populateUserDetails(initApprovalRequest, headers, hesInitApprovalRequest, correlationKey);

        return hesInitApprovalRequest;
    }

    private void populateUserDetails(com.mmt.hotels.clientgateway.request.InitApprovalRequest initApprovalRequest, Map<String, String> headers, InitApprovalRequest hesInitApprovalRequest, String correlationKey) {

        try{

            if (initApprovalRequest != null) {

                String mmtAuth = commonHelper.getAuthToken(headers);

                UserServiceResponse userServiceRsp = commonHelper.getUserDetails(mmtAuth, "","","", correlationKey, Constants.CORP_ID_CONTEXT, null,null,headers);
                if(userServiceRsp!=null && userServiceRsp.getResult()!=null
                        && userServiceRsp.getResult().getExtendedUser()!=null) {

                    ExtendedUser user = userServiceRsp.getResult().getExtendedUser();

                    hesInitApprovalRequest.setCorpUserId(user.getProfileId());
                    hesInitApprovalRequest.setCorrelationKey(correlationKey);
                    hesInitApprovalRequest.setUuid(user.getUuid());
                    hesInitApprovalRequest.setMmtAuth(mmtAuth);


                }

            }

        }catch (Exception e){

        }

    }

    private void populateWorkflowData(com.mmt.hotels.clientgateway.request.InitApprovalRequest initApprovalRequest, InitApprovalRequest hesInitApprovalRequest) {

        hesInitApprovalRequest.setApprovalInfo(new ApprovalInfo());
        hesInitApprovalRequest.getApprovalInfo().setEmployeeComment(initApprovalRequest.getEmployeeComment());
        hesInitApprovalRequest.getApprovalInfo().setWorkflowStatus(initApprovalRequest.getWorkflowStatus());
    }

    private void populateSpecialRequest(com.mmt.hotels.clientgateway.request.InitApprovalRequest initApprovalRequest, InitApprovalRequest hesInitApprovalRequest) {

        if(initApprovalRequest.getSpecialRequest() != null && CollectionUtils.isNotEmpty(initApprovalRequest.getSpecialRequest().getCategories()) ){
            hesInitApprovalRequest.setSpecialRequest(new SpecialRequest());
            hesInitApprovalRequest.getSpecialRequest().setDisclaimer(initApprovalRequest.getSpecialRequest().getDisclaimer());
            hesInitApprovalRequest.getSpecialRequest().setCategories(new ArrayList<>());
            for(SpecialRequestCategory category : initApprovalRequest.getSpecialRequest().getCategories()){
                com.mmt.hotels.model.response.pricing.SpecialRequestCategory sRC = new com.mmt.hotels.model.response.pricing.SpecialRequestCategory();
                BeanUtils.copyProperties(category, sRC);
                if(CollectionUtils.isNotEmpty(category.getSubCategories())) {
                    sRC.setSubCategories(new ArrayList<>());
                    for (SpecialRequestCategory subCat : category.getSubCategories()) {
                        com.mmt.hotels.model.response.pricing.SpecialRequestCategory sRCSub = new com.mmt.hotels.model.response.pricing.SpecialRequestCategory();
                        BeanUtils.copyProperties(subCat,sRCSub);
                        sRC.getSubCategories().add(sRCSub);
                    }
                }
                hesInitApprovalRequest.getSpecialRequest().getCategories().add(sRC);
            }
        }
    }

    private void populateTravellerDetails(com.mmt.hotels.clientgateway.request.InitApprovalRequest initApprovalRequest, InitApprovalRequest hesInitApprovalRequest) {

        List<TravelerDetail> trvlrList = new ArrayList<>();
        for(com.mmt.hotels.clientgateway.request.payment.TravellerDetail trvlr : initApprovalRequest.getTravellerDetailsList()){
            TravelerDetail newTrvlr = new TravelerDetail();
            BeanUtils.copyProperties(trvlr,newTrvlr);
            trvlrList.add(newTrvlr);
        }
        hesInitApprovalRequest.setTravelerDetailsList(trvlrList);
    }

    private void populateTripTag(com.mmt.hotels.clientgateway.request.InitApprovalRequest initApprovalRequest, InitApprovalRequest hesInitApprovalRequest) {

        if (initApprovalRequest.getTripTag()!=null){

            String tripTag = new Gson().toJson(initApprovalRequest.getTripTag());
            hesInitApprovalRequest.setTripTag(new TripTag());
            hesInitApprovalRequest.setTripTag(new Gson().fromJson(tripTag, TripTag.class));
        }
    }
}
