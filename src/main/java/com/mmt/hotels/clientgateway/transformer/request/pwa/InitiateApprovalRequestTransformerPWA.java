package com.mmt.hotels.clientgateway.transformer.request.pwa;

import com.mmt.hotels.clientgateway.transformer.request.InitiateApprovalRequestTransformer;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class InitiateApprovalRequestTransformerPWA extends InitiateApprovalRequestTransformer {

    @Override
    public InitApprovalRequest convertInitApprovalRequest(com.mmt.hotels.clientgateway.request.InitApprovalRequest initApprovalRequest, Map<String, String> headers, String correlationKey) {
        return super.convertInitApprovalRequest(initApprovalRequest, headers, correlationKey);
    }
}
