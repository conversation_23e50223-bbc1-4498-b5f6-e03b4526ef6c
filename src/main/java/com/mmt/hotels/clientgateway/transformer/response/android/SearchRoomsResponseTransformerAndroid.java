package com.mmt.hotels.clientgateway.transformer.response.android;

import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;

import java.util.*;

@Component
public class SearchRoomsResponseTransformerAndroid extends SearchRoomsResponseTransformer{

	@Autowired
	private Utility utility;
    @Override
    public SearchRoomsResponse convertSearchRoomsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, HotelImage hotelImage, Map<String, String> expData, List<RoomStayCandidate> roomStayCandidates, SearchCriteria searchRoomsCriteria, List<Filter> filterCriteria, RequestDetails requestDetails) {
        SearchRoomsResponse searchRoomsResponse = super.convertSearchRoomsResponse(roomDetailsResponse,hotelsRoomInfoResponseEntity,hotelImage,expData,roomStayCandidates,searchRoomsCriteria,filterCriteria,requestDetails);
        removeImpInfo(searchRoomsResponse);
        //addPriceToolTip(searchRoomsResponse);
        return searchRoomsResponse;
    }

    private void removeImpInfo(SearchRoomsResponse searchRoomsResponse) {
        /* HTL-30428 Remove ImpInfo as per Product Teams request - only for Android for now */
        if (searchRoomsResponse!=null && searchRoomsResponse.getImpInfo()!=null)
            searchRoomsResponse.setImpInfo(null);
    }

    @Override
    protected PersuasionObject createTopRatedPersuasion() {
        return createTopRatedPersuasionForMoblie();
    }
    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    protected GroupRatePlanFilter buildGroupFilterForDevice(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation) {
        return buildStaycationFilter(groupRatePlanFilterConfMap, filterCriteria, staycation);
    }
}
