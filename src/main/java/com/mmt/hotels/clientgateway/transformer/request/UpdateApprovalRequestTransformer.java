package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.corporate.UpdateApprovalRequest;
import com.mmt.hotels.model.request.payment.ApprovalAction;
import com.mmt.hotels.model.request.payment.DeviceDetails;
import com.mmt.hotels.model.request.payment.OsType;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class UpdateApprovalRequestTransformer {

  @Autowired
  private CorporateHelper corporateHelper;

  public UpdateApprovalRequest convertUpdateApprovalRequest(com.mmt.hotels.clientgateway.request.UpdateApprovalRequest
                                                                updateApprovalRequest, String client,
                                                            String correlationKey, Map<String, String> httpHeaderMap)
      throws Exception {
    UpdateApprovalRequest updateApprovalRequestHES = new UpdateApprovalRequest();
    updateApprovalRequestHES.setDeviceDetails(getDeviceDetails(client, updateApprovalRequest));
    updateApprovalRequestHES.setCorrelationKey(correlationKey);
    updateApprovalRequestHES.setComment(updateApprovalRequest.getComment());
    updateApprovalRequestHES.setAction(getApprovalAction(updateApprovalRequest));
    updateApprovalRequestHES
        .setApproverUuid(getApproverUuid(updateApprovalRequestHES, correlationKey, httpHeaderMap));
    return updateApprovalRequestHES;
  }

  private String getApproverUuid(UpdateApprovalRequest updateApprovalRequestHES, String correlationKey,
                                 Map<String, String> httpHeaderMap) throws Exception {
    UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(
        Utility.getDeviceInfo(updateApprovalRequestHES.getDeviceDetails()), httpHeaderMap, correlationKey, null,
        null);
    if (userDetailsDTO != null) {
      return userDetailsDTO.getUuid();
    }
    return StringUtils.EMPTY;
  }

  private ApprovalAction getApprovalAction(com.mmt.hotels.clientgateway.request.UpdateApprovalRequest updateApprovalRequest) {
    String action = updateApprovalRequest.getAction();
    if(StringUtils.isNotBlank(action)) {
      action = updateActionForCancelRequest(action);
    }
    if (StringUtils.isBlank(action) || ApprovalAction.map.get(action) == null) {
      return ApprovalAction.FAILED;
    }
    return ApprovalAction.map.get(action);
  }

  private DeviceDetails getDeviceDetails(String client, com.mmt.hotels.clientgateway.request.UpdateApprovalRequest updateApprovalRequest) {
    DeviceDetails deviceDetails = new DeviceDetails();
    String deviceType = client;
    if (updateApprovalRequest.getDeviceDetails() != null && StringUtils.isNotBlank(
        updateApprovalRequest.getDeviceDetails().getBookingDevice())) {
      deviceType = updateApprovalRequest.getDeviceDetails().getBookingDevice().toUpperCase();
    } else if (updateApprovalRequest.getDeviceDetails() != null && StringUtils.isNotBlank(
        updateApprovalRequest.getDeviceDetails().getDeviceType())) {
      deviceType = updateApprovalRequest.getDeviceDetails().getDeviceType();
    }
    if (Constants.DEVICE_OS_ANDROID.equalsIgnoreCase(deviceType)) {
      deviceDetails.setOsType(OsType.ANDROID);
    } else if (Constants.DEVICE_OS_IOS.equalsIgnoreCase(deviceType)) {
      deviceDetails.setOsType(OsType.IOS);
    } else {
      deviceDetails.setOsType(OsType.DESKTOP);
    }
    if (updateApprovalRequest.getDeviceDetails() != null) {
      deviceDetails.setDeviceId(updateApprovalRequest.getDeviceDetails().getDeviceId());
      deviceDetails.setVersion(updateApprovalRequest.getDeviceDetails().getAppVersion());
    }
    return deviceDetails;
  }

  private String updateActionForCancelRequest(String action) {
    String newAction = new String(action);
    // the action needed to be passed to corp is recalled, in case client sends us action as cancelled
    if (newAction.equalsIgnoreCase(Constants.CANCELLED)) {
      newAction = new String(Constants.RECALLED);
    }
    return newAction;
  }
}
