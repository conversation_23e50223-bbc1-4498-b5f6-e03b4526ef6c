package com.mmt.hotels.clientgateway.transformer.response.ios;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.RatePlanFilter;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class SearchRoomsResponseTransformerIOS extends SearchRoomsResponseTransformer{

    @Override
    protected PersuasionObject createTopRatedPersuasion() {
        return createTopRatedPersuasionForMoblie();
    }
    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    protected GroupRatePlanFilter buildGroupFilterForDevice(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation) {
        return buildStaycationFilter(groupRatePlanFilterConfMap, filterCriteria, staycation);
    }
}
