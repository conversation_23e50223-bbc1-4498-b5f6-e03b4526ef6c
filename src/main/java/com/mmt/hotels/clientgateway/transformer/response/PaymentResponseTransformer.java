package com.mmt.hotels.clientgateway.transformer.response;


import com.mmt.hotels.clientgateway.configuration.HotelsClientGatewayApplication;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsRequestor;
import com.mmt.hotels.clientgateway.helpers.ErrorHelper;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.model.enums.REDIRECT;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PaymentResponseTransformer {

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    ErrorHelper errorHelper;

    private static final Logger logger = LoggerFactory.getLogger(PaymentResponseTransformer.class);

    public PaymentResponse processResponse(PaymentCheckoutResponse checkoutResponse, BeginCheckoutReqBody beginCheckoutReqBody){
        String txnkey = beginCheckoutReqBody.getTransactionKey();
        PaymentResponse paymentResponse = new PaymentResponse();
        paymentResponse.setCorrelationKey(checkoutResponse.getCorrelationKey());
        if(checkoutResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(checkoutResponse.getResponseErrors().getErrorList())){
            String errorCode = checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorCode();
            SpecifiedErrorsRequestor specificError = SpecifiedErrorsRequestor.resolve(errorCode);
            if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(beginCheckoutReqBody.getIdContext()) && specificError!=null){
                //will execute only if payment-checkout is called from corp and the error code matches to specified errors
                Error error = new Error(specificError.getErrorCode(), errorHelper.getSubtitleForError(specificError.getSubTitle()), null, errorHelper.getTitleForError(specificError.getTitle()));
                logger.warn("Error: {}", error);
                paymentResponse.setError(error);
            }else{
                Error error  = new Error(checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorCode(), checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
                paymentResponse.setError(error);
            }
        }else {
            paymentResponse.setBookingID(checkoutResponse.getBookingID());
            paymentResponse.setCheckoutId(checkoutResponse.getPaymentParams() != null 
            		&& checkoutResponse.getPaymentParams().get("checkoutId") != null ? checkoutResponse.getPaymentParams().get("checkoutId").toString() : null);
            paymentResponse.setPaymentRespMessage(checkoutResponse.getPaymentRespMessage());
            paymentResponse.setTransactionKey(txnkey);
            paymentResponse.setOldPaymentParameter(checkoutResponse.getOldPaymentParameter());
            paymentResponse.setIsNewPayment(checkoutResponse.isNewPayment());
            paymentResponse.setIsCBFlow(checkoutResponse.isCBFlow());
            if(checkoutResponse.isAlternateCurrencySelected()){
                paymentResponse.setTotalAmount(checkoutResponse.getDisplayPriceAlternateCurrency());
                paymentResponse.setCurrency(checkoutResponse.getAlternateCurrencyCode());
            }else {
                paymentResponse.setTotalAmount(checkoutResponse.getTotalAmount());
                paymentResponse.setCurrency(checkoutResponse.getCurrency());
            }
            paymentResponse.setRedirect(getRedirectContext(checkoutResponse));
        }

        return  paymentResponse;
    }

    public String getRedirectContext(PaymentCheckoutResponse checkoutResponse) {
        String redirect = checkoutResponse.getRedirect()!=null  ? checkoutResponse.getRedirect().name() : REDIRECT.PAYMENT.name();
        if (checkoutResponse.getOldPaymentParameter() != null && checkoutResponse.getOldPaymentParameter().getPay_mode() != null) {
            switch (checkoutResponse.getOldPaymentParameter().getPay_mode()) {
                //for PAH without CC and BNPL@0 - redirecting to Thankyou
                case 2:
                case 9:
                    redirect =REDIRECT.THANKYOU.name();
                    break;
            }
        }
        return redirect;
    }
}
