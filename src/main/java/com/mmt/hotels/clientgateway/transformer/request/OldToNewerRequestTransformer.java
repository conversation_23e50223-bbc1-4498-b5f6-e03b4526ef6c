package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.request.LatLngBounds;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SortCriteria;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.request.flyfish.FlyFishSummaryRequest;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/*
 *  No new apis to be integrated here.
 *  This is to support older apps only.
 *  Any new API corresponding to for example Listing should
 *  be part of ListingTransformers only.
 */
@Component
public class OldToNewerRequestTransformer {

	public SearchAddonsRequest updateAddonsRequest(GetAddonsRequest getAddonsRequest){
		SearchAddonsRequest searchAddonsRequest  = new SearchAddonsRequest();
		searchAddonsRequest.setExpData(getAddonsRequest.getExperimentData());
		searchAddonsRequest.setDeviceDetails(getDeviceDetails(getAddonsRequest.getAppVersion(),
				getAddonsRequest.getBookingDevice(),
				getAddonsRequest.getDeviceId(),
				getAddonsRequest.getDeviceType(),
				""));
		searchAddonsRequest.setRequestDetails( new RequestDetails());
		searchAddonsRequest.getRequestDetails().setVisitorId(getAddonsRequest.getVisitorId());
		searchAddonsRequest.getRequestDetails().setIdContext(getAddonsRequest.getIdContext());
		searchAddonsRequest.getRequestDetails().setSiteDomain(getAddonsRequest.getSiteDomain());
		searchAddonsRequest.getRequestDetails().setPayMode(getAddonsRequest.getPayMode());
		searchAddonsRequest.getRequestDetails().setPageContext("REVIEW");
		searchAddonsRequest.setClient(getAddonsRequest.getBookingDevice());
		searchAddonsRequest.setCorrelationKey(getAddonsRequest.getCorrelationKey());
		searchAddonsRequest.setNetRateSelected(getAddonsRequest.isNetRateSelected());
		searchAddonsRequest.setTotalPriceWithoutTax(getAddonsRequest.getTotalPriceWithoutTax());
		searchAddonsRequest.setTotalPriceWithTax(getAddonsRequest.getTotalPriceWithTax());
		searchAddonsRequest.setSearchCriteria(new SearchAddonsCriteria());
		searchAddonsRequest.getSearchCriteria().setHotelId(getAddonsRequest.getRoomCriteria().get(0).getHotelId());
		searchAddonsRequest.getSearchCriteria().setCountryCode(getAddonsRequest.getCountryCode());
		searchAddonsRequest.getSearchCriteria().setCurrency(getAddonsRequest.getCurrency());
		searchAddonsRequest.getSearchCriteria().setCheckIn(getAddonsRequest.getCheckin());
		searchAddonsRequest.getSearchCriteria().setCheckOut(getAddonsRequest.getCheckout());
		searchAddonsRequest.getSearchCriteria().setCityCode(getAddonsRequest.getCityCode());
		searchAddonsRequest.getSearchCriteria().setLocationId(getAddonsRequest.getLocationId());
		searchAddonsRequest.getSearchCriteria().setLocationType(getAddonsRequest.getLocationType());
		searchAddonsRequest.getSearchCriteria().setRoomCriteria(getAvailRoomCriteria(getAddonsRequest.getRoomCriteria()));
		return searchAddonsRequest;
	}

	public MobLandingRequest updateMobLandingRequest(HotelLandingMobRequestBody oldMobLanding){
		MobLandingRequest mobLandingRequest = new MobLandingRequest();
		ListingSearchRequest searchHotelsRequest = updateSearchHotelsRequest(oldMobLanding.getHotelSearchRequest());
		mobLandingRequest.setSearchCriteria(searchHotelsRequest.getSearchCriteria());
		mobLandingRequest.setRequestDetails(searchHotelsRequest.getRequestDetails());
		mobLandingRequest.setExpData(searchHotelsRequest.getExpData());
		mobLandingRequest.setDeviceDetails(searchHotelsRequest.getDeviceDetails());
		mobLandingRequest.setClient(searchHotelsRequest.getClient());
		mobLandingRequest.setCohertVar(searchHotelsRequest.getCohertVar());
		mobLandingRequest.setCorrelationKey(searchHotelsRequest.getCorrelationKey());
		mobLandingRequest.setFilterCriteria(searchHotelsRequest.getFilterCriteria());
		mobLandingRequest.setFilterGroupsToRemove(searchHotelsRequest.getFilterGroupsToRemove());
		mobLandingRequest.setFiltersToRemove(searchHotelsRequest.getFiltersToRemove());
		mobLandingRequest.setFeatureFlags(searchHotelsRequest.getFeatureFlags());
		mobLandingRequest.setImageDetails(searchHotelsRequest.getImageDetails());
		mobLandingRequest.setMatchMakerDetails(searchHotelsRequest.getMatchMakerDetails());
		mobLandingRequest.setReviewDetails(searchHotelsRequest.getReviewDetails());
		mobLandingRequest.setSortCriteria(searchHotelsRequest.getSortCriteria());
		mobLandingRequest.setUuids(oldMobLanding.getUuids());

		if(oldMobLanding.getSelectedTags() != null) {
			mobLandingRequest.setGuidedSearchRequest(new GuidedSearchRequest());
			mobLandingRequest.getGuidedSearchRequest().setCurrentSelectedTags(oldMobLanding.getSelectedTags().getCurrentSelected());
			mobLandingRequest.getGuidedSearchRequest().setPreviousSelectedTags(oldMobLanding.getSelectedTags().getPreviousSelected());
			mobLandingRequest.getGuidedSearchRequest().setLevel(oldMobLanding.getSelectedTags().getLevel());
			mobLandingRequest.getGuidedSearchRequest().setGsFlowIdentifier(oldMobLanding.getGsFlowIdentifier());
		}

		if(oldMobLanding.getRequiredApis() != null) {
			mobLandingRequest.setRequiredApis(new RequiredApis());
			mobLandingRequest.getRequiredApis().setFilterSuggestionRequired(oldMobLanding.getRequiredApis().getIsFilterSuggestionRequired());
			mobLandingRequest.getRequiredApis().setMetaRequired(oldMobLanding.getRequiredApis().getIsMetaRequired());
			mobLandingRequest.getRequiredApis().setPersonalizationRequired(oldMobLanding.getRequiredApis().getIsPersonalizationRequired());
			mobLandingRequest.getRequiredApis().setMmrRequired(oldMobLanding.getRequiredApis().getIsMMRRequired());
			mobLandingRequest.getRequiredApis().setMmrV2Required(oldMobLanding.getRequiredApis().isMMRV2Required());
		}
		mobLandingRequest.setTravellerType(oldMobLanding.getTravelType());

		return mobLandingRequest;
	}

	public SearchHotelsRequest updateSearchHotelsRequest(SearchWrapperInputRequest searchWrapperInputRequest) {
		
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setCorrelationKey(searchWrapperInputRequest.getCorrelationKey());
		searchHotelsRequest.setDeviceDetails(getDeviceDetails(searchWrapperInputRequest.getAppVersion(),
				searchWrapperInputRequest.getBookingDevice(),
				searchWrapperInputRequest.getDeviceId(),
				searchWrapperInputRequest.getDeviceType(),
				searchWrapperInputRequest.getNetworkType()));
		searchHotelsRequest.setExpData(searchWrapperInputRequest.getExperimentData());
		searchHotelsRequest.setLastPeekedOnMapHotelIds(searchWrapperInputRequest.getLastPeekedOnMapHotelIds());
		if(null!=searchWrapperInputRequest.getResponseFilterFlags()) {
			searchHotelsRequest.setFeatureFlags(updateFeatureFlags(searchWrapperInputRequest.getResponseFilterFlags(), searchWrapperInputRequest.getNumberOfCoupons()));
		}
        setRequestDetails(searchHotelsRequest, searchWrapperInputRequest);
		setSearchCriteria(searchHotelsRequest, searchWrapperInputRequest);
		setCollectionCriteria(searchHotelsRequest,searchWrapperInputRequest);
		searchHotelsRequest.getRequestDetails().setSiteDomain(searchWrapperInputRequest.getSiteDomain());
		setSortCriteria(searchHotelsRequest, searchWrapperInputRequest);
		searchHotelsRequest.getRequestDetails().setPageContext(searchWrapperInputRequest.getPageContext());
		searchHotelsRequest.getRequestDetails().setChannel(searchWrapperInputRequest.getChannel());
		searchHotelsRequest.setImageDetails(getImageDetails(searchWrapperInputRequest));
		searchHotelsRequest.setReviewDetails(getReviewDetails(searchWrapperInputRequest.getFlyfishSummaryRequest()));
		searchHotelsRequest.setFilterCriteria(getFilterCriteria(searchWrapperInputRequest.getAppliedFilterMap()));
		searchHotelsRequest.setMatchMakerDetails(searchWrapperInputRequest.getMatchMakerRequest());
		searchHotelsRequest.setMapDetails(buildMapDetails(searchWrapperInputRequest));
		searchHotelsRequest.setAdditionalProperties(new HashMap<>());
		searchHotelsRequest.getAdditionalProperties().put("secureUrl", searchWrapperInputRequest.getSecureURL());
		searchHotelsRequest.getAdditionalProperties().put("imageCount", Integer.toString(searchWrapperInputRequest.getImageCount()));
		searchHotelsRequest.setCardId(searchWrapperInputRequest.getCardId());

		if(searchWrapperInputRequest.getRequestIdentifier() != null && StringUtils.isNotEmpty(searchWrapperInputRequest.getRequestIdentifier().getRequestId())){
			searchHotelsRequest.getRequestDetails().setRequestId(searchWrapperInputRequest.getRequestIdentifier().getRequestId());
		}

		if(searchWrapperInputRequest.getRequestIdentifier() != null && StringUtils.isNotEmpty(searchWrapperInputRequest.getRequestIdentifier().getJourneyId())){
			searchHotelsRequest.getRequestDetails().setJourneyId(searchWrapperInputRequest.getRequestIdentifier().getJourneyId());
		}

		return searchHotelsRequest;
		
	}

	private MapDetails buildMapDetails(SearchWrapperInputRequest searchWrapperInputRequest) {
		if(searchWrapperInputRequest.getLatLngBounds() == null)
			return null;
		MapDetails mapDetails = new MapDetails();
		mapDetails.setLatLngBounds(new LatLngBounds());
		mapDetails.getLatLngBounds().setNeLat(searchWrapperInputRequest.getLatLngBounds().getNELat());
		mapDetails.getLatLngBounds().setNeLng(searchWrapperInputRequest.getLatLngBounds().getNELng());
		mapDetails.getLatLngBounds().setSwLat(searchWrapperInputRequest.getLatLngBounds().getSWLat());
		mapDetails.getLatLngBounds().setSwLng(searchWrapperInputRequest.getLatLngBounds().getSWLng());
		mapDetails.setLatSegments(searchWrapperInputRequest.getLatSegments());
		mapDetails.setLngSegments(searchWrapperInputRequest.getLongSegments());
		mapDetails.setRadius(searchWrapperInputRequest.getLatLngBounds().getRadius());
		return mapDetails;
	}

	private FeatureFlags updateFeatureFlags(ResponseFilterFlags responseFilterFlags, Integer couponCount) {
        FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setReviewSummaryRequired(responseFilterFlags.isFlyfishSummaryRequired());
		featureFlags.setWalletRequired(responseFilterFlags.isWalletRequired());
		featureFlags.setNoOfCoupons(couponCount != null ? couponCount : 0);
		featureFlags.setCheckAvailability(responseFilterFlags.isCheckAvailibility());
		featureFlags.setCoupon(null != responseFilterFlags.isBestCoupon() ? responseFilterFlags.isBestCoupon() : false);
		//featureFlags.setDealOfTheDayRequired(responseFilterFlags.getDealOfTheDayRequired() != null?responseFilterFlags.getDealOfTheDayRequired(): false);
		//Removing flag dependency fix for SWAT-4039659
		featureFlags.setDealOfTheDayRequired(true);
		featureFlags.setShortlistingRequired(responseFilterFlags.getShortlistRequired() != null ? responseFilterFlags.getShortlistRequired(): false);
		featureFlags.setStaticData(responseFilterFlags.getStaticData() != null ? responseFilterFlags.getStaticData() : false);
		featureFlags.setApplyAbsorption(responseFilterFlags.getApplyAbsorption() != null ? responseFilterFlags.getApplyAbsorption(): false);
		featureFlags.setRoomInfoRequired(responseFilterFlags.getRoomLevelDetails() != null ? responseFilterFlags.getRoomLevelDetails(): false);
		featureFlags.setCorpMMRRequired(responseFilterFlags.isCorporateMMRLocationsRequired());
		featureFlags.setExtraAltAccoRequired(responseFilterFlags.isExtraAltAccoPropertiesRequired());
		featureFlags.setHotelCatAndPropNotRequiredInMeta(responseFilterFlags.isHotelCatAndPropNotRequiredInMeta());
		featureFlags.setLimitedFilterCall(responseFilterFlags.isLimitedFilterCall());
		featureFlags.setPoisRequiredOnMap(responseFilterFlags.isPoisRequiredOnMap());
		featureFlags.setUnmodifiedAmenities(responseFilterFlags.isUnmodifiedAmenities());
		featureFlags.setPersuasionsRequired(responseFilterFlags.isPersuasionRequired());
		return featureFlags;
    }

    private void setCollectionCriteria(SearchHotelsRequest searchHotelsRequest, SearchWrapperInputRequest searchWrapperInputRequest){
		if(searchWrapperInputRequest.isCollectionRequired()
				|| searchWrapperInputRequest.isTrendingNow()){
			CollectionCriteria collectionCriteria = new CollectionCriteria();
			collectionCriteria.setCollectionRequired(searchWrapperInputRequest.isCollectionRequired());
			collectionCriteria.setTrendingNow(searchWrapperInputRequest.isTrendingNow());
			collectionCriteria.setCollectionIds(searchWrapperInputRequest.getCollectionIds());
			collectionCriteria.setAthenaCategory(searchWrapperInputRequest.getAthenaCategory());
			collectionCriteria.setStaticFilterCardsRequired(searchWrapperInputRequest.isStaticFilterCardsRequired());
			collectionCriteria.setInspiredCardsRequired(searchWrapperInputRequest.isInspiredCardsRequired());
			collectionCriteria.setDiscoverByDestinationCardsRequired(searchWrapperInputRequest.isDiscoverByDestinationCardsRequired());
			collectionCriteria.setOffbeatCitiesCardsRequired(searchWrapperInputRequest.isOffbeatCitiesCardsRequired());
			searchHotelsRequest.getSearchCriteria().setCollectionCriteria(collectionCriteria);
		}

	}

	private ReviewDetails getReviewDetails(FlyFishSummaryRequest flyfishSummaryRequest) {
		if (flyfishSummaryRequest == null)
			return null;
		ReviewDetails reviewDetails = new ReviewDetails();
		List<String> OTAs = new ArrayList<>();
		
		if (flyfishSummaryRequest.getFilter() != null && CollectionUtils.isNotEmpty(flyfishSummaryRequest.getFilter().getOtas())) {
			for (OTA ota: flyfishSummaryRequest.getFilter().getOtas()) {
				OTAs.add(ota.getValue());
			}
			reviewDetails.setOtas(OTAs);
		}
		
		if (flyfishSummaryRequest.getFilter() != null && flyfishSummaryRequest.getFilter().getSubConcept() != null && 
				CollectionUtils.isNotEmpty(flyfishSummaryRequest.getFilter().getSubConcept().getTagTypes())) {
			reviewDetails.setTagTypes(flyfishSummaryRequest.getFilter().getSubConcept().getTagTypes());
		}
		return reviewDetails;
	}

	private ImageDetails getImageDetails(SearchWrapperInputRequest searchWrapperInputRequest) {
		if (CollectionUtils.isEmpty(searchWrapperInputRequest.getImageCategory()) &&
				CollectionUtils.isEmpty(searchWrapperInputRequest.getImageType()))
			return null;
		ImageDetails imageDetails = new ImageDetails ();
		imageDetails.setTypes(searchWrapperInputRequest.getImageType());
		
		List<com.mmt.hotels.clientgateway.request.ImageCategory> imageCategoryList = new ArrayList<>();
		for (ImageCategoryEntityBO imageCategory: searchWrapperInputRequest.getImageCategory()) {
			com.mmt.hotels.clientgateway.request.ImageCategory imageCategoryCG = new com.mmt.hotels.clientgateway.request.ImageCategory();
			imageCategoryCG.setCount(imageCategory.getCount());
			imageCategoryCG.setHeight(imageCategory.getHeight());
			imageCategoryCG.setWidth(imageCategory.getWidth());
			imageCategoryCG.setType(imageCategory.getCategory());
			imageCategoryCG.setImageFormat(imageCategory.getOutputFormat());
			imageCategoryList.add(imageCategoryCG);
		}
		if (CollectionUtils.isNotEmpty(imageCategoryList))
			imageDetails.setCategories(imageCategoryList);
		return imageDetails;
	}

	private void setSortCriteria(SearchHotelsRequest searchHotelsRequest,
			SearchWrapperInputRequest searchWrapperInputRequest) {
		if (searchWrapperInputRequest.getSortCriteria() != null) {
			searchHotelsRequest.setSortCriteria(new SortCriteria());
			searchHotelsRequest.getSortCriteria().setField(searchWrapperInputRequest.getSortCriteria().getField());
			searchHotelsRequest.getSortCriteria().setOrder(searchWrapperInputRequest.getSortCriteria().getOrder());
		}
	}

	private void setSearchCriteria(SearchHotelsRequest searchHotelsRequest,
			SearchWrapperInputRequest searchWrapperInputRequest) {
		searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
		searchHotelsRequest.getSearchCriteria().setCheckIn(searchWrapperInputRequest.getCheckin());
		searchHotelsRequest.getSearchCriteria().setCheckOut(searchWrapperInputRequest.getCheckout());
		searchHotelsRequest.getSearchCriteria().setCityCode(searchWrapperInputRequest.getCityCode());
		searchHotelsRequest.getSearchCriteria().setCountryCode(searchWrapperInputRequest.getCountryCode());
		searchHotelsRequest.getSearchCriteria().setCurrency(searchWrapperInputRequest.getCurrency());
		searchHotelsRequest.getSearchCriteria().setHotelIds(searchWrapperInputRequest.getHotelIdList());
		searchHotelsRequest.getSearchCriteria().setLastHotelCategory(searchWrapperInputRequest.getLastFetchedHotelCategory());
		searchHotelsRequest.getSearchCriteria().setLastHotelId(searchWrapperInputRequest.getLastFetchedHotelId());
		searchHotelsRequest.getSearchCriteria().setLat(searchWrapperInputRequest.getLatitude());
		searchHotelsRequest.getSearchCriteria().setLimit(searchWrapperInputRequest.getLimit());
		searchHotelsRequest.getSearchCriteria().setLng(searchWrapperInputRequest.getLongitude());
		searchHotelsRequest.getSearchCriteria().setLocationId(searchWrapperInputRequest.getLocationId());
		searchHotelsRequest.getSearchCriteria().setLocationType(searchWrapperInputRequest.getLocationType());
		//below one needs a flag
		//searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(false);		//Commented due to sequencing issue
		searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(searchWrapperInputRequest.isPersonalizedSearch());
		searchHotelsRequest.getSearchCriteria().setRoomStayCandidates(getRoomStayCandidates(searchWrapperInputRequest.getRoomStayCandidates()));
		searchHotelsRequest.getSearchCriteria().setTotalHotelsShown(searchWrapperInputRequest.getTotalHotelsShown());
		if(CollectionUtils.isNotEmpty(searchWrapperInputRequest.getTravelerDetailsList())) {
			searchHotelsRequest.getSearchCriteria().setTravellerEmailID(new ArrayList<>());
			for(TravelerDetail trvlr : searchWrapperInputRequest.getTravelerDetailsList()) {
				if(StringUtils.isNotBlank(trvlr.getEmailID())){
					searchHotelsRequest.getSearchCriteria().getTravellerEmailID().add(trvlr.getEmailID());
				}
			}
		}
	}

	private void setRequestDetails(SearchHotelsRequest searchHotelsRequest,
			SearchWrapperInputRequest searchWrapperInputRequest) {
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		//set coupon count
		searchHotelsRequest.getRequestDetails().setFunnelSource(searchWrapperInputRequest.getFunnelSource());
		searchHotelsRequest.getRequestDetails().setIdContext(searchWrapperInputRequest.getIdContext());
		searchHotelsRequest.getRequestDetails().setLoggedIn(searchWrapperInputRequest.getLoggedIn());
		searchHotelsRequest.getRequestDetails().setNotifCoupon(searchWrapperInputRequest.getNotifCoupon());
		searchHotelsRequest.getRequestDetails().setFirstTimeUserState(searchWrapperInputRequest.getFirstTimeUserState());
		//doubt
		searchHotelsRequest.getRequestDetails().setPayMode(searchWrapperInputRequest.getPaymentChannel());
		searchHotelsRequest.getRequestDetails().setSrCon(searchWrapperInputRequest.getSrCon());
		searchHotelsRequest.getRequestDetails().setSrCty(searchWrapperInputRequest.getSrCty());
		searchHotelsRequest.getRequestDetails().setSrLat(searchWrapperInputRequest.getSrLat());
		searchHotelsRequest.getRequestDetails().setSrLng(searchWrapperInputRequest.getSrLng());
		if (searchWrapperInputRequest.getTrafficSource() != null) {
			searchHotelsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
			searchHotelsRequest.getRequestDetails().getTrafficSource().setSource(
					searchWrapperInputRequest.getTrafficSource().getSource());
			searchHotelsRequest.getRequestDetails().getTrafficSource().setType(
					searchWrapperInputRequest.getTrafficSource().getType());
		}
		if (StringUtils.isNotEmpty(searchWrapperInputRequest.getVisitNumber()))
			searchHotelsRequest.getRequestDetails().setVisitNumber(
					Integer.parseInt(searchWrapperInputRequest.getVisitNumber()));
		searchHotelsRequest.getRequestDetails().setVisitorId(searchWrapperInputRequest.getVisitorId());
		if (StringUtils.isNotEmpty(searchWrapperInputRequest.getRequester())){
			searchHotelsRequest.getRequestDetails().setRequestor(searchWrapperInputRequest.getRequester());
		}

		searchHotelsRequest.getRequestDetails().setSeoCorp(searchWrapperInputRequest.isSeoCorp());
		searchHotelsRequest.getRequestDetails().setBrand(searchWrapperInputRequest.getBrand());

	}


	private List<RoomStayCandidate> getRoomStayCandidates(
			List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesCB) {
		if (CollectionUtils.isNotEmpty(roomStayCandidatesCB)) {
			List<RoomStayCandidate> roomStayCandidates = new ArrayList<RoomStayCandidate>();
			for (com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidateCB: roomStayCandidatesCB) {
				RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
				for (GuestCount guestCount: roomStayCandidateCB.getGuestCounts()) {
					roomStayCandidate.setAdultCount(Integer.parseInt(guestCount.getCount()));
					roomStayCandidate.setChildAges(guestCount.getAges());
				}
				roomStayCandidates.add(roomStayCandidate);
			}
			return roomStayCandidates;
		}
		return null;
	}

	public SearchRoomsRequest updateSearchRoomsRequest(PriceByHotelsRequestBody priceByHotelsRequestBody){
		SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
		searchRoomsRequest.setDeviceDetails(getDeviceDetails(priceByHotelsRequestBody.getAppVersion(),
				priceByHotelsRequestBody.getBookingDevice(),
				priceByHotelsRequestBody.getDeviceId(),
				priceByHotelsRequestBody.getDeviceType(),
				null));
		searchRoomsRequest.setCorrelationKey(priceByHotelsRequestBody.getCorrelationKey());
		searchRoomsRequest.setExpData(priceByHotelsRequestBody.getExperimentData());
		//searchCriteria
		setSearchCriteria(searchRoomsRequest,priceByHotelsRequestBody);
		//requestDetails;
		searchRoomsRequest.setRequestDetails(getRequestDetails(priceByHotelsRequestBody));
		searchRoomsRequest.getRequestDetails().setFirstTimeUserState(priceByHotelsRequestBody.getFirstTimeUserState());
		searchRoomsRequest.getRequestDetails().setBrand(priceByHotelsRequestBody.getBrand());

		//filterCriteria
        searchRoomsRequest.setFilterCriteria(getFilterCriteria(priceByHotelsRequestBody.getAppliedFilterMap()));
		searchRoomsRequest.setFeatureFlags(getFeatureFlags(priceByHotelsRequestBody));
		searchRoomsRequest.getRequestDetails().setSiteDomain(priceByHotelsRequestBody.getSiteDomain());
		searchRoomsRequest.getRequestDetails().setPageContext(priceByHotelsRequestBody.getPageContext());

		if (priceByHotelsRequestBody.getCorpAuthCode()!=null){
			searchRoomsRequest.getRequestDetails().setCorpAuthCode(priceByHotelsRequestBody.getCorpAuthCode());
		}

		return searchRoomsRequest;
	}

	public AvailRoomsRequest updateAvailRoomsRequest(PriceByHotelsRequestBody priceByHotelsRequestBody){
		AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
		availRoomsRequest.setDeviceDetails(getDeviceDetails(priceByHotelsRequestBody.getAppVersion(),
				priceByHotelsRequestBody.getBookingDevice(),
				priceByHotelsRequestBody.getDeviceId(),
				priceByHotelsRequestBody.getDeviceType(),
				null));

		availRoomsRequest.setExpData(priceByHotelsRequestBody.getExperimentData());
		availRoomsRequest.setCorrelationKey(priceByHotelsRequestBody.getCorrelationKey());
		//search Criteria
		availRoomsRequest.setSearchCriteria(getAvailSearchCriteria(priceByHotelsRequestBody));
		//Request Details
		availRoomsRequest.setRequestDetails(getRequestDetails(priceByHotelsRequestBody));
		availRoomsRequest.getRequestDetails().setFirstTimeUserState(priceByHotelsRequestBody.getFirstTimeUserState());

		//EMI Details
		availRoomsRequest.setEmiDetail(getEmiDetail(priceByHotelsRequestBody));
		//Feature flags
		availRoomsRequest.setFeatureFlags(getFeatureFlags(priceByHotelsRequestBody));

		availRoomsRequest.getRequestDetails().setSiteDomain(priceByHotelsRequestBody.getSiteDomain());
		availRoomsRequest.getRequestDetails().setPageContext(priceByHotelsRequestBody.getPageContext());

		if (priceByHotelsRequestBody.getCorpAuthCode()!=null){
			availRoomsRequest.getRequestDetails().setCorpAuthCode(priceByHotelsRequestBody.getCorpAuthCode());
		}

		return availRoomsRequest;
	}

	private FeatureFlags getFeatureFlags(PriceByHotelsRequestBody priceByHotelsRequestBody){
		FeatureFlags featureFlags = new FeatureFlags();


		featureFlags.setNoOfAddons(priceByHotelsRequestBody.getNumberOfAddons());
		featureFlags.setBestOffersLimit(null != priceByHotelsRequestBody.getBestOffersLimit() ? priceByHotelsRequestBody.getBestOffersLimit() : 0);
		if(null != priceByHotelsRequestBody.getResponseFilterFlags()){
			featureFlags.setCoupon(priceByHotelsRequestBody.getResponseFilterFlags().isBestCoupon() !=null ? priceByHotelsRequestBody.getResponseFilterFlags().isBestCoupon() : false);
			featureFlags.setWalletRequired(priceByHotelsRequestBody.getResponseFilterFlags().isWalletRequired());
			featureFlags.setApplyAbsorption(null != priceByHotelsRequestBody.getResponseFilterFlags().getApplyAbsorption()
					?priceByHotelsRequestBody.getResponseFilterFlags().getApplyAbsorption() : false);
			featureFlags.setAddOnRequired(null != priceByHotelsRequestBody.getResponseFilterFlags().isAddOnRequired()
					? priceByHotelsRequestBody.getResponseFilterFlags().isAddOnRequired() : false);
		}
		return featureFlags;
	}

	private EMIDetail getEmiDetail(PriceByHotelsRequestBody priceByHotelsRequestBody){
		EMIDetail emiDetail = null;
		if(null != priceByHotelsRequestBody.getEmiDetails()){
			emiDetail = new EMIDetail();
			emiDetail.setBankId(priceByHotelsRequestBody.getEmiDetails().getBankId());
			emiDetail.setBankName(priceByHotelsRequestBody.getEmiDetails().getBankName());
			emiDetail.setPayOption(priceByHotelsRequestBody.getEmiDetails().getPayOption());
			emiDetail.setTenure(priceByHotelsRequestBody.getEmiDetails().getTenure());
		}

		return emiDetail;
	}

    private List<Filter> getFilterCriteria(Map<FilterGroup, Set<com.mmt.hotels.filter.Filter>> appliedFilterMap) {
	    List<Filter> filters = null;
	    if(MapUtils.isNotEmpty(appliedFilterMap)){
	    	filters = new ArrayList<>();
	        for(FilterGroup fg : appliedFilterMap.keySet()){
	            for(com.mmt.hotels.filter.Filter oldFilter : appliedFilterMap.get(fg)){
	                Filter filter = new Filter();
	                filter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.getFilterGroupFromFilterName(oldFilter.getFilterGroup().name()));
	                filter.setFilterValue(oldFilter.getFilterValue());
	                if(oldFilter.getFilterRange() != null) {
						FilterRange filterRange = new FilterRange();
						filterRange.setMaxValue(oldFilter.getFilterRange().getMaxValue());
						filterRange.setMinValue(oldFilter.getFilterRange().getMinValue());
						filter.setFilterRange(filterRange);
					}
	                filter.setRangeFilter(oldFilter.isRangeFilter());
	                filters.add(filter);
                }
            }
        }
	    return filters;
    }


	private DeviceDetails getDeviceDetails(String appVersion,
										  String bookingDevice,
										  String deviceId,
										  String deviceType,
										  String networkType){
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setAppVersion(appVersion);
		deviceDetails.setBookingDevice(bookingDevice);
		deviceDetails.setDeviceId(deviceId);
		deviceDetails.setDeviceType(deviceType);
		deviceDetails.setNetworkType(networkType);

		return deviceDetails;
	}
	
	private RequestDetails getRequestDetails(PriceByHotelsRequestBody request) {
		RequestDetails requestDetails = new RequestDetails();
		//set coupon count
		requestDetails.setFunnelSource(request.getFunnelSource());
		requestDetails.setIdContext(request.getIdContext());
		requestDetails.setLoggedIn(request.isLoggedIn());
		requestDetails.setNotifCoupon(request.getNotifCoupon());
		//doubt
		requestDetails.setPayMode(request.getPayMode());
		requestDetails.setSrCon(request.getSrCon());
		requestDetails.setSrCty(request.getSrCty());
		requestDetails.setSrLat(request.getSrLat());
		requestDetails.setSrLng(request.getSrLng());
		if (request.getTrafficSource() != null) {
			requestDetails.setTrafficSource(new TrafficSource());
			requestDetails.getTrafficSource().setSource(
					request.getTrafficSource().getSource());
			requestDetails.getTrafficSource().setType(
					request.getTrafficSource().getType());
		}
		if (StringUtils.isNotEmpty(request.getVisitNumber()))
			requestDetails.setVisitNumber(
					Integer.parseInt(request.getVisitNumber()));
		requestDetails.setVisitorId(request.getVisitorId());
		requestDetails.setChannel(request.getChannel());
		if(request.getCouponCount() > 0 && request.getCouponCount() <3)
			request.setCouponCount(3);

		requestDetails.setCouponCount(request.getCouponCount());


		return requestDetails;
	}

	private void setSearchCriteria(SearchRoomsRequest searchRoomsRequest,
								   PriceByHotelsRequestBody priceByHotelsRequestBody) {
		searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
		populateBaseSearchCriteria(searchRoomsRequest.getSearchCriteria(),priceByHotelsRequestBody);
		if(CollectionUtils.isNotEmpty(priceByHotelsRequestBody.getHotelIds())) {
			searchRoomsRequest.getSearchCriteria().setHotelId(priceByHotelsRequestBody.getHotelIds().get(0));
		} else if (CollectionUtils.isNotEmpty(priceByHotelsRequestBody.getGiHotelIds())) {
			searchRoomsRequest.getSearchCriteria().setGiHotelId(priceByHotelsRequestBody.getGiHotelIds().get(0));
		}
		searchRoomsRequest.getSearchCriteria().setVcId(priceByHotelsRequestBody.getVcId());
		searchRoomsRequest.getSearchCriteria().setMtKey(priceByHotelsRequestBody.getMtKey());
		//below one needs a flag
		searchRoomsRequest.getSearchCriteria().setRoomStayCandidates(getRoomStayCandidates(priceByHotelsRequestBody.getRoomStayCandidates()));
	}

	private void populateBaseSearchCriteria(SearchCriteria searchCriteria, PriceByHotelsRequestBody priceByHotelsRequestBody){
		searchCriteria.setCheckIn(priceByHotelsRequestBody.getCheckin());
		searchCriteria.setCheckOut(priceByHotelsRequestBody.getCheckout());
		searchCriteria.setCityCode(priceByHotelsRequestBody.getCityCode());
		searchCriteria.setCountryCode(priceByHotelsRequestBody.getCountryCode());
		searchCriteria.setCurrency(priceByHotelsRequestBody.getCurrency());
		searchCriteria.setLocationId(priceByHotelsRequestBody.getLocationId());
		searchCriteria.setLocationType(priceByHotelsRequestBody.getLocationType());
	}

	private AvailPriceCriteria getAvailSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody){
		AvailPriceCriteria availPriceCriteria = new AvailPriceCriteria();
		populateBaseSearchCriteria(availPriceCriteria,priceByHotelsRequestBody);
		String hotelId = null;
		if(CollectionUtils.isNotEmpty(priceByHotelsRequestBody.getHotelIds()))
			hotelId = priceByHotelsRequestBody.getHotelIds().get(0);
		else{
			if(CollectionUtils.isNotEmpty(priceByHotelsRequestBody.getRoomCriteria())){
				hotelId = priceByHotelsRequestBody.getRoomCriteria().get(0).getHotelId();
			}
		}
		availPriceCriteria.setHotelId(hotelId);
		availPriceCriteria.setPricingKey(CollectionUtils.isNotEmpty(priceByHotelsRequestBody.getRoomCriteria())?priceByHotelsRequestBody.getRoomCriteria().get(0).getPricingKey():null); // pricing key is same for all rate plans
		availPriceCriteria.setSearchType(null != priceByHotelsRequestBody.getExtraInfo() ? priceByHotelsRequestBody.getExtraInfo().getSearchType(): null);
		List<AvailRoomsSearchCriteria> roomCriteria = getAvailRoomCriteria(priceByHotelsRequestBody.getRoomCriteria());
		availPriceCriteria.setRoomCriteria(roomCriteria);
		return availPriceCriteria;
	}

	private List<AvailRoomsSearchCriteria> getAvailRoomCriteria(List<RoomCriterion> roomCriteriaOld) {
		if(roomCriteriaOld == null){
			return null;
		}
		List<AvailRoomsSearchCriteria> roomCriteria = new ArrayList<>();
		for(RoomCriterion roomCriterion : roomCriteriaOld){
			AvailRoomsSearchCriteria availRoomsSearchCriteria = new AvailRoomsSearchCriteria();
			availRoomsSearchCriteria.setRoomStayCandidates(getRoomStayCandidates(roomCriterion.getRoomStayCandidates()));
			availRoomsSearchCriteria.setMtKey(roomCriterion.getMtKey());
			availRoomsSearchCriteria.setPricingKey(roomCriterion.getPricingKey());
			availRoomsSearchCriteria.setRatePlanCode(roomCriterion.getRatePlanCode());
			availRoomsSearchCriteria.setRoomCode(roomCriterion.getRoomCode());
			availRoomsSearchCriteria.setSupplierCode(roomCriterion.getSupplierCode());
			roomCriteria.add(availRoomsSearchCriteria);
		}
		return roomCriteria;
	}

	public SearchHotelsRequest updateNearByHotelsRequest(SearchWrapperInputRequest searchWrapperInputRequest) {
		SearchHotelsRequest searchHotelsRequest = updateSearchHotelsRequest(searchWrapperInputRequest);
		searchHotelsRequest.setCorrelationKey(searchWrapperInputRequest.getCorrelationKey());
		searchHotelsRequest.setNearbyFilter(searchWrapperInputRequest.getNearbyRequest());
		if(null != searchHotelsRequest.getSearchCriteria()){
			searchHotelsRequest.getSearchCriteria().setNearBySearch(true);
		}
		return searchHotelsRequest;
	}


	public SearchRoomsRequest updateSearchRoomRequest(HotelDetailsMobRequestBody request) {
		SearchRoomsRequest searchRoomsRequest  = new SearchRoomsRequest();
		searchRoomsRequest.setSearchCriteria(getSearchCriteria(request));
		searchRoomsRequest.setDeviceDetails(getDeviceDetails(request.getAppVersion(),request.getBookingDevice(),
				request.getDeviceId(),request.getDeviceType(),""));
		searchRoomsRequest.setRequestDetails(getRequestDetails(request));
		searchRoomsRequest.setCorrelationKey(request.getCorrelationKey());
		searchRoomsRequest.setExpData(request.getExperimentData());
		searchRoomsRequest.setFilterCriteria(getFilterCriteria(request.getAppliedFilterMap()));
		searchRoomsRequest.setFeatureFlags(getFeatureFlags(request));
		return searchRoomsRequest;
	}

	private RequestDetails getRequestDetails(HotelDetailsMobRequestBody request) {
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setIdContext(request.getIdContext());
		requestDetails.setLoggedIn(null!=request.getLoggedIn()?request.getLoggedIn():false);
		requestDetails.setNotifCoupon(request.getNotifCoupon());
		if (request.getTrafficSource() != null) {
			requestDetails.setTrafficSource(new TrafficSource());
			requestDetails.getTrafficSource().setSource(
					request.getTrafficSource().getSource());
			requestDetails.getTrafficSource().setType(
					request.getTrafficSource().getType());
		}
		if (StringUtils.isNotEmpty(request.getVisitNumber()))
			requestDetails.setVisitNumber(
					Integer.parseInt(request.getVisitNumber()));
		requestDetails.setVisitorId(request.getVisitorId());
		requestDetails.setChannel(request.getChannel());
		requestDetails.setPageContext(request.getPageContext());
		requestDetails.setFirstTimeUserState(request.getFirstTimeUserState());
		requestDetails.setSiteDomain(request.getSiteDomain());
		return requestDetails;
	}

	private SearchRoomsCriteria getSearchCriteria(HotelDetailsMobRequestBody request){
		SearchRoomsCriteria criteria = new SearchRoomsCriteria();
		criteria.setRoomStayCandidates(getRoomStayCandidates(request.getRoomStayCandidates()));
		criteria.setCheckIn(request.getCheckin());
		criteria.setCheckOut(request.getCheckout());
		criteria.setCityCode(request.getCityCode());
		criteria.setCountryCode(request.getCountryCode());
		criteria.setLocationId(request.getLocationId());
		criteria.setLocationType(request.getLocationType());
		criteria.setCurrency(request.getCurrency());
		criteria.setHotelId(request.getHotelId());
		return criteria;
	}

	private FeatureFlags getFeatureFlags(HotelDetailsMobRequestBody requestBody){
		FeatureFlags featureFlags = new FeatureFlags();

		if(null != requestBody.getResponseFilterFlags()){
			featureFlags.setCoupon(null!=requestBody.getResponseFilterFlags().isBestCoupon()?
					requestBody.getResponseFilterFlags().isBestCoupon():false);
			featureFlags.setWalletRequired(requestBody.getResponseFilterFlags().isWalletRequired());
			featureFlags.setApplyAbsorption(null != requestBody.getResponseFilterFlags().getApplyAbsorption()
					?requestBody.getResponseFilterFlags().getApplyAbsorption() : false);
		}
		return featureFlags;
	}
	
}
