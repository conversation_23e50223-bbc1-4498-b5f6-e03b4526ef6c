package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.corporate.UpdatePolicyResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpdatePolicyResponseTransformer {

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    public UpdatePolicyResponse convertUpdatePolicyResponse(CorpPolicyUpdateResponse respHES) {


        UpdatePolicyResponse updatePolicyResponse = new UpdatePolicyResponse();
        if(respHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(respHES.getResponseErrors().getErrorList())) {
            Error error = new Error(respHES.getResponseErrors().getErrorList().get(0).getErrorCode(), respHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            updatePolicyResponse.setError(error);
        }else {
            updatePolicyResponse.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(respHES.getCorpMetaInfo()));
            updatePolicyResponse.setApprovingManagers(commonResponseTransformer.buildManagers(respHES.getApprovingManagers()));
            updatePolicyResponse.setReasonForBooking(commonResponseTransformer.buildReasonForBooking(respHES.getReasonForBooking()));
            updatePolicyResponse.setSkipApprovalReasons(commonResponseTransformer.buildSkipApprovalReasons(respHES.getSkipApprovalReasons()));
            updatePolicyResponse.setCorpAutobookRequestorConfig(commonResponseTransformer.buildCorpAutobookRequestorConfig(respHES.getAutobookRequestorConfig()));
        }

        return updatePolicyResponse;
    }
}