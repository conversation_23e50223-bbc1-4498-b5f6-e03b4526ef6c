package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserAssociatedTraveller;
import com.mmt.hotels.clientgateway.thirdparty.response.UserTravelDocument;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.response.emi.EmiDetailsRequest;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;

@Component
public class AvailRoomsRequestTransformer extends BaseRoomRequestTransformer{

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    Utility utility;

    private static final Logger LOGGER = LoggerFactory.getLogger(AvailRoomsRequestTransformer.class);

    public PriceByHotelsRequestBody convertAvailRoomsRequest(AvailRoomsRequest availRoomsRequest, CommonModifierResponse commonModifierResponse) {

        if (availRoomsRequest == null)
            return null;
        long startTime = System.currentTimeMillis();
        PriceByHotelsRequestBody availRequestCB = new PriceByHotelsRequestBody();
        try {
            super.buildDeviceDetails(availRequestCB, availRoomsRequest.getDeviceDetails());
            buildSearchCriteria(availRequestCB, availRoomsRequest.getSearchCriteria(), commonModifierResponse);
            buildRequestDetails(availRequestCB, availRoomsRequest.getRequestDetails(),commonModifierResponse);
            availRequestCB.setResponseFilterFlags(super.buildResponseFilterFlags(availRequestCB, availRoomsRequest, commonModifierResponse));
            availRequestCB.setCorrelationKey(availRoomsRequest.getCorrelationKey());
            availRequestCB.setEmiDetails(buildEmiDetail(availRoomsRequest.getEmiDetail()));
            availRequestCB.setExperimentData(availRoomsRequest.getExpData());
            availRequestCB.setMobile(commonModifierResponse.getMobile());
            availRequestCB.setAffiliateId(commonModifierResponse.getAffiliateId());
            availRequestCB.setCdfContextId(commonModifierResponse.getCdfContextId());
            if (commonModifierResponse.getExtendedUser() != null) {
                availRequestCB.setUuid(commonModifierResponse.getExtendedUser().getUuid());
                availRequestCB.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
                availRequestCB.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
                availRequestCB.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
                availRequestCB.setEmail(commonModifierResponse.getExtendedUser().getPrimaryEmailId());
                availRequestCB.setUserIdGi(commonModifierResponse.getExtendedUser().getAccountId());
                availRequestCB.setRequestIdentifier(utility.buildRequestIdentifier(availRoomsRequest.getRequestDetails()));
                if(StringUtils.isNotEmpty(commonModifierResponse.getExtendedUser().getPrimaryEmailId())){
                    ScramblerClient sc = ScramblerClient.getInstance();
                    availRequestCB.setEmailCommId(sc.encode(commonModifierResponse.getExtendedUser().getPrimaryEmailId(), HashType.F));
                }
                String panNo = getUserPAN(commonModifierResponse.getExtendedUser());
                if (StringUtils.isNotEmpty(panNo)) {
                    availRequestCB.setPnAvlbl(true);
                }
            }

            if (commonModifierResponse.getHydraResponse() != null) {
                availRequestCB.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
                availRequestCB.setFlightBooker(commonModifierResponse.getHydraResponse().isFlightBooker());
            }

            availRequestCB.setSiteDomain(availRoomsRequest.getRequestDetails().getSiteDomain());
            availRequestCB.setZcpHash(availRoomsRequest.getRequestDetails().getZcp());
            //setting roomInfoRequired to true for new review revamp API to store room info in txn data
            availRequestCB.setRoomInfoRequired(true);
            availRequestCB.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
            availRequestCB.setBrand(commonModifierResponse.getBrand());
            //availRequestCB.setStreaksThankYouMessageAvailable(streaksThankYouMessageAvailable(commonModifierResponse)); Removed streaks flag set since streaks is completely stopped.
            availRequestCB.setUserLocation(commonModifierResponse.getUserLocation());
            if (utility.isBusinessIdentifyEnableExperimentOn(commonModifierResponse.getExpDataMap())
                    && commonModifierResponse.getExtendedUser() != null) {
                availRequestCB.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
            }
            availRequestCB.setAppliedFilterMap(buildAppliedFilterMapForAvail(availRoomsRequest.getFilterCriteria()));
        } catch (ScramblerClientException e) {
            LOGGER.warn("Scrambler Exception while encoding emailid ", e);
        } finally {
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_REVIEW_REQUEST_PROCESS, REVIEW_AVAIL_ROOMS, System.currentTimeMillis() - startTime);
        }
        return availRequestCB;

    }

    Map<FilterGroup, Set<com.mmt.hotels.filter.Filter>> buildAppliedFilterMapForAvail(List<com.mmt.hotels.clientgateway.request.Filter> filters) {
        Map<FilterGroup,Set<com.mmt.hotels.filter.Filter>> appliedFilterMap = null;
        if(CollectionUtils.isNotEmpty(filters)){
            appliedFilterMap = new HashMap<>();
            for(com.mmt.hotels.clientgateway.request.Filter filter : filters){
                if(filter.getFilterGroup() != null) {
                    FilterGroup filterGroup = FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name());
                    com.mmt.hotels.filter.Filter oldFilter = getOldFilterFromNewFilter(filter, filterGroup);
                    if (appliedFilterMap.containsKey(filterGroup)) {
                        appliedFilterMap.get(filterGroup).add(oldFilter);
                    } else {
                        Set<com.mmt.hotels.filter.Filter> filterSet = new HashSet<>();
                        filterSet.add(oldFilter);
                        appliedFilterMap.put(filterGroup, filterSet);
                    }
                }
            }
        }
        return appliedFilterMap;
    }

    com.mmt.hotels.filter.Filter getOldFilterFromNewFilter(com.mmt.hotels.clientgateway.request.Filter filter, FilterGroup filterGroup) {
        com.mmt.hotels.filter.Filter oldFilter = new com.mmt.hotels.filter.Filter();
        oldFilter.setFilterGroup(filterGroup);
        oldFilter.setFilterValue(filter.getFilterValue());
        oldFilter.setRangeFilter(filter.isRangeFilter());
        if(null != filter.getFilterRange()) {
            com.mmt.hotels.filter.FilterRange filterRange = new com.mmt.hotels.filter.FilterRange();
            filterRange.setMaxValue(filter.getFilterRange().getMaxValue());
            filterRange.setMinValue(filter.getFilterRange().getMinValue());
            oldFilter.setFilterRange(filterRange);
        }
        return oldFilter;
    }

    private boolean streaksThankYouMessageAvailable(CommonModifierResponse commonModifierResponse) {
        return commonModifierResponse!=null && commonModifierResponse.getStreaksUserInfoResponse()!=null
                && commonModifierResponse.getStreaksUserInfoResponse().getPageDetails()!=null
                && commonModifierResponse.getStreaksUserInfoResponse().getPageDetails().getThankYou()!=null
                && StringUtils.isNotBlank(commonModifierResponse.getStreaksUserInfoResponse().getPageDetails().getThankYou().getMessage())
                && commonModifierResponse.getStreaksUserInfoResponse().isGostreaksActive();
    }

    private String getUserPAN(ExtendedUser extendedUser) {
		String panNo = null;
		List<UserTravelDocument> bookerTravelDocs = extendedUser.getTravelDocuments();
		panNo = findPanInDocs(bookerTravelDocs);
		if(null == panNo && !CollectionUtils.isEmpty(extendedUser.getAssociatedTravellers()) && extendedUser.getPersonalDetails() != null){
			String fname = extendedUser.getPersonalDetails().getName() != null && extendedUser.getPersonalDetails().getName().getFirstName()!=null ? extendedUser.getPersonalDetails().getName().getFirstName() : "";
			String lname = extendedUser.getPersonalDetails().getName() != null && extendedUser.getPersonalDetails().getName().getLastName()!=null ? extendedUser.getPersonalDetails().getName().getLastName() : "";
			for(UserAssociatedTraveller trvlr : extendedUser.getAssociatedTravellers()){
				String trvlFname = trvlr.getName() != null && StringUtils.isNotBlank(trvlr.getName().getFirstName()) ? trvlr.getName().getFirstName() : null;
				String trvlLname = trvlr.getName() != null && StringUtils.isNotBlank(trvlr.getName().getLastName()) ? trvlr.getName().getLastName() : null;
				if(fname.equalsIgnoreCase(trvlFname) && lname.equalsIgnoreCase(trvlLname)){
					List<UserTravelDocument> travelDocs = trvlr.getTravelDocuments();
					panNo = findPanInDocs(travelDocs);
					break;
				}
			}
		}
		return panNo;
	}

    private String findPanInDocs(List<UserTravelDocument> bookerTravelDocs) {
		String panNo = null;
		if(!CollectionUtils.isEmpty(bookerTravelDocs)){
			for(UserTravelDocument travelDocument : bookerTravelDocs){
				if(Constants.DOC_TYPE_PAN.equals(travelDocument.getDocType())
						&& StringUtils.isNotEmpty(travelDocument.getDocNumber())){
					panNo = travelDocument.getDocNumber();
					break;
				}
			}
		}
		return panNo;
	}

    private void buildSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody, AvailPriceCriteria searchCriteria, CommonModifierResponse commonModifierResponse) {

        List<String> hotelIds = new ArrayList<String>();
        hotelIds.add(searchCriteria.getHotelId());
        super.populateSearchCriteria(priceByHotelsRequestBody, searchCriteria, hotelIds, commonModifierResponse);
        priceByHotelsRequestBody.setPricingKey(searchCriteria.getPricingKey());
        priceByHotelsRequestBody.setRoomCriteria(buildRoomCriteria(searchCriteria.getRoomCriteria(), searchCriteria.getHotelId(), searchCriteria.getPricingKey()));
        priceByHotelsRequestBody.setSupplierCode(searchCriteria.getRoomCriteria().get(0).getSupplierCode());
        priceByHotelsRequestBody.setExtraInfo(buildExtraInfo(searchCriteria.getSearchType()));
        priceByHotelsRequestBody.setMetaChannelInfo(searchCriteria.getMetaChannelInfo());
        priceByHotelsRequestBody.setGiHotelId(searchCriteria.getGiHotelId());
        priceByHotelsRequestBody.setVcId(searchCriteria.getVcId());
        priceByHotelsRequestBody.setRoomStayCandidates(buildRoomStayCandidates(searchCriteria.getRoomCriteria().stream().map(a -> a.getRoomStayCandidates()).flatMap(List::stream).collect(Collectors.toList())));
        priceByHotelsRequestBody.setUpgradeAvailable(searchCriteria.isCheckUpgrade());
    }

    private ExtraInfo buildExtraInfo(String searchType) {
        if (searchType == null)
            return null;

        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setSearchType(searchType);
        return extraInfo;

    }

    private List<RoomCriterion> buildRoomCriteria(List<AvailRoomsSearchCriteria> roomCriteria, String hotelId, String pricingKey) {

        if (CollectionUtils.isNotEmpty(roomCriteria)){
            List<RoomCriterion> roomCriterionList = new ArrayList<>();

            for (AvailRoomsSearchCriteria roomCriteriaCG : roomCriteria){
                RoomCriterion roomCriterion = new RoomCriterion();
                roomCriterion.setHotelId(hotelId);
                roomCriterion.setMtKey(roomCriteriaCG.getMtKey());
                roomCriterion.setPricingKey(StringUtils.isNotBlank(roomCriteriaCG.getPricingKey()) ? roomCriteriaCG.getPricingKey() : pricingKey);
                roomCriterion.setRatePlanCode(roomCriteriaCG.getRatePlanCode());
                roomCriterion.setLucky(roomCriteriaCG.isLucky());
                roomCriterion.setRoomCode(roomCriteriaCG.getRoomCode());
                roomCriterion.setRoomStayCandidates(buildRoomStayCandidates(roomCriteriaCG.getRoomStayCandidates()));
                roomCriterion.setSupplierCode(roomCriteriaCG.getSupplierCode());
                roomCriterionList.add(roomCriterion);
            }
            return roomCriterionList;
        }

        return null;

    }

    private List<com.mmt.hotels.model.request.RoomStayCandidate> buildRoomStayCandidates(List<RoomStayCandidate> roomStayCandidates) {


        if(roomStayCandidates==null)
            return null;

        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates){
            com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(RoomStayCandidate roomStayCandidateCG) {


        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidateCG.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;

    }

    private void buildRequestDetails(PriceByHotelsRequestBody priceByHotelsRequestBody, RequestDetails requestDetails,CommonModifierResponse commonModifierResponse) {

        priceByHotelsRequestBody.setFunnelSource(requestDetails.getFunnelSource());
        priceByHotelsRequestBody.setIdContext(commonHelper.updateIdContext(requestDetails.getIdContext(), priceByHotelsRequestBody.getBookingDevice()));
        priceByHotelsRequestBody.setChannel(requestDetails.getChannel());
        priceByHotelsRequestBody.setPaymentChannel(requestDetails.getChannel());
        priceByHotelsRequestBody.setPageContext(requestDetails.getPageContext());
        priceByHotelsRequestBody.setVisitorId(requestDetails.getVisitorId());
        priceByHotelsRequestBody.setVisitNumber(requestDetails.getVisitNumber() != null?
                String.valueOf(requestDetails.getVisitNumber()): "");
        priceByHotelsRequestBody.setLoggedIn(requestDetails.isLoggedIn());
        priceByHotelsRequestBody.setNotifCoupon(requestDetails.getNotifCoupon());
        priceByHotelsRequestBody.setPayMode(requestDetails.getPayMode());
        if(null != requestDetails.getTrafficSource()) {
            priceByHotelsRequestBody.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource()));
        }
        priceByHotelsRequestBody.setPreApprovedValidity(requestDetails.getPreApprovedValidity());
        if (commonModifierResponse.getUserLocation() != null) {
            priceByHotelsRequestBody.setSrCty(commonModifierResponse.getUserLocation().getCity());
            priceByHotelsRequestBody.setSrcState(commonModifierResponse.getUserLocation().getState());
            priceByHotelsRequestBody.setSrCon(commonModifierResponse.getUserLocation().getCountry());
        }
        priceByHotelsRequestBody.setSrLat(requestDetails.getSrLat());
        priceByHotelsRequestBody.setSrLng(requestDetails.getSrLng());
        priceByHotelsRequestBody.setCouponCount(requestDetails.getCouponCount() != null? requestDetails.getCouponCount(): 0);
        priceByHotelsRequestBody.setFirstTimeUserState(requestDetails.getFirstTimeUserState());
        priceByHotelsRequestBody.setWishCode(requestDetails.getWishCode());
        priceByHotelsRequestBody.setRequisitionID(requestDetails.getRequisitionID());
        priceByHotelsRequestBody.setMyBizFlowIdentifier(requestDetails.getMyBizFlowIdentifier());
        priceByHotelsRequestBody.setRequestIdentifier(utility.buildRequestIdentifier(requestDetails));
        setOtherDetails(priceByHotelsRequestBody);
    }

    private void setOtherDetails(PriceByHotelsRequestBody priceByHotelsRequestBody) {

        priceByHotelsRequestBody.setApplicationId("310");
        priceByHotelsRequestBody.setDomain("B2C");
        priceByHotelsRequestBody.setRequestType("B2CAgent");
        priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());

    }

    private GuestRecommendationEnabledReqBody buildGuestRecommendationEnabled() {
        GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
        guestRecommendationEnabledReqBody.setMaxRecommendations("1");
        guestRecommendationEnabledReqBody.setText("true");
        return guestRecommendationEnabledReqBody;
    }

    private com.mmt.hotels.model.request.TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {

        com.mmt.hotels.model.request.TrafficSource trafficSourceCB = new com.mmt.hotels.model.request.TrafficSource();
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        return trafficSourceCB;

    }

    private EmiDetailsRequest buildEmiDetail(EMIDetail emiDetail) {

        if (emiDetail == null)
            return null;

        EmiDetailsRequest emiDetailCB = new EmiDetailsRequest();
        emiDetailCB.setBankId(emiDetail.getBankId());
        emiDetailCB.setBankName(emiDetail.getBankName());
        emiDetailCB.setPayOption(emiDetail.getPayOption());
        emiDetailCB.setTenure(emiDetail.getTenure());
        return emiDetailCB;

    }

}