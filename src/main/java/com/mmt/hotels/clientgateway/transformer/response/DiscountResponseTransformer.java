package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.BNPLDisabledReason;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.discount.CharityInfo;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import com.mmt.hotels.model.response.txn.UserCard;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GIFT_CARD_TEXT_GI;

@Component
public class DiscountResponseTransformer {

	@Autowired
	private CommonResponseTransformer commonResponseTransformer;

	@Autowired
	private PolyglotService polyglotService;

	@Autowired
	Utility utility;

	
	public ValidateCouponResponseBody convertValidateCouponResponse(ValidateCouponResponse validateCouponResponse,
																	String expData, String countryCode, boolean showBnplCard, String client) {

		ValidateCouponResponseBody response = new ValidateCouponResponseBody();
		TotalPricing totalPricing = new TotalPricing();
		Map<String, String > expDataMap = utility.getExpDataMap(expData);
		if(expDataMap == null)
			expDataMap = new HashMap<>();
		if (expDataMap != null) {
			if (Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))
				expDataMap.put("wallet_exp", (validateCouponResponse != null && validateCouponResponse.getWalletVariant() != null && validateCouponResponse.getWalletVariant() > 0) ? validateCouponResponse.getWalletVariant().toString() : "0");
			else
				expDataMap.put(WalletExpIH, (validateCouponResponse != null && validateCouponResponse.getWalletVariant() != null && validateCouponResponse.getWalletVariant() > 0) ? validateCouponResponse.getWalletVariant().toString() : "0");
			expDataMap.put("goTribe3", (validateCouponResponse != null && validateCouponResponse.isGoTribe3Exp()) ? "true" : "false");
		}
		if (validateCouponResponse != null && validateCouponResponse.getDisplayPriceBreakDown() != null) {
			List<PricingDetails> pricingDetails = commonResponseTransformer.getPricingDetails(validateCouponResponse.getDisplayPriceBreakDown(), countryCode, validateCouponResponse.getValidCouponResult().getPayMode(),
					validateCouponResponse.getValidCouponResult().getCorpMetaInfo()!=null,
					commonResponseTransformer.getCorporateSegmentId(validateCouponResponse.getRoomTypes()), expDataMap, false,validateCouponResponse.getDisplayPriceBreakDown().isCbrAvailable(), validateCouponResponse.getLoyaltyMessage(), validateCouponResponse.getClmPersuasion(), null, null, 0.0,0.0);
			totalPricing.setDetails(pricingDetails);

			if (validateCouponResponse.getCharityInfo() != null && CollectionUtils.isNotEmpty(totalPricing.getDetails())) {
				PricingDetails charity = getCharityPricingDetails(validateCouponResponse.getCharityInfo());
				if (charity != null) totalPricing.getDetails().add(charity);
			}
			
			List<Coupon> coupons = commonResponseTransformer.getCouponDetails(validateCouponResponse.getDisplayPriceBreakDown());
			totalPricing.setCoupons(coupons);
			totalPricing.setPinCodeMandatory(validateCouponResponse.getDisplayPriceBreakDown().isPinCodeMandatory());
			EMIDetail emiDetail = commonResponseTransformer.getEmiDetails(validateCouponResponse.getDisplayPriceBreakDown());
			totalPricing.setEmiBankDetails(emiDetail);
			totalPricing.setPricingKey(validateCouponResponse.getDisplayPriceBreakDown().getPricingKey());

			String pahText = polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_POLICY_TEXT_GENERIC);
			if (validateCouponResponse.getValidCouponResult().getPayMode() != null)
				Utility.updatePayAtHotelText(totalPricing, validateCouponResponse.getValidCouponResult().getPayMode(), pahText);

		}
		
		if (validateCouponResponse.getValidCouponResult()!= null && validateCouponResponse.getValidCouponResult().getCorpMetaInfo()!=null){
			response.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(validateCouponResponse.getValidCouponResult().getCorpMetaInfo()));
		}

		BNPLVariant bnplVariant = null;
		if(validateCouponResponse.getValidCouponResult() != null){
			bnplVariant = validateCouponResponse.getValidCouponResult().getBnplVariant();
			totalPricing.setCurrency(validateCouponResponse.getValidCouponResult().getCurrency());
			totalPricing.setAffiliateFeeDetails(commonResponseTransformer.buildAffiliateFeeDetails(validateCouponResponse.getValidCouponResult().getAffiliateFeeOptions()));
			commonResponseTransformer.updateTotalAmountInHotelierCurrency(totalPricing.getDetails(), validateCouponResponse.getValidCouponResult().getPayMode(),
					validateCouponResponse.getValidCouponResult().getCurrency(), validateCouponResponse.getValidCouponResult().getHotelierCurrency(),
					validateCouponResponse.getValidCouponResult().getHotelierCurrencyConvFactor());

			boolean bnplDisabledDueToNonBnplCouponApplied = commonResponseTransformer.checkIfInvalidCoupon(totalPricing);
			boolean insuranceAddonSelected = validateCouponResponse.getDisplayPriceBreakDown() != null && MapUtils.isNotEmpty(validateCouponResponse.getDisplayPriceBreakDown().getInsuranceBreakupMap());
			boolean goCashApplied = validateCouponResponse.getDisplayPriceBreakDown() != null && validateCouponResponse.getGoCashDetails() != null && validateCouponResponse.getDisplayPriceBreakDown().getGoCashDetails().isGoCashApplied();

			BNPLDisabledReason bnplDisabledReason = null;
			if (validateCouponResponse.isShowDisabledBnplDetails()) {
				bnplDisabledReason = commonResponseTransformer.getBNPLDisabledReason(validateCouponResponse.isUserLevelBnplDisabled(), bnplDisabledDueToNonBnplCouponApplied, insuranceAddonSelected, goCashApplied);
			}

			if (CollectionUtils.isNotEmpty(validateCouponResponse.getHydraSegments()) || ((CollectionUtils.isEmpty(validateCouponResponse.getHydraSegments())
					&& StringUtils.isNotBlank(client) && !client.equalsIgnoreCase(Constants.DEVICE_IOS) && !client.equalsIgnoreCase(Constants.DEVICE_OS_ANDROID)))) {
				response.setBnplDetails(
						commonResponseTransformer.buildBNPLDetails(
								validateCouponResponse.getValidCouponResult().isBnplApplicable(), validateCouponResponse.getValidCouponResult().getBnplPersuasionMsg(),
								validateCouponResponse.getValidCouponResult().getBnplPolicyText(), validateCouponResponse.getValidCouponResult().getBnplNewVariantText(),
								validateCouponResponse.getValidCouponResult().getBnplNewVariantSubText(), validateCouponResponse.getValidCouponResult().isOriginalBNPL(),
								showBnplCard, bnplVariant, bnplDisabledReason, countryCode, validateCouponResponse.getValidCouponResult().getBnplExtraFees()
						)
				);
				if (response.getBnplDetails() != null) {
					response.getBnplDetails().setDetails(
							commonResponseTransformer.getPricingDetails(
									validateCouponResponse.getDisplayPriceBreakDown(), countryCode, validateCouponResponse.getValidCouponResult().getPayMode(),
									validateCouponResponse.getValidCouponResult().getCorpMetaInfo() != null, commonResponseTransformer.getCorporateSegmentId(validateCouponResponse.getRoomTypes()),
									expDataMap, false, validateCouponResponse.getDisplayPriceBreakDown().isCbrAvailable(), validateCouponResponse.getLoyaltyMessage(),
									validateCouponResponse.getClmPersuasion(), null, null, validateCouponResponse.getValidCouponResult().getBnplExtraFees(),0.0
							)
					);
				}

				if (validateCouponResponse.getValidCouponResult().getFullPayment() != null) {
					double tcsAmount = validateCouponResponse.getDisplayPriceBreakDown() != null
							? validateCouponResponse.getDisplayPriceBreakDown().getTcsAmount() : 0d;
					response.setFullPayment(utility.buildFullPayment(validateCouponResponse.getValidCouponResult().getFullPayment(),
							validateCouponResponse.getValidCouponResult().getBnplExtraFees(), tcsAmount));
				}
			}

			response.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(validateCouponResponse.getValidCouponResult().getCancellationTimeline()));
			response.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(validateCouponResponse.getValidCouponResult().getCancellationTimeline()));
			response.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(validateCouponResponse.getPaymentPlan()));
			if(validateCouponResponse.getUserCards() != null && validateCouponResponse.getUserCards().size() > 0) {
				int tierNumber = validateCouponResponse.getBlackInfo() != null && StringUtils.isNotBlank(validateCouponResponse.getBlackInfo().getTierNumber()) ? Integer.parseInt(validateCouponResponse.getBlackInfo().getTierNumber()) : 0;
				UserCard card = commonResponseTransformer.GetUserCardById(validateCouponResponse.getUserCards(), COMMONS_CARD_TEMPLATE_ID, tierNumber);
				if(card != null)
					response.setReviewCommonsCardData(card);
			}

			response.setEmiDetails(commonResponseTransformer.getEmiAbridgeDetails(validateCouponResponse.getEmiDetails()));
			response.setStatusMessage(validateCouponResponse.getValidCouponResult().getSuccessApplyMessage());
			List<Coupon> coupons = commonResponseTransformer.getCouponDetails(validateCouponResponse.getDisplayPriceBreakDown());
			if (StringUtils.isBlank((response.getStatusMessage())) && coupons != null && !coupons.isEmpty()){
				response.setStatusMessage(coupons.get(0).getDescription());
			}
			
		}

		if (validateCouponResponse.getMsmeCorpCard() != null) {
			response.setMsmeCorpCard(validateCouponResponse.getMsmeCorpCard());
		}
		
		response.setRateplanlist(commonResponseTransformer.buildRateplanList(validateCouponResponse.getRoomTypes(), response.getBnplDetails(), bnplVariant,
				validateCouponResponse.getDisplayPriceBreakDown(), expDataMap, validateCouponResponse.getLoyaltyMessage(), validateCouponResponse.getClmPersuasion(), countryCode));
		if(response.getCorpApprovalInfo() != null && response.getCorpApprovalInfo().isWalletQuickPayAllowed() && validateCouponResponse.getDisplayPriceBreakDown() != null) {
			response.setMyBizQuickPayConfig(commonResponseTransformer.buildMyBizQuickPayConfig(validateCouponResponse.getDisplayPriceBreakDown()));
		}
		//GIHTL-15995 building taj gift card or hotel credit persuasion
		if (validateCouponResponse.getDisplayPriceBreakDown() != null && validateCouponResponse.getDisplayPriceBreakDown().getHotelBenefitInfo() != null) {
			Map<String,PersuasionResponse> persuasionMap = response.getHotelPersuasions();
			if(MapUtils.isEmpty(response.getHotelPersuasions())) {
				persuasionMap = new HashMap<>();
			}
			persuasionMap.put(TAJ_CAMPAIGN_PERSUASION_KEY, commonResponseTransformer.buildTajGiftCardOrHotelCreditPersuasion(validateCouponResponse.getDisplayPriceBreakDown().getHotelBenefitInfo()));
			response.setHotelPersuasions(persuasionMap);
		}
		totalPricing.setCouponSubtext(polyglotService.getSafeTranslatedData(GIFT_CARD_TEXT_GI));
		response.setTotalPricing(totalPricing );

		// Set the BNPL unavailable message when BNPL is removed due to addition of coupon for which BNPL is not applicable
		if (StringUtils.isNotBlank(validateCouponResponse.getBnplUnavailableMsg())) {
			response.getTotalPricing().setBnplUnavailableMsg(validateCouponResponse.getBnplUnavailableMsg());
		}
		if(!Utility.gocashVariant(expDataMap, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY))){
			response.setClmPersuasion(validateCouponResponse.getClmPersuasion());
		}

		response.setGoCashDetails(validateCouponResponse.getGoCashDetails());
		response.setLoyaltyMessage(validateCouponResponse.getLoyaltyMessage());
		response.setFooterStrip(commonResponseTransformer.buildFooterStripValidateCoupon(validateCouponResponse.getDisplayPriceBreakDown(),
				validateCouponResponse.getLoyaltyMessage(), validateCouponResponse.getClmPersuasion(), validateCouponResponse.getHydraSegments(), expDataMap, validateCouponResponse.getBlackInfo(), countryCode));

		return response;

	}

	private PricingDetails getCharityPricingDetails(CharityInfo charityInfo) {
		PricingDetails pricingDetails = null;
		if (charityInfo != null && charityInfo.getCharityAmount() > 0.0d) {
			pricingDetails = new PricingDetails();
			pricingDetails.setAmount(charityInfo.getCharityAmount());
			pricingDetails.setKey(charityInfo.isGICharityV2()?CHARITY_KEY_V2:CHARITY_KEY);
			pricingDetails.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			//pricingDetails.setSubTitle(charityInfo.getTncUrl());
			pricingDetails.setLabel(Constants.CHARITY_LABEL);
			//pricingDetails.setSubLine("T&Cs");
		}
		return pricingDetails;
	}

}
