package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.AvailRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.AvailRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.ios.AvailRoomsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.AvailRoomsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.desktop.AvailRoomsResponseTransformerDesktop;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AvailRoomsFactory {

    @Autowired
    private AvailRoomsRequestTransformer availRoomsRequestTransformer;

    @Autowired
    private AvailRoomsResponseTransformerPWA availRoomsResponseTransformerPWA;

    @Autowired
    private AvailRoomsResponseTransformerDesktop availRoomsResponseTransformerDesktop;

    @Autowired
    private AvailRoomsResponseTransformerAndroid availRoomsResponseTransformerAndroid;

    @Autowired
    private AvailRoomsResponseTransformerIOS availRoomsResponseTransformerIOS;


    public AvailRoomsRequestTransformer getRequestService(String client) {
    	if (StringUtils.isEmpty(client))
			return availRoomsRequestTransformer;
        switch(client.toUpperCase()) {
            case "PWA": return availRoomsRequestTransformer;
            case "DESKTOP": return availRoomsRequestTransformer;
            case "ANDROID": return availRoomsRequestTransformer;
            case "IOS": return availRoomsRequestTransformer;
        }
        return availRoomsRequestTransformer;
    }

    public AvailRoomsResponseTransformer getResponseService(String client) {
    	if (StringUtils.isEmpty(client))
			return availRoomsResponseTransformerDesktop;
        switch(client){
            case "PWA": return availRoomsResponseTransformerPWA;
            case "DESKTOP": return availRoomsResponseTransformerDesktop;
            case "ANDROID": return availRoomsResponseTransformerAndroid;
            case "IOS": return availRoomsResponseTransformerIOS;
        }
        return availRoomsResponseTransformerDesktop;
    }

}
