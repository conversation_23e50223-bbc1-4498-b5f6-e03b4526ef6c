package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.UpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatedPriceResponseTransformer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpdatedPriceFactory {


    @Autowired
    private UpdatedPriceRequestTransformer updatedPriceRequestTransformer;

    @Autowired
    private UpdatedPriceResponseTransformer updatedPriceResponseTransformer;
    
    public UpdatedPriceRequestTransformer getRequestService(String client) {
    	if (StringUtils.isEmpty(client))
			return updatedPriceRequestTransformer;
        switch(client) {
            case "PWA": return updatedPriceRequestTransformer;
            case "DESKTOP": return updatedPriceRequestTransformer;
            case "ANDROID": return updatedPriceRequestTransformer;
            case "IOS": return updatedPriceRequestTransformer;
        }
        return updatedPriceRequestTransformer;
    }

    public UpdatedPriceResponseTransformer getResponseService(String client) {
    	if (StringUtils.isEmpty(client))
			return updatedPriceResponseTransformer;
        switch(client) {
            case "PWA": return updatedPriceResponseTransformer;
            case "DESKTOP": return updatedPriceResponseTransformer;
            case "ANDROID": return updatedPriceResponseTransformer;
            case "IOS": return updatedPriceResponseTransformer;
        }
        return updatedPriceResponseTransformer;
    }

}
