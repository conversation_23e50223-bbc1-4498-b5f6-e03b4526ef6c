package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.response.Address;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.SoldOutInfoCG;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SoldOutInfo;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import com.mmt.hotels.pojo.response.ListingHotelMapEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ListingMapResponseTransformer {

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    private static final Logger LOGGER = LoggerFactory.getLogger(ListingMapResponseTransformer.class);

    public ListingMapResponse convertListingMapResponse(HotelListingMapResponse listingMapResponse, String expData, DeviceDetails deviceDetails, CommonModifierResponse commonModifierResponse) {
        if (listingMapResponse == null) {
            return null;
        }

        ListingMapResponse listingMapResponseCG = new ListingMapResponse();
        listingMapResponseCG.setHotelCount(listingMapResponse.getTotalHotelCounts());
        listingMapResponseCG.setHotelCountInCity(listingMapResponse.getHotelCountInCity());
        listingMapResponseCG.setHotelCountInViewPort(listingMapResponse.getHotelCountInViewPort());
        listingMapResponseCG.setNoMoreHotels(listingMapResponse.isNoMoreAvailableHotels());
        listingMapResponseCG.setLocationDetail(buildLocationDetail(listingMapResponse));
        listingMapResponseCG.setCityLocationDetail(buildLocationDetail(listingMapResponse));
        listingMapResponseCG.setHotels(buildHotelList(listingMapResponse.getHotelList(), listingMapResponse.getCountryCode(), expData,deviceDetails, commonModifierResponse));
        listingMapResponseCG.setPois(commonResponseTransformer.getPois(listingMapResponse.getPoiList()));
        return listingMapResponseCG;
    }

    private List<Hotel> buildHotelList(List<ListingHotelMapEntity> hotelList, String countryCode, String expData,DeviceDetails deviceDetails, CommonModifierResponse commonModifierResponse) {

        if (hotelList == null) {
            return null;
        }

        List<Hotel> hotelListCG  = new ArrayList<>();

        for (ListingHotelMapEntity hotelCB: hotelList){
            Hotel hotelCG  = new Hotel();

            hotelCG.setId(hotelCB.getId());
            hotelCG.setName(hotelCB.getName());
            hotelCG.setPropertyType(hotelCB.getPropertyType());
            hotelCG.setPropertyLabel(hotelCB.getPropertyLabel());
            hotelCG.setStayType(hotelCB.getStayType());
            hotelCG.setLocationDetail(buildLocationInfo(hotelCB));
            hotelCG.setStarRating(hotelCB.getStarRating());
            hotelCG.setAddress(buildAddress(hotelCB.getAddress()));
            hotelCG.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelCB.getGeoLocation()));
            hotelCG.setPriceDetail(buildPriceDetail(hotelCB.getDisplayFare(), commonResponseTransformer.enableSaveValue(expData)));
            hotelCG.setReviewSummary(commonResponseTransformer.buildReviewSummary(countryCode,hotelCB.getFlyfishReviewSummary(),deviceDetails,commonModifierResponse!=null?commonModifierResponse.getExpDataMap():null));
            hotelCG.setSoldOut(hotelCB.getIsSoldOut());
            hotelCG.setSoldOutInfo(buildSoldOutInfo(hotelCB.getSoldOutInfo()));
            hotelCG.setMedia(commonResponseTransformer.buildMedia(hotelCB.getMainImages(),null,expData));
            hotelCG.setLocationPersuasion(hotelCB.getLocationPersuasion());
            hotelCG.setHotelPersuasions(hotelCB.getHotelPersuasions());
            addLocationPersuasionToHotelPersuasions(hotelCG, hotelCB.getLocationPersuasion(),commonModifierResponse.getFunnelSource());
            hotelCG.setCategories(hotelCB.getCategories());
            hotelCG.setFreeCancellationText(hotelCB.getFreeCancellationText());
            hotelCG.setMmtHotelCategory(hotelCB.getMmtHotelCategory());
            hotelListCG.add(hotelCG);
        }
        return hotelListCG;
    }

    private PriceDetail buildPriceDetail(DisplayFare displayFare, boolean enableSaveValue) {

        if (displayFare == null || displayFare.getDisplayPriceBreakDown() == null) {
            return null;
        }

        DisplayPriceBreakDown displayPriceBreakDown = displayFare.getDisplayPriceBreakDown();
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setDisplayPrice(displayFare.getDisplayPriceBreakDown().getDisplayPrice());
        priceDetail.setNonDiscountedPrice(displayFare.getDisplayPriceBreakDown().getNonDiscountedPrice());
        priceDetail.setCoupon(buildCoupon(displayPriceBreakDown.getCouponInfo()));
        priceDetail.setEmiDetails(buildEmiDetails(displayPriceBreakDown.getEmiDetails()));
        priceDetail.setTotalTax(displayPriceBreakDown.getTotalTax());
        if(enableSaveValue) {
            priceDetail.setTotalSaving(displayPriceBreakDown.getTotalSaving());
            priceDetail.setSavingPerc(displayPriceBreakDown.getSavingPerc());
        }
        priceDetail.setPrice(displayPriceBreakDown.getNonDiscountedPrice() - (displayPriceBreakDown.isTaxIncluded() ?
                                                                              displayPriceBreakDown.getTotalTax() : 0));
        priceDetail.setPriceWithTax(
            displayPriceBreakDown.getNonDiscountedPrice() + (displayPriceBreakDown.isTaxIncluded() ? 0 :
                                                             displayPriceBreakDown.getTotalTax()));
        priceDetail.setDiscountedPrice(
            displayPriceBreakDown.getDisplayPrice() - (displayPriceBreakDown.isTaxIncluded() ?
                                                       displayPriceBreakDown.getTotalTax() : 0));
        priceDetail.setDiscountedPriceWithTax(
            displayPriceBreakDown.getDisplayPrice() + (displayPriceBreakDown.isTaxIncluded() ? 0 :
                                                       displayPriceBreakDown.getTotalTax()));
        priceDetail.setPricingKey(displayPriceBreakDown.getPricingKey());

        return priceDetail;
    }

    private Coupon buildCoupon(BestCoupon couponInfo) {
        if (couponInfo == null) {
            return null;
        }
        Coupon coupon = new Coupon();
        coupon.setDescription(couponInfo.getDescription());
        coupon.setCode(couponInfo.getCouponCode());
        coupon.setType(couponInfo.getType());
        coupon.setSpecialPromo(couponInfo.isSpecialPromoCoupon());
        return coupon;
    }

    private EMIDetail buildEmiDetails(Emi emiInfo){
        if (emiInfo == null) {
            return null;
        }
        EMIDetail emiDetail = new EMIDetail();
        emiDetail.setAmount((double) emiInfo.getEmiAmount());
        emiDetail.setType(emiInfo.getEmiType());
        emiDetail.setBankName(emiInfo.getBankName());
        emiDetail.setTenure(emiInfo.getTenure());
        emiDetail.setTotalCost(emiInfo.getTotalCost());
        emiDetail.setTotalInterest(emiInfo.getTotalInterest());
        return emiDetail;
    }

    private SoldOutInfoCG buildSoldOutInfo(SoldOutInfo soldOutInfo){
        if (soldOutInfo == null)
            return null;
        SoldOutInfoCG soldOutInfoCG = new SoldOutInfoCG();
        soldOutInfoCG.setSoldOutText(soldOutInfo.getSoldOutText());
        soldOutInfoCG.setSoldOutSubText(soldOutInfo.getSoldOutSubText());
        soldOutInfoCG.setSoldOutReason(soldOutInfo.getSoldOutReason());
        soldOutInfoCG.setSoldOutType(soldOutInfo.getSoldOutType());
        return soldOutInfoCG;
    }

    private Address buildAddress(com.mmt.hotels.model.response.staticdata.Address address) {

        if (address == null)
            return null;

        Address addressCG  = new Address();
        if (CollectionUtils.isNotEmpty(address.getArea())) {
            addressCG.setArea(address.getArea().get(0));
        }
        addressCG.setLine1(address.getLine1());
        addressCG.setLine2(address.getLine2());

        return addressCG;
    }

    private LocationDetail buildLocationInfo(ListingHotelMapEntity hotelCB) {
        if (hotelCB == null) {
            return null;
        }
        LocationDetail locationDetail = new LocationDetail(hotelCB.getCityCode(), hotelCB.getCityName(),
                                                           Constants.TYPE_CITY,
                                                           hotelCB.getCountryCode(), hotelCB.getCountryName());
        return locationDetail;
    }

    private LocationDetail buildLocationDetail(HotelListingMapResponse listingMapResponse) {
        LocationDetail locationDetail = new LocationDetail();
        locationDetail.setCountryName(listingMapResponse.getCountryName());
        locationDetail.setId(listingMapResponse.getCityCode());
        locationDetail.setName(listingMapResponse.getCityName());
        locationDetail.setType("city");
        locationDetail.setCountryId(listingMapResponse.getCountryCode());

        return locationDetail;
    }


    public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion,String funnelSource) {
        if (CollectionUtils.isNotEmpty(locationPersuasion)) {
            if (hotel.getHotelPersuasions() == null) {
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            }
            PersuasionObject locPers = new PersuasionObject();
            locPers.setData(new ArrayList<>());
            locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP);
            locPers.setTemplate("IMAGE_TEXT_H");
            locPers.setPlaceholder("SINGLE");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setHasAction(false);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");

            locPers.getData().add(locPersuasionData);
            if (locationPersuasion.size() == 1) {
                locPersuasionData.setText(locationPersuasion.get(0));
            } else if (locationPersuasion.size() >= 2) {
                locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
            }

            //if funnelSource is DAYUSE then we need to set iconType in locPersuasionData
            if (Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnelSource)) {
                locPersuasionData.setIcontype(Constants.LOCATION_PERSUASION_ICON_TYPE_DAYUSE);
            }

            try {
                ((Map<Object, Object>) hotel.getHotelPersuasions())
                    .put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP, locPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added in Listing Map due to ClassCastException : {} ", e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added in Listing Map due to : {} ", e.getMessage());
            }

        }
    }
}
