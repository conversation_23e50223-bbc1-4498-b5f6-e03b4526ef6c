package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.UpdatedPriceRoomCriteria;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.HermesRatePlanData;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.PaxWiseInfoResponse;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.PriceDetail;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class UpdatedPriceResponseTransformer {

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private Utility utility;

    @Autowired
    private DateUtil dateUtil;

    public UpdatePriceResponse convertUpdatedPriceResponse(PriceBreakDownResponse updatedPriceResponseCB, UpdatePriceRequest updatePriceRequest, CommonModifierResponse commonModifierResponse) {
        boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        String askedCurrency = updatePriceRequest.getSearchCriteria().getCurrency();
        List<UpdatedPriceRoomCriteria> updatedPriceRoomCriteria = updatePriceRequest.getSearchCriteria().getRoomCriteria();
        if (updatedPriceResponseCB == null)
            return null;
        String sellableType = CollectionUtils.isNotEmpty(updatedPriceRoomCriteria) ? updatedPriceRoomCriteria.get(0).getSellableType() :null;
        Integer roomCount = 1;
        if(CollectionUtils.isNotEmpty(updatedPriceRoomCriteria)){
            roomCount = 0;
            for(UpdatedPriceRoomCriteria rc : updatedPriceRoomCriteria){
                roomCount += rc.getRoomStayCandidates().size();
            }
        }
        String checkIn = updatePriceRequest.getSearchCriteria().getCheckIn();
        String checkOut = updatePriceRequest.getSearchCriteria().getCheckOut();
        int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn),LocalDate.parse(checkOut));
        UpdatePriceResponse updatePriceResponse = new UpdatePriceResponse();
        updatePriceResponse.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(updatedPriceResponseCB.getCorpMetaInfo()));
        updatePriceResponse.setPriceMap(commonResponseTransformer.getPriceMap(updatedPriceResponseCB.getDisplayPriceBreakDown(),
                updatedPriceResponseCB.getDisplayPriceBreakDownList(),
                updatePriceRequest.getExpDataMap(), roomCount, askedCurrency,
                sellableType, los, false, "",
                utility.buildToolTip(updatePriceRequest.getRequestDetails().getFunnelSource()),
                Utility.isGroupBookingFunnel(updatePriceRequest.getRequestDetails().getFunnelSource()), false,myPartner,null));
        if (MapUtils.isNotEmpty(updatePriceResponse.getPriceMap())) {
            updatePriceResponse.setDefaultPriceKey(getDefaultPriceKey(updatedPriceResponseCB.getDisplayPriceBreakDown()));
        }
        String totalPax = getTotalPaxString(updatePriceRequest.getSearchCriteria().getRoomCriteria());
        if (totalPax != null) {
            updatePriceResponse.setTotalPax(totalPax);
        }
        List<HermesRatePlanData> hermesRatePlanData = getUpdatePriceHermesData(
                updatePriceRequest.getSearchCriteria().getRoomCriteria());
        if (!hermesRatePlanData.isEmpty()) {
            updatePriceResponse.setHermesData(hermesRatePlanData);
        }
        return updatePriceResponse;
    }

    private List<HermesRatePlanData> getUpdatePriceHermesData(List<UpdatedPriceRoomCriteria> roomCriteria) {
        List<HermesRatePlanData> hermesRatePlanDataList = new ArrayList<>();
        for (UpdatedPriceRoomCriteria updatedPriceRoomCriteria: roomCriteria) {
            HermesRatePlanData hermesRatePlanData = new HermesRatePlanData();
            hermesRatePlanData.setRatePlanCode(updatedPriceRoomCriteria.getRatePlanCode());
            hermesRatePlanData.setRoomCode(updatedPriceRoomCriteria.getRoomCode());

            String paxString = String.valueOf(updatedPriceRoomCriteria.getRoomStayCandidates().size());
            for (RoomStayCandidate roomStayCandidate: updatedPriceRoomCriteria.getRoomStayCandidates()) {
                String roomStayPax = getRoomStayCandidatePax(roomStayCandidate);
                paxString = String.format("%s%s%s", paxString, Constants.ROOM_SEPARATOR, roomStayPax);
            }
            PaxWiseInfoResponse paxWiseInfoResponse = new PaxWiseInfoResponse();
            PriceDetail priceDetail = new PriceDetail();
            priceDetail.setPax(paxString);
            paxWiseInfoResponse.setPriceDetail(priceDetail);
            hermesRatePlanData.setPaxWiseInfo(Collections.singletonList(paxWiseInfoResponse));
            hermesRatePlanDataList.add(hermesRatePlanData);
        }
        return hermesRatePlanDataList;
    }

    private String getDefaultPriceKey(DisplayPriceBreakDown displayPriceBreakDown) {
        return displayPriceBreakDown != null ? (displayPriceBreakDown.getCouponInfo() != null ? displayPriceBreakDown.getCouponInfo().getCouponCode() : "DEFAULT") : null;
    }

    private String getTotalPaxString(List<UpdatedPriceRoomCriteria> roomCriteria) {
        if (CollectionUtils.isEmpty(roomCriteria)) {
            return null;
        }
        int totalRooms = 0;
        for (UpdatedPriceRoomCriteria updatedPriceRoomCriteria: roomCriteria) {
            totalRooms += updatedPriceRoomCriteria.getRoomStayCandidates().size();
        }
        String paxString = String.valueOf(totalRooms);
        for (UpdatedPriceRoomCriteria updatedPriceRoomCriteria: roomCriteria) {
            for (RoomStayCandidate roomStayCandidate: updatedPriceRoomCriteria.getRoomStayCandidates()) {
                String roomStayPax = getRoomStayCandidatePax(roomStayCandidate);
                paxString = String.format("%s%s%s", paxString, Constants.ROOM_SEPARATOR, roomStayPax);
            }
        }
        return paxString;
    }

    private String getRoomStayCandidatePax(RoomStayCandidate roomStayCandidate) {
        int adultCount = 0;
        String childAgeString;
        if (roomStayCandidate.getAdultCount() != null) {
            adultCount = roomStayCandidate.getAdultCount();
        }
        if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
            int childCount = roomStayCandidate.getChildAges().size();
            String childAge = roomStayCandidate.getChildAges().stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(Constants.OCCUPANCY_SEPARATOR));
            childAgeString = String.format("%s%s%s", childCount, Constants.OCCUPANCY_SEPARATOR, childAge);
        } else {
            childAgeString = "0";
        }
        return String.format("%s%s%s", adultCount, Constants.OCCUPANCY_SEPARATOR, childAgeString);
    }
}
