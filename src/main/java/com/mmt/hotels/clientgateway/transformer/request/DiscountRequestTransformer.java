package com.mmt.hotels.clientgateway.transformer.request;

import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.request.ValidateCouponRequest;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;

@Component
public class DiscountRequestTransformer {

	public ValidateCouponRequestBody convertValidateCouponRequest(ValidateCouponRequest validateCouponRequest) {
		ValidateCouponRequestBody req = new ValidateCouponRequestBody();
		req.setTxnKey(validateCouponRequest.getTxnKey());
		req.setBrand(validateCouponRequest.getBrand());
		req.setCouponCode(validateCouponRequest.getCouponCode());
		req.setRemoveCoupon(validateCouponRequest.isRemoveCoupon());
		req.setCorrelationKey(validateCouponRequest.getCorrelationKey());
		req.setQuoteId(validateCouponRequest.getQuoteId());
		req.setUserLocation(validateCouponRequest.getUserLocation());
		return req;
	}

}
