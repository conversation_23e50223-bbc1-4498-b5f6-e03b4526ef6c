package com.mmt.hotels.clientgateway.transformer.request.android;

import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.transformer.request.UpdatePolicyRequestTransformer;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class UpdatePolicyRequestTransformerAndroid extends UpdatePolicyRequestTransformer {

    @Override
    public UpdatePolicyRequest convertUpdatePolicyRequest(UpdatePolicyRequest updatePolicyRequest, Map<String, String> headers, String correlationKey) {
        return super.convertUpdatePolicyRequest(updatePolicyRequest, headers, correlationKey);
    }
}
