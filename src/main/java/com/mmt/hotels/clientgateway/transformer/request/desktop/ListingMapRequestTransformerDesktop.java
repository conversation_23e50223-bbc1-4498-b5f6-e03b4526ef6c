package com.mmt.hotels.clientgateway.transformer.request.desktop;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.ListingMapRequest;
import com.mmt.hotels.clientgateway.transformer.request.ListingMapRequestTransformer;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.springframework.stereotype.Component;

@Component
public class ListingMapRequestTransformerDesktop extends ListingMapRequestTransformer {
    @Override
    public SearchWrapperInputRequest convertListingMapRequest(ListingMapRequest listingMapRequest, 
    		CommonModifierResponse commonModifierResponse) {
        SearchWrapperInputRequest searchWrapperInputRequest =  super.convertListingMapRequest(listingMapRequest, 
        		commonModifierResponse);
        return searchWrapperInputRequest;
    }
}
