package com.mmt.hotels.clientgateway.transformer.response;

import com.gommt.hotels.content.proto.mediaid.MediaIdRequest;
import com.gommt.hotels.content.proto.mediaid.MediaIdResponse;
import com.gommt.hotels.content.proto.mediaobj.MediaObj;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaObj;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.response.gi.mediabyid.TaggedMediaByIdResponse;
import com.mmt.hotels.clientgateway.response.gi.taggedmedia.*;
import com.mmt.hotels.model.response.errors.Error;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

@Component
public class GrpcContentResponseBuilder {
    private static final Gson gson = new Gson();

    private Map<String,String> imageResizeFQDNMapGI;

    @Value("${hotels.image.gi.cdn.fqdn}")
    private String imageResizeGiFQDN;
    private static final String AKAMAI_IMG_DOWNSIZE_PARAM = "downsize";
    private static final Logger LOGGER = LoggerFactory.getLogger(GrpcContentResponseBuilder.class);

    @PostConstruct
    public void init() {
        imageResizeFQDNMapGI = gson.fromJson(imageResizeGiFQDN, new TypeToken<Map<String, String>>() {
        }.getType());
    }

    public TaggedMediaResponse buildTaggedMediaResponse(com.gommt.hotels.content.proto.taggedmedia.TaggedMediaResponse grpcTaggedMediaResponse) {
        TaggedMediaResponse response = new TaggedMediaResponse();
        if (grpcTaggedMediaResponse == null) {
            response.setSuccess(false);
            return response;
        } else if (grpcTaggedMediaResponse.hasStatus() && !StringUtils.equalsIgnoreCase(grpcTaggedMediaResponse.getStatus().getMessage(), "ok") &&
                grpcTaggedMediaResponse.getStatus().getCode() > 0) {
            try {
                response.setResponseError(buildErrorResponse(grpcTaggedMediaResponse.getStatus().getCode(), grpcTaggedMediaResponse.getStatus().getMessage(),
                        grpcTaggedMediaResponse.getStatus().getErrorDetails() != null ? grpcTaggedMediaResponse.getStatus().getErrorDetails().getReason(): ""));
                throw new ErrorResponseFromDownstreamException(DependencyLayer.HSC, ErrorType.DOWNSTREAM,
                        response.getResponseError().getErrorCode(),
                        response.getResponseError().getErrorMessage());
            } catch (ErrorResponseFromDownstreamException e) {
                LOGGER.error("Error while creating Error Response From Downstream Exception {}", e.getMessage(), e);
            }
        }
        response.setSuccess(grpcTaggedMediaResponse.hasStatus() && StringUtils.equalsIgnoreCase(grpcTaggedMediaResponse.getStatus().getMessage(), "ok"));
        if (CollectionUtils.isNotEmpty(grpcTaggedMediaResponse.getTaggedMediaList())) {
            response.setData(buildTaggedMediaData(grpcTaggedMediaResponse.getTaggedMediaList()));
        }
        return response;
    }

    private Error buildErrorResponse(int code, String message, String reason) {
        Error error = new Error.Builder()
                .buildErrorCode(String.valueOf(code), message)
                .buildAlternateMessage(reason)
                .build();
        return error;
    }

    private TaggedMediaData buildTaggedMediaData(List<TaggedMediaObj> taggedMedia) {
        TaggedMediaData taggedMediaData = new TaggedMediaData();
        taggedMediaData.setHotelMedia(buildHotelMedia(taggedMedia));
        return taggedMediaData;
    }

    private HotelMedia buildHotelMedia(List<TaggedMediaObj> taggedMedia) {
        HotelMedia hotelMedia = new HotelMedia();
        hotelMedia.setTaggedMediaV3(buildTaggedMediaV3(taggedMedia));
        return hotelMedia;
    }

    private List<TaggedMedia> buildTaggedMediaV3(List<TaggedMediaObj> taggedMediaObjList) {
        List<TaggedMedia> taggedMediaList = new ArrayList<>();
        for (TaggedMediaObj taggedMediaObj : taggedMediaObjList) {
            TaggedMedia taggedMediaRes = new TaggedMedia();
            taggedMediaRes.setTitle(taggedMediaObj.getTitle());
            taggedMediaRes.setId(taggedMediaObj.getId());
            taggedMediaRes.setCount(taggedMediaObj.getCount());
            if (CollectionUtils.isNotEmpty(taggedMediaObj.getMediaList())) {
                taggedMediaRes.setMedia(buildMediaUrl(taggedMediaObj.getMediaList(), taggedMediaObj.getType(), taggedMediaRes));
            }
//            else if (CollectionUtils.isNotEmpty(taggedMediaObj.getVideoUrlList())) {
//                taggedMediaRes.setMedia(buildVideoUrl(taggedMediaObj));
//                taggedMediaRes.setAllVideos(true);
//            }
            taggedMediaList.add(taggedMediaRes);
        }
        return taggedMediaList;
    }

    private ProcessedVideos buildProcessedVideos(MediaObj videoMedia) {
        ProcessedVideos processedVideos = new ProcessedVideos();
        if (videoMedia != null) {
            processedVideos.setMp4Format(buildMp4Format(videoMedia.getVideoUrl()));
            processedVideos.setSnapshotUrl(videoMedia.getImageUrl());
            processedVideos.setMp4Thumbnail(buildMp4Format(videoMedia.getThumbnailVideoUrl()));
            processedVideos.setWebmFormat(buildMp4Format(""));
            processedVideos.setWebmThumbnail(buildMp4Format(""));
        }
        return processedVideos;
    }

    private FormatUrl buildMp4Format(String videoUrl) {
        FormatUrl formatUrl = new FormatUrl();
        formatUrl.setUrl(videoUrl);
        return formatUrl;
    }

    /**
     *  "t_fs":   "1920:1080",
     *  "t_g":    "634:357",
     *  "t_srp":  "275:375",
     *  "t_th":   "86:48",
     *  "t_r":    "200:113",
     *  "t_srpe": "200:400",
     *  "t_srpc": "328:180",
     */
    private List<Media> buildMediaUrl(List<MediaObj> mediaList, String type, TaggedMedia taggedMediaRes) {
        List<Media> mediaListRes = new ArrayList<>();
        for (MediaObj mediaObj : mediaList) {
            Media media = new Media();
            if (StringUtils.equalsIgnoreCase(type,"Image")) {
                media.setType("image");
                prepareOptimizedImageURL(mediaObj.getImageUrl(), media);
            } else if (StringUtils.equalsIgnoreCase(type,"Video")) {
                media.setType("video");
                media.setProcessedVideos(buildProcessedVideos(mediaObj));
                taggedMediaRes.setAllVideos(true);
            } else if (StringUtils.equalsIgnoreCase(type,"STREETVIEW")) {
                media.setType("streetView");
                prepareOptimizedImageURL(mediaObj.getImageUrl(), media);
            }
            mediaListRes.add(media);
        }
        return mediaListRes;
    }

    private String prepareOptimizedImageURL(String imageUrl, Media media) {
        if (MapUtils.isEmpty(imageResizeFQDNMapGI)) {
            return "";
        }
        Map<String, String> imageResizeFQDNMap = imageResizeFQDNMapGI ;
        StringBuilder resizedImageURL = new StringBuilder(StringUtils.EMPTY);
        boolean inhouseImage = false;
        try {
            URL imgUrl = new URL(imageUrl);
            String fqdn = imgUrl.getHost();
            Optional<String> resizedFQDN = imageResizeFQDNMap.keySet().stream().filter(m -> fqdn.equalsIgnoreCase(m)).findFirst();
            if (resizedFQDN.isPresent()) {
                String acutalImagePath = resizedFQDN.get();
                String replaceImagePath = imageResizeFQDNMap.get(acutalImagePath);
                if (StringUtils.isNotBlank(replaceImagePath)) {
                    inhouseImage = true;
                    resizedImageURL = resizedImageURL.append(imageUrl.replace(acutalImagePath, replaceImagePath));
                }
            }
            resizedImageURL = inhouseImage ? resizedImageURL.append("?").append(AKAMAI_IMG_DOWNSIZE_PARAM).append("=") : resizedImageURL.append(imageUrl);
            media.setBigUrl(inhouseImage ? resizedImageURL + Constants.BIG_URL : resizedImageURL.toString());
            media.setGalleryUrl(inhouseImage ? resizedImageURL + Constants.GALLERY_URL : resizedImageURL.toString());
            media.setThumbUrl(inhouseImage ? resizedImageURL + Constants.THUMB_URL : resizedImageURL.toString());
            media.setMobileThumbUrl(inhouseImage ? resizedImageURL + Constants.MOBILE_THUMB_URL : resizedImageURL.toString());

        }catch(MalformedURLException ex){
            LOGGER.error("malformed url exception : {}, imageUrl {}" ,ex.getMessage(), imageUrl );
        }catch(Exception ex){

            LOGGER.error("Unknown Exception : {}" ,ex.getMessage());
        }
        return resizedImageURL.toString().isEmpty() ? imageUrl : resizedImageURL.toString();
    }

    public TaggedMediaByIdResponse buildTaggedMediaByIdResponse(MediaIdRequest mediaIdRequest, MediaIdResponse mediaIdResponse) {
        TaggedMediaByIdResponse response = new TaggedMediaByIdResponse();
        if (mediaIdResponse == null) {
            response.setSuccess(false);
            return response;
        } else if (mediaIdResponse.hasStatus() && !StringUtils.equalsIgnoreCase(mediaIdResponse.getStatus().getMessage(), "ok") &&
                mediaIdResponse.getStatus().getCode() > 0) {
            try {
                response.setResponseError(buildErrorResponse(mediaIdResponse.getStatus().getCode(), mediaIdResponse.getStatus().getMessage(),
                        mediaIdResponse.getStatus().getErrorDetails() != null ? mediaIdResponse.getStatus().getErrorDetails().getReason(): ""));
                throw new ErrorResponseFromDownstreamException(DependencyLayer.HSC, ErrorType.DOWNSTREAM,
                        response.getResponseError().getErrorCode(),
                        response.getResponseError().getErrorMessage());
            } catch (ErrorResponseFromDownstreamException e) {
                LOGGER.error("Error while creating Error Response From Downstream Exception {}", e.getMessage(), e);
            }
        }
        response.setSuccess(mediaIdResponse.hasStatus() && StringUtils.equalsIgnoreCase(mediaIdResponse.getStatus().getMessage(), "ok"));
        if (CollectionUtils.isNotEmpty(mediaIdResponse.getMediaList())) {
            response.setData(buildTaggedMediaByIdData(mediaIdRequest, mediaIdResponse.getMediaList()));
        }
        return response;
    }

    private Map<String, List<Media>> buildTaggedMediaByIdData(MediaIdRequest mediaIdRequest, List<MediaObj> mediaObjList) {
        Map<String, List<Media>> mediaByIdDataMap = new HashMap<>();
        List<Media> mediaListRes = buildMediaByIdUrl(mediaObjList);
        mediaByIdDataMap.put(mediaIdRequest.getTag(), mediaListRes);
        return mediaByIdDataMap;
    }

    private List<Media> buildMediaByIdUrl(List<MediaObj> mediaObjList) {
        List<Media> mediaListRes = new ArrayList<>();
        for (MediaObj mediaObj : mediaObjList) {
            Media media = new Media();
            if (StringUtils.isEmpty(mediaObj.getVideoUrl())) {
                media.setType("image");
                prepareOptimizedImageURL(mediaObj.getImageUrl(), media);
            } else {
                media.setType("video");
                media.setProcessedVideos(buildProcessedVideos(mediaObj));
            }
            mediaListRes.add(media);
        }
        return mediaListRes;
    }
}
