package com.mmt.hotels.clientgateway.transformer.response.ios;

import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ThankYouResponseTransformerIOS extends ThankYouResponseTransformer {

    @Value("${thankyou.mytrips.deeplink}")
    private String myTripsDeeplink;

    @Value("${hotelDetail.deeplink.url}")
    private String hotelDetailsRawDeepLink;

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    public ThankYouResponse convertThankYouResponse(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity, FeatureFlags featureFlags, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ErrorResponseFromDownstreamException {
        return super.convertThankYouResponse(persistanceMultiRoomResponseEntity,featureFlags, parameterMap, httpHeaderMap);
    }

    @Override
    protected String getMytripActionCorpUrl(String cardType) {
        return myTripsCardTypeToIconUrls.get(cardType).getIconUrlIosCorp();
    }

    @Override
    protected String getMytripActionB2CUrl(String cardType) {
        return myTripsCardTypeToIconUrls.get(cardType).getIconUrlIos();
    }

    @Override
    protected String getHotelDetailsRawDeepLinkUrl() {
        return hotelDetailsRawDeepLink;
    }

    @Override
    protected String getMytripsRawDeepLinkUrl() {
        return myTripsDeeplink;
    }

    @Override
    protected boolean tildeRequiredInRSQ() {
        return true;
    }
}
