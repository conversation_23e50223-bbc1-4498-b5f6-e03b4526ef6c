package com.mmt.hotels.clientgateway.transformer.request.desktop;

import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.transformer.request.UpdatePolicyRequestTransformer;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class UpdatePolicyRequestTransformerDesktop extends UpdatePolicyRequestTransformer {

    @Override
    public UpdatePolicyRequest convertUpdatePolicyRequest(UpdatePolicyRequest updatePolicyRequest, Map<String, String> headers, String correlationKey) {
        return super.convertUpdatePolicyRequest(updatePolicyRequest, headers, correlationKey);
    }
}
