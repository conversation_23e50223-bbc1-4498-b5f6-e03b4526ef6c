package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.da.AvailDetails;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.ResponseContextDetail;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomAmentiesHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomInfoHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsMediaHelper;
import com.mmt.hotels.clientgateway.util.DayUseUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.TAXES_LABEL;

/**
 * Transformer class for handling OrchV2 search slots (day use) response conversions.
 * This class is specifically responsible for converting OrchV2 HotelDetailsResponse 
 * to DayUseRoomsResponse for search slots API.
 * 
 * Separated from OrchSearchRoomsResponseTransformer to maintain proper separation 
 * of concerns between search-rooms and search-slots APIs.
 */
@Component
public class OrchSearchSlotsResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchSlotsResponseTransformer.class);

    @Autowired
    private Utility utility;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private RoomInfoHelper roomInfoHelper;

    @Autowired
    private RoomAmentiesHelper roomAmentiesHelper;

    @Autowired
    private SearchRoomsMediaHelper searchRoomsMediaHelper;

    @Autowired
    private DayUseUtil dayUseUtil;

    @Autowired
    PropertyManager propManager;

    // Configuration objects initialized in @PostConstruct
    private MissingSlotDetail missingSlotDetails = null;
    private Map<String, DayUsePersuasion> dayUseFunnelPersuasions = null;

    /**
     * Initializes configuration objects from consul.
     * This method is called lazily to ensure configurations are loaded when needed.
     */
    @PostConstruct
    void init() {

        CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
        missingSlotDetails = commonConfig.missingSlotDetails();
        commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
        dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions();
        commonConfig.addPropertyChangeListener("dayUseFunnelPersuasions", event -> dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions());

    }

    /**
     * Converts OrchV2 HotelDetailsResponse to DayUseRoomsResponse for search slots API.
     * This method handles the complete transformation from OrchV2 format to legacy dayUse format.
     *
     * @param dayUseRoomsRequest The original request containing search criteria
     * @param hotelDetailsResponse The OrchV2 response from orchestrator
     * @param commonModifierResponse Common modifier response containing experiments
     * @return DayUseRoomsResponse formatted for clients
     */
    public DayUseRoomsResponse convertSearchSlotsResponse(DayUseRoomsRequest dayUseRoomsRequest, 
                                                          HotelDetailsResponse hotelDetailsResponse, 
                                                          CommonModifierResponse commonModifierResponse) {
        long startTime = System.currentTimeMillis();
        DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
        SearchRoomsCriteria searchRoomsCriteria = dayUseRoomsRequest.getSearchCriteria();
        String expVariantKeys = dayUseRoomsRequest.getExpVariantKeys();

        try {

            // Fix: Correct null check - should be == null, not != null
            if (hotelDetailsResponse == null || hotelDetailsResponse.getHotelDetails() == null) {
                return dayUseRoomsResponse;
            }

            HotelDetails hotelDetails = hotelDetailsResponse.getHotelDetails();
            String askedCurrency = (searchRoomsCriteria != null && StringUtils.isNotBlank(searchRoomsCriteria.getCurrency()))
                    ? searchRoomsCriteria.getCurrency() : Constants.DEFAULT_CUR_INR;

            // Check if we have rooms or room combos
            if (CollectionUtils.isEmpty(hotelDetails.getRooms()) && CollectionUtils.isEmpty(hotelDetails.getRoomCombos())) {
                return dayUseRoomsResponse;
            }

            // Build DayUseSlotPlan list from OrchV2 rooms data (similar to legacy setDayUseSlotPlanList)
            List<DayUseSlotPlan> dayUseSlotPlanList = buildDayUseSlotPlansFromOrchV2(hotelDetails, dayUseRoomsRequest, searchRoomsCriteria);
            if (!dayUseSlotPlanList.isEmpty()) dayUseRoomsResponse.setSlotPlans(dayUseSlotPlanList);

            // Build DayUseRoom list from OrchV2 rooms data
            List<DayUseRoom> dayUseRoomList = buildDayUseRooms(hotelDetails, dayUseRoomsRequest);
            if (!dayUseRoomList.isEmpty()) dayUseRoomsResponse.setRooms(dayUseRoomList);
            buildHeaderPriceDetails(hotelDetails, dayUseRoomsResponse);

            // Set basic response properties (similar to legacy)
            dayUseRoomsResponse.setSearchType("R");
            dayUseRoomsResponse.setDefaultPriceKey("DEFAULT");

            // Populate missing slots (similar to legacy populateMissingSlots)
            if (dayUseRoomsRequest.getSearchCriteria() != null && dayUseRoomsRequest.getSearchCriteria().getSlot() != null) {
                populateMissingSlots(dayUseRoomsResponse, dayUseRoomsRequest.getSearchCriteria().getSlot().getTimeSlot());
            }

            // Set feature flags and other metadata
            dayUseRoomsResponse.setExpVariantKeys(StringUtils.isNotBlank(expVariantKeys) ? expVariantKeys : null);
            dayUseRoomsResponse.setFeatureFlags(getFeatureFlags(hotelDetails, (commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null)));
            dayUseRoomsResponse.getFeatureFlags().setDayUsePersuasion(dayUseRoomsRequest.getFeatureFlags() != null && dayUseRoomsRequest.getFeatureFlags().isDayUsePersuasion());
            dayUseRoomsResponse.setContextDetails(getContextDetails(hotelDetails));
            dayUseRoomsResponse.setDayUsePersuasions(buildDayUsePersuasion(hotelDetails.getDayUsePersuasions()));

            LOGGER.info("Successfully converted OrchV2 response to DayUseRoomsResponse");

        } catch (Exception e) {
            LOGGER.error("Error converting search slots response from OrchV2: {}", e.getMessage(), e);
        } finally {
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, "DETAIL_SEARCH_SLOTS", System.currentTimeMillis() - startTime);
        }

        return dayUseRoomsResponse;
    }


    private FeatureFlags getFeatureFlags(HotelDetails hotelDetails, LinkedHashMap<String, String> expDataMap) {
        if (hotelDetails == null)
            return null;
        HotelRateFlags hotelRateFlags = hotelDetails.getHotelRateFlags();
        FeatureFlags featureFlags = new FeatureFlags();
        featureFlags.setBestPriceGuaranteed(hotelRateFlags.isBestPriceGuaranteed());
        featureFlags.setBnpl(hotelRateFlags.isBnplAvailable());
        featureFlags.setBnplBaseAmount(hotelDetails.getAdditionalDetails() != null ? hotelDetails.getAdditionalDetails().getBnplBaseAmount() : 0);
        featureFlags.setFirstTimeUser(hotelRateFlags.isFirstTimeUser());
        featureFlags.setFreeCancellation(hotelRateFlags.isFreeCancellationAvailable());
        featureFlags.setPahAvailable(hotelRateFlags.isPahAvailable());
        featureFlags.setPahTariffAvailable(hotelRateFlags.isPahAvailable());
        if(Constants.DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))){
            featureFlags.setPwaDetailSelectMerge(hotelRateFlags.isDetailSelectMerge());
        }
        featureFlags.setPahWalletApplicable(hotelRateFlags.isPahWalletApplicable());
        featureFlags.setRequestToBook(hotelRateFlags.isRequestToBook());
        featureFlags.setRtbPreApproved(hotelRateFlags.isRtbPreApproved());
        featureFlags.setRtbAutoCharge(hotelRateFlags.isRtbAutoCharge());
        featureFlags.setGroupBookingPrice(hotelRateFlags.isGroupBookingPrice());
        featureFlags.setMaskedPrice(hotelRateFlags.isMaskedPrice());
        featureFlags.setOptimisedSelection(MapUtils.isNotEmpty(expDataMap) && Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType()) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP)));
        /*
        //TODO Add for mypartner
        if(hotelDetails.getAdditionalDetails().getBnplBaseAmount()!=null) {
            featureFlags.setHotelFareHold(true);
        }*/
        return featureFlags;
    }

    /**
     * Builds day use persuasions from Orch data.
     */
    private List<DayUsePersuasion> buildDayUsePersuasion(List<com.gommt.hotels.orchestrator.detail.model.response.content.DayUsePersuasion> dayUsePersuasions) {
        List<DayUsePersuasion> dayUsePersuasionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayUsePersuasions)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.DayUsePersuasion dayUsePersuasion : dayUsePersuasions) {
                DayUsePersuasion dayUsePersuasionCG = new DayUsePersuasion();
                dayUsePersuasionCG.setId(dayUsePersuasion.getId());
                dayUsePersuasionCG.setText(dayUsePersuasion.getText());
                dayUsePersuasionCG.setIcon(dayUsePersuasion.getIcon());
                dayUsePersuasionCG.setIconType(dayUsePersuasion.getIconType());
                dayUsePersuasionList.add(dayUsePersuasionCG);
            }
        }
        return dayUsePersuasionList;
    }

    /**
     * Builds price detail for day use response.
     */
    private void buildHeaderPriceDetails(HotelDetails hotelDetails, DayUseRoomsResponse dayUseRoomsResponse) {
        List<RatePlan> ratePlans = hotelDetails.getRooms().stream()
                .filter(room -> "DAYUSE".equalsIgnoreCase(room.getType()))
                .findFirst()
                .orElse(new Rooms()).getRatePlans();
        if (CollectionUtils.isNotEmpty(ratePlans)) {
            PriceDetail priceDetail = ratePlans.get(0).getPrice();
            AvailDetails availDetail = ratePlans.get(0).getAvailDetail();
            DayUsePriceDetail dayUsePriceDetail = new DayUsePriceDetail();
            dayUsePriceDetail.setTotalPrice(priceDetail.getDisplayPrice());
            dayUsePriceDetail.setTotalTax(priceDetail.getTotalTax());
            dayUsePriceDetail.setPriceDisplayMsg(polyglotService.getTranslatedData(DAYUSE_PER_NIGHT));
            if (priceDetail.getTotalTax() > 0) {
                String translatedText = polyglotService.getTranslatedData(DAYUSE_PER_NIGHT_TAX);
                translatedText = MessageFormat.format(translatedText, priceDetail.getTotalTax());
                dayUsePriceDetail.setPriceTaxMsg(translatedText);
            }

            if (availDetail != null && availDetail.getOccupancyDetails() != null) {
                dayUseRoomsResponse.setOccupancyDetails(buildOccupencyDetails(availDetail.getOccupancyDetails()));
                dayUseRoomsResponse.setAvailCount(availDetail.getCount());
            }
            boolean shouldSetPriceDetails = dayUseUtil.shouldSetPriceDetailsForDayUseOnDetailPage(dayUseRoomsResponse.getSlotPlans(), priceDetail.getDisplayPrice());
            if (shouldSetPriceDetails) {
                dayUseRoomsResponse.setPriceDetail(dayUsePriceDetail);
            }
        }
    }

    /**
     * Builds occupancy details for room tariff.
     */
    private static RoomTariff buildOccupencyDetails(OccupancyDetails occupancyDetails) {
        RoomTariff roomTariff = null;
        if (occupancyDetails != null && occupancyDetails.getAdult() > 0) {
            roomTariff = new RoomTariff();
            roomTariff.setRoomCount(occupancyDetails.getNumberOfRooms());
            roomTariff.setNumberOfAdults(occupancyDetails.getAdult());
            roomTariff.setNumberOfChildren(occupancyDetails.getChild());
            if (CollectionUtils.isNotEmpty(occupancyDetails.getChildAges())) {
                roomTariff.setChildAges(occupancyDetails.getChildAges());
            }
        }
        return roomTariff;
    }

    /**
     * Builds day use slot plans from OrchV2 data.
     */
    private List<DayUseSlotPlan> buildDayUseSlotPlansFromOrchV2(HotelDetails hotelDetails, 
                                                                DayUseRoomsRequest dayUseRoomsRequest, 
                                                                SearchRoomsCriteria searchRoomsCriteria) {
        List<DayUseSlotPlan> dayUseSlotPlanList = new ArrayList<>();
        DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            getSlotRoomData(searchRoomsCriteria, hotelDetails.getRooms(), dayUseRoomsResponse, dayUseSlotPlanList);
        }
        if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
            for (RoomCombo roomCombo : hotelDetails.getRoomCombos()) {
                if (CollectionUtils.isNotEmpty(roomCombo.getRooms())) {
                    getSlotRoomData(searchRoomsCriteria, roomCombo.getRooms(), dayUseRoomsResponse, dayUseSlotPlanList);
                }
            }
        }

        return dayUseSlotPlanList;
    }

    /**
     * Processes slot room data and builds slot plans.
     */
    private void getSlotRoomData(SearchRoomsCriteria searchRoomsCriteria, 
                                List<Rooms> rooms, 
                                DayUseRoomsResponse dayUseRoomsResponse, 
                                List<DayUseSlotPlan> dayUseSlotPlanList) {
        for (Rooms room : rooms) {
            if (room.getSlot() != null && CollectionUtils.isNotEmpty(room.getRatePlans())) {
                // Create DayUseSlotPlan from OrchV2 room data
                DayUseSlotPlan slotPlan = new DayUseSlotPlan();

                //TODO Don't convert from orchV2 to HES-D object
                com.mmt.hotels.model.response.dayuse.Slot slot = new com.mmt.hotels.model.response.dayuse.Slot();
                slot.setDuration(room.getSlot().getDuration());
                slot.setTimeSlot(room.getSlot().getTimeSlot());

                // Set slot information from OrchV2 Slot
                com.mmt.hotels.clientgateway.response.dayuse.Slot slotCG = new com.mmt.hotels.clientgateway.response.dayuse.Slot();
                slotCG.setDuration(room.getSlot().getDuration());
                slotCG.setTimeSlot(utility.calculateTimeSlot_Meridiem(slot));
                slotPlan.setSlot(slotCG);

                // Build rate plan information
                RatePlan firstRatePlan = room.getRatePlans().get(0);
                PriceDetail priceDetail = room.getPrice();
                if (firstRatePlan != null) {
                    // Set price detail from rate plan

                    if (priceDetail == null || priceDetail.getDisplayPrice() <= 0.0) {
                        priceDetail = firstRatePlan.getPrice();
                    }

                    if (priceDetail != null) {
                        buildSlotPriceDetail(slotPlan, priceDetail, dayUseRoomsResponse);
                        slotPlan.setDefaultPriceKey(priceDetail.getCouponCode());
                        if (StringUtils.isNotBlank(priceDetail.getCouponCode())) {
                            slotPlan.setDefaultPriceKey(priceDetail.getCouponCode());
                        } else {
                            slotPlan.setDefaultPriceKey("DEFAULT");
                        }
                    }
                    //TODO fix this
                    slotPlan.setPayMode("PAS");

                    // Build room criteria for this slot
                    List<DayUseRoomCriteria> roomCriteriaList = buildRoomCriteriaFromOrchV2(room, firstRatePlan, searchRoomsCriteria);
                    slotPlan.setRoomCriteria(roomCriteriaList);
                }
                dayUseSlotPlanList.add(slotPlan);
            }
        }
    }

    private ResponseContextDetail getContextDetails(HotelDetails hotelDetails) {
        ResponseContextDetail responseContextDetail = new ResponseContextDetail();
        responseContextDetail.setCurrency(hotelDetails.getCurrencyCode());
        responseContextDetail.setMmtHotelCategory(hotelDetails.getHotelCategory());
        return responseContextDetail;
    }

    /**
     * Builds day use rooms from OrchV2 data.
     */
    private List<DayUseRoom> buildDayUseRooms(HotelDetails hotelDetails, DayUseRoomsRequest dayUseRoomsRequest) {
        List<DayUseRoom> dayUseRoomList = new ArrayList<>();
        Map<String, DayUseRoom> roomCodeAndRoomMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            buildDayUseRoom(hotelDetails.getRooms(), roomCodeAndRoomMap, hotelDetails);
        }
        if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
            for (RoomCombo roomCombo : hotelDetails.getRoomCombos()) {
                if (CollectionUtils.isNotEmpty(roomCombo.getRooms())) {
                    buildDayUseRoom(roomCombo.getRooms(), roomCodeAndRoomMap, hotelDetails);
                }
            }
        }

        if (!roomCodeAndRoomMap.isEmpty()) {
            dayUseRoomList = new ArrayList<>(roomCodeAndRoomMap.values());
        }

        return dayUseRoomList;
    }

    /**
     * Builds individual day use room from OrchV2 room data.
     */
    private void buildDayUseRoom(List<Rooms> rooms, Map<String, DayUseRoom> roomCodeAndRoomMap, HotelDetails hotelDetails) {
        for (Rooms room : rooms) {
            String roomCode = room.getCode();

            // Create DayUseRoom if not already created for this room code
            if (StringUtils.isNotEmpty(roomCode) && !roomCodeAndRoomMap.containsKey(roomCode)) {
                DayUseRoom dayUseRoom = new DayUseRoom();
                dayUseRoom.setRoomCode(roomCode);
                dayUseRoom.setRoomName(room.getName());

                List<RoomHighlight> roomHighlights = roomInfoHelper.transformRoomHighlights(room.getRoomInfo(), null,
                        hotelDetails.getHotelRateFlags().isAltAcco(), false);

                dayUseRoom.setRoomHighlights(roomHighlights);
                dayUseRoom.setAmenities(roomAmentiesHelper.buildAmenities(room.getRoomInfo()));
                // Set room images from OrchV2 Media (equivalent to legacy HotelImage logic)
                dayUseRoom.setImages(searchRoomsMediaHelper.extractRoomImagesFromMedia(hotelDetails.getMedia(), room.getCode()));
                dayUseRoom.setOccupancydetails(buildOccupencyDetails(room.getOccupancyDetails()));
                roomCodeAndRoomMap.put(roomCode, dayUseRoom);
            }
        }
    }

    /**
     * Builds room criteria from OrchV2 data.
     */
    private List<DayUseRoomCriteria> buildRoomCriteriaFromOrchV2(Rooms room, RatePlan ratePlan, SearchRoomsCriteria searchRoomsCriteria) {
        List<DayUseRoomCriteria> roomCriteriaList = new ArrayList<>();

        DayUseRoomCriteria roomCriteria = new DayUseRoomCriteria();

        // Set basic room and rate plan information
        roomCriteria.setRatePlanCode(ratePlan.getCode());
        roomCriteria.setRoomCode(room.getCode());

        OccupancyDetails occupancyDetail = ratePlan.getAvailDetail() != null ? ratePlan.getAvailDetail().getOccupancyDetails() : null;
        String mtKey = occupancyDetail != null && StringUtils.isNotEmpty(occupancyDetail.getRatePlanCode()) ? occupancyDetail.getRatePlanCode() : ratePlan.getCode();
        roomCriteria.setMtKey(mtKey);

        // Set supplierCode - extract from segmentId or use default supplier
        // In OrchV2, supplier information might be embedded in segmentId or price details
        String supplierCode = extractSupplierCodeFromOrchV2(ratePlan);
        if (StringUtils.isNotBlank(supplierCode)) {
            roomCriteria.setSupplierCode(supplierCode);
        } else {
            //TODO Fix this
            roomCriteria.setSupplierCode("INGO");
        }

        // Set pricingKey from rate plan price details
        if (ratePlan.getPrice() != null && StringUtils.isNotBlank(ratePlan.getPrice().getPricingKey())) {
            roomCriteria.setPricingKey(ratePlan.getPrice().getPricingKey());
        }

        // Build room stay candidates directly from OrchV2 ratePlan occupancy details (avoiding 2-level transformation)
        // This follows the same pattern as OrchSearchRoomsResponseTransformer.getTariff()
        List<DayUseRoomStayCandidate> dayUseRoomStayCandidateList = new ArrayList<>();
        
        // Check if we have multiple occupancy details in pricePerOccupancy (for occupancy rooms)
        if (ratePlan.getPrice() != null && CollectionUtils.isNotEmpty(ratePlan.getPrice().getPricePerOccupancy())) {
            // Use pricePerOccupancy details for occupancy-based rooms
            for (OccupancyDetails orchOccupancyDetails : ratePlan.getPrice().getPricePerOccupancy()) {
                DayUseRoomStayCandidate dayUseCandidate = buildDayUseRoomStayCandidate(orchOccupancyDetails);
                dayUseRoomStayCandidateList.add(dayUseCandidate);
            }
        } else if (ratePlan.getAvailDetail() != null && ratePlan.getAvailDetail().getOccupancyDetails() != null) {
            // Fallback to availDetail occupancy for non-occupancy rooms
            OccupancyDetails orchOccupancyDetails = ratePlan.getAvailDetail().getOccupancyDetails();
            DayUseRoomStayCandidate dayUseCandidate = buildDayUseRoomStayCandidate(orchOccupancyDetails);
            dayUseRoomStayCandidateList.add(dayUseCandidate);
        }
        
        if (!dayUseRoomStayCandidateList.isEmpty()) {
            roomCriteria.setRoomStayCandidates(dayUseRoomStayCandidateList);
        }

        roomCriteriaList.add(roomCriteria);
        return roomCriteriaList;
    }

    /**
     * Builds a DayUseRoomStayCandidate from OrchV2 OccupancyDetails.
     * This method follows the same pattern as OrchSearchRoomsResponseTransformer.getTariff()
     */
    private DayUseRoomStayCandidate buildDayUseRoomStayCandidate(OccupancyDetails orchOccupancyDetails) {
        DayUseRoomStayCandidate dayUseCandidate = new DayUseRoomStayCandidate();
        
        // Set occupancy details directly from OrchV2 ratePlan
        dayUseCandidate.setAdultCount(orchOccupancyDetails.getAdult());
        
        // Set child ages if children are present (following OrchSearchRoomsResponseTransformer pattern)
        if (orchOccupancyDetails.getChild() > 0 && CollectionUtils.isNotEmpty(orchOccupancyDetails.getChildAges())) {
            dayUseCandidate.setChildAges(orchOccupancyDetails.getChildAges());
        } else {
            // Ensure childAges is set to null if no children to match legacy behavior
            dayUseCandidate.setChildAges(null);
        }
        
        return dayUseCandidate;
    }

    /**
     * Extracts supplier code from OrchV2 rate plan data.
     * In OrchV2, supplier information might be available in segmentId or other fields.
     * This method attempts to extract it based on known patterns from legacy code.
     */
    private String extractSupplierCodeFromOrchV2(RatePlan ratePlan) {
        // Check if segmentId contains supplier information (common pattern in legacy)
        if (StringUtils.isNotBlank(ratePlan.getSegmentId())) {
            String segmentId = ratePlan.getSegmentId();

            // Legacy pattern: segmentId might contain supplier codes like "INGO", "GTA", etc.
            if (segmentId.contains("INGO")) {
                return "INGO";
            } else if (segmentId.contains("GTA")) {
                return "GTA";
            } else if (segmentId.contains("EXPEDIA")) {
                return "EXPEDIA";
            } else if (segmentId.contains("HB")) {
                return "HB";
            }
            // Add more supplier patterns as needed based on legacy data
        }

        // TODO: If supplier information is not available in OrchV2 response,
        // we might need to derive it from other fields or use a default value
        // For now, return null and let the downstream handle missing supplier code
        return null;
    }

    /**
     * Builds slot price detail for day use from OrchV2 PriceDetail.
     * This method replicates the legacy logic from SearchRoomsResponseTransformer.buildSlotDetailList()
     * and CommonResponseTransformer.getPriceDetail() to ensure compatibility.
     *
     * @param slotPlan The slot plan to set price details on
     * @param priceDetail The OrchV2 price detail containing pricing information
     * @param dayUseRoomsResponse The day use response for context (if needed)
     */
    private void buildSlotPriceDetail(DayUseSlotPlan slotPlan, PriceDetail priceDetail, DayUseRoomsResponse dayUseRoomsResponse) {
        if (priceDetail == null) {
            return;
        }

        // Create a new DayUsePriceDetail for the slot plan
        DayUsePriceDetail dayUsePriceDetail = new DayUsePriceDetail();
        // Set basic price information from OrchV2 PriceDetail
        // Total price includes base price + taxes (similar to legacy logic - ideally this should be coming from PDO as TPT)
        double totalPrice = priceDetail.getDisplayPrice() + priceDetail.getTotalTax();
        dayUsePriceDetail.setTotalPrice(totalPrice);
        dayUsePriceDetail.setTotalTax(priceDetail.getTotalTax());
        dayUsePriceDetail.setCouponAmount(priceDetail.getDiscount() != null ? priceDetail.getDiscount().getCoupon() : 0);

        // Build price display message - use polyglot service for translation
        // Default to per hour pricing for day use slots (similar to legacy constants)
        String priceDisplayMessage = polyglotService.getTranslatedData("DAYUSE_PER_HOUR");
        if (StringUtils.isBlank(priceDisplayMessage)) {
            // Fallback to legacy constant for day use per hour
            priceDisplayMessage = "per hour";
        }
        dayUsePriceDetail.setPriceDisplayMsg(priceDisplayMessage);
        dayUsePriceDetail.setPriceDisplayMsg(polyglotService.getTranslatedData(TOTAL_AMOUNT_LABEL));
        dayUsePriceDetail.setPriceTaxMsg(polyglotService.getTranslatedData(TAXES_LABEL));
        dayUsePriceDetail.setCouponAmount(priceDetail.getDiscount() != null ? priceDetail.getDiscount().getCoupon() : 0);
        // Set the price detail on the slot plan
        slotPlan.setPriceDetail(dayUsePriceDetail);
    }

    /**
     * Populates missing slots in the day use response if there are fewer than 3 slots available.
     * This method replicates the legacy logic from SearchRoomsResponseTransformer.populateMissingSlots()
     * 
     * @param dayUseRoomsResponse The day use response to populate missing slots for
     * @param slotTime The time slot from the search criteria
     */
    private void populateMissingSlots(DayUseRoomsResponse dayUseRoomsResponse, Integer slotTime) {
        if (dayUseRoomsResponse == null || CollectionUtils.isEmpty(dayUseRoomsResponse.getSlotPlans())) {
            return;
        }

        List<DayUseSlotPlan> dayUseSlotPlanList = dayUseRoomsResponse.getSlotPlans();
        Set<Integer> slotDetailCount = new HashSet<>();
        
        // Collect existing slot durations
        for (DayUseSlotPlan dayUseSlotPlan : dayUseSlotPlanList) {
            if (dayUseSlotPlan.getSlot() != null) {
                slotDetailCount.add(dayUseSlotPlan.getSlot().getDuration());
            }
        }

        // If we have fewer than 3 slots, add missing ones from configuration
        if (!slotDetailCount.isEmpty() && slotDetailCount.size() < 3) {
            if (missingSlotDetails != null && CollectionUtils.isNotEmpty(missingSlotDetails.getDuration())) {
                for (Integer value : missingSlotDetails.getDuration()) {
                    if (!slotDetailCount.contains(value)) {
                        // Create new slot plan for missing duration
                        DayUseSlotPlan dayUseSlotPlan = new DayUseSlotPlan();
                        com.mmt.hotels.clientgateway.response.dayuse.Slot slot = new com.mmt.hotels.clientgateway.response.dayuse.Slot();
                        slot.setDuration(value);

                        // Create temporary slot for time calculation (replicating legacy logic)
                        com.mmt.hotels.model.response.dayuse.Slot tempSlot = new com.mmt.hotels.model.response.dayuse.Slot();
                        tempSlot.setDuration(slot.getDuration());
                        tempSlot.setTimeSlot(String.valueOf(slotTime));

                        // Calculate and set the formatted time slot
                        slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(tempSlot));
                        dayUseSlotPlan.setSlot(slot);

                        // Add the missing slot to the list
                        dayUseSlotPlanList.add(dayUseSlotPlan);
                    }
                }
                // Update the response with the populated slot plans
                dayUseRoomsResponse.setSlotPlans(dayUseSlotPlanList);
            }
        }
    }
} 