package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.transformer.request.InitiateApprovalRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.UpdatePolicyRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.InitiateApprovalRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.android.UpdatePolicyRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.InitiateApprovalRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.desktop.UpdatePolicyRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.InitiateApprovalRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.ios.UpdatePolicyRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.InitiateApprovalRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.InitiateApprovalResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatePolicyResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.InitiateApprovalResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.android.UpdatePolicyResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.InitiateApprovalResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.desktop.UpdatePolicyResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.InitiateApprovalResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.ios.UpdatePolicyResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.InitiateApprovalResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InitiateApprovalFactory {

    @Autowired
    private InitiateApprovalRequestTransformerPWA initiateApprovalRequestTransformerPWA;

    @Autowired
    private InitiateApprovalRequestTransformerAndroid initiateApprovalRequestTransformerAndroid;

    @Autowired
    private InitiateApprovalRequestTransformerDesktop initiateApprovalRequestTransformerDesktop;

    @Autowired
    private InitiateApprovalRequestTransformerIOS initiateApprovalRequestTransformerIOS;

    @Autowired
    private InitiateApprovalResponseTransformerPWA initiateApprovalResponseTransformerPWA;

    @Autowired
    private InitiateApprovalResponseTransformerAndroid initiateApprovalResponseTransformerAndroid;

    @Autowired
    private InitiateApprovalResponseTransformerDesktop initiateApprovalResponseTransformerDesktop;

    @Autowired
    private InitiateApprovalResponseTransformerIOS initiateApprovalResponseTransformerIOS;


    public InitiateApprovalRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return initiateApprovalRequestTransformerDesktop;
        switch (client) {
            case "PWA":
            case "MSITE":
                return initiateApprovalRequestTransformerPWA;
            case "DESKTOP":
                return initiateApprovalRequestTransformerDesktop;
            case "ANDROID":
                return initiateApprovalRequestTransformerAndroid;
            case "IOS":
                return initiateApprovalRequestTransformerIOS;
        }
        return initiateApprovalRequestTransformerDesktop;
    }

    public InitiateApprovalResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return initiateApprovalResponseTransformerDesktop;
        switch (client) {
            case "PWA":
            case "MSITE":
                return initiateApprovalResponseTransformerPWA;
            case "DESKTOP":
                return initiateApprovalResponseTransformerDesktop;
            case "ANDROID":
                return initiateApprovalResponseTransformerAndroid;
            case "IOS":
                return initiateApprovalResponseTransformerIOS;
        }
        return initiateApprovalResponseTransformerDesktop;
    }
}
