package com.mmt.hotels.clientgateway.transformer.response.orchestrator;


import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.CancellationPolicy;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PriceDetail;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.DAYUSE_LOCAL_ID;

@Component
public class OrchSearchHotelsResponseTransformerPWA extends OrchSearchHotelsResponseTransformer {

    @Autowired
    PersuasionUtil persuasionUtil;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformerPWA.class);

    @Override
    protected BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse) {
        return null;
    }

    @Override
    public void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity) {
        hotel.setLovedByIndians(persuasionUtil.checkIfIndianessPersuasionExists(hotel.getHotelPersuasions()));
    }

    @Override
    public void addSeoTextPersuasion(Hotel hotel, HotelDetails hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName) {

    }

    @Override
    public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocationDetails locusData, boolean hCardV2) {
        if(CollectionUtils.isNotEmpty(locationPersuasion)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject locPers = new PersuasionObject();
            locPers.setData(new ArrayList<>());
            locPers.setTemplate(IMAGE_TEXT_H);
            locPers.setPlaceholder("SINGLE");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setHasAction(false);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");

            locPers.getData().add(locPersuasionData);
            if(locationPersuasion.size() == 1 ) {
                locPersuasionData.setText(locationPersuasion.get(0));
            }else if(StringUtils.isEmpty(locationPersuasion.get(0))){
                locPersuasionData.setText(locationPersuasion.get(1));
            }
            else if (locationPersuasion.size() >= 2){
                locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
                //For Secondary Location Persuasion, if it is present, add it in the Location Persuasion
                if (locationPersuasion.size() > 2)
                    locPersuasionData.setText(locPersuasionData.getText() + " | " + locationPersuasion.get(2));
            }
            //if funnelSource is DAYUSE then we need to set iconType in locPersuasionData
            if (searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                locPersuasionData.setIcontype(Constants.LOCATION_PERSUASION_ICON_TYPE_DAYUSE);
            }
            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP,locPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }

        }

        if(CollectionUtils.isNotEmpty(facilities) && enableAmenities) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId("AMENITIES");
            amenPersuasionData.setPersuasionType("AMENITIES");
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            StringBuilder text = new StringBuilder();
            int index = 1;
            int facilitiesSize = facilities.size();
            Iterator<String> iter = facilities.iterator();
            while(iter.hasNext() && index <=3){

                text.append( iter.next());
                if(index < 3 && facilitiesSize > 1)
                    text.append(" | ");
                index++;
                --facilitiesSize;
            }
            amenPersuasionData.setText(text.toString());
            amenityPers.getData().add(amenPersuasionData);


            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }
        }

        if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && org.apache.commons.lang3.StringUtils.isNotEmpty(dayUsePersuasionsText)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId(DAYUSE_LOCAL_ID);
            amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            amenPersuasionData.setText(dayUsePersuasionsText);
            amenPersuasionData.setIcontype("b_dot");
            amenityPers.getData().add(amenPersuasionData);
            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }
        }

    }

    @Override
    protected String buildBGColor(String section, String orientation, String cardType) {
        return "";
    }

    @Override
    protected void addBookingConfirmationPersuasion(HotelDetails hotelEntity) {

    }

    @Override
    public MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels) {
        return null;
    }
}
