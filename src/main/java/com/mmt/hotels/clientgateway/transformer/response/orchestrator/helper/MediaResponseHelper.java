package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.TreelsMediaEntity;
import com.mmt.hotels.clientgateway.response.staticdetail.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.staticdata.Tag;
import com.mmt.hotels.model.response.staticdata.TreelGalleryData;
import com.mmt.hotels.model.response.staticdata.TreelMedia;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STATIC_ROOM_TAG;
import static java.lang.Math.min;

@Component
public class MediaResponseHelper {

    @Autowired
    private Utility utility;

    @Autowired
    private PolyglotService polyglotService;

    @Value("${detail.grid.image.limit}")
    private int detailGridImageLimit;

    @Value("${listing.media.limit.exp}")
    private int listingMediaLimitExp;

    //TODO:: Ask lepsy
    @Value("#{'${traveller.image_order}'.split(',')}")
    private List<String> travellerImageOrder;

    public MediaV2 buildMedia(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isChatBotEnable, boolean isImageExpEnable, boolean isLuxe, String client, String listingType) {
        if (src == null) return null;
        MediaV2 mediaV2 = new MediaV2();
        // Traveller images (group by imgTag)
        mediaV2.setTraveller(setTravellerImages(src, isChatBotEnable));
        mediaV2.setHotel(setHotelImages(src, isChatBotEnable));
        //mediaV2.setView360(set360Images(src, isLuxe, client));
        mediaV2.setGrid(setGridImages(src, isImageExpEnable, listingType));

        // Grid images (professionalMediaEntities)
        return mediaV2;
    }

    private ProfessionalImages setGridImages(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isImageExpEnable, String listingType) {
        ProfessionalImages grid = new ProfessionalImages();
        if (src.getProfessionalMediaEntities() != null && src.getProfessionalMediaEntities().containsKey("H")) {
            List< com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity> professionalList = src.getProfessionalMediaEntities().get("H");
            if (professionalList != null && !professionalList.isEmpty()) {
                List<MediaInfo> mediaInfos = new ArrayList<>();
                int mediaLimit = isImageExpEnable && Utility.isAppRequest() ? listingMediaLimitExp : detailGridImageLimit;
                if(CollectionUtils.isNotEmpty(professionalList)) {
                    List<MediaInfo> professionalMediaInfos = buildProfessionalImagesFromContentResponse(src.getProfessionalMediaEntities(), listingType);
                    if (CollectionUtils.isNotEmpty(professionalMediaInfos)) {
                        int numOfImages = min(mediaLimit, professionalMediaInfos.size());
                        grid.setImages(professionalMediaInfos.subList(0, numOfImages));
                    }
                }
            }
        }
        return grid;

    }

    private HotelImages setHotelImages(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isChatBotEnable) {
        // ProfessionalV2 images (tagInfoList)
        HotelImages hotelImages = new HotelImages();
        if (src.getTagInfoList() != null && src.getTagInfoList().containsKey("H")) {
            List< com.gommt.hotels.orchestrator.detail.model.response.content.media.TagInfo> tagInfos = src.getTagInfoList().get("H");
            List<com.mmt.hotels.model.response.staticdata.Tag> hotelTags = new ArrayList<>();
            for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.TagInfo tagInfo : tagInfos) {
                com.mmt.hotels.model.response.staticdata.Tag tag = new com.mmt.hotels.model.response.staticdata.Tag();
                tag.setName(tagInfo.getName());
                List<com.mmt.hotels.model.response.staticdata.Subtag> subtagList = new ArrayList<>();
                if (tagInfo.getSubTagInfoList() != null) {
                    for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.SubTagInfo subTagInfo : tagInfo.getSubTagInfoList()) {
                        com.mmt.hotels.model.response.staticdata.Subtag subtag = new com.mmt.hotels.model.response.staticdata.Subtag();
                        subtag.setName(subTagInfo.getName());
                        List<com.mmt.hotels.model.response.staticdata.ImageData> data = new ArrayList<>();
                        if (subTagInfo.getData() != null) {
                            for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity img : subTagInfo.getData()) {
                                com.mmt.hotels.model.response.staticdata.ImageData d = new com.mmt.hotels.model.response.staticdata.ImageData();

                                // Basic fields from MediaEntity
                                d.setUrl(img.getUrl());
                                d.setTitle(img.getTitle());
                                d.setThumbnailURL(img.getThumbnailUrl());

                                // Set mediaType - either from source or default to IMAGE
                                d.setMediaType(img.getMediaType() != null ? img.getMediaType() : "IMAGE");

                                // Additional fields from MediaEntity
                                if (img.getDescription() != null) {
                                    d.setDescription(img.getDescription());
                                }
                                if (img.getPreviewUrl() != null) {
                                    d.setPreviewUrl(img.getPreviewUrl());
                                }

                                // Room-specific fields from RoomEntity
                                if (img.getRoomCode() != null) {
                                    d.setRoomCode(img.getRoomCode());
                                }
                                if (img.getRoomName() != null) {
                                    d.setRoomName(img.getRoomName());
                                }

                                // Note: Traveler-specific fields (travelerName, travellerImage, userReview,
                                // travelerRating, reviewCount, date) are not available in RoomEntity
                                // They are only available in TravellerMediaEntity

                                data.add(d);
                            }
                        }
                        subtag.setData(data);
                        subtagList.add(subtag);
                    }
                }
                tag.setSubtags(subtagList);
                hotelTags.add(tag);
            }
            hotelImages.setTags(hotelTags);
        }

        if(hotelImages != null && CollectionUtils.isNotEmpty(hotelImages.getTags())){
            List<Tag> sortedTags = hotelImages.getTags().stream().sorted(Comparator.comparing(tag -> {
                if (tag.getName().equalsIgnoreCase(Street_View)) {
                    return -1;
                }
                return 1;
            })).collect(Collectors.toList());
            hotelImages.setTags(sortedTags);
        }
        return hotelImages;

    }

    private TravellerImages setTravellerImages(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isChatBotEnable) {
        List<com.mmt.hotels.model.response.staticdata.Tag> tags = null;
        TravellerImages travellerImages = new TravellerImages();
        if (src.getTraveller() != null && src.getTraveller().containsKey("H")) {
            List< com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity> travellerList = src.getTraveller().get("H");
            Map<String, List< TravellerMediaEntity>> spaceNameToImageEntityMap = new java.util.HashMap<>();
            for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity entity : travellerList) {
                String key = entity.getImgTag();
                if (!spaceNameToImageEntityMap.containsKey(key)) {
                    spaceNameToImageEntityMap.put(key, new ArrayList<>());
                }
                spaceNameToImageEntityMap.get(key).add(entity);
            }
            // Convert to tags
            tags = new ArrayList<>();
            for (Map.Entry<String, List< com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity>> entry : spaceNameToImageEntityMap.entrySet()) {
                com.mmt.hotels.model.response.staticdata.Tag tag = new com.mmt.hotels.model.response.staticdata.Tag();
                tag.setName(entry.getKey());
                com.mmt.hotels.model.response.staticdata.Subtag subtag = new com.mmt.hotels.model.response.staticdata.Subtag();
                subtag.setName(entry.getKey());
                List<com.mmt.hotels.model.response.staticdata.ImageData> data = new ArrayList<>();
                for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity travellerImageEntity : entry.getValue()) {
                    com.mmt.hotels.model.response.staticdata.ImageData imageData = new com.mmt.hotels.model.response.staticdata.ImageData();
                    imageData.setDate(travellerImageEntity.getDate());
                    imageData.setTravelerName(travellerImageEntity.getTravellerName());
                    imageData.setMediaType(travellerImageEntity.getMediaType());
                    imageData.setUrl(travellerImageEntity.getUrl());
                    imageData.setTitle(travellerImageEntity.getTitle());
                    imageData.setThumbnailURL(travellerImageEntity.getThumbnailUrl());
                    imageData.setDescription(travellerImageEntity.getDescription());
                    imageData.setTravelerRating(travellerImageEntity.getTravellerRating());
                    imageData.setReviewCount(travellerImageEntity.getReviewCount());
                    imageData.setDate(travellerImageEntity.getDate());
                    imageData.setPreviewUrl(travellerImageEntity.getPreviewUrl());
                    imageData.setRoomCode(travellerImageEntity.getRoomCode());
                    data.add(imageData);
                }
                subtag.setData(data);
                List<com.mmt.hotels.model.response.staticdata.Subtag> subtagList = new ArrayList<>();
                subtagList.add(subtag);
                tag.setSubtags(subtagList);
                tags.add(tag);
            }
            // Sort tags if imageTagOrder is present
            //LinkedHashSet<String> imageTagOrder = src.getProfessionalImageTagOrder();
            //TODO :: Ask Lepsy
            LinkedHashSet<String> imageTagOrder = new LinkedHashSet<>(travellerImageOrder);
            if (imageTagOrder != null && !imageTagOrder.isEmpty() && tags != null && !tags.isEmpty()) {
                sortTravellerTagsBasedImageTagsOrder(tags, new ArrayList<>(imageTagOrder));
            }
            travellerImages.setTags(tags);
        }
        return travellerImages;

    }

    // Add this method to support tag sorting in mediaV2 mapping
    private static void sortTravellerTagsBasedImageTagsOrder(List<com.mmt.hotels.model.response.staticdata.Tag> tags, List<String> imageTagsOrderList) {
        if (imageTagsOrderList == null || imageTagsOrderList.isEmpty() || tags == null || tags.isEmpty()) {
            return;
        }
        tags.sort(new java.util.Comparator<com.mmt.hotels.model.response.staticdata.Tag>() {
            @Override
            public int compare(com.mmt.hotels.model.response.staticdata.Tag tag1, com.mmt.hotels.model.response.staticdata.Tag tag2) {
                // Keep the "Others" tag at the last
                if ("Others".equalsIgnoreCase(tag1.getName())) {
                    return 1;
                } else if ("Others".equalsIgnoreCase(tag2.getName())) {
                    return -1;
                }
                int index1 = imageTagsOrderList.indexOf(tag1.getName());
                int index2 = imageTagsOrderList.indexOf(tag2.getName());
                if (index2 == -1)
                    return -1;
                else if (index1 == -1)
                    return 1;
                else
                    return Integer.compare(index1, index2);
            }
        });
    }

    private List<MediaInfo> buildProfessionalImagesFromContentResponse(Map<String, List<ProfessionalMediaEntity>> hotelImage, String listingType){

        List<MediaInfo> professionalMediaInfos = new ArrayList<>();

        if (MapUtils.isNotEmpty(hotelImage)) {
            List<ProfessionalMediaEntity> professionalList = hotelImage.get("H");
            if (CollectionUtils.isNotEmpty(professionalList)) {
                for (ProfessionalMediaEntity professionalImageEntity : professionalList) {
                    MediaInfo mediaInfo = new MediaInfo();
                    mediaInfo.setFilterInfo(professionalImageEntity.getFilterInfo());
                    mediaInfo.setMediaType("IMAGE");
                    mediaInfo.setTags(professionalImageEntity.getSeekTags());
                    mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailUrl());
                    mediaInfo.setTitle(professionalImageEntity.getTitle());
                    mediaInfo.setUrl(professionalImageEntity.getUrl());
                    professionalMediaInfos.add(mediaInfo);
                }
            }
            professionalList = hotelImage.get("R");
            //HTL-41751 Not Merging RoomImages in GalleryImages when listingType is Entire Property
            //as same set of images were coming twice, once in gallery images and second time in room images.
            if (CollectionUtils.isNotEmpty(professionalList) && !LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)) {
                for (ProfessionalMediaEntity professionalImageEntity : professionalList) {
                    MediaInfo mediaInfo = new MediaInfo();
                    mediaInfo.setMediaType("IMAGE");
                    mediaInfo.setTags(professionalImageEntity.getSeekTags());
                    String roomTag = polyglotService.getTranslatedData(STATIC_ROOM_TAG);
                    if (mediaInfo.getTags() != null) {
                        mediaInfo.getTags().add(roomTag);
                    } else {
                        List<String> tags = new ArrayList<String>();
                        tags.add(roomTag);
                        mediaInfo.setTags(tags);
                    }
                    mediaInfo.setFilterInfo(roomTag);
                    mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailUrl());
                    mediaInfo.setTitle(professionalImageEntity.getTitle());
                    mediaInfo.setUrl(professionalImageEntity.getUrl());
                    mediaInfo.setRoomCode(professionalImageEntity.getRoomCode());
                    professionalMediaInfos.add(mediaInfo);
                }
            }
        }

        return professionalMediaInfos;
    }

    public TreelGalleryData mapTreelsImagesToTreelGalleryData(List<TreelsMediaEntity> treelsImages) {
        if (CollectionUtils.isEmpty(treelsImages)) {
            return null;
        }

        TreelGalleryData treelGalleryData = new TreelGalleryData();
        List<TreelMedia> treelMediaList = new ArrayList<>();

        for (TreelsMediaEntity treelsEntity : treelsImages) {
            if (treelsEntity != null) {
                TreelMedia treelMedia = TreelMedia.builder()
                        .url(treelsEntity.getUrl())
                        .thumbnailUrl(treelsEntity.getThumbnailUrl())
                        .mediaType(treelsEntity.getMediaType())
                        .title(treelsEntity.getTitle())
                        .subtitle(treelsEntity.getSubtitle())
                        .description(treelsEntity.getDescription())
                        .shareCount(treelsEntity.getShareCount())
                        .shareUrl(treelsEntity.getShareUrl())
                        .brandIcon(treelsEntity.getBrandIcon())
                        .mediaBrand(treelsEntity.getMediaBrand())
                        .build();
                treelMediaList.add(treelMedia);
            }
        }

        treelGalleryData.setTreelMedia(treelMediaList);
        // Set iconUrl if available from the first media entity
        if (!treelMediaList.isEmpty() && treelsImages.get(0) != null) {
            treelGalleryData.setIconUrl(treelsImages.get(0).getThumbnailUrl());
        }

        return treelGalleryData;
    }
}
