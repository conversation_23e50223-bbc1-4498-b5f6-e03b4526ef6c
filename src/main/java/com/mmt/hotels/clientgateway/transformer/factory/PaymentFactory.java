package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.PaymentRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.PaymentResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.desktop.PaymentResponseTransformerDesktop;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PaymentFactory {

    @Autowired
    PaymentRequestTransformer paymentRequestTransformer;

    @Autowired
    PaymentResponseTransformer paymentResponseTransformer;

    @Autowired
    PaymentResponseTransformerDesktop paymentResponseTransformerDesktop;

    public PaymentRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return paymentRequestTransformer;
        switch(client.toUpperCase()) {
            case "PWA": return paymentRequestTransformer;
            case "DESKTOP": return  paymentRequestTransformer;
            case "ANDROID": return  paymentRequestTransformer;
            case "IOS": return  paymentRequestTransformer;
        }
        return paymentRequestTransformer;
    }

    public PaymentResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return paymentResponseTransformer;
        switch(client.toUpperCase()){
            case "PWA": return paymentResponseTransformerDesktop;
            case "DESKTOP": return  paymentResponseTransformerDesktop;
            case "ANDROID": return  paymentResponseTransformer;
            case "IOS": return  paymentResponseTransformer;
        }
        return paymentResponseTransformer;
    }
}
