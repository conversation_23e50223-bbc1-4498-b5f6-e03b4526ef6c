package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.Hover;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.listpersonalization.*;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.hotels.pojo.response.IndianessPersuasion;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

@Component
public class SearchHotelsResponseTransformerDesktop extends SearchHotelsResponseTransformer {

	private static final Logger LOGGER = LoggerFactory.getLogger(SearchHotelsResponseTransformerDesktop.class);

	private MySafetyTooltip mySafetyDataTooltips;

	List<String> amenetiesWithUrl;

	private Map<String, Map<String, String>> amenitiesIconUrls;

	@Autowired
	PropertyManager propManager;
	@Autowired
	private PolyglotHelper polyglotHelper;
	private ValueStaysTooltip valueStaysTooltipDom;
	private ValueStaysTooltip valueStaysTooltipIntl;
	private Map<Integer, Set<String>> budgetHotelCityConfig;
	private LuxeToolTip luxeToolTipConfig;
	private MySafetyTooltip mysafetytooltip;
	private MyBizAssuredToolTip myBizAssuredTooltipDom;

	@Autowired
	PolyglotService polyglotService;

	@Autowired
	DateUtil dateUtil;

	@Value("${desktop.tool.tip.persuasions}")
	private String toolTipPersuasions;

	@Value("${mybiz.assured.url}")
	private String myBizAssuredUrl;

	@Value("${high.rated.url}")
	private String highRatedUrl;

	@Value("${gst.invoice.url}")
	private String gstInvoiceUrl;

	@Value("${bpg.url}")
	private String bpgUrl;

	@Value("${active.languages}")
	private String activeLanguages;

	private Map<String, Object> desktopToolTipPersuasionsMap;

	private Map<String, MySafetyTooltip> mySafetyToolTipTranslated;

	private MyBizStaticCard myBizStaticCard;

	@Autowired
	PersuasionUtil persuasionUtil;

	private static final String DEVICE_TYPE = "Desktop";

	@PostConstruct
	public void init() {
		PropertyTextConfig propertyTextConfig = propManager.getProperty("propertyTextConfig", PropertyTextConfig.class);
		mySafetyDataTooltips = propertyTextConfig.mySafetyTooltipKeys();
		propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> {
			mySafetyDataTooltips = propertyTextConfig.mySafetyTooltipKeys();
		});
		mySafetyToolTipTranslated = createMySafetyTooltipTranslated();
		amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl();

		/*Config to get Icons For Amenities*/
		amenitiesIconUrls = propertyTextConfig.amenitiesIconUrls();

		propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> {
			amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl();

		});
		CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
		valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom();
		valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl();
		luxeToolTipConfig = commonConfig.luxeToolTip();
		myBizAssuredTooltipDom = commonConfig.myBizAssuredTooltipDom();
		myBizStaticCard = commonConfig.myBizStaticCard();
		missingSlotDetails = commonConfig.missingSlotDetails();
		thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
		commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
		commonConfig.addPropertyChangeListener("myBizStaticCard", event -> myBizStaticCard = commonConfig.myBizStaticCard());
		commonConfig.addPropertyChangeListener("mmtValueStaysTooltipDom", evt -> valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom());
		commonConfig.addPropertyChangeListener("mmtValueStaysTooltipIntl", evt -> valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl());
		commonConfig.addPropertyChangeListener("luxeToolTip", evt -> luxeToolTipConfig = commonConfig.luxeToolTip());
		commonConfig.addPropertyChangeListener("myBizAssuredTooltipDom", evt -> myBizAssuredTooltipDom = commonConfig.myBizAssuredTooltipDom());
		LOGGER.warn("missingSlotDetails : {}", missingSlotDetails);


		try {
			desktopToolTipPersuasionsMap = objectMapperUtil.getObjectFromJsonWithType(toolTipPersuasions,
					new TypeReference<Map<String, Object>>() {
					},
					DependencyLayer.CLIENTGATEWAY);
		} catch (JsonParseException e) {
			e.printStackTrace();
			LOGGER.error("error in creating desktopToolTipPersuasionsMap from string {} ", toolTipPersuasions);
		}
	}


	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Override
	public void populateClientSpecificParameters() {
	}

	@Override
	public void addSeoTextPersuasion(Hotel hotel, SearchWrapperHotelEntity hotelEntity, boolean oddHotel, ListingSearchRequest searchHotelsRequest, String sectionName) {
		if (Utility.isSeoPersuasionAllowed(searchHotelsRequest, sectionName)) return;

		if (hotel.getHotelPersuasions() == null) {
			hotel.setHotelPersuasions(new HashMap<String, Object>());
		}
		PersuasionObject seoPersuasion = new PersuasionObject();
		seoPersuasion.setData(new ArrayList<>());
		seoPersuasion.setPlaceholder(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID);
		seoPersuasion.setPlaceholder("SINGLE");
		seoPersuasion.setTemplate("TEXT_OVERFLOW");

		PersuasionData seoPersuasionData = new PersuasionData();
		seoPersuasionData.setHasAction(true);
		seoPersuasionData.setHtml(true);
		seoPersuasionData.setStyle(new PersuasionStyle());
		List<String> styleClasses = new ArrayList<>();
		styleClasses.add("pc__seoText");
		styleClasses.add("pc__textOverflow");
		seoPersuasionData.getStyle().setStyleClasses(styleClasses);
		seoPersuasionData.setPersuasionType(Constants.PERSUASION_TYPE_SEO);
		seoPersuasionData.setText(getSeoPersuasionText(oddHotel, true, hotelEntity));
		seoPersuasion.getData().add(seoPersuasionData);
		seoPersuasionData.setActionType("SEO_TEXT_OVERFLOW");
		((Map<String, Object>) hotel.getHotelPersuasions())
				.put(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID, seoPersuasion);
	}

	@Override
	public HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo) {
		HotelCard hotelBottomCard = new HotelCard();
		hotelBottomCard.setHeading(StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE), "{TITLE}", quickBookInfo.getTitleWithPrice()));
		hotelBottomCard.setSubHeading(StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE), "{SUBTITLE}", quickBookInfo.getRoomPersuasion()));
		hotelBottomCard.setRoomSubHeading(StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE), "{SUBTITLE}", quickBookInfo.getRoomPersuasionWithSize()));
		hotelBottomCard.setShowCard(quickBookInfo.isShowQuickBookCard());
		hotelBottomCard.setCta(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA));
		BGLinearGradient bgLinearGradient = new BGLinearGradient();
		bgLinearGradient.setDirection("91.03deg");
		bgLinearGradient.setStart("#2D6F95 1.46%");
		bgLinearGradient.setEnd("#192B43 99.9%");
		hotelBottomCard.setBgLinearGradient(bgLinearGradient);
		DesktopStylingClassesObj classesObj = new DesktopStylingClassesObj();
		classesObj.setOuterClass("myBizQuickBook");
		classesObj.setCtaClass1("quick__book-btn");
		classesObj.setCtaClass2("double--arw");
		hotelBottomCard.setDesktopStylingClassesObj(classesObj);
		return hotelBottomCard;
	}

	@Override
	public String getMyBizDirectHotelDistanceText(String distanceText) {
		return StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_DESKTOP_DISTANCE_TEXT), "{DISTANCE_TEXT}", distanceText);
	}

	@Override
	public void addPersuasionHoverData(Hotel hotel, SearchWrapperHotelEntity hotelEntity, CancellationTimeline cancellationTimeline, DisplayFare displayFare) {
		try {
			if (null != hotel.getHotelPersuasions()) {
				JSONObject hotelPersuasions = new JSONObject(objectMapperUtil.getJsonFromObject(hotel.getHotelPersuasions(), DependencyLayer.CLIENTGATEWAY));
				for (String placeHolder : hotelPersuasions.keySet()) {
					JSONObject persuasion = hotelPersuasions.has(placeHolder) ? hotelPersuasions.getJSONObject(placeHolder) : null;
					if (null != persuasion && persuasion.has("data")) {
						JSONArray persuasionDataList = persuasion.getJSONArray("data");
						for (int i = 0; i < persuasionDataList.length(); i++) {
							JSONObject persuasionData = persuasionDataList.getJSONObject(i);
							if (persuasionData.has("hover") && persuasionData.getJSONObject("hover").has("tooltipType")) {
								addToolTip(persuasionData.getJSONObject("hover"), hotelEntity);
								if (persuasionData != null && persuasionData.getJSONObject("hover") != null && StringUtils.isNotEmpty(persuasionData.getJSONObject("hover").getString("tooltipType"))
										&& TOOL_TIP_INDIANNESS.equalsIgnoreCase(persuasionData.getJSONObject("hover").getString("tooltipType").toUpperCase())) {
									hotel.setLovedByIndians(true);
								}
							}
						}
					}
				}
				hotel.setHotelPersuasions(hotelPersuasions.toMap());
			}
		} catch (Exception e) {
			LOGGER.error("Error while updating hover data for desktop", e);
		}

	}

	private void updateTopLevelHover(JSONObject topLevelHoverData, JSONArray persuasionDataList) {

		switch (topLevelHoverData.getString("tooltipType").toUpperCase()) {
			case Constants.MP_FARE_HOLD:

				Hover hover = new Hover();
				//We will run a loop for each persuasion and for which we get expiry data we will break the loop at that time
				//and set hover title for top level hover
				for (int i = 0; i < persuasionDataList.length(); i++) {
					JSONObject persuasionData = persuasionDataList.getJSONObject(i);
					if (persuasionData.has("timer") && persuasionData.getJSONObject("timer").has("expiry")) {
						long expiry = persuasionData.getJSONObject("timer").getLong("expiry");
						hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
								dateUtil.convertEpochToDateTime(expiry, dateUtil.DD_MMM_hh_mm_a)));
						persuasionData.remove("timer");
						break;
					}
				}
				hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));

				topLevelHoverData.put("data", hover);
				topLevelHoverData.put(LOG_HOVER_KEY, LOG_HOVER_VALUE);
				topLevelHoverData.put("tooltipType", TITLE_SUBTITLE_TOOLTIP);
				break;
		}
	}

	private void addToolTip(JSONObject hoverData, SearchWrapperHotelEntity hotelEntity) {
		if (hoverData.getString("tooltipType").equalsIgnoreCase(TOOL_TIP_INDIANNESS)) {
			hoverData.put("data", createIndianessPersuasion(hotelEntity.getIndianessPersuasion()));
			hoverData.put("titleText",polyglotService.getTranslatedData(INDIANESS_HOVER_TITLE));
			hoverData.put("subText", polyglotService.getTranslatedData(INDIANESS_HOVER_SUBTITLE));
			hoverData.put("iconUrl", INDIANNESS_URL);
		}
	}

	public List<IndianessToolTipGi> createIndianessPersuasion(IndianessPersuasion indianessPersuasion){
		if(indianessPersuasion == null || CollectionUtils.isEmpty(indianessPersuasion.getShortSummary()))
			return new ArrayList<>();
		List<IndianessToolTipGi> data = new ArrayList<>();
		for(CampaignPojo campaignPojo : indianessPersuasion.getShortSummary()){
			IndianessToolTipGi indianessToolTipGi = new IndianessToolTipGi();
			indianessToolTipGi.setText(campaignPojo.getHeading());
			indianessToolTipGi.setIconUrl(campaignPojo.getIconUrl());
			data.add(indianessToolTipGi);
		}
		return data;
	}


	public CancellationTimeline createFreeCancellationTooltip(CancellationTimeline cancellationTimeline) {
		CancellationTimeline cancellationToolTip = new CancellationTimeline();
		if (cancellationTimeline != null) {
			cancellationToolTip.setCheckInDate(cancellationTimeline.getCheckInDate());
			cancellationToolTip.setCancellationDate(cancellationTimeline.getCancellationDate());
			cancellationToolTip.setSubTitle(cancellationTimeline.getSubTitle());
			cancellationToolTip.setFreeCancellationBenefits(cancellationTimeline.getFreeCancellationBenefits());
			cancellationToolTip.setFreeCancellationText(cancellationTimeline.getFreeCancellationText());
			cancellationToolTip.setTitle(cancellationTimeline.getTitle());
			cancellationToolTip.setBookingDate(cancellationTimeline.getBookingDate());
			cancellationToolTip.setFcTextForPersuasion(cancellationTimeline.getFcTextForPersuasion());
			cancellationToolTip.setCancellationPolicyTimelineList(cancellationTimeline.getCancellationPolicyTimelineList());
		}
		return cancellationToolTip;
	}

	public ValueStaysTooltip createValueStayToolTip(String countryCode) {
		ValueStaysTooltip valueStaysTooltip;
		if (StringUtils.isBlank(countryCode) || "IN".equalsIgnoreCase(countryCode)) {
			valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipDom);
		} else {
			valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipIntl);
		}
		polyglotHelper.translateValueStaysTooltip(valueStaysTooltip);
		return valueStaysTooltip;
	}

	public MyBizAssuredToolTip createMyBizAssuredToolTip() {
		MyBizAssuredToolTip myBizAssuredToolTip;
		myBizAssuredToolTip = SerializationUtils.clone(myBizAssuredTooltipDom);
		polyglotHelper.translateMyBizAssuredTooltip(myBizAssuredToolTip);
		return myBizAssuredToolTip;
	}

	private LuxeToolTip createLuxeToolTip() {
		LuxeToolTip luxeToolTip;
		luxeToolTip = SerializationUtils.clone(luxeToolTipConfig);
		polyglotHelper.translateLuxeToolTip(luxeToolTip);
		return luxeToolTip;
	}


	private Map<String, MySafetyTooltip> createMySafetyTooltipTranslated() {
		Map<String, MySafetyTooltip> mySafetyTooltipTranslated = new HashMap<>();
		List<String> activeLanguagesList = Arrays.asList(activeLanguages.split(","));
		MySafetyTooltip mySafetyTooltipConfig = SerializationUtils.clone(mySafetyDataTooltips);

		for (String lang : activeLanguagesList) {
			MySafetyTooltip mySafetyTooltip;
			mySafetyTooltip = polyglotHelper.translateMySafetyToolTip(mySafetyTooltipConfig, lang);
			mySafetyTooltipTranslated.put(lang, mySafetyTooltip);
		}
		return mySafetyTooltipTranslated;
	}

	private Map<String, Object> fetchToolTipPersuasionData(String stayType) {
		Map<String, Object> toolTipData = new HashMap<>();
		Map<String, Object> toolTipConfig = (Map<String, Object>) desktopToolTipPersuasionsMap.get(stayType);
		toolTipData.put("imageUrl", toolTipConfig.get("imageUrl"));
		toolTipData.put("toolTipHeading", polyglotService.getTranslatedData((String) toolTipConfig.get("toolTipHeading")));
		toolTipData.put("toolTipData", new ArrayList<String>());
		List<String> toolTipList = (List<String>) toolTipConfig.get("data");
		for (String toolTipKey : toolTipList) {
			((List<String>) toolTipData.get("toolTipData")).add(polyglotService.getTranslatedData(toolTipKey));
		}
		return toolTipData;
	}

	@Override
	public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasionList, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, boolean hcardV2) {
		PersuasionObject locPers = new PersuasionObject();
		if (CollectionUtils.isNotEmpty(locationPersuasionList)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String, Object>());

			locPers.setData(new ArrayList<>());
			locPers.setTemplate("LOC_PERSUASION");
			locPers.setPlaceholder(LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP);

			for (int index = 0; index < locationPersuasionList.size(); index++) {
				PersuasionData locPersuasionData = setLocationPersuasionData(locationPersuasionList, index);
				locPers.getData().add(locPersuasionData);
			}
		}

		try {
			((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP, locPers);
		} catch (ClassCastException e) {
			LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
		}


		if (CollectionUtils.isNotEmpty(facilities)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String, Object>());
			PersuasionObject amenityPersuasion = new PersuasionObject();

			try {
				if (((Map<Object, Object>) hotel.getHotelPersuasions()).containsKey(AMENITIES_PLACEHOLDER_ID_DESKTOP))
					amenityPersuasion = (PersuasionObject) ((Map<Object, Object>) hotel.getHotelPersuasions()).get(AMENITIES_PLACEHOLDER_ID_DESKTOP);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
			}

			amenityPersuasion.setData(new ArrayList<>());
			amenityPersuasion.setTemplate("MULTI_PERSUASION_EH");
			amenityPersuasion.setPlaceholder("DT_CARD_LEFT_10");



			/*BUILD HOVER*/
			PersuasionStyle headingStyle = new PersuasionStyle();
//			headingStyle.setTextColor("#2276E3");
//			headingStyle.setBgColor("#FFFF");
//			headingStyle.setCornerRadii("20");
			if(amenitiesIconUrls.containsKey("hover_heading")){
				headingStyle.setIconUrl(amenitiesIconUrls.get("hover_heading").get("url"));
			}

			/* Not Required
			PersuasionStyle hoverStyle = new PersuasionStyle();
			BorderGradient bgGradient= new BorderGradient();
			bgGradient.setStart("#F4EEE1");
			bgGradient.setEnd("#D8EAFF");
			bgGradient.setAngle("300");
			hoverStyle.setBgGradient(bgGradient);*/


			com.mmt.hotels.clientgateway.thirdparty.response.Hover hover = new com.mmt.hotels.clientgateway.thirdparty.response.Hover();
			hover.setOpenHoverThreshold(3);
			hover.setHeadingStyle(headingStyle);
//			hover.setStyle(hoverStyle);
			hover.setCtaText("& more");
			hover.setCtaColor("#2276E3");
			hover.setHeadingText("Amenities");
			amenityPersuasion.setHover(hover);



			/*TOP LEVEL STYLE*/
			PersuasionStyle persuasionStyle = new PersuasionStyle();
			persuasionStyle.setTextColor("#717171");
			persuasionStyle.setBorderColor("#E4E4E4");

			amenityPersuasion.setStyle(persuasionStyle);

			int index = 1 ;
			for (String facility:facilities) {
				PersuasionData amenPersuasionData = new PersuasionData();
				amenPersuasionData.setId("AMENITIES_" + index++);
				amenPersuasionData.setText(facility);
				amenPersuasionData.setPersuasionType("usptagPersuasion");
				String text = Utility.removeSpecialChar(amenPersuasionData.getText());
				if (StringUtils.isNotBlank(text) && amenitiesIconUrls.containsKey(text.toLowerCase())) {
					amenPersuasionData.setIconurl(amenitiesIconUrls.get(text.toLowerCase()).get("url"));
				} else if (StringUtils.isNotBlank(text) && amenitiesIconUrls.containsKey("default")) {
						amenPersuasionData.setIconurl(amenitiesIconUrls.get("default").get("url"));
				}
				amenityPersuasion.getData().add(amenPersuasionData);
			}
			try {
				if (!((Map<Object,Object>) hotel.getHotelPersuasions()).containsKey(AMENITIES_PLACEHOLDER_ID_DESKTOP)) {
					((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_DESKTOP,amenityPersuasion);
				}else{
					((Map<Object,Object>) hotel.getHotelPersuasions()).replace(Constants.AMENITIES_PLACEHOLDER_ID_DESKTOP,amenityPersuasion);
				}
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}

		if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && StringUtils.isNotEmpty(dayUsePersuasionsText)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject amenityPers = new PersuasionObject();
			amenityPers.setData(new ArrayList<>());
			amenityPers.setTemplate(IMAGE_TEXT_H);
			amenityPers.setPlaceholder("SINGLE");


			PersuasionData amenPersuasionData = new PersuasionData();
			amenPersuasionData.setHasAction(false);
			amenPersuasionData.setHtml(false);
			amenPersuasionData.setId(DAYUSE_LOCAL_ID);
			amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
			amenPersuasionData.setStyle(new PersuasionStyle());
			amenPersuasionData.getStyle().setTextColor("#000000");
			amenPersuasionData.setText(dayUsePersuasionsText);
			amenityPers.getData().add(amenPersuasionData);
			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}
	}


	private PersuasionData setLocationPersuasionData(List<String> locationPersuasionList, int index) {
		String location = locationPersuasionList.get(index);
		PersuasionData locPersuasionData = new PersuasionData();
		PersuasionStyle persuasionStyle =new PersuasionStyle();

		if(index ==0) {
			locPersuasionData.setText(location);
			locPersuasionData.setHasAction(true);
			locPersuasionData.setActionType("LOCATION_FILTER");
			persuasionStyle.setTextColor("#2276E3");
		}
		else{
			locPersuasionData.setText(" | " + location);
			persuasionStyle.setTextColor("#717171");
		}

		locPersuasionData.setStyle(persuasionStyle);
		locPersuasionData.setId("LOC_PERSUASION_" + (index +1));
		locPersuasionData.setPersuasionType("LOCATION");

		return locPersuasionData;
	}

	@Override
	public  BottomSheet buildBottomSheet(PersonalizedResponse<SearchWrapperHotelEntity> perResponse) {
		BottomSheet bottomSheet = new BottomSheet();
		bottomSheet.setHeading(perResponse.getHeading());
		bottomSheet.setSubHeading(perResponse.getHeading());
		bottomSheet.setImgUrl(myBizAssuredUrl);
		bottomSheet.setCta(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_ASSURED_FILTER_CARD_CTA));
		bottomSheet.setCtaAction("");
		List<SectionFeature> sectionFeatureList = new ArrayList<>();
		sectionFeatureList.add(new SectionFeature(highRatedUrl, polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(gstInvoiceUrl, polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(bpgUrl, polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT), "grayDot", null, null));
		bottomSheet.setSectionFeatures(sectionFeatureList);
		return bottomSheet;
	}

	public MyBizStaticCard buildStaticCard(String section, List<SearchWrapperHotelEntity> hotels) {
		MyBizStaticCard staticCard = null;
		if(CORPBUDGET_DIRECT_HOTEL.equalsIgnoreCase(section) && CollectionUtils.isNotEmpty(hotels) && !hotels.get(0).isCorpBudgetHotel() &&
				myBizStaticCard != null) {
			staticCard = SerializationUtils.clone(myBizStaticCard);
			staticCard.setActionUrl(hotels.get(0).getDetailDeeplinkUrl());
			translateStaticCard(staticCard);
		}
		return staticCard;
	}

	protected void translateStaticCard(MyBizStaticCard staticCard) {
		staticCard.setText(polyglotService.getTranslatedData(staticCard.getText()));
		staticCard.setSubtext(polyglotService.getTranslatedData(staticCard.getSubtext()));
		staticCard.setCtaText(polyglotService.getTranslatedData(staticCard.getCtaText()));
	}

	//To build location persuasion to Mob-Landing Cards
	public void addLocationPersuasionToHotelPersuasions(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity != null && CollectionUtils.isNotEmpty(hotelEntity.getLocationPersuasion())) {
			List<String> locationPersuasion = hotelEntity.getLocationPersuasion();
			if (hotelEntity.getHotelPersuasions() == null)
				hotelEntity.setHotelPersuasions(new HashMap<String, Object>());
			PersuasionObject locPers = new PersuasionObject();
			locPers.setData(new ArrayList<>());
			locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP);
			locPers.setTemplate("MULTI_PERSUASION_V");
			locPers.setPlaceholder("MULTI");

			int index = 1;
			PersuasionData locPersuasionData = new PersuasionData();
			locPersuasionData.setHasAction(false);
			locPersuasionData.setStyle(new PersuasionStyle());
			List<String> styleClasses = new ArrayList<>();
			styleClasses.add("pc__location");
			locPersuasionData.getStyle().setStyleClasses(styleClasses);
			locPersuasionData.setHtml(true);
			locPersuasionData.setId("LOC_PERSUASION_" + index++);
			locPersuasionData.setPersuasionType("LOCATION");

			locPers.getData().add(locPersuasionData);

			if (locationPersuasion.size() == 1) {
				locPersuasionData.setText(locationPersuasion.get(0));
			} else if (locationPersuasion.size() >= 2) {
				locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
			}

			try {
				((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP, locPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
			}
		}

	}

	/**
	 * This is an encapsulated method to build all the required persuasions for Hidden Gem card.
	 */
	@Override
	public void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<String,Object>());
		addLocationPersuasionToHotelPersuasions(hotelEntity);
		buildHiddenGemPersuasion(hotelEntity);
		buildHiddenGemIconPersuasion(hotelEntity);
		buildHomeStaysPersuasion(hotelEntity);
	}

	/**
	 * Method to build Hidden Gem Persuasion, this method calls persuasionUtil to build Hidden Gem persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			if(CollectionUtils.isNotEmpty(hiddenGemPersuasion.getData()))
				hiddenGemPersuasion.getData().get(0).setPersuasionType(PersuasionType.PEITHO.name());
			hiddenGemPersuasion.setTemplate(PersuasionTemplate.IMAGE_TEXT_H.name());
			hiddenGemPersuasion.setTemplateType(TemplateType.DEFAULT.name());
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdDesktop());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdDesktop(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Hidden Gem Icon Persuasion, this method calls persuasionUtil to build Hidden Gem Icon persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemIconPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemIconPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			if(CollectionUtils.isNotEmpty(hiddenGemPersuasion.getData()))
				hiddenGemPersuasion.getData().get(0).setPersuasionType(PersuasionType.HOTEL_CATEGORY.name());
			hiddenGemPersuasion.setTemplateType(TemplateType.DEFAULT.name());
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdDesktop());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdDesktop(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Home Stays Persuasions, this method calls persuasionUtil to build Homestay persuasions.
	 * If the util method return a non-empty Persuasion List for homestay title, this method will add that persuasion in Hotel Persuasion object.
	 * And, if util method return a non-empty Persuasion List for homestay title and sub-title, this method will add both the persuasions in Hotel Persuasion object, on the same placeholder
	 */
	public void buildHomeStaysPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject homeStaysTitlePersuasion = persuasionUtil.buildHomeStaysTitlePersuasion(hotelEntity, DEVICE_TYPE);
		if (homeStaysTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysTitlePersuasion.getData()) && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			homeStaysTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdDesktop());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdDesktop(), homeStaysTitlePersuasion);

			PersuasionObject homeStaysSubTitlePersuasion = persuasionUtil.buildHomeStaysSubTitlePersuasion(hotelEntity, DEVICE_TYPE);
			if (homeStaysSubTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysSubTitlePersuasion.getData())) {
				homeStaysSubTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdDesktop());
				homeStaysTitlePersuasion.getData().add(homeStaysSubTitlePersuasion.getData().get(0));
				MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdDesktop(), homeStaysTitlePersuasion);
			}
		}
	}

}
