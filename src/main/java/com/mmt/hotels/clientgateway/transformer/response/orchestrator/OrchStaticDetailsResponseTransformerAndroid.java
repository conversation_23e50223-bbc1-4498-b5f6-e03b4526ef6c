package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;

@Component
public class OrchStaticDetailsResponseTransformerAndroid extends OrchStaticDetailResponseTransformer {

    @Value("${star.host.icon.app}")
    private String starHostIconApp;

    @Autowired
    private PropertyManager propertyManager;

    @Autowired
    PolyglotService polyglotService;

    Map<String, String> cardTitleMap;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchStaticDetailsResponseTransformerAndroid.class);

    @PostConstruct
    public void init() {
        try {
            CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
            cardTitleMap = commonConfig.cardTitleMap();

            commonConfig.addPropertyChangeListener("cardTitleMap", evt -> cardTitleMap = commonConfig.cardTitleMap());
        } catch (Exception ex) {
            LOGGER.error("error in fetching commonConfig pms properties", ex);
        }
    }


    @Override
    public Map<String, String> buildCardTitleMap() {
        Map<String, String> cardTitleMapAndroid = null;
        try {
            if (MapUtils.isNotEmpty(cardTitleMap)) {
                cardTitleMapAndroid = cardTitleMap.entrySet().stream().collect(Collectors.toMap(e -> e.getKey(), e -> polyglotService.getTranslatedData(e.getValue())));
            }
        } catch (Exception e) {
            LOGGER.error("Error while creating cardTitleMap for android", e);
        }
        return cardTitleMapAndroid;
    }

    @Override
    public void addTitleData(HotelResult hotelResult, String countryCode) {
    }

    @Override
    public String getLuxeIcon() {
        return LUXE_ICON_APPS;
    }

    @Override
    public StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if(staffInfo != null && BooleanUtils.isTrue(staffInfo.getIsStarHost())){
            staffInfo.setStarHostIconUrl(starHostIconApp);
        }
        removeIcon(staffInfo);
        StaffInfo staffInfoCg = super.convertStaffInfo(staffInfo);
        return staffInfoCg;
    }
}
