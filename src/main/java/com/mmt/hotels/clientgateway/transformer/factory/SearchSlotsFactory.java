package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchSlotsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchSlotsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchSlotsResponseTransformerIOS;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SearchSlotsFactory {

    @Autowired
    private OrchSearchSlotsResponseTransformerAndroid orchSearchRoomsResponseTransformerAndroid;

    @Autowired
    private OrchSearchSlotsResponseTransformerIOS orchSearchRoomsResponseTransformerIOS;

    public OrchSearchSlotsResponseTransformer getOrchResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return orchSearchRoomsResponseTransformerAndroid;
        switch (client) {
            case "PWA":
            case "MSITE":
            case "ANDROID":
                return orchSearchRoomsResponseTransformerAndroid;
            case "IOS":
                return orchSearchRoomsResponseTransformerIOS;
        }
        return orchSearchRoomsResponseTransformerAndroid;
    }
}
