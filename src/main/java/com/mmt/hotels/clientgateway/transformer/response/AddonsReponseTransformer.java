package com.mmt.hotels.clientgateway.transformer.response;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.request.payment.AddOnNode;
import com.mmt.hotels.clientgateway.response.SearchAddonsResponse;
import com.mmt.hotels.model.response.addon.AddOnEntity;

@Component
public class AddonsReponseTransformer {

	@Autowired
	private CommonResponseTransformer commonResponseTransformer;
	
	public SearchAddonsResponse convertSearchAddonsResponse(AddOnEntity getAddonsResponse) {
		SearchAddonsResponse searchAddonsResponse = new SearchAddonsResponse();
		List<AddOnNode> addonsList = commonResponseTransformer.getAddons(getAddonsResponse.getAddOns());
		searchAddonsResponse.setAddons(addonsList);
		return searchAddonsResponse;
	}

}
