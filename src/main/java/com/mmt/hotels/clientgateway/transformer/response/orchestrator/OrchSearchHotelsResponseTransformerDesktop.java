package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.content.CampaignPojo;
import com.gommt.hotels.orchestrator.model.response.content.IndiannessPersuasion;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.IndianessToolTipGi;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.response.IndianessPersuasion;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.AMENITIES_PLACEHOLDER_ID_DESKTOP;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INDIANESS_HOVER_SUBTITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INDIANESS_HOVER_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INDIANNESS_URL;

@Component
public class OrchSearchHotelsResponseTransformerDesktop extends OrchSearchHotelsResponseTransformer {

    @Autowired
    PropertyManager propManager;

    @Autowired
    PolyglotHelper polyglotHelper;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    DateUtil dateUtil;

    @Autowired
    PersuasionUtil persuasionUtil;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Value("${desktop.tool.tip.persuasions}")
    private String toolTipPersuasions;

    @Value("${active.languages}")
    private String activeLanguages;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformerDesktop.class);

    private MySafetyTooltip mySafetyDataTooltips;

    List<String> amenetiesWithUrl;

    private Map<String, Map<String, String>> amenitiesIconUrls;

    @PostConstruct
    public void init() {
        PropertyTextConfig propertyTextConfig = propManager.getProperty("propertyTextConfig", PropertyTextConfig.class);
        mySafetyDataTooltips = propertyTextConfig.mySafetyTooltipKeys();
        propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> {
            mySafetyDataTooltips = propertyTextConfig.mySafetyTooltipKeys();
        });
        amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl();

        /*Config to get Icons For Amenities*/
        amenitiesIconUrls = propertyTextConfig.amenitiesIconUrls();

        propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> {
            amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl();
        });
        CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
        missingSlotDetails = commonConfig.missingSlotDetails();
        thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
        commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
    }

    @Override
    public void addLocationPersuasionToHotelPersuasions(
            Hotel hotel, List<String> locationPersuasionList, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities,
            boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocationDetails locusData, boolean hcardV2) {

        PersuasionObject locPers = new PersuasionObject();
        if (CollectionUtils.isNotEmpty(locationPersuasionList)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            locPers.setData(new ArrayList<>());
            locPers.setTemplate("LOC_PERSUASION");
            locPers.setPlaceholder(LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP);

            for (int index = 0; index < locationPersuasionList.size(); index++) {
                PersuasionData locPersuasionData = setLocationPersuasionData(locationPersuasionList, index);
                locPers.getData().add(locPersuasionData);
            }
        }

        try {
            ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP, locPers);
        } catch (ClassCastException e) {
            LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
        } catch (Exception e) {
            LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
        }

        if (CollectionUtils.isNotEmpty(facilities)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject amenityPersuasion = new PersuasionObject();

            try {
                if (((Map<Object, Object>) hotel.getHotelPersuasions()).containsKey(AMENITIES_PLACEHOLDER_ID_DESKTOP))
                    amenityPersuasion = (PersuasionObject) ((Map<Object, Object>) hotel.getHotelPersuasions()).get(AMENITIES_PLACEHOLDER_ID_DESKTOP);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }

            amenityPersuasion.setData(new ArrayList<>());
            amenityPersuasion.setTemplate("MULTI_PERSUASION_EH");
            amenityPersuasion.setPlaceholder("DT_CARD_LEFT_10");

            /*BUILD HOVER*/
            PersuasionStyle headingStyle = new PersuasionStyle();
//			headingStyle.setTextColor("#2276E3");
//			headingStyle.setBgColor("#FFFF");
//			headingStyle.setCornerRadii("20");
            if (amenitiesIconUrls.containsKey("hover_heading")) {
                headingStyle.setIconUrl(amenitiesIconUrls.get("hover_heading").get("url"));
            }

			/* Not Required
			PersuasionStyle hoverStyle = new PersuasionStyle();
			BorderGradient bgGradient= new BorderGradient();
			bgGradient.setStart("#F4EEE1");
			bgGradient.setEnd("#D8EAFF");
			bgGradient.setAngle("300");
			hoverStyle.setBgGradient(bgGradient);*/

            com.mmt.hotels.clientgateway.thirdparty.response.Hover hover = new com.mmt.hotels.clientgateway.thirdparty.response.Hover();
            hover.setOpenHoverThreshold(3);
            hover.setHeadingStyle(headingStyle);
//			hover.setStyle(hoverStyle);
            hover.setCtaText("& more");
            hover.setCtaColor("#2276E3");
            hover.setHeadingText("Amenities");
            amenityPersuasion.setHover(hover);

            /*TOP LEVEL STYLE*/
            PersuasionStyle persuasionStyle = new PersuasionStyle();
            persuasionStyle.setTextColor("#717171");
            persuasionStyle.setBorderColor("#E4E4E4");
            amenityPersuasion.setStyle(persuasionStyle);

            int index = 1;
            for (String facility : facilities) {
                PersuasionData amenPersuasionData = new PersuasionData();
                amenPersuasionData.setId("AMENITIES_" + index++);
                amenPersuasionData.setText(facility);
                amenPersuasionData.setPersuasionType("usptagPersuasion");
                String text = Utility.removeSpecialChar(amenPersuasionData.getText());
                if (StringUtils.isNotBlank(text) && amenitiesIconUrls.containsKey(text.toLowerCase())) {
                    amenPersuasionData.setIconurl(amenitiesIconUrls.get(text.toLowerCase()).get("url"));
                } else if (StringUtils.isNotBlank(text) && amenitiesIconUrls.containsKey("default")) {
                    amenPersuasionData.setIconurl(amenitiesIconUrls.get("default").get("url"));
                }
                amenityPersuasion.getData().add(amenPersuasionData);
            }
            try {
                if (!((Map<Object, Object>) hotel.getHotelPersuasions()).containsKey(AMENITIES_PLACEHOLDER_ID_DESKTOP)) {
                    ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_DESKTOP, amenityPersuasion);
                } else {
                    ((Map<Object, Object>) hotel.getHotelPersuasions()).replace(Constants.AMENITIES_PLACEHOLDER_ID_DESKTOP, amenityPersuasion);
                }
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }
        }

        if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && StringUtils.isNotEmpty(dayUsePersuasionsText)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");

            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId(DAYUSE_LOCAL_ID);
            amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            amenPersuasionData.setText(dayUsePersuasionsText);
            amenityPers.getData().add(amenPersuasionData);
            try {
                ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP, amenityPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }
        }
    }

    @Override
    protected BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse) {
        return null;
    }

    @Override
    public void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity) {
        if (hotel == null || hotel.getHotelPersuasions() == null) return;
        try {
            if (null != hotel.getHotelPersuasions()) {
                JSONObject persuasions = new JSONObject(objectMapperUtil.getJsonFromObject(hotel.getHotelPersuasions(), DependencyLayer.CLIENTGATEWAY));
                for (String placeHolder : persuasions.keySet()) {
                    JSONObject persuasion = persuasions.has(placeHolder) ? persuasions.getJSONObject(placeHolder) : null;
                    if (null != persuasion && persuasion.has("data")) {
                        JSONArray persuasionDataList = persuasion.getJSONArray("data");
                        if (persuasionDataList != null) {
                            for (int i = 0; i < persuasionDataList.length(); i++) {
                                JSONObject persuasionData = persuasionDataList.getJSONObject(i);
                                if (persuasionData != null && persuasionData.has("hover") && persuasionData.getJSONObject("hover") != null && StringUtils.isNotEmpty(persuasionData.getJSONObject("hover").getString("tooltipType"))
                                        && TOOL_TIP_INDIANNESS.equalsIgnoreCase(persuasionData.getJSONObject("hover").getString("tooltipType"))) {
                                    addToolTip(persuasionData.getJSONObject("hover"), hotelEntity);
                                    hotel.setLovedByIndians(true);
                                }
                            }
                        }
                    }
                }
                hotel.setHotelPersuasions(persuasions.toMap());
            }
        } catch (Exception e) {
            LOGGER.error("Error Occurred while accessing for indianess persuasions", e);
        }
    }

    private void addToolTip(JSONObject hoverData, HotelDetails hotelEntity) {
        if (hoverData.getString("tooltipType").equalsIgnoreCase(TOOL_TIP_INDIANNESS)) {
            List<IndianessToolTipGi> indianessToolTipGiList = createIndianessPersuasion(hotelEntity.getIndianessPersuasion());
            JSONArray jsonArray = new JSONArray();
            for (IndianessToolTipGi item : indianessToolTipGiList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("text", item.getText());
                jsonObject.put("iconUrl", item.getIconUrl());
                jsonArray.put(jsonObject);
            }
            hoverData.put("data", jsonArray);
            hoverData.put("titleText",polyglotService.getTranslatedData(INDIANESS_HOVER_TITLE));
            hoverData.put("subText", polyglotService.getTranslatedData(INDIANESS_HOVER_SUBTITLE));
            hoverData.put("iconUrl", INDIANNESS_URL);
        }
    }

    public List<IndianessToolTipGi> createIndianessPersuasion(IndiannessPersuasion indianessPersuasion) {
        if (indianessPersuasion == null || CollectionUtils.isEmpty(indianessPersuasion.getShortSummary())) return new ArrayList<>();
        List<IndianessToolTipGi> data = new ArrayList<>();
        for (CampaignPojo campaignPojo : indianessPersuasion.getShortSummary()) {
            IndianessToolTipGi indianessToolTipGi = new IndianessToolTipGi();
            indianessToolTipGi.setText(campaignPojo.getHeading());
            indianessToolTipGi.setIconUrl(campaignPojo.getIconUrl());
            data.add(indianessToolTipGi);
        }
        return data;
    }

    @Override
    public void addSeoTextPersuasion(Hotel hotel, HotelDetails hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName) {}

    @Override
    protected String buildBGColor(String section, String orientation, String cardType) {
        return "";
    }

    @Override
    protected void addBookingConfirmationPersuasion(HotelDetails hotelEntity) {}

    @Override
    public MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels) {
        return null;
    }

    private PersuasionData setLocationPersuasionData(List<String> locationPersuasionList, int index) {
        String location = locationPersuasionList.get(index);
        PersuasionData locPersuasionData = new PersuasionData();
        PersuasionStyle persuasionStyle = new PersuasionStyle();

        if (index == 0) {
            locPersuasionData.setText(location);
            locPersuasionData.setHasAction(true);
            locPersuasionData.setActionType("LOCATION_FILTER");
            persuasionStyle.setTextColor("#2276E3");
        } else {
            locPersuasionData.setText(" | " + location);
            persuasionStyle.setTextColor("#717171");
        }

        locPersuasionData.setStyle(persuasionStyle);
        locPersuasionData.setId("LOC_PERSUASION_" + (index + 1));
        locPersuasionData.setPersuasionType("LOCATION");

        return locPersuasionData;
    }

}
