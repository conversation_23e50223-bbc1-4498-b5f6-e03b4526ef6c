package com.mmt.hotels.clientgateway.transformer.response.ios;

import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;

@Component
public class StaticDetailResponseTransformerIOS extends StaticDetailResponseTransformer{

    @Value("${star.host.icon.app}")
    private String starHostIconApp;

    @Override
    protected Map<String, String> buildCardTitleMap() {
        return null;
    }

    @Override
    protected void addTitleData(HotelResult hotelResult, String countryCode) {

    }

    @Override
    protected String getLuxeIcon() {
        return LUXE_ICON_APPS;
    }

    @Override
    public StaffInfo convertStaffInfo(StaffInfo staffInfo) {
        if(staffInfo != null && BooleanUtils.isTrue(staffInfo.getIsStarHost())){
            staffInfo.setStarHostIconUrl(starHostIconApp);
        }
        removeIcon(staffInfo);
        return staffInfo;
    }
}
