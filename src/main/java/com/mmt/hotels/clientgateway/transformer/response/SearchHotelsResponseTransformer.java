package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.searchHotels.CollectionCardPersuasion;
import com.mmt.hotels.clientgateway.response.searchHotels.Facility;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.request.flyfish.FlyfishReviewData;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.flyfish.TopicRating;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.ConceptSummaryDTO;
import com.mmt.hotels.model.response.flyfish.ReviewSummary;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.response.detail.FeaturedAmenity;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;

@Component
public abstract class SearchHotelsResponseTransformer {

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private DayUseUtil dayUseUtil;

    @Value("${listing.myBiz.Assured.tooltip.iconType}")
    private String myBizToolTipIconType;

    @Value("${mybiz.assured.url}")
    private String myBizAssuredUrl;

    @Value("${high.rated.url}")
    private String highRatedUrl;

    @Value("${gst.invoice.url}")
    private String gstInvoiceUrl;

    @Value("${bpg.url}")
    private String bpgUrl;

    @Value("${desktop.persuasion.placeholders.demand.concentration.tobe.blocked}")
    private List<String> desktopPersPlaceholdersToBeBlockedDemandConc;

    @Value("${apps.persuasion.placeholders.demand.concentration.tobe.blocked}")
    private List<String> appsPersPlaceholdersToBeBlockedDemandConc;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private MobConfigHelper mobConfigHelper;

    @Autowired
    protected ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private PropertyManager propManager;

    @Autowired
    private Utility utility;

    @Value("${persuasion.place.holders.to.show}")
    private String placeHoldersToShowConfig;

    @Value("${filter.conditions}")
    private String filterConditionsConfig;

    @Autowired
    private CommonHelper commonHelper;

    public MissingSlotDetail missingSlotDetails = null;

    public int thresholdForSlashedAndDefaultHourPrice = 0;

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchHotelsResponseTransformer.class);

    @PostConstruct
    public void init() {
        CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
        missingSlotDetails = commonConfig.missingSlotDetails();
        LOGGER.warn("missingSlotDetails : {}", missingSlotDetails);
        thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
    }

    public SearchHotelsResponse convertSearchHotelsResponse(ListingPagePersonalizationResponsBO listingPageResponseBO,
                                                            SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {
        long startTime = System.currentTimeMillis();
        SearchHotelsResponse searchHotelsResponse = new SearchHotelsResponse();
        try {
            searchHotelsResponse.setCurrency(listingPageResponseBO.getCurrency());
            searchHotelsResponse.setHotelCount(listingPageResponseBO.getTotalHotelCounts());
            searchHotelsResponse.setHotelCountInCity(listingPageResponseBO.getHotelCountInCity());
            searchHotelsResponse.setLastHotelCategory(listingPageResponseBO.getLastFetchedHotelCategory());
            if (listingPageResponseBO.getExpData()!=null) {
                if (listingPageResponseBO.getExpData().containsKey("nearbyFixes") && Boolean.parseBoolean(listingPageResponseBO.getExpData().get("nearbyFixes"))
                        || listingPageResponseBO.getExpData().containsKey("emptyShopSolution") && Boolean.parseBoolean(listingPageResponseBO.getExpData().get("emptyShopSolution"))) {
                    searchHotelsResponse.setLastFetchedHotelCategory(listingPageResponseBO.getLastFetchedHotelCategory());
                }
            }
            searchHotelsResponse.setLastHotelId(listingPageResponseBO.getLastFetchedHotelId());
            searchHotelsResponse.setLastFetchedWindowInfo(listingPageResponseBO.getLastFetchedWindowInfo());
            if (listingPageResponseBO.getLocusData() != null) {
                searchHotelsResponse.setLocationDetail(buildLocationDetail(listingPageResponseBO.getLocusData().getLocusId(), listingPageResponseBO.getLocusData().getLocusName(),
                        listingPageResponseBO.getLocusData().getLocusType(), listingPageResponseBO.getCountryCode(), listingPageResponseBO.getCountryName()));
            }
            searchHotelsResponse.setCityLocationDetail(buildLocationDetail(listingPageResponseBO.getCityCode(), listingPageResponseBO.getCityName(),
                    "city", null, null));
            searchHotelsResponse.setNoMoreHotels(listingPageResponseBO.isNoMoreAvailableHotels());
            String idContext = searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
            searchHotelsResponse.setPersonalizedSections(buildPersonalizedSections(listingPageResponseBO.getPersonalizedResponse(), searchHotelsRequest.getExpData(), idContext, searchHotelsRequest, commonModifierResponse));
            searchHotelsResponse.setSortCriteria(buildSortCriteria(listingPageResponseBO.getSortCriteria()));
            searchHotelsResponse.setExpData(listingPageResponseBO.getExpData());
            searchHotelsResponse.setUsradid(listingPageResponseBO.getUsradid());
            searchHotelsResponse.setHydraSegments(fetchHydraSegment(listingPageResponseBO,commonModifierResponse));
            searchHotelsResponse.setUserLoyaltyStatus(listingPageResponseBO.getUserLoyaltyStatus());
            searchHotelsResponse.setUserFareHold(listingPageResponseBO.isUserFareHold());
            if (listingPageResponseBO.getSectionsType() != null && SectionsType.NEARBY.equals(listingPageResponseBO.getSectionsType())) {
                searchHotelsResponse.setSectionsType(listingPageResponseBO.getSectionsType().getValue());
            }
            if (StringUtils.isNotBlank(listingPageResponseBO.getSharingUrl())) {
                searchHotelsResponse.setSharingUrl(listingPageResponseBO.getSharingUrl());
            }
            searchHotelsResponse.setTickTockDealApplied(listingPageResponseBO.isTickTockApplied());
            if(listingPageResponseBO.getLuckyCouponStartTime() != null
                    && listingPageResponseBO.getLuckyCouponEndTime() != null) {
                /* Set Lucky Data V2 */
                LuckyData luckyDataV2 = new LuckyData();
                LuckyUserDetails luckyUserDetails = new LuckyUserDetails();
                luckyUserDetails.setCurrent(listingPageResponseBO.getLuckyCouponStartTime());
                luckyUserDetails.setEnd(listingPageResponseBO.getLuckyCouponEndTime());
                luckyDataV2.setLuckyUserDetails(luckyUserDetails);
                luckyDataV2.setLuckyPersuasion(polyglotService.getTranslatedData(GI_LUCKY_TIMER_TEXT));
                searchHotelsResponse.setLuckyDataV2(luckyDataV2);
            }
            if (StringUtils.isNotBlank(listingPageResponseBO.getListingDeepLinkWithoutFilters())) {
//				set listingDeepLinkWithoutFilters as recentDeepLink to pass to the client
                searchHotelsResponse.setRecentDeepLink(listingPageResponseBO.getListingDeepLinkWithoutFilters());
            }
            if (CollectionUtils.isNotEmpty(listingPageResponseBO.getPaxDetailsList())) {
                searchHotelsResponse.setPaxDetails(listingPageResponseBO.getPaxDetailsList());
            }
            if(commonModifierResponse != null) {
                searchHotelsResponse.setExpData(commonModifierResponse.getExpDataMap());
                searchHotelsResponse.setVariantKey(commonModifierResponse.getVariantKey());
            }
            /*ADDING FILTER CRITERIA TO PRE APPLIED FILTERS*/
            searchHotelsResponse.setPreAppliedFilters(searchHotelsRequest.getFilterCriteria());
            searchHotelsResponse.setNearMeFilterPill(getNearMeFilterPill(listingPageResponseBO, searchHotelsRequest));
        } finally {
            metricAspect.addToTimeInternalProcess(PROCESS_SEARCH_RESPONSE_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);
        }
        return searchHotelsResponse;
    }

    protected List<String> fetchHydraSegment(ListingPagePersonalizationResponsBO listingPageResponseBO, CommonModifierResponse commonModifierResponse) {
        if (listingPageResponseBO.getHydraSegments() != null) {
            return new ArrayList<>(listingPageResponseBO.getHydraSegments());
        }
        if (commonModifierResponse != null && commonModifierResponse.getHydraResponse() != null && commonModifierResponse.getHydraResponse().getHydraMatchedSegment()!=null) {
            return new ArrayList<>(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
        }
        return null;
    }

    private LocationDetail buildLocationDetail(String id, String name, String type, String countryId, String countryName) {
        LocationDetail locationDetail = new LocationDetail(id, name, type, countryId, countryName);
        return locationDetail;
    }

    private List<PersonalizedSection> buildPersonalizedSections(List<PersonalizedResponse<SearchWrapperHotelEntity>> list, String expData, String idContext, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<PersonalizedSection> personalizedSections = new ArrayList<>();
        Tuple<Boolean, String> directSearchHotelNameTup = new Tuple<>(Boolean.FALSE,"");

        //Fetch HotelId from Matchmaker Request
        String hotelId = Optional.of(searchHotelsRequest)
                .map(SearchHotelsRequest::getMatchMakerDetails)
                .map(MatchMakerRequest::getHotels)
                .filter(hotels -> !hotels.isEmpty())
                .map(hotels -> hotels.get(0))
                .map(InputHotel::getHotelId)
                .orElse("");

        //Fetch from Direct Hotel Section
        if(StringUtils.isNotEmpty(hotelId)){
            String hotelName = list.stream()
                    .filter(x -> DIRECT_HOTEL.equalsIgnoreCase(x.getSection()))
                    .findFirst()
                    .map(PersonalizedResponse::getHotels)
                    .filter(hotels -> !hotels.isEmpty())
                    .map(hotels -> hotels.get(0))
                    .map(SearchWrapperHotelEntityAbridged::getName)
                    .orElse("");
            if(StringUtils.isNotEmpty(hotelName)){
                directSearchHotelNameTup = new Tuple<>(Boolean.TRUE,hotelName);
            }
        }
        Tuple<Boolean, String> finalDirectSearchHotelNameTup = directSearchHotelNameTup;

        AtomicInteger similarSectionCounter = new AtomicInteger(0);
        list.forEach(perResponse -> {
            PersonalizedSection personalizedSection = new PersonalizedSection();
            personalizedSection.setName(perResponse.getSection());
            /*NO HEADINGS FOR GI, Only to show for Direct Search Case*/
//            personalizedSection.setHeading(perResponse.getHeading());
            if (!DIRECT_HOTEL.equalsIgnoreCase(perResponse.getSection()) && (Boolean.TRUE.equals(finalDirectSearchHotelNameTup.getX()) || SIMILAR_HOTELS.equalsIgnoreCase(perResponse.getSection())) && similarSectionCounter.get() <1) {
                String hotelName = finalDirectSearchHotelNameTup.getY();
                if (StringUtils.isNotEmpty(hotelName)) {
                    personalizedSection.setHeading(SIMILAR_PROPERTIES_HEADING_DYNAMIC_TEXT + hotelName);
                } else {
                    // Handle the case where hotelName is null
                    personalizedSection.setHeading(SIMILAR_PROPERTIES_HEADING_DEFAULT);
                }
                similarSectionCounter.incrementAndGet();
            }
            if (NEARBY_HOTELS_SECTION.equalsIgnoreCase(perResponse.getSection())) {
                personalizedSection.setHeading(perResponse.getHeading());
            }

            if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && Utility.isCorpBudgetHotelFunnel(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                personalizedSection.setStaticCard(buildStaticCard(perResponse.getSection(), perResponse.getHotels()));
            }
            personalizedSection.setSubHeading(perResponse.getSubHeading());
            if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && Utility.isGroupBookingFunnel(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                personalizedSection.setHeadingVisible(perResponse.isHeadingVisible());
            }
            if (StringUtils.isNotBlank(personalizedSection.getName()) && Constants.NOT_MYBIZ_ASSURED_SHOWN.equalsIgnoreCase(personalizedSection.getName())) {
                /*NO HEADINGS FOR GI*/
                //                overridePersonalizedSectionHeadingForDirectHotelSearch(searchHotelsRequest, personalizedSection);
                buildFilterCardForMyBizAndNonMyBizProperties(perResponse, personalizedSection);
            }
            if (StringUtils.isNotBlank(personalizedSection.getName()) && personalizedSection.getName().equalsIgnoreCase(Constants.MY_BIZ_ASSURED_SECTION)) {
                personalizedSection.setToolTip(buildMyBizAssuredToolTip());
            }
            if (perResponse.isMyBizAssuredRecommended()) {
                personalizedSection.setSectionFeatures(getMybizSimilarHotelsFeatures());
            }
            personalizedSection.setHotels(buildPersonalizedHotels(perResponse.getHotels(), expData, searchHotelsRequest, personalizedSection.getName(), commonModifierResponse));
            personalizedSection.setHotelCount(perResponse.getCount());
            personalizedSection.setOrientation(buildOrientation(perResponse.isHorizontal()));
            //Adding hotelCardType, seeMoreCTA, minHotelsToShow node here based newListingUi exp
            if (StringUtils.isNotEmpty(perResponse.getHotelCardType()))
                personalizedSection.setHotelCardType(perResponse.getHotelCardType().toLowerCase());
            if (perResponse.getMinCardCount() != null) {
                personalizedSection.setMinHotelsToShow(Integer.valueOf(perResponse.getMinCardCount()));
            }
            if (StringUtils.isNotEmpty(perResponse.getSeeMoreCTA())) {
                personalizedSection.setSeeMoreCTA(perResponse.getSeeMoreCTA());
            }
            personalizedSection.setShowMore(false);
            personalizedSection.setCardInsertionAllowed(getCardInsertionAllowedValue(perResponse, idContext));
            personalizedSection.setMyBizSimilarHotel(perResponse.getMyBizSimilarToDirectHotel());
            if (StringUtils.isNotBlank(perResponse.getSection()) && (mobConfigHelper.getCorpSectionListCount()).containsKey(perResponse.getSection())) {
                personalizedSection.setMinItemsToShow((mobConfigHelper.getCorpSectionListCount()).getOrDefault(perResponse.getSection(), 5));
                if (CollectionUtils.isNotEmpty(perResponse.getHotels()) && perResponse.getHotels().size() > personalizedSection.getMinItemsToShow())
                    personalizedSection.setShowMore(true);
            }
            personalizedSections.add(personalizedSection);
        });
        return personalizedSections;
    }

    private void overridePersonalizedSectionHeadingForDirectHotelSearch(SearchHotelsRequest searchHotelsRequest, PersonalizedSection personalizedSection) {
        MatchMakerRequest matchMakerDetails = null;
        String funnelSource = null;
        if (searchHotelsRequest != null) {
            if (searchHotelsRequest.getMatchMakerDetails() != null) {
                matchMakerDetails = searchHotelsRequest.getMatchMakerDetails();
            }
            if (searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getFunnelSource() != null) {
                funnelSource = searchHotelsRequest.getRequestDetails().getFunnelSource();
            }
        }
        if (matchMakerDetails != null && CollectionUtils.isNotEmpty(matchMakerDetails.getHotels())
                && null != matchMakerDetails.getHotels().get(0) && null != matchMakerDetails.getHotels().get(0).getHotelId()) {
            if (funnelSource != null && FUNNEL_SOURCE_CORPBUDGET.equalsIgnoreCase(funnelSource)) {
                personalizedSection.setHeading(polyglotService.getTranslatedData(Constants.CORP_BUDGET_PROPERTIES_TEXT));
            } else {
                personalizedSection.setHeading(polyglotService.getTranslatedData("MYBIZ_RECOMMENDED_PROPERTIES_NEAR_THIS_PROPERTY"));
            }
        }
    }

    private void buildFilterCardForMyBizAndNonMyBizProperties(PersonalizedResponse<SearchWrapperHotelEntity> perResponse,
                                                              PersonalizedSection personalizedSection) {
        personalizedSection.setFilterInfo(buildFilterInfo());
        personalizedSection.setBottomSheet(buildBottomSheet(perResponse));
    }

    protected abstract BottomSheet buildBottomSheet(PersonalizedResponse<SearchWrapperHotelEntity> perResponse);

    private SectionFeature buildFilterInfo() {
        SectionFeature filterInfo = new SectionFeature();
        filterInfo.setIconUrl(myBizAssuredUrl);
        filterInfo.setText(polyglotService.getTranslatedData("MYBIZ_ASSURED_FILTER_CARD_TEXT"));
        filterInfo.setIconType("checkbox");
        filterInfo.setActionTitle(polyglotService.getTranslatedData("KNOW_MORE"));
        List<Filter> filterCriteria = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.MMT_OFFERING);
        filter.setFilterValue(Constants.MyBiz_Assured);
        filter.setFilterRange(null);
        filter.setRangeFilter(false);
        filterCriteria.add(filter);
        filterInfo.setFilterCriteria(filterCriteria);
        return filterInfo;
    }

    private boolean getCardInsertionAllowedValue(PersonalizedResponse<SearchWrapperHotelEntity> perResponse, String idContext) {
        return !perResponse.isHorizontal() && (!StringUtils.isNotBlank(perResponse.getSection()) || (!LAST_BOOKED_HOTELS.equalsIgnoreCase(perResponse.getSection()) && (!StringUtils.isNotBlank(idContext) ||
                !RECENTLY_VIEWED_HOTELS.equalsIgnoreCase(perResponse.getSection()) || !B2C.equalsIgnoreCase(idContext))));
    }

    private String buildOrientation(boolean horizontal) {
        if (horizontal) {
            return Constants.HORIZONTAL;
        } else {
            return Constants.VERTICAL;
        }
    }

    /**
     * @param searchHotelsRequest can be null when called from buildComparatorResponse in StaticDetailResponseTransformer
     *                            adding null check to searchHotelsRequest is mandatory to avoid NPE
     * @param sectionName         Hotels section name
     * @throws NullPointerException if null checks are not added for searchHotelsRequest
     */
    public List<Hotel> buildPersonalizedHotels(List<SearchWrapperHotelEntity> hotelList, String expData, ListingSearchRequest searchHotelsRequest, String sectionName, CommonModifierResponse commonModifierResponse) {
        if (CollectionUtils.isEmpty(hotelList)) {
            return null;
        }
        try {

            boolean isMyPartnerRequest = (commonModifierResponse != null) && (commonModifierResponse.getExtendedUser() != null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
            List<Hotel> hotels = new ArrayList<>();
            String funnelSource;
            if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                funnelSource = searchHotelsRequest.getRequestDetails().getFunnelSource();
            } else {
                funnelSource = "";
            }
            final boolean[] odd = {true};
            hotelList.forEach(hotelEntity -> {
                Hotel hotel = new Hotel();
                hotel.setSpotlightApplicable(hotelEntity.isSpotlightApplicable());
                hotel.setGroupBookingHotel(hotelEntity.isGroupBookingHotel());
                hotel.setGroupBookingPrice(hotelEntity.isGroupBookingPrice());
                hotel.setMaskedPrice(hotelEntity.isMaskedPrice());
                hotel.setIsGroupBookingForSimilar(hotelEntity.isGroupBookingForSimilar());
                hotel.setId(hotelEntity.getId());
                hotel.setGiId(hotelEntity.getGiHotelId());
                hotel.setName(hotelEntity.getName());
                hotel.setPropertyType(hotelEntity.getPropertyType());
                hotel.setPropertyLabel(hotelEntity.getPropertyLabel());
                hotel.setStayType(hotelEntity.getStayType());
                hotel.setStarRating(hotelEntity.getStarRating());
                hotel.setFromCity(hotelEntity.getFromCity());
                hotel.setDistance(hotelEntity.getDistance());
                hotel.setCrossSellTag(hotelEntity.getCrossSellTag());
                hotel.setDistanceUnit(hotelEntity.getDistanceUnit());
                hotel.setFreeCancellationText(hotelEntity.getFreeCancellationText());
                hotel.setSoldOut(hotelEntity.getIsSoldOut() != null ? hotelEntity.getIsSoldOut() : (hotelEntity.getDisplayFare() == null));
                hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));
                hotel.setAlternateDates(hotelEntity.isAlternateDatesAvailable());
                hotel.setMultiRoomRecommendation(hotelEntity.isRecommendedMultiRoom());
                hotel.setIsAltAcco(hotelEntity.isAltAcco());
                hotel.setMtKey(hotelEntity.getMtkey());
                hotel.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelEntity.getGeoLocation()));
                hotel.setDeepLink(hotelEntity.getDesktopDeeplink());
                hotel.setAppDeeplink(hotelEntity.getAppDeeplink());
                hotel.setSeoUrl(hotelEntity.getSeoUrl());
                hotel.setShortDescSeo(hotelEntity.getShortDescSeo());
                hotel.setLocationPersuasion(hotelEntity.getLocationPersuasion());
                hotel.setMedia(commonResponseTransformer.buildMedia(hotelEntity.getMainImages(), hotelEntity.getHotelVideos(), expData));
                hotel.setTotalImageCount(CollectionUtils.isNotEmpty(hotelEntity.getMainImages()) ? hotelEntity.getMainImages().size() : 0);
                hotel.setTravellerImageCount(hotelEntity.getTravellerImageCount());
                if (FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource)) {
                    hotel.setSlotDetail(buildSlotDetails(hotelEntity, expData, searchHotelsRequest));
                }

                /* START - Build Price Detail */
                if ((FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource) && hotelEntity.getDisplayFare() != null)) {
                    if (!hotelEntity.getDisplayFare().isSlotRate() && dayUseUtil.shouldSetPriceDetailsForDayUse(hotel.getSlotDetail(), hotelEntity.getDisplayFare())) {
                        hotel.setPriceDetail(buildPriceDetailForDayUse(hotelEntity.getDisplayFare()));
                    }
                } else {
                    String idContext = searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
                    String priceSuffix = utility.getPriceSuffix(hotelEntity.isAltAcco(),hotelEntity.getDisplayFare()!=null ? hotelEntity.getDisplayFare().getTotalRoomCount() : 0, searchHotelsRequest);
                    hotel.setPriceDetail(buildPriceDetail(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null,
                            commonResponseTransformer.enableSaveValue(expData),
                            commonResponseTransformer.enableDiscount(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null),
                            (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().isMetaInfo()),
                            hotelEntity.getLowestRateSegmentId(), getGroupPriceAndSavingText(hotelEntity, searchHotelsRequest), idContext, priceSuffix,""));
                }
                /* END -  Build Price Detail */

                //updateAppDeeplinkForNoSlot(hotel, funnelSource);
                hotel.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getCorpMetaData() : null));
                hotel.setHotelPersuasions(hotelEntity.getHotelPersuasions());
                if (searchHotelsRequest != null) {
                    hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelEntity.getCountryCode(),
                            hotelEntity.getFlyfishReviewSummary(),
                            searchHotelsRequest.getDeviceDetails(),
                            commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null));
                } else {
                    hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelEntity.getCountryCode(),
                            hotelEntity.getFlyfishReviewSummary(), null,
                            commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null));
                }
                hotel.setLocationDetail(buildLocationDetail(hotelEntity.getCityCode(), hotelEntity.getCityName(), "city", hotelEntity.getCountryCode(), hotelEntity.getCountryName()));
                hotel.setCategories(hotelEntity.getCategories());
                hotel.setShortList(hotelEntity.isShortList());
                hotel.setTrackingInfo(hotelEntity.getTrackingInfo());
                hotel.setSponsored(hotelEntity.isSponsored());
                hotel.setNewType(hotelEntity.isNewType()); //"NEW" is the tag type which will be sent by singularity for reference (HTL-37120)
                hotel.setPoiTag(hotelEntity.getPoiTag());
                hotel.setTotalRoomCount((hotelEntity.getDisplayFare() != null && hotelEntity.getDisplayFare().getTotalRoomCount() != null) ? hotelEntity.getDisplayFare().getTotalRoomCount() : null);
                hotel.setPerNightTitle(commonHelper.buildPerNightPriceDescription(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getTotalRoomCount() : 0));
                hotel.setExtraMeals(hotelEntity.getExtraMeals());
                hotel.setViewType(hotelEntity.getViewType());
                hotel.setRatePersuasionText(hotelEntity.getRatePersuasionText());
                hotel.setCalendarCriteria(buildCalendarCriteria(hotelEntity.getCalendarCriteria()));

                boolean sectionMyBizSimilarToDirectHtl = MYBIZ_SIMILAR_TO_DIRECT_HOTEL.equalsIgnoreCase(sectionName) ? true : false;
                if (searchHotelsRequest != null)
                    addLocationPersuasionToHotelPersuasions(hotel, hotelEntity.getLocationPersuasion(), hotelEntity.getFacilityHighlights(), searchHotelsRequest, commonResponseTransformer.enableAmenitiesPersuasion(expData, searchHotelsRequest.getRequestDetails().getFunnelSource(), isMyPartnerRequest), sectionMyBizSimilarToDirectHtl, hotelEntity.getDayUsePersuasionsText(), hotelEntity.getNearestGroundTransportPoi(),commonResponseTransformer.getHCARDV2(expData));
                else
                    addLocationPersuasionToHotelPersuasions(hotel, hotelEntity.getLocationPersuasion(), hotelEntity.getFacilityHighlights(), null, commonResponseTransformer.enableAmenitiesPersuasion(expData, "HOTELS", isMyPartnerRequest), sectionMyBizSimilarToDirectHtl, hotelEntity.getDayUsePersuasionsText(), hotelEntity.getNearestGroundTransportPoi(),commonResponseTransformer.getHCARDV2(expData));
                addPersuasionHoverData(hotel, hotelEntity, hotelEntity.getCancellationTimeline(), hotelEntity.getDisplayFare());
                hotel.setLastBookedInfo(hotelEntity.getLastBookedInfo());
                hotel.setHotelBottomCard(buildHotelBottomCard(hotelEntity.getQuickBookInfo()));
                hotel.setHotelTopCard(buildHotelTopCard(hotelEntity.getMyBizSimilarToDirectObj()));
                hotel.setReviewDeeplinkUrl(hotelEntity.getReviewDeeplinkUrl());
                hotel.setSearchRoomDeeplinkUrl(hotelEntity.getSearchRoomDeeplinkUrl());
                hotel.setDetailDeeplinkUrl(hotelEntity.getDetailDeeplinkUrl());
                hotel.setHeroImage(hotelEntity.getHeroImage());

                if (hotelEntity.getCollectionCardPersuasion() != null) {
                    hotel.setCollectionCardPersuasion(new CollectionCardPersuasion());
                    hotel.getCollectionCardPersuasion().setText(hotelEntity.getCollectionCardPersuasion().getText());
                    hotel.getCollectionCardPersuasion().setIconUrl(hotelEntity.getCollectionCardPersuasion().getIconUrl());
                }
                hotel.setMmtHotelCategory(hotelEntity.getMmtHotelCategory());
                hotel.setWishListed(hotelEntity.isWishListed());
                hotel.setStoryViewDescription(hotelEntity.getLongTailStoryPersuasions());
                hotel.setPropertyHighlightText(hotelEntity.getLongTailPropertyCardPersuasions());
//                hotel.setGiReviewData(hotelEntity.getGiReviewData());
                hotel.setFacilitiesConfigMap(hotelEntity.getFacilitiesConfigMap());

                hotel.setPastBookingRating(hotelEntity.getPastBookingRating());
                hotel.setDateOfPreviousStay(hotelEntity.getDateOfPreviosStay());
                hotel.setRoomName(hotelEntity.getLowestRoomType());
                hotel.setReviewSummaryUgc(buildReviewSummary(hotelEntity.getReviewSummary(), commonModifierResponse));
                hotel.setBookingCount(hotelEntity.getBookingCount());
                hotel.setFacilityHighlights(hotelEntity.getFacilityHighlights());

                // Based on this feature flag need to suppress few persuasions at specific placeholders configured at PMS and provided by client
                //At PMS level maintain map of key and List or PlaceHolders to suppress to make it generic if in future we want to suppress different placeholder for different flow we can use the same map
                if (searchHotelsRequest != null && searchHotelsRequest.getFeatureFlags() != null && searchHotelsRequest.getFeatureFlags().isPersuasionSuppression()) {
                    Map<String, List<String>> placeholdersToShowMap = null;
                    List<String> placeholdersToShow = null;
                    try {
                        //Not Using PMS config here, as it will be deprecated
                        placeholdersToShowMap = objectMapperUtil.getObjectFromJsonWithType(placeHoldersToShowConfig, new TypeReference<Map<String, List<String>>>() {
                                },
                                DependencyLayer.CLIENTGATEWAY);
                    } catch (JsonParseException e) {
                        e.printStackTrace();
                    }
                    if (MapUtils.isNotEmpty(placeholdersToShowMap)) {
                        placeholdersToShow = placeholdersToShowMap.get(SIMILAR_HOTELS);
                    }
                    LOGGER.debug("Placeholder we want to show for PersuasionSuppression {}", placeholdersToShow);
                    if (CollectionUtils.isNotEmpty(placeholdersToShow)) {
                        Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
                        List<String> placeholdersToBeBlocked = new ArrayList<>();
                        for (Object key : hotelPersuasions.keySet()) {
                            if (!placeholdersToShow.contains(key)) {
                                placeholdersToBeBlocked.add(key.toString());
                            }
                        }
                        LOGGER.debug("Placeholder we have to block from Persuasion for PersuasionSuppression {}", placeholdersToBeBlocked);
                        for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
                            if (MapUtils.isNotEmpty(hotelPersuasions)) {
                                hotelPersuasions.remove(placeholderToBeBlocked);
                            }
                        }
                    }
                }


                String bookingDevice = (searchHotelsRequest != null && searchHotelsRequest.getDeviceDetails() != null) ? searchHotelsRequest.getDeviceDetails().getBookingDevice() : null;
                if (MYBIZ_SIMILAR_TO_DIRECT_HOTEL.equalsIgnoreCase(sectionName) && (DEVICE_IOS.equalsIgnoreCase(bookingDevice)
                        || DEVICE_OS_ANDROID.equalsIgnoreCase(bookingDevice) || DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice))) {
                    List<String> placeholdersToBeBlocked = DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice) ? desktopPersPlaceholdersToBeBlockedDemandConc : appsPersPlaceholdersToBeBlockedDemandConc;
                    Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
                    for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
                        if (MapUtils.isNotEmpty(hotelPersuasions)) {
                            hotelPersuasions.remove(placeholderToBeBlocked);
                        }
                    }

                }
                commonResponseTransformer.buildSelectiveHotelPersuasions(hotel, hotelEntity);


                //For GI-ALL, B2C Hotels Funnel Except Direct Search Will be part of Similar Hotels
                if (commonHelper.checkValidHotel(searchHotelsRequest, hotelEntity)) {
//                    try {
                        if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null
                                && searchHotelsRequest.getSearchCriteria() != null
                                && searchHotelsRequest.getRequestDetails().getIdContext() != null
                                && searchHotelsRequest.getRequestDetails().getFunnelSource() != null
                                && searchHotelsRequest.getSearchCriteria().getLocationType() != null
                                && B2C.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext())
                                && FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
                                && !(Constants.ZONE).equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getLocationType()))
                            //For GI-ALL Hotels Except Direct Search Will be part of Similar Hotels.
                            hotel.setSimilarHotelsRequired(true);
                        //TO DO FUTURE USE
                    // Based on this node client will hit search-hotel api similarHotel flow, and we set this flag based on some conditions provide by Product and configured at PMS to change them dynamically
                    //HTL-39243 Supress SimilarHotels true for Direct Searched Property
//                            hotel.setSimilarHotelsRequired(getSimilarHotelRequired(hotelEntity));


//                    } catch (JsonParseException e) {
//                        e.printStackTrace();
//                    }
                }

                odd[0] = !odd[0];
                hotels.add(hotel);
            });

            return hotels;
        } catch (Exception e) {
            LOGGER.error("An exception occurred in buildPersonalizedHotels ", e);
            return null;
        }
    }

    public FlyfishReviewData buildReviewSummary(ReviewSummary reviewSummaryUgc, CommonModifierResponse commonModifierResponse) {
        if (reviewSummaryUgc != null) {
            FlyfishReviewData flyfishReviewData = new FlyfishReviewData();
            flyfishReviewData.setFilteredReviewCount(reviewSummaryUgc.getFilteredReviewCount());
            flyfishReviewData.setHotelRating(reviewSummaryUgc.getHotelRating());
            flyfishReviewData.setReviewCount(reviewSummaryUgc.getReviewCount());
            flyfishReviewData.setOta(reviewSummaryUgc.getOta());
            flyfishReviewData.setOta(changeUGCDataSource(reviewSummaryUgc.getOta(), commonModifierResponse));
            flyfishReviewData.setRatingCount(reviewSummaryUgc.getRatingCount());
            flyfishReviewData.setRatingWiseCount(reviewSummaryUgc.getRatingWiseCount());
            flyfishReviewData.setIsNewListing(reviewSummaryUgc.getIsNewListing());
            if (CollectionUtils.isNotEmpty(reviewSummaryUgc.getTopicRatings())) {
                flyfishReviewData.setTopicRatings(reviewSummaryUgc.getTopicRatings().stream()
                        .filter(Objects::nonNull)
                        .map(topicRatings -> {
                            TopicRating topicRating = new TopicRating();
                            topicRating.setRating(topicRatings.getRating());
                            topicRating.setTitle(topicRatings.getTitle());
                            return topicRating;
                        })
                        .collect(Collectors.toCollection(ArrayList::new)));
            }
            return flyfishReviewData;
        }
        return null;
    }

    private String changeUGCDataSource(String currentOTA, CommonModifierResponse commonModifierResponse) {
        if (!StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, COMBINED_OTA_FLOW), TRUE)) {
            if (StringUtils.isNotBlank(currentOTA) && (StringUtils.equalsIgnoreCase(currentOTA, OTA.GI_EXP.getValue()) || StringUtils.equalsIgnoreCase(currentOTA, OTA.GI_BKG.getValue()))) {
                return OTA.GI.getValue();
            }
        }
        return currentOTA;
    }

    private boolean getSimilarHotelRequired(SearchWrapperHotelEntity hotelEntity) throws JsonParseException {
        FilterConditions filterConditions = objectMapperUtil.getObjectFromJsonWithType(filterConditionsConfig, new TypeReference<FilterConditions>() {
                },
                DependencyLayer.CLIENTGATEWAY);
        double price = -1;
        if (hotelEntity.getDisplayFare() != null && hotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) {
            price = (hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice() - (hotelEntity.getDisplayFare().getDisplayPriceBreakDown().isTaxIncluded() ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getTotalTax() : 0));
        }
        if (filterConditions != null && filterConditions.getRange() != null &&
                price < filterConditions.getRange().getMinValue() || price > filterConditions.getRange().getMaxValue()) {
            return false;
        }

        //if hotel category is not in excludedCategoryList then only we return true
        boolean isExcludedCategory = false;
        if (filterConditions != null && CollectionUtils.isNotEmpty(filterConditions.getCategoriesExcluded()) && CollectionUtils.isNotEmpty(hotelEntity.getCategories())) {
            for (String category : hotelEntity.getCategories()) {
                if (filterConditions.getCategoriesExcluded().contains(category)) {
                    isExcludedCategory = true;
                    break;
                }
            }
        }
        return !isExcludedCategory;
    }


    public void buildHiddenGemPersuasions(List<SearchWrapperHotelEntity> hotelList) {
        hotelList.forEach(this::addPersuasionsForHiddenGemCard);
    }

    public abstract void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity);

    private void updateAppDeeplinkForNoSlot(Hotel hotel, String funnelSource) {
        if (CollectionUtils.isEmpty(hotel.getSlotDetail()) && hotel.getPriceDetail() != null && FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource)) {
            if (StringUtils.isNotBlank(hotel.getAppDeeplink()) && hotel.getAppDeeplink().contains(FUNNEL_DAYUSE)) {
                String updatedDeeplink = hotel.getAppDeeplink().replace(FUNNEL_DAYUSE, FUNNEL_SOURCE_HOTELS);
                hotel.getPriceDetail().setHotelSearchDeeplink(null);
            }
        }
    }

    protected PriceDetail buildPriceDetailForDayUse(DisplayFare displayFare) {
        if (displayFare == null) {
            return null;
        }
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setTotalTax(displayFare.getTax() != null ? displayFare.getTax().getValue() : 0);
        if (displayFare.getSlashedPrice() != null) {
            priceDetail.setPrice(Math.floor(displayFare.getSlashedPrice().getSellingPriceNoTax()));
            priceDetail.setPriceWithTax(Math.floor(displayFare.getSlashedPrice().getSellingPriceWithTax()));
            priceDetail.setDiscountedPrice(Math.floor(displayFare.getSlashedPrice().getSellingPriceNoTax()));
            priceDetail.setDiscountedPriceWithTax(Math.floor(displayFare.getSlashedPrice().getSellingPriceWithTax()));
            priceDetail.setPriceSuffix(DAY_USE_ONE_NIGHT_PRICE);
        }
        return priceDetail;
    }

    public List<SlotDetail> buildSlotDetails(SearchWrapperHotelEntity hotelEntity, String expData, ListingSearchRequest searchHotelsRequest) {
        if (hotelEntity == null || CollectionUtils.isEmpty(hotelEntity.getRecommendedRoomTypeDetails())) {
            return null;
        }
        List<SlotDetail> slotDetailList = new ArrayList<>();
        List<RoomTypeDetails> roomTypeDetailsList = hotelEntity.getRecommendedRoomTypeDetails();
        Set<Integer> slotDetailCount = new HashSet<>();
        Slot slot = null;
        SlotDetail slotDetail = null;
        String slotTime = null;

        // If a particular select is requested return that slot only
        if (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null && searchHotelsRequest.getSearchCriteria().getSlot() != null
                && searchHotelsRequest.getSearchCriteria().getSlot().getDuration() != null && searchHotelsRequest.getSearchCriteria().getSlot().getDuration() > 0) {
            Integer requestedSlotDuration = searchHotelsRequest.getSearchCriteria().getSlot().getDuration();
            for (RoomTypeDetails roomTypeDetails : roomTypeDetailsList) {
                if (roomTypeDetails.getSlot() != null && roomTypeDetails.getSlot().getDuration() != null
                        && Objects.equals(requestedSlotDuration, roomTypeDetails.getSlot().getDuration())) {
                    buildSlotDetailList(hotelEntity, expData, searchHotelsRequest, slotDetailList, slotDetailCount, roomTypeDetails);
                }
            }
            return slotDetailList;
        }

        for (RoomTypeDetails roomTypeDetails : roomTypeDetailsList) {
            if (roomTypeDetails.getSlot() != null && roomTypeDetails.getSlot().getDuration() != null) {
                slotTime = buildSlotDetailList(hotelEntity, expData, searchHotelsRequest, slotDetailList, slotDetailCount, roomTypeDetails);
            }
        }

        if (!slotDetailCount.isEmpty() && slotDetailCount.size() < 3) {
            Iterator<Integer> itr = missingSlotDetails != null && missingSlotDetails.getDuration() != null ? missingSlotDetails.getDuration().iterator() : null;
            while (itr != null && itr.hasNext()) {
                Integer value = itr.next();
                if (!slotDetailCount.contains(value)) {
                    slotDetail = new SlotDetail();
                    slot = new Slot();
                    slot.setDuration(value);
                    com.mmt.hotels.model.response.dayuse.Slot tempSlot = new com.mmt.hotels.model.response.dayuse.Slot();
                    tempSlot.setDuration(slot.getDuration());
                    tempSlot.setTimeSlot(slotTime);
                    slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(tempSlot));
                    slotDetail.setSlot(slot);
                    slotDetailList.add(slotDetail);
                }
            }
        }
        return slotDetailList;
    }

    private String buildSlotDetailList(SearchWrapperHotelEntity hotelEntity, String expData, ListingSearchRequest searchHotelsRequest, List<SlotDetail> slotDetailList,
                                       Set<Integer> slotDetailCount, RoomTypeDetails roomTypeDetails) {
        Slot slot;
        SlotDetail slotDetail;
        String slotTime;
        slotDetail = new SlotDetail();
        slotDetail.setPriceDetail(buildPriceDetail(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown() != null ? roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown() : null,
                commonResponseTransformer.enableSaveValue(expData),
                commonResponseTransformer.enableDiscount(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null),
                (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().isMetaInfo()),
                hotelEntity.getLowestRateSegmentId(), Collections.emptyList(), null, PN_PRICE_SUFFIX,PRICES_TEXT_DAYUSE));
        slot = new Slot();
        slot.setDuration(roomTypeDetails.getSlot().getDuration());
        slotDetailCount.add(slot.getDuration());
        slotTime = roomTypeDetails.getSlot().getTimeSlot();
        slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(roomTypeDetails.getSlot()));
        slotDetail.setSlot(slot);
        slotDetailList.add(slotDetail);
        return slotTime;
    }

    private List<String> getGroupPriceAndSavingText(SearchWrapperHotelEntity searchWrapperHotelEntity, ListingSearchRequest listingSearchRequest) {
        List<String> groupPriceAndSavingText = new ArrayList<>();
        long displayPrice = (long) ((searchWrapperHotelEntity.getDisplayFare() != null && searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) ? searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getTotalAmount() : 0.0d);
        long savingPerc = (long) ((searchWrapperHotelEntity.getDisplayFare() != null && searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) ? searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getSavingPerc() : 0.0d);
        int roomCount = searchWrapperHotelEntity.getRoomCount();
        String checkIn = null;
        String checkOut = null;
        if (listingSearchRequest != null && listingSearchRequest.getSearchCriteria() != null) {
            checkOut = listingSearchRequest.getSearchCriteria().getCheckOut();
            checkIn = listingSearchRequest.getSearchCriteria().getCheckIn();
        }
        int nightCount = 0;
        if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
            nightCount = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
        }
        if (StringUtils.equalsIgnoreCase(searchWrapperHotelEntity.getStayType(), "ENTIRE") && (searchWrapperHotelEntity.getDisplayFare() != null && searchWrapperHotelEntity.getDisplayFare().getTotalRoomCount() == 1)) {
            NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en", "IN"));
            groupPriceAndSavingText.add(polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT, listingSearchRequest != null ? listingSearchRequest.getClient() : ""))
                    .replace(ConstantsTranslation.AMOUNT.toUpperCase(), numberFormat.format(displayPrice))
                    .replace("{NIGHT_COUNT}", String.valueOf(nightCount))
                    .replace("{ROOM_COUNT}", String.valueOf(roomCount)));
        } else {
            NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en", "IN"));
            groupPriceAndSavingText.add(commonResponseTransformer.getGroupPriceText(roomCount, nightCount, numberFormat.format(displayPrice), listingSearchRequest != null ? listingSearchRequest.getClient() : ""));
        }


        String savingPercText = null;
        if (savingPerc != 0.0 && searchWrapperHotelEntity.isGroupBookingPrice()) {
            savingPercText = polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(SAVING_PERC_TEXT, listingSearchRequest != null ? listingSearchRequest.getClient() : "")).replace("{PERCENTAGE}", String.valueOf(savingPerc));
        }
        groupPriceAndSavingText.add(savingPercText);

        return groupPriceAndSavingText;
    }

    private HotelCard buildHotelTopCard(MyBizSimilarToDirectObj myBizSimilarToDirectObj) {
        if (myBizSimilarToDirectObj == null) {
            return null;
        }
        HotelCard hotelTopCard = new HotelCard();
        hotelTopCard.setHeading(getMyBizDirectHotelDistanceText(myBizSimilarToDirectObj.getDistance()));
        hotelTopCard.setSubHeading(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_SUBHEADING));
        hotelTopCard.setTag(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_TAG));
        if (CollectionUtils.isNotEmpty(myBizSimilarToDirectObj.getAmenities())) {
            List<Facility> amenities = new ArrayList<>();
            myBizSimilarToDirectObj.getAmenities().forEach(myBizAmenity -> amenities.add(getFacility(myBizAmenity)));
            hotelTopCard.setAmenities(amenities);
        }
        return hotelTopCard;
    }

    private Facility getFacility(FeaturedAmenity myBizAmenity) {
        Facility facility = new Facility();
        facility.setIconUrl(myBizAmenity.getIconUrl());
        facility.setName(myBizAmenity.getName());
        return facility;
    }

    private HotelCard buildHotelBottomCard(QuickBookInfo quickBookInfo) {
        if (quickBookInfo != null) {
            return buildQuickBookCard(quickBookInfo);
        }
        return null;
    }

    private PriceDetail buildPriceDetail(DisplayPriceBreakDown displayPriceBreakDown,
                                         boolean enableSaveValue, boolean enableDiscount, boolean metaInfo, String lowestRateSegmentId, List<String> groupPriceAndSavingText, String idContext, String priceSuffix,String taxAndFeeText) {
        if (displayPriceBreakDown == null) {
            return null;
        }
        PriceDetail priceDetail = new PriceDetail();
        if (CollectionUtils.isNotEmpty(groupPriceAndSavingText)) {
            priceDetail.setGroupPriceText(groupPriceAndSavingText.get(0));
            priceDetail.setSavingsText(groupPriceAndSavingText.get(1));
        }
        priceDetail.setCoupon(buildCoupon(displayPriceBreakDown.getCouponInfo()));
        priceDetail.setEmiDetails(buildEmiDetails(displayPriceBreakDown.getEmiDetails()));
        priceDetail.setTotalTax(displayPriceBreakDown.getTotalTax());
        if (enableSaveValue) {
            priceDetail.setTotalSaving(displayPriceBreakDown.getTotalSaving());
            priceDetail.setSavingPerc(displayPriceBreakDown.getSavingPerc());
        }
        priceDetail.setPrice(displayPriceBreakDown.getNonDiscountedPrice() - (displayPriceBreakDown.isTaxIncluded() ? displayPriceBreakDown.getTotalTax() : 0));
        priceDetail.setPriceWithTax(displayPriceBreakDown.getNonDiscountedPrice() + (displayPriceBreakDown.isTaxIncluded() ? 0 : displayPriceBreakDown.getTotalTax()));
        priceDetail.setDiscountedPrice(displayPriceBreakDown.getDisplayPrice() - (displayPriceBreakDown.isTaxIncluded() ? displayPriceBreakDown.getTotalTax() : 0));
        priceDetail.setDiscountedPriceWithTax(displayPriceBreakDown.getDisplayPrice() + (displayPriceBreakDown.isTaxIncluded() ? 0 : displayPriceBreakDown.getTotalTax()));
        priceDetail.setPricingKey(displayPriceBreakDown.getPricingKey());
        /*
         * myPartner change log :
         * 	This value is added in the schema repo.
         * 	We've not versioned this, since there is no changes to existing keys, this is a new addition only used for
         * 	myPartner affilaiteID
         * 	Conditionally it will be set - value > 100 and > 5% of the discounted Price [to be closed on the product side]
         * */
        if (enableDiscount) {
            priceDetail.setMyPartnerDiscount(displayPriceBreakDown.getTotalSaving());
        }

        if (enableDiscount && StringUtils.equalsIgnoreCase(idContext, CORP_ID_CONTEXT)) {
            priceDetail.setTotalDiscount(displayPriceBreakDown.getTotalSaving());
        }

        priceDetail.setPriceSuffix(priceSuffix);
        priceDetail.setTaxesAndFeesText(StringUtils.isNotBlank(taxAndFeeText) ? taxAndFeeText:TAXES_AND_FEES_TEXT);

        if (metaInfo) {
            Map<String, String> metaMap = new HashMap<String, String>();
            metaMap.put(SERVICE_CHARGE_KEY, String.valueOf(displayPriceBreakDown.getHotelServiceCharge()));
            metaMap.put(HOTEL_TAX_KEY, String.valueOf(displayPriceBreakDown.getHotelTax()));
            metaMap.put(SERVICE_FEES_KEY, String.valueOf(displayPriceBreakDown.getMmtServiceCharge()));
            metaMap.put(AFFILIATE_FEES_KEY, String.valueOf(displayPriceBreakDown.getAffiliateFee()));
            metaMap.put(WALLET_KEY, String.valueOf(displayPriceBreakDown.getWallet()));
            metaMap.put(MMT_DISCOUNT_KEY, String.valueOf(displayPriceBreakDown.getMmtDiscount()));
            metaMap.put(BLACK_DISCOUNT_KEY, String.valueOf(displayPriceBreakDown.getBlackDiscount()));
            metaMap.put(CDF_DISCOUNT_KEY, String.valueOf(displayPriceBreakDown.getCdfDiscount()));
            if (displayPriceBreakDown.getOfferDiscountBreakup() != null) {
                List<Map.Entry<PromotionalOfferType, Double>> list = displayPriceBreakDown.getOfferDiscountBreakup().entrySet()
                        .stream()
                        .filter(entry -> entry.getValue() != null && entry.getValue() > 0.0d)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (Map.Entry<PromotionalOfferType, Double> entry : list) {
                        if (entry.getKey() != PromotionalOfferType.MIXED) {
                            PricingDetails pd = new PricingDetails();
                            metaMap.put(entry.getKey().getName(), String.valueOf(entry.getValue()));
                        }
                    }
                }
            }
            metaMap.put(LOWEST_RATE_SEGMENT_KEY, lowestRateSegmentId);
            priceDetail.setMetaInfo(metaMap);

        }

        return priceDetail;
    }

    private EMIDetail buildEmiDetails(Emi emiInfo) {
        if (emiInfo == null) {
            return null;
        }
        EMIDetail emiDetail = new EMIDetail();
        emiDetail.setAmount((double) emiInfo.getEmiAmount());
        emiDetail.setType(emiInfo.getEmiType());
        emiDetail.setBankName(emiInfo.getBankName());
        emiDetail.setTenure(emiInfo.getTenure());
        emiDetail.setTotalCost(emiInfo.getTotalCost());
        emiDetail.setTotalInterest(emiInfo.getTotalInterest());
        return emiDetail;
    }

    private Coupon buildCoupon(BestCoupon couponInfo) {
        if (couponInfo == null) {
            return null;
        }
        Coupon coupon = new Coupon();
        coupon.setDescription(couponInfo.getDescription());
        coupon.setCode(couponInfo.getCouponCode());
        coupon.setType(couponInfo.getType());
        coupon.setCouponAmount(couponInfo.getDiscountAmount());
        coupon.setSpecialPromo(couponInfo.isSpecialPromoCoupon());
        return coupon;
    }

    private SortCriteria buildSortCriteria(com.mmt.hotels.model.request.SortCriteria criteria) {
        if (criteria == null) {
            return null;
        }
        SortCriteria sortCriteria = new SortCriteria(criteria.getField(), criteria.getOrder());
        return sortCriteria;
    }

    private ToolTip buildMyBizAssuredToolTip() {
        ToolTip toolTip = new ToolTip();
        toolTip.setHeading(polyglotService.getTranslatedData("WHY_MYBIZ_ASSURED_TEXT"));
        toolTip.setIconType(myBizToolTipIconType);
        toolTip.setData(new ArrayList<>());
        toolTip.getData().add(polyglotService.getTranslatedData("RATED_HIGH_BT"));
        toolTip.getData().add(polyglotService.getTranslatedData("GST_INVOICE_ASSURANCE_TEXT"));
        toolTip.getData().add(polyglotService.getTranslatedData("BPG_TEXT"));
        return toolTip;
    }

    private List<SectionFeature> getMybizSimilarHotelsFeatures() {
        List<SectionFeature> sectionFeatureList = new ArrayList<>();
        sectionFeatureList.add(new SectionFeature(highRatedUrl, polyglotService.getTranslatedData("RATED_HIGH_BT"), null, null, null));
        sectionFeatureList.add(new SectionFeature(gstInvoiceUrl, polyglotService.getTranslatedData("GST_INVOICE_ASSURANCE_TEXT"), null, null, null));
        sectionFeatureList.add(new SectionFeature(bpgUrl, polyglotService.getTranslatedData("BPG_TEXT"), null, null, null));
        return sectionFeatureList;
    }

    private SoldOutInfoCG buildSoldOutInfo(SoldOutInfo soldOutInfo) {
        if (soldOutInfo == null)
            return null;
        SoldOutInfoCG soldOutInfoCG = new SoldOutInfoCG();
        soldOutInfoCG.setSoldOutText(soldOutInfo.getSoldOutText());
        soldOutInfoCG.setSoldOutSubText(soldOutInfo.getSoldOutSubText());
        soldOutInfoCG.setSoldOutReason(soldOutInfo.getSoldOutReason());
        soldOutInfoCG.setSoldOutType(soldOutInfo.getSoldOutType());
        return soldOutInfoCG;
    }

    public abstract void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi,boolean hcardV2);

    public abstract void addPersuasionHoverData(Hotel hotel, SearchWrapperHotelEntity hotelEntity, CancellationTimeline cancellationTimeline, DisplayFare displayFare);

    public abstract void populateClientSpecificParameters();

    public abstract void addSeoTextPersuasion(Hotel hotel, SearchWrapperHotelEntity hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName);

    public abstract HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo);

    public abstract String getMyBizDirectHotelDistanceText(String distanceText);

    public abstract MyBizStaticCard buildStaticCard(String section, List<SearchWrapperHotelEntity> hotels);

    protected String getSeoPersuasionText(boolean oddHotel, boolean viewOnMap, SearchWrapperHotelEntity hotelEntity) {
        boolean starRating = hotelEntity.getStarRating() != null && hotelEntity.getStarRating() != 0;
        boolean addressLine1 = hotelEntity.getAddress() != null && StringUtils.isNotBlank(hotelEntity.getAddress().getLine1());
        boolean addressLine2 = hotelEntity.getAddress() != null && StringUtils.isNotBlank(hotelEntity.getAddress().getLine2());
        boolean cityName = StringUtils.isNotBlank(hotelEntity.getCityName());
        boolean propertyType = StringUtils.isNotBlank(hotelEntity.getPropertyType());
        boolean userRating = isUserRatingPresent(hotelEntity);
        boolean hotelSummary = isHotelSummaryPresent(hotelEntity);
        boolean mobiusInclusion = CollectionUtils.isNotEmpty(hotelEntity.getMobiusInclusions());
        boolean sanitisedAmenityName = CollectionUtils.isNotEmpty(hotelEntity.getFacilityHighlights());
        float userRatingValue = 0.0f;
        if (userRating) {
            if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode())) {
                userRatingValue = hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("cumulativeRating").floatValue();
            } else {
                userRatingValue = hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("cumulativeRating").floatValue();
            }
        }
        String hotelSummaryValue = "";
        if (hotelSummary) {
            hotelSummaryValue = fetchHotelSummary(hotelEntity);
        }
        String sanitisedAmenitiesValue = "";
        if (sanitisedAmenityName) {
            sanitisedAmenitiesValue = fetchSanitisedAmenities(hotelEntity);
        }

        String persuasionText;
        if (oddHotel) {
            persuasionText = buildOddHotelSeoText(hotelEntity, starRating, addressLine1, addressLine2, cityName, propertyType, userRating, hotelSummary, mobiusInclusion, sanitisedAmenityName, userRatingValue, hotelSummaryValue, sanitisedAmenitiesValue, viewOnMap);
        } else {
            persuasionText = buildEvenHotelSeoText(hotelEntity, starRating, addressLine1, addressLine2, cityName, propertyType, hotelSummary, mobiusInclusion, sanitisedAmenityName, userRatingValue, hotelSummaryValue, sanitisedAmenitiesValue, viewOnMap);
        }
        return persuasionText;
    }

    private String buildEvenHotelSeoText(SearchWrapperHotelEntity hotelEntity, boolean starRating, boolean addressLine1, boolean addressLine2, boolean cityName, boolean propertyType, boolean hotelSummary, boolean mobiusInclusion, boolean sanitisedAmenityName, float userRatingValue, String hotelSummaryValue, String sanitisedAmenitiesValue, boolean viewOnMap) {
        StringBuilder persuasionText = new StringBuilder();
        if (addressLine1 && addressLine2 && propertyType) {
            if (viewOnMap) {
                persuasionText.append(String.format(
                        "<span>Location of the %s is %s <span class=\"blueText\"><b>View On Map</b></span> (%s). </span>",
                        hotelEntity.getPropertyType(), hotelEntity.getAddress().getLine2(),
                        hotelEntity.getAddress().getLine1()));
            } else {
                persuasionText.append(String.format(
                        "<span>Location of the %s is %s (%s). </span>",
                        hotelEntity.getPropertyType(), hotelEntity.getAddress().getLine2(),
                        hotelEntity.getAddress().getLine1()));
            }
        }
        if (starRating && addressLine2 && cityName && propertyType && hotelSummary) {
            persuasionText.append(String.format("<span>It’s a %s Star %s with overall Rating %.1f Out of 5 where %s. </span>",
                    hotelEntity.getStarRating(), hotelEntity.getPropertyType(), userRatingValue,
                    hotelSummaryValue));
        }
        if (sanitisedAmenityName && propertyType) {
            persuasionText.append(String.format("<span>Top Facilities of this %s are %s. </span>",
                    hotelEntity.getPropertyType(), sanitisedAmenitiesValue));
        }
        if (mobiusInclusion) {
            persuasionText.append(String.format("<span>%s. </span>", hotelEntity.getMobiusInclusions()));
        }
        return persuasionText.toString();
    }

    private String buildOddHotelSeoText(SearchWrapperHotelEntity hotelEntity, boolean starRating, boolean addressLine1, boolean addressLine2, boolean cityName, boolean propertyType, boolean userRating, boolean hotelSummary, boolean mobiusInclusion, boolean sanitisedAmenityName, float userRatingValue, String hotelSummaryValue, String sanitisedAmenitiesValue, boolean viewOnMap) {
        StringBuilder persuasionText = new StringBuilder();
        if (starRating && addressLine2 && cityName && propertyType) {
            persuasionText.append(String.format("<span>This %d Star %s in %s is located in %s. </span>",
                    hotelEntity.getStarRating(), hotelEntity.getPropertyType(),
                    hotelEntity.getCityName(), hotelEntity.getAddress().getLine2()));
        }
        if (addressLine1) {
            if (viewOnMap) {
                persuasionText.append(String.format(
                        "<span>Full Address of property is %s <span class=\"blueText\"><b>View On Map</b></span> </span>",
                        hotelEntity.getAddress().getLine1()));
            } else {
                persuasionText.append(String.format(
                        "<span>Location of the %s is %s (%s). </span>",
                        hotelEntity.getPropertyType(), hotelEntity.getAddress().getLine2(),
                        hotelEntity.getAddress().getLine1()));
            }
        }
        if (propertyType && userRating && hotelSummary) {
            persuasionText.append(String.format("<span>This %s have %.1f Out of 5 Rating where %s. </span>",
                    hotelEntity.getPropertyType(), userRatingValue, hotelSummaryValue));
        }
        if (mobiusInclusion) {
            persuasionText.append(String.format("<span>%s. </span>", hotelEntity.getMobiusInclusions()));
        }
        if (sanitisedAmenityName) {
            persuasionText.append(String.format("<span>Key amenities of this property are %s. </span>", sanitisedAmenitiesValue));
        }
        return persuasionText.toString();
    }

    protected String fetchSanitisedAmenities(SearchWrapperHotelEntity hotelEntity) {
        int maxLength = Math.min(hotelEntity.getFacilityHighlights().size(), 3);
        int len = 0;
        StringBuilder sanitizedAminitiesText = new StringBuilder();
        for (String facilityHighlight : hotelEntity.getFacilityHighlights()) {
            if (len == maxLength) {
                break;
            }
            sanitizedAminitiesText.append(facilityHighlight);
            if (len != maxLength - 1) {
                sanitizedAminitiesText.append(" & ");
            }
            len++;
        }
        return sanitizedAminitiesText.toString();
    }

    protected String fetchHotelSummary(SearchWrapperHotelEntity hotelEntity) {
        JsonNode travellerRatingSummary = null;
        if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode())) {
            travellerRatingSummary = hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("travellerRatingSummary");
        } else {
            travellerRatingSummary = hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("travellerRatingSummary");
        }
        TravellerRatingSummaryDTO summaryDTO = objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary,
                new TypeReference<TravellerRatingSummaryDTO>() {
                });
        if (summaryDTO == null || CollectionUtils.isEmpty(summaryDTO.getHotelSummary())) {
            return "";
        }
        StringBuilder hotelSummaryText = new StringBuilder();
        int maxLength = Math.min(summaryDTO.getHotelSummary().size(), 3);
        int len = 0;
        for (ConceptSummaryDTO conceptSummaryDTO : summaryDTO.getHotelSummary()) {
            if (len == maxLength) {
                break;
            }
            hotelSummaryText.append(
                    String.format("<span> %.1f/5 for %s", conceptSummaryDTO.getValue(), conceptSummaryDTO.getConcept()));
            if (len != maxLength - 1) {
                hotelSummaryText.append(" &");
            }
            hotelSummaryText.append(" </span>");
            len++;
        }
        return hotelSummaryText.toString();
    }

    protected boolean isHotelSummaryPresent(SearchWrapperHotelEntity hotelEntity) {
        if (hotelEntity.getFlyfishReviewSummary() == null) {
            return false;
        }
        if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
                (hotelEntity.getFlyfishReviewSummary().get(OTA.MMT) == null ||
                        hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("travellerRatingSummary") == null ||
                        hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("travellerRatingSummary")
                                .get("hotelSummary") == null)) {
            return false;
        }
        if (!"IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
                (hotelEntity.getFlyfishReviewSummary().get(OTA.TA) == null ||
                        hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("travellerRatingSummary") == null ||
                        hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("travellerRatingSummary").get("hotelSummary") == null)) {
            return false;
        }
        return true;
    }

    protected boolean isUserRatingPresent(SearchWrapperHotelEntity hotelEntity) {
        if (hotelEntity.getFlyfishReviewSummary() == null) {
            return false;
        }
        if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
                (hotelEntity.getFlyfishReviewSummary().get(OTA.MMT) == null ||
                        hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("cumulativeRating") == null ||
                        Float.compare(hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("cumulativeRating").floatValue(),
                                0.0f) == 0)) {
            return false;
        }
        if (!"IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
                (hotelEntity.getFlyfishReviewSummary().get(OTA.TA) == null ||
                        hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("cumulativeRating") == null ||
                        Float.compare(hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("cumulativeRating").floatValue(),
                                0.0f) == 0)) {
            return false;
        }
        return true;
    }

    private CalendarCriteria buildCalendarCriteria(com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria calendarCriteriaHES) {
        if (calendarCriteriaHES == null)
            return null;
        CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
        calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getAdvanceDays());
        calendarCriteriaCG.setAvailable(calendarCriteriaHES.isAvailable());
        calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getMaxDate());
        calendarCriteriaCG.setMlos(calendarCriteriaHES.getMlos());
        return calendarCriteriaCG;
    }

    private NearMeFilterPill getNearMeFilterPill(ListingPagePersonalizationResponsBO listingPageResponseBO,
                                                 SearchHotelsRequest searchHotelsRequest) {
        // Near me filter pill to be shown in two cases:
        // 1. When user city is same as the search city.
        // 2. When user has explicitly searched "Properties Near Me".
        // In case user has explicitly searched "Properties Near Me", then filter pill to be shown in selected state.

        String searchedCity = searchHotelsRequest.getSearchCriteria() != null ?
                searchHotelsRequest.getSearchCriteria().getCityCode() : EMPTY_STRING;
        boolean userSearchedNearMe = searchHotelsRequest.getMatchMakerDetails() != null
                && CollectionUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getLatLng())
                && Boolean.TRUE.equals(searchHotelsRequest.getMatchMakerDetails().getLatLng().get(0).getGiNearBySearch());
        boolean userCitySameAsSearchedCity = listingPageResponseBO.getUserLocation() != null
                && StringUtils.isNotBlank(listingPageResponseBO.getUserLocation().getCity())
                && listingPageResponseBO.getUserLocation().getCity().equalsIgnoreCase(searchedCity);
        if (userSearchedNearMe || userCitySameAsSearchedCity) {
            NearMeFilterPill nearMeFilterPill = new NearMeFilterPill();
            nearMeFilterPill.setNearMePillName(polyglotService.getTranslatedData(NEAR_ME_PILL_TITLE));
            nearMeFilterPill.setSelected(userSearchedNearMe);
            return nearMeFilterPill;
        }
        return null;
    }
}
