package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.BNPLDetails;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.enums.BNPLVariant;

public class HCouponApplicableTextCreator {


    public String getText(BNPLDetails bnplDetails, boolean isGiftCardAllowed, boolean bnplAllowed, PolyglotService polyglotService) {
        BNPLVariant bnplVariant = getBnplVariantEnum(bnplDetails);
        return getCouponApplicableText(bnplVariant, isGiftCardAllowed, bnplAllowed, polyglotService);
    }


    private BNPLVariant getBnplVariantEnum(BNPLDetails bnplDetails) {
        String bnplVariant = "";
        if (bnplDetails != null) {
            bnplVariant = bnplDetails.getBnplVariant();
        }
        if (bnplVariant == null) {
            bnplVariant = "";
        }
        if (bnplVariant.equalsIgnoreCase(BNPLVariant.BNPL_AT_0.name())) {
            return BNPLVariant.BNPL_AT_0;
        } else if (bnplVariant.equalsIgnoreCase(BNPLVariant.BNPL_AT_1.name())) {
            return BNPLVariant.BNPL_AT_1;
        }
        return BNPLVariant.BNPL_NOT_APPLICABLE;
    }

    private String getCouponApplicableText(BNPLVariant bnplVariant, boolean isGiftCardAllowed, boolean bnplAllowed, PolyglotService polyglotService) {
        if (bnplVariant == BNPLVariant.BNPL_AT_0) {
            if (!bnplAllowed && !isGiftCardAllowed) {
                return polyglotService.getSafeTranslatedData(ConstantsTranslation.COUPON_NA_BNPL0_GIFT_CARD);
            } else if (!bnplAllowed) {
                return polyglotService.getSafeTranslatedData(ConstantsTranslation.COUPON_NA_BNPL0);
            } else if (!isGiftCardAllowed) {
                return polyglotService.getSafeTranslatedData(ConstantsTranslation.COUPON_NA_GIFT_CARD);
            } else {
                return "";
            }
        } else if (bnplVariant == BNPLVariant.BNPL_AT_1) {
            if (!bnplAllowed && !isGiftCardAllowed) {
                return polyglotService.getSafeTranslatedData(ConstantsTranslation.COUPON_NA_BNPL1_GIFT_CARD);
            } else if (!bnplAllowed) {
                return polyglotService.getSafeTranslatedData(ConstantsTranslation.COUPON_NA_BNPL1);
            } else if (!isGiftCardAllowed) {
                return polyglotService.getSafeTranslatedData(ConstantsTranslation.COUPON_NA_GIFT_CARD);
            } else {
                return "";
            }
        } else {
            if (isGiftCardAllowed) {
                return "";
            } else {
                return polyglotService.getSafeTranslatedData(ConstantsTranslation.COUPON_NA_GIFT_CARD);
            }
        }
    }

}
