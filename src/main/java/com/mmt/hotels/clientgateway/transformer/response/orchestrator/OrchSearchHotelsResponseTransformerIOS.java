package com.mmt.hotels.clientgateway.transformer.response.orchestrator;


import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.CancellationPolicy;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PriceDetail;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.transformer.response.ios.SearchHotelsResponseTransformerIOS.IMAGE_TEXT_H1;

@Component
public class OrchSearchHotelsResponseTransformerIOS extends OrchSearchHotelsResponseTransformer {

    @Autowired
    PersuasionUtil persuasionUtil;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformerIOS.class);

    @Override
    protected BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse) {
        return null;
    }

    @Override
    public void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity) {
        hotel.setLovedByIndians(persuasionUtil.checkIfIndianessPersuasionExists(hotel.getHotelPersuasions()));
    }

    @Override
    public void addSeoTextPersuasion(Hotel hotel, HotelDetails hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName) {

    }

    @Override
    public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities,
                                                        ListingSearchRequest searchHotelsRequest, boolean enableAmenities,
                                                        boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText,
                                                        TransportPoi nearestGroundTransportPoi, String drivingTimeText,
                                                        LocationDetails locusData, boolean hcardV2) {
        if(CollectionUtils.isNotEmpty(locationPersuasion)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject locPers = new PersuasionObject();
            locPers.setData(new ArrayList<>());
            locPers.setTemplate(IMAGE_TEXT_H);
            locPers.setPlaceholder("SINGLE");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setHasAction(false);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");
            if(hcardV2){
                PersuasionStyle persuasionStyle = new PersuasionStyle();
                persuasionStyle.setTextColor(LOCATION_PER_COLOUR_CODE);
                locPersuasionData.setStyle(persuasionStyle);
            }

            locPers.getData().add(locPersuasionData);
            if(locationPersuasion.size() == 1 ) {
                locPersuasionData.setText(locationPersuasion.get(0));
            }else if (locationPersuasion.size() >= 2){
                locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
                //For Secondary Location Persuasion, if it is present, add it in the Location Persuasion
                if (locationPersuasion.size() > 2)
                    locPersuasionData.setText(locPersuasionData.getText() + " | " + locationPersuasion.get(2));
            }

            try {
                if(hcardV2)
                    ((Map<Object,Object>) hotel.getHotelPersuasions()).put(LOCATION_PERSUAION_PLACEHOLDER_ID_APP_CARDV2,locPers);
                else
                    ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP,locPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }

        }

        if(CollectionUtils.isNotEmpty(facilities) && !StringUtils.equals(hotel.getViewType(), ONE_CLICK) && enableAmenities) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId("AMENITIES");
            amenPersuasionData.setPersuasionType("AMENITIES");
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            StringBuilder text = new StringBuilder();
            int index = 1;
            Iterator<String> iter = facilities.iterator();
            while(iter.hasNext() && index <=3){

                text.append( iter.next());
                if(index < 3)
                    text.append(" | ");
                index++;
            }
            amenPersuasionData.setText(text.toString());
            amenityPers.getData().add(amenPersuasionData);


            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }
        }

        if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && StringUtils.isNotEmpty(dayUsePersuasionsText)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H1);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId(DAYUSE_LOCAL_ID);
            amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            amenPersuasionData.setText(dayUsePersuasionsText);
            amenPersuasionData.setIcontype("b_dot");
            amenityPers.getData().add(amenPersuasionData);
            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }
        }
    }

    @Override
    protected String buildBGColor(String section, String orientation, String cardType) {
        return "";
    }

    @Override
    protected void addBookingConfirmationPersuasion(HotelDetails hotelEntity) {

    }

    @Override
    public MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels) {
        return null;
    }
}
