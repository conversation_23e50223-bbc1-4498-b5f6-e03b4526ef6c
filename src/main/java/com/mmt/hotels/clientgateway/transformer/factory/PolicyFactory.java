package com.mmt.hotels.clientgateway.transformer.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.response.PoliciesResponseTransformer;

@Component
public class PolicyFactory {

	@Autowired
	private PoliciesResponseTransformer policiesResponseTransformer;

	public PoliciesResponseTransformer getResponseService(String client) {
		return policiesResponseTransformer;
	}
}
