package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData;
import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.PersonalizationCards;
import com.mmt.hotels.clientgateway.response.moblanding.Section;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CardDataResponseHelper {


    public com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse buildListPersonalizationResponse(PersonalizationCards personalizationCards) {
        if (personalizationCards == null || CollectionUtils.isEmpty(personalizationCards.getCardData())) {
            return null;
        }

        com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse listPersonalizationResponseCG =
                new com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse();

        // === TOP-LEVEL MAPPINGS - Based on ExperienceCardBuilder fields ===
        listPersonalizationResponseCG.setExperimentId(personalizationCards.getExperimentId());
        listPersonalizationResponseCG.setTrackText(personalizationCards.getTrackText());

        // === CARD DATA MAPPING ===
        if (personalizationCards.getCardData() != null && !personalizationCards.getCardData().isEmpty()) {
            List<com.mmt.hotels.clientgateway.response.moblanding.CardData> cardDataList = new ArrayList<>();
            int sequence = 1; // Start sequence from 1

            for (CardData sourceCardData : personalizationCards.getCardData()) {
                com.mmt.hotels.clientgateway.response.moblanding.CardData targetCardData = mapCardDataFromOrchestrator(sourceCardData, sequence);
                if (targetCardData != null) {
                    cardDataList.add(targetCardData);
                    sequence++;
                }
            }
            listPersonalizationResponseCG.setCardData(cardDataList);
        } else {
            // Set card data to null when input is empty (as expected by tests)
            listPersonalizationResponseCG.setCardData(null);
        }

        // === META MAPPING ===
        com.mmt.hotels.clientgateway.response.moblanding.Meta meta = new com.mmt.hotels.clientgateway.response.moblanding.Meta();
        com.mmt.hotels.clientgateway.response.moblanding.SavedCardTracking savedCardTracking =
                new com.mmt.hotels.clientgateway.response.moblanding.SavedCardTracking();

        meta.setSavedCardTracking(savedCardTracking);
        listPersonalizationResponseCG.setMeta(meta);

        return listPersonalizationResponseCG;
    }


    /**
     * Maps CardData from orchestrator schema to client gateway schema
     * All 12 distinct fields from ExperienceCardBuilder are mapped here
     */
    private com.mmt.hotels.clientgateway.response.moblanding.CardData mapCardDataFromOrchestrator(CardData sourceCardData, int sequence) {
        if (sourceCardData == null) {
            return null;
        }

        com.mmt.hotels.clientgateway.response.moblanding.CardData targetCardData = new com.mmt.hotels.clientgateway.response.moblanding.CardData();

        // Set sequence based on position in list
        targetCardData.setSequence(sequence);

        // Map cardInfo using proper schema classes
        targetCardData.setCardInfo(mapCardInfoFromOrchestrator(sourceCardData));

        return targetCardData;
    }

    /**
     * Maps CardInfo from orchestrator CardData to client gateway CardInfo
     * Maps all 12 distinct fields identified from ExperienceCardBuilder
     */
    private com.mmt.hotels.clientgateway.response.moblanding.CardInfo mapCardInfoFromOrchestrator(CardData sourceCardData) {
        if (sourceCardData == null) {
            return null;
        }

        com.mmt.hotels.clientgateway.response.moblanding.CardInfo cardInfo = new com.mmt.hotels.clientgateway.response.moblanding.CardInfo();

        // === CORE FIELDS (1-4) from ExperienceCardBuilder ===
        cardInfo.setId(sourceCardData.getCardId());
        cardInfo.setSubType(sourceCardData.getCardSubType());
        cardInfo.setTitleText(sourceCardData.getTitleText());                    // Field 1: titleText
        cardInfo.setTemplateId(sourceCardData.getTemplateId());                  // Field 2: templateId
        cardInfo.setTemplateType(sourceCardData.getTemplateType());
        cardInfo.setPageContext(sourceCardData.getPageContext());
        cardInfo.setIconURL(sourceCardData.getIconUrl());                        // Field 3: iconUrl
        // Field 4: removeCard - stored in extraData since CardInfo doesn't have this field

        // === COMPLEX NESTED FIELDS (5-12) ===
        // Field 5-8: cardPayload and its nested genericCardData
        if (sourceCardData.getCardPayload() != null) {
            cardInfo.setCardPayload(mapCardPayloadData(sourceCardData.getCardPayload()));
        }

        // Field 9-10: data (nested list structure) - handled within cardPayload mapping
        // Field 11-12: cardAction.title and other cardAction fields
        if (sourceCardData.getCardAction() != null && !sourceCardData.getCardAction().isEmpty()) {
            cardInfo.setCardAction(mapCardActionList(sourceCardData.getCardAction()));
        }

        // === ADDITIONAL ORCHESTRATOR FIELDS ===
        // Map fields that exist in both schemas
        cardInfo.setHasAction(sourceCardData.isHasAction());
        cardInfo.setClaimed(sourceCardData.isHasClaimed());
        cardInfo.setSubText(sourceCardData.getSubText());
        cardInfo.setActionText(sourceCardData.getActionText());
        cardInfo.setHeading(sourceCardData.getHeading());
        cardInfo.setDescription(sourceCardData.getDesc());                      // desc -> description
        cardInfo.setBgImageURL(sourceCardData.getBgImageUrl());                 // bgImageUrl -> bgImageURL
        cardInfo.setBgColor(sourceCardData.getBgColor());
        cardInfo.setTextColor(sourceCardData.getTextColor());
        cardInfo.setBgGradient(sourceCardData.getBgGradient());
        cardInfo.setBorderColor(sourceCardData.getBorderColor());
        cardInfo.setMinItemsToShow(sourceCardData.getMinItemsToShow() > 0 ? sourceCardData.getMinItemsToShow() : null);
        cardInfo.setDealType(sourceCardData.getDealType());
        cardInfo.setHeaderUrl(sourceCardData.getHeaderUrl());
        cardInfo.setCouponCode(sourceCardData.getCouponCode());
        cardInfo.setTitleTextColor(sourceCardData.getTitleTextColor());
        cardInfo.setImageList(sourceCardData.getImageList());

        // Store fields not available in target schema in extraData
        if (cardInfo.getExtraData() == null) {
            cardInfo.setExtraData(new java.util.HashMap<>());
        }
        cardInfo.getExtraData().put("removeCard", String.valueOf(sourceCardData.isRemoveCard()));
        cardInfo.getExtraData().put("hasToggle", String.valueOf(sourceCardData.isHasToggle()));
        cardInfo.getExtraData().put("hasLocation", String.valueOf(sourceCardData.isHasLocation()));
        cardInfo.getExtraData().put("cardPosition", String.valueOf(sourceCardData.getCardPosition()));

        // === COMPLEX OBJECT MAPPINGS ===
        if (sourceCardData.getBgLinearGradient() != null) {
            cardInfo.setBgLinearGradient(mapBGLinearGradient(sourceCardData.getBgLinearGradient()));
        }

        if (sourceCardData.getBorderGradient() != null) {
            cardInfo.setBorderGradient(mapBorderGradient(sourceCardData.getBorderGradient()));
        }

        if (sourceCardData.getIconTag() != null) {
            cardInfo.setIconTags(mapIconTag(sourceCardData.getIconTag()));
        }

        if (sourceCardData.getToggleAction() != null) {
            cardInfo.setToggleAction(mapToggleAction(sourceCardData.getToggleAction()));
        }

        if (sourceCardData.getCardSheet() != null) {
            cardInfo.setCardSheet(mapCardSheet(sourceCardData.getCardSheet()));
        }

        if (sourceCardData.getFloatingSheetData() != null) {
            cardInfo.setFloatingSheetData(mapFloatingSheetData(sourceCardData.getFloatingSheetData()));
        }

        if (sourceCardData.getRushDealTimerInfo() != null) {
            cardInfo.setRushDealTimerInfo(mapRushDealTimerInfo(sourceCardData.getRushDealTimerInfo()));
        }

        if (sourceCardData.getCardCondition() != null) {
            cardInfo.setCardCondition(mapCardCondition(sourceCardData.getCardCondition()));
        }

        return cardInfo;
    }


    // ========== HELPER MAPPING METHODS ==========

    /**
     * Maps BGLinearGradient from orchestrator to client gateway
     */
    private com.mmt.hotels.pojo.listing.personalization.BGLinearGradient mapBGLinearGradient(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.BGLinearGradient source) {
        if (source == null) return null;

        com.mmt.hotels.pojo.listing.personalization.BGLinearGradient target =
                new com.mmt.hotels.pojo.listing.personalization.BGLinearGradient();

        // Map color fields
        target.setStart(source.getStartColor());
        target.setEnd(source.getEndColor());
        target.setDirection(source.getDirection());

        // Handle angle - convert direction to angle if needed
        if (source.getDirection() != null) {
            target.setAngle(source.getDirection());
        }

        return target;
    }

    /**
     * Maps BorderGradient from orchestrator to client gateway
     */
    private com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient mapBorderGradient(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.BorderGradient source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient target =
                new com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient();

        // Map color fields
        target.setStart(source.getStartColor());
        target.setEnd(source.getEndColor());
        target.setDirection(source.getDirection());

        // Map colors list if available
        if (source.getColors() != null) {
            target.setColor(source.getColors());
        }

        return target;
    }

    /**
     * Maps IconTag from orchestrator to client gateway
     */
    private com.mmt.hotels.pojo.listing.personalization.IconTag mapIconTag(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.IconTag source) {
        if (source == null) return null;

        com.mmt.hotels.pojo.listing.personalization.IconTag target =
                new com.mmt.hotels.pojo.listing.personalization.IconTag();

        // Map text field
        target.setText(source.getText());

        // Map border color
        target.setBorderColor(source.getTextColor());

        // Create BgGradient if background color is available
        if (source.getBackgroundColor() != null) {
            com.mmt.hotels.model.persuasion.response.BgGradient bgGradient =
                    new com.mmt.hotels.model.persuasion.response.BgGradient();
            bgGradient.setStart(source.getBackgroundColor());
            bgGradient.setEnd(source.getBackgroundColor()); // Use same color for solid background
            target.setBgGradient(bgGradient);
        }

        return target;
    }

    /**
     * Maps Filters from orchestrator to client gateway
     */
    private com.mmt.hotels.model.response.listpersonalization.Filters mapFilters(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.Filters source) {
        if (source == null) return null;

        com.mmt.hotels.model.response.listpersonalization.Filters target =
                new com.mmt.hotels.model.response.listpersonalization.Filters();

        // Note: Filter mapping between orchestrator and client gateway schemas
        // would require complex type conversion. Skipping for now as this is
        // an optional field and may not be required for current use case.

        return target;
    }

    /**
     * Maps CardActionData from orchestrator to client gateway
     */
    private com.mmt.hotels.clientgateway.response.moblanding.CardActionData mapCardActionData(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardActionData source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.moblanding.CardActionData target =
                new com.mmt.hotels.clientgateway.response.moblanding.CardActionData();

        target.setTitle(source.getTitle());

        // Map sections if available
        if (source.getSections() != null && !source.getSections().isEmpty()) {
            List<Section> sections = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardActionData.Section srcSection : source.getSections()) {
                com.mmt.hotels.clientgateway.response.moblanding.Section targetSection =
                        new com.mmt.hotels.clientgateway.response.moblanding.Section();
                targetSection.setTitle(srcSection.getTitle());

                if (srcSection.getItems() != null && !srcSection.getItems().isEmpty()) {
                    List<com.mmt.hotels.clientgateway.response.moblanding.Item> items = new ArrayList<>();
                    for (com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardActionData.Section.Item srcItem : srcSection.getItems()) {
                        com.mmt.hotels.clientgateway.response.moblanding.Item targetItem =
                                new com.mmt.hotels.clientgateway.response.moblanding.Item();
                        targetItem.setText(srcItem.getText());
                        targetItem.setTextBoxTitle(srcItem.getTextBoxTitle());
                        // Note: iconUrl, value, and attributes are not available in target Item schema
                        items.add(targetItem);
                    }
                    targetSection.setItems(items);
                }
                sections.add(targetSection);
            }
            target.setSections(sections);
        }

        return target;
    }

    /**
     * Maps ToggleAction from orchestrator to client gateway
     */
    private com.mmt.hotels.model.enums.ToggleAction mapToggleAction(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.ToggleAction source) {
        if (source == null) return null;

        // Map enum values
        switch (source) {
            case ON:
                return com.mmt.hotels.model.enums.ToggleAction.ON;
            case OFF:
                return com.mmt.hotels.model.enums.ToggleAction.OFF;
            default:
                return null;
        }
    }

    /**
     * Maps CardAction list from orchestrator to client gateway
     */
    private List<com.mmt.hotels.clientgateway.response.moblanding.CardAction> mapCardActionList(
            List<com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardAction> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) return null;

        List<com.mmt.hotels.clientgateway.response.moblanding.CardAction> targetList = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardAction source : sourceList) {
            com.mmt.hotels.clientgateway.response.moblanding.CardAction target = mapCardAction(source);
            if (target != null) {
                targetList.add(target);
            }
        }
        return targetList.isEmpty() ? null : targetList;
    }

    /**
     * Maps single CardAction from orchestrator to client gateway
     */
    private com.mmt.hotels.clientgateway.response.moblanding.CardAction mapCardAction(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardAction source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.moblanding.CardAction target =
                new com.mmt.hotels.clientgateway.response.moblanding.CardAction();

        // Map basic fields
        target.setTitle(source.getTitle());
        target.setWebViewUrl(source.getWebViewUrl());
        target.setDeeplinkUrl(source.getDeeplinkUrl());
        target.setIsLogin(source.getIsLogin());
        target.setCategories(source.getCategories());

        // Map filters if available
        if (source.getFilters() != null) {
            target.setFilters(mapFilters(source.getFilters()));
        }

        // Map action if available
        if (source.getAction() != null) {
            com.mmt.hotels.clientgateway.response.moblanding.CardAction.MoreInfoAction moreInfoAction =
                    new com.mmt.hotels.clientgateway.response.moblanding.CardAction.MoreInfoAction();
            moreInfoAction.setTitle(source.getAction().getTitle());
            moreInfoAction.setActionProp(source.getAction().getActionProp());
            target.setAction(moreInfoAction);
        }

        // Map data if available
        if (source.getData() != null) {
            target.setData(mapCardActionData(source.getData()));
        }

        return target;
    }

    /**
     * Maps CardPayloadResponse from orchestrator to client gateway CardPayloadData
     */
    private com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData mapCardPayloadData(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardPayloadResponse source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData target =
                new com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData();

        // Map genericCardData if available
        if (source.getGenericCardData() != null && !source.getGenericCardData().isEmpty()) {
            List<com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG> genericCardDataList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.GenericCardPayloadData srcData : source.getGenericCardData()) {
                com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG targetData =
                        new com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG();

                targetData.setId(srcData.getId());
                targetData.setText(srcData.getText());
                targetData.setTag(srcData.getTag());
                targetData.setTitleText(srcData.getTitleText());
                targetData.setSubText(srcData.getSubText());
                targetData.setImageUrl(srcData.getImageUrl());
                targetData.setActionUrl(srcData.getActionUrl());
                targetData.setIconUrl(srcData.getIconUrl());
                targetData.setGalleryView(srcData.isGalleryView() ? srcData.isGalleryView() : null);
                targetData.setItemIconType(srcData.getItemIconType());
                // Note: type, description, and metadata are not available in target schema

                // Handle nested data recursively if available
                if (srcData.getData() != null && !srcData.getData().isEmpty()) {
                    List<com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG> nestedDataList = new ArrayList<>();
                    for (com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.GenericCardPayloadData nestedSrc : srcData.getData()) {
                        com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG nestedTarget =
                                new com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG();
                        nestedTarget.setId(nestedSrc.getId());
                        nestedTarget.setText(nestedSrc.getText());
                        nestedTarget.setTag(nestedSrc.getTag());
                        nestedTarget.setTitleText(nestedSrc.getTitleText());
                        nestedTarget.setSubText(nestedSrc.getSubText());
                        nestedTarget.setImageUrl(nestedSrc.getImageUrl());
                        nestedTarget.setActionUrl(nestedSrc.getActionUrl());
                        nestedTarget.setIconUrl(nestedSrc.getIconUrl());
                        nestedTarget.setItemIconType(nestedSrc.getItemIconType());
                        // Note: type, description, and metadata fields not available in target schema
                        nestedDataList.add(nestedTarget);
                    }
                    targetData.setData(nestedDataList);
                }

                genericCardDataList.add(targetData);
            }
            target.setGenericCardData(genericCardDataList);
        }

        return target;
    }

    /**
     * Maps CardSheet from orchestrator to client gateway
     */
    private com.mmt.hotels.clientgateway.response.moblanding.CardSheet mapCardSheet(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardSheet source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.moblanding.CardSheet target =
                new com.mmt.hotels.clientgateway.response.moblanding.CardSheet();

        // Map top sheet from title and content
        if (source.getTitle() != null || source.getContent() != null) {
            com.mmt.hotels.clientgateway.response.moblanding.CardSheetElem topSheet =
                    new com.mmt.hotels.clientgateway.response.moblanding.CardSheetElem();
            topSheet.setText(source.getTitle());
            topSheet.setSubText(source.getContent());
            target.setTopSheet(topSheet);
        }

        // Map bottom sheet from items if available
        if (source.getItems() != null && !source.getItems().isEmpty()) {
            com.mmt.hotels.clientgateway.response.moblanding.CardSheetElem bottomSheet =
                    new com.mmt.hotels.clientgateway.response.moblanding.CardSheetElem();

            // Convert items to infoList
            List<com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG> infoList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardSheet.SheetItem item : source.getItems()) {
                com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG info =
                        new com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG();
                info.setTitleText(item.getTitle());
                info.setSubText(item.getDescription());
                info.setIconUrl(item.getIconUrl());
                info.setActionUrl(item.getActionUrl());
                infoList.add(info);
            }
            bottomSheet.setInfoList(infoList);
            target.setBottomSheet(bottomSheet);
        }

        return target;
    }

    /**
     * Maps FloatingSheetData from orchestrator to client gateway
     */
    public com.mmt.hotels.clientgateway.response.moblanding.FloatingSheetData mapFloatingSheetData(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.FloatingSheetData source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.moblanding.FloatingSheetData target =
                new com.mmt.hotels.clientgateway.response.moblanding.FloatingSheetData();

        // Map basic fields
        target.setText(source.getTitle());
        target.setCurrentTimeStamp(String.valueOf(System.currentTimeMillis()));
        target.setFlotingActionName(source.getActionText());

        // Map action URL
        if (source.getActionUrl() != null) {
            // Store action URL in flotingActionName since that's the closest field
            target.setFlotingActionName(source.getActionText() != null ? source.getActionText() : source.getActionUrl());
        }

        // Set dismissible flag - store in extraData if needed
        // Note: dismissible field not available in target schema

        return target;
    }

    /**
     * Maps RushDealTimerInfo from orchestrator to client gateway
     */
    public com.mmt.hotels.clientgateway.response.moblanding.RushDealTimerInfo mapRushDealTimerInfo(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.RushDealTimerInfo source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.moblanding.RushDealTimerInfo target =
                new com.mmt.hotels.clientgateway.response.moblanding.RushDealTimerInfo();

        // Map urgency text to desc
        target.setDesc(source.getUrgencyText());

        // Map end time to validity timestamp
        if (source.getEndTime() > 0) {
            target.setValidityTimestamp(String.valueOf(source.getEndTime()));
        }

        // Note: bgGradient field exists in target but no equivalent in source
        // showTimer, timerText, timerFormat fields from source not available in target

        return target;
    }

    /**
     * Maps CardCondition from orchestrator to client gateway
     */
    public com.mmt.hotels.clientgateway.response.moblanding.CardCondition mapCardCondition(
            com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData.CardCondition source) {
        if (source == null) return null;

        com.mmt.hotels.clientgateway.response.moblanding.CardCondition target =
                new com.mmt.hotels.clientgateway.response.moblanding.CardCondition();

        // Map checkIfFilterNotApplied if available
        if (source.getCheckIfFilterNotApplied() != null) {
            com.mmt.hotels.clientgateway.request.Filter filter = new com.mmt.hotels.clientgateway.request.Filter();

            // Convert FilterGroup enum from orchestrator to client gateway
            String filterGroupName = source.getCheckIfFilterNotApplied().getFilterGroup().name();
            com.mmt.hotels.clientgateway.response.filter.FilterGroup cgFilterGroup =
                    com.mmt.hotels.clientgateway.response.filter.FilterGroup.getFilterGroupFromFilterName(filterGroupName);
            if (cgFilterGroup != null) {
                filter.setFilterGroup(cgFilterGroup);
            }

            filter.setFilterValue(source.getCheckIfFilterNotApplied().getFilterValue());
            target.setCheckIfFilterNotApplied(filter);
        }

        // Note: Other fields from source (additionalConditions) don't have direct mappings
        // Target has many boolean fields (shouldBeLoggedInUser, etc.) not available in source

        return target;
    }
}
