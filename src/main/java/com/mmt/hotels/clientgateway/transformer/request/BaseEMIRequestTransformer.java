package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import org.apache.commons.lang3.StringUtils;

public abstract class BaseEMIRequestTransformer {
    public void buildDeviceDetails(UpdateEmiDetailRequest updateEmiDetailRequest,
                                   DeviceDetails deviceDetails) {
        updateEmiDetailRequest.setAppVersion(deviceDetails.getAppVersion());
        updateEmiDetailRequest.setBookingDevice(deviceDetails.getBookingDevice());
        updateEmiDetailRequest.setDeviceId(deviceDetails.getDeviceId());
    }

    public void populateSearchCriteria(UpdateEmiDetailRequest updateEmiDetailRequest, SearchCriteria searchCriteria) {
        updateEmiDetailRequest.setCheckin(searchCriteria.getCheckIn());
        updateEmiDetailRequest.setCheckout(searchCriteria.getCheckOut());
        updateEmiDetailRequest.setCityCode(StringUtils.isNotBlank(searchCriteria.getCityCode()) ? searchCriteria.getCityCode() : searchCriteria.getLocationId());
        updateEmiDetailRequest.setCountryCode(searchCriteria.getCountryCode());
        updateEmiDetailRequest.setCurrencyCode(searchCriteria.getCurrency() != null ? searchCriteria.getCurrency().toUpperCase() : null);
    }
}
