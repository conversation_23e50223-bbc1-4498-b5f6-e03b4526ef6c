package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.rooms.AddOnDetails;
import com.mmt.hotels.clientgateway.response.rooms.PolicyDetails;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.addon.AddOnNode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Helper class for transforming OrchV2 AddOns to CG response format.
 * Handles both addOnDetails (FlexiCancelDetails) and addOnPolicies (FlexiCancelAddOn) transformations.
 */
@Component
public class AddOnHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddOnHelper.class);

    @Autowired
    private PolyglotService polyglotService;

    /**
     * Transform OrchV2 AddOns to CG response format.
     * Handles both addOnDetails and addOnPolicies parts of the combined OrchV2 AddOns structure.
     */
    public Map<String, AddOnDetails> transformAddOns(com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOns addOns) {
        if (addOns == null) {
            return new HashMap<>();
        }

        Map<String, AddOnDetails> combinedAddOnDetailsMap = new HashMap<>();

        // Transform addOnDetails (FlexiCancelDetails part) - primary use case
        Map<String, AddOnDetails> addOnDetailsMap = transformAddOnDetails(addOns.getAddOnDetails());
        if (addOnDetailsMap != null) {
            combinedAddOnDetailsMap.putAll(addOnDetailsMap);
        }

        // Transform addOnPolicies (FlexiCancelAddOn part) - additional addOns
        Map<String, AddOnDetails> addOnPoliciesMap = transformAddOnPolicies(addOns.getAddOnPolicies());
        if (addOnPoliciesMap != null) {
            combinedAddOnDetailsMap.putAll(addOnPoliciesMap);
        }

        return combinedAddOnDetailsMap;
    }

    /**
     * Transform OrchV2 addOnDetails (equivalent to legacy FlexiCancelDetails) to CG response format.
     * Following the same pattern as legacy buildAddOnDetails method.
     */
    private Map<String, AddOnDetails> transformAddOnDetails(List<com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOnDetails> orchAddOnDetails) {
        if (CollectionUtils.isEmpty(orchAddOnDetails)) {
            return new HashMap<>();
        }

        Map<String, AddOnDetails> addOnDetailsMap = new HashMap<>();

        // Find FlexiCancel AddOnDetails (matching legacy logic that checks for "FLEXI_CANCEL")
        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOnDetails orchDetail : orchAddOnDetails) {
            if (orchDetail != null && "FLEXI_CANCEL".equals(orchDetail.getType())) {

                // Create CG AddOnDetails and copy properties from OrchV2
                AddOnDetails cgAddOnDetails = new AddOnDetails();
                BeanUtils.copyProperties(orchDetail, cgAddOnDetails);

                // Transform applied policy details
                if (orchDetail.getApplied() != null) {
                    PolicyDetails appliedPolicyDetails = new PolicyDetails();
                    BeanUtils.copyProperties(orchDetail.getApplied(), appliedPolicyDetails);

                    // Set price description with polyglot translation (matching legacy logic)
                    String appliedText = polyglotService.getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT);
                    if (StringUtils.isNotEmpty(appliedText) && StringUtils.isNotEmpty(orchDetail.getApplied().getPriceDescription())) {
                        appliedText = MessageFormat.format(appliedText, orchDetail.getApplied().getPriceDescription());
                        appliedPolicyDetails.setPriceDescription(appliedText);
                    }
                    cgAddOnDetails.setApplied(appliedPolicyDetails);
                }

                // Transform removed policy details
                if (orchDetail.getRemoved() != null) {
                    PolicyDetails removePolicyDetails = new PolicyDetails();
                    BeanUtils.copyProperties(orchDetail.getRemoved(), removePolicyDetails);
                    // Keep the original price description for removed policy
                    cgAddOnDetails.setRemoved(removePolicyDetails);
                }

                addOnDetailsMap.put("FLEXI_CANCEL", cgAddOnDetails);
                break; // Only process first FLEXI_CANCEL found (matching legacy logic)
            }
        }

        return addOnDetailsMap;
    }

    /**
     * Transform OrchV2 addOnPolicies (equivalent to legacy FlexiCancelAddOn) to CG response format.
     * Note: addOnPolicies in OrchV2 is List<CancellationPolicy>, not AddOnPolicy.
     * This handles cancellation policies that might contain addOn-related information.
     */
    private Map<String, AddOnDetails> transformAddOnPolicies(List<com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationPolicy> orchAddOnPolicies) {
        if (CollectionUtils.isEmpty(orchAddOnPolicies)) {
            return null;
        }

        Map<String, AddOnDetails> addOnPoliciesMap = new HashMap<>();

        // Process each cancellation policy for addOn-related information
        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationPolicy orchPolicy : orchAddOnPolicies) {
            if (orchPolicy != null && CollectionUtils.isNotEmpty(orchPolicy.getInclusions())) {

                // Check if this cancellation policy has addOn-related inclusions
                // This is typically used for FlexiCancel policies with inclusions
                for (com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion inclusion : orchPolicy.getInclusions()) {
                    if (inclusion != null && StringUtils.isNotEmpty(inclusion.getInclusionType()) &&
                            "FLEXI_CANCEL".equals(inclusion.getInclusionType())) {

                        // Create CG AddOnDetails from cancellation policy inclusion
                        AddOnDetails cgAddOnDetails = new AddOnDetails();
                        // Note: CG AddOnDetails doesn't have setType method - it's inferred from map key

                        // Map inclusion properties to AddOnDetails (using available methods)
                        cgAddOnDetails.setBackgroundImage(inclusion.getIconUrl());
                        cgAddOnDetails.setCta(inclusion.getValue());

                        // Create basic policy details from inclusion
                        if (StringUtils.isNotEmpty(inclusion.getValue())) {
                            PolicyDetails appliedPolicyDetails = new PolicyDetails();
                            appliedPolicyDetails.setTitle(inclusion.getValue());
                            appliedPolicyDetails.setPriceDescription(inclusion.getValue());

                            // Apply polyglot translation for FlexiCancel
                            String appliedText = polyglotService.getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT);
                            if (StringUtils.isNotEmpty(appliedText)) {
                                appliedPolicyDetails.setPriceDescription(appliedText);
                            }
                            cgAddOnDetails.setApplied(appliedPolicyDetails);
                        }

                        // Add to map using the inclusion type as key
                        addOnPoliciesMap.put(inclusion.getInclusionType(), cgAddOnDetails);

                        LOGGER.debug("Transformed addOnPolicy inclusion type: {} to CG AddOnDetails", inclusion.getInclusionType());
                    }
                }
            }
        }

        return addOnPoliciesMap.isEmpty() ? null : addOnPoliciesMap;
    }

    /**
     * Convert Map<String, AddOnDetails> to List<AddOnNode> format expected by CommonResponseTransformer.
     * This handles the final conversion step for the addOns transformation chain.
     */
    public List<AddOnNode> convertToAddOnNodeList(Map<String, AddOnDetails> addOnDetailsMap) {
        if (addOnDetailsMap == null || addOnDetailsMap.isEmpty()) {
            return new ArrayList<>();
        }

        List<AddOnNode> addOnNodeList = new ArrayList<>();

        for (Map.Entry<String, AddOnDetails> entry : addOnDetailsMap.entrySet()) {
            AddOnNode addOnNode = new AddOnNode();
            addOnNode.setAddOnType(entry.getKey());

            // Convert AddOnDetails to the format expected by AddOnNode
            BeanUtils.copyProperties(entry.getValue(), addOnNode);
            addOnNodeList.add(addOnNode);
        }

        return addOnNodeList;
    }
} 