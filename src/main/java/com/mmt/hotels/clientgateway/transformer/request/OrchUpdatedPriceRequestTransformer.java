package com.mmt.hotels.clientgateway.transformer.request;

import com.gommt.hotels.orchestrator.detail.enums.*;
import com.gommt.hotels.orchestrator.detail.enums.Currency;
import com.gommt.hotels.orchestrator.detail.enums.TrafficSource;
import com.gommt.hotels.orchestrator.detail.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.detail.model.objects.ExtraInfo;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomCriteria;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.FilterDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.state.ChatbotDetails;
import com.gommt.hotels.orchestrator.detail.model.state.ImageDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.service.OrchDetailService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;

@Component
public class OrchUpdatedPriceRequestTransformer {

    @Autowired
    Utility utility;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchUpdatedPriceRequestTransformer.class);

    public DetailRequest buildUpdatePriceRequest(UpdatePriceRequest cgRequest, CommonModifierResponse commonModifierResponse) {
        if (cgRequest == null) {
            LOGGER.error("UpdatePriceRequest is null");
            throw new IllegalArgumentException("UpdatePriceRequest cannot be null");
        }

        if (commonModifierResponse == null) {
            LOGGER.error("CommonModifierResponse is null");
            throw new IllegalArgumentException("CommonModifierResponse cannot be null");
        }

        DetailRequest orchRequest = new DetailRequest();

        // Build location details first
        orchRequest.setLocation(buildUpdatePriceLocationDetails(cgRequest));

        // Extract search criteria safely (similar to buildSearchRoomsRequest)
        UpdatedPriceCriteria searchCriteria = Optional.ofNullable(cgRequest.getSearchCriteria())
                .orElseThrow(() -> new IllegalArgumentException("SearchCriteria cannot be null"));

        // Set required fields: hotelId, check-in, check-out
        orchRequest.setHotelId(searchCriteria.getHotelId());
        orchRequest.setCheckIn(searchCriteria.getCheckIn());
        orchRequest.setCheckOut(searchCriteria.getCheckOut());

        // Build room criteria (Room Stay Candidates) - similar to buildSearchRoomsRequest
        orchRequest.setRooms(buildRoomDetails(cgRequest.getSearchCriteria().getRoomStayCandidates(), cgRequest.getExpDataMap()));
        orchRequest.setExtraInfo(buildExtraInfo(searchCriteria.getSearchType()));
        if(cgRequest!=null && cgRequest.getRequestDetails()!=null) {
            orchRequest.setCouponCount(cgRequest.getRequestDetails().getCouponCount());
        }

        // Build client details - similar to buildSearchRoomsRequest
        orchRequest.setClientDetails(buildClientDetailsForUpdatePrice(cgRequest, commonModifierResponse));

        // Build filters - reuse existing logic if filters are available
        orchRequest.setFilters(buildUpdatePriceFilterDetails(cgRequest));

        // Build image details - reuse logic if needed for update price
        orchRequest.setImageDetails(buildUpdatePriceImageDetails(cgRequest));

        // Set experiment data and additional fields
        orchRequest.setExperimentData(cgRequest.getExpData());
        orchRequest.setExpVariantKeys(cgRequest.getExpVariantKeys());
        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
//        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());

        LOGGER.info("Successfully built UpdatePricingRequest for hotelId: {}", orchRequest.getHotelId());
        return orchRequest;
    }

    private LocationDetails buildUpdatePriceLocationDetails(UpdatePriceRequest updatePriceRequest) {
        LocationDetails locationDetails = new LocationDetails();

        if (updatePriceRequest.getSearchCriteria() != null) {
            locationDetails.setCityId(updatePriceRequest.getSearchCriteria().getCityCode());
            locationDetails.setCountryId(updatePriceRequest.getSearchCriteria().getCountryCode());
            locationDetails.setId(updatePriceRequest.getSearchCriteria().getLocationId());
            locationDetails.setType(updatePriceRequest.getSearchCriteria().getLocationType());
        }

        return locationDetails;
    }

    private List<RoomDetails> buildUpdatePriceRoomDetails(UpdatePriceRequest updatePriceRequest) {
        List<RoomDetails> roomDetailsList = new ArrayList<>();

        if (updatePriceRequest.getSearchCriteria() != null && updatePriceRequest.getSearchCriteria().getRoomCriteria() != null) {
            for (int i = 0; i < updatePriceRequest.getSearchCriteria().getRoomCriteria().size(); i++) {
                UpdatedPriceRoomCriteria roomCriterion = updatePriceRequest.getSearchCriteria().getRoomCriteria().get(i);
                if (roomCriterion != null && roomCriterion.getRoomStayCandidates() != null) {
                    for (RoomStayCandidate candidate : roomCriterion.getRoomStayCandidates()) {
                        RoomDetails roomDetails = new RoomDetails();
                        roomDetails.setAdults(candidate.getAdultCount());
                        roomDetails.setChildrenAges(candidate.getChildAges());
                        roomDetailsList.add(roomDetails);
                    }
                }
            }
        }

        return roomDetailsList;
    }

    private ExtraInfo buildExtraInfo(String searchType) {
        ExtraInfo extraInfo = new ExtraInfo();

        if (StringUtils.isNotEmpty(searchType)) {
            extraInfo.setSearchType(searchType);
        }

        // Set default values for other ExtraInfo fields if needed
        // Add more fields as required based on the ExtraInfo class structure

        return extraInfo;
    }

    private ClientDetails buildClientDetailsForUpdatePrice(UpdatePriceRequest updatePriceRequest, CommonModifierResponse commonModifierResponse) {
        ClientDetails clientDetails = new ClientDetails();
        clientDetails.setFeatureFlags(buildFeatureFlagsForUpdatePrice(updatePriceRequest, commonModifierResponse));
        clientDetails.setRequestDetails(buildRequestDetailsForUpdatePrice(updatePriceRequest, commonModifierResponse));
        clientDetails.setUserDetails(buildUserDetailsForUpdatePrice(updatePriceRequest, commonModifierResponse));
        clientDetails.setVisitorId(updatePriceRequest.getRequestDetails() != null ? updatePriceRequest.getRequestDetails().getVisitorId() : "");
        clientDetails.setMcId(commonModifierResponse.getMcId());
        clientDetails.setChatbotDetails(buildChatbotDetailsForUpdatePrice(updatePriceRequest));
        return clientDetails;
    }

    private List<FilterDetails> buildUpdatePriceFilterDetails(UpdatePriceRequest updatePriceRequest) {
        if (updatePriceRequest == null) {
            LOGGER.warn("UpdatePriceRequest is null while building FilterDetails, returning an empty FilterDetails list.");
            return Collections.emptyList();
        }

        // For update price, we typically don't have filters, but return empty list to be safe
        List<FilterDetails> filterDetailsList = new ArrayList<>();

        // If the updatePriceRequest has any filter criteria in the future, handle it here
        // Currently returning empty list as update price requests typically don't have filters

        LOGGER.info("Successfully built FilterDetails for updatePrice: {} filters.", filterDetailsList.size());
        return filterDetailsList;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.ImageDetails buildUpdatePriceImageDetails(UpdatePriceRequest updatePriceRequest) {
        com.gommt.hotels.orchestrator.detail.model.state.ImageDetails imageDetails = new ImageDetails();

        // For update price requests, image details are typically not required
        // Set empty/default values
        imageDetails.setCategories(Collections.emptyList());
        imageDetails.setTypes(Collections.emptyList());

        return imageDetails;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags buildFeatureFlagsForUpdatePrice(UpdatePriceRequest updatePriceRequest, CommonModifierResponse commonModifierResponse) {
        com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags featureFlags = new com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags();

        if (updatePriceRequest.getFeatureFlags() != null) {
            // Use the correct FeatureFlags method names - set defaults for missing methods
            featureFlags.setWalletRequired(Optional.of(updatePriceRequest.getFeatureFlags()).map(FeatureFlags::isWalletRequired).orElse(false));
            featureFlags.setBestCoupon(Optional.of(updatePriceRequest.getFeatureFlags()).map(FeatureFlags::isCoupon).orElse(false));
            featureFlags.setComparatorHotelRequest(Optional.of(updatePriceRequest.getFeatureFlags()).map(FeatureFlags::isComparator).orElse(false));
            featureFlags.setCheckAvailability(Optional.of(updatePriceRequest.getFeatureFlags()).map(FeatureFlags::isCheckAvailability).orElse(false));
            // Set defaults for missing methods
            featureFlags.setBookingModification(false);
            // Note: setEnableGst method doesn't exist in orchestrator FeatureFlags
        }

        return featureFlags;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.RequestDetails buildRequestDetailsForUpdatePrice(UpdatePriceRequest updatePriceRequest, CommonModifierResponse commonModifierResponse) {
        if (updatePriceRequest == null) {
            LOGGER.warn("UpdatePriceRequest is null while building RequestDetails, returning empty RequestDetails.");
            return new com.gommt.hotels.orchestrator.detail.model.state.RequestDetails();
        }

        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails requestDetails = new com.gommt.hotels.orchestrator.detail.model.state.RequestDetails();

        // Safely retrieve BookingDevice from deviceDetails, defaulting to an empty device
        requestDetails.setBookingDevice(buildBookingDevice(Optional.ofNullable(updatePriceRequest.getDeviceDetails()).orElse(new DeviceDetails())));
        requestDetails.setRequestType("B2CAgent");

        // Safely set currency from search criteria
        String currency = updatePriceRequest.getSearchCriteria() != null && StringUtils.isNotEmpty(updatePriceRequest.getSearchCriteria().getCurrency()) ? updatePriceRequest.getSearchCriteria().getCurrency().toUpperCase() : "INR";
        requestDetails.setPayMode(updatePriceRequest.getRequestDetails()!=null ? updatePriceRequest.getRequestDetails().getPayMode() : EMPTY_STRING);
        requestDetails.setCurrency(Currency.valueOf(currency));

        // Set multi-currency info if available
        if (updatePriceRequest.getSearchCriteria() != null && updatePriceRequest.getSearchCriteria().getMultiCurrencyInfo() != null) {
            requestDetails.setMultiCurrencyInfo(buildMultiCurrencyInfo(updatePriceRequest.getSearchCriteria().getMultiCurrencyInfo()));
        }

        // Set user global info if available
        if (updatePriceRequest.getSearchCriteria() != null && updatePriceRequest.getSearchCriteria().getUserGlobalInfo() != null) {
            requestDetails.setUserGlobalInfo(buildUserGlobalInfo(updatePriceRequest.getSearchCriteria().getUserGlobalInfo()));
        }

        if (updatePriceRequest.getRequestDetails() != null) {
            requestDetails.setRequestor(updatePriceRequest.getRequestDetails().getRequestor());
        }

        // RequestDetails - Safely retrieve values from requestDetails, setting defaults if necessary
        RequestDetails requestDetailsFromGateway = Optional.ofNullable(updatePriceRequest.getRequestDetails())
                .orElse(new RequestDetails());

        requestDetails.setRequestId(Optional.ofNullable(requestDetailsFromGateway.getRequestId()).orElse(UUID.randomUUID().toString()));
        requestDetails.setJourneyId(Optional.ofNullable(requestDetailsFromGateway.getJourneyId()).orElse(""));
        requestDetails.setFunnelSource(Optional.ofNullable(requestDetailsFromGateway.getFunnelSource())
                .map(Funnel::fromValue)
                .orElse(Funnel.HOTELS));

        requestDetails.setPageContext(Optional.ofNullable(requestDetailsFromGateway.getPageContext())
                .map(PageContext::fromValue)
                .orElse(PageContext.DETAIL));  // Update price is typically on detail page

        requestDetails.setVisitorId(Optional.ofNullable(requestDetailsFromGateway.getVisitorId()).orElse(""));

//        requestDetails.setOriginalTrafficSource(Optional.ofNullable(commonModifierResponse.getOriginalTrafficSource())
//                .orElse(TrafficSource.DEFAULT.getName()));

        // Traffic Source and Type - Safely set from requestDetails
        if (SiteDomain.AE.getName().equalsIgnoreCase(requestDetailsFromGateway.getSiteDomain())) {
            requestDetails.setTrafficType(TrafficType.GCC);
        } else if (utility.isMyPartner(commonModifierResponse)) {
            requestDetails.setTrafficType(TrafficType.MYPARTNER);
        } else {
            requestDetails.setTrafficType(Optional.ofNullable(requestDetailsFromGateway.getTrafficSource())
                    .map(com.mmt.hotels.clientgateway.request.TrafficSource::getType)
                    .map(TrafficType::fromValue)
                    .orElse(TrafficType.B2C));
        }

        requestDetails.setChannel(Optional.ofNullable(requestDetailsFromGateway.getChannel()).orElse(""));  // Safely set channel
        requestDetails.setTrafficSource(Optional.ofNullable(requestDetailsFromGateway.getTrafficSource())
                .map(com.mmt.hotels.clientgateway.request.TrafficSource::getSource)
                .orElse(""));

        // Set traffic flow type if available
        if (requestDetailsFromGateway.getTrafficSource() != null && requestDetailsFromGateway.getTrafficSource().getFlowType() != null) {
            requestDetails.setTrafficFlowType(requestDetailsFromGateway.getTrafficSource().getFlowType());
        }

        requestDetails.setBrand(Optional.ofNullable(requestDetailsFromGateway.getBrand())
                .map(Brand::valueOf)
                .orElse(Brand.MMT));

        requestDetails.setIdContext(Optional.ofNullable(requestDetailsFromGateway.getIdContext())
                .map(IdContext::valueOf)
                .orElse(IdContext.B2C));

        // Set site domain with default (removing optional mapping for now)
        requestDetails.setSiteDomain(Optional.ofNullable(requestDetailsFromGateway.getSiteDomain())
                .map(String::toUpperCase)
                .map(SiteDomain::valueOf)
                .orElse(SiteDomain.IN));  // Default to IN domain

        // Set language and region with defaults (using simple assignments)
        requestDetails.setLanguage(Language.ENGLISH);
        requestDetails.setRegion(Region.IN);

        // Set room criteria from search criteria - convert from UpdatedPriceRoomCriteria to orchestrator RoomCriteria
        if (updatePriceRequest.getSearchCriteria() != null && updatePriceRequest.getSearchCriteria().getRoomCriteria() != null) {
            requestDetails.setRoomCriteria(convertToOrchRoomCriteria(updatePriceRequest.getSearchCriteria().getRoomCriteria(), updatePriceRequest));
        }

        LOGGER.info("Successfully built RequestDetails for updatePrice with requestId: {}", requestDetails.getRequestId());
        return requestDetails;
    }

    private UserDetails buildUserDetailsForUpdatePrice(UpdatePriceRequest updatePriceRequest, CommonModifierResponse commonModifierResponse) {
        UserDetails userDetails = new UserDetails();

        if (commonModifierResponse.getExtendedUser() != null) {
            userDetails.setUuid(commonModifierResponse.getExtendedUser().getUuid());
            userDetails.setLoggedIn(updatePriceRequest.getRequestDetails() != null ? updatePriceRequest.getRequestDetails().isLoggedIn() : false);

            // Set profile details
            if (commonModifierResponse.getExtendedUser().getProfileType() != null) {
                userDetails.setProfileType(ProfileType.valueOf(commonModifierResponse.getExtendedUser().getProfileType()));
            }
        }

        userDetails.setMmtAuth(commonModifierResponse.getMmtAuth());

        return userDetails;
    }

    private ChatbotDetails buildChatbotDetailsForUpdatePrice(UpdatePriceRequest updatePriceRequest) {
        if (updatePriceRequest.getRequestDetails() == null || StringUtils.isEmpty(updatePriceRequest.getRequestDetails().getMyraMsgId())) {
            return null;
        }
        ChatbotDetails chatbotDetails = new ChatbotDetails();
        chatbotDetails.setMyraMsgId(updatePriceRequest.getRequestDetails().getMyraMsgId());
        return chatbotDetails;
    }

    private BookingDevice buildBookingDevice(DeviceDetails deviceDetails) {
        if (deviceDetails == null) {
            LOGGER.warn("DeviceDetails is null, returning an empty BookingDevice.");
            return BookingDevice.builder().build();  // Return an empty BookingDevice if deviceDetails is null
        }

        BookingDevice.BookingDeviceBuilder bookingDeviceBuilder = BookingDevice.builder();

        // Safely set device fields, defaulting to empty strings or enums if values are null
        bookingDeviceBuilder.deviceId(Optional.ofNullable(deviceDetails.getDeviceId()).orElse(""));
        bookingDeviceBuilder.deviceName(Optional.ofNullable(deviceDetails.getDeviceName()).orElse(""));

        DeviceType deviceType = Optional.of(deviceDetails.getBookingDevice())
                .map(DeviceType::fromValue)
                .orElse(DeviceType.DESKTOP);
        bookingDeviceBuilder.deviceType(deviceType);

        bookingDeviceBuilder.appVersion(Optional.ofNullable(deviceDetails.getAppVersion()).orElse(""));
        bookingDeviceBuilder.networkType(Optional.ofNullable(deviceDetails.getNetworkType()).orElse(""));
        LOGGER.info("Successfully built BookingDevice with deviceId: {}", deviceDetails.getDeviceId());
        return bookingDeviceBuilder.build();
    }

    private List<RoomCriteria> convertToOrchRoomCriteria(List<UpdatedPriceRoomCriteria> roomCriteria, UpdatePriceRequest updatePriceRequest) {
        if (roomCriteria == null || roomCriteria.isEmpty()) {
            return null;
        }

        List<RoomCriteria> orchRoomCriteriaList = new ArrayList<>();

        for (UpdatedPriceRoomCriteria updatedRoomCriteria : roomCriteria) {
            RoomCriteria orchRoomCriteria = RoomCriteria.builder()
                    .roomCode(updatedRoomCriteria.getRoomCode())
                    .ratePlanCode(updatedRoomCriteria.getRatePlanCode())
                    .mtKey(updatedRoomCriteria.getMtKey())
                    .pricingKey(updatedRoomCriteria.getPricingKey())
                    .count(updatedRoomCriteria.getRoomStayCandidates() != null ?
                            updatedRoomCriteria.getRoomStayCandidates().size() : 1)
                    .supplierCode(updatedRoomCriteria.getSupplierCode())
                    .rooms(buildRoomDetails(updatedRoomCriteria.getRoomStayCandidates(), updatePriceRequest.getExpDataMap()))
                    .build();

            orchRoomCriteriaList.add(orchRoomCriteria);
        }

        return orchRoomCriteriaList;
    }

    private List<RoomDetails> buildRoomDetails(List<RoomStayCandidate> roomStayCandidates, Map<String,String> expDataMap) {
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            LOGGER.warn("ListingSearchRequest or SearchCriteria is null while building RoomDetails, returning an empty RoomDetails list.");
            return Collections.emptyList();  // Return an empty list if the request or search criteria is null
        }


        if (roomStayCandidates == null || roomStayCandidates.isEmpty()) {
            LOGGER.warn("RoomStayCandidates is null or empty, returning an empty RoomDetails list.");
            return Collections.emptyList();  // Return empty list if no candidates are provided
        }

        if (roomStayCandidates != null && utility.isDistributeRoomStayCandidates(roomStayCandidates, expDataMap)) {
            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(roomStayCandidates, expDataMap);
            return roomStayCandidatesHES.stream().map(roomStayCandidate -> {
                RoomDetails roomDetails = new RoomDetails();
                int adultCount = 0;
                List<Integer> childrenAges = new ArrayList<>();
                for (GuestCount guestCount : roomStayCandidate.getGuestCounts()) {
                    adultCount = adultCount + Integer.parseInt(guestCount.getCount());
                    if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                        childrenAges.addAll(guestCount.getAges());
                    }
                }
                roomDetails.setAdults(adultCount);
                roomDetails.setChildrenAges(childrenAges);
                return roomDetails;
            }).collect(Collectors.toList());
        }


        List<RoomDetails> roomDetailsList = new ArrayList<>();

        // Iterate over room stay candidates and build room details
        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            if (roomStayCandidate != null) {
                RoomDetails roomDetails = new RoomDetails();
                roomDetails.setAdults(roomStayCandidate.getAdultCount());
                roomDetails.setChildrenAges(roomStayCandidate.getChildAges());
                roomDetailsList.add(roomDetails);
            } else {
                LOGGER.warn("Encountered a null RoomStayCandidate, skipping.");
            }
        }

        LOGGER.info("Successfully built RoomDetails for {} rooms.", roomDetailsList.size());
        return roomDetailsList;
    }

    private com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo buildMultiCurrencyInfo(com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfo) {

        if(multiCurrencyInfo==null)
            return null;
        return com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo.builder()
                .userCurrency(multiCurrencyInfo.getUserCurrency())
                .regionCurrency(multiCurrencyInfo.getRegionCurrency())
                .build();
    }

    public com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo buildUserGlobalInfo(UserGlobalInfo userGlobalInfo) {
        com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo userGlobalInfoOrch = new com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo();
        userGlobalInfoOrch.setUserCountry(userGlobalInfo.getUserCountry());
        userGlobalInfoOrch.setEntityName(userGlobalInfo.getEntityName());
        return userGlobalInfoOrch;
    }
}
