package com.mmt.hotels.clientgateway.transformer.response;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.MyTripActionUrls;
import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.payment.InsuranceInfo;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.availrooms.PropertyRules;
import com.mmt.hotels.clientgateway.response.dayuse.DayUseDetails;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.response.thankyou.BookerInfo;
import com.mmt.hotels.clientgateway.response.thankyou.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.clm.ClmPersuasion;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.RoomStayCandidateReqBody;
import com.mmt.hotels.model.request.addon.AddOnType;
import com.mmt.hotels.model.request.payment.AddOnDetail;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.addon.TmInsuranceAddOns;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.MealPlan;
import com.mmt.hotels.model.response.pricing.PaymentMode;
import com.mmt.hotels.model.response.pricing.RatePolicy;
import com.mmt.hotels.model.response.txn.BookingMetaInfo;
import com.mmt.hotels.model.response.txn.CouponStatus;
import com.mmt.hotels.model.response.txn.HotelInfo;
import com.mmt.hotels.model.response.txn.HotelReservationId;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.model.response.txn.PersistedTariffInfo;
import com.mmt.hotels.model.response.txn.ThankYouAmountLabel;
import com.mmt.hotels.model.response.txn.TravelerInfo;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GOTRIBE_BENEFITS_SUB_TITLE_REVIEW;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GOTRIBE_BENEFITS_TITLE_REVIEW;
import static com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer.getManthanGocashValueValidateCoupon;

public abstract class ThankYouResponseTransformer {

    private static final Gson gson = new Gson();

    private Map<String, String> mealPlanMapPolyglot;

    @Value("${thankyou.max.inclusions}")
    private int maxInclusionsThankyou;

    @Value("${thankyou.mytrips.section.actions.config}")
    private String myTripCardDetails;

    @Value("${thankyou.mytrips.section.actions.cta.text.replacer}")
    private String myTripCardCtaTextReplacer;

    @Value("${thankyou.mytrips.section.actions.icon.urls}")
    private String myTripCardIconUrls;

    @Value("${thankyou.mytrips.section.conditions}")
    private String myTripCardConditions;

    @Value("${thankyou.pancard.icon.url}")
    private String panCardIconUrl;

    @Value("${thankyou.paynow.url.gi}")
    private String thankyouPaynowUrlGi;

    @Value("${elite.package.icon.url}")
    private String elitePackageIconUrl;

    @Value("${elite.package.type}")
    private String elitePackageType;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    private Map<String, MyTripCard> myTripsCardTypeToCardDetails;

    private Map<String, String> myTripsCtaTextReplacer;

    private Map<String, MyTripCard> myTripsCardTypeToCardDetailsModified;

    protected Map<String, MyTripActionUrls> myTripsCardTypeToIconUrls;

    private Map<String, List<String>> myTripsConditionsToCardsList;

    private Map<String, CardData> thankYouCards;

    @Value("#{'${group.booking.thank.you.page.cards}'.split(',')}")
    private List<String> groupBookingCardKeys;

    List<String> sameDayRoomNames;

    private static final Logger logger = LoggerFactory.getLogger(ThankYouResponseTransformer.class);

    @Autowired
    PropertyManager propManager;

    @Autowired
    private  Utility utility;

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    protected PolyglotHelper polyglotHelper;

    protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);

    @PostConstruct
    public void init() {
        Gson gson = new Gson();
        myTripsCardTypeToCardDetails = gson.fromJson(myTripCardDetails, new TypeToken<Map<String, MyTripCard>>() {
        }.getType());

        myTripsCtaTextReplacer = gson.fromJson(myTripCardCtaTextReplacer, new TypeToken<Map<String, String>>() {
        }.getType());

        myTripsConditionsToCardsList = gson.fromJson(myTripCardConditions, new TypeToken<Map<String, List<String>>>() {
        }.getType());

        myTripsCardTypeToIconUrls = gson.fromJson(myTripCardIconUrls, new TypeToken<Map<String, MyTripActionUrls>>() {
        }.getType());

        try {
            CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
            mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
            thankYouCards = commonConfig.thankYouCards();
            sameDayRoomNames = commonConfig.sameDayRoomNames();
            commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
            commonConfig.addPropertyChangeListener("thankYouCards", evt -> thankYouCards = commonConfig.thankYouCards());
            commonConfig.addPropertyChangeListener("sameDayRoomNames", evt -> sameDayRoomNames = commonConfig.sameDayRoomNames());



        }catch (Exception ex){
            logger.error("error in fetching meal planmap");
        }
    }

    public ThankYouResponse convertThankYouResponse(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity, FeatureFlags featureFlags, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ErrorResponseFromDownstreamException {
        if (persistanceMultiRoomResponseEntity == null || persistanceMultiRoomResponseEntity.getPersistedData() == null)
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM);
        ThankYouResponse thankYouResponse = new ThankYouResponse();
        thankYouResponse.setBookingDetails(buildBookingDetails(persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setTotalAmount(buildTotalAmount(persistanceMultiRoomResponseEntity.getPersistedData()));
        if (thankYouResponse.getTotalAmount() == null) { //full amount not paid.. i.e bnpl or pah cases
            populateAmountBreakup(thankYouResponse, persistanceMultiRoomResponseEntity);

        }
        if (thankYouResponse.getPaidAmount() == null) {
            if(persistanceMultiRoomResponseEntity.getPersistedData().isPartialPayment()) {
                thankYouResponse.setPaidAmount(buildPartialAmountPaid(persistanceMultiRoomResponseEntity.getPersistedData()));
            } else {
                thankYouResponse.setPaidAmount(thankYouResponse.getTotalAmount());//for PAS case both are same
            }
            updatePgChargesInPaidAmount(thankYouResponse.getPaidAmount(), persistanceMultiRoomResponseEntity.getPersistedData());
        }
        thankYouResponse.setHotelDetails(buildHotelDetails(persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setTravellers(buildTravellersDetails(persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setRooms(buildBookedRooms(persistanceMultiRoomResponseEntity.getPersistedData(), thankYouResponse));
        if (persistanceMultiRoomResponseEntity.getPersistedData().getFlexibleCheckIn() != null){
            thankYouResponse.setFlexibleCheckinInfo(persistanceMultiRoomResponseEntity.getPersistedData().getFlexibleCheckIn());
        }
        if (persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare() != null && persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
            double bnplConvFees = thankYouResponse.getBookingDetails() != null && thankYouResponse.getBookingDetails().isBnplBooking() ?
                    persistanceMultiRoomResponseEntity.getPersistedData().getBnplExtraFees() : 0.0;
            Integer charityAmount = null;
            double insuranceAmount = thankYouResponse.getBookingDetails() != null && thankYouResponse.getBookingDetails().isBnplBooking() ?
                    getInsuranceAmount(persistanceMultiRoomResponseEntity) : 0.0;
            if(isCharitySelectedByUser(persistanceMultiRoomResponseEntity.getPersistedData())) {
                if(persistanceMultiRoomResponseEntity.getPersistedData().getRecommendedAddOns().containsKey(CHARITY_ID_V2)) {
                    charityAmount = persistanceMultiRoomResponseEntity.getPersistedData().getRecommendedAddOns().get(CHARITY_ID_V2).getPrice();
                } else {
                    charityAmount = persistanceMultiRoomResponseEntity.getPersistedData().getRecommendedAddOns().get(CHARITY_ID).getPrice();
                }
            }
            thankYouResponse.setTotalpricing(commonResponseTransformer.getTotalPricing(persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare().getDisplayPriceBreakDown(), persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getCountryCode(),
                    persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getPayMode(),
                    isCorp(null),
                    "",
                    persistanceMultiRoomResponseEntity.getPersistedData().getExpData(), Utility.isGroupBookingFunnel(persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getFunnelSource()),persistanceMultiRoomResponseEntity.getPersistedData().isCbrAvailable(),null, null, thankYouResponse.getPaidAmount(),
                    charityAmount, bnplConvFees,insuranceAmount));
            /**
             * Added below add amount details for GI bnpl booking
             */
            addAmountDetails(thankYouResponse);
            // Add charity in pricingDetails list
            addCharityInPricingDetails(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        }
        thankYouResponse.setPropertyRules(buildPropertyRules(persistanceMultiRoomResponseEntity.getPersistedData(), thankYouResponse));
        thankYouResponse.setSelectedSpecialRequests(buildSelectedSpecialRequests(persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setMyTripsSection(buildMyTripsSection(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setExperimentData(buildExperimentData(persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setUserLoyaltyStatus(persistanceMultiRoomResponseEntity.getPersistedData().getUserLoyaltyStatus());
        thankYouResponse.setTrafficSource(persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getTrafficSource());
        if (thankYouResponse.getHotelDetails() != null){
            thankYouResponse.setSafetyPersuasionMap(commonResponseTransformer.buildSafetyPersuasionList(thankYouResponse.getHotelDetails().getCategories()));
            if("android".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || "ios".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                thankYouResponse.getHotelDetails().setApplicableHotelCategoryData(commonResponseTransformer.buildHotelCategoryDataMap(thankYouResponse.getHotelDetails().getCategories()));
            }
            else {
                thankYouResponse.getHotelDetails().setApplicableHotelCategoryDataWeb(commonResponseTransformer.buildHotelCategoryDataWeb(thankYouResponse.getHotelDetails().getCategories()));
            }
        }
        updateInclusions(thankYouResponse, featureFlags != null && featureFlags.isAllInclusions());
        updateAmountBreakupInSuccessBookingCardInfo(thankYouResponse.getTotalpricing(), thankYouResponse.getBookingDetails());
        buildAdditionalCharges(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildCardDetails(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildLogData(thankYouResponse,persistanceMultiRoomResponseEntity.getPersistedData());
        buildMetaChannelInfo(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildRtbChatCard(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildGSTDetails(persistanceMultiRoomResponseEntity.getPersistedData(), thankYouResponse);
        buildPgCharges(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildBPGText(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildPanCardGuidelines(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        if(persistanceMultiRoomResponseEntity.getPersistedData().isPartialPayment()) {
            thankYouResponse.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(persistanceMultiRoomResponseEntity.getPersistedData().getPaymentPlan()));
        }
        buildPendingAmount(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildMyPatHeroBanner(thankYouResponse,persistanceMultiRoomResponseEntity.getPersistedData());
        buildMyPartnerFareHoldData(thankYouResponse,persistanceMultiRoomResponseEntity.getPersistedData());
        /*build cashback offer persuasion/Hero offer persuasion for thankyou page myPartner funnel*/
        thankYouResponse.setHotelPersuasions(buildPersuasionsMap(persistanceMultiRoomResponseEntity.getPersistedData()));
        PersistedMultiRoomData persistedData = persistanceMultiRoomResponseEntity.getPersistedData();
        boolean isDomesticSearch = StringUtils.isNotEmpty(persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getCountryCode()) && StringUtils.equalsIgnoreCase(persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getCountryCode(), Constants.DOM_COUNTRY);

        if (persistedData.getExpData() != null &&
                (!utility.isExperimentTrue(persistedData.getExpData(), GOTRIBE3_POKUS_EXP_KEY) &&
                (persistedData.getExpData().containsKey("recomFlow") && persistedData.getExpData().get("recomFlow").equals("true")) ||
                (isDomesticSearch && persistedData.getExpData().containsKey("wallet_exp") && persistedData.getExpData().get("wallet_exp").equals("1")) ||
                (!isDomesticSearch && persistedData.getExpData().containsKey(WalletExpIH) && persistedData.getExpData().get(WalletExpIH).equals("1"))
        )
        ) {
            thankYouResponse.setClmCashback(buildCLMCashbackData(persistanceMultiRoomResponseEntity.getPersistedData().getClmPersuasion(), thankYouResponse));
        }else {
            thankYouResponse.setTotalCashback(buildTotalCashbackData(persistanceMultiRoomResponseEntity.getPersistedData(), thankYouResponse));
        }
        if (utility.checkIfGoStaysProperty(persistanceMultiRoomResponseEntity.getPersistedData()) && !FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(persistedData.getAvailReqBody().getFunnelSource())) {
            StreaksUserInfoResponse streaksUserInfoResponse = null;
            try {
                streaksUserInfoResponse = utility.getUserInfoResponse(persistanceMultiRoomResponseEntity.getPersistedData().getBookerInfo().getUuid(),
                        persistanceMultiRoomResponseEntity.getPersistedData().getExpData(), persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getCorrelationKey(), parameterMap, httpHeaderMap);
                if (streaksUserInfoResponse != null) {
                    utility.fetchThankYouDetailsFromStreaksResp(streaksUserInfoResponse);
                thankYouResponse.getHotelDetails().setStreaksUserInfoResponse(streaksUserInfoResponse);
                }
            } catch (ClientGatewayException e) {
                logger.error("Unexpected exception from Streak API");
            }
        }
        if (persistedData.getBlackInfo() != null && MapUtils.isNotEmpty(persistedData.getExpData())
                && utility.isExperimentTrue(persistedData.getExpData(), GOTRIBE_REVAMP_POKUS_EXP_KEY) && !utility.isExperimentTrue(persistedData.getExpData(), GOTRIBE3_POKUS_EXP_KEY)) {
            thankYouResponse.setGoTribeInfo(commonResponseTransformer.buildGoTribeInfo(persistedData.getBlackInfo(),
                    polyglotService.getTranslatedData(GOTRIBE_BENEFITS_TITLE_REVIEW),
                    polyglotService.getTranslatedData(GOTRIBE_BENEFITS_SUB_TITLE_REVIEW), false));
        }
        thankYouResponse.setIncognitoMode(persistedData.isIncognitoMode());

        return thankYouResponse;
    }
    private double getInsuranceAmount(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {
        if (persistanceMultiRoomResponseEntity.getPersistedData() != null && persistanceMultiRoomResponseEntity.getPersistedData().getAddOnInfo() != null
                && CollectionUtils.isNotEmpty(persistanceMultiRoomResponseEntity.getPersistedData().getAddOnInfo().getAddOnNode())) {
            Optional<AddOnNode> donatedAddon = persistanceMultiRoomResponseEntity.getPersistedData().getAddOnInfo().getAddOnNode().stream()
                    .filter(addOnNode -> Constants.INSURANCE_WIDGET.equalsIgnoreCase(addOnNode.getId()))
                    .findFirst();
            if (donatedAddon.isPresent()) {
                return donatedAddon.get().getPrice();
            }
        }
        return 0.0;
    }
    private void updateAmountBreakupInSuccessBookingCardInfo(TotalPricing totalpricing, BookingDetails bookingDetails) {
        if (totalpricing != null && bookingDetails != null && bookingDetails.getSuccessBookingCardInfo() != null) {
            double amountPaid = 0.0, amountDue = 0.0, totalPrice = 0.0;
            for (PricingDetails pricingDetails : totalpricing.getDetails()) {
                if (pricingDetails.getKey().equals(TOTAL_PAID_KEY))
                    amountPaid = pricingDetails.getAmount();
                if (pricingDetails.getKey().equals(REMAINING_AMOUNT_TO_PAY_KEY))
                    amountDue = pricingDetails.getAmount();
                if (pricingDetails.getKey().equals(TOTAL_AMOUNT_KEY))
                    totalPrice = pricingDetails.getAmount();
            }

            List<AmountDetail> amountDetails = bookingDetails.getSuccessBookingCardInfo().getAmountBreakup();
            if (CollectionUtils.isNotEmpty(amountDetails)) {
                for (AmountDetail amountDetail : amountDetails) {
                    if (amountDetail.getKey().equals(AMOUNT_PAID_KEY))
                        amountDetail.setAmount(amountPaid);
                    if (amountDetail.getKey().equals(DUE_AMOUNT_KEY))
                        amountDetail.setAmount(amountDue);
                    if (amountDetail.getKey().equals(TOTAL_PRICE_KEY))
                        amountDetail.setAmount(totalPrice);
                }
            }
        }
    }


    private void addAmountDetails(ThankYouResponse response) {
        if (response != null && response.getTotalpricing() != null && CollectionUtils.isNotEmpty(response.getTotalpricing().getDetails())) {
            List<PricingDetails> pricingDetailsList = response.getTotalpricing().getDetails();
            List<PricingDetails> pricingDetailsNewList = new ArrayList<>();
            double paidAmount = response.getPaidAmount() != null && response.getPaidAmount().getAmount() != null ? response.getPaidAmount().getAmount() : 0.0d;
            double totalAmount = 0.0d;
            double goCashAmount = 0.0d;
            int totalAmountIndex = 0;
            int pricingDetailsIndex = 0;
            /* Add go cash in total price only for no PAH booking */
            boolean addGoCashFlag = true;
            String payMode = response.getBookingDetails() != null && StringUtils.isNotEmpty(response.getBookingDetails().getPaymentMode()) ? response.getBookingDetails().getPaymentMode() : "";
            for (PricingDetails pricingDetails : pricingDetailsList) {
                if (StringUtils.equalsIgnoreCase(pricingDetails.getKey(), Constants.TOTAL_AMOUNT_KEY)){
                    switch (payMode){
                        case PAH_WITH_CC:
                            pricingDetails.setLabel("Amount to be charged by the property");
                            addGoCashFlag = false;
                            break;
                        case PAH:
                        case PAH_WITHOUT_CC:
                            pricingDetails.setLabel("Amount to be paid at the property");
                            addGoCashFlag = false;
                            break;
                        default:
                            pricingDetails.setLabel("Total Amount");
                    }
                    totalAmount = pricingDetails.getAmount();
                    totalAmountIndex = pricingDetailsIndex;
                }
                if (StringUtils.equalsIgnoreCase(pricingDetails.getKey(), WALLET_KEY) && StringUtils.equalsIgnoreCase(pricingDetails.getLabel(), "goCash")){
                    goCashAmount = pricingDetails.getAmount();
                }
                pricingDetailsNewList.add(pricingDetails);
                pricingDetailsIndex++;
            }
            if (Double.compare(goCashAmount, 0.0d) != 0 && pricingDetailsNewList.get(totalAmountIndex) != null && addGoCashFlag) {
                pricingDetailsNewList.get(totalAmountIndex).setAmount(totalAmount+goCashAmount);
            }
            if (response.getBookingDetails() != null && response.getBookingDetails().isBnplBooking()) {
                pricingDetailsNewList.add(buildPricingDetailPaidAmount(paidAmount, totalAmount));
                pricingDetailsNewList.add(buildPricingDetailRemainingAmount(paidAmount, totalAmount));
            }
            response.getTotalpricing().setDetails(pricingDetailsNewList);
        }
    }

    private PricingDetails buildPricingDetailRemainingAmount(double paidAmount, double totalAmount) {
        PricingDetails pricingDetail = new PricingDetails();
        pricingDetail.setKey("REMAINING_AMOUNT_TO_PAY");
        pricingDetail.setLabel("Remaining Amount To Be Paid");
        pricingDetail.setAmount(totalAmount - paidAmount);
        pricingDetail.setType("sum");
        return pricingDetail;
    }

    private PricingDetails buildPricingDetailPaidAmount(double paidAmount, double totalAmount) {
        PricingDetails pricingDetail = new PricingDetails();
        pricingDetail.setKey("TOTAL_PAID");
        pricingDetail.setLabel("Total Paid");
        pricingDetail.setAmount(paidAmount);
        pricingDetail.setType("sum");
        return pricingDetail;
    }

    // For GI, we explicitly add charity if selected in the pricingDetails
    private void addCharityInPricingDetails(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        if (thankYouResponse.getTotalpricing() != null && CollectionUtils.isNotEmpty(thankYouResponse.getTotalpricing().getDetails())
                && isCharitySelectedByUser(persistedData)) {
            AddOnNode charity;
            boolean isCharityV2=false;
            if(persistedData.getRecommendedAddOns().containsKey(CHARITY_ID_V2)) {
                charity = persistedData.getRecommendedAddOns().get(CHARITY_ID_V2);
                isCharityV2 = true;
            } else {
                charity = persistedData.getRecommendedAddOns().get(CHARITY_ID);
            }
            if (charity != null && charity.getPrice() != null && charity.getPrice() > 0.0d) {
                PricingDetails charityPricingDetail = new PricingDetails();
                charityPricingDetail.setAmount(charity.getPrice());
                charityPricingDetail.setKey(isCharityV2 ? CHARITY_KEY_V2:CHARITY_KEY);
                charityPricingDetail.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                charityPricingDetail.setLabel(CHARITY_LABEL);
                thankYouResponse.getTotalpricing().getDetails().add(charityPricingDetail);
            }
        }
    }

    private boolean isCharitySelectedByUser(PersistedMultiRoomData persistedData) {
        boolean isCharitySelected = false;
        if(persistedData != null && MapUtils.isNotEmpty(persistedData.getRecommendedAddOns())) {
            if(persistedData.getRecommendedAddOns().containsKey(CHARITY_ID)) {
                isCharitySelected = persistedData.getRecommendedAddOns().get(CHARITY_ID).isCharityAddOnSelected();
            }
            if(persistedData.getRecommendedAddOns().containsKey(CHARITY_ID_V2)) {
                isCharitySelected = persistedData.getRecommendedAddOns().get(CHARITY_ID_V2).isCharityAddOnSelected();
            }
        }
        return isCharitySelected;
    }

    private ClmCashback buildCLMCashbackData(ClmPersuasion clmPersuasion, ThankYouResponse thankYouResponse) {
        if (clmPersuasion == null || StringUtils.isEmpty(clmPersuasion.getBenefitAmount())) {
            return null;
        }
        Double goCashAmt = Double.valueOf(clmPersuasion.getBenefitAmount());
        if (goCashAmt.compareTo(0.0d) > 0) {
            ClmCashback clmCashback = new ClmCashback();
            clmCashback.setBenefitAmount(goCashAmt);
            clmCashback.setImageUrl("https://gos3.ibcdn.com/gocashTY-1623233750.png");
            clmCashback.setTitle(String.format("Your ₹ %s goCash Credit", clmPersuasion.getBenefitAmount()));
            if (thankYouResponse.getBookingDetails() != null && StringUtils.isNotEmpty(thankYouResponse.getBookingDetails().getBookingId())) {
                clmCashback.setGoData(String.format("{\"tid\":\"%s\"}", thankYouResponse.getBookingDetails().getBookingId()));
            }
            clmCashback.setMaxRetry(2);
            clmCashback.setRetryIn(1);
            clmCashback.setStatus("Processing");
            clmCashback.setStatusColor("#EFA410");
            clmCashback.setStatusMsg("Expect credit soon");
            clmCashback.setSubtitle("Applicable only on confirmed booking");
            clmCashback.setStatusColor("#777777");
            clmCashback.setTag(701);
            return clmCashback;
        }
        return null;
    }

    private ClmCashback buildTotalCashbackData(PersistedMultiRoomData persistedData, ThankYouResponse thankYouResponse) {

        if (persistedData == null || (thankYouResponse != null && thankYouResponse.getBookingDetails()!= null
                && thankYouResponse.getBookingDetails().getStatus().equals(BookingStatus.FAILED))) {
            return null;
        } else if(utility.isExperimentTrue(persistedData.getExpData(), GOTRIBE3_POKUS_EXP_KEY) && persistedData.getBlackInfo() != null) { // If latest app and goTribe user, then total cashback node is blocked.
            return null;
        }

        Double goCashAmtClm = persistedData.getClmPersuasion()!= null ? Double.valueOf(persistedData.getClmPersuasion().getBenefitAmount()) : 0;
        Double goCashAmtLoyalty = persistedData.getLoyaltyMessageResponse()!= null ? Double.valueOf(persistedData.getLoyaltyMessageResponse().getWalletEarn()) : 0;
        Double goCashAmtManthan = (double)getManthanGocashValueValidateCoupon(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown());
        Double finalAmount = (goCashAmtManthan + goCashAmtLoyalty + goCashAmtClm);
        String imageUrl = "https://gos3.ibcdn.com/thank_you_default-1691572757.png";

        if (persistedData.getLoyaltyMessageResponse() != null && persistedData.getLoyaltyMessageResponse().getTierName() != null) {
            imageUrl = persistedData.getLoyaltyMessageResponse().getTierName().contains("goTribe Star") ?  "https://gos3.ibcdn.com/thank_you_star-1691572717.png" : "https://gos3.ibcdn.com/thank_you_superstar-1691572735.png";
        }

        if (finalAmount > 0) {
            ClmCashback totalCashback = new ClmCashback();
            totalCashback.setBenefitAmount(finalAmount);
            totalCashback.setImageUrl(imageUrl);
            totalCashback.setTitle(String.format("Adding ₹%s goCash to your wallet.",finalAmount.intValue()));
            totalCashback.setSubtitle("Use it to book your next flight,hotel or cab.Club it with our discounts to save max!");
            return totalCashback;
        }
        return null;
    }

    private void buildMyPartnerFareHoldData(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {

        logger.debug("buildMyPartnerFareHoldData for thankYouResponse{} ", thankYouResponse);
        //building Complete Payment Card for BookNowFareHold if booking status is succcess and user is eligible for booking
        boolean isMpFareHoldFlow = persistedMultiRoomData.isBookNowFareHold() && persistedMultiRoomData.getMpaFareHoldStatus()!=null &&
                persistedMultiRoomData.getMpaFareHoldStatus().isHoldEligible() && persistedMultiRoomData.getMpaFareHoldStatus().isEligibleForHoldBooking();

        if (isMpFareHoldFlow && BookingStatus.SUCCESS.equals(thankYouResponse.getBookingDetails().getStatus())) {

            thankYouResponse.getBookingDetails().setCompletePaymentCard(new CompletePaymentCard());
            List<AmountDetail> amountBreakup = null;
            Long expiry = persistedMultiRoomData.getMpaFareHoldStatus().getExpiry();
            //In case of success booking there can be a case when we have already built success card but in case of fare hold
            //we need to show only complete payment card and need to built amount break up for that
            //If success card is available fetch amount breakup from it else building the data
            //set success card to null

            amountBreakup = buildAmountBreakup(thankYouResponse, persistedMultiRoomData);

            AmountDetail pendingAmount = amountBreakup.stream().filter(amountDetail -> polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_BREAKUP_PENDING).equalsIgnoreCase(amountDetail.getTitle()))
                    .findFirst().orElse(null);
            if(expiry==null || pendingAmount==null) {
                logger.error("MPFAREHOLD Either expiry or pending amount is null on thank-yopu page");
                logger.debug("Pending Amount {}  Expiry {} ", pendingAmount, expiry);
                return;
            }
            //there can be a case when e have already set successBookingCardInfo in the flow as Booking Statuis is Success hence making it null as only one card is required
            thankYouResponse.getBookingDetails().setSuccessBookingCardInfo(null);

            thankYouResponse.getBookingDetails().getCompletePaymentCard().setAmountBreakup(amountBreakup);
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setHeading(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_THANK_YOU_HEADING));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setSubHeading(MessageFormat.format(
                    polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_THANK_YOU_SUB_HEADING),String.format("%,.2f",pendingAmount.getAmount()),
                    dateUtil.convertEpochToDateTime(expiry,dateUtil.DD_MMM_hh_mm_a)));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setPaymentHeading(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_THANK_YOU_PAYMENT_HEADING));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setCtaText(MessageFormat.format(
                    polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_THANK_YOU_CTA_TEXT),String.format("%,.2f",pendingAmount.getAmount())));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(), persistedMultiRoomData.getBookingMetaInfo()));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setExpiry(expiry);
            //Building paid amount as equal to booking amount
            if(thankYouResponse.getPaidAmount()!=null) {
                thankYouResponse.getPaidAmount().setAmount((double)persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount());
            }
            else {
                AmountDetail partialAmountPaid = null;
                partialAmountPaid = new AmountDetail();
                partialAmountPaid.setTitle(AMOUNT_PAID);
                partialAmountPaid.setAmount((double)persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount());
                partialAmountPaid.setCurrency(Constants.DEFAULT_CUR_INR); //MyPartner is only for Indian funnel
            }
        }
    }
    private void buildGroupBookingSpecificNodes(HotelResult hotelResult, PersistedMultiRoomData persistedData, HotelInfo hotelInfo) {
        if (isGroupBookingFunnel(persistedData)) {
            hotelResult.setGroupBookingHotel(hotelInfo.isGroupBookingHotel());
            hotelResult.setGroupBookingPrice(hotelInfo.isGroupBookingPrice());
            if (persistedData.getTotalDisplayFare() != null && persistedData.getTotalDisplayFare().getTotalRoomCount() != null &&
                    CollectionUtils.isNotEmpty(persistedData.getHotelList().get(0).getTariffInfoList()) && persistedData.getHotelList().get(0).getTariffInfoList().stream().anyMatch(PersistedTariffInfo::isBaseRoom)) {
                Integer roomCount = persistedData.getTotalDisplayFare().getTotalRoomCount();
                String roomName = persistedData.getHotelList().get(0).getTariffInfoList().stream().filter(PersistedTariffInfo::isBaseRoom).findFirst().get().getRoomTypeName();
                String roomText = roomCount.toString() + SPACE_X_SPACE + roomName;
                hotelResult.setRoomText(roomText);
            }
        }
    }

    private void buildPendingAmount(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        AmountDetail pendingAmount = null;
        if (persistedMultiRoomData.getAmountLabels() == null) {
            return;
        }
        Optional<ThankYouAmountLabel> totalPendingAmount = persistedMultiRoomData.getAmountLabels().stream().filter(thankYouAmountLabel ->
                AMOUNT_LABEL_PARTIAL_AMOUNT_LEFT.equalsIgnoreCase(thankYouAmountLabel.getLabelType())).findFirst();
        if (totalPendingAmount.isPresent()) {
            double pgCharges = 0;
            if(persistedMultiRoomData.getPaymentInfo() != null) {
                pgCharges = persistedMultiRoomData.getPaymentInfo().getPgCharges();
            }
            pendingAmount = new AmountDetail();
            pendingAmount.setTitle(PENDING_AMOUNT);
            pendingAmount.setAmount(Utility.round(Math.round(totalPendingAmount.get().getAmount() + pgCharges), 0));
            pendingAmount.setCurrency(totalPendingAmount.get().getAmountText().split("\\s+")[1]);
            pendingAmount.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(), persistedMultiRoomData.getBookingMetaInfo()));
            thankYouResponse.setPendingAmount(pendingAmount);
            updatePgChargesInPaidAmount(thankYouResponse.getPendingAmount(), persistedMultiRoomData);
        }
    }

    private AmountDetail buildPartialAmountPaid(PersistedMultiRoomData persistedMultiRoomData) {
        AmountDetail partialAmountPaid = null;
        Optional<ThankYouAmountLabel> partialAmountCollected = persistedMultiRoomData.getAmountLabels().stream().filter(thankYouAmountLabel ->
                AMOUNT_LABEL_PARTIAL_AMOUNT_PAID.equalsIgnoreCase(thankYouAmountLabel.getLabelType())).findFirst();
        if (partialAmountCollected.isPresent()) {
            double pgCharges = 0;
            if(persistedMultiRoomData.getPaymentInfo() != null) {
                pgCharges = persistedMultiRoomData.getPaymentInfo().getPgCharges();
            }
            partialAmountPaid = new AmountDetail();
            partialAmountPaid.setTitle(AMOUNT_PAID);
            partialAmountPaid.setAmount(Utility.round(Math.round(partialAmountCollected.get().getAmount() + pgCharges), 0));
            partialAmountPaid.setCurrency(partialAmountCollected.get().getAmountText().split("\\s+")[1]);
        }
        return partialAmountPaid;
    }

    private void buildBPGText(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList()) && persistedMultiRoomData.getHotelList().get(0).getHotelInfo() != null &&
                persistedMultiRoomData.getHotelList().get(0).getHotelInfo().isGroupBookingHotel() && thankYouResponse.getRooms() != null) {
            thankYouResponse.getRooms().setBpgText(BEST_PRICE_GUARANTEE);
        }
    }

    public void buildPanCardGuidelines(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if (persistedMultiRoomData.getBookingResponse() !=null && BookingStatus.SUCCESS.name().equalsIgnoreCase(persistedMultiRoomData.getBookingResponse().getStatus())
                && DEFAULT_SITE_DOMAIN.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()))
                && persistedMultiRoomData.getAvailReqBody() != null && !Constants.DOM_COUNTRY.equalsIgnoreCase(persistedMultiRoomData.getAvailReqBody().getCountryCode())
                && persistedMultiRoomData.isPanCardRequired() && !persistedMultiRoomData.isPanAvailable()  && StringUtils.isBlank(persistedMultiRoomData.getPanCardNumber())
                ) {
            PanCard pancard = new PanCard();
//            pancard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAN_CARD_DETAILS_TITLE));
//            pancard.setDesc(polyglotService.getTranslatedData(ConstantsTranslation.PAN_CARD_DETAILS_DESCRIPTION));
//            pancard.setIcon(panCardIconUrl);
//            pancard.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(), persistedMultiRoomData.getBookingMetaInfo()));
            pancard.setPanWarning("As per RBI Advisory, it is recommended to submit PAN Card details for International Travel. Submit your PAN details on MyTrips.");
            thankYouResponse.setPanCard(pancard);
        }
    }

    private void updatePgChargesInPaidAmount(AmountDetail paidAmount, PersistedMultiRoomData persistedData) {
        if(paidAmount != null && persistedData.getPaymentInfo() != null)
        {
            double pgCharges = persistedData.getPaymentInfo().getPgCharges();
            if(pgCharges > 0.0) {
                String currSign = paidAmount.getCurrency().equalsIgnoreCase("INR") ? "Rs" : paidAmount.getCurrency();
                Integer roundedPgCharges = (int) Math.round(pgCharges);
                paidAmount.setSubtitle(polyglotService.getTranslatedData(ConstantsTranslation.PG_CHARGES_TEXT).replace("{currency}", currSign).replace("{amount}",roundedPgCharges.toString()));
            }
        }

    }

    private List<InsuranceInfo> buildInsuranceInfo(AddOnDetail addOnDetail) {
        List<InsuranceInfo> list = new ArrayList<>();
        if (addOnDetail!=null && CollectionUtils.isNotEmpty(addOnDetail.getAddOnNode())) {
            Optional<AddOnNode> insuranceAddOn = addOnDetail.getAddOnNode().stream().filter(e-> AddOnType.INSURANCE.toString().equalsIgnoreCase(e.getAddOnType())).findFirst();
            if (insuranceAddOn.isPresent()) {
                AddOnNode addOnNode = insuranceAddOn.get();
                if (addOnNode.getInsuranceData()!=null && CollectionUtils.isNotEmpty(addOnNode.getInsuranceData().getTmInsuranceAddOns())) {
                    for (TmInsuranceAddOns insurance : addOnNode.getInsuranceData().getTmInsuranceAddOns()) {
                        if (StringUtils.isNotBlank(insurance.getTmProBookingId())) {
                            InsuranceInfo i = new InsuranceInfo();
                            //TODO : Have to confirm with hermes for below hardcoded values
                            i.setHeaderText("Travel peacefully with Medical & travel inconvenience");
                            i.setSubText("T&C");
                            i.setViewAllText(insurance.getTncText());
                            i.setViewAllUrl(insurance.getTncLink());
                            list.add(i);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list))
            return list;
        return null;
    }

    private  void buildRtbChatCard(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData){
        if(persistedData.isRequestToBook() && !persistedData.isRtbPreApproved()){
            RtbChatCard rtbChatCard = new RtbChatCard();
            rtbChatCard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RTB_CHAT_CARD_TITLE));
            rtbChatCard.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.RTB_CHAT_CARD_SUBTITLE));
            rtbChatCard.setActionText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_CHAT_CARD_ACTION_TEXT));
            rtbChatCard.setIngoHotelId(persistedData.getHotelList().get(0).getHotelInfo().getGdsHotelCode());
            thankYouResponse.setRtbChatCard(rtbChatCard);
        }
    }

    private void buildMetaChannelInfo(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        if (persistedData == null || persistedData.getAvailReqBody() == null || persistedData.getAvailReqBody()
                                                                                             .getMetaChannelInfo() == null) {
            return;
        }
        thankYouResponse.setMetaChannelInfo(persistedData.getAvailReqBody().getMetaChannelInfo());
    }

    private void buildLogData(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData){

        try{
            if(persistedData!= null ){
                Map<String,String> logData = new HashMap<>();
                thankYouResponse.setLogData(logData);
                if( persistedData.getTotalDisplayFare()!= null && persistedData.getTotalDisplayFare().getDisplayPriceBreakDown()!= null) {
                    logData.put("hotelierDiscount", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getMmtDiscount()));
                    logData.put("hotelierServiceCharge", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getHotelTax()));
                    logData.put("wallet", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getWallet()));
                    logData.put("mmtServiceCharge", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getMmtServiceCharge()));

                }

                if(MapUtils.isNotEmpty(persistedData.getCouponInfo()) && persistedData.getCouponInfo().containsKey(CouponStatus.REDEEMED) && CollectionUtils.isNotEmpty(persistedData.getCouponInfo().get(CouponStatus.REDEEMED))){
                    logData.put("couponDiscount", persistedData.getCouponInfo().get(CouponStatus.REDEEMED).get(0).getAmount());
                }

                if(CollectionUtils.isNotEmpty(persistedData.getTravelerInfoList()) ){
                    logData.put("travellerIsdCode", persistedData.getTravelerInfoList().get(0).getIsdCode());
                }

            }
        }catch (Exception ex){
            logger.error("Error in populating error data : {}", ex.getMessage(), ex);
        }
    }

    private void buildCardDetails(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        if (MapUtils.isEmpty(thankYouCards))
            return;
        List<CardData> thankYouCardList = new ArrayList<>();
        for (String cardId : thankYouCards.keySet()) {
            CardData thankyouCard = gson.fromJson(gson.toJson(thankYouCards.get(cardId)), CardData.class);

            if(StringUtils.isNotBlank(persistedData.getSpiderNextBookingDiscountMessage())) {
                if (cardId.equalsIgnoreCase("NEXTBOOKINGDISCOUNT")){
                    thankyouCard.getCardInfo().setTitleText(persistedData.getSpiderNextBookingDiscountMessage());
                    if (null == persistedData.getAvailReqBody().getTrafficSource()) {
                        thankyouCard.getCardInfo().getCardPayload().setMetaPersuasion(null);
                        thankyouCard.getCardInfo().getCardPayload().setTitle(null);
                        thankYouCardList.add(thankyouCard);
                    }
                } else if (cardId.equalsIgnoreCase("NEXTBOOKINGSCRATCH")) {
                    thankyouCard.getCardInfo().getCardPayload().setScratchText(persistedData.getSpiderNextBookingDiscountMessage());
                    thankYouCardList.add(thankyouCard);
                }
            }
            boolean isMyPartnerRequest = (persistedData.getAvailReqBody()!=null) && Utility.isMyPartnerRequest(persistedData.getAvailReqBody().getProfileType(),persistedData.getAvailReqBody().getSubProfileType());
            if (isGroupBookingFunnel(persistedData) && groupBookingCardKeys.contains(cardId) && !isMyPartnerRequest) {
                thankYouCardList.add(thankyouCard);
            }

            if (isGroupBookingFunnel(persistedData) && isMyPartnerRequest && groupBookingCardKeys.contains(cardId) && !POST_BOOKING_CARD.equalsIgnoreCase(cardId)) {
                thankYouCardList.add(thankyouCard);
            }

            if(CollectionUtils.isNotEmpty(thankYouCardList)) {
                thankYouResponse.setCardData(thankYouCardList);
            }
        }
    }

    private boolean isGroupBookingFunnel(PersistedMultiRoomData persistedData) {
        return persistedData.getAvailReqBody() != null && StringUtils.equalsIgnoreCase(persistedData.getAvailReqBody().getFunnelSource(), FUNNEL_SOURCE_GROUP_BOOKING);
    }

    private void updateInclusions(ThankYouResponse thankYouResponse, boolean isAllInclusion) {
        if (thankYouResponse != null && thankYouResponse.getRooms() != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(thankYouResponse.getRooms().getRatePlanList()) && !isAllInclusion) {
            for (BookedRatePlan ratePlan : thankYouResponse.getRooms().getRatePlanList()) {
                List<BookedInclusion> inclusions = ratePlan.getInclusions();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusions) && inclusions.size() > maxInclusionsThankyou) {
                    inclusions.sort(Comparator.comparing(i -> i.getSegmentIdentifier(), Comparator.nullsLast(Comparator.naturalOrder())));
                    inclusions = inclusions.subList(0, maxInclusionsThankyou);
                    ratePlan.setInclusions(inclusions);
                }
            }
        }
    }

    private Map<String, String> buildExperimentData(PersistedMultiRoomData persistedData) {
        if (MapUtils.isNotEmpty(persistedData.getExpData()))
            return persistedData.getExpData();
        return null;
    }

    private void populateAmountBreakup(ThankYouResponse thankYouResponse, PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {

        List<AmountDetail> amountBreakup = buildAmountBreakup(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        AmountDetail totalAmount = null;
        if (CollectionUtils.isNotEmpty(amountBreakup))
            totalAmount = amountBreakup.stream().filter(amountDetail -> polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_BREAKUP_TOTAL).equalsIgnoreCase(amountDetail.getTitle()))
                    .findFirst().orElse(null);
        if (thankYouResponse.getTotalAmount() == null && totalAmount != null)
            thankYouResponse.setTotalAmount(totalAmount);
        if (BookingStatus.SUCCESS.equals(thankYouResponse.getBookingDetails().getStatus())) {
            thankYouResponse.getBookingDetails().setSuccessBookingCardInfo(new SuccessBookingCardInfo());
            thankYouResponse.getBookingDetails().getSuccessBookingCardInfo().setAmountBreakup(amountBreakup);
        }
        if (amountBreakup != null && !amountBreakup.isEmpty()){
            AmountDetail deducedAmount = amountBreakup.stream().filter(amountDetail -> polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_BREAKUP_DEDUCTED).equalsIgnoreCase(amountDetail.getTitle()))
                    .findFirst().orElse(null);
            if (null != deducedAmount)
                thankYouResponse.setPaidAmount(deducedAmount);
        }

    }


    private BookedRooms buildBookedRooms(PersistedMultiRoomData persistedData, ThankYouResponse thankYouResponse) {
        BookedRooms rooms = new BookedRooms();
        Map<String, Integer> roomBedCountMap = new HashMap<>();
        roomBedCountMap.put(Constants.SELLABLE_ROOM_TYPE,0);
        roomBedCountMap.put(Constants.SELLABLE_BED_TYPE,0);
        rooms.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(), persistedData.getBookingMetaInfo()));
        List<BookedRatePlan> ratePlanList = persistedData.getHotelList().get(0).getTariffInfoList().stream()
                .map(persistedTariffInfo -> {
                    BookedRatePlan bookedRatePlan = new BookedRatePlan();
                    bookedRatePlan.setRoomName(persistedTariffInfo.getRoomTypeName());

                    //populate room occupancy
                    int adultCount = 0, childCount = 0, roomCount = 0;
                    List<String> childAges = new ArrayList<>();
                    BookedChild bookedChild = null;
                    if (CollectionUtils.isNotEmpty(persistedTariffInfo.getStayDetails().getRoomStayCandidates())) {
                        for (RoomStayCandidateReqBody roomStayCandidate : persistedTariffInfo.getStayDetails().getRoomStayCandidates()) {
                            roomCount++;
                            adultCount += roomStayCandidate.getAdult();
                            if (roomStayCandidate.getChild() != null) {
                                if (null != roomStayCandidate.getChild().getCount())
                                    childCount += roomStayCandidate.getChild().getCount();
                                if (CollectionUtils.isNotEmpty(roomStayCandidate.getChild().getAges()))
                                    childAges.addAll(roomStayCandidate.getChild().getAges().stream().map(String::valueOf).collect(Collectors.toList()));
                            }
                        }
                    }
                    if (childCount > 0) {
                        bookedChild = new BookedChild();
                        bookedChild.setCount(childCount);
                        bookedChild.setAges(childAges);
                    }
                    BookedOccupancy occupancy = new BookedOccupancy();
                    occupancy.setAdult(adultCount);
                    if (null != bookedChild)
                        occupancy.setChild(bookedChild);

                    bookedRatePlan.setOccupancy(occupancy);
                    bookedRatePlan.setRoomCount(roomCount);
                    BNPLVariant bnplVariant = persistedData.getTotalDisplayFare()!=null ? persistedData.getTotalDisplayFare().getBnplVariant() : null;
                    bookedRatePlan.setCancellationPolicy(buildCancellationPolicy(persistedTariffInfo, bnplVariant));
                    bookedRatePlan.setInclusions(buildInclusions(persistedTariffInfo));
                    if (utility.isSPKGExperimentOn(persistedData.getExpData()) && persistedTariffInfo.isPackageRatePlan()) {
                        bookedRatePlan.setPackageRateAvailable(true);
                        bookedRatePlan.setType(elitePackageType);
                        bookedRatePlan.setHighlightImage(elitePackageIconUrl);
                        utility.transformInclusionsForPackageRatePlan(bookedRatePlan.getInclusions());
                    }
                    if (utility.isShowOccassionPackagesPlanExperimentOn(persistedData.getExpData()) && StringUtils.isNotBlank(persistedData.getOccasionPackageType())) {
                        bookedRatePlan.setType(persistedData.getOccasionPackageType());
                        bookedRatePlan.setHighlightImage(persistedData.getHighlightOccassionImageUrl());
                    }
                    if(Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(persistedTariffInfo.getSellableType())){
                        roomBedCountMap.put(Constants.SELLABLE_BED_TYPE, roomBedCountMap.get(Constants.SELLABLE_BED_TYPE)+roomCount);
                    }else{
                        roomBedCountMap.put(Constants.SELLABLE_ROOM_TYPE, roomBedCountMap.get(Constants.SELLABLE_ROOM_TYPE)+roomCount);
                    }

                    if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(persistedData.getAvailReqBody().getIdContext()) && persistedTariffInfo.getDisplayFare()!=null && persistedTariffInfo.getDisplayFare().getCorpMetaData()!=null){
                        bookedRatePlan.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(persistedTariffInfo.getDisplayFare().getCorpMetaData()));
                        bookedRatePlan.setCorpRateTags(commonResponseTransformer.buildTags(persistedTariffInfo.getDisplayFare().getCorpMetaData().getTags()));
                    }
                    bookedRatePlan.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(persistedTariffInfo.getCancellationTimeline()));
                    bookedRatePlan.setBasePlan(persistedTariffInfo.isBaseRoom());
                    if (persistedTariffInfo.getRoomDetails() != null && CollectionUtils.isNotEmpty(persistedTariffInfo.getRoomDetails().getImages())) {
                        bookedRatePlan.setImages(buildImagesUrl(persistedTariffInfo.getRoomDetails().getImages()));
                    }
                    if (persistedData.getHotelList().get(0).getHotelInfo() != null) {
                        bookedRatePlan.setRatePlanName(utility.getRatePlanName(persistedTariffInfo.getMealPlans(), bookedRatePlan.getCancellationPolicy(), thankYouResponse.getHotelDetails().getSellableType(), persistedData.getHotelList().get(0).getHotelInfo().getListingType(), persistedData.getExpData()));
                    }
                    return bookedRatePlan;
                }).collect(Collectors.toList());
        rooms.setRatePlanList(ratePlanList);
        if (CollectionUtils.isNotEmpty(ratePlanList) && thankYouResponse.getHotelDetails() != null) {
            int totalRooms = ratePlanList.stream().mapToInt(rp -> rp.getRoomCount()).sum();
            setSellableTypeAndEntirePropertyText(persistedData, thankYouResponse, roomBedCountMap, totalRooms);

        }
        return rooms;
    }

    private LinkedList<String> buildImagesUrl(LinkedList<String> images) {
        if (CollectionUtils.isNotEmpty(images)) {
            LinkedList<String> imagesList = new LinkedList<>();
            images.forEach(image -> {
                if (image != null) imagesList.add(image.startsWith("http") ? image : "https:" + image);
            });
            return imagesList;
        }
        return null;
    }

    private boolean isCorp(PersistedTariffInfo persistedTariffInfo) {
        return persistedTariffInfo != null && persistedTariffInfo.getDisplayFare() != null && persistedTariffInfo.getDisplayFare().getCorpMetaData() != null;
    }

    private void setSellableTypeAndEntirePropertyText(PersistedMultiRoomData persistedData, ThankYouResponse thankYouResponse, Map<String, Integer> roomBedCountMap, int totalRooms) {
        Tuple<String, String> roomBedTuple = utility.getGuestRoomKeyValue(roomBedCountMap,
                persistedData.getHotelList().get(0).getHotelInfo().getPropertyType(),
                persistedData.getHotelList().get(0).getHotelInfo().getListingType());
        thankYouResponse.getHotelDetails().setGuestRoomKey(roomBedTuple.getX());
        thankYouResponse.getHotelDetails().setGuestRoomValue(roomBedTuple.getY());
        thankYouResponse.getHotelDetails().setEntirePropertyText(roomBedTuple.getY());
        thankYouResponse.getHotelDetails().setEntireProperty(true);
        if (totalRooms > 1) {
            if(roomBedCountMap.get(Constants.SELLABLE_BED_TYPE) == 0)
                thankYouResponse.getHotelDetails().setSellableType("Rooms");
            else
                thankYouResponse.getHotelDetails().setSellableType("Beds");
        }
        else{
            if(roomBedCountMap.get(Constants.SELLABLE_BED_TYPE) == 0)
                thankYouResponse.getHotelDetails().setSellableType("Room");
            else
                thankYouResponse.getHotelDetails().setSellableType("Bed");
        }
    }

    private String getMyTripsDeepLink(String myTripsDeeplink, BookingMetaInfo bookingMetaInfo) {
        HashMap<String, String> params = new HashMap<>();
        if (null != bookingMetaInfo && StringUtils.isNotBlank(bookingMetaInfo.getBookingId())) {
            params.put(Constants.BOOKING_ID, bookingMetaInfo.getBookingId());
        }
        if (bookingMetaInfo != null) {
            boolean isCorp = Constants.CORP_ID_CONTEXT.equalsIgnoreCase(bookingMetaInfo.getRequestorIdContext());
            params.put(Constants.CORP_ID_CONTEXT, String.valueOf(isCorp));
        }
        return Utility.appendQueryParamsInUrl(myTripsDeeplink, params);
    }

    private List<BookedInclusion> buildInclusions(PersistedTariffInfo persistedTariffInfo) {
        List<MealPlan> bookedMealPlan = persistedTariffInfo.getMealPlans();
        List<Inclusion> bookedInclusionList = persistedTariffInfo.getInclusions();
        return utility.transformInclusions(bookedMealPlan, bookedInclusionList, mealPlanMapPolyglot ,
                persistedTariffInfo.getSupplierDetails()!= null ?  persistedTariffInfo.getSupplierDetails().getSupplierCode():"", null,null, MapUtils.EMPTY_SORTED_MAP,null,null,null,null,null,0);

    }

    private BookedCancellationPolicy buildCancellationPolicy(PersistedTariffInfo persistedTariffInfo, BNPLVariant bnplVariant) {
        return utility.transformCancellationPolicy(persistedTariffInfo.getCancelPenaltyList(), false, bnplVariant, null, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), null, persistedTariffInfo.getCancellationTimeline());
    }

    private List<Traveller> buildTravellersDetails(PersistedMultiRoomData persistedData) {
        if (persistedData.getTravelerInfoList() == null) {
            return null;
        }
        List<Traveller> travellers = persistedData.getTravelerInfoList().stream()
                .map(travelerInfo -> {
                    Traveller traveller = new Traveller();
                    traveller.setFirstName(travelerInfo.getFirstName());
                    traveller.setLastName(travelerInfo.getLastName());
                    traveller.setTitle(travelerInfo.getTitle());
                    return traveller;
                }).collect(Collectors.toList());
        return travellers;
    }

    private void buildGSTDetails(PersistedMultiRoomData persistedMultiRoomData, ThankYouResponse thankYouResponse){
       if (persistedMultiRoomData.getTravelerInfoList() == null) {
           return;
        }
        TravelerInfo masterTraveler = persistedMultiRoomData.getTravelerInfoList()
                .stream()
                .filter(travelerInfo -> travelerInfo.isMasterPax())
                .findFirst()
                .orElse(null);
        if(masterTraveler != null && StringUtils.isNotBlank(masterTraveler.getRegisteredGstinNum())){
            thankYouResponse.setGstnExist(true);
        }

    }

    private BookingDetails buildBookingDetails(PersistedMultiRoomData persistedData) {
        BookingDetails bookingDetails = new BookingDetails();
        bookingDetails.setBookingId(persistedData.getBookingMetaInfo().getBookingId());
        bookingDetails.setInsuranceInfo(buildInsuranceInfo(persistedData.getAddOnInfo()));
        bookingDetails.setPnr(getPNR(persistedData));
        bookingDetails.setBookerInfo(buildBookerInfo(persistedData));
        bookingDetails.setCheckInDate(persistedData.getAvailReqBody().getCheckin());
        if(checkIfSameDayBooking(persistedData)){
            bookingDetails.setCheckOutDate(persistedData.getAvailReqBody().getCheckin());
            bookingDetails.setCheckInTime("Day Use");
            bookingDetails.setCheckOutTime("Day Use");
        }else {
            bookingDetails.setCheckOutDate(persistedData.getAvailReqBody().getCheckout());
            if (CollectionUtils.isNotEmpty(persistedData.getHotelList()) && persistedData.getHotelList().get(0).getHotelInfo() != null) {
                bookingDetails.setCheckInTime(persistedData.getHotelList().get(0).getHotelInfo().getCheckInTime());
                bookingDetails.setCheckOutTime(persistedData.getHotelList().get(0).getHotelInfo().getCheckOutTime());
            }
        }
        bookingDetails.setCheckInPolicyDesc(getCheckInPolicyDescription(persistedData));
        bookingDetails.setPaymentMode(persistedData.getAvailReqBody().getPayMode());
        bookingDetails.setBlackRegSuccess(persistedData.isBlackRegSuccess());
        bookingDetails.setRequestToBook(persistedData.isRequestToBook());
        bookingDetails.setRtbPreApproved(persistedData.isRtbPreApproved());
        bookingDetails.setRtbAutoCharge(persistedData.isRtbAutoCharge());

        if(persistedData.getTimeOfBooking()!=null)
            bookingDetails.setBookingDate(persistedData.getTimeOfBooking());
        if (StringUtils.isNotEmpty(bookingDetails.getPnr()) && Constants.YET_TO_BE_GENERATED.equalsIgnoreCase(bookingDetails.getPnr())) {
            bookingDetails.setStatus(BookingStatus.PENDING);
        } else {
            bookingDetails.setStatus(buildBookingStatus(persistedData));
        }
        bookingDetails.setDonated(isDonationPresentInBookig(persistedData));
        bookingDetails.setNights(getBookingNights(bookingDetails.getCheckInDate(), bookingDetails.getCheckOutDate()));
        bookingDetails.setDoubleBlackValidated(persistedData.isDoubleBlackValidated());
        if (BookingStatus.PENDING.equals(bookingDetails.getStatus())) {
            bookingDetails.setPendingBookingCardInfo(buildPendingBookingCardInfo(persistedData));
        } else if (BookingStatus.FAILED.equals(bookingDetails.getStatus())) {
            bookingDetails.setFailedBookingCardInfo(buildFailedBookingCardInfo(persistedData));
        }
        return bookingDetails;
    }

    private boolean checkIfSameDayBooking(PersistedMultiRoomData persistedData){

        boolean sameDayUseBooking = false;
        try {
            for (PersistedTariffInfo tarrifInfo : persistedData.getHotelList().get(0).getTariffInfoList()) {
                if (StringUtils.isNotBlank(tarrifInfo.getRoomTypeName())) {
                    sameDayUseBooking = sameDayRoomNames.stream()
                            .anyMatch(e -> tarrifInfo.getRoomTypeName().toLowerCase().contains(e.toLowerCase()));
                    if (sameDayUseBooking) {
                        break;
                    }
                }
            }
        }catch(Exception e){
            logger.error("Error while checking for same day booking",e);
        }
        return sameDayUseBooking;
    }

    private Integer getBookingNights(String checkin, String checkout) {
        return Math.toIntExact(Math.abs(ChronoUnit.DAYS.between(LocalDate.parse(checkin), LocalDate.parse(checkout))));
    }

    private boolean isDonationPresentInBookig(PersistedMultiRoomData persistedData) {
        boolean donated = false;
        if (persistedData.getAddOnInfo() != null
                && CollectionUtils.isNotEmpty(persistedData.getAddOnInfo().getAddOnNode())) {
            Optional<AddOnNode> donatedAddon = persistedData.getAddOnInfo().getAddOnNode().stream()
                    .filter(addOnNode -> Constants.LOB_DONATION.equalsIgnoreCase(addOnNode.getLob()))
                    .findFirst();
            if (donatedAddon.isPresent())
                donated = true;
        }
        return donated;
    }

    private FailedBookingCardInfo buildFailedBookingCardInfo(PersistedMultiRoomData persistedData) {
        FailedBookingCardInfo failedBookingCardInfo = new FailedBookingCardInfo();
        failedBookingCardInfo.setCardNum(null != persistedData.getPaymentInfo() ? persistedData.getPaymentInfo().getCardNumber() : null);
        failedBookingCardInfo.setDetailPageDeepLink(getDetailPageDeepLink(persistedData));
        failedBookingCardInfo.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(), persistedData.getBookingMetaInfo()));
        failedBookingCardInfo.setDuration(polyglotService.getTranslatedData(ConstantsTranslation.FAILED_BOOKING_REFUND_DURATION));
        return failedBookingCardInfo;
    }

    private String getDetailPageDeepLink(PersistedMultiRoomData persistedData) {
        String detailPageDeepLink = null;
        if (null == persistedData || null == persistedData.getAvailReqBody()
                || CollectionUtils.isEmpty(persistedData.getAvailReqBody().getHotelIds()))
            return detailPageDeepLink;
        String hotelId = persistedData.getAvailReqBody().getHotelIds().get(0);
        String checkInDate = dateUtil.getDateFormatted(persistedData.getAvailReqBody().getCheckin(), DateUtil.YYYY_MM_DD, DateUtil.MMDDYYYY);
        String checkOutDate = dateUtil.getDateFormatted(persistedData.getAvailReqBody().getCheckout(), DateUtil.YYYY_MM_DD, DateUtil.MMDDYYYY);
        String countryCode = persistedData.getAvailReqBody().getCountryCode();
        String cityCode = StringUtils.isNotBlank(persistedData.getAvailReqBody().getLocationId()) ? persistedData.getAvailReqBody().getLocationId() : persistedData.getAvailReqBody().getCityCode();
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        for (RoomCriterion roomCriterion : persistedData.getAvailReqBody().getRoomCriteria()) {
            if (CollectionUtils.isNotEmpty(roomCriterion.getRoomStayCandidates()))
                roomStayCandidates.addAll(roomCriterion.getRoomStayCandidates());
        }
        String roomStayQualifier = Utility.buildRoomStayQualifierFromRoomStayCandidates(roomStayCandidates, tildeRequiredInRSQ());
        String currency = persistedData.getAvailReqBody().getCurrency();

        detailPageDeepLink = MessageFormat.format(getHotelDetailsRawDeepLinkUrl(), hotelId, checkInDate, checkOutDate, countryCode, cityCode, roomStayQualifier, currency);
        return detailPageDeepLink;
    }

    private PendingBookingCardInfo buildPendingBookingCardInfo(PersistedMultiRoomData persistedData) {
        PendingBookingCardInfo pendingBookingCardInfo = new PendingBookingCardInfo();
        pendingBookingCardInfo.setPendingTime(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_BOOKING_CONFIRMATION_TIME));
        pendingBookingCardInfo.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(), persistedData.getBookingMetaInfo()));
        pendingBookingCardInfo.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_CTATEXT));
        return pendingBookingCardInfo;
    }

    private BookerInfo buildBookerInfo(PersistedMultiRoomData persistedData) {
        BookerInfo bookerInfo = new BookerInfo();
        if (persistedData.getTravelerInfoList() == null || persistedData.getTravelerInfoList().isEmpty()){
           return bookerInfo;
        }
        String isdCode = "";
        if (StringUtils.isNotBlank(persistedData.getTravelerInfoList().get(0).getIsdCode())) {
            if (!persistedData.getTravelerInfoList().get(0).getIsdCode().startsWith("+")) {
                isdCode += "+";
            }
            isdCode += persistedData.getTravelerInfoList().get(0).getIsdCode() + "-";
        }

        String mobileNumber = persistedData.getTravelerInfoList().get(0).getMobileNumber();
        String email = persistedData.getTravelerInfoList().get(0).getEmail();
        if (StringUtils.isNotBlank(mobileNumber)) {
            bookerInfo.setMobileNum(isdCode + mobileNumber);
        }
        if (StringUtils.isNotBlank(email)) {
            bookerInfo.setEmailId(email);
        }

        return bookerInfo;
    }

    private String getCheckInPolicyDescription(PersistedMultiRoomData persistedData) {
        RatePolicy checkInPolicy = getCheckinPolicy(persistedData);
        if (checkInPolicy != null && StringUtils.isNotBlank(checkInPolicy.getShortDescription())) {
            if (".".equalsIgnoreCase(String.valueOf(checkInPolicy.getShortDescription().charAt(checkInPolicy.getShortDescription().length() - 1)))) {
                return checkInPolicy.getShortDescription().substring(0, checkInPolicy.getShortDescription().length() - 1);
            }
            return checkInPolicy.getShortDescription();
        }
        return null;
    }

    private BookingStatus buildBookingStatus(PersistedMultiRoomData persistedData) {
        if ((null!=persistedData.getBookingResponse() && Constants.BOOKING_STATUS_SUCCESS.equalsIgnoreCase(persistedData.getBookingResponse().getStatus()))) {
            if (Constants.BOOKING_NON_INSTANT_CONFIRMATION.equalsIgnoreCase(persistedData.getBookingResponse().getInstantConfirmation())
            || (persistedData.isRequestToBook() && !persistedData.isRtbPreApproved())) {
                return BookingStatus.PENDING;
            }
            return BookingStatus.SUCCESS;
        }
        return BookingStatus.FAILED;
    }

    private String getPNR(PersistedMultiRoomData persistedData) {
        String pnr = null;
        if (persistedData!= null && persistedData.getBookingResponse() != null &&
                CollectionUtils.isNotEmpty(persistedData.getBookingResponse().getHotelReservationIds())) {
            Optional<HotelReservationId> pnrReservation = persistedData.getBookingResponse().getHotelReservationIds().stream()
                    .filter(hotelReservationId -> Constants.BOOKING_PNR_KEY.equalsIgnoreCase(hotelReservationId.getType()))
                    .findFirst();
            if (pnrReservation.isPresent() && StringUtils.isNotBlank(pnrReservation.get().getValue()))
                pnr = pnrReservation.get().getValue();
        }
        return pnr;
    }

    private AmountDetail buildTotalAmount(PersistedMultiRoomData persistedData) {
        AmountDetail totalAmount = null;
        if (persistedData.getAmountLabels() == null) {
            return totalAmount;
        }
        Optional<ThankYouAmountLabel> totalPaidBooking = persistedData.getAmountLabels().stream().filter(thankYouAmountLabel ->
                Constants.AMOUNT_LABEL_TOTAL_AMOUNT.equalsIgnoreCase(thankYouAmountLabel.getLabelType())).findFirst();
        if (totalPaidBooking.isPresent()) {
            double pgCharges = 0;
            if(persistedData.getPaymentInfo() != null) {
                pgCharges = persistedData.getPaymentInfo().getPgCharges();
            }
            totalAmount = new AmountDetail();
            totalAmount.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_BREAKUP_TOTAL));
            totalAmount.setAmount(Utility.round(Math.round(totalPaidBooking.get().getAmount() + pgCharges), 0));
            totalAmount.setCurrency(totalPaidBooking.get().getAmountText().split("\\s+")[1]);
        }
        return totalAmount;
    }

    private List<AmountDetail> buildAmountBreakup(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        AmountDetail amountPaid = new AmountDetail();
        AmountDetail amountPending = new AmountDetail();
        AmountDetail amountTotal = new AmountDetail();
        double tcsAmount = persistedMultiRoomData.getTotalDisplayFare() != null && persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown() != null ?
                persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown().getTcsAmount() : 0.0;
        double paidAmount = 0, pendingAmount = 0;
        boolean isPah = false, isBNPL = false;
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getAmountLabels()))
            return null;
        for (ThankYouAmountLabel thankYouAmountLabel : persistedMultiRoomData.getAmountLabels()) {
            switch (thankYouAmountLabel.getLabelType()) {
                case Constants.AMOUNT_LABEL_MMT_WALLET:
                case Constants.AMOUNT_LABEL_OTHER_PAYMODES:
                case Constants.AMOUNT_LABEL_AMOUNT_CARD:
                    paidAmount += thankYouAmountLabel.getAmount();
                    break;
                case Constants.AMOUNT_LABEL_REMAINING_AMOUNT:
                    isBNPL = true;
                    pendingAmount += thankYouAmountLabel.getAmount() + tcsAmount;
                    break;
                case Constants.AMOUNT_LABEL_AMOUNT_HOTEL:
                    isPah = true;
                    pendingAmount += thankYouAmountLabel.getAmount();
                default:
                    break;
            }
        }

        boolean isMpFareHoldEligible = persistedMultiRoomData.isBookNowFareHold() && persistedMultiRoomData.getMpaFareHoldStatus()!=null
        && persistedMultiRoomData.getMpaFareHoldStatus().isHoldEligible() && persistedMultiRoomData.getMpaFareHoldStatus().isEligibleForHoldBooking();


//        if (isMpFareHoldEligible && (persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount()==0f)) {
//            logger.debug("MPFAREHOLD Zero Flow on thank-you page for amount Labels {}",persistedMultiRoomData.getAmountLabels());
//            paidAmount = 0;
//            ThankYouAmountLabel totalAmountLabel = persistedMultiRoomData.getAmountLabels().stream().filter(amountDetail -> Constants.AMOUNT_LABEL_TOTAL_AMOUNT.equalsIgnoreCase(amountDetail.getLabelType()))
//                    .findFirst().orElse(null);
//            //pending amount will be equal to total amount as this no payment has done in this flow.
//            pendingAmount = totalAmountLabel.getAmount();
//        }

        double pgCharges = 0;
        if(persistedMultiRoomData.getPaymentInfo() != null) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);
            pgCharges += persistedMultiRoomData.getPaymentInfo().getPgCharges();
            pgCharges /= conversionFactor;

        }
        paidAmount += pgCharges;

        String currency = null != persistedMultiRoomData.getBookingMetaInfo() ? persistedMultiRoomData.getBookingMetaInfo().getCurrencyCode() : "INR";
        String supplierCurrency = Utility.getHotelierCurrency(persistedMultiRoomData);
        String askedCurrency = Utility.getAskedCurrency(persistedMultiRoomData);

        amountPaid.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_BREAKUP_DEDUCTED));
        amountPaid.setAmount(Utility.round(paidAmount, 0));
        amountPaid.setCurrency(currency);
        amountPaid.setKey(AMOUNT_PAID_KEY);
        if(pgCharges > 0.0) {
            String currSign = currency.equalsIgnoreCase("INR") ? "Rs" : currency;
            Integer roundedPgCharges = (int) Math.round(pgCharges);
            amountPaid.setSubtitle(polyglotService.getTranslatedData(ConstantsTranslation.PG_CHARGES_TEXT).replace("{currency}", currSign).replace("{amount}",roundedPgCharges.toString()));
        }

        amountPending.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_BREAKUP_PENDING));
        amountPending.setAmount(Utility.round(pendingAmount, 0));
        amountPending.setCurrency(currency);
        amountPending.setKey(DUE_AMOUNT_KEY);

        if (isBNPL && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList())
                && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList())
                && persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline() != null) {
            if (persistedMultiRoomData.isBnplNewVariant()) {
                String translatedData = polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT);
                if (StringUtils.isNotBlank(translatedData)) {
                    amountPending.setSubtitle(MessageFormat.format(translatedData,
                            persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong()));
                }
            } else {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_BNPL_TEXT),
                        persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong()));
            }
            amountPending.setDueAmountPaymentDate( persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong() + " " + persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeTime());
        }
        else if (isBNPL && persistedMultiRoomData.getCancellationTimeline() != null
                && StringUtils.isNotBlank(persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong())) {
            if (persistedMultiRoomData.isBnplNewVariant()) {
                String translatedData = polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT);
                if (StringUtils.isNotBlank(translatedData)) {
                    amountPending.setSubtitle(MessageFormat.format(translatedData, persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()));
                }
            } else {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_BNPL_TEXT),
                        persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()));
            }
        }

        amountTotal.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_BREAKUP_TOTAL));
        amountTotal.setAmount(Utility.round(paidAmount + pendingAmount, 0));
        amountTotal.setCurrency(currency);
        amountTotal.setKey(TOTAL_PRICE_KEY);

        if (isPah) {
            amountPending.setSubtitle(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_PAH_DEFAULT_TEXT));
        }

        if ((isPah || isMpFareHoldEligible)  && !supplierCurrency.equals(askedCurrency)) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);

            String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
            if (Constants.AE.equalsIgnoreCase(region)) {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_PAH_TEXT), polyglotService.getTranslatedData(ConstantsTranslation.LOCAL_CURRENCY_TEXT)));
            }
            else{
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_PAH_TEXT), supplierCurrency));
                amountPending.setAlternateCurrency(supplierCurrency);
                amountPending.setAlternateAmount(Utility.round(amountPending.getAmount(), 2));
                amountTotal.setAlternateCurrency(supplierCurrency);
                amountTotal.setAlternateAmount(Utility.round(amountTotal.getAmount(), 2));
            }
            amountPending.setAmount(Utility.round(pendingAmount / conversionFactor, 0));
            amountPending.setCurrency(askedCurrency);
            amountPaid.setAmount(Utility.round(paidAmount / conversionFactor, 0));
            amountPaid.setCurrency(askedCurrency);
            amountTotal.setAmount(Utility.round((paidAmount + pendingAmount) / conversionFactor, 0));
            amountTotal.setCurrency(askedCurrency);
        }

        AmountDetail persuasionAmountDetail = new AmountDetail();
        if (isBNPL) {
            if (thankYouResponse.getBookingDetails() != null)
                thankYouResponse.getBookingDetails().setBnplBooking(isBNPL);
            persuasionAmountDetail.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.BNPL_PAYMENT_BREAKUP_LABEL));
        } else if (isPah) {
            if (thankYouResponse.getBookingDetails() != null)
                thankYouResponse.getBookingDetails().setPahBooking(isPah);
            persuasionAmountDetail.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAH_PAYMENT_BREAKUP_LABEL));
        }
        List<AmountDetail> bookingAmountBreakup = new ArrayList<>(Arrays.asList(amountPaid, amountPending, amountTotal));
        if (null != persuasionAmountDetail && !isMpFareHoldEligible) {
            persuasionAmountDetail.setKey(PAY_NOW_KEY);
            persuasionAmountDetail.setMyTripsDeeplink(getMudraPayNowDeeplink(thankyouPaynowUrlGi, persistedMultiRoomData.getBookingMetaInfo(),persistedMultiRoomData.getTravelerInfoList()));
            bookingAmountBreakup.add(persuasionAmountDetail);
        }
        return bookingAmountBreakup;
    }




    private HotelResult buildHotelDetails(PersistedMultiRoomData persistedMultiRoomData) {
        HotelResult hotelResult = new HotelResult();
        if (CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList())) {
            HotelInfo hotelInfo = persistedMultiRoomData.getHotelList().get(0).getHotelInfo();
            hotelResult.setName(hotelInfo.getName());
            hotelResult.setHotelIcon(hotelInfo.getHotelIcon());
            hotelResult.setAddressLines(hotelInfo.getAddressLines());
            hotelResult.setStarRating(hotelInfo.getStarRating());
            hotelResult.setPropertyType(hotelInfo.getPropertyType());
            hotelResult.setPropertyLabel(hotelInfo.getPropertyLabel());
            hotelResult.setAltAcco(hotelInfo.isAltAcco());
            hotelResult.setCountryCode(hotelInfo.getCountryCode());
            //BedInfoText, It is added to summarize the bed types and count for property Layout
            hotelResult.setBedInfoText(hotelInfo.getBedInfoText());
            hotelResult.setCategories(commonResponseTransformer.getHotelCategories(hotelInfo.getCategories(), hotelInfo.isAltAcco()));
            hotelResult.setHotelId(hotelInfo.getHotelId());
            hotelResult.setCityName(hotelInfo.getCityName());
            hotelResult.setLocationId(hotelInfo.getLocationId());
            hotelResult.setLocationType(hotelInfo.getLocusData() != null ? hotelInfo.getLocusData().getLocusType() : null);
            hotelResult.setCategoryDetails(persistedMultiRoomData.getCategoryDetails());
            hotelResult.setGoStay(CollectionUtils.isNotEmpty(hotelInfo.getCategories()) && hotelInfo.getCategories().contains(GO_STAYS_CATEGORY));
            String idContext = MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue());

            if (hotelInfo.isBudgetHotel() && StringUtils.isNotBlank(idContext) && !Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
                String siteDomain = persistedMultiRoomData.getAvailReqBody().getSiteDomain();
                hotelResult.setHotelTags(commonResponseTransformer.buildValueStaysHotelTag(Constants.VALUE_STAY_TAG_TITLE_THANK_YOU, hotelResult.getHotelTags()));
            }
            buildGroupBookingSpecificNodes(hotelResult, persistedMultiRoomData, hotelInfo);

            if (utility.isSPKGExperimentOn(persistedMultiRoomData.getExpData()) && anyPackageRatePlanAvailable(persistedMultiRoomData)) {
                hotelResult.setPackageTagUrl(elitePackageIconUrl);
            }
        }
        if (persistedMultiRoomData.getAvailReqBody() != null)
            hotelResult.setFunnelSource(StringUtils.isNotBlank(persistedMultiRoomData.getAvailReqBody().getFunnelSource()) ? persistedMultiRoomData.getAvailReqBody().getFunnelSource() : "HOTELS");

        if(persistedMultiRoomData.getAvailReqBody() != null && FUNNEL_DAYUSE.equalsIgnoreCase(persistedMultiRoomData.getAvailReqBody().getFunnelSource())){
            hotelResult.setDayUseDetails(new DayUseDetails());
            if(persistedMultiRoomData.getAvailReqBody().getSlot() != null) {
                hotelResult.getDayUseDetails().setStayTime(utility.calculateTimeSlot_Meridiem(persistedMultiRoomData.getAvailReqBody().getSlot()));
                hotelResult.getDayUseDetails().setSlotDuration(String.format("%s Hours", persistedMultiRoomData.getAvailReqBody().getSlot().getDuration()));
            }
            hotelResult.getDayUseDetails().setDayUse(true);
        }
        return hotelResult;
    }

    private boolean anyPackageRatePlanAvailable(PersistedMultiRoomData persistedMultiRoomData) {
        if (persistedMultiRoomData != null
                && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList())
                && persistedMultiRoomData.getHotelList().get(0).getTariffInfoList() != null) {
            return persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().stream().anyMatch(PersistedTariffInfo::isPackageRatePlan);
        }
        return false;
    }


    private PropertyRules buildPropertyRules(PersistedMultiRoomData persistedMultiRoomData, ThankYouResponse thankYouResponse) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList())) {
            return null;
        }

        String supplierCode =StringUtils.isNotBlank(persistedMultiRoomData.getAvailReqBody().getSupplierCode()) ? persistedMultiRoomData.getAvailReqBody().getSupplierCode(): StringUtils.EMPTY;
        if(StringUtils.isBlank(supplierCode) && CollectionUtils.isNotEmpty(persistedMultiRoomData.getAvailReqBody().getRoomCriteria()) )
            supplierCode = persistedMultiRoomData.getAvailReqBody().getRoomCriteria().get(0).getSupplierCode();

        if(StringUtils.isBlank(supplierCode) && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList())  )
            supplierCode = persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails() != null ?
                    persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails().getSupplierCode() : StringUtils.EMPTY;

        HotelInfo hotelInfo = persistedMultiRoomData.getHotelList().get(0).getHotelInfo();
        RequestInputBO inputBo = new RequestInputBO.Builder()
                .buildCountryCode(hotelInfo.getCountryCode())
                .buildPropertyType(hotelInfo.getPropertyType())
                .buildHouseRules(hotelInfo.getHouseRules())
                .buildMustReadRules(hotelInfo.getMustReadRules())
                .buildPah(PaymentMode.isPAHOnlyPaymodes(PaymentMode.findPaymentModeFromString(persistedMultiRoomData.getAvailReqBody().getPayMode())))
                .buildPahWithCC(Utility.isPahWithCCPaymode(persistedMultiRoomData.getAvailReqBody().getPayMode()))
                .buildCancellationPolicyType(getBookedCancellationPolicyType(thankYouResponse).name())
                .buildCancellationDate(getCancellationPolicyTillDate(persistedMultiRoomData))
                .buildSupplierCode( supplierCode )
                .buildCheckinPolicy(getCheckinPolicy(persistedMultiRoomData))
                .buildConfirmationPolicy(getConfirmationPolicy(persistedMultiRoomData))
                .buildNotices(hotelInfo.getNotices())
                .build();

        PropertyRules importantInfoSection = commonResponseTransformer.getImportantInfoSection(inputBo);
        if (null != importantInfoSection)
            importantInfoSection.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(), persistedMultiRoomData.getBookingMetaInfo()));
        return importantInfoSection;
    }

    private RatePolicy getCheckinPolicy(PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()))
            return null;
        RatePolicy checkInPolicy = null;
        for (PersistedTariffInfo persistedTariffInfo : persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()) {
            RatePolicy ratePolicy = persistedTariffInfo.getCheckinPolicy();
            if (checkInPolicy == null && ratePolicy != null) {
                checkInPolicy = ratePolicy;
                if (Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(checkInPolicy.getMostRestrictive())) {
                    return checkInPolicy;
                }
            } else if (checkInPolicy != null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                checkInPolicy = ratePolicy;
                return checkInPolicy;
            }
        }
        return checkInPolicy;
    }

    private String getCancellationPolicyTillDate(PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()))
            return null;
        CancelPenalty mostRestrictedCancelPenalty = null;
        for (PersistedTariffInfo persistedTariffInfo : persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()) {
            if (mostRestrictedCancelPenalty == null) {
                mostRestrictedCancelPenalty = persistedTariffInfo.getCancelPenaltyList().get(0);
                if (Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(mostRestrictedCancelPenalty.getMostRestrictive())) {
                    break;
                }
            }
            Optional<CancelPenalty> cancelPenalty = persistedTariffInfo.getCancelPenaltyList().stream().filter(penalty -> Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(penalty.getMostRestrictive())).findFirst();
            if (cancelPenalty.isPresent()) {
                mostRestrictedCancelPenalty = cancelPenalty.get();
                break;
            }
        }
        if (null != mostRestrictedCancelPenalty && StringUtils.isNotBlank(mostRestrictedCancelPenalty.getTillDate()))
            return mostRestrictedCancelPenalty.getTillDate();
        return null;
    }

    private RatePolicy getConfirmationPolicy(PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()))
            return null;
        RatePolicy confirmationPolicy = null;
        for (PersistedTariffInfo persistedTariffInfo : persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()) {
            RatePolicy ratePolicy = persistedTariffInfo.getConfirmationPolicy();
            if (confirmationPolicy == null && ratePolicy != null) {
                confirmationPolicy = ratePolicy;
                if (Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                    return confirmationPolicy;
                }
            } else if (confirmationPolicy != null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                confirmationPolicy = ratePolicy;
                return confirmationPolicy;
            }
        }
        return confirmationPolicy;
    }

    private SelectedSpecialRequests buildSelectedSpecialRequests(PersistedMultiRoomData persistedData) {
        SelectedSpecialRequests selectedSpecialRequest = null;
        if (CollectionUtils.isEmpty(persistedData.getHotelList())
                || null == persistedData.getHotelList().get(0).getHotelInfo()
                || persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequestAvailable() == null
                || CollectionUtils.isEmpty(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequestAvailable().getCategories())
                || persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest() == null
                || CollectionUtils.isEmpty(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest().getCategories()))
            return null;

        selectedSpecialRequest = commonResponseTransformer.buildSelctedSpecialRequests(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequestAvailable(),
                persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest());
        return selectedSpecialRequest;
    }

    private MyTripsSection buildMyTripsSection(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        boolean isZeroPaymentNowBooking = false;
        if (thankYouResponse.getBookingDetails() != null)
            isZeroPaymentNowBooking = thankYouResponse.getBookingDetails().isBnplBooking() || thankYouResponse.getBookingDetails().isPahBooking();
        BookedCancellationPolicyType cancellationPolicyType = getBookedCancellationPolicyType(thankYouResponse);

        //get the condition key
        String apDays = "X";
        if (dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(thankYouResponse.getBookingDetails().getCheckInDate())) == 0)
            apDays = String.valueOf(0);
        StringBuilder conditionKey = new StringBuilder();
        if (isZeroPaymentNowBooking)
            conditionKey.append("ZP").append(Constants.UNDERSCORE);
        conditionKey.append(cancellationPolicyType.toString()).append(Constants.UNDERSCORE)
                .append("AP").append(Constants.UNDERSCORE).append(apDays);

        logger.warn("MyTrips Section condition key " + conditionKey.toString());


        try {
            // convert myTripsCardTypeToCardDetails here
            String jsonToString = gson.toJson(myTripsCardTypeToCardDetails);
            Map<String, String> textReplacer = null;
            myTripsCardTypeToCardDetailsModified = gson.fromJson(jsonToString, new TypeToken<Map<String, MyTripCard>>() {
            }.getType());
            if (persistedData.getBookerInfo().getProfileType().equalsIgnoreCase(PROFILE_TYPE_CTA))
                textReplacer = myTripsCtaTextReplacer;
            polyglotHelper.translateMyTripsCards(myTripsCardTypeToCardDetailsModified, textReplacer);

        }catch (Exception e){
            logger.warn("Error in translating myTripsCardTypeToCardDetail");
        }


        List<String> cardTypeList = myTripsConditionsToCardsList.get(conditionKey.toString());
        if (CollectionUtils.isEmpty(cardTypeList))
            return null;
        MyTripsSection myTripsSection = new MyTripsSection();
        List<MyTripCard> cards = cardTypeList.stream()
                .map(cardType -> {
                    MyTripCard myTripCard = myTripsCardTypeToCardDetailsModified.get(cardType);
                    updateMyTripIconUrl(myTripCard, persistedData, cardType);
                    myTripCard.setDeepLink(Utility.appendQueryParamsInUrl(getMytripsRawDeepLinkUrl(), Collections.singletonMap(Constants.BOOKING_ID, thankYouResponse.getBookingDetails().getBookingId())));
                    return myTripCard;
                })
                .collect(Collectors.toList());
        myTripsSection.setCards(cards);
        return myTripsSection;
    }

    protected void updateMyTripIconUrl(MyTripCard myTripCard, PersistedMultiRoomData persistedData, String cardType) {
        boolean isCorp = false;
        if (null != persistedData && null != persistedData.getAvailReqBody() && StringUtils.isNotBlank(persistedData.getAvailReqBody().getIdContext())
                && "CORP".equalsIgnoreCase(persistedData.getAvailReqBody().getIdContext()))
            isCorp = true;
        String iconUrl = "";
        if (isCorp)
            iconUrl = getMytripActionCorpUrl(cardType);
        else
            iconUrl = getMytripActionB2CUrl(cardType);
        myTripCard.setIconUrl(iconUrl);
    }

    private BookedCancellationPolicyType getBookedCancellationPolicyType(ThankYouResponse thankYouResponse) {
        BookedCancellationPolicyType cancellationPolicyType = BookedCancellationPolicyType.NR;
        if (CollectionUtils.isNotEmpty(thankYouResponse.getRooms().getRatePlanList())
                && thankYouResponse.getRooms().getRatePlanList().get(0).getCancellationPolicy() != null
                && thankYouResponse.getRooms().getRatePlanList().get(0).getCancellationPolicy().getType().equals(BookedCancellationPolicyType.FC)) {
            cancellationPolicyType = BookedCancellationPolicyType.FC;
        }
        return cancellationPolicyType;
    }

    private void buildAdditionalCharges(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || persistedMultiRoomData.getHotelList().get(0) == null || persistedMultiRoomData.getHotelList().get(0).getHotelInfo() == null)
            return;
        HotelInfo hotelInfo = persistedMultiRoomData.getHotelList().get(0).getHotelInfo();
        AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
                .buildUserCurrency(persistedMultiRoomData.getAvailReqBody().getCurrency())
                .buildHotelierCurrency(Utility.getHotelierCurrency(persistedMultiRoomData))
                .buildPropertyType(hotelInfo.getPropertyType())
                .buildAdditionalFees(hotelInfo.getMandatoryCharges())
                .buildConversionFactor(persistedMultiRoomData.getTotalDisplayFare().getConversionFactor())
                .buildBookingAmount(persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice())
                .build();
        thankYouResponse.setAdditionalfees(commonResponseTransformer.buildAdditionalCharges(additionalChargesBO, false));
    }

    private void buildPgCharges(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if(persistedMultiRoomData.getPaymentInfo() != null) {
            thankYouResponse.setPgCharges(persistedMultiRoomData.getPaymentInfo().getPgCharges());
        }
    }

    private String getMudraPayNowDeeplink(String myTripsDeeplink, BookingMetaInfo bookingMetaInfo, List<TravelerInfo> travelerInfoList) {
        HashMap<String, String> params = new HashMap<>();
        if (null != bookingMetaInfo && StringUtils.isNotBlank(bookingMetaInfo.getBookingId())) {
            params.put(Constants.BOOKING_ID, bookingMetaInfo.getBookingId());
        }
        if (CollectionUtils.isNotEmpty(travelerInfoList) && StringUtils.isNotEmpty(travelerInfoList.get(0).getMobileNumber())) {
            params.put(MOBILE_PARAM, travelerInfoList.get(0).getMobileNumber());
        }
        return Utility.appendQueryParamsInUrl(myTripsDeeplink, params);
    }


    private void buildMyPatHeroBanner(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if(persistedMultiRoomData.getMyPatHeroBanner() != null) {
            if(thankYouResponse.getBookingDetails()!=null && BookingStatus.SUCCESS.equals(thankYouResponse.getBookingDetails().getStatus())) {
                thankYouResponse.setMyPatHeroBanner(persistedMultiRoomData.getMyPatHeroBanner());
            }
        }
    }

    /*Building hero or cashback persuasions for MyPARTNER funnel, where loyalty_offer_message is prioritized
    and calling utility method if couponInfo is available*/
    private Map<String, PersuasionResponse> buildPersuasionsMap(PersistedMultiRoomData persistedData) {
        Map<String,PersuasionResponse> persuasionMap = new HashMap<>();
        boolean isMyPartnerRequest = (persistedData!=null) && (persistedData.getAvailReqBody()!=null) && Utility.isMyPartnerRequest(persistedData.getAvailReqBody().getProfileType(),persistedData.getAvailReqBody().getSubProfileType());
        if(isMyPartnerRequest) {
            if(null!=persistedData.getTotalDisplayFare() && null != persistedData.getTotalDisplayFare().getDisplayPriceBreakDown() && null != persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()) {
                BestCoupon coupon = persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo();
                //If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
                logger.debug("Manthan HybridDiscounts {}",coupon.getHybridDiscounts());
                boolean isCashbackAmtAvailable= MapUtils.isNotEmpty(coupon.getHybridDiscounts()) && coupon.getHybridDiscounts().containsKey(CASHBACK_TO_WALLET);
                if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable) {
                    buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
                }
            }
        }
        if (null != persistedData) {
            PersuasionResponse saleCampaignPersuasion = commonResponseTransformer.getSaleCampaignPersuasion(
                    persistedData.getCampaignPojo());
            if (null != saleCampaignPersuasion) {
                saleCampaignPersuasion.setSubText(persistedData.getCampaignPojo().getPostSaleDescription());
                persuasionMap.put(SALE_CAMPAIGN, saleCampaignPersuasion);
            }
            PersuasionResponse longStayBenefitPersuasion = commonResponseTransformer.getLongStayBenefitPersuasion(persistedData.getLongStayBenefits());
            if (longStayBenefitPersuasion != null) {
                persuasionMap.put(LONG_STAY_BENEFIT, longStayBenefitPersuasion);
            }

        }
        //setting Benefit INFO in the thank you page response.
        if (persistedData != null && persistedData.getHotelBenefitInfo() != null) {
            persuasionMap.put(TAJ_CAMPAIGN_PERSUASION_KEY, commonResponseTransformer.buildTajGiftCardOrHotelCreditPersuasion(persistedData.getHotelBenefitInfo()));
        }
        return persuasionMap;
    }

    protected abstract String getMytripActionCorpUrl(String cardType);

    protected abstract String getMytripActionB2CUrl(String cardType);

    protected abstract String getHotelDetailsRawDeepLinkUrl();

    protected abstract String getMytripsRawDeepLinkUrl();

    protected abstract boolean tildeRequiredInRSQ();

}
