package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT;

@Component
public class OrchSearchRoomsResponseTransformerDesktop extends OrchSearchRoomsResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchRoomsResponseTransformerDesktop.class);

    @Override
    protected PersuasionObject createTopRatedPersuasion() {
        PersuasionObject persuasionObject = new PersuasionObject();
        persuasionObject.setData(new ArrayList<>());
        persuasionObject.setPlaceholder(Constants.PC_SELECT_RIGHT_1);
        persuasionObject.setTemplate("IMAGE_TEXT_H");
        PersuasionData persuasionData = new PersuasionData();
        PersuasionStyle style = new PersuasionStyle();
        style.setStyleClasses(Arrays.asList("rmType__toprated"));
        persuasionData.setStyle(style);
        persuasionData.setPersuasionType("PEITHO");
        persuasionData.setText(polyglotService.getTranslatedData(ConstantsTranslation.TOP_RATED));
        persuasionObject.setData(Arrays.asList(persuasionData));
        return persuasionObject;
    }

    @Override
    public LoginPersuasion buildLoginPersuasion(){
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        LoginPersuasion loginPersuasion = new LoginPersuasion();
        if (Constants.AE.equalsIgnoreCase(region)) {
            loginPersuasion.setLoginPersuasionText(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT_GCC));
            loginPersuasion.setLoginPersuasionSubText(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT_GCC));
        }else {
            loginPersuasion.setLoginPersuasionText(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT));
            loginPersuasion.setLoginPersuasionSubText(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT));
        }
        return loginPersuasion;
    }

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        PersuasionResponse persuasion = new PersuasionResponse();
        StringBuilder persuasionAppliedText=new StringBuilder();
        if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ) {
            LOGGER.debug("loyalty_offer_message: {}", coupon.getLoyaltyOfferMessage());
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT), coupon.getLoyaltyOfferMessage()));
        }
        else{
            LOGGER.debug("Promo_Cash_Amount: {}",coupon.getHybridDiscounts().get("CTW"));
            int cashbackDiscountAmtRounded = (int) Math.round(coupon.getHybridDiscounts().get("CTW"));
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT),cashbackDiscountAmtRounded));
        }
        String iconType= StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage())? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
        persuasion.setPersuasionText(persuasionAppliedText.toString());
        persuasion.setHtml(true);
        persuasion.setIconType(iconType);
        persuasionMap.put (CASHBACK_HERO_OFFER_PERSUASION_NODE, persuasion);
    }

    @Override
    protected GroupRatePlanFilter buildGroupFilterForDevice(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation) {
        return null;
    }
}
