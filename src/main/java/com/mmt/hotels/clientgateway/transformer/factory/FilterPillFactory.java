package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterPillConfigurationWrapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.response.filter.FilterPill;
import com.mmt.hotels.clientgateway.response.filter.SortList;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.transformer.response.FilterPillConfig;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import com.mmt.hotels.model.request.AccessPoint;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import javax.annotation.PostConstruct;
import java.util.*;


import static com.mmt.hotels.clientgateway.constants.Constants.SORT_CRITERIA_ACCESS_POINT_FIELD_VALUE;
import static com.mmt.hotels.clientgateway.constants.Constants.TRUE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SORT_CRITERIA_ACCESS_POINT_TITLE;
@Component
public class FilterPillFactory {

    @Value("${filter.pill.config.B2C}")
    private String pillConfigB2C;
    @Value("${filter.pill.config.ih.B2C}")
    private String pillConfigIhB2C;
    @Value("${filter.pill.config.getaway}")
    private String pillConfigGetaway;

    @Value("${filter.pill.config.luxe}")
    private String pillConfigLuxe;

    @Value("${filter.pill.config.hostel}")
    private String pillConfigHostel;

    @Value("${sort.pill.config}")
    private String sortPillData;

    @Value("${filter.pill.config.dayuse}")
    private String pillConfigDayUse;

    private FilterPillConfig filterPillsB2C;
    private FilterPillConfig filterPillsIhB2C;

    private FilterPillConfig filterPillsGetaway;

    private FilterPillConfig filterPillsLuxe;

    private FilterPillConfig filterPillsHostel;

    private FilterPillConfig filterPillsDayUse;

    private SortList sortList;

    private final Gson gson = new Gson();

    @Autowired
    PolyglotService polyglotService;

    @PostConstruct
    public void init() {
        filterPillsB2C = gson.fromJson(pillConfigB2C, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsIhB2C = gson.fromJson(pillConfigIhB2C, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsGetaway = gson.fromJson(pillConfigGetaway, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsLuxe = gson.fromJson(pillConfigLuxe, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsHostel = gson.fromJson(pillConfigHostel, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsDayUse = gson.fromJson(pillConfigDayUse, new TypeToken<FilterPillConfig>(){
        }.getType());

        sortList = gson.fromJson(sortPillData, new TypeToken<SortList>() {
        }.getType());


    }


    /**
     * returns wrapper for filter pill configuration
     **/

    public FilterPillConfigurationWrapper getFilterPillConfiguration(String funnelSource, String locationType, boolean isDomestic,Map<String, AccessPoint> accessPoints,LinkedHashMap<String, String> expDataMap) {

        FilterPillConfigurationWrapper filterPillConfigurationWrapper = new FilterPillConfigurationWrapper();
        filterPillConfigurationWrapper.setFilterPillConfig(getFilterPillConfig(funnelSource, locationType, isDomestic));
        filterPillConfigurationWrapper.setSortList(getSortListPill(accessPoints,expDataMap));

        return filterPillConfigurationWrapper;

    }

    /**
     * this method returns pill config as per search and funnel
     **/
    private FilterPillConfig getFilterPillConfig(String funnelSource, String locationType, boolean isDomestic) {

        if (StringUtils.isNotBlank(locationType) && Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType)) {
            return copyFilterPillConfigProp(filterPillsLuxe);
        }
        if (StringUtils.isNotBlank(funnelSource) && Constants.FUNNEL_SOURCE_GETAWAY.equalsIgnoreCase(funnelSource)) {
            return copyFilterPillConfigProp(filterPillsGetaway);
        }
        if (StringUtils.isNotBlank(funnelSource) && Constants.FUNNEL_SOURCE_HOSTEL.equalsIgnoreCase(funnelSource)) {
            return copyFilterPillConfigProp(filterPillsHostel);
        }
        if(StringUtils.isNotBlank(funnelSource)&& Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnelSource)){
            return copyFilterPillConfigProp(filterPillsDayUse);
        }
        if(!isDomestic)
            return copyFilterPillConfigProp(filterPillsIhB2C);
        return copyFilterPillConfigProp(filterPillsB2C);


    }


    /**
     * this method creates copy of filter pill config and returns to ensure original config is not modified
     **/
    public FilterPillConfig copyFilterPillConfigProp(FilterPillConfig filterPillConfig) {
        if (filterPillConfig != null && MapUtils.isNotEmpty(filterPillConfig.getFilterPills()) && MapUtils.isNotEmpty(filterPillConfig.getPillSequence())) {
            FilterPillConfig newFilterPillConfig = new FilterPillConfig();
            Map<String, FilterPill> newFilterMap = new HashMap<>();
            for (String pill : filterPillConfig.getFilterPills().keySet()) {
                FilterPill filterPill = new FilterPill();
                if (filterPillConfig.getFilterPills().get(pill) != null) {
                    List<String> filterCategories = new ArrayList<>();
                    filterCategories.addAll(filterPillConfig.getFilterPills().get(pill).getCategories());
                    BeanUtils.copyProperties(filterPillConfig.getFilterPills().get(pill), filterPill);
                    filterPill.setCategories(filterCategories);
                }
                newFilterMap.put(pill, filterPill);
            }

            Map<String, Integer> newPillSequence = new HashMap<>();
            for (String pill : filterPillConfig.getPillSequence().keySet()) {
                if (filterPillConfig.getPillSequence().get(pill) != null) {
                    Integer sequence = new Integer(filterPillConfig.getPillSequence().get(pill));
                    newPillSequence.put(pill, sequence);
                }
            }
            newFilterPillConfig.setFilterPills(newFilterMap);
            newFilterPillConfig.setPillSequence(newPillSequence);
            return newFilterPillConfig;
        }
        return null;
    }

    /**
     * this method creates copy of sort list pill and returns it to ensure config is not changed
     **/
    private SortList getSortListPill(Map<String, AccessPoint> accessPoints,LinkedHashMap<String, String> expDataMap) {
        if (this.sortList != null) {
            SortList sortList = new SortList();
            sortList.setTitle(this.sortList.getTitle());
            List<SortCriteria> sortCriteriaList = new ArrayList<>();
            for (SortCriteria sortCriteria : this.sortList.getSortCriteria()) {
                SortCriteria newSortCriteria = new SortCriteria();
                BeanUtils.copyProperties(sortCriteria, newSortCriteria);
                sortCriteriaList.add(newSortCriteria);
            }
            // Add access point based sort criteria if accessPoints are available
            if (MapUtils.isNotEmpty(accessPoints)  && TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.NATIONALPARK_GI.getKey()))) {
                for (AccessPoint entry : accessPoints.values()) {
                    if (entry != null && StringUtils.isNotEmpty(entry.getAccessPointName())
                            && StringUtils.isNotEmpty(entry.getPoi())) {
                        String title = polyglotService.getSafeTranslatedData(SORT_CRITERIA_ACCESS_POINT_TITLE).replace("{poi_name}",entry.getAccessPointName());
                        SortCriteria accessPointSortCriteria = getSortCriteria(entry, title);
                        sortCriteriaList.add(accessPointSortCriteria);
                    }
                }
            }
            sortList.setSortCriteria(sortCriteriaList);
            return sortList;
        }
        return null;
    }

    private static SortCriteria getSortCriteria(AccessPoint accessPoint, String title) {
        SortCriteria accessPointSortCriteria = new SortCriteria();
        accessPointSortCriteria.setOrder(Constants.ASCENDING);

        accessPointSortCriteria.setTitle(title);
        accessPointSortCriteria.setPillText(title);
        accessPointSortCriteria.setAccessPoint(true);

        String fieldValue = SORT_CRITERIA_ACCESS_POINT_FIELD_VALUE.replace("{poi_id}",accessPoint.getPoi());
        accessPointSortCriteria.setField(fieldValue);

        return accessPointSortCriteria;
    }
}
