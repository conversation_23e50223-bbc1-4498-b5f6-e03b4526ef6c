package com.mmt.hotels.clientgateway.transformer.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.request.DiscountRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.DiscountResponseTransformer;

@Component
public class DiscountServiceFactory {

	@Autowired
	private DiscountRequestTransformer discountRequestTransformer;
	
	@Autowired
	private DiscountResponseTransformer discountResponseTransformer;
	
	public DiscountRequestTransformer getRequestService(String client){
		return discountRequestTransformer;
	}
	
	public DiscountResponseTransformer  getResponseService(String client){
		return discountResponseTransformer;
	}
	
}
