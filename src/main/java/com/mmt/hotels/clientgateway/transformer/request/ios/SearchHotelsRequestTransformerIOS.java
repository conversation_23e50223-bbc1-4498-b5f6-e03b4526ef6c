package com.mmt.hotels.clientgateway.transformer.request.ios;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.transformer.request.SearchHotelsRequestTransformer;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.springframework.stereotype.Component;

@Component
public class SearchHotelsRequestTransformerIOS extends SearchHotelsRequestTransformer {

    @Override
    public SearchWrapperInputRequest convertSearchRequest(
            SearchHotelsRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {

        SearchWrapperInputRequest searchWrapperInputRequest =  super.convertSearchRequest(searchHotelsRequestGateway, 
        		commonModifierResponse);
        return searchWrapperInputRequest;
    }
}
