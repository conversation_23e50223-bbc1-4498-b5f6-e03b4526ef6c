package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.pdt.gi.ClmPersuasion;
import com.gommt.hotels.orchestrator.detail.model.request.prime.LoyaltyMessageResponse;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.thankyou.AmountDetail;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.DiscountPersuasionInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

@Component
public class SearchRoomsPriceHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsPriceHelper.class);

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    Utility utility;

    private NumberFormat numberFormatter;

    @PostConstruct
    void init() {
        numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
        numberFormatter.setMaximumFractionDigits(0); // No decimals
        numberFormatter.setMinimumFractionDigits(0); // Ensure no trailing zeros
    }

    /**
     * New method to build price map from orchestrator v2 PriceDetail
     * Works directly with PriceDetail without converting to DisplayPriceBreakDown
     */
    public Map<String, TotalPricing> getPriceMap(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail,
                                                 Map<String, String> expDataMap, com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails occupancyDetails, String askedCurrency,
                                                 String sellableType, Integer nightCount,
                                                 boolean isCorp, String segmentId, boolean buildToolTip, boolean groupBookingFunnel,
                                                 boolean groupBookingPrice, boolean ismyPartnerRequest, String listingType) {

        if (priceDetail == null) {
            return null;
        }

        Integer roomCount = occupancyDetails != null ? occupancyDetails.getNumberOfRooms() : null;

        String priceDisplayMessage = (null == roomCount) ? "" : commonResponseTransformer.getPriceDisplayMessage(expDataMap, roomCount, sellableType, nightCount,
                groupBookingFunnel, listingType);
        Map<String, TotalPricing> priceMap = new HashMap<>();
        String priceMapKey;
        // Build price map directly from orchestrator v2 PriceDetail
        if (StringUtils.isNotBlank(priceDetail.getCouponCode())) {
            priceMapKey = priceDetail.getCouponCode();
        } else {
            priceMapKey = "DEFAULT";
        }

        // Build TotalPricing directly from PriceDetail
        TotalPricing totalPricing = buildTotalPricingFromPriceDetail(priceDetail, isCorp, segmentId, expDataMap, groupBookingFunnel, askedCurrency, priceDetail.getCouponCode(), null, null, null, null, 0.0,0.0);
        if (occupancyDetails != null && StringUtils.isNotEmpty(occupancyDetails.getPricingKey())) {
            totalPricing.setPricingKey(occupancyDetails.getPricingKey());
        }

        // Set coupon description and amount if available
        if (StringUtils.isNotBlank(priceDetail.getCouponCode()) && CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo appliedCoupon = priceDetail.getApplicableCoupons().stream()
                    .filter(coupon -> priceDetail.getCouponCode().equals(coupon.getCouponCode()))
                    .findFirst()
                    .orElse(null);
            if (appliedCoupon != null) {
                totalPricing.setCouponDesc(appliedCoupon.getDescription());
                totalPricing.setCouponAmount(appliedCoupon.getDiscount());
                totalPricing.setEmiBankDetails(buildEmiBankDetails(appliedCoupon.getNoCostEmiDetails())); // not completely built
            }
        }

        // Set price display message
        totalPricing.setPriceDisplayMsg(priceDisplayMessage);

        // Set tax message
        totalPricing.setPriceTaxMsg(commonResponseTransformer.getShowTaxMessage(expDataMap, roomCount, priceDetail.getTotalTax(), askedCurrency));

        // Build price tooltip if required
        if (buildToolTip) {
            totalPricing.setPriceToolTip(buildPriceToolTipFromPriceDetail(priceDetail, nightCount, askedCurrency));
        }

        // Set group price and savings text
        long savingPerc = (long) calculateSavingPercentage(priceDetail);
        long displayPrice = (long) priceDetail.getDisplayPrice();

        priceMap.put(priceMapKey, totalPricing);


        // Build linked rates persuasions
        buildLinkedRatesPersuasionsFromPriceDetail(priceMapKey, priceMap, priceDetail);

        // Process applicable coupons (similar to displayPriceBreakDownList)
        if (CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo couponInfo : priceDetail.getApplicableCoupons()) {
                if (couponInfo == null || StringUtils.isBlank(couponInfo.getCouponCode()) ||
                        couponInfo.getCouponCode().equals(priceDetail.getCouponCode())) {
                    continue; // Skip if already processed as main coupon
                }

                // Build TotalPricing for this coupon using same priceDetail but different coupon
                TotalPricing couponTotalPricing = buildTotalPricingFromPriceDetail(priceDetail, isCorp, segmentId, expDataMap,
                        groupBookingFunnel, askedCurrency, couponInfo.getCouponCode(), null, null, null, null, 0.0,0.0);
                if (occupancyDetails != null && StringUtils.isNotEmpty(occupancyDetails.getPricingKey())) {
                    couponTotalPricing.setPricingKey(occupancyDetails.getPricingKey());
                }

                // Override with coupon specific values
                couponTotalPricing.setCouponDesc(couponInfo.getDescription());
                couponTotalPricing.setCouponAmount(couponInfo.getDiscount());
                couponTotalPricing.setPricingKey(couponInfo.getCouponCode());
                couponTotalPricing.setPriceDisplayMsg(priceDisplayMessage);
                couponTotalPricing.setPriceTaxMsg(commonResponseTransformer.getShowTaxMessage(expDataMap, roomCount, priceDetail.getTotalTax(), askedCurrency));


                // Build coupon persuasion for this coupon
                if (priceDetail.getExtraDiscount() != null) {
                    String extraDiscountType = priceDetail.getExtraDiscount().getType();
                    DiscountPersuasionInfo discountPersuasionInfo = new DiscountPersuasionInfo();
                    discountPersuasionInfo.setDiscount(priceDetail.getExtraDiscount().getDiscount());
                    discountPersuasionInfo.setBookingCount(priceDetail.getExtraDiscount().getBookingCount());
                    discountPersuasionInfo.setType(priceDetail.getExtraDiscount().getType());
                }

                priceMap.put(couponInfo.getCouponCode(), couponTotalPricing);
                setGroupPriceAndSavingsText(couponTotalPricing, roomCount, nightCount, savingPerc, displayPrice, groupBookingFunnel, groupBookingPrice, ismyPartnerRequest);
                buildLinkedRatesPersuasionsFromPriceDetail(couponInfo.getCouponCode(), priceMap, priceDetail);
            }
        }

        return priceMap;
    }


    /**
     * Build price tooltip from OrchV2 PriceDetail - equivalent to buildPriceToolTip for DisplayPriceBreakDown
     */
    public String buildPriceToolTipFromPriceDetail(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, Integer nightCount, String askedCurrency) {
        if (priceDetail == null || nightCount == null || nightCount <= 0) {
            return null;
        }

        StringBuilder toolTip = new StringBuilder();
        String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol();

        // Base price per night
        if (priceDetail.getBasePrice() > 0.0d) {
            int basePricePerNight = (int) (priceDetail.getBasePrice() / nightCount);
            toolTip.append(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_LABEL))
                    .append(": ").append(currencySymbol).append(convertNumericValueToCommaSeparatedString(basePricePerNight, Locale.ENGLISH))
                    .append(" x ").append(nightCount).append(" nights");
        }

        // Tax per night
        if (priceDetail.getTotalTax() > 0.0d) {
            int taxPerNight = (int) (priceDetail.getTotalTax() / nightCount);
            if (toolTip.length() > 0) {
                toolTip.append(" + ");
            }
            toolTip.append(polyglotService.getTranslatedData(ConstantsTranslation.TAX_AND_SERVICE_FEE))
                    .append(": ").append(currencySymbol).append(convertNumericValueToCommaSeparatedString(taxPerNight, Locale.ENGLISH))
                    .append(" x ").append(nightCount).append(" nights");
        }

        // Total discount
        if (priceDetail.getTotalDiscount() > 0.0d) {
            if (toolTip.length() > 0) {
                toolTip.append(" - ");
            }
            int totalDiscount = (int) (priceDetail.getTotalDiscount());
            toolTip.append(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_DISCOUNT_LABEL))
                    .append(": ").append(currencySymbol).append(convertNumericValueToCommaSeparatedString(totalDiscount, Locale.ENGLISH));
        }

        return toolTip.toString();
    }

    private void buildLinkedRatesPersuasionsFromPriceDetail(String priceMapKey, Map<String, TotalPricing> priceMap, com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail) {

        try{
            LinkedRatePriceCalculations linkedRatePriceCalculations = null;
            if(MapUtils.isNotEmpty(priceDetail.getLinkedRatePriceCalculationsMap())){
                for(Map.Entry<String, com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRatePriceCalculations> linkedRatePriceCalculationsEntry : priceDetail.getLinkedRatePriceCalculationsMap().entrySet()) {
                    linkedRatePriceCalculations = linkedRatePriceCalculationsEntry.getValue();
                }

                if(Objects.nonNull(linkedRatePriceCalculations)){
                    String discountedPrice = Integer.toString(linkedRatePriceCalculations.getDisplayPriceDifference());
                    String parentOriginalPrice = Integer.toString(linkedRatePriceCalculations.getParentOriginalPrice());
                    TotalPricing totalPricing = priceMap.get(priceMapKey);
                    totalPricing.setLinkedRPBottomSheetTitle(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_BOTTOMSHEET_TITLE).replace("{discount}", Currency.INR.getCurrencySymbol() + discountedPrice));
                    totalPricing.setLinkedRPDiscountMsg(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_DISCOUNT_TEXT).replace("{discount}",Currency.INR.getCurrencySymbol() + discountedPrice));
                    totalPricing.setLinkedRPOriginalPriceMsg(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT).replace("{parentOriginalPrice}",Currency.INR.getCurrencySymbol() + " " + parentOriginalPrice));
//                    totalPricing.setLinkedRatePriceCalculationsMap(priceDetail.getLinkedRatePriceCalculationsMap());
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error in building linked rate persuasions", e);
        }

    }

    private void buildBaseFare(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList) {
        if (priceDetail.getBasePrice() > 0.0d) {
            PricingDetails baseFare = new PricingDetails();
            baseFare.setAmount(priceDetail.getBasePrice());
            baseFare.setKey(Constants.BASE_FARE_KEY);
            baseFare.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_LABEL));
            baseFare.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            pricingDetailsList.add(baseFare);
        }
    }

    private void buildTotalDiscounts(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, PriceCouponInfo priceCouponInfo) {
        double totalDiscountAmount = 0.0d;
        List<PricingDetails> priceBreakup = new ArrayList<>();
        PricingDetails supplierDiscount = new PricingDetails();
        if (priceDetail.getDiscount().getSupplier() > 0.0) {
            supplierDiscount.setAmount(priceDetail.getDiscount().getSupplier());
            //// commenting as this breakup is only used for review page api
//            supplierDiscount.setLabel(polyglotService.getTranslatedData(priceDetail.getDiscount().getSupplierDiscountType().concat("_DISC_LABEL")));
//            supplierDiscount.setKey(priceDetail.getDiscount().getSupplierDiscountType());
//            supplierDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
//            priceBreakup.add(supplierDiscount);
//            totalDiscountAmount += supplierDiscount.getAmount();
        }

        /* Build OfferDetailMap from Pricer breakup if available */
        if (priceDetail.getDiscount() != null && priceDetail.getDiscount().getMmt() > 0.0d) {
            PricingDetails mmtDiscount = new PricingDetails();
            mmtDiscount.setAmount(priceDetail.getDiscount().getMmt() + supplierDiscount.getAmount());
            mmtDiscount.setKey(Constants.MMT_DISCOUNT_KEY);
            mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL));
            mmtDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(mmtDiscount);
            totalDiscountAmount += mmtDiscount.getAmount();
        }

        if (priceDetail.getDiscount() != null && priceDetail.getDiscount().getBlack() > 0.0d) {
            PricingDetails blackDiscount = new PricingDetails();
            blackDiscount.setAmount(priceDetail.getDiscount().getBlack());
            blackDiscount.setKey(Constants.BLACK_DISCOUNT_KEY);
            // For GCC, MMT_SELECT Program runs hence picking Select Label in this case
            // For IN, MMT_BLACK Program runs hence picking Black label in this case
            blackDiscount.setLabel("goTribe Discount");
            blackDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(blackDiscount);
            totalDiscountAmount += blackDiscount.getAmount();
        }

        if (priceCouponInfo != null) {
            PricingDetails cdfDiscount = new PricingDetails();
            cdfDiscount.setAmount(priceCouponInfo.getDiscount());
            cdfDiscount.setKey(Constants.CDF_DISCOUNT_KEY);
            cdfDiscount.setLabel(priceCouponInfo.getCouponCode() != null ? priceCouponInfo.getCouponCode() : polyglotService.getTranslatedData(ConstantsTranslation.CDF_DISCOUNT_LABEL));
            cdfDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));

            if (priceDetail.getTaxBreakUp() != null && priceDetail.getTaxBreakUp().getServiceFee() > 0.0d && priceCouponInfo.getDiscount() > priceDetail.getTaxBreakUp().getServiceFee()) {
                List<PricingDetails> cdfCouponBreakup = new ArrayList<>();
                PricingDetails serviceFeeReversal = new PricingDetails();
                serviceFeeReversal.setKey(Constants.SERVICE_FEES_REVERSAL_KEY);
                serviceFeeReversal.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_REVERSAL_LABLE));
                serviceFeeReversal.setAmount(priceDetail.getTaxBreakUp().getServiceFee());
                serviceFeeReversal.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                cdfCouponBreakup.add(serviceFeeReversal);
                PricingDetails effectiveCouponApplied = new PricingDetails();
                effectiveCouponApplied.setKey(Constants.EFFECTIVE_COUPON_APPLIED_KEY);
                effectiveCouponApplied.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.EFFECTIVE_COUPON_APPLIED_LABLE));
                effectiveCouponApplied.setAmount(priceCouponInfo.getDiscount() - priceDetail.getTaxBreakUp().getServiceFee());
                effectiveCouponApplied.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                cdfCouponBreakup.add(effectiveCouponApplied);
                cdfDiscount.setBreakup(cdfCouponBreakup);
            }
            priceBreakup.add(cdfDiscount);
            totalDiscountAmount += cdfDiscount.getAmount();
        }

        if (totalDiscountAmount > 0.0d) {
            PricingDetails totalDiscount = new PricingDetails();
            totalDiscount.setAmount(totalDiscountAmount);
            totalDiscount.setKey(Constants.TOTAL_DISCOUNT_KEY);
            totalDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_DISCOUNT_LABEL));
            totalDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
            totalDiscount.setBreakup(priceBreakup);
            pricingDetailsList.add(totalDiscount);
        }
    }

    private void buildTotalAmount(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, PriceCouponInfo couponInfo) {
        PricingDetails totalAmount = new PricingDetails();
        totalAmount.setAmount(priceDetail.getDisplayPrice() + priceDetail.getDiscount().getCoupon() - (couponInfo != null ? couponInfo.getDiscount() : 0.0));
        totalAmount.setKey(Constants.TOTAL_AMOUNT_KEY);
        totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
        totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_AMOUNT_LABEL));
        totalAmount.setSubTitle("Includes taxes and fees"); // This text is needed by GI
        pricingDetailsList.add(totalAmount);
    }

    private void buildPriceAfterDiscount(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, PriceCouponInfo couponInfo) {
        if (priceDetail.getTotalDiscount() > 0.0d) {
            PricingDetails priceAfterDiscount = new PricingDetails();
            priceAfterDiscount.setAmount(priceDetail.getDisplayPrice() + priceDetail.getDiscount().getCoupon() - (couponInfo != null ? couponInfo.getDiscount() : 0.0d));
            priceAfterDiscount.setKey(Constants.PRICE_AFTER_DISCOUNT_KEY);
            priceAfterDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_AFTER_DISCOUNT_LABEL));
            priceAfterDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            pricingDetailsList.add(priceAfterDiscount);
        }
    }

    private void buildTaxesAndServiceFee(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, Map<String, String> expDataMap) {
        String countryCode = MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue());
        List<PricingDetails> priceBreakup = new ArrayList<>();
        double totalTaxesAndSrvcFee = 0.0d;
        if (priceDetail.getTaxBreakUp() != null) {
            double hotelTaxAmount = priceDetail.getTaxBreakUp().getHotelTax();
            if (priceDetail.getTaxBreakUp().getHotelServiceCharge() > 0.0d) {
                hotelTaxAmount -= priceDetail.getTaxBreakUp().getHotelServiceCharge();
                PricingDetails serviceCharge = new PricingDetails();
                serviceCharge.setAmount(priceDetail.getTaxBreakUp().getHotelServiceCharge());
                serviceCharge.setKey(Constants.SERVICE_CHARGE_KEY);
                serviceCharge.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_CHARGE_LABEL));
                serviceCharge.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                priceBreakup.add(serviceCharge);
                totalTaxesAndSrvcFee += serviceCharge.getAmount();
            }
            if (priceDetail.getTaxBreakUp().getHotelTax() > 0.0d) {
                PricingDetails hotelTax = new PricingDetails();
                hotelTax.setAmount(hotelTaxAmount);
                hotelTax.setKey(Constants.HOTEL_TAX_KEY);
                hotelTax.setLabel(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) ? polyglotService.getTranslatedData(ConstantsTranslation.GST_LABEL) : polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_TAX_LABEL));
                hotelTax.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                priceBreakup.add(hotelTax);
                totalTaxesAndSrvcFee += hotelTax.getAmount();
            }
            if (priceDetail.getTaxBreakUp().getServiceFee() > 0.0d) {
                PricingDetails serviceFee = new PricingDetails();
                serviceFee.setAmount(priceDetail.getTaxBreakUp().getServiceFee());
                serviceFee.setKey(Constants.SERVICE_FEES_KEY);
                serviceFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_LABEL));
                serviceFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                priceBreakup.add(serviceFee);
                totalTaxesAndSrvcFee += serviceFee.getAmount();
            }
        }
        if (totalTaxesAndSrvcFee > 0.0d) {
            PricingDetails taxesAndSrvcFee = new PricingDetails();
            taxesAndSrvcFee.setAmount(totalTaxesAndSrvcFee);
            taxesAndSrvcFee.setKey(Constants.TAXES_KEY);
            //Excluded Charges text to be shown above taxes for GCC on review page
            if (Utility.isGCC() && utility.isExperimentOn(expDataMap, GEC)) {
                taxesAndSrvcFee.setSubLine(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_EXCLUDED_TEXT_REVIEW_PAGE));
            }
            if (priceDetail.getTaxBreakUp().getHotelTax() > 0.0d || priceDetail.getTaxBreakUp().getServiceFee() > 0.0d) {
                taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData("TAXES_LISTING_DETAIL_LABEL"));
            }
            taxesAndSrvcFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            taxesAndSrvcFee.setBreakup(priceBreakup);
            pricingDetailsList.add(taxesAndSrvcFee);
        }
    }

    public String getOffersAppliedText(List<PricingDetails> pricingDetails, Map<String, String> expData, String currency, DiscountDetails discountDetails) {
        if (Utility.gocashVariant3(expData, StringUtils.equalsIgnoreCase(currency, DEFAULT_CUR_INR)) && discountDetails.getHybrid() > 0.0d && (expData.containsKey("recomFlow") && expData.get("recomFlow").equals("false"))) {
            return Constants.OFFERS_CASHBACK_APPLIED_TEXT;
        }
        else {
            for (PricingDetails priceDetail : pricingDetails) {
                if (Constants.TOTAL_DISCOUNT_KEY.equalsIgnoreCase(priceDetail.getKey()) && priceDetail.getBreakup() != null && priceDetail.getBreakup().size() > 0) {
                    return priceDetail.getBreakup().size() + OFFERS_APPLIED_TEXT;
                }
            }
        }
        return EMPTY_STRING;
    }

    /**
     * Build TotalPricing directly from orchestrator v2 PriceDetail
     */
    private TotalPricing buildTotalPricingFromPriceDetail(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail,
                                                          boolean isCorp, String segmentId, Map<String, String> expDataMap, boolean groupBookingFunnel,
                                                          String currency, String couponCode, LoyaltyMessageResponse loyaltyData, ClmPersuasion clmData,
                                                          AmountDetail paidAmount, Integer charityAmount, double bnplConvFees, double insuranceAmount) {
        TotalPricing totalPricing = new TotalPricing();

        // Build pricing details list directly from PriceDetail
        List<PricingDetails> pricingDetailsList = new ArrayList<>();
        // Add base fare if available
        if (priceDetail.getBasePrice() > 0.0d) {
            buildBaseFare(priceDetail, pricingDetailsList);
        }

        // Add discount if available
        PriceCouponInfo priceCouponInfo = CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons()) && StringUtils.isNotEmpty(couponCode) ?
                priceDetail.getApplicableCoupons().stream().filter(coupon -> couponCode.equalsIgnoreCase(coupon.getCouponCode())).findFirst().orElse(null) : null;
        buildTotalDiscounts(priceDetail, pricingDetailsList, priceCouponInfo);
        buildPriceAfterDiscount(priceDetail, pricingDetailsList, priceCouponInfo);
        buildTaxesAndServiceFee(priceDetail, pricingDetailsList, expDataMap);
        buildTotalAmount(priceDetail, pricingDetailsList, priceCouponInfo);
        totalPricing.setDetails(pricingDetailsList);

        totalPricing.setOffersAppliedText(getOffersAppliedText(totalPricing.getDetails(), expDataMap, currency, priceDetail.getDiscount()));

        // Build and set coupons list from PriceDetail
        if (CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            List<Coupon> coupons = buildCouponsFromPriceDetail(priceDetail, couponCode);
            totalPricing.setCoupons(coupons);
        }

        // Set no coupon text if no coupons available
        if (CollectionUtils.isEmpty(totalPricing.getCoupons())) {
            totalPricing.setNoCouponText(polyglotService.getTranslatedData(ConstantsTranslation.NO_COUPON_AVAILABLE_TEXT));
        }

//        totalPricing.setEmiBankDetails(getEmiDetails(displayPriceBrkDwn)); //todo

        // Set pricing key
        totalPricing.setPricingKey(priceDetail.getPricingKey());

        // Set coupon amount if available
        if (priceDetail.getDiscount().getCoupon() > 0.0d) {
            totalPricing.setCouponAmount(priceDetail.getDiscount().getCoupon());
        }

        // Set coupon description if coupon code is available
        if (StringUtils.isNotBlank(priceDetail.getCouponCode()) && CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo appliedCoupon = priceDetail.getApplicableCoupons().stream()
                    .filter(coupon -> priceDetail.getCouponCode().equals(coupon.getCouponCode()))
                    .findFirst()
                    .orElse(null);
            if (appliedCoupon != null) {
                totalPricing.setCouponDesc(appliedCoupon.getDescription());
                totalPricing.setCouponAmount(appliedCoupon.getDiscount());
            }
        }

        // Set coupon subtext for B2C clients (similar to legacy method)
        if (B2C.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()))) {
            totalPricing.setCouponSubtext(polyglotService.getTranslatedData(GIFT_CARD_TEXT_GI));
        }

        // Set EMI plan details from PriceDetail
		/*if (priceDetail.getEmiDetails() != null) {
			EMIPlanDetail emiPlanDetail = buildEmiPlanDetailsFromPriceDetail(priceDetail.getEmiDetails());
			if (emiPlanDetail != null) {
				totalPricing.setEmiPlanDetail(emiPlanDetail);
			}
		}*/

        return totalPricing;
    }

    /**
     * Calculate saving percentage from PriceDetail
     */
    private double calculateSavingPercentage(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail) {
        if (priceDetail.getBasePrice() > 0.0d && priceDetail.getTotalDiscount() > 0.0d) {
            return (priceDetail.getTotalDiscount() / priceDetail.getBasePrice()) * 100;
        }
        return 0.0;
    }

    /**
     * Build coupons list from OrchV2 PriceDetail
     */
    private List<Coupon> buildCouponsFromPriceDetail(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, String couponCode) {
        List<Coupon> coupons = null;
        if (CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo couponInfo : priceDetail.getApplicableCoupons()) {
                if (couponCode.equalsIgnoreCase(couponInfo.getCouponCode())) {
                    Coupon coupon = new Coupon();
                    coupon.setCode(couponInfo.getCouponCode());
                    coupon.setDescription(couponInfo.getDescription());
                    coupon.setCouponAmount(Math.max(couponInfo.getDiscount(), 0.0d));
                    if(coupon.getCouponAmount() <= 0.0d && priceDetail.getDiscount() != null) {
                        coupon.setCouponAmount(priceDetail.getDiscount().getInstantHybrid());
                    }
                    coupon.setBnplAllowed(couponInfo.isBnplAllowed());
                    coupon.setNoCostEmiApplicable(couponInfo.isNoCostEmiApplicable());
                    coupon.setAutoApplicable(priceDetail.getCouponCode().equalsIgnoreCase(couponInfo.getCouponCode()));
                    //coupon.setCouponType(couponInfo.getType());
                    coupon.setPromoIconLink(couponInfo.getPromoIconLink());
                    coupon.setBankOffer(couponInfo.isBankOffer());
                    coupon.setTncUrl(StringUtils.isEmpty(couponInfo.getTncUrl()) ? "" : couponInfo.getTncUrl());
                    coupon.setSubDescription(couponInfo.getSubDescription());
                    coupon.setCtaBankText(couponInfo.getCtaBankText());
                    coupon.setCtaBankUrl(couponInfo.getCtaBankUrl());
                    if (CollectionUtils.isEmpty(coupons)) {
                        coupons = new ArrayList<>();
                    }
                    coupons.add(coupon);
                }
            }
        }

        return coupons;
    }

    private void setGroupPriceAndSavingsText(TotalPricing totalPricing, Integer roomCount, Integer nightCount, long savingPerc, long displayPrice, boolean groupBookingFunnel, boolean groupBookingPrice, boolean ismyPartnerRequest) {
        if (groupBookingFunnel && totalPricing != null) {
            NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en","IN"));
            String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
            //the check has been put inCase of search-roomapi not to add displayPrice in groupPriceText
            if (utility.isDetailPageAPI(controller) && ismyPartnerRequest) {
                totalPricing.setGroupPriceText(commonResponseTransformer.getGroupPriceText(roomCount, nightCount));
            }
            else{
                totalPricing.setGroupPriceText(commonResponseTransformer.getGroupPriceText(roomCount, nightCount, numberFormat.format(displayPrice),""));
            }
            if(savingPerc != 0.0 && groupBookingPrice) {
                totalPricing.setSavingsText(polyglotService.getTranslatedData(SAVING_PERC_TEXT)
                        .replace("{PERCENTAGE}", String.valueOf(savingPerc)));
            }
        }
    }


    public EMIDetail buildEmiBankDetails(NoCostEmiDetails noCostEmiDetails) {
        if (noCostEmiDetails == null || noCostEmiDetails.getEmiAmount() == 0) {
            return null;
        }

        EMIDetail emiDetail = new EMIDetail();
        emiDetail.setAmount((double) noCostEmiDetails.getEmiAmount());
        emiDetail.setBankName(noCostEmiDetails.getBankName());
        emiDetail.setTenure(noCostEmiDetails.getTenure());
        return emiDetail;
    }

    public String convertNumericValueToCommaSeparatedString(int number, Locale formatter) {
        java.text.NumberFormat numberFormat = java.text.NumberFormat.getNumberInstance(formatter);
        return numberFormat.format(number);
    }

}
