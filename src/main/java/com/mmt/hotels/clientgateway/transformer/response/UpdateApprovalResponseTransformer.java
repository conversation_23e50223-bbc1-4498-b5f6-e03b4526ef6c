package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsApprover;
import com.mmt.hotels.clientgateway.helpers.ErrorHelper;
import com.mmt.hotels.clientgateway.response.CorpAutobookRequestorConfig;
import com.mmt.hotels.clientgateway.response.CtaBo;
import com.mmt.hotels.clientgateway.response.corporate.UpdateApprovalResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.corporate.UpdateWorkflowResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpdateApprovalResponseTransformer {

  @Autowired
  protected PolyglotService polyglotService;

  @Autowired
  ErrorHelper errorHelper;

  private static final Logger logger = LoggerFactory.getLogger(UpdateApprovalResponseTransformer.class);

  public UpdateApprovalResponse convertUpdateApprovalResponse(UpdateWorkflowResponse responseHES){
    UpdateApprovalResponse updateApprovalResponse = new UpdateApprovalResponse();
    if (responseHES.getResponseErrors() != null && CollectionUtils
                                                       .isNotEmpty(responseHES.getResponseErrors().getErrorList())) {
      String errorCode = responseHES.getResponseErrors().getErrorList().get(0).getErrorCode();
      SpecifiedErrorsApprover specificError = SpecifiedErrorsApprover.resolve(errorCode);
      if (specificError != null) {
        logger.warn("Error at convertUpdateApprovalResponse {}", specificError);
        updateApprovalResponse.setError(new Error(specificError.getErrorCode(), polyglotService.getTranslatedData(specificError.getSubTitle()), null, polyglotService.getTranslatedData(specificError.getTitle())));
      } else {
        updateApprovalResponse.setError(new Error(responseHES.getResponseErrors().getErrorList().get(0).getErrorCode(),
                responseHES.getResponseErrors().getErrorList().get(0)
                        .getErrorMessage()));
      }
    } else if (responseHES.getFailureReason() != null) {
      updateApprovalResponse.setError(new Error(responseHES.getFailureReason().getErrorCode(),
                                                responseHES.getFailureReason().getMsg()));
    }
    updateApprovalResponse.setMessage(responseHES.getMessage());
    updateApprovalResponse.setResponseCode(responseHES.getResponseCode());
    updateApprovalResponse.setStatus(responseHES.getStatus());
    updateApprovalResponse.setStatusCode(responseHES.getStatusCode());
    if (responseHES.getResponseConfig() != null) {
      CorpAutobookRequestorConfig updateConfig = new CorpAutobookRequestorConfig();
      updateConfig.setTitle(responseHES.getResponseConfig().getTitle());
      updateConfig.setSubTitle(responseHES.getResponseConfig().getSubTitle());
      if (responseHES.getResponseConfig().getCta() != null) {
        CtaBo cta = new CtaBo();
        cta.setText(responseHES.getResponseConfig().getCta().getText());
        updateConfig.setCta(cta);
      }
      updateApprovalResponse.setUpdateConfig(updateConfig);
    }
    return updateApprovalResponse;
  }
}
