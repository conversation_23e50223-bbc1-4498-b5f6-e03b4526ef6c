package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.RoomHighlightType;
import com.mmt.hotels.clientgateway.enums.UpgradeType;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.pricing.Inclusion;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.INCLUSION_TYPE_LOS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BNPL_ZERO_VARIANT_TEXT_GI_AVAILABLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STAR_FACILITIES_TITLE_GI;

@Component
public class SelectRoomV2ResponseTransformer {

    @Autowired
    private PolyglotService polyglotService;

    @Value("${los.title.text.colour}")
    private String losTitleTextColor;

    @Value("${los.icon.url.room}")
    private String losIconUrl;

    public void alterRoomDetailsForSelectRoomRevamp(RoomDetails roomDetails, String propertyType) {
        addSignatureAmenities(roomDetails);
        reorderRoomHighlights(roomDetails);
        restructureRoomHighlightsForHostels(roomDetails, propertyType);
        if (CollectionUtils.isNotEmpty(roomDetails.getRatePlans())) {
            alterRatePlanDetailsForSelectRoomRevamp(roomDetails.getRatePlans());
        }
    }

    private void restructureRoomHighlightsForHostels(RoomDetails roomDetails, String propertyType) {
        if (!PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType)) {
            return;
        }
        List<RoomHighlight> roomHighlights = new ArrayList<>();

        // keep only bed type highlight for hostels
        List<String> requiredHighlights = Arrays.asList(RoomHighlightType.BED_TYPE.name(),RoomHighlightType.ROOM_INFO.name(),RoomHighlightType.DORM_TYPE.name(),RoomHighlightType.DORM_BED_COUNT.name());
        if (CollectionUtils.isNotEmpty(roomDetails.getRoomHighlights())) {
            roomHighlights.addAll(roomDetails.getRoomHighlights().stream()
                    .filter(roomHighlight -> requiredHighlights.contains(roomHighlight.getIdentifier()))
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(roomDetails.getAmenities())) {
            // Back-fill other highlights with amenities.
            int amenitiesToShow = 2;
            for(FacilityGroup facilityGroup: roomDetails.getAmenities()){
                List<Facility>  facilities = facilityGroup.getFacilities();
                if(CollectionUtils.isNotEmpty(facilities)){
                    for(Facility facility: facilities){
                        if(amenitiesToShow==0){
                            break;
                        }
                        RoomHighlight roomHighlight = new RoomHighlight();
                        roomHighlight.setText(facility.getName());
                        roomHighlight.setDescription(facility.getName());
                        roomHighlights.add(roomHighlight);
                        amenitiesToShow--;
                    }
                }
                if(amenitiesToShow==0){
                    break;
                }
            }
        }
        int roomHighlightsSize = roomHighlights.size();

        while(roomHighlightsSize > 3){
                if(roomHighlightsSize > 0){
                roomHighlights.remove(roomHighlightsSize - 1);
            }
            roomHighlightsSize = roomHighlightsSize - 1;
        }

        roomDetails.setRoomHighlights(roomHighlights);
    }

    private void reorderRoomHighlights(RoomDetails roomDetails) {
        if (CollectionUtils.isNotEmpty(roomDetails.getRoomHighlights())) {
            List<RoomHighlight> selectRoomRevampOrderHighlights = roomDetails.getRoomHighlights().stream()
                    .filter(roomHighlight -> roomHighlight.getSelectRoomRevampOrder() != null)
                    .sorted(Comparator.comparingInt(RoomHighlight::getSelectRoomRevampOrder))
                    .collect(Collectors.toList());
            roomDetails.setRoomHighlights(selectRoomRevampOrderHighlights);
        }
    }

    private void addSignatureAmenities(RoomDetails roomDetails) {
        if (CollectionUtils.isNotEmpty(roomDetails.getAmenities())) {
            FacilityGroup signatureAmenitiesGroup = roomDetails.getAmenities().stream()
                    .filter(amenityGroup -> Constants.SIGNATURE_AMENITIES.equalsIgnoreCase(amenityGroup.getId()))
                    .findFirst()
                    .orElse(null);
            if (signatureAmenitiesGroup != null && CollectionUtils.isNotEmpty(signatureAmenitiesGroup.getFacilities())) {
                signatureAmenitiesGroup.setName(polyglotService.getTranslatedData(STAR_FACILITIES_TITLE_GI));
                signatureAmenitiesGroup.getFacilities().stream()
                        .limit(SIGNATURE_AMENITIES_SELECT_ROOM_COUNT)
                        .forEach(facility -> facility.setShowOnSelectRoom(true));
            }
        }
    }

    private void alterRatePlanDetailsForSelectRoomRevamp(List<SelectRoomRatePlan> ratePlans) {
        ratePlans.forEach(ratePlan -> {
            processRatePlan(ratePlan);
            alterRatePlanInclusions(ratePlan);
            setTopLeftGoTribePersuasion(ratePlan);
            setCancellationPolicyDetails(ratePlan);
            processLinkedRatePlan(ratePlan);
            // remove old select room goTribeInclusion details
            ratePlan.setGoTribeInclusion(null);
        });
    }



    private void processLinkedRatePlan(SelectRoomRatePlan ratePlan) {
        if (ratePlan != null && CollectionUtils.isNotEmpty(ratePlan.getLinkedRatePlans())) {
            ratePlan.getLinkedRatePlans().forEach(linkedRatePlan -> {
                SelectRoomRatePlan linkedRatePlanDetails = linkedRatePlan.getRatePlan();
                if (linkedRatePlanDetails != null) {
                    setCancellationPolicyDetails(linkedRatePlanDetails);
                    if (CollectionUtils.isNotEmpty(linkedRatePlanDetails.getInclusionsList())) {
                        for (BookedInclusion inclusion : linkedRatePlanDetails.getInclusionsList()) {
                            if (inclusion != null) {
                                inclusion.setShowOnSelectRoom(true);
                            }
                        }
                    }
                }
            });
        }
    }

    private void setCancellationPolicyDetails(SelectRoomRatePlan ratePlan) {
        BookedCancellationPolicy bookedCancellationPolicy = ratePlan.getCancellationPolicy();
        if (bookedCancellationPolicy != null) {
            BookedCancellationPolicyType policyType = bookedCancellationPolicy.getType();
            if (BookedCancellationPolicyType.NR.equals(policyType)) {
                bookedCancellationPolicy.setShortHtmlText(
                        polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SHORT_HTML_TEXT));
            } else if (BookedCancellationPolicyType.FC.equals(policyType) && ratePlan.getCancellationPolicyTimeline() != null) {
                String cancellationDate = ratePlan.getCancellationPolicyTimeline().getCancellationDate();
                if (StringUtils.isNotBlank(cancellationDate)) {
                    bookedCancellationPolicy.setShortHtmlText(
                            polyglotService.getTranslatedData(ConstantsTranslation.FREE_CANCELLATION_SHORT_HTML_TEXT)
                                    .replace(CANCELLATION_DATE_PLACEHOLDER, cancellationDate)
                    );
                } else {
                    bookedCancellationPolicy.setShortHtmlText(bookedCancellationPolicy.getText());
                }
            } else if (BookedCancellationPolicyType.PR.equals(policyType)) {
                bookedCancellationPolicy.setShortHtmlText(String.format(PARTIAL_REFUNDABLE_TEXT, bookedCancellationPolicy.getText()));
            } else {
                bookedCancellationPolicy.setShortHtmlText(bookedCancellationPolicy.getText());
            }
        }
    }

    private void processRatePlan(SelectRoomRatePlan ratePlan) {
        if (CollectionUtils.isNotEmpty(ratePlan.getInclusionsList())) {
            BookedInclusion zpnInclusion = ratePlan.getInclusionsList().stream()
                    .filter(inclusion -> ZPN.equalsIgnoreCase(inclusion.getCategory()))
                    .findFirst()
                    .orElse(null);
            if (zpnInclusion != null) {
                String bookAtZeroOrPAH = null;
                if (Constants.PAY_MODE.equalsIgnoreCase(zpnInclusion.getType())) {
                    bookAtZeroOrPAH = polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_HTML_TEXT_GI);
                } else if (CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(zpnInclusion.getType())) {
                    bookAtZeroOrPAH = polyglotService.getTranslatedData(ConstantsTranslation.BOOK_AT_ZERO_HTML_TEXT_GI);
                }
                if (StringUtils.isNotBlank(bookAtZeroOrPAH)) {
                    ratePlan.setBookAtZeroOrPAHtmlText(bookAtZeroOrPAH);
                }
            }
        }
    }

    private void setTopLeftGoTribePersuasion(SelectRoomRatePlan ratePlan) {
        if (ratePlan.getGoTribeInclusion() != null) {
            String upgradeType = ratePlan.getGoTribeInclusion().getUpgradeType();
            if (StringUtils.isNotBlank(upgradeType)) {
                UpgradeType upgradeTypeEnum = UpgradeType.valueOf(upgradeType);
                ratePlan.setTopLeftPersuasion(new RatePlanTopLeftPersuasion());
                ratePlan.getTopLeftPersuasion().setText(upgradeTypeEnum.getPersuasionText());
                ratePlan.getTopLeftPersuasion().setIconUrl(ratePlan.getGoTribeInclusion().getGoTribeIconUrlV2());
                ratePlan.getTopLeftPersuasion().setTextColor("#EB6125");
                ratePlan.getTopLeftPersuasion().setBorderGradient(new BorderGradient());
                ratePlan.getTopLeftPersuasion().getBorderGradient().setStart("#FFFFFF");
                ratePlan.getTopLeftPersuasion().getBorderGradient().setEnd("#FFECE5");
            }
        }
    }

    private void alterRatePlanInclusions(SelectRoomRatePlan ratePlan) {
        BookedInclusion goTribeInclusion = checkAndGetGoTribeInclusion(ratePlan);
        BookedInclusion losInclusion = checkAndGetLOSInclusion(ratePlan);
        BookedInclusion noMealInclusion = checkAndGetNoMealInclusion(ratePlan);
        BookedInclusion bookAtZeroInclusion = checkAndGetBookAtZeroInclusion(ratePlan);

        List<BookedInclusion> inclusionsList = new ArrayList<>();
        if (noMealInclusion != null) {
            inclusionsList.add(noMealInclusion);
        }
        inclusionsList.addAll(getDownstreamBookedInclusions(ratePlan));
        if (goTribeInclusion != null) {
            inclusionsList.add(goTribeInclusion);
        }
        if (losInclusion != null) {
            inclusionsList.add(losInclusion);
        }
        long showSelectRoomInclusionsCount = inclusionsList.stream().filter(BookedInclusion::isShowOnSelectRoom).count();
        if (showSelectRoomInclusionsCount < SELECT_ROOM_REVAMP_INCLUSIONS_MAX_COUNT) {
            int remainingCount = SELECT_ROOM_REVAMP_INCLUSIONS_MAX_COUNT - (int) showSelectRoomInclusionsCount;
            inclusionsList.stream()
                    .filter(inclusion -> !inclusion.isShowOnSelectRoom())
                    .limit(remainingCount)
                    .forEach(inclusion -> inclusion.setShowOnSelectRoom(true));
        }
        // Book at zero inclusion is added here because it will never be shown upfront.
        if (bookAtZeroInclusion != null) {
            inclusionsList.add(0, bookAtZeroInclusion);
        }
        ratePlan.setInclusionsList(inclusionsList);
    }

    private BookedInclusion checkAndGetBookAtZeroInclusion(SelectRoomRatePlan ratePlan) {
        if (ratePlan.getCancellationPolicyTimeline() != null
                && StringUtils.isNotBlank(ratePlan.getCancellationPolicyTimeline().getCardChargeTextTitle())
                && StringUtils.isNotBlank(ratePlan.getCancellationPolicyTimeline().getCardChargeTextMsg())) {
            BookedInclusion bookAtZeroInclusion = new BookedInclusion();
            bookAtZeroInclusion.setCode(polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT_GI_AVAILABLE));
            bookAtZeroInclusion.setText(polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT_GI_AVAILABLE));
            bookAtZeroInclusion.setSubText(String.join(Constants.BACK_SLASH_N, Arrays.asList(
                    ratePlan.getCancellationPolicyTimeline().getCardChargeTextTitle(),
                    ratePlan.getCancellationPolicyTimeline().getCardChargeTextMsg()
            )));
            bookAtZeroInclusion.setSubText(String.format("<font color='#714D0A'>%s</font>", bookAtZeroInclusion.getSubText()));
            bookAtZeroInclusion.setIconUrl(Constants.INCLUSIONS_DEFAULT_DOT_ICON_URL);
            bookAtZeroInclusion.setIUrl(INCLUSIONS_BULLETS_ICON_URL);
            // book at zero inclusion will not be visible upfront.
            bookAtZeroInclusion.setShowOnSelectRoom(false);
            return bookAtZeroInclusion;
        }
        return null;
    }

    private List<BookedInclusion> getDownstreamBookedInclusions(SelectRoomRatePlan ratePlan) {
        List<BookedInclusion> downstreamInclusions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ratePlan.getHesInclusions())) {
            for(Inclusion inclusion : ratePlan.getHesInclusions()) {
                // ignoring los & black inclusions as these inclusions are shown separately
                if (INCLUSION_TYPE_LOS.equalsIgnoreCase(inclusion.getInclusionType())
                        || BLACK_SEGMENT_IDENTIFIER.equalsIgnoreCase(inclusion.getSegmentIdentifier()))
                    continue;
                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setCode(inclusion.getCode());
                bookedInclusion.setSubText(inclusion.getValue());
                bookedInclusion.setText(inclusion.getCode());
                bookedInclusion.setIconUrl(inclusion.getIconUrl());
                bookedInclusion.setIUrl(INCLUSIONS_BULLETS_ICON_URL);
                downstreamInclusions.add(bookedInclusion);
            }
        }
        return downstreamInclusions;
    }

    private BookedInclusion checkAndGetNoMealInclusion(SelectRoomRatePlan ratePlan) {
        if (StringUtils.isNotBlank(ratePlan.getMealPlanCode())
                && Constants.NO_MEAL_PLAN_CODES.contains(ratePlan.getMealPlanCode())) {
            BookedInclusion noMealInclusion = new BookedInclusion();
            noMealInclusion.setCode(polyglotService.getTranslatedData(ConstantsTranslation.NO_MEALS_INCLUDED_TEXT));
            noMealInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.NO_MEALS_INCLUDED_TEXT));
            noMealInclusion.setIconUrl(Constants.INCLUSIONS_DEFAULT_DOT_ICON_URL);
            noMealInclusion.setIUrl(INCLUSIONS_BULLETS_ICON_URL);
            noMealInclusion.setShowOnSelectRoom(true);
            return noMealInclusion;
        }
        return null;
    }

    private BookedInclusion checkAndGetLOSInclusion(SelectRoomRatePlan ratePlan) {
        if (CollectionUtils.isEmpty(ratePlan.getHesInclusions()))
            return null;

        List<Inclusion> losInclusions = ratePlan.getHesInclusions().stream()
                .filter(inclusion -> INCLUSION_TYPE_LOS.equalsIgnoreCase(inclusion.getInclusionType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(losInclusions)) {
            BookedInclusion losInclusion = new BookedInclusion();
            losInclusion.setCode(polyglotService.getTranslatedData(ConstantsTranslation.LONG_STAY_INCLUSION_TEXT));
            losInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.LONG_STAY_INCLUSION_TEXT));
            losInclusion.setSubText(losInclusions.stream().map(Inclusion::getCode)
                    .collect(Collectors.joining(Constants.BACK_SLASH_N))
            );
            BenefitInclusionDetails inclusionDetails = new BenefitInclusionDetails();
            List<Benefit> benefitsList = losInclusions.stream().map(inclusion -> {
                Benefit benefit = new Benefit();
                benefit.setBenefitText(inclusion.getCode());
                benefit.setShowOnSelectRoom(true);
                benefit.setIconUrl(losIconUrl);
                return benefit;
            }).collect(Collectors.toList());
            inclusionDetails.setBenefitList(benefitsList);
            inclusionDetails.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.LOS_TITLE_TEXT));
            inclusionDetails.setTitleTextColor(losTitleTextColor);
            inclusionDetails.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.OK_GOT_IT_CTA_TEXT));
            losInclusion.setInclusionsDetails(inclusionDetails);
            losInclusion.setInclusionCode(LOS_INCLUSION_CODE);
            // Show on select room should be true if there is any benefit present with showOnSelectRoom as true
            losInclusion.setShowOnSelectRoom(benefitsList.stream().anyMatch(Benefit::isShowOnSelectRoom));
            losInclusion.setIconUrl(INCLUSIONS_DEFAULT_DOT_ICON_URL);
            losInclusion.setIUrl(INCLUSIONS_BULLETS_ICON_URL);
            return losInclusion;
        }

        return null;
    }

    private BookedInclusion checkAndGetGoTribeInclusion(SelectRoomRatePlan ratePlan) {
        if (ratePlan.getGoTribeInclusion() != null
                && CollectionUtils.isNotEmpty(ratePlan.getGoTribeInclusion().getBenefitsList())) {
            BookedInclusion goTribeInclusion = new BookedInclusion();
            goTribeInclusion.setCode(polyglotService.getTranslatedData(ConstantsTranslation.GO_TRIBE_INCLUSION_TEXT));
            goTribeInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.GO_TRIBE_INCLUSION_TEXT));
            goTribeInclusion.setSubText(ratePlan.getGoTribeInclusion().getBenefitsList().stream()
                    .map(BookedInclusion::getSubText)
                    .collect(Collectors.joining(Constants.BACK_SLASH_N))
            );
            BenefitInclusionDetails inclusionDetails = new BenefitInclusionDetails();
            List<Benefit> benefitsList = ratePlan.getGoTribeInclusion().getBenefitsList().stream()
                    .map(goTribeBenefit -> {
                        Benefit benefit = new Benefit();
                        benefit.setBenefitText(goTribeBenefit.getText());
                        benefit.setShowOnSelectRoom(StringUtils.isBlank(goTribeBenefit.getLeafCategory())
                                || !Constants.GO_TRIBE_UPGRADE_CATEGORIES.contains(goTribeBenefit.getLeafCategory())
                        );
                        benefit.setIconUrl(goTribeBenefit.getIconUrl());
                        benefit.setBenefitCode(goTribeBenefit.getCategory());
                        return benefit;
                    }).collect(Collectors.toList());
            inclusionDetails.setBenefitList(benefitsList);
            inclusionDetails.setIconUrl(ratePlan.getGoTribeInclusion().getCtaGoTribeIconUrlV2());
            goTribeInclusion.setInclusionsDetails(inclusionDetails);
            goTribeInclusion.setInclusionCode(Constants.GO_TRIBE_INCLUSION_CODE);
            // Show on select room should be true if there is any benefit present with showOnSelectRoom as true
            goTribeInclusion.setShowOnSelectRoom(benefitsList.stream().anyMatch(Benefit::isShowOnSelectRoom));
            goTribeInclusion.setIconUrl(INCLUSIONS_DEFAULT_DOT_ICON_URL);
            goTribeInclusion.setIUrl(INCLUSIONS_BULLETS_ICON_URL);
            return goTribeInclusion;
        }
        return null;
    }

    public boolean shouldSkipSelectRoom(List<RoomDetails> roomDetail, String listingType, boolean isRecommendation,
                                        Map<String, String> expData) {

        // Skip select room in case of single rate plan and single room type only for entire properties.
        if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)) {
            return !isRecommendation && CollectionUtils.isNotEmpty(roomDetail) && roomDetail.size() == 1
                    && CollectionUtils.isNotEmpty(roomDetail.get(0).getRatePlans()) && roomDetail.get(0).getRatePlans().size() == 1;
        }
        return false;
    }
}
