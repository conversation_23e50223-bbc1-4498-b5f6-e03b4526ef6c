package com.mmt.hotels.clientgateway.transformer.request;


import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class UpdatePolicyRequestTransformer {

    @Autowired
    CorporateHelper corporateHelper;

    @Autowired
    CommonHelper commonHelper;

    public UpdatePolicyRequest convertUpdatePolicyRequest(UpdatePolicyRequest updatePolicyRequest,
                                                          Map<String, String> headers, String correlationKey) {

        try{

            if (updatePolicyRequest != null) {
                String mmtAuth = commonHelper.getAuthToken(headers);
                UserServiceResponse userServiceRsp = commonHelper.getUserDetails(mmtAuth, "","","", correlationKey, Constants.CORP_ID_CONTEXT, null,null,headers);
                if(userServiceRsp!=null && userServiceRsp.getResult()!=null
                        && userServiceRsp.getResult().getExtendedUser()!=null) {

                    ExtendedUser user = userServiceRsp.getResult().getExtendedUser();
                    updatePolicyRequest.setBookerUuid(user.getUuid());
                    updatePolicyRequest.setProfileType(user.getProfileType());
                    updatePolicyRequest.setProfileId(user.getProfileId());
                    ScramblerClient scramblerClient = ScramblerClient.getInstance();
                    if(CollectionUtils.isNotEmpty(updatePolicyRequest.getTravellerEmailId())){
                        List<String> commEmails=new ArrayList<>();
                        for(String email:updatePolicyRequest.getTravellerEmailId()){
                            String commEmail=scramblerClient.encode(email, HashType.F);
                            commEmails.add(commEmail);
                        }
                        updatePolicyRequest.setTravellerEmailId(commEmails);
                    }
                }

            }

        }catch (Exception e){

        }
        return updatePolicyRequest;
    }
    }