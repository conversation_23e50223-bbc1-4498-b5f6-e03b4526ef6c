package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.UpdateApprovalRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.UpdateApprovalRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.UpdateApprovalRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.UpdateApprovalRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdateApprovalRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.UpdateApprovalResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.UpdateApprovalResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.UpdateApprovalResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.UpdateApprovalResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdateApprovalResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpdateApprovalFactory {
  @Autowired
  private UpdateApprovalRequestTransformerPWA updateApprovalRequestTransformerPWA;

  @Autowired
  private UpdateApprovalResponseTransformerPWA updateApprovalResponseTransformerPWA;

  @Autowired
  private UpdateApprovalRequestTransformerDesktop updateApprovalRequestTransformerDesktop;

  @Autowired
  private UpdateApprovalResponseTransformerDesktop updateApprovalResponseTransformerDesktop;

  @Autowired
  private UpdateApprovalRequestTransformerAndroid updateApprovalRequestTransformerAndroid;

  @Autowired
  private UpdateApprovalResponseTransformerAndroid updateApprovalResponseTransformerAndroid;

  @Autowired
  private UpdateApprovalRequestTransformerIOS updateApprovalRequestTransformerIOS;

  @Autowired
  private UpdateApprovalResponseTransformerIOS updateApprovalResponseTransformerIOS;

  public UpdateApprovalRequestTransformer getRequestService(String client) {
    if (StringUtils.isEmpty(client)) {
      return updateApprovalRequestTransformerDesktop;
    }
    switch (client) {
      case "PWA":
      case "MSITE":
        return updateApprovalRequestTransformerPWA;
      case "DESKTOP":
        return updateApprovalRequestTransformerDesktop;
      case "ANDROID":
        return updateApprovalRequestTransformerAndroid;
      case "IOS":
        return updateApprovalRequestTransformerIOS;
    }
    return updateApprovalRequestTransformerDesktop;
  }

  public UpdateApprovalResponseTransformer getResponseService(String client) {
    if (StringUtils.isEmpty(client)) {
      return updateApprovalResponseTransformerDesktop;
    }
    switch (client) {
      case "PWA":
      case "MSITE":
        return updateApprovalResponseTransformerPWA;
      case "DESKTOP":
        return updateApprovalResponseTransformerDesktop;
      case "ANDROID":
        return updateApprovalResponseTransformerAndroid;
      case "IOS":
        return updateApprovalResponseTransformerIOS;
    }
    return updateApprovalResponseTransformerDesktop;
  }
}
