package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;

@Component
public class OrchStaticDetailsResponseTransformerIOS extends OrchStaticDetailResponseTransformer {

    @Value("${star.host.icon.app}")
    private String starHostIconApp;

    @Override
    public Map<String, String> buildCardTitleMap() {
        return null;
    }

    @Override
    public void addTitleData(HotelResult hotelResult, String countryCode) {

    }

    @Override
    public String getLuxeIcon() {
        return LUXE_ICON_APPS;
    }

    @Override
    public StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if (staffInfo != null && BooleanUtils.isTrue(staffInfo.getIsStarHost())) {
            staffInfo.setStarHostIconUrl(starHostIconApp);
        }
        StaffInfo staffInfoCg = super.convertStaffInfo(staffInfo);
        return staffInfoCg;
    }
}
