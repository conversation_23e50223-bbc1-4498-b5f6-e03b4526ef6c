package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.transformer.response.PaymentResponseTransformer;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class PaymentResponseTransformerDesktop extends PaymentResponseTransformer {

    @Override
    public PaymentResponse processResponse(PaymentCheckoutResponse checkoutResponse, BeginCheckoutReqBody beginCheckoutReqBody){
        PaymentResponse paymentResponse =super.processResponse(checkoutResponse,beginCheckoutReqBody);
        if(checkoutResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(checkoutResponse.getResponseErrors().getErrorList()))
            return paymentResponse;
        else {
            paymentResponse.setFkToken(checkoutResponse.getPaymentParams() !=null && 
            		checkoutResponse.getPaymentParams().get("paymentPlatform") !=null ? checkoutResponse.getPaymentParams().get("paymentPlatform").toString() : null);
            paymentResponse.setThankYouUrl(checkoutResponse.getThankYouURL());
            paymentResponse.setCheckoutUrl(checkoutResponse.getPaymentParams() !=null && 
            		checkoutResponse.getPaymentParams().get("checkoutUrl") !=null ? checkoutResponse.getPaymentParams().get("checkoutUrl").toString() : null);

            return paymentResponse;
        }
    }
}
