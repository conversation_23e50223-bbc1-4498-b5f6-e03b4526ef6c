package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class FilterFactory {

    @Autowired
    FilterResponseTransformer filterResponseTransformer;

    @Autowired
    FilterRequestTransformer filterRequestTransformer;

    @Autowired
    FilterHelper filterHelper;

    @Autowired
    PropertyManager propertyManager;

    private final Gson gson = new Gson();

    @Autowired
    PolyglotHelper polyglotHelper;

    private String baseFilterSettings;
    private String suggestedFilterSettings;
    private String desktopCorpFilterSettings;
    private String desktopB2CFilterSettings;
    private String pwaCorpFilterSettings;
    private String pwaB2CFilterSettings;
    private String androidCorpFilterSettings;
    private String androidB2CFilterSettings;
    private String iosCorpFilterSettings;
    private String iosB2CFilterSettings;
    private String pwaMyPartnerFilterSettings;
    private String desktopMyPartnerFilterSettings;

    private Map<String, List<Filter>> compositeFilterMapping;


    /**
     * below categories will be added to base filter settings for FILTER_PILL_EXP HTL-38235
     **/
    @Value("${filter.meal.preferences.category}")
    private String mealPrefConfig;

    @Value("${filter.toggle.category}")
    private String toggleFilterConfig;

    @Value("${filter.homestay.toggle.category}")
    private String toggleFilterConfigHomestay;

    FilterConfigCategory mealPrefConfigCategory;

    FilterConfigCategory toggleConfigCategory;



    @PostConstruct
    public void init() {
        FilterConfig filterConfig = propertyManager.getProperty("cgGIFilterConfig", FilterConfig.class);
        baseFilterSettings = filterConfig.baseFilterSettings();
        suggestedFilterSettings = filterConfig.suggestedFilterSettings();
        desktopCorpFilterSettings = filterConfig.desktopCorpFilterSettings();
        desktopB2CFilterSettings = filterConfig.desktopB2CFilterSettings();
        pwaCorpFilterSettings = filterConfig.pwaCorpFilterSettings();
        pwaB2CFilterSettings = filterConfig.pwaB2CFilterSettings();
        androidB2CFilterSettings = filterConfig.androidB2CFilterSettings();
        androidCorpFilterSettings = filterConfig.androidCorpFilterSettings();
        iosB2CFilterSettings = filterConfig.iosB2CFilterSettings();
        iosCorpFilterSettings = filterConfig.iosCorpFilterSettings();
        compositeFilterMapping = filterConfig.compositeFilterConfig();

        // myPartner property filter settings
        pwaMyPartnerFilterSettings = filterConfig.pwaMyPartnerFilterSettings();
        desktopMyPartnerFilterSettings = filterConfig.desktopMyPartnerFilterSettings();

        filterConfig.addPropertyChangeListener("baseFilterSettings", event -> {
            baseFilterSettings = filterConfig.baseFilterSettings();
        });
        filterConfig.addPropertyChangeListener("suggestedFilterSettings", event -> {
            suggestedFilterSettings = filterConfig.suggestedFilterSettings();
        });
        filterConfig.addPropertyChangeListener("desktopCorpFilterSettings", event -> {
            desktopCorpFilterSettings = filterConfig.desktopCorpFilterSettings();
        });
        filterConfig.addPropertyChangeListener("desktopB2CFilterSettings", event -> {
            desktopB2CFilterSettings = filterConfig.desktopB2CFilterSettings();
        });
        filterConfig.addPropertyChangeListener("pwaCorpFilterSettings", event -> {
            pwaCorpFilterSettings = filterConfig.pwaCorpFilterSettings();
        });
        filterConfig.addPropertyChangeListener("pwaB2CFilterSettings", event -> {
            pwaB2CFilterSettings = filterConfig.pwaB2CFilterSettings();
        });
        filterConfig.addPropertyChangeListener("androidB2CFilterSettings", event -> {
            androidB2CFilterSettings = filterConfig.androidB2CFilterSettings();
        });
        filterConfig.addPropertyChangeListener("androidCorpFilterSettings", event -> {
            androidCorpFilterSettings = filterConfig.androidCorpFilterSettings();
        });
        filterConfig.addPropertyChangeListener("iosB2CFilterSettings", event -> {
            iosB2CFilterSettings = filterConfig.iosB2CFilterSettings();
        });
        filterConfig.addPropertyChangeListener("iosCorpFilterSettings", event -> {
            iosCorpFilterSettings = filterConfig.iosCorpFilterSettings();
        });

        // myPartner property change listeners
        filterConfig.addPropertyChangeListener("pwaMyPartnerFilterSettings", event -> {
            pwaMyPartnerFilterSettings = filterConfig.pwaMyPartnerFilterSettings();
        });
        filterConfig.addPropertyChangeListener("desktopMyPartnerFilterSettings", event -> {
            desktopMyPartnerFilterSettings = filterConfig.desktopMyPartnerFilterSettings();
        });

        filterConfig.addPropertyChangeListener("compositeFilterMapping", event -> {
            compositeFilterMapping = filterConfig.compositeFilterConfig();
        });

    }

    public FilterRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return filterRequestTransformer;
        switch (client.toUpperCase()) {
            case "PWA":
                return filterRequestTransformer;
            case "DESKTOP":
                return filterRequestTransformer;
            case "ANDROID":
                return filterRequestTransformer;
            case "IOS":
                return filterRequestTransformer;
        }
        return filterRequestTransformer;
    }

    public FilterResponseTransformer getResponseService(String client) {
    	if (StringUtils.isEmpty(client))
			return filterResponseTransformer;
        switch(client.toUpperCase()){
            case "PWA": return filterResponseTransformer;
            case "DESKTOP": return  filterResponseTransformer;
            case "ANDROID": return  filterResponseTransformer;
            case "IOS": return  filterResponseTransformer;
        }
        return filterResponseTransformer;
    }

    /*
    * myPartner change log :
    *   commonModifierResponse which contains profileType and affiliateId[subProfileType] is sent as another request parameter
    *   The values will be used to read myPartner filter configs
    *   We are modifying the current getFilterConfiguration,
    *   since the change affects only the test cases [test cases are modified as well to mock this object] and getFilterConfiguration is
    *   not used in any other service or  any functional part of the code base
    * */
    public FilterConfiguration getFilterConfiguration(String client, String idContext, String funnelSource, CommonModifierResponse commonModifierResponse){

        FilterConfiguration fConfig = null;
        fConfig = gson.fromJson(baseFilterSettings, new TypeToken<FilterConfiguration>() {
        }.getType());

        //HTL-38235 if FILTER_PILL_EXP is T we add meal preferences and toggle filters to config
        if (MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.FILTER_PILL_EXP))) {

            modifyFilterConfigForPillsExperiment(funnelSource, fConfig);

        }

        FilterConfiguration baseFilterSettingsModified = polyglotHelper.translateFilterConfig(fConfig,funnelSource);
        Map<String, Integer> suggFilConfig = gson.fromJson(suggestedFilterSettings, new TypeToken<Map<String, Integer>>() {
        }.getType());
        if(suggFilConfig.containsKey(Constants.SUGGESTED_FILTER_THRESHOLD)){
            baseFilterSettingsModified.setSuggestedFiltersThreshold(suggFilConfig.get(Constants.SUGGESTED_FILTER_THRESHOLD));
        }
        String baseFilterModified = gson.toJson(baseFilterSettingsModified);




        /*
         * myPartner change log :
         *   Since myPartner checks work completely on different request parameters [profile/subProfileType] instead of idContext
         *   we will not be using the switch case and these cases will entirely be moved to a block where the b2c flow is true
         * */
        if (Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId())) {
            /*
             * myPartner change log :
             *   We won't be switching on idContext, and just on the client under the myPartner isolated context
             * */
            switch (client){
                case "PWA":
                    return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaMyPartnerFilterSettings,funnelSource), idContext);
                case "DESKTOP":
                    return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(desktopMyPartnerFilterSettings,funnelSource), idContext);
                default :
                    return  filterHelper.getFilterConfig(baseFilterModified, idContext);
            }
        }else {
            /*
             * myPartner change log :
             *   myPartner flow is retained as is
             * */
            String key = client+"_B2C"; // We don't need CORP for GI usecase. TODO : Code to be removed during clean up later.
            switch (key){
                case "PWA_CORP":
                    return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaCorpFilterSettings,funnelSource), idContext);
                case "PWA_B2C":
                    return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(pwaB2CFilterSettings,funnelSource), idContext);
                case "ANDROID_CORP":
                    return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(androidCorpFilterSettings,funnelSource), idContext);
                case "ANDROID_B2C":
                    return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(androidB2CFilterSettings,funnelSource), idContext);
                case "DESKTOP_B2C":
                    return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(desktopB2CFilterSettings,funnelSource), idContext);
                case "DESKTOP_CORP":
                    return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopCorpFilterSettings,funnelSource), idContext);
                case "IOS_B2C":
                    return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(iosB2CFilterSettings,funnelSource), idContext);
                case "IOS_CORP":
                    return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(iosCorpFilterSettings,funnelSource), idContext);
                default:
                    return  filterHelper.getFilterConfig(baseFilterModified, idContext);

            }
        }
    }


    /**
     * this method adds meal_preferences and toggle_filters category to base filter settings HTL-38235
     **/
    private void modifyFilterConfigForPillsExperiment(String funnelSource, FilterConfiguration fConfig) {
        if (StringUtils.isNotBlank(mealPrefConfig) && StringUtils.isNotBlank(toggleFilterConfigHomestay) && StringUtils.isNotBlank(toggleFilterConfig)) {
            mealPrefConfigCategory = gson.fromJson(mealPrefConfig, new TypeToken<FilterConfigCategory>() {
            }.getType());

            if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
                toggleConfigCategory = gson.fromJson(toggleFilterConfigHomestay, new TypeToken<FilterConfigCategory>() {
                }.getType());
            } else {
                toggleConfigCategory = gson.fromJson(toggleFilterConfig, new TypeToken<FilterConfigCategory>() {
                }.getType());
            }
            if (fConfig != null && fConfig.getFilters() != null) {
                fConfig.getFilters().put(Constants.MEAL_PREFERENCE_CATEGORY, mealPrefConfigCategory);
                fConfig.getFilters().put(Constants.TOGGLE_FILTERS_CATEGORY, toggleConfigCategory);
            }
        }
    }

    public String modifiedFilterWithPolyglotData(String filter,String funnelSource){
        FilterConfiguration fConfig = null;
        fConfig = gson.fromJson(filter, new TypeToken<FilterConfiguration>() {
        }.getType());

        polyglotHelper.translateFilterConfig(fConfig,funnelSource);
        String modifiedFilter = gson.toJson(fConfig);
        return modifiedFilter;
    }

    public Map<String, List<Filter>> getCompositeFilterConfig() {
        return compositeFilterMapping;
    }

}
