package com.mmt.hotels.clientgateway.transformer.response.pwa;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.RatePlanFilter;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class SearchRoomsResponseTransformerPWA extends SearchRoomsResponseTransformer{

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsResponseTransformerPWA.class);

    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        PersuasionResponse persuasion = new PersuasionResponse();
        StringBuilder persuasionAppliedText=new StringBuilder();
        if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ) {
            LOGGER.debug("loyalty_offer_message: {}", coupon.getLoyaltyOfferMessage());
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT), coupon.getLoyaltyOfferMessage()));
        }
        else{
            LOGGER.debug("Promo_Cash_Amount: {}",coupon.getHybridDiscounts().get("CTW"));
            int cashbackDiscountAmtRounded = (int) Math.round(coupon.getHybridDiscounts().get("CTW"));
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT_PWA),cashbackDiscountAmtRounded));
        }
        String iconType= StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage())? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
        persuasion.setPersuasionText(persuasionAppliedText.toString());
        persuasion.setHtml(true);
        persuasion.setIconType(iconType);
        persuasionMap.put (CASHBACK_HERO_OFFER_PERSUASION_NODE, persuasion);
    }

    @Override
    protected GroupRatePlanFilter buildGroupFilterForDevice(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation) {
        return buildStaycationFilter(groupRatePlanFilterConfMap, filterCriteria, staycation);
    }

    @Override
    public PersuasionObject createTopRatedPersuasion() {
        return createTopRatedPersuasionForMoblie();
    }
}
