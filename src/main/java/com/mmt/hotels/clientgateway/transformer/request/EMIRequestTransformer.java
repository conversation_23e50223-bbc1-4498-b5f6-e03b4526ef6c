package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.UpdatedEMIRoomCriteria;
import com.mmt.hotels.clientgateway.request.UpdatedEMISearchCriteria;
import com.mmt.hotels.clientgateway.request.UpdatedEmiRequest;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.model.request.ExtraInfo;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.model.response.emi.EmiDetailsRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class EMIRequestTransformer extends BaseEMIRequestTransformer {
    public UpdateEmiDetailRequest convertEmiRequest(UpdatedEmiRequest updatedEmiRequest, CommonModifierResponse commonModifierResponse) {
        UpdateEmiDetailRequest updateEmiDetailRequest = new UpdateEmiDetailRequest();
        super.buildDeviceDetails(updateEmiDetailRequest, updatedEmiRequest.getDeviceDetails());
        buildSearchCriteria(updateEmiDetailRequest, updatedEmiRequest.getSearchCriteria(), commonModifierResponse);
        buildRequestDetails(updateEmiDetailRequest, updatedEmiRequest.getRequestDetails());
        updateEmiDetailRequest.setEmiDetails(buildEmiDetail(updatedEmiRequest.getEmiDetail()));
        updateEmiDetailRequest.setExperimentData(updatedEmiRequest.getExpData());
        updateEmiDetailRequest.setSrcClient(updatedEmiRequest.getClient());
        return updateEmiDetailRequest;
    }

    private void buildSearchCriteria(UpdateEmiDetailRequest updateEmiDetailRequest,
                                     UpdatedEMISearchCriteria searchCriteria, CommonModifierResponse commonModifierResponse) {
        updateEmiDetailRequest.setHotelId(searchCriteria.getHotelId());
        super.populateSearchCriteria(updateEmiDetailRequest, searchCriteria);
        updateEmiDetailRequest.setRoomCriteria(buildRoomCriteria(searchCriteria.getRoomCriteria()));
        updateEmiDetailRequest.setRoomStayCandidates(buildRoomStayCandidates(searchCriteria.getRoomStayCandidates()));
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setSearchType(searchCriteria.getSearchType());
        updateEmiDetailRequest.setExtraInfo(extraInfo);
    }

    private List<RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {

        if (roomStayCandidates == null)
            return null;

        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates) {
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG) {


        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidateCG.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;

    }

    private List<RoomCriterion> buildRoomCriteria(List<UpdatedEMIRoomCriteria> roomCriteria) {

        if (CollectionUtils.isNotEmpty(roomCriteria)) {
            List<RoomCriterion> roomCriterionList = new ArrayList<>();

            for (UpdatedEMIRoomCriteria roomCriteriaCG : roomCriteria) {
                RoomCriterion roomCriterion = new RoomCriterion();
                roomCriterion.setPricingKey(roomCriteriaCG.getPricingKey());
                roomCriterion.setRatePlanCode(roomCriteriaCG.getRatePlanCode());
                roomCriterion.setRoomCode(roomCriteriaCG.getRoomCode());
                List<String> pricingKeys = new ArrayList<>();
                pricingKeys.add(roomCriteriaCG.getPricingKey());
                roomCriterion.setPricingKeys(pricingKeys);
                roomCriterion.setPaymentMode(roomCriteriaCG.getPaymentMode());
                roomCriterionList.add(roomCriterion);
            }
            return roomCriterionList;
        }

        return null;

    }

    private void buildRequestDetails(UpdateEmiDetailRequest updateEmiDetailRequest, RequestDetails requestDetails) {

        updateEmiDetailRequest.setIdContext(requestDetails.getIdContext());
        updateEmiDetailRequest.setChannel(requestDetails.getChannel());
        updateEmiDetailRequest.setPaymentChannel(requestDetails.getChannel());
        updateEmiDetailRequest.setPageContext(requestDetails.getPageContext());
        updateEmiDetailRequest.setVisitNumber(requestDetails.getVisitNumber() != null ?
                String.valueOf(requestDetails.getVisitNumber()) : "");
        updateEmiDetailRequest.setLoggedIn(requestDetails.isLoggedIn());
        updateEmiDetailRequest.setSiteDomain(requestDetails.getSiteDomain());
        updateEmiDetailRequest.setCouponCount(requestDetails.getCouponCount() != null ? requestDetails.getCouponCount() : 0);
    }

    private EmiDetailsRequest buildEmiDetail(EMIDetail emiDetail) {

        if (emiDetail == null)
            return null;

        EmiDetailsRequest emiDetailCB = new EmiDetailsRequest();
        emiDetailCB.setBankId(emiDetail.getBankId());
        emiDetailCB.setBankName(emiDetail.getBankName());
        emiDetailCB.setPayOption(emiDetail.getPayOption());
        emiDetailCB.setTenure(emiDetail.getTenure());
        return emiDetailCB;

    }
}
