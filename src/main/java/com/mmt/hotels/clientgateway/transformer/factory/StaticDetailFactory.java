package com.mmt.hotels.clientgateway.transformer.factory;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.request.StaticDetailRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.StaticDetailRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.StaticDetailRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.StaticDetailRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.StaticDetailRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.StaticDetailResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.StaticDetailResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.StaticDetailResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.StaticDetailResponseTransformerPWA;

@Component
public class StaticDetailFactory {
	
	@Autowired
	private StaticDetailRequestTransformerPWA staticDetailRequestTransformerPWA;
	
	@Autowired
	private StaticDetailResponseTransformerPWA staticDetailResponseTransformerPWA;
	
	@Autowired
	private StaticDetailRequestTransformerDesktop staticDetailRequestTransformerDesktop;
	
	@Autowired
	private StaticDetailResponseTransformerDesktop staticDetailResponseTransformerDesktop;
	
	@Autowired
	private StaticDetailRequestTransformerAndroid staticDetailRequestTransformerAndroid;
	
	@Autowired
	private StaticDetailResponseTransformerAndroid staticDetailResponseTransformerAndroid;
	
	@Autowired
	private StaticDetailRequestTransformerIOS staticDetailRequestTransformerIOS;
	
	@Autowired
	private StaticDetailResponseTransformerIOS staticDetailResponseTransformerIOS;
	
	public StaticDetailRequestTransformer getRequestService(String client) {
		if (StringUtils.isEmpty(client))
			return staticDetailRequestTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return staticDetailRequestTransformerPWA;
			case "DESKTOP": return staticDetailRequestTransformerDesktop;
			case "ANDROID": return staticDetailRequestTransformerAndroid;
			case "IOS": return staticDetailRequestTransformerIOS;
		}
		return staticDetailRequestTransformerDesktop;
	}

	public StaticDetailResponseTransformer getResponseService(String client) {
		if (StringUtils.isEmpty(client))
			return staticDetailResponseTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return staticDetailResponseTransformerPWA;
			case "DESKTOP": return staticDetailResponseTransformerDesktop;
			case "ANDROID": return staticDetailResponseTransformerAndroid;
			case "IOS": return staticDetailResponseTransformerIOS;
		}
		return staticDetailResponseTransformerDesktop;
	}
}
