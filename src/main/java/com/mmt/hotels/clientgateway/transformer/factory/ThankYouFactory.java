package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.ThankYouResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.ThankYouResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.ThankYouResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ThankYouResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ThankYouFactory {

    @Autowired
    private ThankYouResponseTransformerPWA thankYouResponseTransformerPWA;

    @Autowired
    private ThankYouResponseTransformerAndroid thankYouResponseTransformerAndroid;

    @Autowired
    private ThankYouResponseTransformerDesktop thankYouResponseTransformerDesktop;

    @Autowired
    private ThankYouResponseTransformerIOS thankYouResponseTransformerIOS;

    public ThankYouResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return thankYouResponseTransformerDesktop;
        switch (client) {
            case "PWA":
            case "MSITE":
                return thankYouResponseTransformerPWA;
            case "DESKTOP":
                return thankYouResponseTransformerDesktop;
            case "ANDROID":
                return thankYouResponseTransformerAndroid;
            case "IOS":
                return thankYouResponseTransformerIOS;
        }
        return thankYouResponseTransformerDesktop;
    }
}
