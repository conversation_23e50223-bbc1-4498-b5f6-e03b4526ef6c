package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.LogicalErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.LogicalException;
import com.mmt.hotels.clientgateway.helpers.HermesHelper;
import com.mmt.hotels.clientgateway.response.hermes.HermesPriceApiResponse;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.HermesRatePlanData;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.PaxWiseInfoResponse;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.PriceDetail;
import com.mmt.hotels.clientgateway.response.hermes.rateplaninfo.MultiPaxRatePlanInfo;
import com.mmt.hotels.clientgateway.response.hermes.rateplaninfo.RecommendedRatePlanInfo;
import com.mmt.hotels.clientgateway.response.hermes.rateplaninfo.RegularRatePlanInfo;
import com.mmt.hotels.clientgateway.response.hermes.roominfo.MultiPaxRoomInfo;
import com.mmt.hotels.clientgateway.response.hermes.roominfo.RecommendedRoomsInfo;
import com.mmt.hotels.clientgateway.response.hermes.roominfo.RegularRoomsInfo;
import com.mmt.hotels.clientgateway.response.rooms.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class InjectHermesResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(InjectHermesResponseTransformer.class);
    @Autowired
    HermesHelper hermesHelper;

    public SearchRoomsResponse injectHermesRatePlanData(SearchRoomsResponse searchRoomsResponse,
                                                        HermesPriceApiResponse hermesPriceApiResponse, String uuid, Map<String, String> expData) throws ClientGatewayException {
        if (MapUtils.isNotEmpty(expData) && ((expData.containsKey("unificationReviewV2") && StringUtils.equalsIgnoreCase(expData.get("unificationReviewV2"), "true")) || ((expData.containsKey(Constants.FlyerRatesLoggedOutExp) &&
                StringUtils.equalsIgnoreCase(expData.get(Constants.FlyerRatesLoggedOutExp), "true"))))) {
            return searchRoomsResponse;
        }
        if (searchRoomsResponse.getExactRooms() != null && searchRoomsResponse.getExactRooms().size() > 0) {
            List<RegularRoomsInfo> regularRoomsInfoList = null;
            try {
                regularRoomsInfoList = hermesPriceApiResponse.getData().getRegularRoomsInfoList();
            } catch (NullPointerException ignored) {}

            // Case when we do not get regular rooms from hermes.
            if (regularRoomsInfoList == null || regularRoomsInfoList.size() == 0) {
                LOGGER.warn("Exact room match not received from Hermes for UUID {}", uuid);
                throw new LogicalException(DependencyLayer.HERMES, ErrorType.LOGICAL,
                        LogicalErrors.NO_COMMON_RATE_PLAN.getErrorCode(),
                        LogicalErrors.NO_COMMON_RATE_PLAN.getErrorMsg());
            }
            // Merge hermes rate plan data with HES rate plan
            List<RoomDetails> roomDetails = mergeHermesHESExactRoomsRatePlan(searchRoomsResponse.getExactRooms(),
                    regularRoomsInfoList);
            // There must be at least a common room between hermes and HES.
            if (roomDetails.size() == 0) {
                LOGGER.warn("No common Exact room received from Hermes for UUID {}", uuid);
                throw new LogicalException(DependencyLayer.HERMES, ErrorType.LOGICAL,
                        LogicalErrors.NO_COMMON_RATE_PLAN.getErrorCode(),
                        LogicalErrors.NO_COMMON_RATE_PLAN.getErrorMsg());
            }else{
                searchRoomsResponse.setOccupancyRooms(null);
            }
            searchRoomsResponse.setExactRooms(roomDetails);
            if (hermesPriceApiResponse.getMetaInfo() != null &&
                    hermesPriceApiResponse.getMetaInfo().getCarryForwardHash() != null) {
                searchRoomsResponse.setCarryForwardHash(hermesPriceApiResponse.getMetaInfo().getCarryForwardHash());
            }
            return searchRoomsResponse;
        }

        if (searchRoomsResponse.getRecommendedCombos() != null
                && searchRoomsResponse.getRecommendedCombos().size() > 0) {
            List<RecommendedRoomsInfo> recommendedRoomsInfoList = null;
            try {
                recommendedRoomsInfoList = hermesPriceApiResponse.getData().getRecommendedRoomsInfoList();
            } catch (NullPointerException ignored) {}
            boolean commonComboFound = false;
            if (recommendedRoomsInfoList != null && recommendedRoomsInfoList.size() > 0) {
                List<RecommendedCombo> recommendedCombos = mergeHermesHESRecommendedRatePlan(
                        searchRoomsResponse.getRecommendedCombos(), recommendedRoomsInfoList);
                if (recommendedCombos.size() > 0) {
                    searchRoomsResponse.setRecommendedCombos(recommendedCombos);
                    commonComboFound = true;
                }
            }
            if (!commonComboFound) {
                LOGGER.warn("No common Recommended combo found for UUID {}", uuid);
                searchRoomsResponse.setRecommendedCombos(null);
            }
        }

        if (searchRoomsResponse.getOccupancyRooms() != null && searchRoomsResponse.getOccupancyRooms().size() > 0) {
            List<MultiPaxRoomInfo> multiPaxRoomInfoList = null;
            try {
                multiPaxRoomInfoList = hermesPriceApiResponse.getData().getMultiPaxRoomInfoList();
            } catch (NullPointerException ignored) {}

            boolean commonOccupancyRooms = false;
            if (multiPaxRoomInfoList != null && multiPaxRoomInfoList.size() > 0) {
                List<RoomDetails> occupancyRooms = mergeHermesHESOccupancyRatePlan(
                        searchRoomsResponse.getOccupancyRooms(), multiPaxRoomInfoList);
                if (occupancyRooms.size() > 0) {
                    searchRoomsResponse.setOccupancyRooms(occupancyRooms);
                    commonOccupancyRooms = true;
                }
            }
            if (!commonOccupancyRooms) {
                LOGGER.warn("No common occupancy rooms found for UUID {}", uuid);
                searchRoomsResponse.setOccupancyRooms(null);
            }
        }
        // If we don't get any recommended or occupancy rooms, it is impossible for user to book anything.
        // So throw exception in this particular case.
        if (searchRoomsResponse.getOccupancyRooms() == null && searchRoomsResponse.getRecommendedCombos() == null) {
            LOGGER.warn("No Common Recommended and Occupancy room found for UUID {}", uuid);
            throw new LogicalException(DependencyLayer.HERMES, ErrorType.LOGICAL,
                    LogicalErrors.NO_COMMON_RATE_PLAN.getErrorCode(),
                    LogicalErrors.NO_COMMON_RATE_PLAN.getErrorMsg());
        }
        return searchRoomsResponse;
    }

    private List<RoomDetails> mergeHermesHESExactRoomsRatePlan(List<RoomDetails> exactRooms,
                                                               List<RegularRoomsInfo> regularRoomsInfoList) {
        List<RoomDetails> commonRooms = new ArrayList<>();
        exactRooms.forEach(roomDetails -> {
            Set<String> hesSegment = new HashSet<>();
            Set<String> hermesSegment = new HashSet<>();
            String roomCode = roomDetails.getRoomCode();
            Optional<RegularRoomsInfo> regularRoomsInfo = regularRoomsInfoList.stream()
                    .filter(roomInfo -> roomInfo.getRoomTypeCode().equalsIgnoreCase(roomCode))
                    .findFirst();
            List<SelectRoomRatePlan> commonRatePlans = new ArrayList<>();
            if(!regularRoomsInfo.isPresent()){
             LOGGER.warn("{} roomcode not received from hermes",roomCode);
            }
            regularRoomsInfo.ifPresent(roomInfo -> {
                roomDetails.getRatePlans().forEach(selectRoomRatePlan -> {
                    Optional<RegularRatePlanInfo> regularRatePlanInfo = roomInfo.getRatePlanList().stream()
                            .filter(ratePlan -> ratePlan.getRatePlanCode().equalsIgnoreCase(selectRoomRatePlan.getRpc()))
                            .findFirst();
                    if(!regularRatePlanInfo.isPresent()){
                        LOGGER.warn("{} ratePlan for {} room not received from hermes",selectRoomRatePlan.getRpc(),roomInfo.getRoomTypeCode());
                        try {
                            String[] hesList = selectRoomRatePlan.getRpc().split(":");
                            hesSegment.add(hesList[2]);
                            for (RegularRatePlanInfo ratePlanList : roomInfo.getRatePlanList()) {
                                if (!selectRoomRatePlan.getRpc().equals(ratePlanList.getRatePlanCode())) {
                                    String[] hermesList = ratePlanList.getRatePlanCode().split(":");
                                    hermesSegment.add(hermesList[2]);
                                }
                            }
                        } catch (Exception ex) {
                            LOGGER.error("Segment id diff parsing error {}", ex.getMessage(), ex);
                        }
                    }
                    regularRatePlanInfo.ifPresent(ratePlanInfo -> {
                        HermesRatePlanData hermesRatePlanData = new HermesRatePlanData();
                        hermesRatePlanData.setSegmentId(ratePlanInfo.getSegmentId());
                        hermesRatePlanData.setPaymentMode(ratePlanInfo.getPaymentMode());
                        hermesRatePlanData.setOfferCode(ratePlanInfo.getOfferCode());
                        hermesRatePlanData.setPromoCode(ratePlanInfo.getPromoCode());
                        PaxWiseInfoResponse paxWiseInfoResponse = new PaxWiseInfoResponse();
                        paxWiseInfoResponse.setHermesMetaData(ratePlanInfo.getHermesMetaData());
                        paxWiseInfoResponse.setForwardParams(ratePlanInfo.getForwardParams());
                        paxWiseInfoResponse.setContractType(ratePlanInfo.getContractType());
                        if (ratePlanInfo.getPriceDetail() != null) {
                            PriceDetail priceDetail = new PriceDetail();
                            priceDetail.setPax(ratePlanInfo.getPriceDetail().getPax());
                            paxWiseInfoResponse.setPriceDetail(priceDetail);
                        }
                        hermesRatePlanData.setPaxWiseInfo(Collections.singletonList(paxWiseInfoResponse));
                        if (selectRoomRatePlan.getTariffs().size() > 0) {
                            selectRoomRatePlan.getTariffs().get(0).setHermesData(
                                    Collections.singletonList(hermesRatePlanData)
                            );
                        }
                        commonRatePlans.add(selectRoomRatePlan);
                    });
                });
            });
            if (commonRatePlans.size() > 0) {
                roomDetails.setRatePlans(commonRatePlans);
                commonRooms.add(roomDetails);
            } else {
                LOGGER.warn("segmentId Diff HES {} hermes {}",hesSegment, hermesSegment);
            }
        });
        return commonRooms;
    }

    private List<RecommendedCombo> mergeHermesHESRecommendedRatePlan(
            List<RecommendedCombo> recommendedCombos, List<RecommendedRoomsInfo> recommendedRoomsInfoList) {
        Map<String, RecommendedRoomsInfo> comboKeyValueMap = new HashMap<>();
        List<RecommendedCombo> response = new ArrayList<>();
        recommendedRoomsInfoList.forEach(recommendedRoomsInfo -> {
            comboKeyValueMap.put(recommendedRoomsInfo.getComboKey(), recommendedRoomsInfo);
        });
        LOGGER.warn("Combo size : {}",comboKeyValueMap.size());
        for (RecommendedCombo cgCombo: recommendedCombos) {
            String hermesComboId = hermesHelper.getHermesComboId(cgCombo);
            if (hermesComboId != null && comboKeyValueMap.get(hermesComboId) != null) {
                RecommendedRoomsInfo hermesComboInfo = comboKeyValueMap.get(hermesComboId);
                Map<String, SelectRoomRatePlan> cgRatePlanMap = new HashMap<>();
                cgCombo.getRooms().forEach(roomDetails -> {
                    roomDetails.getRatePlans().forEach(ratePlan -> {
                        cgRatePlanMap.put(String.format("%s_%s", roomDetails.getRoomCode(), ratePlan.getRpc()),
                                ratePlan);
                    });
                });
                for (RecommendedRatePlanInfo ratePlanInfo: hermesComboInfo.getRatePlanList()) {
                    String ratePlanKey = String.format("%s_%s", ratePlanInfo.getRoomTypeCode(),
                            ratePlanInfo.getRatePlanCode());
                    if (cgRatePlanMap.get(ratePlanKey) != null) {
                        SelectRoomRatePlan cgRatePlan = cgRatePlanMap.get(ratePlanKey);
                        HermesRatePlanData hermesRatePlanData = new HermesRatePlanData();

                        hermesRatePlanData.setSegmentId(ratePlanInfo.getSegmentId());
                        hermesRatePlanData.setPaymentMode(ratePlanInfo.getPaymentMode());
                        hermesRatePlanData.setOfferCode(ratePlanInfo.getOfferCode());
                        hermesRatePlanData.setPromoCode(ratePlanInfo.getPromoCode());

                        List<PaxWiseInfoResponse> paxWiseInfoResponseList = new ArrayList<>();
                        ratePlanInfo.getPaxWiseInfoList().forEach(hermesPaxWiseInfo -> {
                            PaxWiseInfoResponse paxWiseInfoResponse = new PaxWiseInfoResponse();
                            paxWiseInfoResponse.setHermesMetaData(hermesPaxWiseInfo.getHermesMetaData());
                            paxWiseInfoResponse.setForwardParams(hermesPaxWiseInfo.getForwardParams());
                            paxWiseInfoResponse.setContractType(hermesPaxWiseInfo.getContractType());
                            PriceDetail priceDetail = new PriceDetail();
                            priceDetail.setPax(hermesPaxWiseInfo.getPriceDetail().getPax());
                            paxWiseInfoResponse.setPriceDetail(priceDetail);
                            paxWiseInfoResponseList.add(paxWiseInfoResponse);
                        });
                        hermesRatePlanData.setPaxWiseInfo(paxWiseInfoResponseList);

                        if (cgRatePlan.getTariffs().get(0).getHermesData() != null) {
                            cgRatePlan.getTariffs().get(0).getHermesData().add(hermesRatePlanData);
                        } else {
                            List<HermesRatePlanData> hermesRatePlanDataList = new ArrayList<>();
                            hermesRatePlanDataList.add(hermesRatePlanData);
                            cgRatePlan.getTariffs().get(0).setHermesData(hermesRatePlanDataList);
                        }
                    }
                }
                HermesRatePlanData hermesComboData = new HermesRatePlanData();
                PaxWiseInfoResponse paxWiseInfoResponse = new PaxWiseInfoResponse();
                if (hermesComboInfo.getPriceDetail() != null) {
                    PriceDetail priceDetail = new PriceDetail();
                    priceDetail.setPax(hermesComboInfo.getPriceDetail().getPax());
                    paxWiseInfoResponse.setPriceDetail(priceDetail);
                }
                hermesComboData.setPaxWiseInfo(Collections.singletonList(paxWiseInfoResponse));
                hermesComboData.setPromoCode(hermesComboInfo.getPromoCode());
                cgCombo.getComboTariff().setHermesData(Collections.singletonList(hermesComboData));
                response.add(cgCombo);
            }else{
                LOGGER.warn("{} comboId not found",hermesComboId);
            }
        }
        return response;
    }

    private List<RoomDetails> mergeHermesHESOccupancyRatePlan(List<RoomDetails> occupancyRooms,
                                                              List<MultiPaxRoomInfo> multiPaxRoomInfoList) {
        List<RoomDetails> commonRooms = new ArrayList<>();
        occupancyRooms.forEach(roomDetails -> {
            Optional<MultiPaxRoomInfo> multiPaxRoomInfo = multiPaxRoomInfoList.stream()
                    .filter(roomInfo -> roomInfo.getRoomTypeCode().equalsIgnoreCase(roomDetails.getRoomCode()))
                    .findFirst();
            List<SelectRoomRatePlan> commonRatePlan = new ArrayList<>();
            multiPaxRoomInfo.ifPresent(paxRoomInfo -> {
                roomDetails.getRatePlans().forEach(selectRoomRatePlan -> {
                    List<MultiPaxRatePlanInfo> hermesRatePlans = paxRoomInfo.getRatePlanList().stream()
                            .filter(ratePlan -> ratePlan.getRatePlanCode().startsWith(selectRoomRatePlan.getRpc()))
                            .collect(Collectors.toList());
                    if (hermesRatePlans.size() > 0) {
                        boolean allTariffsMatched = false;
                        for (MultiPaxRatePlanInfo ratePlanInfo: hermesRatePlans) {
                            HermesRatePlanData hermesRatePlanData = new HermesRatePlanData();
                            hermesRatePlanData.setSegmentId(ratePlanInfo.getSegmentId());
                            hermesRatePlanData.setPaymentMode(ratePlanInfo.getPaymentMode());
                            hermesRatePlanData.setOfferCode(ratePlanInfo.getOfferCode());
                            hermesRatePlanData.setPromoCode(ratePlanInfo.getPromoCode());
                            List<PaxWiseInfoResponse> paxWiseInfoResponseList = new ArrayList<>();
                            ratePlanInfo.getPaxWiseInfoList().forEach(hermesPaxWiseInfo -> {
                                PaxWiseInfoResponse paxWiseInfoResponse = new PaxWiseInfoResponse();
                                paxWiseInfoResponse.setHermesMetaData(hermesPaxWiseInfo.getHermesMetaData());
                                paxWiseInfoResponse.setForwardParams(hermesPaxWiseInfo.getForwardParams());
                                paxWiseInfoResponse.setContractType(hermesPaxWiseInfo.getContractType());
                                PriceDetail priceDetail = new PriceDetail();
                                priceDetail.setPax(hermesPaxWiseInfo.getPriceDetail().getPax());
                                paxWiseInfoResponse.setPriceDetail(priceDetail);
                                paxWiseInfoResponseList.add(paxWiseInfoResponse);
                            });
                            hermesRatePlanData.setPaxWiseInfo(paxWiseInfoResponseList);
                            Optional<Tariff> tariff = selectRoomRatePlan.getTariffs().stream()
                                    .filter(tariff1 -> tariff1.getTariffCode().equals(ratePlanInfo.getRatePlanCode()))
                                    .findFirst();
                            if (tariff.isPresent()) {
                                tariff.get().setHermesData(Collections.singletonList(hermesRatePlanData));
                                allTariffsMatched = true;
                            }
                        }
                        if (allTariffsMatched) {
                            commonRatePlan.add(selectRoomRatePlan);
                        }
                    }
                });
                if (commonRatePlan.size() > 0) {
                    roomDetails.setRatePlans(commonRatePlan);
                    commonRooms.add(roomDetails);
                }
            });
        });
        return commonRooms;
    }
}
