package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public abstract class BaseRoomRequestTransformer {

    @Autowired
    private Utility utility;

	public ResponseFilterFlags buildResponseFilterFlags(PriceByHotelsRequestBody priceByHotelsRequestBody, BaseSearchRequest searchRoomsRequest, CommonModifierResponse commonModifierResponse) {

	    FeatureFlags featureFlags = searchRoomsRequest.getFeatureFlags();

        if (featureFlags == null)
            return null;
        ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
        responseFilterFlags.setStaticData(featureFlags.isStaticData());
        responseFilterFlags.setFlyfishSummaryRequired(featureFlags.isReviewSummaryRequired());
        responseFilterFlags.setWalletRequired(featureFlags.isWalletRequired());
        responseFilterFlags.setShortlistRequired(featureFlags.isShortlistingRequired());
        responseFilterFlags.setPriceInfoReq(true);
        responseFilterFlags.setRoomLevelDetails(true);
        responseFilterFlags.setPriceInfoReq(true);
        responseFilterFlags.setBestCoupon(true);
        responseFilterFlags.setUpsellRequested(featureFlags.isShowUpsell());
        responseFilterFlags.setCityTaxExclusive(commonModifierResponse.isCityTaxExclusive());
        responseFilterFlags.setApplyAbsorption(featureFlags.isApplyAbsorption());
        responseFilterFlags.setAddOnRequired(featureFlags.isAddOnRequired());
        responseFilterFlags.setQuickReview(featureFlags.isQuickReview());
        responseFilterFlags.setHidePrice(featureFlags.isHidePrice());
        priceByHotelsRequestBody.setNumberOfAddons(featureFlags.getNoOfAddons());
        priceByHotelsRequestBody.setBestOffersLimit(featureFlags.getBestOffersLimit());
        if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(searchRoomsRequest.getRequestDetails().getIdContext())) {
            responseFilterFlags.setNewCorp(true);
            responseFilterFlags.setPersuasionSeg(null);
            responseFilterFlags.setCityTaxExclusive(null);
        }
        responseFilterFlags.setDayUsePersuasion(featureFlags.isDayUsePersuasion());
        responseFilterFlags.setModifyBooking(featureFlags.isModifyBooking());
        return responseFilterFlags;
    }

    public void buildDeviceDetails(PriceByHotelsRequestBody priceByHotelsRequestBody,
                                    DeviceDetails deviceDetails) {
        priceByHotelsRequestBody.setAppVersion(deviceDetails.getAppVersion());
        priceByHotelsRequestBody.setBookingDevice(deviceDetails.getBookingDevice());
        priceByHotelsRequestBody.setLob(deviceDetails.getBookingDevice());
        priceByHotelsRequestBody.setDeviceId(deviceDetails.getDeviceId());
        priceByHotelsRequestBody.setDeviceType(deviceDetails.getDeviceType());
        priceByHotelsRequestBody.setDeviceName(deviceDetails.getDeviceName());
        priceByHotelsRequestBody.setAppVersionIntGi(StringUtils.isNotEmpty(deviceDetails.getAppVersionIntGi()) ? Integer.valueOf(deviceDetails.getAppVersionIntGi()): null);
    }

    public void populateSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody, SearchCriteria searchCriteria, List<String> hotelIds, CommonModifierResponse commonModifierResponse) {
        priceByHotelsRequestBody.setHotelIds(hotelIds);
        if(StringUtils.isNotEmpty(searchCriteria.getGiHotelId())){
            List<String> giHotelIds = new ArrayList<>();
            giHotelIds.add(searchCriteria.getGiHotelId());
            priceByHotelsRequestBody.setGiHotelIds(giHotelIds);
        }
        priceByHotelsRequestBody.setCheckin(searchCriteria.getCheckIn());
        priceByHotelsRequestBody.setCheckout(searchCriteria.getCheckOut());
        priceByHotelsRequestBody.setCityCode(searchCriteria.getCityCode());
        priceByHotelsRequestBody.setCountryCode(searchCriteria.getCountryCode());
        priceByHotelsRequestBody.setLocationId(searchCriteria.getLocationId());
        priceByHotelsRequestBody.setLocationType(searchCriteria.getLocationType());
        priceByHotelsRequestBody.setCurrency(searchCriteria.getCurrency()!=null ? searchCriteria.getCurrency().toUpperCase() : null);
        priceByHotelsRequestBody.setAuthToken(commonModifierResponse.getMmtAuth());
        priceByHotelsRequestBody.setMcid(commonModifierResponse.getMcId());
        priceByHotelsRequestBody.setPersonalCorpBooking(searchCriteria.isPersonalCorpBooking());
        if(searchCriteria.getUserSearchType()!=null) {
            priceByHotelsRequestBody.setUserSearchType(searchCriteria.getUserSearchType());
        }
        utility.buildSlot(priceByHotelsRequestBody, searchCriteria);
    }
}
