package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.BusinessProcessor.GrpcContentBusinessProcessor;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.MediaDataUrls;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.restexecutors.TaggedMediaExecutor;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.mmt.hotels.clientgateway.constants.Constants.DEVICE_IOS;
import static com.mmt.hotels.clientgateway.constants.Constants.REQUEST_IDENTIFIER;

@RestController
@RequestMapping("/")
public class MediaTagController {

    @Value("${tagged.media.url}")
    private String taggedMediaUrl;

    @Value("${media.by.tag.id.url}")
    private String mediaByTagIdUrl;

    @Autowired
    private TaggedMediaExecutor taggedMediaExecutor;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    GrpcContentBusinessProcessor grpcContentBusinessProcessor;

    @Autowired
    private PropertyManager propManager;

    @Autowired
    private Utility utility;

    private boolean enableHscCall;

    private static final Logger logger = LoggerFactory.getLogger(MediaTagController.class);

    @PostConstruct
    public void init() {
        CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
        enableHscCall = commonConfig.enableHscCall();
    }

    @RequestMapping(value = MediaDataUrls.TAGGED_MEDIA_ENDPOINT, method = RequestMethod.GET)
    public String getTaggedMedia(@RequestParam(name = Constants.CORRELATIONKEY, required = false) String correlationKey,
                                              @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                              @RequestParam(name = Constants.VOYAGER_ID, required = false) String voyagerId,
                                              @RequestParam(name = Constants.MMT_HOTEL_ID, required = false) String mmtHotelID,
                                              @RequestParam(name = Constants.FLAVOUR, required = false) String deviceType,
                                              @RequestParam(name = Constants.VERSION_CODES, required = false, defaultValue = "0") int versionCode,
                                              @RequestParam(name = Constants.STREET_VIEW_REQ, required = false, defaultValue = "false") Boolean streetViewReq,
                                              HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws ClientGatewayException {
        correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
        if (StringUtils.isEmpty(correlationKey)) { correlationKey = UUID.randomUUID().toString(); }
        getHeadersAndCreateMDC(correlationKey, httpRequest);
        requestHandler.validateHotelIdTaggedMedia(mmtHotelID,voyagerId);
        String response = null;
        try {
            if (enableHscCall) {
                //TODO -remove the device IOS check once street view issue is resolved at IOS end.
                response = grpcContentBusinessProcessor.processTaggedMedia(
                        correlationKey, voyagerId, mmtHotelID, "GI",
                        (DEVICE_IOS.equalsIgnoreCase(deviceType) && versionCode < 1530) ? false : streetViewReq
                );
            } else if (StringUtils.isNotEmpty(voyagerId)){
                // TODO - Passing empty headers since Voyager doesn't require them as of now.
                String destinationUrl = String.format(taggedMediaUrl, voyagerId);
                   response = taggedMediaExecutor.executeGetTaggedMediaRequest(new HashMap<>(), destinationUrl);
                if (StringUtils.isEmpty(response) || Constants.NULL_STRING.equalsIgnoreCase(response)) {
                    response = Constants.ERROR_GENERIC_STRING_TAGGED_MEDIA;
                    logger.error("Response Null String received from Voyager :: {}  Correlation Key  {} Destination Url {}", response, correlationKey, destinationUrl);
                }
            } else {
                logger.error("HSC call disabled and voygarerId is missing");
            }
        } catch (Exception e) {
            response = Constants.ERROR_GENERIC_STRING_TAGGED_MEDIA;
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getTaggedMedia");
            else
                metricErrorLogger.logGeneralException(e, "getTaggedMedia", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
        } finally {
            MDC.clear();
        }
        return response;
    }

    private void getHeadersAndCreateMDC(String correlationKey, HttpServletRequest httpRequest) {
        Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
        String region = httpHeaderMap.containsKey(Constants.REGION) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.REGION)) ? httpHeaderMap.get(Constants.REGION) : Constants.DEFAULT_SITE_DOMAIN;
        String language = httpHeaderMap.containsKey(Constants.LANGUAGE) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.LANGUAGE)) ? httpHeaderMap.get(Constants.LANGUAGE) : Constants.DEFAULT_LANGUAGE;
        String currency = httpHeaderMap.containsKey(Constants.CURRENCY) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.CURRENCY)) ? httpHeaderMap.get(Constants.CURRENCY) : Constants.DEFAULT_CUR_INR;
        String device = httpHeaderMap.containsKey("os") && StringUtils.isNotBlank(httpHeaderMap.get(Constants.REGION)) ? httpHeaderMap.get("os") : Constants.CLIENT_DESKTOP;
        MDCHelper.createMDC( device,"", correlationKey,region, language, currency, MediaDataUrls.TAGGED_MEDIA_ENDPOINT,"", StringUtils.EMPTY, null, null);
    }

    @RequestMapping(value = MediaDataUrls.MEDIA_DATA_BY_ID_ENDPOINT, method = RequestMethod.GET)
    public String getMediaDataByID(@RequestParam(name = Constants.CORRELATIONKEY, required = false) String correlationKey,
                                   @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                                    @RequestParam(name = Constants.VOYAGER_ID, required = false) String voyagerId,
                                                    @RequestParam(name = Constants.TAG_IDS, required = true) String tagids,
                                                    @RequestParam(name = Constants.OFFSET, required = true) String offset,
                                                    @RequestParam(name = Constants.LIMIT, required = true) String limit,
                                                    @RequestParam(name = Constants.MMT_HOTEL_ID, required = false) String mmtHotelID,
                                                    HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws ClientGatewayException {
        correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
        if (StringUtils.isEmpty(correlationKey)) { correlationKey = UUID.randomUUID().toString(); }
        getHeadersAndCreateMDC(correlationKey, httpRequest);
        requestHandler.validateHotelIdTaggedMedia(mmtHotelID,voyagerId);
        String response = null;
        try {
            if (enableHscCall) {
                /*$1 is the capturing group*/
                tagids = utility.replaceText("\\[(.*?)\\]", tagids, "$1");
                response = grpcContentBusinessProcessor.processMediaIdDetails(correlationKey, voyagerId, mmtHotelID, tagids, "GI", offset, limit);
            } else if (StringUtils.isNotEmpty(voyagerId)) {
                // TODO - Passing empty headers since Voyager doesn't require them as of now.
                String destinationUrl = String.format(mediaByTagIdUrl, voyagerId, tagids, limit, offset);
                response = taggedMediaExecutor.executeGetMediaByTagIdRequest(new HashMap<>(), destinationUrl);
            } else {
                logger.error("HSC call disabled and voygarerId is missing");
            }
        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getMediaDataByID");
            else
                metricErrorLogger.logGeneralException(e, "getMediaDataByID", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
        } finally {
            MDC.clear();
        }
        return response;
    }
}
