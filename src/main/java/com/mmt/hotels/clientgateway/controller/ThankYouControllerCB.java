package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.MarshallingErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.ThankYouRequest;
import com.mmt.hotels.clientgateway.service.ThankYouService;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.response.base.FailureReason;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.util.Tuple;
import io.swagger.annotations.ApiParam;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@RestController
@RequestMapping("/")
@Deprecated
public class ThankYouControllerCB {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private ThankYouService thankYouService;

    @RequestMapping(value = "entity/api/txnData/mrs/get/{key}", method = RequestMethod.GET)
    public String getTxnDataForMultiRoom(HttpServletRequest httpRequest, HttpServletResponse httpResponse, @PathVariable("key") String key) throws ClientGatewayException {
        String correlationKey;
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpResponse, null, null, "", "txnData/mrs/get",null);
        correlationKey = tup.getX();
        ThankYouRequest thankYouRequest = new ThankYouRequest();
        thankYouRequest.setTxnKey(key);
        thankYouRequest.setCorrelationKey(correlationKey);
        try {
            return thankYouService.getThankYouResponseOld(thankYouRequest, httpRequest.getParameterMap(), tup.getY());
        } catch (Exception e) {
            return ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            MDC.clear();
        }
    }

}
