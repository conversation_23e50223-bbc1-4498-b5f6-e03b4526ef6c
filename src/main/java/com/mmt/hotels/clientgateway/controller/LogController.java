package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.service.LogService;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/logs")
@Profile(Constants.PROFILE_DEV)
public class LogController {
    private final LogService logService;

    public LogController(LogService logService) {
        this.logService = logService;
    }

    @GetMapping("/search")
    public String searchLogs(
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = true) String correlationKey,
            @RequestParam(name = Constants.KEY_QUERY, required = false, defaultValue = Constants.EMPTY_STRING) String query,
            @RequestParam(name = Constants.KEY_API, required = false, defaultValue = Constants.EMPTY_STRING) String api) {
        return logService.getLogs(correlationKey, query, api);
    }
} 