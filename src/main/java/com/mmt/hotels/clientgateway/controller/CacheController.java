package com.mmt.hotels.clientgateway.controller;

import com.github.benmanes.caffeine.cache.stats.CacheStats;

import com.mmt.hotels.clientgateway.response.CacheStatistics;
import com.mmt.hotels.clientgateway.service.PolyglotService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/")
public class CacheController {

    @Autowired
    PolyglotService polyglotService;



    private static final Logger logger = LoggerFactory.getLogger(CacheController.class);



    @RequestMapping(value = "cg/caffiene-stats", method = RequestMethod.GET)
    public Map<String, CacheStatistics> getCacheStats(HttpServletRequest httpRequest, HttpServletResponse httpResponse) {

        Map<String, CacheStatistics> statsMap = new HashMap<>();

        try {

            Map<String, CacheStats> stats = polyglotService.getCacheStats();

            for (Map.Entry<String, CacheStats> entry : stats.entrySet()){
                String key = entry.getKey();
                CacheStats val = entry.getValue();

                CacheStatistics cacheStatistics = new CacheStatistics();
                cacheStatistics.setHitCount(val.hitCount());
                cacheStatistics.setMissCount(val.missCount());
                cacheStatistics.setHitRate(Double.valueOf(val.hitCount())/(Double.valueOf(val.hitCount())+ Double.valueOf(val.missCount())));

                statsMap.put(key, cacheStatistics);
            }


        } catch (Exception e) {

            logger.error("Error occured in caffiene-stats info", e);
        }finally {
            MDC.clear();
        }

        return statsMap;
    }

}
