package com.mmt.hotels.clientgateway.controller.advice;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.ErrorHelper;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.util.MDCHelper.MDCKeys;
import org.springframework.web.context.request.WebRequest;

@org.springframework.web.bind.annotation.ControllerAdvice
public class ControllerAdvice {

	private static final Logger logger = LoggerFactory.getLogger(ControllerAdvice.class);
	private static final Gson gson = new Gson();

	@Autowired
	MetricAspect metricAspect;

	@Autowired
	ErrorHelper errorHelper;
	
	@org.springframework.web.bind.annotation.ExceptionHandler(ClientGatewayException.class)
	public <T> ResponseEntity<ResponseWrapper<T>> handleException(ClientGatewayException exception) {
		ResponseWrapper<T> response = new ResponseWrapper<T>();
		StringBuilder sb = new StringBuilder();
		if (exception.getErrorType()==ErrorType.DOWNSTREAM) {
			sb.append(exception.getCode());
		} else {
			sb.append(Constants.ERR_CODE_API).append(exception.getDependencyLayer().getCode()).append(exception.getErrorType().getCode()).append(exception.getCode());
		}
		// adding if condition to check if it is a corp request and if the error is to be sent with title
		if(errorHelper.sendErrorWithTitle(exception.getCode(), MDC.get(MDCKeys.IDCONTEXT.getStringValue()))){
			String controller = MDC.get(MDCKeys.CONTROLLER.getStringValue());
			Error error = errorHelper.getErrorWithTitle(controller, exception.getCode(), new com.mmt.hotels.clientgateway.response.wrapper.Error(sb.toString(),
					exception.getMessage()), exception.getMetaData());
			response.setError(error);
		} else {
			com.mmt.hotels.clientgateway.response.wrapper.Error error = null != exception.getAlternateMessage() ?
					new com.mmt.hotels.clientgateway.response.wrapper.Error(sb.toString(), exception.getMessage(), exception.getAlternateMessage()) :
					new com.mmt.hotels.clientgateway.response.wrapper.Error(sb.toString(), exception.getMessage());
			response.setError(error);
		}
		String correlationKey = MDC.get(MDCKeys.CORRELATION.getStringValue());
		response.setCorrelationKey(correlationKey);
		metricAspect.addToCounter(exception.getDependencyLayer(),exception.getErrorType(), sb.toString());
		MDC.clear();
		return new ResponseEntity<>(response, exception.getHttpStatusCode());
	}
	
	@org.springframework.web.bind.annotation.ExceptionHandler(MethodArgumentNotValidException.class)
	public <T> ResponseEntity<ResponseWrapper<T>> handleException(MethodArgumentNotValidException exception) {
		ResponseWrapper<T> response = new ResponseWrapper<T>();

		try {
			logger.error("INVALID_REQUEST_EXCEPTION_CAUGHT: Binding Result: {}", gson.toJson(exception.getBindingResult()));
		} catch (Exception ex) {
			logger.error("FAILED_TO_LOG_BINDING_RESULT for reason={}", ex.getMessage());
		}

		com.mmt.hotels.clientgateway.response.wrapper.Error error = fromBindingErrors(
				exception.getBindingResult());
		response.setError(error);
		String correlationKey = MDC.get(MDCKeys.CORRELATION.getStringValue());
		metricAspect.addToCounter(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, error.getCode());
		response.setCorrelationKey(correlationKey);
		MDC.clear();
		return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
	}
	
	public Error fromBindingErrors(BindingResult bindingResult) {
		StringBuilder errorMessageBuilder = new StringBuilder();
		for (ObjectError objectError: bindingResult.getAllErrors()) {
			String[] messageCodes = (String[]) objectError.getCodes();
			errorMessageBuilder.append(messageCodes[0]).append(Constants.SPACE).append(objectError.getDefaultMessage())
			.append(Constants.SEMICOLON);
		}
		StringBuilder sbErrorCodeBuilder = new StringBuilder();
		sbErrorCodeBuilder.append(Constants.ERR_CODE_API).append(DependencyLayer.CLIENTGATEWAY.getCode()).append(ErrorType.VALIDATION.getCode()).append(ValidationErrors.INVALID_REQUEST.getErrorCode());

		String correlationKey = MDC.get(MDCKeys.CORRELATION.getStringValue());
		String client = MDC.get(MDCKeys.CLIENT.getStringValue());
		logger.error("INVALID_REQUEST: Method argument validation failed. Errors: {}, CorrelationKey: {}, Client: {}", errorMessageBuilder.toString(), correlationKey, client);

		Error error = new Error(sbErrorCodeBuilder.toString(), errorMessageBuilder.toString());
		return error;
	}
}
