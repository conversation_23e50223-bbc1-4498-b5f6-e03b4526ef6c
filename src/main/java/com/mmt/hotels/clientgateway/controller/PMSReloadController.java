package com.mmt.hotels.clientgateway.controller;

import org.aeonbits.owner.Reloadable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.propertymanager.pojo.ReloadResponse;

@Controller
public class PMSReloadController {
	
	@Autowired
	private PropertyManager propertyManager;
	
	private static final  Logger LOGGER = LoggerFactory.getLogger(PMSReloadController.class);
	
	@RequestMapping(value="pmsListener",produces="application/json; charset=UTF-8")
	@ResponseBody
	public ReloadResponse reloadProperty(@RequestParam("qualifier")String qualifier) {
		ReloadResponse resp=new ReloadResponse();
		try{
			Reloadable reloadable=propertyManager.getProperty(qualifier,Reloadable.class);
			reloadable.reload();			
		}catch(Exception e){
			resp.setError(e.getMessage());
			LOGGER.error("Error occured while reloading properties from pms: ",e);
		}
		return resp;
	}
}
