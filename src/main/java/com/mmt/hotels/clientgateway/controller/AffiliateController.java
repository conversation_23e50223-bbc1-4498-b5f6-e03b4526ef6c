package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.CreateQuoteRequest;
import com.mmt.hotels.clientgateway.request.GetQuoteRequest;
import com.mmt.hotels.clientgateway.request.UpdateAffiliateFeeRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse;
import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.AffiliateService;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.CreateQuoteRequestBody;
import com.mmt.hotels.model.request.UpdateAffiliateFeeReqBody;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import com.mmt.hotels.util.Tuple;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.CK_CORRELATION_KEY;
import static com.mmt.hotels.clientgateway.constants.Constants.REQUEST_IDENTIFIER;

@RestController
@RequestMapping("/")
public class AffiliateController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private AffiliateService affiliateService;

    @Autowired
	private MetricAspect metricAspect;

    @RequestMapping(value = "cg/update-fee/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<UpdatedAffiliateFeeResponse>> updateAffiliateFee(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody UpdateAffiliateFeeRequest updateAffiliateFeeRequest,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	) throws ClientGatewayException {
    	long startTime = new Date().getTime();
        client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", "cg/update-fee", null);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, updateAffiliateFeeRequest, correlationKey);
        ResponseWrapper<UpdatedAffiliateFeeResponse> updatedAffiliateFeeResponseResponseWrapper = new ResponseWrapper<>();
		updatedAffiliateFeeResponseResponseWrapper.setCorrelationKey(correlationKey);
        updatedAffiliateFeeResponseResponseWrapper.setResponse(affiliateService.getUpdateAffiliateFeeResponse(updateAffiliateFeeRequest, httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/update_fee/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
        return new ResponseEntity(updatedAffiliateFeeResponseResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/create-quote/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<CreateQuoteResponse>> createQuote(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody CreateQuoteRequest createQuoteRequest,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	) throws ClientGatewayException {
		long startTime = new Date().getTime();
        client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", "cg/create-quote", null);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, createQuoteRequest, correlationKey);
        ResponseWrapper<CreateQuoteResponse> createQuoteResponseResponseWrapper = new ResponseWrapper<>();
		createQuoteResponseResponseWrapper.setCorrelationKey(correlationKey);
        createQuoteResponseResponseWrapper.setResponse(affiliateService.createQuote(createQuoteRequest, httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/create_quote/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
        return new ResponseEntity(createQuoteResponseResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/get-quote/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<AvailRoomsResponse>> getQuote(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody GetQuoteRequest getQuoteRequest,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	) throws ClientGatewayException {
		long startTime = new Date().getTime();
        client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", "cg/get-quote", null);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, getQuoteRequest, correlationKey);
        ResponseWrapper<AvailRoomsResponse> getQuoteResponseResponseWrapper = new ResponseWrapper<>();
		getQuoteResponseResponseWrapper.setCorrelationKey(correlationKey);
        getQuoteResponseResponseWrapper.setResponse(affiliateService.getQuote(getQuoteRequest, httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/get_quote/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
        return new ResponseEntity(getQuoteResponseResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "entity/api/hotels/affiliate/updateFee", method = RequestMethod.POST)
	public String updateAffiliateFee(
			HttpServletRequest httpRequest, HttpServletResponse httpResponse,
			@RequestBody UpdateAffiliateFeeReqBody updateAffiliateFeeReqBody,
			@RequestParam(name = "srcClient", required = true) String srcClient,
			@RequestParam(name = "correlationKey", required = true) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	) {
		String response;
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,srcClient, "" , "entity/api/hotels/affiliate/updateFee", null);
			correlationKey = tup.getX();
			response = affiliateService.getUpdateAffiliateFeeResponseOld(updateAffiliateFeeReqBody, httpRequest.getParameterMap(), tup.getY(), correlationKey);
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/affiliate/updateFee",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
			MDC.clear();
		}
		return response;
	}
    
    @RequestMapping(value = "entity/api/hotels/affiliate/createQuote", method = RequestMethod.POST)
    public String createQuote(
			HttpServletRequest httpRequest, HttpServletResponse httpResponse,
			@RequestBody CreateQuoteRequestBody createQuoteRequestBody,
     		@RequestParam(name = "srcClient", required = true) String srcClient,
			@RequestParam(name = "region", required = false) String siteDomain,
			@RequestParam(name = "currency", required = false) String currency,
			@RequestParam(name = "language", required = false) String language,
			@RequestParam(name = "correlationKey", required = true) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	){
    	
    	String response = null;
    	long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,srcClient, "" , "entity/api/hotels/affiliate/updateFee", null);
			correlationKey = tup.getX();
			if(StringUtils.isNotBlank(siteDomain)){
				createQuoteRequestBody.setSiteDomain(siteDomain.toUpperCase());
			}
			response = affiliateService.createQuoteOld(createQuoteRequestBody, httpRequest.getParameterMap(), tup.getY(), correlationKey);
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/affiliate/createQuote",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
			MDC.clear();
		}
		return response;
    }

	@RequestMapping(value = "entity/api/hotels/affiliate/getQuote", method = RequestMethod.POST)
	@ResponseBody
	public Object getQuote(
			HttpServletRequest httpRequest,
			HttpServletResponse httpResponse,
			@RequestBody com.mmt.hotels.model.affiliate.GetQuoteRequest getQuoteRequestBody,
			@RequestParam(name = "srcClient", required = true) String srcClient,
			@RequestParam(name = "correlationKey", required = true) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	) {
		long startTime = new Date().getTime();
		Object response = null;
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpResponse, correlationKey, null, "", "entity/api/hotels/affiliate/updateFee", null);
			correlationKey = tup.getX();
			response = affiliateService.getQuoteOld(getQuoteRequestBody, httpRequest.getParameterMap(), tup.getY(), correlationKey);

		} catch (Exception ex) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/affiliate/getQuote", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
			MDC.clear();
		}
		return response;
	}
}
