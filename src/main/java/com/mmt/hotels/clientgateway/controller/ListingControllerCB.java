package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.LandingService;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/*
 *  No new apis to be integrated here.
 *  This is to support older apps only.
 *  Any new API corresponding to Listing should
 *  be part of ListingController only.
 *
 *  Neither this controller should be used as a template to create new apis
 *  Please refrain from copying from here for a new controller as it will
 *  maintain inconsistency over api frameworks.
 */
@RestController
@RequestMapping("/")
@Deprecated
public class ListingControllerCB {

	@Autowired
	private ListingService listingService;

	@Autowired
	private RequestHandler requestHandler;

	@Autowired
	private LandingService landingService;

	@Autowired
	private OldToNewerRequestTransformer oldToNewerRequestTransformer;

	@Autowired
	private MetricErrorLogger metricErrorLogger;

	@Autowired
	private MetricAspect metricAspect;

	private static final Logger LOGGER = LoggerFactory.getLogger(ListingControllerCB.class);


	@RequestMapping(value = "entity/api/searchHotels", method = RequestMethod.POST)
	public String getSearchResult(
			@RequestBody SearchWrapperInputRequest searchWrapperInputRequest,
			@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
		    @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
		    @RequestParam(name = "srcClient", required = false) String srcClient,
			HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		String response;
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			LOGGER.info("Received getSearchResult request: searchWrapperInputRequest={}, correlationKey={}, requestId={}, seoCorp={}, srcClient={}",
					searchWrapperInputRequest, correlationKey, requestId, seoCorp, srcClient);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,searchWrapperInputRequest.getBookingDevice(), searchWrapperInputRequest.getIdContext() ,  httpRequest.getRequestURI(),null);
			correlationKey = tup.getX();
			Map<String,String> httpHeaderMap = tup.getY();
			httpHeaderMap.put("srcClient",srcClient);
			searchWrapperInputRequest.setCorrelationKey(correlationKey);
			searchWrapperInputRequest.setSeoCorp(seoCorp);
			Map<String, String[]> parameterMap =  new HashMap<>(httpRequest.getParameterMap());
			response = listingService.searchHotelsOld(searchWrapperInputRequest, parameterMap, tup.getY());
		} catch (Exception e) {
			LOGGER.error("Error occured in searchHotelsCB",e);
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/searchHotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}
	
	@RequestMapping(value = "entity/api/searchPersonalizedHotels", method = RequestMethod.POST)
	public String getSearchPersonalizedResult(
			@RequestBody SearchWrapperInputRequest searchWrapperInputRequest,
			@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			@RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			@RequestParam(name = "srcClient", required = false) String srcClient,
			HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws ClientGatewayException{
		String response;
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,searchWrapperInputRequest.getBookingDevice(), searchWrapperInputRequest.getIdContext() , "entity/api/searchPersonalizedHotels",null);
			correlationKey = tup.getX();
			Map<String,String> httpHeaderMap = tup.getY();
			httpHeaderMap.put("srcClient",srcClient);
			searchWrapperInputRequest.setCorrelationKey(correlationKey);
			searchWrapperInputRequest.setSeoCorp(seoCorp);
			response = listingService.searchPersonalizedHotelsOld(searchWrapperInputRequest, httpRequest.getParameterMap(), tup.getY());
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/searchPersonalizedHotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}

	@RequestMapping(value = "cg/searchHotelsIos", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchHotelsResponse>> getListingResult(@RequestBody SearchWrapperInputRequest searchWrapperInputRequest,
																				  @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
																				  @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
																				  @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
																				  @RequestParam(name = "srcClient", required = false) String srcClient,
																				  HttpServletRequest httpRequest, HttpServletResponse httpResponse) {

		ResponseWrapper<SearchHotelsResponse> searchHotelsResponseWrapper = new ResponseWrapper<>();
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,searchWrapperInputRequest.getBookingDevice(), searchWrapperInputRequest.getIdContext() ,  httpRequest.getRequestURI(),null);
			correlationKey = tup.getX();
			Map<String,String> httpHeaderMap = tup.getY();
			httpHeaderMap.put("srcClient",srcClient);
			searchWrapperInputRequest.setCorrelationKey(correlationKey);
			searchWrapperInputRequest.setSeoCorp(seoCorp);
			//response = listingService.searchHotelsOld(searchWrapperInputRequest, httpRequest.getParameterMap(), tup.getY());
			SearchHotelsRequest request = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
			request.getFeatureFlags().setPersuasionsEngineHit(true);
			request.setClient("IOS");
			request.getSearchCriteria().setPersonalizedSearch(true);
			searchHotelsResponseWrapper.setResponse(listingService.searchHotels(request, httpRequest.getParameterMap(), tup.getY()));
			searchHotelsResponseWrapper.setCorrelationKey(correlationKey);

		} catch (Exception e) {
			LOGGER.error("Error occurred in searchHotelsCB",e);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/searchHotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
			MDC.clear();
		}
		return new ResponseEntity<>(searchHotelsResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "entity/api/hotels/filterCount", method = RequestMethod.POST)
	public String getFilterCount(@RequestBody SearchWrapperInputRequest searchRequestWrapper,
								  @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = StringUtils.EMPTY) String correlationKey,
								  @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
								  @RequestParam(name = Constants.REGION, required = false) String siteDomain,
								  @RequestParam(name = Constants.CURRENCY, required = false) String currency,
								  @RequestParam(name = Constants.LANGUAGE, required = false) String language,
								  @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
								  HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		String response;
		long startTime = new Date().getTime();
		try {
			if(seoCorp){
				searchRequestWrapper.setIdContext(Constants.CORP_ID_CONTEXT);
				searchRequestWrapper.setSeoCorp(true);
			}
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,searchRequestWrapper.getBookingDevice(), searchRequestWrapper.getIdContext() , "entity/api/hotels/filterCount",null);
			correlationKey = tup.getX();
			searchRequestWrapper.setCorrelationKey(correlationKey);
			if(StringUtils.isNotBlank(siteDomain)){
				searchRequestWrapper.setSiteDomain(siteDomain.toUpperCase());
			}
			response = listingService.filterCountOld(searchRequestWrapper, httpRequest.getParameterMap(), tup.getY());
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/filterCount",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), searchRequestWrapper.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}

	@RequestMapping(value = "entity/api/listingMap", method = RequestMethod.POST)
	public String getSearchResultListingMap(
			@RequestBody SearchWrapperInputRequest searchWrapperInputRequest,
			@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws ClientGatewayException{
		String response;
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,searchWrapperInputRequest.getBookingDevice(), searchWrapperInputRequest.getIdContext() , "entity/api/listingMap",null);
			correlationKey = tup.getX();
			searchWrapperInputRequest.setCorrelationKey(correlationKey);
			response = listingService.listingMapOld(searchWrapperInputRequest, httpRequest.getParameterMap(), tup.getY());
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/listingMap",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), searchWrapperInputRequest.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}

	@RequestMapping(value = "entity/api/fetchCollections", method = RequestMethod.POST)
	public String getFetchCollection(@RequestBody SearchWrapperInputRequest searchWrapperInputRequest,
									 @RequestParam(name = Constants.REGION, required = false) String siteDomain,
									 @RequestParam(name = Constants.CURRENCY, required = false) String currency,
									 @RequestParam(name = Constants.LANGUAGE, required = false) String language,
									 @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
									 @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
									 HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws ClientGatewayException {
		String response;
		long startTime = new Date().getTime();
		try {
			if(StringUtils.isNotBlank(searchWrapperInputRequest.getCorrelationKey()))
				correlationKey = searchWrapperInputRequest.getCorrelationKey();
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,searchWrapperInputRequest.getBookingDevice() , searchWrapperInputRequest.getIdContext(), "entity/api/fetchCollections",null);
			correlationKey = tup.getX();
			searchWrapperInputRequest.setCorrelationKey(correlationKey);
			if (StringUtils.isNotBlank(siteDomain)) {
				searchWrapperInputRequest.setSiteDomain(siteDomain.toUpperCase());
			}
			response = listingService.fetchCollectionsOld(searchWrapperInputRequest, httpRequest.getParameterMap(),tup.getY());
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/fetchCollections",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), searchWrapperInputRequest.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}

		return response;
	}

	@RequestMapping(value = "entity/api/hotel/getGiMobConfig", method = RequestMethod.GET)
	public String getHotelMobConfig(HttpServletRequest httpServletRequest,
													HttpServletResponse httpServletResponse) {

		String mobConfigResponse;
		long startTime = new Date().getTime();
		try {
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,null,null,"","entity/api/hotel/getMobConfig",null);
			mobConfigResponse = listingService.getMobConfig();
		}catch (Exception e) {
			mobConfigResponse = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		}finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotel/getMobConfig",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), "", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return mobConfigResponse;
	}

	@RequestMapping(value = "entity/api/hotel/getPlaceLatLng", method = RequestMethod.GET)
	public String getCityDataFromLatLngOrPlaceID(HttpServletRequest httpRequest,
												 HttpServletResponse httpResponse,
												 @RequestParam(value = "id", required = false) String placeId,
												 @RequestParam(value = "lat", required = false) Double lat,
												 @RequestParam(value = "lng", required = false) Double lng,
												 @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
												 @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) {

		String serverResponse = null;
		long startTime = new Date().getTime();

		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,null,"","entity/api/hotel/getPlaceLatLng",null);
			correlationKey = tup.getX();

			boolean placeIdCase = placeId != null && !placeId.isEmpty();
			boolean latLngCase = lat != null && lng != null && Double.compare(lat, 0.0d) != 0
					&& Double.compare(lng, 0.0d) != 0;

			if (!placeIdCase && !latLngCase) {
				LOGGER.error("Missing params in getCityDataFromLatLngOrPlaceID");
				MetricError metricError = new MetricError(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION
						, "", "Missing params in request");
				metricErrorLogger.logErrorInMetric(metricError , MDC.getCopyOfContextMap());
				return  ClientBackendUtility.setCBErrorResponse(CBError.BAD_REQUEST);
			}

			serverResponse = landingService.getLatLngFromGooglePlaceId(placeId, lat, lng);

		} catch (Exception e) {
			serverResponse = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		}finally{
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotel/getPlaceLatLng",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), "", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}

		return serverResponse;
	}

	@RequestMapping(value = "entity/api/city/{cityId}/hotels/metadata", method = RequestMethod.GET)
	@ResponseBody
	public String getHotelsMetadataByCity(
			HttpServletRequest httpRequest, HttpServletResponse httpResponse,
			@PathVariable("cityId") String cityId,
			@RequestParam(name = "filterCode", required = false) String filterCode,
			@RequestParam(name = "priceBucketCriteria", required = false, defaultValue = "") String priceBucketCriteria,
			@RequestParam(name = "filterCriteria", required = false, defaultValue = "") String filterCriteria,
			@RequestParam(name = "lng", required = false) Double longitude,
			@RequestParam(name = "lat", required = false) Double latitude,
			@RequestParam(name = "countryCode", required = false) String countryCode,
			@RequestParam(name = "region", required = false) String siteDomain,
			@RequestParam(name = "currency", required = false) String currency,
			@RequestParam(name = "language", required = false) String language,
			@RequestParam(name = "correlationKey", required = false, defaultValue = "") String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			@RequestParam(name = "srCon", required = false) String srCon,
			@RequestParam(name = "srcClient", required = false) String srcClient,
			@RequestParam(name = "locationId", required = false) String locationId,
			@RequestParam(name = "locationType", required = false) String locationType
	){
		String serverResponse = null;
		long startTime = new Date().getTime();

		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,srcClient,"","entity/api/city/{cityId}/hotels/metadata",null);
			correlationKey = tup.getX();
			Map<String, String> requestParams = Utility.getRequestParam(httpRequest.getParameterMap());
			serverResponse = listingService.getMetaDataByCityResponse(cityId, locationId, locationType, filterCode,
					correlationKey,requestParams);

		} catch (Exception e) {
			serverResponse = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		}finally{
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/city/{cityId}/hotels/metadata",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return serverResponse;

	}



	@RequestMapping(value = "entity/api/hotel/listPersonalization", method = RequestMethod.POST)
	public String getListingPersonalizedCardData(
			@RequestBody HotelLandingMobRequestBody listPersonalizationRequest, HttpServletRequest httpRequest, HttpServletResponse httpResponse,
			@RequestParam(name = "srcClient", required = true) String srcClient,
			@RequestParam(name = Constants.REGION, required = false) String siteDomain,
			@RequestParam(name = Constants.CURRENCY, required = false) String currency,
			@RequestParam(name = Constants.LANGUAGE, required = false) String language,
			@RequestParam(name = "countryCode", required = true) String countryCode,
			@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) {

		String response;
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,
					correlationKey,listPersonalizationRequest.getHotelSearchRequest().getBookingDevice(),
					listPersonalizationRequest.getHotelSearchRequest().getIdContext() , "entity/api/listPersonalization",null);
			correlationKey = tup.getX();
			listPersonalizationRequest.getHotelSearchRequest().setCorrelationKey(correlationKey);
			listPersonalizationRequest.setCorrelationKey(correlationKey);
			response = listingService.listingPersonalizationOld(listPersonalizationRequest, httpRequest.getParameterMap(), tup.getY());
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotel/listPersonalization",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}

	@RequestMapping(value = "entity/api/nearByApi", method = RequestMethod.POST)
	public String getNearBySearchResult(@RequestBody SearchWrapperInputRequest searchRequestWrapper,
										@RequestParam(name = "srcClient", required = false) String srcClient,
										@RequestParam(name = Constants.REGION, required = false) String siteDomain,
										@RequestParam(name = Constants.CURRENCY, required = false) String currency,
										@RequestParam(name = Constants.LANGUAGE, required = false) String language,
										@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
										@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
										HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		String response;
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpResponse,
					correlationKey, searchRequestWrapper.getBookingDevice(),
					searchRequestWrapper.getIdContext(), "entity/api/nearByApi",null);
			correlationKey = tup.getX();
			searchRequestWrapper.setCorrelationKey(correlationKey);
			response = listingService.nearByOld(searchRequestWrapper, httpRequest.getParameterMap(), tup.getY());
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/nearByApi",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}

	@RequestMapping(value = "entity/api/hotel/mobLanding", method = RequestMethod.POST)
	public String getMobLanding(@RequestBody HotelLandingMobRequestBody requestBody,
								@RequestParam(name = Constants.REGION, required = false) String siteDomain,
								@RequestParam(name = Constants.CURRENCY, required = false) String currency,
								@RequestParam(name = Constants.LANGUAGE, required = false) String language,
								@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
								@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
								HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		String response;
		long startTime = new Date().getTime();
		try {
			requestBody.setSiteDomain(siteDomain);
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpResponse,
					correlationKey, requestBody.getHotelSearchRequest().getBookingDevice(),
					requestBody.getHotelSearchRequest().getIdContext(), "entity/api/hotel/moblanding",null);
			correlationKey = tup.getX();
			requestBody.setCorrelationKey(correlationKey);
			response = listingService.mobLandingOld(requestBody, httpRequest.getParameterMap(), tup.getY());
		} catch (Exception e) {
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotel/mobLanding",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), requestBody.getSrcClient(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}

	@RequestMapping(value = "entity/api/landingDiscovery", method = RequestMethod.POST)
	public String getLandingDiscoveryResult(
			@RequestBody SearchWrapperInputRequest searchWrapperInputRequest,
			@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			@RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			@RequestParam(name = "srcClient", required = false) String srcClient,
			HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		String response;
		long startTime = new Date().getTime();
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,searchWrapperInputRequest.getBookingDevice(), searchWrapperInputRequest.getIdContext() ,  httpRequest.getRequestURI(),null);
			correlationKey = tup.getX();
			Map<String,String> httpHeaderMap = tup.getY();
			httpHeaderMap.put("srcClient",srcClient);
			searchWrapperInputRequest.setCorrelationKey(correlationKey);
			searchWrapperInputRequest.setSeoCorp(seoCorp);
			response = listingService.landingDiscoveryOld(searchWrapperInputRequest, httpRequest.getParameterMap(), tup.getY());
		} catch (Exception e) {
			LOGGER.error("Error occured in searchHotelsCB",e);
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/landingDiscovery",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

			MDC.clear();
		}
		return response;
	}
}
