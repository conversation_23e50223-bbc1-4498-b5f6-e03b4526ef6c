package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.ByPassUrls;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.restexecutors.ByPassExecutor;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.collections.MapUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.Map;

@SuppressWarnings("deprecation")
@RestController
@RequestMapping("/")
public class ByPassController {
	
	@Autowired
	private ByPassExecutor byPassExecutor;
	
	@Autowired
    private MetricErrorLogger metricErrorLogger;

	@Autowired
	private RequestHandler requestHandler;
	
	@SuppressWarnings({ })
	@RequestMapping(value = {ByPassUrls.SOURCE_OTP_GENERATE_URL,
							 ByPassUrls.SOURCE_OTP_VALIDATE_URL,
							 ByPassUrls.SOURCE_GET_FLYFISH_UPVOTE_DOWNVOTE_URL,
							 ByPassUrls.SOURCE_GET_TOTAL_PRICING_URL,
								ByPassUrls.SOURCE_VALIDATE_COUPON_URL,
							ByPassUrls.SOURCE_EMI_DETAILS}, method = RequestMethod.POST)
	public String postByPass(
			@RequestBody String request,
			@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		String response = null;
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,null,"",httpRequest.getRequestURI(), null);
			correlationKey= tup.getX();
			String destinationUrl = getDestinationUrl(httpRequest.getRequestURI(), httpRequest.getParameterMap(), correlationKey,null);
			response = byPassExecutor.executeByPassRequest(request, tup.getY(), destinationUrl, correlationKey);
		} catch (Exception e) {
			if (e instanceof ClientGatewayException)
    			metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "postByPass");
    		else
    			metricErrorLogger.logGeneralException(e, "postByPass", DependencyLayer.CLIENTGATEWAY,
    					ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			MDC.clear();
		}
		return response;
	}
	
	@SuppressWarnings({ })
	@RequestMapping(value = {ByPassUrls.SOURCE_OTP_GENERATE_URL,
							 ByPassUrls.SOURCE_OTP_VALIDATE_URL,
							ByPassUrls.SOURCE_STATIC_POLICIES,
							ByPassUrls.SOURCE_GET_PMS_CONFIG}, method = RequestMethod.GET)
	public String getByPass(
			@RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		String response = null;
		try {
			correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,null,"", httpRequest.getRequestURI(),null);
			correlationKey = tup.getX();
			Map<String,String> pathMap = (LinkedHashMap<String,String>)httpRequest.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
			String url = httpRequest.getRequestURI();
			if(MapUtils.isNotEmpty(pathMap))
				url =(String) httpRequest.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
			String destinationUrl = getDestinationUrl(url, httpRequest.getParameterMap(), correlationKey,pathMap);
			response = byPassExecutor.executeGetByPassRequest(tup.getY(), destinationUrl, correlationKey);
		} catch (Exception e) {
			if (e instanceof ClientGatewayException)
    			metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getByPass");
    		else
    			metricErrorLogger.logGeneralException(e, "getByPass", DependencyLayer.CLIENTGATEWAY, 
    					ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		} finally {
			MDC.clear();
		}
		return response;
	}

	public String getDestinationUrl(String requestURI, Map<String, String[]> parameterMap, String correlationKey,Map<String,String> pathMap) throws UnsupportedEncodingException {
		String destinationUrl = null;
		requestURI = requestURI.replaceAll(Constants.CONTEXT_PATH, "");
		switch(requestURI) {
			case ByPassUrls.SOURCE_OTP_GENERATE_URL: destinationUrl = ByPassUrls.DESTINATION_OTP_GENERATE_URL;
													 break;
			case ByPassUrls.SOURCE_OTP_VALIDATE_URL: destinationUrl = ByPassUrls.DESTINATION_OTP_VALIDATE_URL;
													 break;
			case ByPassUrls.SOURCE_GET_TOTAL_PRICING_URL: destinationUrl = ByPassUrls.DESTINATION_GET_TOTAL_PRICING_URL;
			 										 break;	
			case ByPassUrls.SOURCE_GET_FLYFISH_UPVOTE_DOWNVOTE_URL: destinationUrl = ByPassUrls.DESTINATION_FLYFISH_REVIEWS_UPVOTE_DOWNVOTE_API_URL;
			 										 break;
			case ByPassUrls.SOURCE_VALIDATE_COUPON_URL: destinationUrl = ByPassUrls.DESTINATION_VALIDATE_COUPON_URL;
				break;
			case ByPassUrls.SOURCE_EMI_DETAILS:
				destinationUrl = ByPassUrls.DESTINATION_EMI_DETAILS_URL;
				break;
			case ByPassUrls.SOURCE_STATIC_POLICIES: destinationUrl = ByPassUrls.DESTINATION_STATICPOLICY_URL;
				break;
			case ByPassUrls.SOURCE_GET_PMS_CONFIG:
				destinationUrl = ByPassUrls.DESTINATION_GET_PMS_CONFIG;
				break;
		}

		if(MapUtils.isNotEmpty(pathMap)){
			for(String key : pathMap.keySet()) {
				destinationUrl = destinationUrl.replace("{" + key + "}" , pathMap.get(key));
			}
		}
		StringBuilder sb = new StringBuilder(destinationUrl);
		sb.append("?");
		sb.append(Constants.CORRELATIONKEY);
		sb.append("=");
		sb.append(correlationKey);
		if (null != parameterMap) {
			for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
				sb.append("&");
				sb.append(entry.getKey());
				sb.append("=");
				sb.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
			}	
		}
		updateAdditionalParamInUrl(requestURI, sb);
		return sb.toString();
	}
	
	private void updateAdditionalParamInUrl(String requestURI, StringBuilder destinationUrl) {
		if(ByPassUrls.SOURCE_GET_FLYFISH_UPVOTE_DOWNVOTE_URL.equalsIgnoreCase(requestURI)) {
			destinationUrl.append("&");
			destinationUrl.append(Constants.CLIENT);
			destinationUrl.append("=");
			destinationUrl.append(Constants.SRC_CLIENTBACKEND);
		}
	}
}
