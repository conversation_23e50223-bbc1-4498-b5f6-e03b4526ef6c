package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.ThankYouRequest;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.ThankYouService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/")
public class ThankyouController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private ThankYouService thankYouService;

    @Autowired
    private MetricAspect metricAspect;

    @RequestMapping(value = "cg/thank-you/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<ThankYouResponse>> thankYouGet(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody ThankYouRequest thankYouRequest,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"","cg/thankyou",null);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, thankYouRequest, correlationKey);
        ResponseWrapper<ThankYouResponse> thankYouResponseResponseWrapper = new ResponseWrapper<>();
        thankYouResponseResponseWrapper.setResponse(thankYouService.getThankYouResponse(thankYouRequest, httpServletRequest.getParameterMap(), tup.getY()));
        thankYouResponseResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/thank_you/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(thankYouResponseResponseWrapper, HttpStatus.OK);
    }
}
