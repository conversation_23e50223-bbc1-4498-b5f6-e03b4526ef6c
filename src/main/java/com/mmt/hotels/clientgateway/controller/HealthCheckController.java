package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.configuration.PropertyManagerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
public class HealthCheckController {

    private static final Logger logger = LoggerFactory.getLogger(PropertyManagerConfig.class);

    @RequestMapping(value = "/", method = RequestMethod.GET)
    @ResponseBody
    public String getTest() {

        return "Server is running..";
    }

    @RequestMapping(value = "/cg/healthcheck", method = RequestMethod.GET)
    @ResponseBody
    public String monitorHealth() {
        return "{\"Status\": \"UP\"}";
    }

    @RequestMapping(value = "/health", method = RequestMethod.GET)
    @ResponseBody
    public String monitorHealthCB() {
        return "{\"Status\": \"UP\"}";
    }
}
