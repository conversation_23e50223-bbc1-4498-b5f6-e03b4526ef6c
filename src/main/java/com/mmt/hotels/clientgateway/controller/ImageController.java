package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaV2;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.pojo.request.image.HotelImageRequest;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.Date;
import com.mmt.hotels.clientgateway.service.ImageService;
@RestController
@RequestMapping("/")
public class ImageController {
    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private ImageService imageService ;

    @RequestMapping(value = "cg/traveller-image/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<MediaV2>> getTravellerImage(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody HotelImageRequest hotelImageRequest,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
             {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        ResponseWrapper<MediaV2> mediaV2ResponseWrapper = new ResponseWrapper<>();
        mediaV2ResponseWrapper.setCorrelationKey(ck);
        mediaV2ResponseWrapper.setResponse(imageService.travellerImageResponse(hotelImageRequest, client));
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/static_detail", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        return new ResponseEntity<>(mediaV2ResponseWrapper, HttpStatus.OK);
    }
}
