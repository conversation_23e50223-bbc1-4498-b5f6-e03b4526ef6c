package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.DetailService;
import com.mmt.hotels.clientgateway.service.EmiService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.util.Tuple;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.*;

@RestController
@RequestMapping("/")
public class DetailController {

	@Autowired
	private RequestHandler requestHandler;

	@Autowired
	private DetailService detailService;

	@Autowired
	private EmiService emiService;

	@Autowired
	private MetricAspect metricAspect;

	@RequestMapping(value = "cg/search-rooms/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchRoomsResponse>> searchRooms(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody SearchRoomsRequest searchRoomsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException {
		long startTime  = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, searchRoomsRequest.getRequestDetails().getIdContext(), DETAIL_SEARCH_ROOMS, searchRoomsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, searchRoomsRequest, correlationKey);
		requestHandler.validateHotelIdSearchRoom(searchRoomsRequest);
		ResponseWrapper<SearchRoomsResponse> searchRoomsResponseWrapper = new ResponseWrapper<>();
		searchRoomsResponseWrapper.setCorrelationKey(correlationKey);
		searchRoomsResponseWrapper.setResponse(detailService.searchRooms(searchRoomsRequest, httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/search_rooms",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(searchRoomsResponseWrapper, HttpStatus.OK);
	}
	
	@RequestMapping(value = "cg/static-detail/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<StaticDetailResponse>> staticDetail(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody StaticDetailRequest staticDetailRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
					throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,  staticDetailRequest.getRequestDetails().getIdContext() ,DETAIL_STATIC_DETAIL, staticDetailRequest );
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, staticDetailRequest, correlationKey);
		requestHandler.validateHotelId(staticDetailRequest);
		ResponseWrapper<StaticDetailResponse> staticDetailResponseWrapper = new ResponseWrapper<>();
		staticDetailResponseWrapper.setCorrelationKey(correlationKey);
		staticDetailResponseWrapper.setResponse(detailService.staticDetail(staticDetailRequest, httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/static_detail",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(staticDetailResponseWrapper, HttpStatus.OK);
	}
	
	@RequestMapping(value = "cg/update-price/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<UpdatePriceResponse>> updatePrice(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody UpdatePriceRequest updatePriceRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
					throws ClientGatewayException{
		long startTime = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, updatePriceRequest.getRequestDetails().getIdContext(), DETAIL_UPDATE_PRICE, updatePriceRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, updatePriceRequest, correlationKey);
		ResponseWrapper<UpdatePriceResponse> updatePriceResponseWrapper = new ResponseWrapper<>();
		updatePriceResponseWrapper.setCorrelationKey(correlationKey);
		updatePriceResponseWrapper.setResponse(detailService.updatePrice(updatePriceRequest,httpServletRequest.getParameterMap(),tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/update_price",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(updatePriceResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/updated-emi-details/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchRoomsResponse>> getUpdatedEmi(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody UpdatedEmiRequest updateEmiDetailRequest,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			HttpServletRequest httpRequest,
			HttpServletResponse httpServletResponse) throws ClientGatewayException {
		long startTime = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpServletResponse, ck, client, updateEmiDetailRequest.getRequestDetails().getIdContext(), DETAIL_UPDATED_EMI_DETAILS, updateEmiDetailRequest);
		ck = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpRequest, client, updateEmiDetailRequest, ck);
		ResponseWrapper<SearchRoomsResponse> searchRoomsResponseWrapper = new ResponseWrapper<>();
		searchRoomsResponseWrapper.setCorrelationKey(ck);
		searchRoomsResponseWrapper.setResponse(emiService.updatedEmiDetails(updateEmiDetailRequest, httpRequest.getParameterMap(), ck, tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/updated_emi_details",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(searchRoomsResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/city-guide/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<CityGuideResponse>> getCityGuide(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody CityGuideRequest cityGuideRequest,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			HttpServletRequest httpRequest,
			HttpServletResponse httpServletResponse) throws ClientGatewayException {
		long startTime = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpServletResponse, ck, client, null, DETAIL_UPDATED_EMI_DETAILS);
		ck = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpRequest, client, cityGuideRequest, ck);
		ResponseWrapper<CityGuideResponse> CityGuideResponseWrapper = new ResponseWrapper<>();
		CityGuideResponseWrapper.setCorrelationKey(ck);
		CityGuideResponseWrapper.setResponse(detailService.cityGuideData(cityGuideRequest, httpRequest.getParameterMap(), tup.getY(), ck));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/city_guide",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(CityGuideResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/wishListed-static-detail/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<WishListedHotelsDetailResponse>> wishListedStaticDetail(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody WishListedHotelsDetailRequest wishListedHotelsDetailRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck, client,
				wishListedHotelsDetailRequest.getRequestDetails().getIdContext(), WISHLISTED_STATIC_DETAILS, wishListedHotelsDetailRequest );
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, wishListedHotelsDetailRequest, correlationKey);
		ResponseWrapper<WishListedHotelsDetailResponse> wishListedHotelsDetailResponseWrapper = new ResponseWrapper<>();
		wishListedHotelsDetailResponseWrapper.setCorrelationKey(correlationKey);
		wishListedHotelsDetailResponseWrapper.setResponse(detailService.wishListedStaticDetail(wishListedHotelsDetailRequest, httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/wishListed-static-detail",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()),
				MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(wishListedHotelsDetailResponseWrapper, HttpStatus.OK);
	}

	/** Search-Slot API for Day Use funnel.
	 * */
	@RequestMapping(value = "cg/search-slots/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<DayUseRoomsResponse>> searchSlots(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody DayUseRoomsRequest dayUseRoomsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
		long startTime  = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, dayUseRoomsRequest.getRequestDetails().getIdContext(), DETAIL_SEARCH_SLOTS, dayUseRoomsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, dayUseRoomsRequest, correlationKey);
		ResponseWrapper<DayUseRoomsResponse> dayUseRoomsResponseResponseWrapper = new ResponseWrapper<>();
		dayUseRoomsResponseResponseWrapper.setCorrelationKey(correlationKey);
		dayUseRoomsResponseResponseWrapper.setResponse(detailService.searchSlots(dayUseRoomsRequest, httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/search_slots",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()),
				MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(dayUseRoomsResponseResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/calendar-availability/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<CalendarAvailabilityResponse>> calendarAvailability(
			@Valid @RequestBody CalendarAvailabilityRequest calendarAvailabilityRequest,
			@PathVariable("client") String client,
			HttpServletRequest httpServletRequest,
			HttpServletResponse httpServletResponse,
			@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
		long startTime = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", CALENDAR_AVAILABILITY);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, calendarAvailabilityRequest, correlationKey);
		ResponseWrapper<CalendarAvailabilityResponse> responseWrapper = new ResponseWrapper<>();
		responseWrapper.setCorrelationKey(correlationKey);
		responseWrapper.setResponse(detailService.fetchCalendarAvailability(calendarAvailabilityRequest, tup.getX(), httpServletRequest.getParameterMap(), tup.getY()));
		metricAspect.addToTime(
				new Date().getTime() - startTime,
				CALENDAR_AVAILABILITY,
				MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()),
				MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()),
				MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()),
				MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue())
		);
		MDC.clear();
		return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
	}

}
