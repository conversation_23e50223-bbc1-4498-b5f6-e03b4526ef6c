package com.mmt.hotels.clientgateway.controller;
import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.response.ugc.ClientUgcResponse;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.model.response.flyfish.*;
import com.mmt.hotels.clientgateway.service.*;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.util.Tuple;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import org.springframework.web.multipart.MultipartRequest;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.DEFAULT_SITE_DOMAIN;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.*;

@RestController
@RequestMapping("/")
public class ReviewController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private ReviewService reviewService;

    @Autowired
    private AddonsService addonsService;

    @Autowired
    private DiscountService discountService;

    @Autowired
    private PolicyService policyService;

    @Autowired
    private RoomInfoService roomInfoService;

    @Autowired
    private MetricAspect metricAspect;

    @RequestMapping(value = "cg/avail-rooms/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<AvailRoomsResponse>> availRooms(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody AvailRoomsRequest availRoomsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client, availRoomsRequest.getRequestDetails().getIdContext() , REVIEW_AVAIL_ROOMS, availRoomsRequest);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, availRoomsRequest, correlationKey);
        requestHandler.validateAvailRequest(availRoomsRequest);
        availRoomsRequest.getDeviceDetails().setAppVersionIntGi(httpServletRequest.getHeader("APPVERSION-GOIBIBO"));
        ResponseWrapper<AvailRoomsResponse> availRoomsRoomsResponseWrapper = new ResponseWrapper<>();
        availRoomsRoomsResponseWrapper.setResponse(reviewService.availRooms(availRoomsRequest, httpServletRequest.getParameterMap(), tup.getY()));
        availRoomsRoomsResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/avail_rooms",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(availRoomsRoomsResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/validate-coupon/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<ValidateCouponResponseBody>> validateCoupon(
            @Valid @RequestBody ValidateCouponRequest validateCouponRequest,
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", REVIEW_VAILDATE_COUPON, null);
        String correlationKey = tup.getX();
        ResponseWrapper<ValidateCouponResponseBody> validateCouponResponseWrapper = new ResponseWrapper<>();

        validateCouponResponseWrapper.setResponse(discountService.validateCoupon(validateCouponRequest, httpServletRequest.getParameterMap(), tup.getY(), client));
        validateCouponResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/validate_coupon/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(validateCouponResponseWrapper, HttpStatus.OK);
    }


    @RequestMapping(value = "cg/search-addons/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<SearchAddonsResponse>> searchAddons(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody SearchAddonsRequest searchAddonsRequest,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchAddonsRequest.getRequestDetails().getIdContext(),REVIEW_SEARCH_ADD_ONS,searchAddonsRequest);
        ResponseWrapper<SearchAddonsResponse> searchAddonsResponseWrapper = new ResponseWrapper<>();
        searchAddonsResponseWrapper.setResponse(addonsService.getAddons(searchAddonsRequest, httpServletRequest.getParameterMap(), tup.getY()));
        searchAddonsResponseWrapper.setCorrelationKey(tup.getX());
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/search_addons/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(searchAddonsResponseWrapper, HttpStatus.OK);
    }
    
    @RequestMapping(value = "cg/total-pricing/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<TotalPriceResponse>> getTotalPrice(
            @Valid @RequestBody TotalPricingRequest getTotalPricingRequest,
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", REVIEW_TOTAL_PRICING,null);
        String correlationKey =  tup.getX();
		ResponseWrapper<TotalPriceResponse> totalPriceResponseWrapper = new ResponseWrapper<>();
        totalPriceResponseWrapper.setCorrelationKey(correlationKey);
        totalPriceResponseWrapper.setResponse(reviewService.getTotalPrice(getTotalPricingRequest, httpServletRequest.getParameterMap(), correlationKey, tup.getY(), client));
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/total_pricing/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(totalPriceResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/get-policies/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<PoliciesResponse> getPolicies(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody PoliciesRequest policiesRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"",REVIEW_GET_POLICIES,null);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, policiesRequest, correlationKey);

        PoliciesResponse policiesResponse = policyService.getPolicies(policiesRequest, httpServletRequest.getParameterMap());
        policiesResponse.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/get_policies/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(policiesResponse, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/room-info/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<RoomInfoResponse> getRoomInfo(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody RoomInfoRequest roomInfoRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"",REVIEW_ROOM_INFO,null);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, roomInfoRequest, correlationKey);
        RoomInfoResponse roomInfoResponse = roomInfoService.getRoomInfo(roomInfoRequest,httpServletRequest.getParameterMap());
        roomInfoResponse.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/room_info/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(roomInfoResponse, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/room-infos/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<RoomInfoResponse>> getRoomInfos(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody RoomInfoRequest roomInfoRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", REVIEW_ROOM_INFOS,null);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, roomInfoRequest, correlationKey);
        ResponseWrapper<RoomInfoResponse> roomInfoResponseWrapper = new ResponseWrapper<>();
        roomInfoResponseWrapper.setResponse(roomInfoService.getRoomInfos(roomInfoRequest,httpServletRequest.getParameterMap()));
        roomInfoResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/room_infos/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(roomInfoResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/pay-later-eligibility/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<PayLaterEligibilityResponse>> checkPayLaterEligibility(
            @Valid @RequestBody PayLaterEligibilityRequest request,
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", PAY_LATER_ELIGIBILITY, null);
        String correlationKey = tup.getX();
        ResponseWrapper<PayLaterEligibilityResponse> responseWrapper = new ResponseWrapper<>();

        responseWrapper.setResponse(reviewService.fetchPayLaterEligibility(request, correlationKey,httpServletRequest.getParameterMap(), tup.getY(), client));
        responseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, PAY_LATER_ELIGIBILITY,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/ugc-reviews/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<UgcReviewResponseData> getUgcReviewsGI(
            @Valid @RequestBody UgcReviewRequest ugcReviewRequest, HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse, @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false,
            defaultValue = Constants.EMPTY_STRING) String ck,
            @PathVariable(VERSION) String version, @PathVariable(CLIENT) String client,
            @RequestParam(name = DES_CON, defaultValue = DEFAULT_SITE_DOMAIN) String countryCode,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", UGC_REVIEWS, null);
        UgcReviewResponseData ugcReviewResponseData = reviewService.getUgcReviewsFromHes(ugcReviewRequest, ck);
        metricAspect.addToTime(new Date().getTime() - startTime, UGC_REVIEWS,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        return new ResponseEntity<>(ugcReviewResponseData, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/ugc-summary/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<UGCPlatformReviewSummaryDTO> getUgcSummaryGI(
            @Valid @RequestBody UgcSummaryRequest ugcSummaryRequest, HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse, @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false,
            defaultValue = Constants.EMPTY_STRING) String ck,
            @PathVariable(VERSION) String version, @PathVariable(CLIENT) String client,
            @RequestParam(name = DES_CON, defaultValue = DEFAULT_SITE_DOMAIN) String countryCode,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", UGC_SUMMARY, null);
        UGCPlatformReviewSummaryDTO ugcPlatformReviewSummaryDTO = reviewService.getUgcSummaryFromHes(ugcSummaryRequest);
        metricAspect.addToTime(new Date().getTime() - startTime, UGC_SUMMARY,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        return new ResponseEntity<>(ugcPlatformReviewSummaryDTO, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/ugc-question/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<ClientUgcResponse>> loadProgramGi(
            @PathVariable("client") String client, @RequestBody ClientLoadProgramRequest request,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
        long startTime = System.currentTimeMillis();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", UGC_QUESTIONS, null);
        String correlationKey = tup.getX();
        ResponseWrapper<ClientUgcResponse> responseWrapper = new ResponseWrapper<>();
        Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpServletRequest);
        responseWrapper.setResponse(reviewService.loadProgram(request, httpServletRequest.getParameterMap(), correlationKey, client, httpHeaderMap));
        metricAspect.addToTime(new Date().getTime() - startTime, UGC_QUESTIONS,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/ugc-submit/partial/{srcClient}/2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResponseWrapper<ClientUgcResponse>> submitAnswersGi(
            @PathVariable("srcClient") String client,
            @RequestPart(value = "file") ClientSubmitApiRequest request, MultipartRequest multipartRequest,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
        long startTime = System.currentTimeMillis();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "",

                UGC_SUBMIT, null);
        String correlationKey = tup.getX();
        ResponseWrapper<ClientUgcResponse> responseWrapper = new ResponseWrapper<>();
        request.setClient(client);
        responseWrapper.setResponse(reviewService.submitAnswers(request, multipartRequest, correlationKey));
        metricAspect.addToTime(new Date().getTime() - startTime, UGC_SUBMIT,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
    }
}
