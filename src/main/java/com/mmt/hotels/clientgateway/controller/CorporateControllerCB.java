package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.UUIDException;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.restexecutors.CorporateExecutor;
import com.mmt.hotels.clientgateway.service.CorporateService;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.corporate.UpdateApprovalRequest;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateRequest;
import com.mmt.hotels.util.Tuple;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;

import java.net.SocketTimeoutException;
import java.util.Date;
import java.util.Map;


@RestController
@RequestMapping("/")
@Deprecated
public class CorporateControllerCB {

    @Autowired
    CorporateExecutor corporateExecutor;

    @Autowired
    CorporateHelper corporateHelper;

    @Autowired
    CorporateService corporateService;
    
	@Autowired
	private RequestHandler requestHandler;

	@Autowired
    private MetricAspect metricAspect;

    private static final Logger logger = LoggerFactory.getLogger(CorporateControllerCB.class);


    @RequestMapping(value = "entity/api/v3/approvals", method = RequestMethod.POST)
    public CGServerResponse updateApprovalByAuthcode(@RequestBody UpdateApprovalRequest updateApprovalBody,
                                                     @RequestParam(name = "authCode", required = true) String authCode,
                                                     @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
                                                     @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                                     HttpServletRequest httpRequest) {
        CGServerResponse serverResponse = new CGServerResponse();
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
            if(StringUtils.isNotBlank(authCode)) {
                serverResponse = corporateService.updateApprovalByAuthcode(updateApprovalBody, httpRequest.getParameterMap(), authCode, httpHeaderMap, correlationKey);
            }
            else {
                serverResponse.setResponseErrors(corporateHelper.generateInvalidApprovalErrorRsp());
                return serverResponse;
            }

        }catch (SocketTimeoutException ex) {
            logger.error("Corp Approval Socket Time Out Error", ex);
            serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.SOCKET_TIMEO_OUT_ERROR));

        }catch (UUIDException ex) {
            serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.UNAUTHORIZED_USER));
            logger.error("Error occured in update corp approval using authcode", ex);

        }catch (Exception ex) {
            serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.GENERIC_ERROR));

            logger.error("Error occured in update corp approval using authcode", ex);
        }finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/v3/approvals/update",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), "", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
        }
        return serverResponse;
    }


    @RequestMapping(value = "entity/api/v3/approvals", method = RequestMethod.GET)
    public CGServerResponse getWorkflowInfoByAuthcode(@RequestParam(name = Constants.auth_code, required = true) String authCode,
                                                      @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
                                                      @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                                      @RequestParam(name = Constants.SRC_CLIENT, required = false) String srcClient,
                                                      @RequestParam(name = "siteDomain", required = false, defaultValue = Constants.EMPTY_STRING) String siteDomain,
                                                      HttpServletRequest httpRequest) {

        CGServerResponse serverResponse = new CGServerResponse();
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
            if(StringUtils.isNotBlank(authCode)) {
                serverResponse = corporateService.workflowInfoByAuthcode(authCode, httpRequest.getParameterMap(), httpHeaderMap, correlationKey, siteDomain, srcClient);
            }
            else {
                serverResponse.setResponseErrors(corporateHelper.generateInvalidApprovalErrorRsp());
                return serverResponse;
            }

        }catch (SocketTimeoutException ex) {
            logger.error("Corp Approval Socket Time Out Error", ex);
            serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.SOCKET_TIMEO_OUT_ERROR));

        }catch (Exception ex) {
            serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.GENERIC_ERROR));

            logger.error("Error occured in update corp approval using authcode", ex);
        }finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/v3/approvals/get",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
        }
        return serverResponse;
    }

	@RequestMapping(value = "entity/api/v2/approvals", method = RequestMethod.POST)
	public CGServerResponse requestApproval(
            @RequestBody InitApprovalRequest approvalRequest,
			@RequestParam(name = Constants.CORRELATIONKEY, required = false) String correlationKey,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
			HttpServletRequest httpRequest,HttpServletResponse httpResponse) {
		
		CGServerResponse serverResponse = new CGServerResponse();
		long startTime = new Date().getTime();
		try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpRequest,httpResponse,
						correlationKey,Utility.getDeviceInfo(approvalRequest.getDeviceDetails()), "CORP" , "entity/api/v2/approvals",null);
			correlationKey = tup.getX();
				
			 Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
			 
			 serverResponse =  corporateService.requestApproval(approvalRequest, httpRequest.getParameterMap(), httpHeaderMap,correlationKey);
			 
		} catch (Exception e) {
			 serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.GENERIC_ERROR));

	         logger.error("Error occured in requestApproval", e);		
	    }finally{
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/v2/approvals/init",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), "", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
	    }
		 
		return serverResponse;
	}

    @RequestMapping(value = "entity/api/v2/approvals/{workflow_id}", method = RequestMethod.GET)
    public CGServerResponse getWorkflowInfo(@NotEmpty @PathVariable("workflow_id") String workflowId,
                                            @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
                                            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                            @RequestParam(name = Constants.SOURCE_CLIENT, required = true) String srcClient,
                                            @RequestParam(name = "siteDomain", required = false, defaultValue = Constants.EMPTY_STRING) String siteDomain,
                                            HttpServletRequest httpRequest,HttpServletResponse httpResponse) {

        CGServerResponse serverResponse = new CGServerResponse();
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpRequest,httpResponse,
                    correlationKey,srcClient, "CORP" , "entity/api/v2/approvals/getWorkflowId",null);
            correlationKey = tup.getX();
            Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
            serverResponse =  corporateService.getWorkflowInfo(workflowId,httpRequest.getParameterMap(),httpHeaderMap,correlationKey,siteDomain,srcClient);
        } catch (Exception e) {
            serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.GENERIC_ERROR));

            logger.error("Error occured in getapproval info", e);
        }finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/v2/approvals/get",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }

        return serverResponse;
    }

    @RequestMapping(value = "entity/api/v2/approvals/{workflow_id}", method = RequestMethod.POST)
    public CGServerResponse updateApproval(@RequestBody UpdateApprovalRequest updateApprovalBody,
                                           @NotEmpty @PathVariable("workflow_id") String workflowId,
                                           @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
                                           @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                           HttpServletRequest httpRequest,HttpServletResponse httpResponse) {
        CGServerResponse serverResponse = new CGServerResponse();
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpRequest,httpResponse,
                    correlationKey,Utility.getDeviceInfo(updateApprovalBody.getDeviceDetails()), "CORP" , "entity/api/v2/approvals/getWorkflowId",null);
            correlationKey = tup.getX();
            Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
            serverResponse = corporateService.updateApproval(updateApprovalBody,httpRequest.getParameterMap(),workflowId,httpHeaderMap, correlationKey);

        }catch (Exception e) {
            serverResponse.setResponseErrors(corporateHelper.getErrorResponseFromCBError(CBError.GENERIC_ERROR));

            logger.error("Error occured in getapproval info", e);
        }finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/v2/approvals/post",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), "", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
        return serverResponse;
    }
	
	@RequestMapping(value = "entity/api/v1/updateCorpPolicy",method = RequestMethod.POST)
	public String updateCorpPolicy(
            @RequestBody CorpPolicyUpdateRequest request,
			@RequestParam(name = "correlationKey", required = false) String correlationKey,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
            @RequestParam(name = "srcClient", required = false) String srcClient,
            HttpServletRequest httpRequest,HttpServletResponse httpResponse){
		String response=null;
		long startTime = new Date().getTime();
		try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
			String bookingDevice = request != null ? request.getBookingDevice() :"";
			Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpRequest,httpResponse,
					correlationKey,bookingDevice, "CORP" , "entity/api/v1/updateCorpPolicy",null);
			correlationKey = tup.getX();

			 Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
			 
			 response = corporateService.updateCorpPolicy(request,httpRequest.getParameterMap(),httpHeaderMap,correlationKey);
		}catch(UUIDException e){
			logger.error("UUIDException  in update Corporate Policy", e);
			response = ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
		} catch (Exception e) {
			logger.error("Error Occur in update Corporate Policy", e);
			response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
		}finally{
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/v1/updateCorpPolicy",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), srcClient, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
	    }
		 
		return response;
	}
}
