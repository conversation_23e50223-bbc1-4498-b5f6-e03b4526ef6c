package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.service.ComparatorService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.HotelComparatorResponse;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/")
@Deprecated
public class ComparatorControllerCB {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComparatorControllerCB.class);

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    ComparatorService comparatorService;

    @Autowired
    CorporateHelper corporateHelper;

    @Autowired
    MetricAspect metricAspect;

    @RequestMapping(value = "entity/api/hotelComparator", method = RequestMethod.POST)
    @ResponseBody
    public HotelComparatorResponse getHotelComparisonResponse(
            @RequestBody HotelDetailsMobRequestBody request,
            @RequestParam(name = Constants.REGION, required = false) String siteDomain,
            @RequestParam(name = Constants.CURRENCY, required = false) String currency,
            @RequestParam(name = Constants.LANGUAGE, required = false) String language,
            @RequestParam(name = Constants.CORRELATIONKEY, required = false) String correlationKey,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
            @RequestParam(name = Constants.REQ_PARAM_TYPE, required = false) String type,
            HttpServletRequest httpRequest, HttpServletResponse httpServletResponse
    ) {
        HotelComparatorResponse hotelComparatorResponse = new HotelComparatorResponse();
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpServletResponse, correlationKey, request.getBookingDevice(), request.getIdContext(), "entity/api/hotelComparator",null);

            if (StringUtils.isNotBlank(siteDomain)) {
                request.setSiteDomain(siteDomain.toUpperCase());
            }
            request.setCorrelationKey(correlationKey);
            hotelComparatorResponse = comparatorService.comparatorOld(request, httpRequest.getParameterMap(), tup.getY(), type);
        } catch (Exception ex) {
            hotelComparatorResponse.setResponseErrors(getResponseErrorFromCbError(CBError.GENERIC_ERROR));
            LOGGER.error("Error Occur in fetching Comparator response", ex);

        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotelComparator",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), request.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
        }
        return hotelComparatorResponse;
    }

    private ResponseErrors getResponseErrorFromCbError(CBError genericError) {
        Error error = new Error.Builder().buildErrorCode(genericError.getCode(), genericError.getDescription()).build();
        List<Error> errorList = new ArrayList<>();
        errorList.add(error);
        return new ResponseErrors.Builder().buildErrorList(errorList).build();
    }

}
