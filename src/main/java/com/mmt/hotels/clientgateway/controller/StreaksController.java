package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.MediaDataUrls;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.StreaksUserRequest;
import com.mmt.hotels.clientgateway.response.streaks.balance.StreaksUserEarningResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.StreaksService;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.STREAK_BALANCE;


@RestController
@RequestMapping("/")
public class StreaksController {


    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private StreaksService streaksService;
    @Autowired
    private MetricAspect metricAspect;



    private static final Logger logger = LoggerFactory.getLogger(StreaksController.class);

    @RequestMapping(value = "cg/streaks-user-earning/{client}/{version}", method = RequestMethod.GET)
    public ResponseEntity<ResponseWrapper<StreaksUserEarningResponse>> streaksEarningInfo(
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck)
            throws ClientGatewayException {

        StreaksUserRequest streaksUserRequest = new StreaksUserRequest();
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", STREAK_BALANCE);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, streaksUserRequest, correlationKey);
        streaksUserRequest.setCorrelationKey(correlationKey);

        ResponseWrapper<StreaksUserEarningResponse> streaksUserEarningResponseResponseWrapper = new ResponseWrapper<>();
        streaksUserEarningResponseResponseWrapper.setCorrelationKey(correlationKey);
        streaksUserEarningResponseResponseWrapper.setResponse(streaksService.streaksUserEarning(streaksUserRequest, httpServletRequest.getParameterMap(), tup.getY()));
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/streaks-user-earning", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        logger.debug("Successful, Returning Response.");
        MDC.clear();
        return new ResponseEntity<>(streaksUserEarningResponseResponseWrapper, HttpStatus.OK);
    }

}
