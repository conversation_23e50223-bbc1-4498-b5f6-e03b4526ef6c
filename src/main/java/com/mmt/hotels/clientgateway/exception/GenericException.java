package com.mmt.hotels.clientgateway.exception;

public class GenericException extends Exception {
    /**
     *
     */
    private static final long serialVersionUID = -1420106608282838653L;

    public GenericException(String message) {
        super(message);
    }

    public GenericException(Throwable throwable) {
        super(throwable);
    }

    public GenericException(String message, Throwable throwable) {
        super(message, throwable);
    }

    @Override
    public String toString() {
        return super.toString();
    }

}