package com.mmt.hotels.clientgateway.exception;

// only for backward compatibility , not to be used in newer apis as it is not based on client gateway exception

public class UUIDException extends Exception{

    public UUIDException(String message) {
        super(message);
    }

    public UUIDException(Throwable throwable) {
        super(throwable);
    }

    public UUIDException(String message, Throwable throwable) {
        super(message, throwable);
    }

    @Override
    public String toString() {
        return super.toString();
    }
}