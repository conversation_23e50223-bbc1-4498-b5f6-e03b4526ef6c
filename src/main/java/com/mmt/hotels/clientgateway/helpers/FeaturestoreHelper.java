package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.FeaturestoreConstants;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.model.request.dpt.FeatureStoreEntity;
import com.mmt.hotels.model.request.dpt.FeatureStoreRequest;
import com.mmt.hotels.model.request.dpt.FeatureStoreRequestEntity;
import com.mmt.hotels.model.response.dpt.FeatureStoreResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class FeaturestoreHelper {

    public FeatureStoreRequest prepareFeatureStoreRequest(String userProfile, String cityCheckInDate, List<String> feature) {
        FeatureStoreRequest request = new FeatureStoreRequest();
        FeatureStoreRequestEntity featureStoreRequestEntity = new FeatureStoreRequestEntity();
        featureStoreRequestEntity.setFeatures(new ArrayList<>());
        featureStoreRequestEntity.getFeatures().addAll(feature);
        FeatureStoreEntity featureStoreEntity = new FeatureStoreEntity();

        if(StringUtils.isNotEmpty(userProfile)){
            featureStoreEntity.setUserDetails(new ArrayList<>());
            featureStoreEntity.getUserDetails().add(userProfile);
        }
        if(StringUtils.isNotEmpty(cityCheckInDate)){
            featureStoreEntity.setCityIdCheckInDate(new ArrayList<>());
            featureStoreEntity.getCityIdCheckInDate().add(cityCheckInDate);
        }
        featureStoreRequestEntity.setEntity(featureStoreEntity);
        request.setEntities(new ArrayList<>());
        request.getEntities().add(featureStoreRequestEntity);
        return request;
    }

    public String prepareUserProfile(ExtendedUser extendedUser) {
        return (extendedUser.getUuid() + FeaturestoreConstants.SLACE + extendedUser.getProfileType());
    }

    public boolean processFeatureStoreResponseForBusinessUser(FeatureStoreResponse featureStoreResponse, FeatureStoreRequest featureStoreRequest) {
        if(featureStoreRequest != null && CollectionUtils.isNotEmpty(featureStoreRequest.getEntities())) {
            for (FeatureStoreRequestEntity featureStoreRequestEntity : featureStoreRequest.getEntities()) {
                if(featureStoreRequestEntity != null && featureStoreRequestEntity.getEntity() != null && featureStoreRequestEntity.getEntity().getUserDetails() != null) {
                    String userDetails = featureStoreRequestEntity.getEntity().getUserDetails().stream().findFirst().orElse(Constants.EMPTY_STRING);
                    if (featureStoreResponse != null && featureStoreResponse.getData() != null && StringUtils.isNotEmpty(userDetails)) {
                        JsonNode featureNode = featureStoreResponse.getData().get(userDetails);
                        for (String feature : featureStoreRequestEntity.getFeatures()) {
                            if(featureNode != null &&  featureNode.get(feature) != null) {
                                JsonNode featureValue = featureNode.get(feature).get(FeaturestoreConstants.VALUE_NODE);
                                return  featureValue != null && featureValue.isBoolean() && featureValue.booleanValue();
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

}
