package com.mmt.hotels.clientgateway.helpers;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.RoomPaxInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollection;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@PropertySource("classpath:defaultSequenceId.properties")
public class ListingHelper {
	
	@Autowired
	private CommonConfigHelper commonConfigHelper;
    
    @Autowired
	private Environment env;

	@Value("${pool.name}")
	private String poolName;
    
    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
	private PolyglotService polyglotService;

	public ListingPagePersonalizationResponsBO convertSearchHotelsToPersonalizedHotels(
			SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO, SearchHotelsRequest searchHotelsRequest) {
		if (searchWrapperResponseBO == null)
			return null;
		ListingPagePersonalizationResponsBO listingPagePersonalizationResponsBO = new ListingPagePersonalizationResponsBO();
		listingPagePersonalizationResponsBO.setBlackEligibilityDays(searchWrapperResponseBO.getBlackEligibilityDays());
		listingPagePersonalizationResponsBO.setBlackEligible(searchWrapperResponseBO.isBlackEligible());
		listingPagePersonalizationResponsBO.setHotelCountInCity(searchWrapperResponseBO.getHotelCountInCity());
		listingPagePersonalizationResponsBO.setBrinDetail(searchWrapperResponseBO.getBrinDetail());
		listingPagePersonalizationResponsBO.setCityCode(searchWrapperResponseBO.getCityCode());
		listingPagePersonalizationResponsBO.setCityName(searchWrapperResponseBO.getCityName());
		listingPagePersonalizationResponsBO.setCorrelationKey(searchWrapperResponseBO.getCorrelationKey());
		listingPagePersonalizationResponsBO.setCountryCode(searchWrapperResponseBO.getCountryCode());
		listingPagePersonalizationResponsBO.setCountryName(searchWrapperResponseBO.getCountryName());
		listingPagePersonalizationResponsBO.setCurrency(searchWrapperResponseBO.getCurrency());
		listingPagePersonalizationResponsBO.setExpData(searchWrapperResponseBO.getExpData());
		listingPagePersonalizationResponsBO.setFailureReason(searchWrapperResponseBO.getFailureReason());
		listingPagePersonalizationResponsBO.setFirstTimeUser(searchWrapperResponseBO.isFirstTimeUser());
		listingPagePersonalizationResponsBO.setIncrementalDiscount(searchWrapperResponseBO.getIncrementalDiscount());
		listingPagePersonalizationResponsBO.setLastFetchedHotelCategory(searchWrapperResponseBO.getLastFetchedHotelCategory());
		listingPagePersonalizationResponsBO.setLastFetchedHotelId(searchWrapperResponseBO.getLastFetchedHotelId());
		listingPagePersonalizationResponsBO.setLastFetchedWindowInfo(searchWrapperResponseBO.getLastFetchedWindowInfo());
		listingPagePersonalizationResponsBO.setMatchMakerQuestions(searchWrapperResponseBO.getMatchMakerQuestions());
		listingPagePersonalizationResponsBO.setMatchMakerResponse(searchWrapperResponseBO.isMatchMakerResponse());
		listingPagePersonalizationResponsBO.setNoMoreAvailableHotels(searchWrapperResponseBO.isNoMoreAvailableHotels());
		listingPagePersonalizationResponsBO.setPersonalizedResponse(buildPersonalizedResponse(searchWrapperResponseBO,searchHotelsRequest.getRequestDetails(), searchHotelsRequest.getFeatureFlags(), searchHotelsRequest));
		listingPagePersonalizationResponsBO.setResponseErrors(searchWrapperResponseBO.getResponseErrors());
		listingPagePersonalizationResponsBO.setShowFcBanner(searchWrapperResponseBO.getShowFcBanner());
		listingPagePersonalizationResponsBO.setSortCriteria(searchWrapperResponseBO.getSortCriteria());
		listingPagePersonalizationResponsBO.setStaticHotelCounts(searchWrapperResponseBO.getStaticHotelCounts());
		listingPagePersonalizationResponsBO.setSuppressPas(searchWrapperResponseBO.isSuppressPas());
		listingPagePersonalizationResponsBO.setTotalHotelCounts(searchWrapperResponseBO.getTotalHotelCounts());
		listingPagePersonalizationResponsBO.setWarningMessage(searchWrapperResponseBO.getWarningMessage());
		listingPagePersonalizationResponsBO.setLocusData(searchWrapperResponseBO.getLocusData());
		listingPagePersonalizationResponsBO.setUsradid(searchWrapperResponseBO.getUsradid());
		listingPagePersonalizationResponsBO.setHydraSegments(searchWrapperResponseBO.getHydraSegments());
		listingPagePersonalizationResponsBO.setSectionsType(searchWrapperResponseBO.getSectionsType());
		listingPagePersonalizationResponsBO.setSharingUrl(searchWrapperResponseBO.getSharingUrl());
		return listingPagePersonalizationResponsBO;
	}

	private List<PersonalizedResponse<SearchWrapperHotelEntity>> buildPersonalizedResponse(SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO, RequestDetails requestDetails, FeatureFlags featureFlags, SearchHotelsRequest searchHotelsRequest) {

		List<PersonalizedResponse<SearchWrapperHotelEntity>> personalizedResponses= new ArrayList<>();

		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.RECOMMENDED_HOTELS_SECTION);
			if ("GETAWAY".equalsIgnoreCase(requestDetails.getFunnelSource())) {
				personalizedResponse.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.GETAWAYS_RECOMMENDED_HEADING));
			}else if (featureFlags!= null && featureFlags.isMostBooked()) {
				personalizedResponse.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.MOST_BOOKED_HEADING));
			}else {
//				personalizedResponse.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.RECOMMENDED_HOTELS_HEADING));
				String heading = polyglotService.getTranslatedData(ConstantsTranslation.RECOMMENDED_HOTELS_HEADING_NEW);
				heading = updateHeading(heading, searchHotelsRequest, searchWrapperResponseBO.getCityName());
				personalizedResponse.setHeading(heading);
			}
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getHotelList());
                  //in case of SimilarHotels are true we will get this card from HES and need to give it to client always. HTL-37224
			if(StringUtils.isNotBlank(searchWrapperResponseBO.getHotelCardType())){
				personalizedResponse.setHotelCardType(searchWrapperResponseBO.getHotelCardType());
			}
			personalizedResponses.add(personalizedResponse);
		}
		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getNearbyHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.NEARBY_HOTELS_SECTION);
			personalizedResponse.setHeading(searchWrapperResponseBO.getNearbyHeading());
			personalizedResponse.setSubHeading(searchWrapperResponseBO.getNearbySubHeading());
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getNearbyHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getNearbyHotelList());
			personalizedResponses.add(personalizedResponse);
		}
		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getOtherAltAccoHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.OTHER_ALTACCO_HOTELS_SECTION);
			personalizedResponse.setHeading(searchWrapperResponseBO.getOtherAltAccoHeading());
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getOtherAltAccoHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getOtherAltAccoHotelList());
			personalizedResponses.add(personalizedResponse);
		}
		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getNonAltAccoHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.NON_ALTACCO_HOTELS_SECTION);
			personalizedResponse.setHeading(searchWrapperResponseBO.getNonAltAccoHeading());
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getNonAltAccoHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getNonAltAccoHotelList());
			personalizedResponses.add(personalizedResponse);
		}
		if (CollectionUtils.isEmpty(personalizedResponses))
			return null;
		return personalizedResponses;
	}

	private String updateHeading(String heading, SearchHotelsRequest searchHotelsRequest, String cityName){
		//Recommend_Hotels section's heading is to be updated to Properties in {City/Area}
		if (searchHotelsRequest.getMatchMakerDetails() != null && CollectionUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getSelectedTags())) {
			heading = heading.replace("{AREA}", searchHotelsRequest.getMatchMakerDetails().getSelectedTags().get(0).getTagDescription());
		} else if (StringUtils.isNotBlank(cityName)) {
			heading = heading.replace("{AREA}", cityName);
		} else {
			heading = polyglotService.getTranslatedData(ConstantsTranslation.RECOMMENDED_HOTELS_HEADING);
		}
		return heading;
	}

	public void updateCollectionCounts(ListingSearchRequest searchHotelsRequest){
		if (MapUtils.isNotEmpty(commonConfigHelper.getCollectionCountMapping())
				&& StringUtils.isNotBlank(searchHotelsRequest.getDeviceDetails().getBookingDevice())
				&& commonConfigHelper.getCollectionCountMapping().containsKey(searchHotelsRequest.getDeviceDetails().getBookingDevice().toUpperCase())) {
			Map<String, String> pageContextToCountMap = commonConfigHelper.getCollectionCountMapping().get(searchHotelsRequest.getDeviceDetails().getBookingDevice().toUpperCase());
			if (MapUtils.isNotEmpty(pageContextToCountMap) && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getPageContext())
					&& pageContextToCountMap.containsKey(searchHotelsRequest.getRequestDetails().getPageContext().toUpperCase())
					&& searchHotelsRequest.getSearchCriteria().getCollectionCriteria() != null)
				searchHotelsRequest.getSearchCriteria().getCollectionCriteria().setCollectionsCount(pageContextToCountMap.get(searchHotelsRequest.getRequestDetails().getPageContext().toUpperCase()));
		}
	}

	public void sortBasedOnPriority(List<FetchCollection> fetchCollectionList) {
		Collections.sort(fetchCollectionList, new Comparator<FetchCollection>() {
			@Override
			public int compare(FetchCollection coll1, FetchCollection coll2) {
				return Integer.valueOf(coll1.getPriority()) - Integer.valueOf(coll2.getPriority());
			}
		});
	}
}
