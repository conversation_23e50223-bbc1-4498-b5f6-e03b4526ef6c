package com.mmt.hotels.clientgateway.helpers;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigDetail;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.request.matchmaker.LatLngObject;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.SELECTED_AREA_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FILTER_VALUE_SPLITTER_DPT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.MATCHMAKER_AREA;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.MATCHMAKER_POI;

@Component
public class FilterHelper {

    private final Gson gson = new Gson();

    @Autowired
    PolyglotService polyglotService;


    public FilterConfiguration getFilterConfig(String... filterConfigList) {
        FilterConfiguration filterConfiguration = null;

        if (filterConfigList != null && filterConfigList.length > 0) {
            String idContext = filterConfigList[filterConfigList.length - 1];
            for (int i = 0; i < filterConfigList.length - 1; i++) {
                FilterConfiguration fConfig = null;

                if (StringUtils.isNotEmpty(filterConfigList[i])) {
                    fConfig = gson.fromJson(filterConfigList[i], new TypeToken<FilterConfiguration>() {
                    }.getType());
                }

                if (filterConfiguration == null)
                    filterConfiguration = fConfig;
                else {
                    filterConfiguration = mergeFilterConfig(filterConfiguration, fConfig, idContext);
                }
            }
        }
        return filterConfiguration;
    }

    public FilterConfiguration mergeFilterConfig(FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig, String idContext) {
        FilterConfiguration mergedFilterConfig = new FilterConfiguration();

        if (addedFilterConfig == null) {
            mergedFilterConfig = srcFilterConfig;
            return mergedFilterConfig;
        }

        mergeFilters(mergedFilterConfig, srcFilterConfig, addedFilterConfig, Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext));
        mergedFilterConfig = calculateFiltersToShow(mergedFilterConfig, addedFilterConfig);
        overrideRankOrder(mergedFilterConfig, srcFilterConfig, addedFilterConfig);
        addConditions(mergedFilterConfig, srcFilterConfig, addedFilterConfig);
        if (!Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            mergedFilterConfig.setHomestayBannerIconUrl(srcFilterConfig.getHomestayBannerIconUrl());
        }
        if(mergedFilterConfig.getSuggestedFiltersThreshold() == 0 && srcFilterConfig.getSuggestedFiltersThreshold() != 0)
            mergedFilterConfig.setSuggestedFiltersThreshold(srcFilterConfig.getSuggestedFiltersThreshold());
        return mergedFilterConfig;
    }

    public static MatchMakerRequest updateAppliedFilterMapForLocationDptFilters(MatchMakerRequest matchMakerDetails, String[] filters) {

        if (filters != null && filters.length > 1) {
            String[] filterValueList = filters[1].split(FILTER_VALUE_SPLITTER_DPT);
            if (matchMakerDetails == null) {
                matchMakerDetails = new MatchMakerRequest();
            }
            if (MATCHMAKER_AREA.equalsIgnoreCase(filters[0])) {
                if (CollectionUtils.isEmpty(matchMakerDetails.getSelectedTags())) {
                    matchMakerDetails.setSelectedTags(new ArrayList<>());
                }
                for (int i = 0; i < filterValueList.length; i++) {
                    Tags areaTag = new Tags();
                    areaTag.setTagAreaId(filterValueList[i]);
                    areaTag.setTagDescription(SELECTED_AREA_TEXT);
                    matchMakerDetails.getSelectedTags().add(areaTag);
                }
            } else if (MATCHMAKER_POI.equalsIgnoreCase(filters[0])) {
                if (CollectionUtils.isEmpty(matchMakerDetails.getLatLng())) {
                    matchMakerDetails.setLatLng(new ArrayList<>());
                }
                for (int i = 0; i < filterValueList.length; i++) {
                    LatLngObject latLngObject = new LatLngObject();
                    latLngObject.setPoiId(filterValueList[i]);
                    matchMakerDetails.getLatLng().add(latLngObject);
                }
            }
        }
        return matchMakerDetails;
    }

    private FilterConfiguration calculateFiltersToShow(FilterConfiguration mergedFilterConfig, FilterConfiguration addedFilterConfig) {
        if (MapUtils.isNotEmpty(addedFilterConfig.getFiltersToShow()) && mergedFilterConfig != null && mergedFilterConfig.getFilters() != null) {
            FilterConfiguration finalConfig = new FilterConfiguration();
            finalConfig.setConditions(mergedFilterConfig.getConditions());
            finalConfig.setRankOrder(mergedFilterConfig.getRankOrder());
            finalConfig.setFilters(new LinkedHashMap<>());
            for (Map.Entry<String, LinkedHashMap<String, List<String>>> catEntry : addedFilterConfig.getFiltersToShow().entrySet()) {
                FilterConfigCategory filterCat = mergedFilterConfig.getFilters().get(catEntry.getKey());
                if (filterCat != null) {
                    FilterConfigCategory finalCat = new FilterConfigCategory();
                    finalCat.setCondition(filterCat.getCondition());
                    finalCat.setTitle(filterCat.getTitle());
                    finalCat.setAppliedTitle(filterCat.getAppliedTitle());
                    finalCat.setSubTitle(filterCat.getSubTitle());
                    finalCat.setViewType(filterCat.getViewType());
                    finalCat.setSingleSelection(filterCat.isSingleSelection());
                    finalCat.setShowCustomRange(filterCat.isShowCustomRange());
                    finalCat.setCustomRangeTitle(filterCat.getCustomRangeTitle());
                    finalCat.setVisible(filterCat.isVisible());
                    finalCat.setShowMore(filterCat.isShowMore());
                    finalCat.setMinItemsToShow(filterCat.getMinItemsToShow());
                    finalCat.setShowImageUrl(filterCat.isShowImageUrl());
                    finalCat.setIconUrl(filterCat.getIconUrl());
                    finalCat.setGroups(new LinkedHashMap<>());
                    for (Map.Entry<String, List<String>> filterGrpEntry : catEntry.getValue().entrySet()) {
                        finalCat.getGroups().put(filterGrpEntry.getKey(), new LinkedHashMap<>());
                        LinkedHashMap<String, FilterConfigDetail> filterGroupValues = filterCat.getGroups().get(filterGrpEntry.getKey());
                        if (MapUtils.isNotEmpty(filterGroupValues)) {
                            if (CollectionUtils.isNotEmpty(filterGrpEntry.getValue())) {
                                for (String filterGrpVal : filterGrpEntry.getValue()) {
                                    String filterValue = filterGrpVal.split(":")[0];
                                    FilterConfigDetail finalConfigDetail = filterGroupValues.get(filterValue);
                                    finalCat.getGroups().get(filterGrpEntry.getKey()).put(filterGrpVal, finalConfigDetail);
                                }
                            }
                        }
                    }
                    finalConfig.getFilters().put(catEntry.getKey(), finalCat);
                }
            }


            return finalConfig;
        }
        return mergedFilterConfig;

    }

    private void overrideRankOrder(FilterConfiguration mergedFilterConfig, FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig) {
        if (MapUtils.isNotEmpty(addedFilterConfig.getRankOrder()))
            mergedFilterConfig.setRankOrder(addedFilterConfig.getRankOrder());
        else if (MapUtils.isNotEmpty(srcFilterConfig.getRankOrder()))
            mergedFilterConfig.setRankOrder(srcFilterConfig.getRankOrder());
    }

    private void addConditions(FilterConfiguration mergedFilterConfig, FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig) {
        mergedFilterConfig.setConditions(new LinkedHashMap<>());

        if (MapUtils.isNotEmpty(srcFilterConfig.getConditions()))
            mergedFilterConfig.setConditions(srcFilterConfig.getConditions());
        if (MapUtils.isNotEmpty(addedFilterConfig.getConditions())) {
            for (Map.Entry<String, List<String>> entry : addedFilterConfig.getConditions().entrySet())
                mergedFilterConfig.getConditions().put(entry.getKey(), entry.getValue());
        }

    }

    private void mergeFilters(FilterConfiguration mergedFilterConfig, FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig, boolean isCorpContext) {
        if (MapUtils.isNotEmpty(addedFilterConfig.getFilters())) {
            if (MapUtils.isNotEmpty(srcFilterConfig.getFilters())) {
                mergedFilterConfig.setFilters(new LinkedHashMap<>());
                for (Map.Entry<String, FilterConfigCategory> entry : srcFilterConfig.getFilters().entrySet()) {
                    FilterConfigCategory fCategory = entry.getValue();
                    if (addedFilterConfig.getFilters().containsKey(entry.getKey())) {
                        FilterConfigCategory fCategoryAdded = addedFilterConfig.getFilters().get(entry.getKey());
                        mergeConditions(fCategory, fCategoryAdded);
                        if (!isCorpContext) {
                            updateIconUrls(fCategory, fCategoryAdded);
                        }
                        mergedFilterConfig.getFilters().put(entry.getKey(), fCategoryAdded);
                    } else {
                        mergedFilterConfig.getFilters().put(entry.getKey(), fCategory);
                    }
                }
            }

            //those which are present in the addedFilterConfig but not present in the srcFilterConfig, so not yet added into mergedFilterConfig
            for (Map.Entry<String, FilterConfigCategory> entry : addedFilterConfig.getFilters().entrySet()) {
                FilterConfigCategory fCategory = entry.getValue();
                if (!mergedFilterConfig.getFilters().containsKey(entry.getKey())) {
                    mergedFilterConfig.getFilters().put(entry.getKey(), fCategory);
                }

            }
        } else {
            mergedFilterConfig.setFilters(srcFilterConfig.getFilters());
            if (isCorpContext) {
                /*don't set new icon urls*/
                removeIconUrlsAndImageUrls(mergedFilterConfig);
            }
        }
    }

    private void removeIconUrlsAndImageUrls(FilterConfiguration mergedFilterConfig) {
        if (mergedFilterConfig == null || MapUtils.isEmpty(mergedFilterConfig.getFilters())) {
            return;
        }
        mergedFilterConfig.getFilters().forEach((filterGroup, filterConfig) -> {
            filterConfig.setIconUrl(null);
            if (MapUtils.isNotEmpty(filterConfig.getGroups())) {
                filterConfig.getGroups().forEach((filterGroupValue, fConfig) -> {
                    if (MapUtils.isNotEmpty(fConfig)) {
                        fConfig.forEach((filterValue, filterValueConfig) -> {
                            if (filterValueConfig != null) {
                                filterValueConfig.setImageUrl(null);
                                filterValueConfig.setIconList(null);
                            }
                        });
                    }
                });
            }
        });
    }

    private void updateIconUrls(FilterConfigCategory fCategory, FilterConfigCategory fCategoryAdded) {
        if (MapUtils.isNotEmpty(fCategory.getGroups()) && MapUtils.isNotEmpty(fCategoryAdded.getGroups())) {
            fCategoryAdded.getGroups().forEach((filterGroup, filters) -> {
                if (fCategory.getGroups().containsKey(filterGroup) && MapUtils.isNotEmpty(filters)) {
                    LinkedHashMap<String, FilterConfigDetail> filterConfigDetail = fCategory.getGroups().get(filterGroup);
                    filters.forEach((filterValue, filterConfig) -> {
                        FilterConfigDetail configDetail = filterConfigDetail.get(filterValue);
                        if (configDetail != null && filterConfig != null) {
                            filterConfig.setIconList(configDetail.getIconList());
                            filterConfig.setImageUrl(configDetail.getImageUrl());
                        }
                    });
                }
            });
        }
        fCategoryAdded.setIconUrl(fCategory.getIconUrl());
    }

    private void mergeConditions(FilterConfigCategory fCategory, FilterConfigCategory fCategoryAdded) {
        if (MapUtils.isNotEmpty(fCategory.getCondition())) {
            LinkedHashMap<String, String> finalCondition = new LinkedHashMap<>(fCategory.getCondition());
            if (MapUtils.isNotEmpty(fCategoryAdded.getCondition()))
                finalCondition.putAll(fCategoryAdded.getCondition());
            fCategoryAdded.setCondition(finalCondition);
        }
    }

    public String fetchPriceTitle(LinkedHashMap<String, String> expDataMap) {
        StringBuilder sb = new StringBuilder();

        String pdo = (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(Constants.PRICE_EXP)) ? expDataMap.get(Constants.PRICE_EXP) : EMPTY_STRING;
        switch (pdo) {
            case Constants.PRICE_PN:
                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PN_TITLE));
                break;
            case Constants.PRICE_PNT:
                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PNT_TITLE));
                break;
            case Constants.PRICE_PRN:
                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PRN_TITLE));
                break;
            case Constants.PRICE_PRNT:
                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PRNT_TITLE));
                break;
            case Constants.PRICE_TP:
                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TP_TITLE));
                break;
            case Constants.PRICE_TPT:
                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TPT_TITLE));
                break;
//            case Constants.PRICE_PPPN:
//                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PPPN_TITLE));
//                break;
            default:
                sb.append(polyglotService.getTranslatedData("PRICE_FILTER_TITLE"));

        }
        return sb.toString();
    }

}
