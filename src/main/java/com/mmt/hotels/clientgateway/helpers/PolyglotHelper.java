package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigDetail;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.thankyou.MyTripCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.listpersonalization.LuxeToolTip;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class PolyglotHelper {

    @Autowired
    PolyglotService polyglotService;


    public FilterConfiguration translateFilterConfig(FilterConfiguration fConfig,String funnelSource) {

        if (fConfig == null){
            return null;
        }

        if (MapUtils.isNotEmpty(fConfig.getFilters())){

            for (Map.Entry<String, FilterConfigCategory> entry : fConfig.getFilters().entrySet()) {

                FilterConfigCategory filterConfigCategory = entry.getValue();
                filterConfigCategory.setCustomRangeTitle(polyglotService.getTranslatedData(filterConfigCategory.getCustomRangeTitle(),funnelSource));
                filterConfigCategory.setTitle(polyglotService.getTranslatedData(filterConfigCategory.getTitle(),funnelSource));

                if (MapUtils.isNotEmpty(filterConfigCategory.getGroups())){

                    for (Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> group : filterConfigCategory.getGroups().entrySet()){

                        LinkedHashMap<String, FilterConfigDetail> filterConfigDetailLinkedHashMap = group.getValue();

                        if (MapUtils.isNotEmpty(filterConfigDetailLinkedHashMap)){

                            for (Map.Entry<String, FilterConfigDetail> filterConfigDetailEntry : filterConfigDetailLinkedHashMap.entrySet()){

                                FilterConfigDetail filterConfigDetail = filterConfigDetailEntry.getValue();
                                if (filterConfigDetail != null){

                                    filterConfigDetail.setTitle(polyglotService.getTranslatedData(filterConfigDetail.getTitle(),funnelSource));
                                    filterConfigDetail.setAppliedTitle(polyglotService.getTranslatedData(filterConfigDetail.getAppliedTitle(),funnelSource));
                                    filterConfigDetail.setSubTitle(polyglotService.getTranslatedData(filterConfigDetail.getSubTitle(),funnelSource));
                                    if(StringUtils.isNotBlank(filterConfigDetail.getDescription())){
                                        filterConfigDetail.setDescription(polyglotService.getTranslatedData(filterConfigDetail.getDescription()));
                                    }
                                }
                            }
                        }
                    }
                }
            }


        }

        return fConfig;

    }

    public void translateMyTripsCards(Map<String, MyTripCard> myTripsCardTypeToCardDetailsModified,
                                      Map<String, String> myTripsCardTypeToCardTextReplacer) {

        if (MapUtils.isNotEmpty(myTripsCardTypeToCardDetailsModified)){


            for (Map.Entry<String, MyTripCard> element : myTripsCardTypeToCardDetailsModified.entrySet()){

                MyTripCard myTripCard = element.getValue();
                if (myTripsCardTypeToCardTextReplacer != null && myTripsCardTypeToCardTextReplacer.containsKey(myTripCard.getText())) {
                    myTripCard.setText(myTripsCardTypeToCardTextReplacer.get(myTripCard.getText()));
                }
                myTripCard.setText(polyglotService.getTranslatedData(myTripCard.getText()));

            }
        }
    }

    public void translatePersuasionMap(Map<String, Map<String, PersuasionResponse>> persuasionResponseMap) {

        if (MapUtils.isNotEmpty(persuasionResponseMap)){

            for (Map.Entry<String, Map<String, PersuasionResponse>> mySafetyData : persuasionResponseMap.entrySet()){

                Map<String, PersuasionResponse> safetyMap = mySafetyData.getValue();

                if (MapUtils.isNotEmpty(safetyMap)){

                    for (Map.Entry<String, PersuasionResponse> entry : safetyMap.entrySet()){

                        PersuasionResponse persuasion = entry.getValue();
                        persuasion.setTitle(polyglotService.getTranslatedData(persuasion.getTitle()));
                        persuasion.setSubText(polyglotService.getTranslatedData(persuasion.getSubText()));
                        persuasion.setDisplayText(polyglotService.getTranslatedData(persuasion.getDisplayText()));
                        persuasion.setDisplaySubText(polyglotService.getTranslatedData(persuasion.getDisplaySubText()));
                        persuasion.setId("MYSAFETY_PERSUASION");
                        persuasion.setPlaceholderId("MYSAFETY_DATA");
                    }
                }
            }
        }
    }

    public void translateHotelCategoryData(Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMap) {
        if (MapUtils.isNotEmpty(hotelCategoryDataWebMap)){
            for (Map.Entry<String, HotelCategoryDataWeb> entry : hotelCategoryDataWebMap.entrySet()){
                HotelCategoryDataWeb hotelCategoryDataWeb = entry.getValue();
                hotelCategoryDataWeb.setTitle(polyglotService.getTranslatedData(hotelCategoryDataWeb.getTitle()));
            }
        }
    }

    public void translateValueStaysTooltip(ValueStaysTooltip tooltip) {
        if (tooltip == null) {
            return;
        }
        tooltip.setTitleText(polyglotService.getTranslatedData(tooltip.getTitleText()));
        tooltip.setSubText(polyglotService.getTranslatedData(tooltip.getSubText()));
        tooltip.setFooterText(polyglotService.getTranslatedData(tooltip.getFooterText()));
        if (CollectionUtils.isNotEmpty(tooltip.getData())) {
            tooltip.getData().forEach(t -> t.setTitleText(polyglotService.getTranslatedData(t.getTitleText())));
        }
    }

    public void translateMyBizAssuredTooltip(MyBizAssuredToolTip myBizAssuredToolTip) {
        if (myBizAssuredToolTip == null) {
            return;
        }
        myBizAssuredToolTip.setTitleText(polyglotService.getTranslatedData(myBizAssuredToolTip.getTitleText()));
        myBizAssuredToolTip.setSubText(polyglotService.getTranslatedData(myBizAssuredToolTip.getSubText()));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(myBizAssuredToolTip.getData())) {
            myBizAssuredToolTip.getData().forEach(t -> t.setTitleText(polyglotService.getTranslatedData(t.getTitleText())));
        }
    }

    public void translateLuxeToolTip(LuxeToolTip toolTip)
    {
        if(toolTip == null) {
            return;
        }
        toolTip.setTitleText(polyglotService.getTranslatedData(toolTip.getTitleText()));
        toolTip.setSubText(polyglotService.getTranslatedData(toolTip.getSubText()));
        if (CollectionUtils.isNotEmpty(toolTip.getData())) {
            toolTip.getData().forEach(t -> t.setTitleText(polyglotService.getTranslatedData(t.getTitleText())));
        }
    }


    public MySafetyTooltip translateMySafetyToolTip(MySafetyTooltip toolTip, String lang)
    {
        if(toolTip == null) {
            return null;
        }
        MySafetyTooltip mySafetyTooltipTranslated = new MySafetyTooltip();
        mySafetyTooltipTranslated.setTitle(polyglotService.getTranslatedDataInLang(toolTip.getTitle(),lang));
        if (ArrayUtils.isNotEmpty(toolTip.getData())) {
            List<String> translatedString = Arrays.stream(toolTip.getData()).map(str -> polyglotService.getTranslatedDataInLang(str,lang)).collect(Collectors.toList());
            String[] strings = translatedString.toArray(new String[toolTip.getData().length]);
            mySafetyTooltipTranslated.setData(strings);
        }
        mySafetyTooltipTranslated.setIconUrl(toolTip.getIconUrl());
        return mySafetyTooltipTranslated;
    }

    public void translateMobgenJsonBO(Map<String, Map<String, MobgenJsonBO>> mobgenJsonBOMap) {

        if (MapUtils.isNotEmpty(mobgenJsonBOMap)) {

            for (Map.Entry<String, Map<String, MobgenJsonBO>> entry1 :
                    mobgenJsonBOMap.entrySet()) {
                for (Map.Entry<String, MobgenJsonBO> entry2 : entry1.getValue().entrySet()) {
                    MobgenJsonBO mobgenJsonBO = entry2.getValue();
                    mobgenJsonBO.setTitle(polyglotService.getTranslatedData(mobgenJsonBO.getTitle()));
                    mobgenJsonBO.setNegativeBtnText(polyglotService.getTranslatedData(mobgenJsonBO.getNegativeBtnText()));
                    mobgenJsonBO.setPositiveBtnText(polyglotService.getTranslatedData(mobgenJsonBO.getPositiveBtnText()));
                    mobgenJsonBO.setMsgToOverride(polyglotService.getTranslatedData(mobgenJsonBO.getMsgToOverride()));
                    mobgenJsonBO.setQuickBook(polyglotService.getTranslatedData(mobgenJsonBO.getQuickBook()));
                    mobgenJsonBO.setTariffLevel(polyglotService.getTranslatedData(mobgenJsonBO.getTariffLevel()));
                }

            }
        }

    }


    public void translateMobgenStringsBO(Map<String, MobgenStringsBO> mobgenStringsBOMap) {

        if(MapUtils.isNotEmpty(mobgenStringsBOMap)) {

            for(Map.Entry<String, MobgenStringsBO> entry : mobgenStringsBOMap.entrySet()) {

                MobgenStringsBO mobgenStringsBO = entry.getValue();
                mobgenStringsBO.setCardMessageFreeFailedBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessageFreeFailedBooking()));
                mobgenStringsBO.setCardMessageFreeNonRefundablePendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessageFreeNonRefundablePendingBooking()));
                mobgenStringsBO.setCardMessageFreePendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessageFreePendingBooking()));
                mobgenStringsBO.setCardMessageFreeRefundablePendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessageFreeRefundablePendingBooking()));
                mobgenStringsBO.setCardMessagePaidFailedBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessagePaidFailedBooking()));
                mobgenStringsBO.setCardMessagePaidNonRefundablePendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessagePaidNonRefundablePendingBooking()));
                mobgenStringsBO.setCardMessagePaidPendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessagePaidPendingBooking()));
                mobgenStringsBO.setCardMessagePaidRefundablePendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessagePaidRefundablePendingBooking()));
                mobgenStringsBO.setCardMessageTrackFailedBookingRefund(polyglotService.getTranslatedData(mobgenStringsBO.getCardMessageTrackFailedBookingRefund()));
                mobgenStringsBO.setDonationText(polyglotService.getTranslatedData(mobgenStringsBO.getDonationText()));
                mobgenStringsBO.setHeaderDescFreeFailedBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderDescFreeFailedBooking()));
                mobgenStringsBO.setHeaderDescPaidFailedBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderDescPaidFailedBooking()));
                mobgenStringsBO.setHeaderDescPendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderDescPendingBooking()));
                mobgenStringsBO.setHeaderDescSuccessBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderDescSuccessBooking()));
                mobgenStringsBO.setHeaderTitleFailedBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderTitleFailedBooking()));
                mobgenStringsBO.setHeaderTitleFreePendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderTitleFreePendingBooking()));
                mobgenStringsBO.setHeaderTitlePaidPendingBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderTitlePaidPendingBooking()));
                mobgenStringsBO.setHeaderTitleSuccessBooking(polyglotService.getTranslatedData(mobgenStringsBO.getHeaderTitleSuccessBooking()));
                mobgenStringsBO.setMessagePropertyRulesCard(polyglotService.getTranslatedData(mobgenStringsBO.getMessagePropertyRulesCard()));
                mobgenStringsBO.setMessagePropertyRulesCardMyBizz(polyglotService.getTranslatedData(mobgenStringsBO.getMessagePropertyRulesCardMyBizz()));
                mobgenStringsBO.setMessageRoomBreakUpCard(polyglotService.getTranslatedData(mobgenStringsBO.getMessageRoomBreakUpCard()));
                mobgenStringsBO.setMessageRoomBreakUpCardMyBizz(polyglotService.getTranslatedData(mobgenStringsBO.getMessageRoomBreakUpCardMyBizz()));
                mobgenStringsBO.setMessageSuccessBookingAmountBreak(polyglotService.getTranslatedData(mobgenStringsBO.getMessageSuccessBookingAmountBreak()));
                mobgenStringsBO.setMessageSuccessBookingAmountBreakMyBizz(polyglotService.getTranslatedData(mobgenStringsBO.getMessageSuccessBookingAmountBreakMyBizz()));
                mobgenStringsBO.setGst(polyglotService.getTranslatedData(mobgenStringsBO.getGst()));
                mobgenStringsBO.setPrice(polyglotService.getTranslatedData(mobgenStringsBO.getPrice()));
                mobgenStringsBO.setRating(polyglotService.getTranslatedData(mobgenStringsBO.getRating()));
            }
        }
    }

    public void translateHotelCategoryDataMap(Map<String, HotelCategoryData> hotelCategoryDataMapNode) {
        for(Map.Entry<String, HotelCategoryData> entry : hotelCategoryDataMapNode.entrySet()) {
            HotelCategoryData hotelCategoryData = entry.getValue();
            hotelCategoryData.setDisplayText(polyglotService.getTranslatedData(hotelCategoryData.getDisplayText()));
            hotelCategoryData.setTitle(polyglotService.getTranslatedData(hotelCategoryData.getTitle()));

        }
    }

    public void translateHotelCategoryDataWebMapNew(HotelCategoryDataWeb hotelCategoryDataWeb) {
        hotelCategoryDataWeb.setTitle(polyglotService.getTranslatedData(hotelCategoryDataWeb.getTitle()));
    }

    /**
     * Function to replace nodes from Polyglot. For Specific nodes which are nor present at base level.
     *This problems comes due to non-flat hierarchy of nodes
     */
    public void translateConfigNodesFromPolyglot(JsonNode node) {
        if(node!=null && node.get(Constants.GROUP_BOOKING_TEXT)!=null && node.get(Constants.GROUP_BOOKING_TEXT).get(ConstantsTranslation.LANDING_GB_MSG)!=null &&
                StringUtils.isNotEmpty(node.get(Constants.GROUP_BOOKING_TEXT).get(ConstantsTranslation.LANDING_GB_MSG).textValue())) {
            ((ObjectNode)node.get(Constants.GROUP_BOOKING_TEXT)).put(ConstantsTranslation.LANDING_GB_MSG, polyglotService.getTranslatedData(ConstantsTranslation.LANDING_GB_MSG));
        }

        //Fetching data from poly to show pop up on client side for Book now flow MyPartner
        //Fetching every translation/polyglot key from PMS itself
        String polyglotKey;
        if(node!=null && node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)!=null) {
            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_HEADING).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_HEADING,polyglotService.getTranslatedData(polyglotKey));

            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_SUB_HEADING).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_SUB_HEADING,polyglotService.getTranslatedData(polyglotKey));

            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_DESCRIPTION).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_DESCRIPTION,polyglotService.getTranslatedData(polyglotKey));

            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_OKAY_GOT_IT).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_OKAY_GOT_IT,polyglotService.getTranslatedData(polyglotKey));

            Iterator<JsonNode> benefitNodes = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_BENEFITS).elements();
            List<JsonNode> updatedNodes = new ArrayList<>();
            while(benefitNodes.hasNext()) {
                JsonNode benefitNode = benefitNodes.next();
                polyglotKey = benefitNode.get(Constants.BOOK_NOW_TEXT_KEY).textValue();
                ((ObjectNode)benefitNode).put(Constants.BOOK_NOW_TEXT_KEY,polyglotService.getTranslatedData(polyglotKey));
                updatedNodes.add(benefitNode);
            }
        }
    }
}
