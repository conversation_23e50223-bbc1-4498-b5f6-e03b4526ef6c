package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.ErrorCodesCorp;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsApprover;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsRequestor;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ErrorHelper {

    @Autowired
    protected PolyglotService polyglotService;

    public String getSubtitleForError(String subtitle){
        // translation Constants is received in subtitle
        String translatedSubtitle = polyglotService.getTranslatedData(subtitle);
        return StringUtils.isNotEmpty(translatedSubtitle) && !Constants.NULL_STRING.equalsIgnoreCase(translatedSubtitle)?translatedSubtitle:polyglotService.getTranslatedData(ConstantsTranslation.GENERIC_ERROR_SUBTITLE);
    }

    public String getTitleForError(String title){
        // translation Constants is received in title
        String translatedTitle = polyglotService.getTranslatedData(title);
        return StringUtils.isNotEmpty(translatedTitle) && !Constants.NULL_STRING.equalsIgnoreCase(translatedTitle)?translatedTitle:polyglotService.getTranslatedData(ConstantsTranslation.GENERIC_ERROR_TITLE);
    }

    public boolean sendErrorWithTitle(String errorCode, String controller){
        return Constants.CORP_ID_CONTEXT.equalsIgnoreCase(controller) && checkIferrorCodeMatches(errorCode);
    }

    private boolean checkIferrorCodeMatches(String errorcode){
        //add errorcode here to expand the functionality for other errors
        return ErrorCodesCorp.resolve(errorcode);
    }

    public Error getErrorWithTitle(String controller, String errorCode, Error error, String userRole){
        //this function creates the new error for approver/requestor else we return the usual error
        if(Constants.GET_APPROVAL_ENDPOINT.equalsIgnoreCase(controller)){
            if("APPROVER".equalsIgnoreCase(userRole)){
                SpecifiedErrorsApprover specificError = SpecifiedErrorsApprover.resolve(errorCode);
                return specificError!=null ? new Error(error.getCode(), getSubtitleForError(specificError.getSubTitle()), null, getSubtitleForError(specificError.getTitle())): error;
            }else {
                SpecifiedErrorsRequestor specificError = SpecifiedErrorsRequestor.resolve(errorCode);
                return specificError!=null ? new Error(error.getCode(), getSubtitleForError(specificError.getSubTitle()), null, getSubtitleForError(specificError.getTitle())): error;
            }
        }else{
            return error;
        }
    }
}
