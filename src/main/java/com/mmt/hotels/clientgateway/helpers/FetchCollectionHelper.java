package com.mmt.hotels.clientgateway.helpers;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterDetail;
import com.mmt.hotels.clientgateway.request.FetchCollectionRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class FetchCollectionHelper {

    public boolean shouldAddFilters(String filters, FetchCollectionRequest fetchCollectionRequest, Map<String, FilterDetail> landingFilterConditions) {
        if (fetchCollectionRequest != null && fetchCollectionRequest.getRequestDetails() != null) {
            FilterDetail filterDetail = landingFilterConditions.get(filters);
            return isFilterCondition(filterDetail, fetchCollectionRequest.getRequestDetails());
        }
        return false;
    }

    private boolean isFilterCondition(FilterDetail filterDetail, RequestDetails requestDetails) {
        return filterDetail.getFunnelSource().contains(requestDetails.getFunnelSource()) &&
                filterDetail.getPageContext().contains(requestDetails.getPageContext());
    }

}
