package com.mmt.hotels.clientgateway.helpers;

import com.gommt.hotels.orchestrator.detail.enums.*;
import com.gommt.hotels.orchestrator.detail.enums.Currency;
import com.gommt.hotels.orchestrator.detail.enums.TrafficSource;
import com.gommt.hotels.orchestrator.detail.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserSessionData;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.service.OrchDetailService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.UserLocation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.OS;
import static com.mmt.hotels.clientgateway.constants.Constants.USER_CURRENCY;

@Component
public class OrchAlternatePriceHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchAlternatePriceHelper.class);

    @Autowired
    Utility utility;


    public DetailRequest buildAlternatePriceRequest(PriceByHotelsRequestBody cgRequest, Map<String, String[]> parameterMap, Map<String, String> headers, CommonModifierResponse commonModifierResponse) {
        if (cgRequest == null) {
            LOGGER.error("ListingSearchRequest is null");
            throw new IllegalArgumentException("ListingSearchRequest cannot be null");
        }

        DetailRequest orchRequest = new DetailRequest();

        if(cgRequest!=null ) {
            orchRequest.setCouponCount(cgRequest.getCouponCount());
        }
        if(CollectionUtils.isNotEmpty(cgRequest.getGiHotelIds())) {
            orchRequest.setGiHotelId(cgRequest.getGiHotelIds().get(0));
        }
        LocationDetails locationDetails = new LocationDetails();
        locationDetails.setCityId(cgRequest.getCityCode());
        locationDetails.setCountryId(cgRequest.getCountryCode());
        orchRequest.setLocation(locationDetails);

        // Set check-in, check-out, and other parameters
        orchRequest.setCheckIn(cgRequest.getCheckin());
        orchRequest.setCheckOut(cgRequest.getCheckout());

        // Build room criteria (Room Stay Candidates) using converted data
        List<RoomStayCandidate> convertedRoomStayCandidates = convertHesRoomStayCandidatesToClientGateway(cgRequest.getRoomStayCandidates());
        orchRequest.setRooms(buildRoomDetails(convertedRoomStayCandidates, commonModifierResponse.getExpDataMap()));

        // Build dummy required api section
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(MapUtils.isNotEmpty(headers) && headers.containsKey("srcClient") ? headers.get("srcClient") : "");
        orchRequest.setClientDetails(buildClientDetails(cgRequest, deviceDetails, headers, commonModifierResponse));

        // Set experiment data and additional fields
        orchRequest.setExperimentData(cgRequest.getExperimentData());
        orchRequest.setSourceApi("alternate");
        LOGGER.info("Successfully built ListingRequest");
        return orchRequest;
    }

    private List<RoomStayCandidate> convertHesRoomStayCandidatesToClientGateway(List<com.mmt.hotels.model.request.RoomStayCandidate> hesRoomStayCandidates) {
        if (hesRoomStayCandidates == null || hesRoomStayCandidates.isEmpty()) {
            LOGGER.warn("HES RoomStayCandidates is null or empty, returning empty list.");
            return Collections.emptyList();
        }

        List<RoomStayCandidate> clientGatewayRoomStayCandidates = new ArrayList<>();

        for (com.mmt.hotels.model.request.RoomStayCandidate hesCandidate : hesRoomStayCandidates) {
            if (hesCandidate != null) {
                for(GuestCount guestCount : hesCandidate.getGuestCounts()) {
                    RoomStayCandidate cgCandidate = new RoomStayCandidate();
                    // Convert adult count
                    cgCandidate.setAdultCount(Optional.ofNullable(Integer.parseInt(guestCount.getCount())).orElse(0));
                    // Convert child ages
                    cgCandidate.setChildAges(Optional.ofNullable(guestCount.getAges()).orElse(Collections.emptyList()));
                    clientGatewayRoomStayCandidates.add(cgCandidate);
                }
            } else {
                LOGGER.warn("Encountered a null HES RoomStayCandidate, skipping.");
            }
        }

        LOGGER.info("Successfully converted {} HES RoomStayCandidates to ClientGateway format.", clientGatewayRoomStayCandidates.size());
        return clientGatewayRoomStayCandidates;
    }

    private List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> buildRoomDetails(List<RoomStayCandidate> roomStayCandidates, Map<String, String> expDataMap) {
        if (roomStayCandidates == null || roomStayCandidates.isEmpty()) {
            LOGGER.warn("RoomStayCandidates is null or empty, returning an empty RoomDetails list.");
            return Collections.emptyList();
        }

        // Handle distribution logic for SearchRoomsRequest (when expDataMap is provided)
        if (expDataMap != null && utility.isDistributeRoomStayCandidates(roomStayCandidates, expDataMap)) {
            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(roomStayCandidates, expDataMap);
            return roomStayCandidatesHES.stream().map(roomStayCandidate -> {
                com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails roomDetails = new com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails();
                int adultCount = 0;
                List<Integer> childrenAges = new ArrayList<>();
                for (com.mmt.hotels.model.request.GuestCount guestCount : roomStayCandidate.getGuestCounts()) {
                    adultCount = adultCount + Integer.parseInt(guestCount.getCount());
                    if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                        childrenAges.addAll(guestCount.getAges());
                    }
                }
                roomDetails.setAdults(adultCount);
                roomDetails.setChildrenAges(childrenAges);
                return roomDetails;
            }).collect(Collectors.toList());
        }

        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> roomDetailsList = new ArrayList<>();

        // Iterate over room stay candidates and build room details
        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            if (roomStayCandidate != null) {
                com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails roomDetails = new com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails();
                roomDetails.setAdults(roomStayCandidate.getAdultCount());
                roomDetails.setChildrenAges(roomStayCandidate.getChildAges());
                roomDetailsList.add(roomDetails);
            } else {
                LOGGER.warn("Encountered a null RoomStayCandidate, skipping.");
            }
        }

        LOGGER.info("Successfully built RoomDetails for {} rooms.", roomDetailsList.size());
        return roomDetailsList;
    }

    private ClientDetails buildClientDetails(PriceByHotelsRequestBody cgRequest, DeviceDetails deviceDetails, Map<String, String> httpHeaderMap, CommonModifierResponse commonModifierResponse) {
        ClientDetails clientDetails = new ClientDetails();
        com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags featureFlags = new com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags();
        featureFlags.setCityTaxExclusive(true);
        clientDetails.setFeatureFlags(featureFlags);
        clientDetails.setUserDetails(buildUserDetails(cgRequest, commonModifierResponse));
        clientDetails.setRequestDetails(buildRequestDetails(cgRequest.getCorrelationKey(), deviceDetails, httpHeaderMap));
        return clientDetails;
    }

    private UserDetails buildUserDetails(PriceByHotelsRequestBody cgRequest, CommonModifierResponse commonModifierResponse) {
        UserDetails userDetails = new UserDetails();

        // Safely set the location details using LocationDetails builder
        LocationDetails locationDetails = LocationDetails.builder()
                .cityName(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if city is null
                .cityId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if cityId is null
                .countryId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCountry)
                        .orElse(""))  // Default to empty string if countryId is null
                .stateId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getState)
                        .orElse(""))  // Default to empty string if stateId is null
                .build();
        userDetails.setLocation(locationDetails);

        // Safely set mmtAuth, uuid, profileType, and subProfileType with default values if null
        userDetails.setMmtAuth(Optional.ofNullable(commonModifierResponse.getMmtAuth()).orElse(""));
        userDetails.setUuid(Optional.ofNullable(commonModifierResponse.getExtendedUser()).map(ExtendedUser::getUuid).orElse(""));

        UserSessionData userSessionData = new UserSessionData();

        userSessionData.setUuid(userDetails.getUuid());
        userDetails.setSessionData(userSessionData);

        // Safely map profileType and subProfileType, defaulting to UNKNOWN or empty if null
        userDetails.setProfileType(Optional.ofNullable(commonModifierResponse.getExtendedUser())
                .map(ExtendedUser::getProfileType)
                .map(ProfileType::valueOf)
                .orElse(null));  // Default to UNKNOWN if profileType is null

        userDetails.setSubProfileType(Optional.ofNullable(commonModifierResponse.getExtendedUser())
                .map(ExtendedUser::getAffiliateId)
                .map(SubProfileType::fromValue)
                .orElse(SubProfileType.DEFAULT));
        // Default to DEFAULT if subProfileType is null

        // Safely set loggedIn, defaulting to false if null
        userDetails.setLoggedIn(cgRequest.isLoggedIn());

        // Safely set user segments
        List<String> userSegmentsList = new ArrayList<>(Optional.ofNullable(commonModifierResponse.getHydraResponse())
                .map(HydraResponse::getHydraMatchedSegment)
                .orElse(Collections.emptySet()));
        userDetails.setUserSegments(userSegmentsList);
        LOGGER.info("Successfully built UserDetails for UUID: {}", userDetails.getUuid());
        return userDetails;
    }



    private com.gommt.hotels.orchestrator.detail.model.state.RequestDetails buildRequestDetails(String requestId, DeviceDetails deviceDetails, Map<String, String> httpHeaderMap) {
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails requestDetails = new com.gommt.hotels.orchestrator.detail.model.state.RequestDetails();

        requestDetails.setBrand(Brand.GI);
        requestDetails.setFunnelSource(Funnel.ALL);
        requestDetails.setLanguage(Language.ENGLISH);
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setIdContext(IdContext.B2C);
        requestDetails.setRequestType("B2CAgent");
        requestDetails.setChannel("B2Cweb");
        requestDetails.setTrafficType(TrafficType.ALL);
        requestDetails.setPageContext(PageContext.DETAIL);
        requestDetails.setBookingDevice(buildBookingDevice(deviceDetails));
        requestDetails.setRequestId(StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString());

        String currency = MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(USER_CURRENCY) ?
                httpHeaderMap.get(USER_CURRENCY).toUpperCase() : "INR";
        requestDetails.setCurrency(Currency.valueOf(currency));

        String journeyId = MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(VISITOR_ID) ?
                httpHeaderMap.get(VISITOR_ID).toUpperCase() : "";
        requestDetails.setJourneyId(journeyId);

        return requestDetails;
    }

    private BookingDevice buildBookingDevice(DeviceDetails deviceDetails) {
        if (deviceDetails == null) {
            LOGGER.warn("DeviceDetails is null, returning an empty BookingDevice.");
            return BookingDevice.builder().build();  // Return an empty BookingDevice if deviceDetails is null
        }

        BookingDevice.BookingDeviceBuilder bookingDeviceBuilder = BookingDevice.builder();

        // Safely set device fields, defaulting to empty strings or enums if values are null
        bookingDeviceBuilder.deviceId(Optional.ofNullable(deviceDetails.getDeviceId()).orElse(""));
        bookingDeviceBuilder.deviceName(Optional.ofNullable(deviceDetails.getDeviceName()).orElse(""));

        DeviceType deviceType = Optional.ofNullable(deviceDetails.getBookingDevice())
                .map(DeviceType::fromValue)
                .orElse(DeviceType.DESKTOP);
        bookingDeviceBuilder.deviceType(deviceType);

        bookingDeviceBuilder.appVersion(Optional.ofNullable(deviceDetails.getAppVersion()).orElse(""));
        bookingDeviceBuilder.networkType(
                Optional.ofNullable(deviceDetails.getNetworkType())
                        .orElse("")
                        .toUpperCase()
        );
        LOGGER.info("Successfully built BookingDevice with deviceId: {}", deviceDetails.getDeviceId());
        return bookingDeviceBuilder.build();
    }
}