package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class HermesHelper {

    @Autowired
    Utility utility;

    @Value("${hermes.domain}")
    private String domain;

    @Value("${hermes.api.detail.price.url}")
    private String detailPriceUrl;

    @Autowired
    PricingEngineHelper pricingEngineHelper;
    private static final Logger LOGGER = LoggerFactory.getLogger(HermesHelper.class);

    public String getPaxStringFromRoomStay(List<RoomStayCandidate> roomStayCandidates) {
        int totalAdults = utility.getTotalAdultsFromRequest(roomStayCandidates);
        int totalRooms = roomStayCandidates.size();
        int totalChild = utility.getTotalChildrenFromRequest(roomStayCandidates);
        String paxString = String.format("%s-%s-%s", totalRooms, totalAdults, totalChild);
        if (totalChild > 0) {
            List<String> childAgeStringArray = new ArrayList<>();
            roomStayCandidates.forEach(roomStayCandidate -> {
                if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                    String childAge = roomStayCandidate.getChildAges().stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("_"));
                    if (!childAge.trim().isEmpty()) {
                        childAgeStringArray.add(childAge);
                    }
                }
            });
            paxString = String.format("%s-%s", paxString, String.join("_", childAgeStringArray));
        }
        return paxString;
    }

    public String getHermesFlavour(String client) {
        switch (client.toUpperCase()) {
            case "PWA":
                return "mobile";
            case "MSITE":
                return "mobile";
            case "IOS":
                return "ios";
            case "ANDROID":
                return "android";
        }
        return "v3";
    }

    public String getHermesDetailPriceEndPoint(SearchRoomsRequest searchRoomsRequest) {
        String voyagerCityId = searchRoomsRequest.getSearchCriteria().getVcId();
        String checkIn = searchRoomsRequest.getSearchCriteria().getCheckIn();
        String checkOut = searchRoomsRequest.getSearchCriteria().getCheckOut();
        String giHotelId = searchRoomsRequest.getSearchCriteria().getGiHotelId();
        String paxString = utility.getPaxStringFromRoomStay(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());
        String client = searchRoomsRequest.getClient();
        String hermesFlavour = getHermesFlavour(client);

        checkIn = checkIn.replaceAll("-", "");
        checkOut = checkOut.replaceAll("-", "");
        String url= String.format("%s/%s/%s/%s/%s/%s/%s/%s", domain, detailPriceUrl, hermesFlavour,
                voyagerCityId, checkIn, checkOut, paxString, giHotelId);
        url = pricingEngineHelper.appendKeyAndValue(url, "caller", "orch");
        return pricingEngineHelper.appendKeyAndValue(url, Constants.CORRELATIONKEY,searchRoomsRequest.getCorrelationKey());
    }

    public String getHermesComboId(RecommendedCombo cgCombo) {
        SelectRoomRatePlan comboRatePlan;
        try {
            comboRatePlan = cgCombo.getRooms().get(0).getRatePlans().get(0);
            String payMode = comboRatePlan.getPayMode();
            String segmentId = comboRatePlan.getSegmentId();
            String supplierCode = comboRatePlan.getSupplierCode();
            String comboMealPlan = cgCombo.getComboMealPlan();
            return String.format("%s_%s_%s_%s", payMode, segmentId, comboMealPlan, supplierCode);
        } catch (Exception ex) {
            LOGGER.error("Exception while getting hermes combo id: {}", ex.getMessage(), ex);
            return null;
        }
    }
}
