package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.mypartner.MarkUp;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import static com.mmt.hotels.model.response.mypartner.MarkUpType.PERCENTAGE;

@Component
public class PricingEngineHelper {
    private static final Logger logger = LoggerFactory.getLogger(PricingEngineHelper.class);

    @Autowired
    Utility utility;

    public String appendKeyAndValue(String relativeURL, String key, String value) {
        if (StringUtils.isNotBlank(relativeURL)) {
            if (StringUtils.contains(relativeURL, '?')) {
                relativeURL = relativeURL + Constants.AMP;
            } else {
                relativeURL = relativeURL + Constants.QUESTION;
            }
            relativeURL = relativeURL + key.toLowerCase() + Constants.EQUI + value;
        }
        return relativeURL;
    }

    /**
     * Helper function to add PEE in the api url
     * @param relativeURL
     * @param experimentData
     */
    public String appendPEEInUrl(String relativeURL, String experimentData) {
        Map<String, String> experimentDataMap = utility.getExpDataMap(experimentData);
        String value = "F";
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.PEE)) {
            value = experimentDataMap.get(Constants.PEE);
        }
        return appendKeyAndValue(relativeURL, Constants.PEE, value.toUpperCase());
    }

    /**
     * Helper function to add PEED in the api url
     * @param relativeURL
     * @param experimentData
     */
    public String appendPEEDInUrl(String relativeURL, String experimentData) {
        Map<String, String> experimentDataMap = utility.getExpDataMap(experimentData);
        String value = "F";
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.PEED)) {
            value = experimentDataMap.get(Constants.PEED);
        }
        return appendKeyAndValue(relativeURL, Constants.PEED, value.toUpperCase());
    }

    public String appendFilterServiceExpInUrl(String relativeURL, String experimentData) {
        Map<String, String> experimentDataMap = utility.getExpDataMap(experimentData);
        String value = "false";
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.newFilterService)) {
            value = experimentDataMap.get(Constants.newFilterService);
        }
        return appendKeyAndValue(relativeURL, Constants.newFilterService, value.toUpperCase());
    }

    public double getMarkUpForHotels(final MarkUpDetails markUpDetails, final Double finalPriceWithoutTax){
        final boolean isDomestic= Constants.DOMESTIC.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
        if(Objects.nonNull(finalPriceWithoutTax) && finalPriceWithoutTax >0 && markUpDetails!=null && markUpDetails.isMarkupEligible()&& markUpDetails.getMarkupMap()!=null){
            final MarkUp markUp=isDomestic?markUpDetails.getMarkupMap().get("DH"):markUpDetails.getMarkupMap().get("IH");
            if(Objects.nonNull(markUp))
                return PERCENTAGE.equals(markUp.getType())?(markUp.getValue()*finalPriceWithoutTax*.01):markUp.getValue();
        }
        return 0;
    }
}
