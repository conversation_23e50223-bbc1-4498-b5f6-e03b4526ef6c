//package com.mmt.hotels.clientgateway.helpers;
//import com.mmt.hotels.clientgateway.restexecutors.HydraExecutor;
//import com.mmt.hotels.clientgateway.thirdparty.request.*;
//import com.mmt.hotels.clientgateway.thirdparty.response.HydraUserFirstTimeStateResponse;
//import com.mmt.hotels.clientgateway.constants.Constants;
//import com.mmt.hotels.clientgateway.enums.DependencyLayer;
//import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.drools.core.util.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.UUID;
//@Component
//public class HydraHelper {
//    @Autowired
//    private ObjectMapperUtil mapper;
//    @Autowired
//    HydraExecutor hydraExecutor;
//    private static final Logger logger = LoggerFactory.getLogger(HydraHelper.class);
//    public HydraUserStateRequest populateHydraUserStateRequest(HydraUserIdDTO hydraUserDetail, String countryCode,
//                                                               String correlationKey, String couponCode) {
//        HydraUserStateRequest request = null;
//        if (hydraUserDetail == null) {
//            return request;
//        }
//        request = new HydraUserStateRequest();
//        request.setCorrelationKey(correlationKey);
//        request.setUserId(hydraUserDetail);
//        request.setCouponCode(couponCode);
//        List<String> lobs = new ArrayList<String>();
//        if ("in".equalsIgnoreCase(countryCode)) {
//            lobs.add("dh");
//        } else {
//            lobs.add("ih");
//        }
//        request.setLobs(lobs);
//        return request;
//    }
////    public boolean checkUserFirstTimeState(HydraUserStateRequest hydraRequest, int clientFirstTimeUserState) {
////        boolean isUserFirstTime = false;
////        long t1 = System.currentTimeMillis();
////        try {
////            if(hydraRequest.getUserId() != null && (!StringUtils.isEmpty(hydraRequest.getUserId().getUuid())
////                    || !StringUtils.isEmpty(hydraRequest.getUserId().getDeviceId()))){
////                String couponCode = hydraRequest.getCouponCode() == null ? "" : hydraRequest.getCouponCode().toUpperCase();
////                if (clientFirstTimeUserState == 3 || couponCode.contains("FREENIGHT")) {
////                    HydraRequest hydraFeaturesRequest = buildUserStateFeatureRequest(hydraRequest);
////                    logger.debug("HYDRA USER STATE FEATURE REQUEST ={}", mapper.getJsonFromObject(hydraFeaturesRequest, DependencyLayer.CLIENTGATEWAY));
////                    HydraUserFirstTimeStateResponse hydraUserFirstTimeStateResponse =  hydraExecutor.getUserFirstTimeState(hydraFeaturesRequest, UUID.randomUUID().toString());
////                    isUserFirstTime = getUserState(hydraFeaturesRequest, hydraUserFirstTimeStateResponse);
////                }
////                else if (clientFirstTimeUserState == 1 && couponCode.isEmpty()) {
////                    isUserFirstTime = false;
////                } else if (clientFirstTimeUserState == 2 && couponCode.isEmpty()) {
////                    isUserFirstTime = true;
////                }
////            }
////        }
////        catch (Exception ex) {
////            logger.error("Error in checkUserFirstTimeState=", ex);
//////            metricLogger.addToCounter(DependencyLayer.HYDRA, ErrorType.UNEXPECTED,
//////                    RestErrors.UNEXPECTED_ERROR.getErrorCode());
////        }
////        return isUserFirstTime;
////    }
////    public boolean getUserState(HydraRequest hydraFeaturesRequest, HydraUserFirstTimeStateResponse hydraResponse ) {
////        boolean isUserFirstTime = false;
////        if (hydraResponse != null && hydraResponse.getCode() == 200) {
////            isUserFirstTime = true;
////            if (MapUtils.isNotEmpty(hydraResponse.getData())) {
////                HydraEntityWrapper hydraEntityWrapper = hydraFeaturesRequest.getEntities().get(0);
////                String respKey = null;
////                if (CollectionUtils.isNotEmpty(hydraEntityWrapper.getEntity().getDeviceLob())
////                        && org.apache.commons.lang3.StringUtils.isNotEmpty(hydraEntityWrapper.getEntity().getDeviceLob().get(0))) {
////                    respKey = hydraEntityWrapper.getEntity().getDeviceLob().get(0);
////                } else {
////                    respKey = hydraEntityWrapper.getEntity().getUserLob().get(0);
////                }
////                if (MapUtils.isNotEmpty(hydraResponse.getData().get(respKey))) {
////                    isUserFirstTime = false;
////                }
////            }
////        }
////        return isUserFirstTime;
////    }
////    private HydraRequest buildUserStateFeatureRequest(HydraUserStateRequest hydraRequest) {
////        HydraRequest featuresRequest = new HydraRequest();
////        List<HydraEntityWrapper> entities = new ArrayList<HydraEntityWrapper>();
////        HydraEntityWrapper entityWrapr = new HydraEntityWrapper();
////        List<String> features = new ArrayList<>();
////        HydraEntity entity = new HydraEntity();
//////		hydraRequest.getUserId().setUuid("UDEH7FCCOHN");
//////		hydraRequest.getUserId().setProfileType("BUSINESS");
////        if(!StringUtils.isEmpty(hydraRequest.getUserId().getUuid())){
////            List<String> userLobs = new ArrayList<>();
////            userLobs.add(getUserLobKey(hydraRequest.getUserId(),hydraRequest.getLobs().get(0)));
////            entity.setUserLob(userLobs);
////            features.add(Constants.USER_LOB_LAST_BOOKED_TS);
////        }else{
////            List<String> deviceLobs = new ArrayList<>();
////            deviceLobs.add(getDeviceLobKey(hydraRequest.getUserId(),hydraRequest.getLobs().get(0)));
////            entity.setDeviceLob(deviceLobs);
////            features.add(Constants.DEVICE_LOB_LAST_BOOKED_TS);
////        }
////        entityWrapr.setFeatures(features);
////        entityWrapr.setEntity(entity);
////        entities.add(entityWrapr);
////        featuresRequest.setEntities(entities );
////        return featuresRequest;
////    }
////    private String getDeviceLobKey(HydraUserIdDTO hydraUserIdDTO,String lob) {
////        return hydraUserIdDTO.getDeviceId() + "/" + lob;
////    }
////    private String getUserLobKey(HydraUserIdDTO hydraUserIdDTO,String lob) {
////        return hydraUserIdDTO.getUuid() + "/" + hydraUserIdDTO.getProfileType() + "/" + lob;
////    }
//}
