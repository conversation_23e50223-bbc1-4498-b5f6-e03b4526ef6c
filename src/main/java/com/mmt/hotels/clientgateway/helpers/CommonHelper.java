package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierRequest;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.GoCashExpData;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.FeaturestoreConstants;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.SourceRegionSpecificDataConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.corporate.WorkflowInfoResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.restexecutors.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.request.*;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.BookingDevice;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.request.dpt.FeatureStoreRequest;
import com.mmt.hotels.model.request.flyfish.FlyFishReviewsRequest;
import com.mmt.hotels.model.request.flyfish.UserInfoDTO;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.response.dpt.FeatureStoreResponse;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.PredicateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.json.simple.parser.JSONParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.PAGE_CONTEXT_DETAIL;
import static com.mmt.hotels.clientgateway.constants.Constants.PAGE_CONTEXT_REVIEW;
import static com.mmt.hotels.clientgateway.constants.Constants.TRUE;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PER_NIGHT_TITLE_TEXT;

@Component
@PropertySource("classpath:searchAPI.properties")
@PropertySource("classpath:defaultAffiliateId.properties")
public class CommonHelper {

    @Autowired
    private CommonConfigHelper commonConfigHelper;

    @Autowired
    private UserServiceExecutor userServiceExecutor;

    @Autowired
    private PokusExperimentExecutor pokusExperimentExecutor;

    @Autowired
    DataPlatformExecutor dataPlatformExecutor;

    @Autowired
    FeaturestoreHelper featurestoreHelper;

    @Autowired
    private HydraExecutor hydraExecutor;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MobConfigHelper mobConfigHelper;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private Environment env;

    @Autowired
    CorporateExecutor corporateExecutor;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    Utility utility;

    @Value("${hotel.cohort.mob.show}")
    private boolean cohortValid;

    @Value("#{'${hotels.compApps.Dom.list}'.split(',')}")
    private List<String> cohertDom;

    @Value("#{'${hotels.compApps.Intl.list}'.split(',')}")
    private List<String> cohertIntl;

    @Value("#{'${hotels.compApps.list}'.split(',')}")
    private List<String> cohertApps;

    @Value("#{'${non.alt.acco.properties}'.split(',')}")
    private List<String> nonAltAccoProperties;

    @Value("#{'${gi.hydra.user.segments}'.split(',')}")
    private Set<String> hydraSegmentGi;

    @Value("${emi.and.bnpl.detail.enable.gi}")
    private String emiAndBNPLDetailEnableGi;

    @Value("${emi.detail.enable.gi}")
    private String emiDetailEnableGi;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    @Qualifier("userServiceThreadPool")
    private ThreadPoolTaskExecutor userServiceThreadPool;

    @Autowired
    @Qualifier("hydraServiceThreadPool")
    private ThreadPoolTaskExecutor hydraServiceThreadPool;

    @Value("${gi.india.city.mapping}")
    private String cityMappingForIndia;

    private
    Map<String, Boolean> isIndianCity;

    private static final Gson gson = new Gson();

    private static final Logger logger = LoggerFactory.getLogger(CommonHelper.class);

    @PostConstruct
    public void init() {
        isIndianCity = gson.fromJson(cityMappingForIndia, new TypeToken<Map<String, Boolean>>() {
        }.getType());
    }

    public ExtendedUser executePokusAndUserDetails(SearchCriteria searchCriteria, BaseSearchRequest baseSearchRequest,
                                                   String correlationKey, Map<String, String> httpHeaderMap, String mmtAuth, String mcId, CommonModifierResponse commonModifierResponse) {
        CommonModifierRequest commonReq = buildCommonRequest(baseSearchRequest, searchCriteria, mmtAuth, mcId);
        ExtendedUser extendedUser = executePokusAndUserDetails(commonReq, httpHeaderMap, baseSearchRequest.getRequestDetails() != null ? baseSearchRequest.getRequestDetails().getFunnelSource() : Constants.FUNNEL_SOURCE_HOTELS, commonModifierResponse, baseSearchRequest);
        if (MapUtils.isNotEmpty(commonReq.getManthanExpDataMap())) {
            baseSearchRequest.setManthanExpDataMap(commonReq.getManthanExpDataMap());
        }
        return extendedUser;
    }


    public ExtendedUser executePokusAndUserDetails(CommonModifierRequest commonModifierRequest, Map<String, String> httpHeaderMap, String funnelSource, CommonModifierResponse commonModifierResponse, BaseSearchRequest baseSearchRequest) {
        try {
            CountDownLatch countDownLatchAsync = new CountDownLatch(1);
            List<UserServiceResponse> userServiceResponseList = new ArrayList<UserServiceResponse>();
            getUserDetailsAsync(httpHeaderMap, countDownLatchAsync, userServiceResponseList,
                    commonModifierRequest, MDC.getCopyOfContextMap());
            PokusExperimentResponse pokusExperimentResponse = getPokusExperimentResponse(httpHeaderMap, commonModifierRequest, funnelSource, userServiceResponseList);
            baseSearchRequest.setExpDataMap(updateExperimentDataMap(pokusExperimentResponse, commonModifierRequest, funnelSource, commonModifierResponse));
            countDownLatchAsync.await();
            UserServiceResponse userServiceResponse = null;
            if (CollectionUtils.isNotEmpty(userServiceResponseList))
                userServiceResponse = userServiceResponseList.get(0);
            if (userServiceResponse != null && userServiceResponse.getResult() != null) {
                return userServiceResponse.getResult().getExtendedUser();
            }
        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "executePokusAndUserDetails");
            else
                metricErrorLogger.logGeneralException(e, "executePokusAndUserDetails", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
        }
        return null;
    }

    private Map<String, String> updateExperimentDataMap(PokusExperimentResponse pokusExperimentResponse,
                                                        CommonModifierRequest commonModifierRequest, String funnelSource, CommonModifierResponse modifierResponse) throws JsonParseException {
        if (pokusExperimentResponse == null || MapUtils.isEmpty(pokusExperimentResponse.getPerLobMap())) {
            Map<String, String> expDataMap = new HashMap<>();
            return expDataMap;
        }
        PokusAssignVariantResponse hotelLobExp = pokusExperimentResponse.getPerLobMap().get("HOTEL");
        Map<String, String> expData = new HashMap<>();
        Map<String, String> manthanExpData = new HashMap<>();
        Map<String, String> bnplExpData = new HashMap<>();
        for (String key : hotelLobExp.getMetadataValues().keySet()) {
            if (key.startsWith("ddAPI") || key.startsWith("giDisc") || "wallet_exp".equalsIgnoreCase(key) || WalletExpIH.equalsIgnoreCase(key)) {
                manthanExpData.put(key, String.valueOf(hotelLobExp.getMetadataValues().get(key)));
            }
            String gcPreApplyData;
            //Mapping of GI pokus exp to MMT pokus experiment data
            switch (key) {
                case "new_bnpl_kt":
                    bnplExpData.put("new_bnpl_kt", String.valueOf(hotelLobExp.getMetadataValues().get(key)));
                    break;
                case "book_at_zero":
                    bnplExpData.put("book_at_zero", String.valueOf(hotelLobExp.getMetadataValues().get(key)));
                    break;
                case "bnpl_hourly":
                    bnplExpData.put("bnpl_hourly", String.valueOf(hotelLobExp.getMetadataValues().get(key)));
                    break;
                case "gcPreApply":
                    try {
                        gcPreApplyData = new ObjectMapper().writeValueAsString(hotelLobExp.getMetadataValues().get(key));
                        if (StringUtils.isNotEmpty(gcPreApplyData)) {
                            modifierResponse.setGoCashExpData(objectMapperUtil.getObjectFromJson(gcPreApplyData, GoCashExpData.class, DependencyLayer.POKUS));
                        }
                    } catch (Exception e) {
                        if (e instanceof ClientGatewayException)
                            metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "updateExperimentDataMap");
                        else
                            metricErrorLogger.logGeneralException(e, "updateExperimentDataMap", DependencyLayer.CLIENTGATEWAY,
                                    ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
                    }
                    break;
                case "APEINTL_GI":
                    expData.put("APEINTL", String.valueOf(hotelLobExp.getMetadataValues().get(key)));
                    break;
                case "unificationReviewV2":
                    /* Not reading this exp from pokus, directly relying on client exp data*/
                    break;
                case "gi.backend.hotel.default.listing.default.unifiedRankingGRPC":
                    expData.put("mmt.backend.hotel.default.listing.default.unifiedRankingGRPC", String.valueOf(hotelLobExp.getMetadataValues().get(key)));
                    break;
                case "gi.backend.hotel.default.default.default.tcsReviewPageFlowGI":
                    expData.put("mmt.backend.hotel.default.default.default.tcsReviewPageFlow", String.valueOf(hotelLobExp.getMetadataValues().get(key)));
                    break;
                case "grouprates_multiroom":
                    expData.put("mmt.backend.hotel.default.listing.default.groupFunnelEnhancement", String.valueOf(hotelLobExp.getMetadataValues().get(key)));
                    break;
                default:
                    expData.put(key, String.valueOf(hotelLobExp.getMetadataValues().get(key)));
            }
        }
        if(expData.containsKey("ldrsegregation")){
            MDC.put(MDCHelper.MDCKeys.LDRSEGREGATION.getStringValue(), expData.get("ldrsegregation"));
        }
        //Adding default values for GI case -> Keys should be equals to MMT keys
        addDefaultValues(expData, commonModifierRequest, bnplExpData, funnelSource);
        logger.warn("Total pokus experiment data count : {} and manthan experimant data count : {}", expData.size(), manthanExpData.size());
        //This logging is for tracking purpose for pricer v2 one its done we can remove it
        logger.warn("PricerV2 exp data flags before update: pricerV2 : {}, pricerIntlV2: {}", expData.containsKey("pricerV2") && "TRUE".equalsIgnoreCase(String.valueOf(expData.get("pricerV2"))), expData.containsKey("pricerIntlV2") && "TRUE".equalsIgnoreCase(String.valueOf(expData.get("pricerIntlV2"))));
        commonModifierRequest.setManthanExpDataMap(manthanExpData);
        modifierResponse.setVariantKey(hotelLobExp.getVariantKey());
        Map<String, String> existingExpData = new HashMap<>();
        if (commonModifierRequest != null && !StringUtils.isEmpty(commonModifierRequest.getExpData())) {
            String experimentString = commonModifierRequest.getExpData().replaceAll("^\"|\"$", "");
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            //TODO
            existingExpData = new Gson().fromJson(experimentString, type);
        }
        existingExpData.putAll(expData);
        //Adding default ALC experiment is not received from client
        if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
            existingExpData.put(Constants.HOMESTAY_PERSUASION_ALC, "T");
        } else {
            existingExpData.put(Constants.HOMESTAY_PERSUASION_ALC, "F");
        }
        if (MapUtils.isNotEmpty(expData) && (expData.containsKey("pricerV2") || expData.containsKey("pricerIntlV2")) &&
                ("TRUE".equalsIgnoreCase(String.valueOf(expData.get("pricerV2"))) || "TRUE".equalsIgnoreCase(String.valueOf(expData.get("pricerIntlV2"))))) {
            existingExpData.put("pricerV2", "true");
            existingExpData.put("pricerIntlV2", "true");
        } else {
            existingExpData.put("pricerV2", "false");
            existingExpData.put("pricerIntlV2", "false");
        }
        existingExpData.put("unifiedRankingGRPC","true");
        if(PAGE_CONTEXT_LISTING.equalsIgnoreCase(commonModifierRequest.getPageContext()) && FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(funnelSource)){
            existingExpData.put("newListingPerGI","true");
        }
        existingExpData.put("htlInlineEnable","1");
        //EXPERIMENTS TO BE HARDCODED FOR GI
        existingExpData.put("HIS", "DEFAULT");
        existingExpData.put("HAFC","T");
        existingExpData.put("enableMergedPropertyType", "true");
        existingExpData.put("unifiedUserRating", "true");
        // Display all room cards in new select room.
        if (utility.isExperimentOn(existingExpData, SELECT_ROOM_REVAMP_EXP_KEY)) {
            existingExpData.put("sendAllRoomCards", "true");
        }
        // Charity experiments are concluded, so make them hard coded.
        existingExpData.put("charity_new", "true");
        existingExpData.put("DisableDonation", "true");
        return existingExpData;
    }

    private void addDefaultValues(Map<String, String> expData, CommonModifierRequest commonModifierRequest, Map<String, String> bnplExpData, String funnelSource) {
        if (MapUtils.isNotEmpty(expData)) {
//            expData.put("enableMergedPropertyType", "true");
            expData.put("showIndiannness", "true");
            expData.put("isWindowBasedPagination", "true");
            expData.put("unifiedRankingGRPC", "true");
            expData.put("priceSortingFixes", "true");
//            if (StringUtils.equalsIgnoreCase(funnelSource, FUNNEL_SOURCE_HOSTEL) ) {
//                expData.put("PDO", "PPPN");
                expData.put("gi.backend.hotel.default.listing.default.unifiedRankingGRPC", "true");
//            }
            if (commonModifierRequest != null && StringUtils.equalsIgnoreCase(commonModifierRequest.getBookingDevice(), BookingDevice.DESKTOP.name())) {
                expData.put("EMIDT", emiDetailEnableGi);
                expData.put("HSCFS", emiAndBNPLDetailEnableGi);  //enable BNPL with EMI details
            }
            /* BNPL Variant for GI
             * Old Experiment : new_bnpl_kt
             * New Experiment : book_at_zero
             * 1 = no bnpl
             * 2 = is BNPL 1 -> bnpl0 Will Always be false also bnplZeroVariant Will Always be false, bnplNewVariant = true
             * 3 = is bnpl 0 -> bnpl0 = true , bnplZeroVariant = true and bnplNewVariant = false
             */
            if (MapUtils.isNotEmpty(bnplExpData)) {
                expData.put("BNPL", "t");
                expData.put(Constants.EXP_BNPL_NEW_VARIANT, bnplExpData.getOrDefault("new_bnpl_kt", "false"));
                String bnplExp = Constants.FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource) ? bnplExpData.getOrDefault("bnpl_hourly", "0") : bnplExpData.getOrDefault("book_at_zero", "0");
                logger.warn("BNPL Experiments Map : {}, PageContext: {}, FunnelSource: {}", bnplExpData, commonModifierRequest != null ? commonModifierRequest.getPageContext(): "NA", funnelSource);
                switch (bnplExp) {
                    case "2":
                        expData.put("BNPL0", "f");
                        expData.put("bnplZeroVariant", String.valueOf(false));
                        expData.put(Constants.EXP_BNPL_NEW_VARIANT, String.valueOf(true));
                        break;
                    case "3":
                        expData.put("BNPL0", "t");
                        expData.put("bnplZeroVariant", String.valueOf(true));
                        expData.put(Constants.EXP_BNPL_NEW_VARIANT, String.valueOf(false));
                        break;
                }
            }
        }
    }

    private CommonModifierRequest buildCommonRequest(RatePreviewRequest rateRequest, Map<String, String> httpHeaderMap, String pageContext) {
        CommonModifierRequest commonModifierRequest = new CommonModifierRequest();
        commonModifierRequest.setCorrelationKey(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        commonModifierRequest.setRegion(httpHeaderMap.get("REGION"));
        commonModifierRequest.setBookingDevice(rateRequest.getClient());
        commonModifierRequest.setPageContext(pageContext);
        commonModifierRequest.setChannel(rateRequest.getChannel());
        commonModifierRequest.setCorpAuthCode(null);
        commonModifierRequest.setUuid(null);
        commonModifierRequest.setCityCode(rateRequest.getCityCode());
        commonModifierRequest.setLocationId(rateRequest.getCityCode());
        commonModifierRequest.setIdContext(rateRequest.getIdContext());
        commonModifierRequest.setAppVersion(rateRequest.getAppVersion());
        commonModifierRequest.setDeviceId(rateRequest.getDeviceId());
        commonModifierRequest.setExpData("{}");


        return commonModifierRequest;
    }

    private CommonModifierRequest buildCommonRequest(BaseSearchRequest baseSearchRequest, SearchCriteria searchCriteria, String mmtAuth, String mcid) {
        CommonModifierRequest commonModifierRequest = new CommonModifierRequest();
        commonModifierRequest.setMmtAuth(mmtAuth);
        commonModifierRequest.setMcid(mcid);
        commonModifierRequest.setCorrelationKey(baseSearchRequest.getCorrelationKey());
        commonModifierRequest.setRegion(baseSearchRequest.getRequestDetails().getSiteDomain());
        commonModifierRequest.setBookingDevice(baseSearchRequest.getDeviceDetails().getBookingDevice());
        commonModifierRequest.setPageContext(baseSearchRequest.getRequestDetails().getPageContext());
        commonModifierRequest.setChannel(baseSearchRequest.getRequestDetails().getChannel());
        commonModifierRequest.setCorpAuthCode(baseSearchRequest.getRequestDetails().getCorpAuthCode());
        commonModifierRequest.setUuid(baseSearchRequest.getRequestDetails().getUuid());
        commonModifierRequest.setCityCode(searchCriteria.getCityCode());
        commonModifierRequest.setLocationId(searchCriteria.getLocationId());
        commonModifierRequest.setIdContext(baseSearchRequest.getRequestDetails().getIdContext());
        commonModifierRequest.setAppVersion(baseSearchRequest.getDeviceDetails().getAppVersion());
        commonModifierRequest.setDeviceId(baseSearchRequest.getDeviceDetails().getDeviceId());
        commonModifierRequest.setVisitorId(baseSearchRequest.getRequestDetails().getVisitorId());
        commonModifierRequest.setExpData(baseSearchRequest.getExpData());
        commonModifierRequest.setApWindow(getAdvancePurchaseParam(searchCriteria));
        commonModifierRequest.setCurrency(null != searchCriteria.getCurrency() ? searchCriteria.getCurrency() : Constants.DEFAULT_CUR_INR);
        commonModifierRequest.setCountryCode(searchCriteria.getCountryCode());
        if (StringUtils.isNotEmpty(searchCriteria.getVcId()) && StringUtils.isEmpty(searchCriteria.getCountryCode()) && isIndianCity.containsKey(searchCriteria.getVcId())) {
            commonModifierRequest.setCountryCode("IN");
        }
        commonModifierRequest.setTrafficSource(ObjectUtils.isNotEmpty(baseSearchRequest.getRequestDetails().getTrafficSource()) ? baseSearchRequest.getRequestDetails().getTrafficSource().getSource() : "");
        if (StringUtils.isNotEmpty(searchCriteria.getCheckIn())) {
            commonModifierRequest.setCheckInDate(searchCriteria.getCheckIn());
            commonModifierRequest.setLos(dateUtil.getDaysDiff(LocalDate.parse(searchCriteria.getCheckIn()), LocalDate.parse(searchCriteria.getCheckOut())));
        }
        setRoomAndAdultCount(searchCriteria, commonModifierRequest, baseSearchRequest.getRequestDetails().getPageContext());
        setFlyerDetails(commonModifierRequest, baseSearchRequest);
        return commonModifierRequest;
    }

    private void setFlyerDetails(CommonModifierRequest commonModifierRequest, BaseSearchRequest baseSearchRequest) {
        String lob, currentDate, flightBookedDate;
        RequestDetails requestDetails = baseSearchRequest.getRequestDetails();
        if (requestDetails != null && requestDetails.getFlyerInfo() != null) {
            lob = requestDetails.getFlyerInfo().getLob();
            currentDate = requestDetails.getFlyerInfo().getCurrentDate();
            flightBookedDate = requestDetails.getFlyerInfo().getFlightBookedDate();
            // If any of the required values form lob, currentDate, flightBookedDate are absent. Skip initialization.
            if (lob == null || lob.isEmpty() || currentDate == null || currentDate.isEmpty() || flightBookedDate == null || flightBookedDate.isEmpty()) {
                return;
            }

            commonModifierRequest.setCrossLob(lob);
            int flightBookedAP = -1;
            // find differenrce betwen the dates.
            SimpleDateFormat myFormat = new SimpleDateFormat("yyyy-MM-dd");
            try {
                Date date1 = myFormat.parse(currentDate);
                Date date2 = myFormat.parse(flightBookedDate);
                long diff = date1.getTime() - date2.getTime();
                flightBookedAP = (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            } catch (ParseException e) {
                logger.error("Error in processing flyer details from cookie (possible causes may be invalid format recieved from FE) - ", e);
            }
            commonModifierRequest.setFlyerThresholdLoggedout(flightBookedAP);
        }
    }

    private void setRoomAndAdultCount(SearchCriteria searchCriteria, CommonModifierRequest commonModifierRequest, String pageContext) {
        Integer adultCount = 0;
        Integer roomCount = 0;
        if (CollectionUtils.isNotEmpty(searchCriteria.getRoomStayCandidates())) {
            for (RoomStayCandidate roomStayCandidate : searchCriteria.getRoomStayCandidates()) {
                adultCount += roomStayCandidate.getAdultCount();
                roomCount += 1;
            }
        }
        if (PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext) && ((AvailPriceCriteria) searchCriteria) != null && CollectionUtils.isNotEmpty(((AvailPriceCriteria) searchCriteria).getRoomCriteria())) {
            for (AvailRoomsSearchCriteria roomsSearchCriteria : ((AvailPriceCriteria) searchCriteria).getRoomCriteria()) {
                for (RoomStayCandidate roomStayCandidate : roomsSearchCriteria.getRoomStayCandidates()) {
                    adultCount += roomStayCandidate.getAdultCount();
                    roomCount += 1;
                }
            }
        }
        commonModifierRequest.setRoomCount(roomCount);
        commonModifierRequest.setAdultCount(adultCount);
    }


    private PokusExperimentResponse getPokusExperimentResponse(
            Map<String, String> httpHeaderMap, CommonModifierRequest commonModifierRequest, String funnelSource, List<UserServiceResponse> userServiceResponseList) throws ClientGatewayException, ParseException {
        String cityCode = getCityCodeForLocus(commonModifierRequest.getCityCode(), commonModifierRequest.getLocationId());
        PokusExperimentRequest pokusExperimentRequest = new PokusExperimentRequest();
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("city", cityCode);
        attributes.put("contextId", commonModifierRequest.getIdContext());
        attributes.put("idContext", commonModifierRequest.getIdContext());
        attributes.put("funnel", commonModifierRequest.getBookingDevice());
        attributes.put("device_os", getDeviceOS(commonModifierRequest.getBookingDevice()));
        attributes.put("currency", commonModifierRequest.getCurrency());
        attributes.put("region", commonModifierRequest.getRegion());
        attributes.put("TrafficSource", commonModifierRequest.getTrafficSource());
        attributes.put("los", commonModifierRequest.getLos());
        attributes.put("adult", commonModifierRequest.getAdultCount());
        attributes.put("room_count", commonModifierRequest.getRoomCount());
        // Set flyer attributes here. todo find how to get cookie here.
        attributes.put("crossell_lob", commonModifierRequest.getCrossLob());
        attributes.put("flyer_threshold_loggedout", commonModifierRequest.getFlyerThresholdLoggedout());
        if (StringUtils.isNotEmpty(commonModifierRequest.getCheckInDate())) {
            attributes.put("checkindate", changeDateStringToInteger(commonModifierRequest.getCheckInDate()));
        }

        if (StringUtils.isNotEmpty(funnelSource)) {
            attributes.put("funnel_src", getFunnelSource(funnelSource, userServiceResponseList));
        }
        //Modified it as per GI pokus.
        attributes.put("country", commonModifierRequest.getCountryCode());
        if (null != commonModifierRequest.getApWindow()) {
            attributes.put("apWindow", commonModifierRequest.getApWindow());
        }
        if (Constants.ANDROID.equalsIgnoreCase(commonModifierRequest.getBookingDevice()) || Constants.DEVICE_IOS.equalsIgnoreCase(commonModifierRequest.getBookingDevice())) {
            attributes.put("app_version", commonModifierRequest.getAppVersion());
            if (StringUtils.isNotBlank(httpHeaderMap.get("appversion-goibibo")))
                try {
                    attributes.put("app_version_code", Integer.parseInt(httpHeaderMap.get("appversion-goibibo")));
                } catch (Exception e) {
                    logger.debug("Invalid value in APPVERSION-GOIBIBO header", e);
                }
        }
        CollectionUtils.filter(attributes.values(), PredicateUtils.notNullPredicate());
        pokusExperimentRequest.setAttributes(attributes);
        PokusUserRequest pokusUserRequest = new PokusUserRequest();
        String deviceInfo = getDeviceinfo(commonModifierRequest.getBookingDevice(), commonModifierRequest.getDeviceId(), commonModifierRequest.getMcid(), commonModifierRequest.getVisitorId());
        if (StringUtils.isBlank(deviceInfo))
            return null;

        pokusUserRequest.setDeviceId(deviceInfo);
        if (StringUtils.isNotEmpty(httpHeaderMap.get("mmt-auth"))) {
            pokusUserRequest.setMmtAuth(httpHeaderMap.get("mmt-auth"));
        }
        pokusExperimentRequest.setUser(pokusUserRequest);
        if (StringUtils.isNotEmpty(commonModifierRequest.getPageContext())) {
            PokusContextRequest pokusContextRequest = new PokusContextRequest();
            pokusContextRequest.setPageName(commonModifierRequest.getPageContext());
            pokusExperimentRequest.setContext(pokusContextRequest);
        }
        PokusExperimentResponse response = null;
        try {
            response = pokusExperimentExecutor.getPokusExperimentResponse(pokusExperimentRequest, httpHeaderMap, commonModifierRequest.getMmtAuth());
        } catch (Exception e) {
            logger.error("Error in pokus response - ", e);
        }
        return response;
    }


    private int changeDateStringToInteger(String checkInDate) throws ParseException {
        String parsedDate = checkInDate.replace("-", "");
        int dateAsInt = Integer.parseInt(parsedDate);
        return dateAsInt;
    }

    private Integer getAdvancePurchaseParam(SearchCriteria searchCriteria) {
        Integer apWindow = null;
        try {
            if (null != searchCriteria && StringUtils.isNotBlank(searchCriteria.getCheckIn())) {
                String checkin = searchCriteria.getCheckIn().split(" ")[0];
                apWindow = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkin));
            }
        } catch (Exception e) {
            logger.error("Exception while getting apwindow for pokus request", e);
        }
        return apWindow;
    }

    private String getDeviceinfo(String bookingDevice, String deviceId, String mcId, String visitorId) {

        String deviceInfo = "";
        if (Constants.DEVICE_OS_ANDROID.equalsIgnoreCase(bookingDevice) || Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
            deviceInfo = deviceId;
        } else if (StringUtils.isNotEmpty(visitorId)) {
            deviceInfo = visitorId;
        } else {
            deviceInfo = mcId;
        }
        return deviceInfo;
    }

    private String getDeviceOS(String bookingDevice) {
        String deviceOS = null;
        if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)) {
            deviceOS = Constants.DEVICE_OS_ANDROID;
        } else if (Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
            deviceOS = Constants.DEVICE_OS_IOS;
        } else if (Constants.DEVICE_MSITE.equalsIgnoreCase(bookingDevice) || Constants.DEVICE_OS_PWA.equalsIgnoreCase(bookingDevice)) {
            deviceOS = Constants.DEVICE_OS_PWA;
        } else {
            deviceOS = Constants.DEVICE_OS_DESKTOP;
        }
        return deviceOS;
    }

    private String getCityCodeForLocus(String cityCode, String locationId) {
        if (StringUtils.isNotBlank(locationId))
            return locationId;
        return cityCode;
    }

    public void getUserDetailsAsync(Map<String, String> httpHeaderMap,
                                    CountDownLatch countDownLatchAsync, List<UserServiceResponse> userServiceResponseList, CommonModifierRequest commonModifierRequest, Map<String, String> contextMap) {
        try {
//			userServiceThreadPool.submit(() -> { // moving to sync call as pokus needs identifier for mypartner -HTL-36268
            try {
                if (MapUtils.isNotEmpty(contextMap)) {
                    MDC.setContextMap(contextMap);
                }
                UserServiceResponse userServiceResponse = null;

                if (PAGE_CONTEXT_REVIEW.equalsIgnoreCase(commonModifierRequest.getPageContext())) {

                    if (!StringUtils.isBlank(commonModifierRequest.getMmtAuth())) {
                        userServiceResponse = getUserDetails(commonModifierRequest.getMmtAuth(), null, null,
                                commonModifierRequest.getChannel(), commonModifierRequest.getCorrelationKey(), commonModifierRequest.getIdContext(), commonModifierRequest.getRegion(), commonModifierRequest.getUuid(), httpHeaderMap);

                    } else if (StringUtils.isNotBlank(commonModifierRequest.getCorpAuthCode())) {
                        String corpAuthCodeDecoded = null;
                        try {
                            if (commonModifierRequest.getCorpAuthCode().contains("%"))
                                corpAuthCodeDecoded = (URLDecoder.decode(commonModifierRequest.getCorpAuthCode(), "UTF-8"));
                        } catch (Exception ex) {
                            logger.error("error in decoding auth code corp :{}", commonModifierRequest.getCorpAuthCode());
                        }
                        WorkflowInfoResponse workflowInfo = corporateExecutor.getWorkflowInfoByAuthCode(corpAuthCodeDecoded, null, commonModifierRequest.getCorrelationKey());
                        logger.info("workflow response :{}", workflowInfo);
                        if (workflowInfo != null && workflowInfo.getApproverInfo() != null
                                && StringUtils.isNotBlank(workflowInfo.getApproverInfo().getEmailId())) {
                            String emailId = workflowInfo.getApproverInfo().getEmailId();
                            logger.debug("workflow response email :{}", emailId);
                            userServiceResponse = getUserDetails(null, emailId, null, null,
                                    commonModifierRequest.getCorrelationKey(), Constants.CORP_ID_CONTEXT, commonModifierRequest.getRegion(), commonModifierRequest.getUuid(), httpHeaderMap);
                        }
                    }
                } else {
                    userServiceResponse = getUserDetails(commonModifierRequest.getMmtAuth(), null, null, commonModifierRequest.getChannel(), commonModifierRequest.getCorrelationKey(), commonModifierRequest.getIdContext(), commonModifierRequest.getRegion(), commonModifierRequest.getUuid(), httpHeaderMap);
                }

                if (userServiceResponse != null)
                    userServiceResponseList.add(userServiceResponse);
            } catch (Exception e) {
                if (e instanceof ClientGatewayException)
                    metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getUserDetailsAsync");
                else
                    metricErrorLogger.logGeneralException(e, "getUserDetailsAsync", DependencyLayer.CLIENTGATEWAY,
                            ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());

            } finally {
                //	MDC.clear(); //commenting for sync call
                countDownLatchAsync.countDown();
            }
//			});
        } catch (Exception e) {
            metricErrorLogger.logGeneralException(e, "getUserDetailsAsync", DependencyLayer.CLIENTGATEWAY,
                    ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
            countDownLatchAsync.countDown();
        }
    }

    public UserServiceResponse getUserDetails(String mmtAuth, String emailId, String mobile, String channel, String correlationKey, String idContext, String siteDomain, String uuid, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        UserServiceResponse userServiceResponse = userServiceExecutor.getUserServiceResponse(mmtAuth, emailId, mobile, channel, correlationKey, idContext, siteDomain, uuid, httpHeaderMap);

        return userServiceResponse;
    }

    public String getMMTAuth(Map<String, String> httpHeaderMap, String bookingDevice) {
        String mmtAuth = null;
        if ("ANDROID".equalsIgnoreCase(bookingDevice)) {
            String androidAuth = httpHeaderMap.get("backup_auth");
            if (!StringUtils.isEmpty(androidAuth) && androidAuth.indexOf("mmtAuth") > -1)
                mmtAuth = androidAuth.substring(androidAuth.indexOf("mmtAuth") + 9, androidAuth.length() - 1);
        }
        if (StringUtils.isBlank(mmtAuth)) {
            mmtAuth = httpHeaderMap.get("mmt-auth");
        }

        return mmtAuth;
    }

    public void updateCurrencyAndSource(SearchCriteria searchCriteria, RequestDetails requestDetails, Map<String, String> httpHeaderMap) {

        Pair<String, String> countryAndCity = getCountryAndCityCodeFromHeader(httpHeaderMap);
        String countryCode = countryAndCity.getLeft();
        String cityCode = countryAndCity.getRight();

        if (StringUtils.isEmpty(requestDetails.getSrCon()))
            requestDetails.setSrCon(countryCode);
        if (StringUtils.isEmpty(requestDetails.getSrCty()))
            requestDetails.setSrCty(cityCode);

        if (StringUtils.isNotBlank(countryCode)) {
            SourceRegionSpecificDataConfig sourceRegionSpecificDataConfig = new SourceRegionSpecificDataConfig();
            if (mobConfigHelper.getSourceRegionSpecificDataMapping().containsKey(countryCode.toUpperCase())) {
                sourceRegionSpecificDataConfig = mobConfigHelper.getSourceRegionSpecificDataMapping().get(countryCode.toUpperCase());
            } else {
                sourceRegionSpecificDataConfig = mobConfigHelper.getSourceRegionSpecificDataMapping().get(Constants.DEFAULT_DOMAIN);
            }

            if (StringUtils.isEmpty(searchCriteria.getCurrency()))
                searchCriteria.setCurrency(sourceRegionSpecificDataConfig.getCurrency());
        } else {
            if (StringUtils.isEmpty(searchCriteria.getCurrency()))
                searchCriteria.setCurrency(Constants.DEFAULT_CUR_INR);
        }
    }

    public Pair<String, String> getCountryAndCityCodeFromHeader(Map<String, String> httpHeaderMap) {
        String headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI);
        if (StringUtils.isEmpty(headerValue))
            headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI.toLowerCase());
        String countryCode = null;
        String cityCode = null;
        if (!StringUtils.isEmpty(headerValue)) {
            logger.warn("akamai header value : {}", headerValue);
            String[] values = headerValue.split(Constants.COMMA);
            for (String value : values) {
                if (value.contains(Constants.HEADER_COUNTRY_CODE)) {
                    countryCode = value.split(Constants.EQUI)[1];
                }

                if (value.contains(Constants.REGION_CODE)) {
                    cityCode = value.split(Constants.EQUI)[1];
                }
            }
        }
        return Pair.of(countryCode, cityCode);
    }

    public void updateLatLngFromHeader(SearchCriteria searchCriteria, Map<String, String> httpHeaderMap) {
        Pair<Double, Double> countryAndCity = getLatLngFromHeader(httpHeaderMap);
        Double lat = countryAndCity.getLeft();
        Double lng = countryAndCity.getRight();
        if (null != lat && null != lng) {
            searchCriteria.setLat(lat);
            searchCriteria.setLng(lng);
        }
    }

    public Pair<Double, Double> getLatLngFromHeader(Map<String, String> httpHeaderMap) {
        String headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI);
        if (StringUtils.isEmpty(headerValue))
            headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI.toLowerCase());
        Double lat = null;
        Double lng = null;
        try {
            if (!StringUtils.isEmpty(headerValue)) {
                String[] values = headerValue.split(Constants.COMMA);
                for (String value : values) {
                    if (value.contains(Constants.HEADER_LAT)) {
                        lat = Double.valueOf(value.split(Constants.EQUI)[1]);
                    }

                    if (value.contains(Constants.HEADER_LONG)) {
                        lng = Double.valueOf(value.split(Constants.EQUI)[1]);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error while fetching lat lng from akamai header");
        }

        return Pair.of(lat, lng);
    }


    public HydraResponse executeHydraService(String cityCode, String bookingDevice, String correlationKey, String trafficSource, int firstTimeUserState, String countryCode, ExtendedUser extendedUser, Map<String, String> httpHeaderMap, String mcId) {
        try {
            if (Constants.COSMOS.equals(trafficSource) || Constants.CBINTERNAL.equals(trafficSource)) {
                return null;
            }
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            Set<String> matchedSegment = getHydraMatchedSegment(extendedUser, bookingDevice, correlationKey, mcId);
            boolean userFirstTimeState = getUserFirstTimeState(bookingDevice, firstTimeUserState, countryCode, extendedUser, mcId, contextMap, matchedSegment);
            HydraResponse hydraResponse = new HydraResponse();
            hydraResponse.setFlightBooker(false);
            hydraResponse.setFirstUserState(userFirstTimeState);
            hydraResponse.setHydraMatchedSegment(matchedSegment);
            return hydraResponse;
        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "executeHydraService");
            else
                metricErrorLogger.logGeneralException(e, "executeHydraService", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
        }
        return null;
    }

    private boolean getUserFirstTimeState(String bookingDevice, int firstTimeUserState, String countryCode, ExtendedUser extendedUser, String mcId, Map<String, String> contextMap, Set<String> matchedSegment) {
        String hydraDeviceId = null;
        if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)
                || Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
            hydraDeviceId = mcId;
        }

        if (extendedUser != null && StringUtils.isBlank(extendedUser.getUuid()) && StringUtils.isBlank(hydraDeviceId)) {
            return false;
        }
        if (firstTimeUserState == 3) {
            String segmentToCheck = "r1063"; // Intl segment code for more than 1 booking
            if ("IN".equalsIgnoreCase(countryCode))
                segmentToCheck = "r1043";  // Dom segment code for more than 1 booking
            if (CollectionUtils.isNotEmpty(matchedSegment)) {
                for (String segmentId : matchedSegment) {
                    if (segmentId.equalsIgnoreCase(segmentToCheck))
                        return false;
                }
            }
            return true;
        } else if (firstTimeUserState == 1)
            return false;
        else return firstTimeUserState == 2;
    }

    private Set<String> getHydraMatchedSegment(ExtendedUser extendedUser, String bookingDevice, String correlationKey, String mcId) {
        try {
            String hydraDeviceId = null;
            if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)
                    || Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
                hydraDeviceId = mcId;
            }
            HydraUserSegmentRequest hydraUserSegmentRequest = buildUserSegmentRequest(extendedUser, mcId, hydraDeviceId);

            if (hydraUserSegmentRequest == null)
                return null;
            HydraUserSegmentResponse response = hydraExecutor.getHydraMatchedSegment(hydraUserSegmentRequest, correlationKey);
            Set<String> hydraMatchingSegmentCodeSet = null;
            if (response != null) {
                if (response.getCode() == 200 && response.getData() != null) {
                    Set<HydraSegment> hydraRespSegments = response.getData().getSegments();
                    if (!CollectionUtils.isEmpty(hydraRespSegments)) {
                        hydraMatchingSegmentCodeSet = new HashSet<>();
                        for (HydraSegment hydraSegment : hydraRespSegments) {
                            hydraMatchingSegmentCodeSet.add(hydraSegment.getSegmentCode());
                        }
                    }
                }
            }
            return hydraMatchingSegmentCodeSet;
        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getHydraMatchedSegment");
            else
                metricErrorLogger.logGeneralException(e, "getHydraMatchedSegment", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
        }
        return null;
    }

    private HydraUserSegmentRequest buildUserSegmentRequest(ExtendedUser extendedUser, String visitorId, String deviceId) {
        if ((extendedUser == null || StringUtils.isEmpty(extendedUser.getUuid())) && StringUtils.isEmpty(visitorId))
            return null;
        HydraUserSegmentRequest hydraUserSegmentRequest = new HydraUserSegmentRequest();
        hydraUserSegmentRequest.setSegments(hydraSegmentGi);
        hydraUserSegmentRequest.setService("gi_hydra");
        HydraUserIdObject hydraUserIdObject = new HydraUserIdObject();
        hydraUserIdObject.setDeviceId(deviceId);

        if (extendedUser != null) {
            hydraUserIdObject.setProfileType(extendedUser.getProfileType());
            hydraUserIdObject.setUuid(extendedUser.getUuid());
        }
        hydraUserIdObject.setVisitorId(visitorId);
        hydraUserSegmentRequest.setUserId(hydraUserIdObject);
        return hydraUserSegmentRequest;
    }

    @Deprecated
    private boolean isFlightBooker(String bookingDevice, String correlationKey, String cityCode, ExtendedUser extendedUser, String mcId, Map<String, String> contextMap) {
        try {
            MDC.setContextMap(contextMap);
            String hydraDeviceId = null;
            if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)
                    || Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
                hydraDeviceId = mcId;
            }
            boolean validUserId = checkValidUserId(extendedUser, hydraDeviceId);
            if (!validUserId)
                return false;
            HydraRequest hydraRequest = buildFlightBookerRequest(extendedUser, hydraDeviceId);
            HydraLastBookedFlightResponse hydraLastBookedFlightResponse = null;
            hydraLastBookedFlightResponse = hydraExecutor.getHydraLastBookedFlightResponse(hydraRequest, correlationKey);
            return isFlightBooker(hydraLastBookedFlightResponse, cityCode);
        } catch (ClientGatewayException e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException(e, "isFlightBooker");
            else
                metricErrorLogger.logGeneralException(e, "isFlightBooker", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
        } finally {
            MDC.clear();
        }
        return false;
    }

    @Deprecated
    private boolean isFlightBooker(HydraLastBookedFlightResponse hydraLastBookedFlightResponse, String cityCode) {
        List<String> flightCityList = commonConfigHelper.getMapHotelAndFlightCityt().get(StringUtils.isNotBlank(cityCode) ? cityCode.toUpperCase() : "");
        List<HydraLastBookedEntity> lastBookedFlights = new ArrayList<>();

        for (Map.Entry<String, Map<String, HydraLastBookedResponseEntity>> entry : hydraLastBookedFlightResponse.getData().entrySet()) {
            if (MapUtils.isNotEmpty(entry.getValue())) {
                for (Map.Entry<String, HydraLastBookedResponseEntity> lstBkEntry : entry.getValue().entrySet()) {
                    HydraLastBookedResponseEntity hydraLastBookedResponseEntity = lstBkEntry.getValue();
                    lastBookedFlights.add(hydraLastBookedResponseEntity.getValue());
                }
            }
        }

        if (CollectionUtils.isEmpty(lastBookedFlights))
            return false;

        Collections.sort(lastBookedFlights, (lb1, lb2) -> Long.compare(lb2.getDepDateTimeStamp(), lb1.getDepDateTimeStamp()));

        HydraLastBookedEntity lastBookedFlight = lastBookedFlights.get(0);

        String toCity = lastBookedFlight.getToCity();
        long depDateTimeStamp = lastBookedFlight.getDepDateTimeStamp();

        Date pastFlightBookingDate = DateUtils.addDays(new Date(), -commonConfigHelper.getPastFlightBookingDays());
        Date depDate = new Date();
        depDate.setTime(depDateTimeStamp);

        if (depDate.after(pastFlightBookingDate)) {
            if (commonConfigHelper.isSkipFlightCityCheck())
                return true;

            if (StringUtils.isEmpty(toCity))
                return false;

            return flightCityList.contains(toCity.toUpperCase());
        }
        return false;
    }

    public boolean isBusinessUserApplicableFromFeatureStore(ExtendedUser extendedUser) {
        try {
            String userProfile = featurestoreHelper.prepareUserProfile(extendedUser);
            FeatureStoreRequest featureStoreRequest = featurestoreHelper.prepareFeatureStoreRequest(userProfile, null, Arrays.asList(FeaturestoreConstants.BUSINESS_USER_FEATURE));
            FeatureStoreResponse featureStoreResponse = dataPlatformExecutor.getFeatures(featureStoreRequest);
            return featurestoreHelper.processFeatureStoreResponseForBusinessUser(featureStoreResponse, featureStoreRequest);
        } catch (Exception e) {
            logger.error("Error occured while getting business user feature from DPT : {}", e.getMessage());
        }
        return false;
    }


    private boolean checkValidUserId(ExtendedUser extendedUser, String deviceId) {
        if (extendedUser != null && !StringUtils.isEmpty(extendedUser.getUuid()) && !StringUtils.isEmpty(extendedUser.getProfileType()))
            return true;
        return !StringUtils.isEmpty(deviceId) && !Constants.HYDRA_BLOCKED_DEVICEID_LIST.contains(deviceId);
    }

    private HydraRequest buildUserStateFeatureRequest(BaseSearchRequest baseSearchRequest, SearchCriteria searchCriteria, ExtendedUser extendedUser, String hydraDeviceId) {
        HydraRequest featuresRequest = new HydraRequest();
        List<HydraEntityWrapper> entities = new ArrayList<HydraEntityWrapper>();
        HydraEntityWrapper entityWrapr = new HydraEntityWrapper();
        List<String> features = new ArrayList<>();
        HydraEntity entity = new HydraEntity();
        String lob = null;
        if ("in".equalsIgnoreCase(searchCriteria.getCountryCode())) {
            lob = "dh";
        } else {
            lob = "ih";
        }
        if (extendedUser != null && !StringUtils.isEmpty(extendedUser.getUuid())) {
            List<String> userLobs = new ArrayList<>();
            userLobs.add(getUserLobKey(extendedUser.getUuid(), extendedUser.getProfileType(), lob));
            entity.setUserLob(userLobs);
            features.add(Constants.USER_LOB_LAST_BOOKED_TS);

        } else {
            List<String> deviceLobs = new ArrayList<>();
            deviceLobs.add(getDeviceLobKey(hydraDeviceId, lob));
            entity.setDeviceLob(deviceLobs);
            features.add(Constants.DEVICE_LOB_LAST_BOOKED_TS);
        }
        entityWrapr.setFeatures(features);
        entityWrapr.setEntity(entity);
        entities.add(entityWrapr);
        featuresRequest.setEntities(entities);
        return featuresRequest;
    }

    private HydraRequest buildFlightBookerRequest(ExtendedUser extendedUser, String hydraDeviceId) {
        HydraRequest hydraRequest = new HydraRequest();
        List<HydraEntityWrapper> entities = new ArrayList<HydraEntityWrapper>();
        HydraEntityWrapper entityWrapr = new HydraEntityWrapper();
        List<String> features = new ArrayList<>();
        HydraEntity entity = new HydraEntity();
        if (extendedUser != null && !StringUtils.isEmpty(extendedUser.getUuid())) {
            List<String> userLobs = new ArrayList<>();
            userLobs.add(getUserLobKey(extendedUser.getUuid(), extendedUser.getProfileType(), Constants.LOB_DOM_FLIGHTS));
            userLobs.add(getUserLobKey(extendedUser.getUuid(), extendedUser.getProfileType(), Constants.LOB_INTL_FLIGHTS));
            entity.setUserLob(userLobs);
            features.add(Constants.USER_LOB_LAST_BOOKED_SC);
        } else {
            List<String> deviceLobs = new ArrayList<>();
            deviceLobs.add(getDeviceLobKey(hydraDeviceId, Constants.LOB_DOM_FLIGHTS));
            deviceLobs.add(getDeviceLobKey(hydraDeviceId, Constants.LOB_INTL_FLIGHTS));
            entity.setDeviceLob(deviceLobs);
            features.add(Constants.DEVICE_LOB_LAST_BOOKED_SC);
        }
        entityWrapr.setFeatures(features);
        entityWrapr.setEntity(entity);
        entities.add(entityWrapr);
        hydraRequest.setEntities(entities);
        return hydraRequest;
    }

    private String getDeviceLobKey(String deviceId, String lob) {
        return deviceId + "/" + lob;
    }

    private String getUserLobKey(String uuid, String profileType, String lob) {
        return uuid + "/" + profileType + "/" + lob;
    }

    public String getMcId(Map<String, String> httpHeaderMap, String bookingDevice) {
        if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)) {
            return httpHeaderMap.get(Constants.ANDROID_MCID);
        }
        return httpHeaderMap.get(Constants.COMMON_MCID);
    }


    public int getApplicationId(String pageContext) {

        StringBuilder sb = new StringBuilder();
        String key = sb.append(Constants.APPLICATION_ID).append(Constants.DOT)
                .append(pageContext.toUpperCase()).toString();
        if (env.getProperty(key) == null) {
            logger.warn("ApplicationId not found for key {},returning default 410", key);
            return 410; // TODO :- To be reverted
        }
        return Integer.parseInt(env.getProperty(key));

    }


    public CommonModifierResponse processRequest(SearchCriteria searchCriteria, BaseSearchRequest baseSearchRequest, Map<String, String> httpHeaderMap) throws AuthenticationException {
        /* Temp check for OLD CB APIs */
        if (baseSearchRequest != null && baseSearchRequest.getRequestDetails() != null
                && StringUtils.isBlank(baseSearchRequest.getRequestDetails().getSiteDomain())) {
            baseSearchRequest.getRequestDetails().setSiteDomain(httpHeaderMap.get(Constants.REGION));
        }

        //Certain old apps send B2B as idcontext, in order to maintain compatibility, update B2B as CORP"
        if (baseSearchRequest != null && baseSearchRequest.getRequestDetails() != null &&
                Constants.B2B_ID_CONTEXT.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getIdContext())) {
            baseSearchRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
        }
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        updateCurrencyAndSource(searchCriteria, baseSearchRequest.getRequestDetails(), httpHeaderMap);

        /* Update lat lng from akamai header
         * For "nearby getaways" request - there may be no city/country in the request.
         * For such cases - pick lat/long from akamai header. */
        if (null == searchCriteria.getLat() && null == searchCriteria.getLng()) {
            updateLatLngFromHeader(searchCriteria, httpHeaderMap);
        }

		commonModifierResponse.setMmtAuth(getMMTAuth(httpHeaderMap, baseSearchRequest.getDeviceDetails().getBookingDevice()));
		commonModifierResponse.setMcId(getMcId(httpHeaderMap, baseSearchRequest.getDeviceDetails().getBookingDevice()));
		commonModifierResponse.setExtendedUser(executePokusAndUserDetails(searchCriteria, baseSearchRequest, baseSearchRequest.getCorrelationKey(),
				httpHeaderMap, commonModifierResponse.getMmtAuth(), commonModifierResponse.getMcId(), commonModifierResponse));
		commonModifierResponse.setManthanExpDataMap(baseSearchRequest.getManthanExpDataMap()); //Manthan Exp Data Map already set in Method above.
        commonModifierResponse.setFunnelSource(baseSearchRequest.getRequestDetails().getFunnelSource());

        if(baseSearchRequest.getRequestDetails() != null && baseSearchRequest.getRequestDetails().getTrafficSource() != null){
            commonModifierResponse.setTrafficSource(baseSearchRequest.getRequestDetails().getTrafficSource().getSource());
        }

		/*BNPL disable for review logged out user*/
		boolean setExp = false;
		if (baseSearchRequest.getExpDataMap() !=null && baseSearchRequest.getRequestDetails() != null && StringUtils.equalsIgnoreCase(PAGE_CONTEXT_REVIEW,baseSearchRequest.getRequestDetails().getPageContext()) &&
				!baseSearchRequest.getRequestDetails().isLoggedIn() &&
				( baseSearchRequest.getDeviceDetails()!=null  && baseSearchRequest.getDeviceDetails().getBookingDevice() != null &&
						(!baseSearchRequest.getDeviceDetails().getBookingDevice().equalsIgnoreCase(DEVICE_IOS) && !baseSearchRequest.getDeviceDetails().getBookingDevice().equalsIgnoreCase(DEVICE_OS_ANDROID)))) {
			baseSearchRequest.getExpDataMap().put("disableBNPL", FALSE);
			setExp = true;
		}

        //[HTL-44802] Setting unifiedUserRating true for unified user rating API.
        if (baseSearchRequest != null && MapUtils.isNotEmpty(baseSearchRequest.getExpDataMap())) {
            baseSearchRequest.getExpDataMap().put("unifiedUserRating", TRUE);
        }
        //group rates open for only DOM hotels
        if (baseSearchRequest.getRequestDetails() != null &&
                !FUNNEL_SOURCE_HOSTEL.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getFunnelSource())
                        && !FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getFunnelSource())
                &&(StringUtils.equalsIgnoreCase(PAGE_CONTEXT_DETAIL, baseSearchRequest.getRequestDetails().getPageContext())
                || StringUtils.equalsIgnoreCase(PAGE_CONTEXT_LISTING, baseSearchRequest.getRequestDetails().getPageContext()))
                && searchCriteria.getRoomStayCandidates() != null
                && MapUtils.isNotEmpty(baseSearchRequest.getExpDataMap())) {
            boolean groupBooking = utility.isGroupBooking(utility.getPaxStringFromRoomStay(searchCriteria.getRoomStayCandidates()), baseSearchRequest.getExpDataMap(), searchCriteria.getCountryCode());
            if (groupBooking) {
                baseSearchRequest.getRequestDetails().setFunnelSource(Constants.FUNNEL_SOURCE_GROUP_BOOKING);
            }
        }

        ExtendedUser extendedUser = commonModifierResponse.getExtendedUser();

        if (extendedUser != null && CollectionUtils.isNotEmpty(extendedUser.getLoginInfoList())) {
            for (UserLoginInfo loginInfo : extendedUser.getLoginInfoList()) {
                if (Constants.LOGIN_TYPE_MOBILE.equalsIgnoreCase(loginInfo.getLoginType())) {
                    commonModifierResponse.setMobile(loginInfo.getLoginId());
                }
            }
        }

        if (baseSearchRequest.getUserLocation() != null) {
            commonModifierResponse.setUserLocation(baseSearchRequest.getUserLocation());
        } else {
            commonModifierResponse.setUserLocation(buildUserLocationFromHeader(httpHeaderMap));
        }

        if (commonModifierResponse.getExtendedUser() == null ||
                StringUtils.isBlank(commonModifierResponse.getExtendedUser().getUuid())) {
            if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getIdContext()) &&
                    !baseSearchRequest.getRequestDetails().isSeoCorp()) {
//				throw new AuthenticationException(DependencyLayer.USERSERVICE,ErrorType.AUTHENTICATION, AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());

            } else
                baseSearchRequest.getRequestDetails().setLoggedIn(false);
        }

        addFiltersToRemove(baseSearchRequest, commonModifierResponse.getExtendedUser(), searchCriteria);

        if (baseSearchRequest.getCohertVar() != null && !baseSearchRequest.getRequestDetails().isLoggedIn()) {
            baseSearchRequest.getRequestDetails().setLoggedIn(baseSearchRequest.getCohertVar().isLoggedIn());
        }
        String trafficSource = baseSearchRequest.getRequestDetails().getTrafficSource() != null && StringUtils.isNotBlank(baseSearchRequest.getRequestDetails().getTrafficSource().getSource()) ? baseSearchRequest.getRequestDetails().getTrafficSource().getSource().toUpperCase() : "";
        String cityCode = StringUtils.isNotBlank(searchCriteria.getLocationId()) ? searchCriteria.getLocationId() : searchCriteria.getCityCode();
        commonModifierResponse.setHydraResponse(executeHydraService(cityCode, baseSearchRequest.getDeviceDetails().getBookingDevice(),
                baseSearchRequest.getCorrelationKey(), trafficSource, baseSearchRequest.getRequestDetails().getFirstTimeUserState(),
                searchCriteria.getCountryCode(), commonModifierResponse.getExtendedUser(), httpHeaderMap, commonModifierResponse.getMcId()));
        setGoCashExpData(commonModifierResponse, baseSearchRequest);
        if (baseSearchRequest.getRequestDetails() != null && (PAGE_CONTEXT_REVIEW).equalsIgnoreCase(baseSearchRequest.getRequestDetails().getPageContext())) {
            setPricerV2Flag((AvailPriceCriteria) searchCriteria, baseSearchRequest);
            Map<String, String> expDataMap = utility.getExpDataMap(baseSearchRequest.getExpData());
            if(MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(UGCV2) && UGCV2_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(UGCV2))) {
                baseSearchRequest.getExpDataMap().put(UGCV2, UGCV2_TRUE_VALUE);
            }
        }
        convertExpDataToString(baseSearchRequest);

        commonModifierResponse.setFilterRules(commonConfigHelper.getFilterRuleObject());

        String profileType = commonModifierResponse.getExtendedUser() != null ? commonModifierResponse.getExtendedUser().getProfileType() : null;
        commonModifierResponse.setCdfContextId(getCdfContextId(baseSearchRequest.getDeviceDetails().getBookingDevice(), baseSearchRequest.getRequestDetails().getIdContext(), profileType));

        commonModifierResponse.setCategoryRequired("MOBILE".equalsIgnoreCase(baseSearchRequest.getDeviceDetails().getDeviceType()));
        commonModifierResponse.setNumberOfCoupons(getNumberOfCoupons(baseSearchRequest));

        if (null != baseSearchRequest.getRequestDetails().getTrafficSource()) {
            commonModifierResponse.setDomain(commonConfigHelper.getCdfDomainMapping().get(baseSearchRequest.getRequestDetails().getTrafficSource().getSource()) == null ?
                    commonConfigHelper.getCdfDomainMapping().get("DEFAULT") : commonConfigHelper.getCdfDomainMapping().get(baseSearchRequest.getRequestDetails().getTrafficSource().getSource()));
        } else {
            commonModifierResponse.setDomain("B2C");
        }

        if ((StringUtils.isNotBlank(searchCriteria.getCountryCode()) && commonConfigHelper.getConList().contains(searchCriteria.getCountryCode().toUpperCase())) || (StringUtils.isNotBlank(searchCriteria.getCityCode()) && commonConfigHelper.getConList().contains(searchCriteria.getCityCode().toUpperCase()))) {
            commonModifierResponse.setCityTaxExclusive(true);
        }
        commonModifierResponse.setApplicationId(getApplicationId(baseSearchRequest.getRequestDetails().getPageContext()));
        commonModifierResponse.setExpDataMap(buildExpDataMap(baseSearchRequest.getExpData()));
        if (setExp) {
            commonModifierResponse.getExpDataMap().put("disableBNPL", FALSE);
        }
        commonModifierResponse.setBrand((StringUtils.isNotEmpty(baseSearchRequest.getRequestDetails().getBrand()) &&
                Brand.GI.name().equalsIgnoreCase(baseSearchRequest.getRequestDetails().getBrand())) ? Brand.GI.name() : Brand.MMT.name());
        commonModifierResponse.setAppVersionIntGi(baseSearchRequest.getDeviceDetails().getAppVersionIntGi());
        commonModifierResponse.setFlavour(baseSearchRequest.getDeviceDetails().getBookingDevice());
        return commonModifierResponse;
    }

    private void setPricerV2Flag(AvailPriceCriteria searchCriteria, BaseSearchRequest baseSearchRequest) {
        if (searchCriteria != null && CollectionUtils.isNotEmpty(searchCriteria.getRoomCriteria()) && searchCriteria.getRoomCriteria().get(0) != null) {
            String rpc = searchCriteria.getRoomCriteria().get(0).getRatePlanCode();
            if (MapUtils.isEmpty(baseSearchRequest.getExpDataMap())) {
                baseSearchRequest.setExpDataMap(new HashMap<>());
            }
            if (rpc.contains("MSE") || (baseSearchRequest.getRequestDetails() != null &&
                    null != baseSearchRequest.getRequestDetails().getPreApprovedValidity() && baseSearchRequest.getRequestDetails().getPreApprovedValidity() > 0)) {
                baseSearchRequest.getExpDataMap().put("pricerV2", "FALSE");
                baseSearchRequest.getExpDataMap().put("pricerIntlV2", "FALSE");
            } else if (!rpc.contains("MSE")) {
                baseSearchRequest.getExpDataMap().put("pricerV2", "TRUE");
                baseSearchRequest.getExpDataMap().put("pricerIntlV2", "TRUE");
            }
        }
    }

    private void convertExpDataToString(BaseSearchRequest baseSearchRequest) {
        try {
            if (MapUtils.isNotEmpty(baseSearchRequest.getExpDataMap())) {
                baseSearchRequest.setExpData(objectMapperUtil.getJsonFromObject(baseSearchRequest.getExpDataMap(), DependencyLayer.CLIENTGATEWAY));
            }
        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "processRequest");
            else
                metricErrorLogger.logGeneralException(e, "processRequest", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
        }
    }

    private void setGoCashExpData(CommonModifierResponse commonModifierResponse, BaseSearchRequest baseSearchRequest) {
        if (MapUtils.isEmpty(baseSearchRequest.getExpDataMap())) {
            return;
        }
        boolean gcAppliedFlag = false;
        if (commonModifierResponse != null && commonModifierResponse.getGoCashExpData() != null
                && CollectionUtils.isEmpty(commonModifierResponse.getGoCashExpData().getHydraSegList())
                && commonModifierResponse.getGoCashExpData().isApplyForall()) {
            gcAppliedFlag = true;
        } else if (commonModifierResponse != null && commonModifierResponse.getGoCashExpData() != null
                && CollectionUtils.isNotEmpty(commonModifierResponse.getGoCashExpData().getHydraSegList())
                && commonModifierResponse.getGoCashExpData().isApplyForall()) {
            for (String hydraSeg : commonModifierResponse.getGoCashExpData().getHydraSegList()) {
                if (commonModifierResponse.getHydraResponse().getHydraMatchedSegment().contains(hydraSeg)) {
                    gcAppliedFlag = true;
                    break;
                }
            }
        }
        baseSearchRequest.getExpDataMap().put("gocashPreApply", String.valueOf(gcAppliedFlag));
    }

    private LinkedHashMap<String, String> buildExpDataMap(String expData) {
        LinkedHashMap<String, String> experimentDataMap = null;
        try {
            if (StringUtils.isNotBlank(expData)) {
                String experimentString = expData.replaceAll("^\"|\"$", "");
                Type type = new com.google.gson.reflect.TypeToken<LinkedHashMap<String, String>>() {
                }.getType();
                experimentDataMap = new Gson().fromJson(experimentString, type);
            }
        } catch (Exception ex) {
            logger.error("Error while Creating Experiment Data");
        }
        return experimentDataMap;
    }

    public CommonModifierResponse processRequestForBkgMod(Map<String, String> httpHeaderMap, RatePreviewRequest rateRequest, String pageContext) throws AuthenticationException {
        CommonModifierRequest commonModifierRequest = buildCommonRequest(rateRequest, httpHeaderMap, pageContext);

        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMmtAuth(getMMTAuth(httpHeaderMap, commonModifierRequest.getBookingDevice()));
        commonModifierResponse.setMcId(getMcId(httpHeaderMap, commonModifierRequest.getBookingDevice()));
        commonModifierRequest.setMcid(commonModifierResponse.getMcId());
        commonModifierRequest.setVisitorId(commonModifierResponse.getMcId());
        commonModifierRequest.setMmtAuth(commonModifierResponse.getMmtAuth());
        BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
        commonModifierResponse.setExtendedUser(executePokusAndUserDetails(commonModifierRequest, httpHeaderMap, "", commonModifierResponse, baseSearchRequest));
        baseSearchRequest.setExpDataMap(null != baseSearchRequest.getExpDataMap() ? baseSearchRequest.getExpDataMap() : new HashMap<>());
        updatePricerV2Experiments(baseSearchRequest);
        convertExpDataToString(baseSearchRequest);
        commonModifierResponse.setExpData(baseSearchRequest.getExpData());
        ExtendedUser extendedUser = commonModifierResponse.getExtendedUser();

        if (extendedUser != null && CollectionUtils.isNotEmpty(extendedUser.getLoginInfoList())) {
            for (UserLoginInfo loginInfo : extendedUser.getLoginInfoList()) {
                if (Constants.LOGIN_TYPE_MOBILE.equalsIgnoreCase(loginInfo.getLoginType())) {
                    commonModifierResponse.setMobile(loginInfo.getLoginId());
                }
            }
        }

        if (commonModifierResponse.getExtendedUser() == null ||
                StringUtils.isBlank(commonModifierResponse.getExtendedUser().getUuid())) {
            throw new AuthenticationException(DependencyLayer.USERSERVICE, ErrorType.AUTHENTICATION,
                    AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());
        }

        commonModifierResponse.setHydraResponse(executeHydraService(commonModifierRequest.getCityCode(),
                commonModifierRequest.getBookingDevice(), commonModifierRequest.getCorrelationKey(), "",
                1, rateRequest.getCountryCode(), commonModifierResponse.getExtendedUser(), httpHeaderMap,
                commonModifierResponse.getMcId()));

        String profileType = commonModifierResponse.getExtendedUser() != null ? commonModifierResponse.getExtendedUser().getProfileType() : null;
        String idContext = "B2C";
        if (StringUtils.isNotBlank(profileType) && Constants.PROFILE_CORPORATE.equalsIgnoreCase(profileType))
            idContext = "CORP";
        commonModifierRequest.setIdContext(idContext);
        commonModifierResponse.setCdfContextId(getCdfContextId(commonModifierRequest.getBookingDevice(), idContext, profileType));

        commonModifierResponse.setNumberOfCoupons(1);
        commonModifierResponse.setDomain("B2C");

        if (commonConfigHelper.getConList().contains(rateRequest.getCountryCode()) || commonConfigHelper.getConList().contains(commonModifierRequest.getCityCode())) {
            commonModifierResponse.setCityTaxExclusive(true);
        }
        commonModifierResponse.setApplicationId(getApplicationId(pageContext));

        return commonModifierResponse;
    }

    private void updatePricerV2Experiments(BaseSearchRequest baseSearchRequest) {
        baseSearchRequest.getExpDataMap().put(PRICER_V2_EXP, "true");
        baseSearchRequest.getExpDataMap().put(PRICER_V2_INTL_EXP, "true");
    }

    public String getAuthToken(Map<String, String> httpHeaderMap) {
        if (MapUtils.isEmpty(httpHeaderMap)) {
            return null;
        }
        String authToken = null;
        try {
            if (httpHeaderMap.containsKey("backup_auth")) {
                String androidAuth = httpHeaderMap.get("backup_auth");
                if (!StringUtils.isEmpty(androidAuth) && androidAuth.indexOf("mmtAuth") > -1) {
                    authToken = androidAuth.substring(androidAuth.indexOf("mmtAuth") + 9, androidAuth.length() - 1);
                }

            } else {
                authToken = httpHeaderMap.get("mmt-auth");
            }

        } catch (Exception e) {
            logger.error("Error Occured in getAuthTockenHeader=", e);
        }
        if (authToken != null && StringUtils.isEmpty(httpHeaderMap.get("mmt-auth"))) {
            httpHeaderMap.put("mmt-auth", authToken);
        }
        return authToken;
    }

    public String sanitizeInput(String data) {
        if (StringUtils.isNotBlank(data))
            data = data.replaceAll(Constants.XSS_DISALLOWED_CHARACTERS_IN_URL, "");
        return data;
    }


    private int getNumberOfCoupons(BaseSearchRequest baseSearchRequest) {
        int noOfCupon = 0;
        FeatureFlags featureFlags = baseSearchRequest.getFeatureFlags();
        if (featureFlags != null
                && null != baseSearchRequest.getRequestDetails()
                && StringUtils.isNotBlank(baseSearchRequest.getRequestDetails().getPageContext())) {
            switch (baseSearchRequest.getRequestDetails().getPageContext().toUpperCase()) {
                case "LISTING":
                    noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
                            featureFlags.getNoOfCoupons() : 1;
                    break;
                case "DETAIL":
                    noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
                            featureFlags.getNoOfCoupons() : 2;
                    break;
                case "REVIEW":
                    noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
                            featureFlags.getNoOfCoupons() : 3;
                    break;
                case "ALTACCOLANDING":
                    noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
                            featureFlags.getNoOfCoupons() : 1;
                    break;
                case "PAGE_CONTEXT_HOMEPAGE":
                    noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
                            featureFlags.getNoOfCoupons() : 1;
                    break;
                default:
                    noOfCupon = 0;
                    break;
            }
        }
        return noOfCupon;
    }

    private String getCdfContextId(String bookingDevice, String idContext, String profileType) {
        String cdfContextId = null;
        if ("BUSINESS".equalsIgnoreCase(profileType)) {
            cdfContextId = "CORP";
            return cdfContextId;
        }
        if (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice)) {

            return StringUtils.isNotBlank(idContext) ? idContext : Constants.B2C;
        }
        cdfContextId = StringUtils.isNotBlank(profileType) ? "MOB_LI" : Constants.MOB_ID_CONTEXT;
        return cdfContextId;
    }

    private void addFiltersToRemove(BaseSearchRequest baseSearchRequest, ExtendedUser extendedUser, SearchCriteria
            searchCriteria) {
        baseSearchRequest.setFiltersToRemove(new ArrayList<>());
        List<FilterGroup> filterGroupsToRemove = new ArrayList<>();
        if ("US".equalsIgnoreCase(baseSearchRequest.getRequestDetails().getSiteDomain()) || "AE".equalsIgnoreCase(baseSearchRequest.getRequestDetails().getSiteDomain())) {
            filterGroupsToRemove.add(FilterGroup.BLACKDEALS);
            filterGroupsToRemove.add(FilterGroup.EMI);
            filterGroupsToRemove.add(FilterGroup.GLAZE_FILTERS);
        }
        /* GIHTL-15565 CLEAN UP
        if (blockPAHExperimentOn(baseSearchRequest.getExpData())) {
            filterGroupsToRemove.add(FilterGroup.PAY_AT_HOTEL_AVAIL);
            filterGroupsToRemove.add(FilterGroup.PAY_AT_HOTEL);
        }*/
        String profileType = (extendedUser != null && extendedUser.getProfileType() != null) ? extendedUser.getProfileType() : null;
        if ("BUSINESS".equalsIgnoreCase(profileType)) {
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.MMT_Assured));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.MMT_Assured));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.COUPLE_FRIENDLY));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.COUPLE_FRIENDLY));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.STAYCATION));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.STAYCATION));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.GREATVALUE));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.GREATVALUE));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.STAR_RATING, Constants.UNRATED_SR));
        } else {
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.MyBiz_Assured));
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.MyBiz_Assured));
        }

        if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getFunnelSource())
                || Constants.FUNNEL_SOURCE_HOMESTAY_NEW.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getFunnelSource())) {
            if (CollectionUtils.isEmpty(baseSearchRequest.getFilterCriteria())) {
                baseSearchRequest.setFilterCriteria(new ArrayList<>());
            }
//			boolean propCategoryFilterApplied = false;
//			for (Filter filter : baseSearchRequest.getFilterCriteria()) {
//				if (FilterGroup.PROPERTY_CATEGORY.equals(filter.getFilterGroup())) {
//					propCategoryFilterApplied = true;
//					break;
//				}
//			}
//			if (!propCategoryFilterApplied) {
//				Filter homeStayfilter = new Filter(FilterGroup.PROPERTY_CATEGORY, "Alt_Acco_properties");
//				baseSearchRequest.getFilterCriteria().add(homeStayfilter);
//			}
            // Removing the above check so to apply alt_acco_properties filter even if some other property_category filter is applied
            Filter homeStayfilter = new Filter(FilterGroup.ALT_ACCO_PROPERTY, Constants.ALTACCO);
            baseSearchRequest.getFilterCriteria().add(0, homeStayfilter);
            baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.ALT_ACCO_PROPERTY, Constants.ALTACCO));
            if ("IN".equalsIgnoreCase(searchCriteria.getCountryCode())) {
                filterGroupsToRemove.add(FilterGroup.STAR_RATING);
                List<Filter> propTypeFiltersForDomHomestay = getPropTypeFiltersForHomestay();
                baseSearchRequest.getFiltersToRemove().addAll(propTypeFiltersForDomHomestay);
            }
        }
        baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.KID_FRIENDLY));
        baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.KID_FRIENDLY));
        filterGroupsToRemove.add(FilterGroup.VIBE);
        baseSearchRequest.setFilterGroupsToRemove(filterGroupsToRemove);
    }

//    public boolean blockPAHExperimentOn(String expData) {
//        if (StringUtils.isBlank(expData))
//            return false;
//        String experimentString = expData.replaceAll("^\"|\"$", "");
//        Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {
//        }.getType();
//        try {
//            Map<String, String> experimentDataMap = new Gson().fromJson(experimentString, type);
//            if (org.apache.commons.collections.MapUtils.isNotEmpty(experimentDataMap)) {
//                return "TRUE".equalsIgnoreCase(experimentDataMap.get("blockPAH"));
//            }
//        } catch (Exception ex) {
//            logger.error("error building experiment data {}", ex);
//        }
//        return false;
//    }

    private List<Filter> getPropTypeFiltersForHomestay() {
        List<Filter> propTypeFiltersForDomHomestay = new ArrayList<>();
        for (String nonAltAccoProperty : nonAltAccoProperties) {
            propTypeFiltersForDomHomestay.add(new Filter(FilterGroup.PROPERTY_TYPE, nonAltAccoProperty));
            propTypeFiltersForDomHomestay.add(new Filter(FilterGroup.PROPERTY_CATEGORY, nonAltAccoProperty));
            propTypeFiltersForDomHomestay.add(new Filter(FilterGroup.MERGE_PROPERTY_TYPE, nonAltAccoProperty));
        }
        return propTypeFiltersForDomHomestay;
    }

    public String getInboundCurrencyCode(String srcCountry, String destCountry, String deviceType) {
        if (isApplicableForInboundCheck(srcCountry, destCountry, deviceType)) {
            if (commonConfigHelper.getCurrCityMap().containsKey(srcCountry.trim())) {
                return commonConfigHelper.getCurrCityMap().get(srcCountry);
            } else {
                return Constants.DEFAULT_CUR_USD;
            }
        } else {
            return Constants.DEFAULT_CUR_INR;
        }
    }

    private boolean isApplicableForInboundCheck(String srcCountry, String destCountry, String bookingDevice) {
        String srcCountryType = getCountryType(srcCountry);
        String desCountryType = getCountryType(destCountry);
        if (StringUtils.isEmpty(srcCountry) || StringUtils.isEmpty(destCountry)) {
            return false;
        } else if (!commonConfigHelper.getEbableInboundExpDeviceList().contains(bookingDevice)) {
            return false;
        } else
            return !commonConfigHelper.isDisableSrcDesIntlExp() || !"INTL".equalsIgnoreCase(srcCountryType) || !"INTL".equalsIgnoreCase(desCountryType);
    }

    private String getCountryType(String countryNameOrCode) {
        String countryType = null;
        if (countryNameOrCode != null) {
            if ("IN".contentEquals(countryNameOrCode) || "India".equalsIgnoreCase(countryNameOrCode)) {
                countryType = "IN";
            } else {
                countryType = "INTL";
            }
        }
        return countryType;
    }

    public String updateIdContext(String idContext, String client) {
        if (Constants.B2C.equalsIgnoreCase(idContext) &&
                (Constants.ANDROID.equalsIgnoreCase(client) ||
                        Constants.DEVICE_IOS.equalsIgnoreCase(client) ||
                        Constants.DEVICE_MSITE.equalsIgnoreCase(client) ||
                        Constants.DEVICE_OS_PWA.equalsIgnoreCase(client))) {
            return Constants.MOB_ID_CONTEXT;
        }
        return idContext;
    }

    /**
     * It would return true if the input string is json
     *
     * @param inputString
     * @return
     */
    public boolean isJsonString(String inputString) {
        try {
            if (!StringUtils.isEmpty(inputString)) {
                JSONParser parser = new JSONParser();
                parser.parse(inputString);
                return true;
            } else {
                return false;

            }
        } catch (Exception e) {
            return false;
        }
    }

    public void updateReviewsRequest(Map<String, String> httpHeaderMap, FlyFishReviewsRequest flyFishReviewsRequest) {
        String mmtAuth = getAuthToken(httpHeaderMap);
        if (CollectionUtils.isEmpty(flyFishReviewsRequest.getUserInfo())) {
            flyFishReviewsRequest.setUserInfo(new ArrayList<>());
            flyFishReviewsRequest.getUserInfo().add(new UserInfoDTO());
        }
        flyFishReviewsRequest.getUserInfo().get(0).setMmtAuth(mmtAuth);
    }

    public CommonModifierResponse processRequest(WishListedHotelsDetailRequest wishListedHotelsDetailRequest, Map<String, String> httpHeaderMap) throws AuthenticationException {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMmtAuth(getMMTAuth(httpHeaderMap, wishListedHotelsDetailRequest.getDeviceDetails().getBookingDevice()));
        commonModifierResponse.setMcId(getMcId(httpHeaderMap, wishListedHotelsDetailRequest.getDeviceDetails().getBookingDevice()));
        SearchCriteria searchCriteria = new SearchCriteria();
        searchCriteria.setCityCode(wishListedHotelsDetailRequest.getSearchCriteria().getCityCode());
        searchCriteria.setLocationId(wishListedHotelsDetailRequest.getSearchCriteria().getLocationId());
        searchCriteria.setLocationType(wishListedHotelsDetailRequest.getSearchCriteria().getLocationType());
        commonModifierResponse.setExtendedUser(executePokusAndUserDetails(searchCriteria, wishListedHotelsDetailRequest, wishListedHotelsDetailRequest.getCorrelationKey(),
                httpHeaderMap, commonModifierResponse.getMmtAuth(), commonModifierResponse.getMcId(), commonModifierResponse));
        return commonModifierResponse;
    }

    public String getFunnelSource(String funnelSource, List<UserServiceResponse> userServiceResponseList) {
        if (CollectionUtils.isNotEmpty(userServiceResponseList)) {
            UserServiceResponse userServiceResponse = userServiceResponseList.get(0);
            if (userServiceResponse != null && userServiceResponse.getResult() != null) {
                if (userServiceResponse.getResult().getExtendedUser() != null && StringUtils.isNotEmpty(userServiceResponse.getResult().getExtendedUser().getProfileType())) {
                    if (Constants.PROFILE_TYPE_CTA.equalsIgnoreCase(userServiceResponse.getResult().getExtendedUser().getProfileType()) && Constants.MYPARTNER.equalsIgnoreCase(userServiceResponse.getResult().getExtendedUser().getAffiliateId())) {
                        return Constants.MYPARTNER;
                    }
                }
            }
        }
        return funnelSource;
    }

    public boolean checkValidHotel(ListingSearchRequest searchHotelsRequest, SearchWrapperHotelEntity hotelEntity) {
        if (searchHotelsRequest != null && searchHotelsRequest.getMatchMakerDetails() != null) {
            if (CollectionUtils.isEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels())) {
                return true;
            } else {
                String hotelId = null;
                List<InputHotel> hotels = searchHotelsRequest.getMatchMakerDetails().getHotels();
                for (InputHotel inputHotel : hotels) {
                    hotelId = inputHotel.getHotelId();
                }
                return !(StringUtils.isNotEmpty(hotelEntity.getId()) && (StringUtils.isEmpty(hotelId) || hotelEntity.getId().equalsIgnoreCase(hotelId)));
            }
        }
        return false;
    }


    public boolean checkValidHotel(ListingSearchRequest searchHotelsRequest, HotelDetails hotelEntity) {
        if (searchHotelsRequest != null && searchHotelsRequest.getMatchMakerDetails() != null) {
            if (CollectionUtils.isEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels())) {
                return true;
            } else {
                String hotelId = null;
                List<InputHotel> hotels = searchHotelsRequest.getMatchMakerDetails().getHotels();
                for (InputHotel inputHotel : hotels) {
                    hotelId = inputHotel.getHotelId();
                }
                return !(StringUtils.isNotEmpty(hotelEntity.getId()) && hotelEntity.getId().equalsIgnoreCase(hotelId));
            }
        }
        return false;
    }

    public void updateRoomStayCandidate(SearchRoomsRequest searchRoomsRequest) {
        try {
            SearchRoomsCriteria searchCriteria = searchRoomsRequest.getSearchCriteria();
            String paxString = utility.getPaxStringFromRoomStay(searchCriteria.getRoomStayCandidates());
            String hermesPaxString = getHermesPaxString(paxString);
            List<RoomStayCandidate> newRoomStayCandidates = parseRoomStayCandidates(hermesPaxString);
            if (CollectionUtils.isNotEmpty(newRoomStayCandidates)) {
                searchRoomsRequest.getSearchCriteria().setRoomStayCandidates(newRoomStayCandidates);
            }
        } catch (Exception exception) {
            logger.error("Unexpected error in updateRoomStayCandidate for search room: {}", exception.getMessage(), exception);
        }
    }

    public void updateRoomStayCandidate(StaticDetailRequest staticDetailRequest) {
        try {
            StaticDetailCriteria searchCriteria = staticDetailRequest.getSearchCriteria();
            String paxString = utility.getPaxStringFromRoomStay(searchCriteria.getRoomStayCandidates());
            String hermesPaxString = getHermesPaxString(paxString);
            List<RoomStayCandidate> newRoomStayCandidates = parseRoomStayCandidates(hermesPaxString);
            if (CollectionUtils.isNotEmpty(newRoomStayCandidates)) {
                staticDetailRequest.getSearchCriteria().setRoomStayCandidates(newRoomStayCandidates);
            }
        } catch (Exception exception) {
            logger.error("Unexpected error in updateRoomStayCandidate for static details: {}", exception.getMessage(), exception);
        }
    }

    private List<RoomStayCandidate> parseRoomStayCandidates(String occupancyString) {
        if (StringUtils.isEmpty(occupancyString)) {
            logger.error("The occupancy string cannot be null");
            return new ArrayList<>();
        }

        try {
            String[] occupancyDetails = occupancyString.split(Constants.HYPEN);
            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();

            for (String occupancy : Arrays.copyOfRange(occupancyDetails, 1, occupancyDetails.length)) {
                String[] roomOccupants = occupancy.split(Constants.UNDERSCORE);
                if (roomOccupants.length < 2) {
                    logger.error("Invalid occupancy detail: {}", occupancy);
                    return new ArrayList<>();
                }

                int numAdults = Integer.parseInt(roomOccupants[0]);
                List<Integer> childAges = new ArrayList<>();
                for (int i = 2; i < roomOccupants.length; i++) {
                    childAges.add(Integer.parseInt(roomOccupants[i]));
                }

                RoomStayCandidate roomStayCandidate = new RoomStayCandidate(Integer.parseInt(occupancyDetails[0]), numAdults, childAges);
                roomStayCandidates.add(roomStayCandidate);
            }
            return roomStayCandidates;
        } catch (Exception ex) {
            logger.error("Unexpected error in parseRoomStayCandidates", ex);
            return new ArrayList<>();
        }
    }


    private String getHermesPaxString(String paxString) {
        if (StringUtils.isEmpty(paxString)) {
            logger.error("paxString cannot be null or empty");
            return null;
        }
        String[] paxList = paxString.split(Constants.ROOM_SEPARATOR);
        if (paxList.length < 3) {
            logger.error("Input must contain at least 3 fields separated by hyphen");
            return null;
        }

        try {
            int rooms = Integer.parseInt(paxList[0]);
            int adults = Integer.parseInt(paxList[1]);
            int children = Integer.parseInt(paxList[2]);
            String[] childrenAges = new String[0];
            if (children > 0 && paxList.length > 3) {
                childrenAges = paxList[3].split(Constants.OCCUPANCY_SEPARATOR);
            }

            if (rooms > adults) {
                rooms = adults;
            }
            int adultsPerRoom = adults / rooms;
            int childPerRoom = children / rooms;
            int remAdults = adults % rooms;
            int remChild = children % rooms;

            StringBuilder pax = new StringBuilder(paxList[0]);
            List<Integer> adultsList = new ArrayList<>();
            List<Integer> childList = new ArrayList<>();
            for (int i = 0; i < rooms; i++) {
                if (remAdults > 0) {
                    adultsList.add(adultsPerRoom + 1);
                    remAdults--;
                } else {
                    adultsList.add(adultsPerRoom);
                }

                if (remChild > 0) {
                    childList.add(childPerRoom + 1);
                    remChild--;
                } else {
                    childList.add(childPerRoom);
                }
            }

            for (int i = 0; i < rooms; i++) {
                int adultsCurr = adultsList.get(i);
                int childCurr = childList.get(childList.size() - i - 1);
                pax.append("-").append(adultsCurr).append("_").append(childCurr);

                for (int j = 0; j < childCurr; j++) {
                    if (childrenAges.length > j) {
                        pax.append("_").append(childrenAges[j]);
                    }
                }

                if (childCurr <= childrenAges.length) {
                    childrenAges = Arrays.copyOfRange(childrenAges, childCurr, childrenAges.length);
                }
            }

            String result = pax.toString();
            if (result.isEmpty()) {
                logger.warn("Unexpected empty output from getHermesPax");
            }
            return result;
        } catch (Exception ex) {
            logger.error("Unexpected error in getHermesPaxString", ex);
            return null;
        }
    }

    public UserLocation buildUserLocationFromHeader(Map<String, String> httpHeaderMap) {

        Pair<String, String> countryAndCity = getCountryAndCityCodeFromHeader(httpHeaderMap);
        String country = countryAndCity.getLeft();
        String state = countryAndCity.getRight();
        String city = getCityFromHeader(httpHeaderMap);
        UserLocation userLocation = new UserLocation();
        userLocation.setCity(city);
        userLocation.setCountry(country);
        userLocation.setState(state);
        return userLocation;
    }

    public String getFlavour(String deviceType) {
        //for Apps deviceType is the flavour, for DT v3 is flavour and for PWA mobile is flavour
        switch (deviceType.toLowerCase()) {
            case "android":
            case "ios":
                return deviceType.toLowerCase();
            case "desktop":
                return "v3";
            default:
                return "mobile";
        }
    }

    private String getCityFromHeader(Map<String, String> httpHeaderMap) {
        String headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI);
        if (StringUtils.isEmpty(headerValue))
            headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI.toLowerCase());
        String city = null;
        if (!StringUtils.isEmpty(headerValue)) {
            String[] values = headerValue.split(Constants.COMMA);
            for (String value : values) {
                if (value.contains(Constants.HEADER_CITY)) {
                    city = value.split(Constants.EQUI)[1];
                }
            }
        }
        return city;
    }

    public String buildPerNightPriceDescription(int totalRoomCount) {
        String priceDescription = polyglotService.getTranslatedData(PER_NIGHT_TITLE_TEXT);
        String roomDesc = totalRoomCount > 0 ? (totalRoomCount == 1 ? " room" : " rooms") : StringUtils.EMPTY;
        return StringUtils.isEmpty(roomDesc) ? null : MessageFormat.format(priceDescription, totalRoomCount + roomDesc);
    }
}
