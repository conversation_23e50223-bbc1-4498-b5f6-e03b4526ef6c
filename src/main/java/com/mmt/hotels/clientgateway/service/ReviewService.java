package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.*;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.BNPLDetails;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.TotalPriceResponse;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.response.ugc.*;
import com.mmt.hotels.clientgateway.transformer.response.HCouponApplicableTextCreator;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponseData;
import com.mmt.hotels.clientgateway.restexecutors.*;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.flyfish.UgcReviewRequest;
import com.mmt.hotels.model.response.flyfish.UgcSummaryRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartRequest;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;


@Component
public class ReviewService {

    @Autowired
    private AvailRoomsFactory availRoomsFactory;

    @Autowired
    private AvailRoomsExecutor availRoomsExecutor;
    
    @Autowired
    private SearchRoomsExecutor searchRoomsExecutor;

    @Autowired
    private ReviewExecutor reviewExecutor;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    UserServiceExecutor userServiceExecutor;
    
    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private Utility utility;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    StreaksExecutor streaksExecutor;

    @Autowired
    @Qualifier("reviewServiceThreadPool")
    private ThreadPoolTaskExecutor reviewServiceThreadPool;

    private String rewardIconUrl;
    private int reviewLevel1MaxAmount;
    private int reviewLevel1MaxPer;
    private int reviewLevel1Count;

    private int reviewLevel2MaxAmount;
    private int reviewLevel2MaxPer;
    private int reviewLevel2Count;

    private int reviewLevel3MaxAmount;
    private int reviewLevel3MaxPer;
    private int reviewLevel3Count;

    private static final Logger logger = LoggerFactory.getLogger(ReviewService.class);


    @PostConstruct
    public void init() {
        rewardIconUrl = commonConfigConsul.getRewardIconUrl();
        reviewLevel1Count = commonConfigConsul.getReviewLevel1Count();
        reviewLevel1MaxPer = commonConfigConsul.getReviewLevel1MaxPer();
        reviewLevel1MaxAmount = commonConfigConsul.getReviewLevel1MaxAmount();

        reviewLevel2Count = commonConfigConsul.getReviewLevel2Count();
        reviewLevel2MaxPer = commonConfigConsul.getReviewLevel2MaxPer();
        reviewLevel2MaxAmount = commonConfigConsul.getReviewLevel2MaxAmount();

        reviewLevel3Count = commonConfigConsul.getReviewLevel3Count();
        reviewLevel3MaxPer = commonConfigConsul.getReviewLevel3MaxPer();
        reviewLevel3MaxAmount = commonConfigConsul.getReviewLevel3MaxAmount();
    }
    public AvailRoomsResponse availRooms(AvailRoomsRequest availRoomsRequest,  Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
        	String request = objectMapperUtil.getJsonFromObject(availRoomsRequest, DependencyLayer.CLIENTGATEWAY);
            logger.warn("Client Request for avail rooms:- " + request);
            long startTime = System.currentTimeMillis();
            int totalCandidates=0;
        	CommonModifierResponse commonModifierResponse = commonHelper.processRequest(availRoomsRequest.getSearchCriteria(), availRoomsRequest, httpHeaderMap);

            parameterMap = utility.addFunnelSourceToParameterMap(availRoomsRequest.getRequestDetails().getFunnelSource(), parameterMap, null);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_REVIEW_COMMON_REQUEST_PROCESS, REVIEW_AVAIL_ROOMS, System.currentTimeMillis() - startTime);
            getStreaksUserInfoResponse(commonModifierResponse, availRoomsRequest.getCorrelationKey(), parameterMap, httpHeaderMap);
            PriceByHotelsRequestBody priceByHotelsRequestBody = availRoomsFactory.
                    getRequestService(availRoomsRequest.getClient()).convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);

            startTime = new Date().getTime();
            try {
                RoomDetailsResponse roomDetailsResponse = availRoomsExecutor.availRooms(priceByHotelsRequestBody, parameterMap, httpHeaderMap);

                //availRoomsRequest.updateExpDataAsPerRecom()
                Map<String, String> expData = availRoomsRequest.getExpDataMap();
                if (expData != null) {
                    expData.put("recomFlow", Utility.availRecomValue(roomDetailsResponse));
                }
                availRoomsRequest.setExpDataMap(expData);
                CountDownLatch countDownLatchAsync = new CountDownLatch(2);
                Future<HotelsRoomInfoResponseEntity> hotelsRoomInfoResponseEntityFuture  = searchRoomsExecutor.getRoomStaticDetails(availRoomsRequest.getSearchCriteria().getHotelId(), availRoomsRequest.getSearchCriteria().getVcId(), priceByHotelsRequestBody.getBrand(), totalCandidates, availRoomsRequest.getCorrelationKey(), parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_REVIEW,availRoomsRequest,availRoomsRequest.getSearchCriteria().getLocationId());
                Future<HotelImage> hotelImageFuture = availRoomsExecutor.getHotelImages(availRoomsRequest, parameterMap, httpHeaderMap, Constants.PAGE_CONTEXT_REVIEW, countDownLatchAsync);
                countDownLatchAsync.await();
                HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = hotelsRoomInfoResponseEntityFuture != null ? hotelsRoomInfoResponseEntityFuture.get() : new HotelsRoomInfoResponseEntity();
                HotelImage hotelImage = hotelImageFuture!=null ? hotelImageFuture.get() : null;

                String siteDomain = (availRoomsRequest.getRequestDetails() != null) ? availRoomsRequest.getRequestDetails().getSiteDomain() : Constants.DEFAULT_SITE_DOMAIN;
                String checkIn = (availRoomsRequest.getSearchCriteria() != null) ? availRoomsRequest.getSearchCriteria().getCheckIn() : StringUtils.EMPTY;
                String checkOut = (availRoomsRequest.getSearchCriteria() != null) ? availRoomsRequest.getSearchCriteria().getCheckOut() : StringUtils.EMPTY;
                String funnelSource = (availRoomsRequest.getRequestDetails() != null) ? availRoomsRequest.getRequestDetails().getFunnelSource() : StringUtils.EMPTY;
                boolean allInclusions = availRoomsRequest.getFeatureFlags() != null && availRoomsRequest.getFeatureFlags().isAllInclusions();
                boolean showBnplCard = Utility.isShowBnplCard(availRoomsRequest.getFeatureFlags());
                boolean showLuckyTimer = availRoomsRequest.getSearchCriteria()!=null && availRoomsRequest.getSearchCriteria().isLuckyProperty();
                boolean isCheckUpgrade = availRoomsRequest.getSearchCriteria()!=null && availRoomsRequest.getSearchCriteria().isCheckUpgrade();
                String selectedRoomCode = availRoomsRequest.getSearchCriteria()!=null && CollectionUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getRoomCriteria()) ? availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomCode() : StringUtils.EMPTY;
                String selectedRoomType = availRoomsRequest.getSearchCriteria()!=null && CollectionUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getRoomCriteria()) ? availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRatePlanType() : StringUtils.EMPTY;

                return availRoomsFactory.getResponseService(availRoomsRequest.getClient())
                        .convertAvailRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, siteDomain, checkIn, checkOut, availRoomsRequest.getExpDataMap(), allInclusions,
                                funnelSource, commonModifierResponse, showBnplCard, hotelImage, priceByHotelsRequestBody.getGiHotelId(), showLuckyTimer, isCheckUpgrade, selectedRoomCode, selectedRoomType);
            } catch (Exception ex) {
                throw ex;
            } finally {
                metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "availRoomsTotal", new Date().getTime() - startTime);
            }

        }catch (Throwable e) {
        	if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch availRooms: " + e.getMessage());
        		logger.debug("error occured in fetch availRooms: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch availRooms: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String getAvailPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try{
            AvailRoomsRequest availRoomsRequest = oldToNewerRequestTransformer.updateAvailRoomsRequest(priceByHotelsRequestBody);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(availRoomsRequest.getSearchCriteria(), availRoomsRequest, httpHeaderMap);
            PriceByHotelsRequestBody priceByHotelsRequestBodyModified = availRoomsFactory
                    .getRequestService(availRoomsRequest.getClient())
                    .convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
            return availRoomsExecutor.availRoomsOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);
        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
        	else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch availPriceOld: " + e.getMessage());
        		logger.debug("error occured in fetch availPriceOld: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch availPriceOld: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }
    
    public TotalPriceResponse getTotalPrice(TotalPricingRequest getTotalPriceRequest, Map<String,String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap, String client) throws ClientGatewayException {
    	TotalPriceResponse totalPriceResponse;
    	try {
        	String request = objectMapperUtil.getJsonFromObject(getTotalPriceRequest, DependencyLayer.CLIENTGATEWAY);
            logger.warn("Client Request for Total Pricing:- " + request);
    		TotalPricingResponse totalPricingResponseOld = availRoomsExecutor.getTotalPricingDetails(getTotalPriceRequest, parameterMap, correlationKey, httpHeaderMap);
    		getTotalPriceRequest.setClient(client);
            totalPriceResponse =  availRoomsFactory.getResponseService(client).convertTotalPricingResponse(totalPricingResponseOld, getTotalPriceRequest, getTotalPriceRequest.getCountryCode());
            if (CollectionUtils.isEmpty(totalPricingResponseOld.getHydraSegments()) &&
                    !client.equalsIgnoreCase(Constants.DEVICE_IOS) && !client.equalsIgnoreCase(Constants.DEVICE_OS_ANDROID)) {
                // If LoggedOut Bnpl experiment is true, we send all the bnpl details
                if (totalPricingResponseOld.getLoggedOutBnplWebExp()) {
                    if (totalPriceResponse.getBnplDetails() != null) totalPriceResponse.getBnplDetails().setLoggedOutBnplWebExp(true);
                } else {
                    BNPLDetails bnplDetails = new BNPLDetails();
                    bnplDetails.setBnplVariant(totalPriceResponse.getBnplDetails() != null ? totalPriceResponse.getBnplDetails().getBnplVariant() : BNPLVariant.BNPL_NOT_APPLICABLE.toString());
                    totalPriceResponse.setBnplDetails(bnplDetails);
                }
            }
            updateCouponsWithApplicabilityText(totalPriceResponse);
        }catch (Throwable e) {
        	if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch totalPrice: " + e.getMessage());
        		logger.debug("error occured in fetch totalPrice: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch totalPrice: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return totalPriceResponse;
    }

    private void updateCouponsWithApplicabilityText(TotalPriceResponse totalPriceResponse) {
        if( totalPriceResponse.getTotalPricing()!=null){
            List<Coupon> couponsList = totalPriceResponse.getTotalPricing().getCoupons();
            if (couponsList != null && !couponsList.isEmpty()) {
                HCouponApplicableTextCreator couponApplicableTextCreator = new HCouponApplicableTextCreator();
                couponsList.forEach(coupon -> {
                    String applicabilityText = couponApplicableTextCreator.getText(totalPriceResponse.getBnplDetails(), coupon.isGiftCardAllowed(), coupon.isBnplAllowed(), polyglotService);
                    coupon.setCouponApplicabilityText(applicabilityText);
                });
            }
        }
    }
    
    public String getUpdatedPriceOccuLessOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try{
            AvailRoomsRequest availRoomsRequest = oldToNewerRequestTransformer.updateAvailRoomsRequest(priceByHotelsRequestBody);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(availRoomsRequest.getSearchCriteria(), availRoomsRequest, httpHeaderMap);
            PriceByHotelsRequestBody priceByHotelsRequestBodyModified = availRoomsFactory
                    .getRequestService(availRoomsRequest.getClient())
                    .convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
            return availRoomsExecutor.updatedPriceOccuLessOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);
        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
        	else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch updatedPriceOccupancyLessOld: " + e.getMessage());
        		logger.debug("error occured in fetch updatedPriceOccupancyLessOld: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch updatedPriceOccupancyLessOld: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public PayLaterEligibilityResponse fetchPayLaterEligibility(PayLaterEligibilityRequest request,String correlationKey, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String client) throws ClientGatewayException {
        try {
            String mobile = null;
            String mmtAuth = commonHelper.getMMTAuth(httpHeaderMap, client);
            httpHeaderMap.put("mmt-auth",mmtAuth);
            UserServiceResponse userServiceResponse = userServiceExecutor.getUserServiceResponse(mmtAuth, null, null, null, correlationKey, "IN", null, null, httpHeaderMap);
            if (null != userServiceResponse
                    && null != userServiceResponse.getResult()
                    && null != userServiceResponse.getResult().getExtendedUser()
                    && null != userServiceResponse.getResult().getExtendedUser().getLoginInfoList()) {
                mobile = userServiceResponse.getResult().getExtendedUser().getLoginInfoList().stream().filter(e -> "MOBILE".equalsIgnoreCase(e.getLoginType())).findFirst().get().getLoginId();
            }
            if (StringUtils.isNotEmpty(mobile) && StringUtils.isNotEmpty(request.getTxnKey())) {
                request.setMobileNumber(mobile);
                com.mmt.hotels.model.response.PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(request, parameterMap, correlationKey, httpHeaderMap);
                return availRoomsFactory.getResponseService(client).convertPayLaterEligibilityResponse(client, response,request.isMemoize());
            } else {
                throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, ValidationErrors.INVALID_PAY_LATER_REQUEST.getErrorCode(), ValidationErrors.INVALID_PAY_LATER_REQUEST.getErrorMsg());
            }
        } catch (Throwable e) {
            logger.error("error occured in paylater eligibility: ", e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private void getStreaksUserInfoResponse(CommonModifierResponse commonModifierResponse, String correlationKey, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) {
        try{
            StreaksUserInfoResponse streaksUserInfoResponse = utility.getUserInfoResponse(commonModifierResponse.getExtendedUser() != null ? commonModifierResponse.getExtendedUser().getUuid(): "",
                    commonModifierResponse.getExpDataMap(), correlationKey, parameterMap, httpHeaderMap);
            commonModifierResponse.setStreaksUserInfoResponse(streaksUserInfoResponse);
        }catch (Exception e){
            logger.error("Exception occured for streaks processing : {}",e.getMessage(),e);
        }
    }

    public UgcReviewResponseData getUgcReviewsFromHes(UgcReviewRequest ugcReviewRequest, String ck) throws ClientGatewayException {
        try {
            long startTime = System.currentTimeMillis();
            UgcReviewResponseData ugcReviewResponseData = reviewExecutor.getReviewData(ugcReviewRequest, ck);
            metricAspect.addToTime(DependencyLayer.CLIENTGATEWAY.name(), "getUgcReviewsFromHes", System.currentTimeMillis() - startTime);
            return ugcReviewResponseData;
        } catch (Exception e) {
            logger.error("Exception occured in getUgcReviewsFromHes : {}", e.getMessage(), e);
            return null;
        }
    }

    public UGCPlatformReviewSummaryDTO getUgcSummaryFromHes(UgcSummaryRequest ugcSummaryRequest)
    {
        try {
            long startTime = System.currentTimeMillis();
            UGCPlatformReviewSummaryDTO ugcPlatformReviewSummaryDTO = reviewExecutor.getUgcSummary(ugcSummaryRequest);
            metricAspect.addToTime(DependencyLayer.CLIENTGATEWAY.name(), "getUgcSummary", System.currentTimeMillis() - startTime);
            return ugcPlatformReviewSummaryDTO;
        } catch (Exception e) {
            logger.error("Exception occured in getUgcSummary : {}", e.getMessage(), e);
            return null;
        }
    }

    public ClientUgcResponse loadProgram(ClientLoadProgramRequest request, Map<String, String[]> parameterMap, String correlationKey, String client, Map<String, String> httpHeaderMap){
        try {
            logger.debug("Fetching loadProgram answer for request: {}, {}", correlationKey, request);
            UgcResponse result = reviewExecutor.fetchLoadProgram(request, parameterMap, httpHeaderMap);
            logger.debug("Received response for loadProgram for request: {}, {}", correlationKey, result);
            ClientUgcResponse ugcResponse = new ClientUgcResponse();
            UgcResponseClientUgcResponseMapper(result, ugcResponse, client);
            return ugcResponse;
        } catch (Exception e) {
            logger.error("Exception occured in loadProgram : {}", e.getMessage(), e, correlationKey);
            return getErrorResponseForClient(e);
        }
    }

    public ClientUgcResponse submitAnswers(ClientSubmitApiRequest request, MultipartRequest multipartRequest, String correlationKey) {
        try {
            logger.info("Submitting answer for request: {}, {}", request, correlationKey);
            List<ImageUploadResult> imageUploadResults = uploadImageToPlatformsS3(multipartRequest, correlationKey);
            UgcResponse result = reviewExecutor.submitAnswersToPlatforms(request, imageUploadResults, correlationKey);
            logger.info("Received response for submit answer for request: {}, {}", correlationKey, result);
            ClientUgcResponse clientUgcResponse = new ClientUgcResponse();
            UgcResponseClientUgcResponseMapper(result, clientUgcResponse, request.getClient());
            return clientUgcResponse;
        } catch (Exception e) {
            logger.error("Error in submitAnswers for: {} ", correlationKey, e);
            return getErrorResponseForClient(e);
        }
    }

    public void UgcResponseClientUgcResponseMapper(UgcResponse ugcResponse, ClientUgcResponse clientUgcResponse, String source) {
        try {
            if (ugcResponse == null || ugcResponse.getQuestionData() == null) {
                clientUgcResponse.setSuccess(false);
                return;
            }
            QuestionData questionData = ugcResponse.getQuestionData();
            ResponseData data = new ResponseData();
            Configs configs = new Configs();

            if(ugcResponse.getQuestionData().getLobData()!=null && ugcResponse.getQuestionData().getLobData().getBookingId()!=null)
                data.setBookingid(ugcResponse.getQuestionData().getLobData().getBookingId());

            if(questionData.getLobData()!=null && questionData.getLobData().getSrcClient()!=null && source!=null)
            {
                if(!questionData.getLobData().getSrcClient().equalsIgnoreCase(source)){
                    data.setUnsupportedProgramIdData(createUnsupportedProgramIdData());
                }
            }

            // Set guidelines
            List<String> reviewTextList = IntStream.rangeClosed(1, 4)
                    .sequential()
                    .mapToObj(i -> "TEXT_REVIEW_TEXT_" + i)
                    .map(polyglotService::getTranslatedData)
                    .collect(Collectors.toList());

            configs.getGuidelines().getTextReview().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_TEXT_TITLE));
            configs.getGuidelines().getTextReview().setText(reviewTextList);

            List<String> imageReviewList = IntStream.rangeClosed(1, 3)
                    .sequential()
                    .mapToObj(i -> "IMAGE_REVIEW_TEXT_" + i)
                    .map(polyglotService::getTranslatedData)
                    .collect(Collectors.toList());

            configs.getGuidelines().getImageReview().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_IMAGE_TITLE));
            configs.getGuidelines().getImageReview().setText(imageReviewList);

            List<String> baseTextList = IntStream.rangeClosed(1, 3)
                    .sequential()
                    .mapToObj(i -> "BASE_TEXT_" + i)
                    .map(polyglotService::getTranslatedData)
                    .collect(Collectors.toList());
            configs.getGuidelines().getBase().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.BASE_TITLE));
            configs.getGuidelines().getBase().setText(baseTextList);

            // Set onboarding data
            configs.getOnBoardingData().setIcon(rewardIconUrl);

            // Set other properties
            if(questionData.getLobData()!=null && questionData.getLobData().getHotelImage()!=null)
                configs.getOnBoardingData().setHtlImage(questionData.getLobData().getHotelImage());
            configs.setEstimatedTime(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_ESTIMATED_TIME));

            if(questionData.getLobData()!=null && questionData.getLobData().getHotelName()!=null)
                data.setHotelName(questionData.getLobData().getHotelName());
            if(questionData.getLobData()!=null && questionData.getLobData().getCityCode()!=null)
                data.setLocusId(questionData.getLobData().getCityCode());
            data.setLocusType(polyglotService.getTranslatedData(ConstantsTranslation.LOCUS_TYPE));
            data.setEndCityName(null);
            data.setSegmentId(polyglotService.getTranslatedData(ConstantsTranslation.SEGMENT_ID));
            data.setUgcId(questionData.getUgcId());
            data.setLevelNumber(questionData.getLevelNumber());
            data.setNumberOfLevels(questionData.getNumberOfLevels());
            data.setPageId(questionData.getPageId());

            List<QuestionDetails> submittedQuestions = new ArrayList<>();
            if (questionData.getSubmittedQuestions() != null) {
                for (QuestionData questionDataMap : questionData.getSubmittedQuestions().values()) {
                    for (Question question : questionDataMap.getQuestions()) {
                        submittedQuestions.add(createQuestionDetails(question, questionDataMap));
                    }
                }
            }
            data.setSubmittedQuestions(submittedQuestions);

            if (StringUtils.isNotEmpty(questionData.getProgramId())) {
                data.setProgramId(questionData.getProgramId());
            }
            if (StringUtils.isNotEmpty(questionData.getContentId())) {
                data.setContentId(questionData.getContentId());
            }

            int totalQuesCount = 0;
            if (questionData.getLevelConfig() != null && questionData.getNumberOfLevels() > 0) {
                totalQuesCount = fetchDynamicLevelData(questionData, data, null);
            }

            String hotelName = questionData.getLobData() != null && questionData.getLobData().getHotelName() != null ? questionData.getLobData().getHotelName() : StringUtils.EMPTY;
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(submittedQuestions) && totalQuesCount == submittedQuestions.size()) {
                configs.getOnBoardingData().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_TITLE));
                configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_TEXT), hotelName));
                configs.getOnBoardingData().setDesktopText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_TEXT), hotelName));
                configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_EXIT_REVIEW_STRING));
            } else {
                Map<String, LevelConfig> levelConfigMap = questionData.getLevelConfig();
                String lastLevel = String.valueOf(questionData.getNumberOfLevels());
                int maxPerc = 0;
                int maxAmount = 0;
                if (levelConfigMap!=null && levelConfigMap.get(lastLevel) != null) {
                    maxPerc = levelConfigMap.get(lastLevel).getMaxPerc() != null ? levelConfigMap.get(lastLevel).getMaxPerc().intValue() : 0;
                    maxAmount = levelConfigMap.get(lastLevel).getMaxAmount() != null ? levelConfigMap.get(lastLevel).getMaxAmount().intValue() : 0;
                }

                if (maxPerc == 0 && maxAmount == 0) {
                    //Non incentive - both null
                    configs.getOnBoardingData().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_NONINCENTIVE_TITLE));
                    configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_NONINCENTIVE_TEXT), hotelName));
                    configs.getOnBoardingData().setDesktopText(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DESKTOP_NONINCENTIVE_TEXT));
                    configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EXIT_REVIEW_NONINCENTIVE_STRING));
                } else if (maxPerc == 0) {
                    //Oyo - percentage null
                    configs.getOnBoardingData().setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DISCAMOUNT_TITLE), maxAmount));
                    configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DISCAMOUNT_TEXT), hotelName, maxAmount));
                    configs.getOnBoardingData().setDesktopText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DESKTOP_DISCAMOUNT_TEXT), maxAmount));
                    configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EXIT_REVIEW_DISCAMOUNT_STRING));
                } else {
                    //Incentive - percentage & amount available
                    configs.getOnBoardingData().setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_TITLE), maxPerc));
                    configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_TEXT), hotelName, maxPerc, maxAmount));
                    configs.getOnBoardingData().setDesktopText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DESKTOP_TEXT), maxPerc, maxAmount));
                    configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EXIT_REVIEW_STRING));
                }

                data.setMaxAmount(maxAmount);
                data.setMaxPercent(maxPerc);
                int levelSpecificMaxAmount = getLevelSpecificMaxAmount(data.getLevelNumber(), data.getLevelconfig(), maxAmount);
                data.setQuestionConfig(createQuestionConfig(levelSpecificMaxAmount));
            }
            data.setConfigs(configs);

            if (questionData.getReviewTerminationMessage() != null) {
                data.setNextQuestion(null);
                data.setState(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_COMPLETE));
                clientUgcResponse.setData(data);
                clientUgcResponse.setSuccess(true);
                return;
            }

            List<QuestionDetails> nextQuestions = new ArrayList<>();
            if (questionData.getQuestions() != null) {
                for (Question question : questionData.getQuestions()) {
                    nextQuestions.add(createQuestionDetails(question, questionData));
                }
            }
            data.setNextQuestions(nextQuestions);
            data.setState(polyglotService.getTranslatedData(ConstantsTranslation.IN_PROGRESS_REVIEW));
            if(questionData.getError()!=null)
            {
                data.setError(questionData.getError());
            }
            clientUgcResponse.setData(data);
            clientUgcResponse.setSuccess(true);
        } catch (Exception e) {
            clientUgcResponse.setSuccess(false);
            logger.error("Error in UgcResponseClientUgcResponseMapper", e);
        }
    }

    public int getLevelSpecificMaxAmount(Integer levelNo, List<Level> levelconfigList, Integer overAllMaxAmount) {
        if (levelconfigList != null && !levelconfigList.isEmpty() && levelNo != null && levelNo <= levelconfigList.size() && levelNo > 0) {
            return levelconfigList.get(levelNo - 1).getMaxAmount();
        }
        return overAllMaxAmount;
    }

    public QuestionDetails convertQuestionDataToQuestionDetails(QuestionData questionData) {
        try {
            QuestionDetails questionDetails = new QuestionDetails();
            if (questionData.getQuestions() != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(questionData.getQuestions())) {
                questionDetails = createQuestionDetails(questionData.getQuestions().get(0), questionData);
            }
            return questionDetails;
        } catch (Exception e) {
            logger.error("Error in UgcResponseClientUgcResponseMapper", e);
            return null;
        }
    }

    public QuestionDetails createQuestionDetails(Question question, QuestionData questionData) {
        try {
            QuestionDetails questionDetails = new QuestionDetails();
            questionDetails.setDynamicQuestionType(question.getAnswerType());
            questionDetails.setIconUrl(polyglotService.getTranslatedData(Constants.DEFAULT_QUESTION_URL));
            questionDetails.setOptionsInfo(question.getOptionsInfo());
            questionDetails.setId(String.valueOf(question.getQuestionId()));
            questionDetails.setQuestionDescription(question.getQuestionTitle());
            questionDetails.setPageId(questionData.getPageId());
            questionDetails.setUserPage(questionData.getUserPage());
            questionDetails.setSubText(question.getQuestionSubtitle());
            questionDetails.setAdditionalProperties(question.getAdditionalProperties());
            questionDetails.setAnswerProvided(question.getAnswerProvided());
            questionDetails.setAnswerHelpText(question.getAnswerHelpText());
            questionDetails.setAnswerTitle(question.getAnswerTitle());
            questionDetails.setLevel(question.getLevel());
            if(question.getValidators() != null)
                questionDetails.setMandatory(question.getValidators().isMandatory());
            questionDetails.setValidators(question.getValidators());
            if(question.getParentQuestionId()!=null) {
                questionDetails.setParentQuestionId(question.getParentQuestionId().toString());
            }
            questionDetails.setTag(question.getTag());
            return questionDetails;
        } catch (Exception e) {
            logger.error("Error in UgcResponseClientUgcResponseMapper", e);
            return null;
        }
    }

    private int fetchDynamicLevelData(QuestionData questionData, ResponseData responseData, QuestionDetails questionDetails) {
        int totalQuesCount = 0;
        Map<String, LevelConfig> platformsLevelConfig = questionData.getLevelConfig();
        List<Level> levels = new ArrayList<>();

        for (int i = 1; i <= questionData.getNumberOfLevels(); i++) {
            LevelConfig config = platformsLevelConfig.get(String.valueOf(i));
            if (config != null) {
                Level level = new Level();
                level.setMaxAmount(config.getMaxAmount() != null ? config.getMaxAmount().intValue() : 0);
                level.setMaxPercent(config.getMaxPerc() != null ? config.getMaxPerc().intValue() : 0);
                level.setLevelHeader(config.getLevelText());
                level.setCount(config.getLevelTotalQuestions());
                level.setLevelHeaderIcon(ConstantsTranslation.LEVEL_HEADER_ICON);
                totalQuesCount += level.getCount();

                level.setEstimatedTime(polyglotService.getTranslatedData("REVIEW_LEVEL_" + i + "_ESTIMATED_TIME"));
                ThankyouResponse response = new ThankyouResponse();
                if (i == questionData.getNumberOfLevels()) {
                    response.setTitle(polyglotService.getTranslatedData("REVIEW_LEVEL_3_TITLE"));
                    response.setDescription(polyglotService.getTranslatedData("REVIEW_LEVEL_3_DESCRIPTION"));
                    response.setEditTextDesc(polyglotService.getTranslatedData("REVIEW_LEVEL_3_EDIT_TEXT_DESC"));
                    response.setIcon(ConstantsTranslation.LEVEL_2_COMPLETE_URL);
                    level.setLevelSubHeader(String.format(ConstantsTranslation.LEVEL_2_SUBHEADER, level.getMaxAmount()));
                    level.setShowProgressBar(true);
                } else {
                    response.setTitle(polyglotService.getTranslatedData("REVIEW_LEVEL_" + i + "_TITLE"));
                    response.setDescription(polyglotService.getTranslatedData("REVIEW_LEVEL_" + i + "_DESCRIPTION"));
                    response.setEditTextDesc(polyglotService.getTranslatedData("REVIEW_LEVEL_" + i + "_EDIT_TEXT_DESC"));
                    response.setIcon(ConstantsTranslation.LEVEL_1_COMPLETE_URL);
                }

                level.setThankyouResponse(response);

                levels.add(level);
            }
        }

        if (responseData != null) responseData.setLevelconfig(levels);
        if (questionDetails != null) questionDetails.setLevelconfig(levels);

        return totalQuesCount;
    }

    public List<ImageUploadResult> uploadImageToPlatformsS3(MultipartRequest multipartRequest, String correlationKey) throws RestConnectorException, IOException {
        return reviewExecutor.uploadImagesToPlatformsS3(multipartRequest, correlationKey);
    }

    public QuestionConfig createQuestionConfig(Integer maxAmount){
        QuestionConfig questionConfig = new QuestionConfig();
        TextReviewConfig textReviewConfig = new TextReviewConfig();
        RatingConfig ratingConfig = new RatingConfig();
        textReviewConfig.setErrorText(ConstantsTranslation.TEXT_ERROR);
        ratingConfig.setErrorText(ConstantsTranslation.RATING_ERROR);
        Map<String,String> label = new HashMap<>();
        label.put("1", ConstantsTranslation.TERRIBLE_RATING);
        label.put("2", ConstantsTranslation.POOR_RATING);
        label.put("3", ConstantsTranslation.AVERAGE_RATING);
        label.put("4", ConstantsTranslation.GOOD_RATING);
        label.put("5", ConstantsTranslation.EXCELLENT_RATING);
        ratingConfig.setLabel(label);
        questionConfig.setRatingConfig(ratingConfig);
        questionConfig.setTextReviewConfig(textReviewConfig);
        String exitString = polyglotService.getSafeTranslatedData(ConstantsTranslation.REVIEW_EDIT_EXIT_QUESTION_CONFIG_STRING);
        String finalExitString = StringUtils.replace(exitString, "{MAX_AMOUNT}", "₹"+ maxAmount);
        questionConfig.setExitReviewString(finalExitString);
        return questionConfig;
    }

    private UnsupportedProgramIdData createUnsupportedProgramIdData() {
        UnsupportedProgramIdData unsupportedProgramIdData = new UnsupportedProgramIdData();
        unsupportedProgramIdData.setTitle(polyglotService.getTranslatedData(Constants.UNSUPPORTED_TITLE));
        unsupportedProgramIdData.setSubtitle(polyglotService.getTranslatedData(Constants.UNSUPPORTED_SUBTITLE));
        unsupportedProgramIdData.setIconUrl(polyglotService.getTranslatedData(Constants.UNSUPPORTED_ICON_URL));
        return unsupportedProgramIdData;
    }

    /**
     * Generates a ClientUgcResponse object containing error details based on the provided exception.
     *
     * @param e the exception that occurred
     * @return a ClientUgcResponse object populated with error details
     */
    private ClientUgcResponse getErrorResponseForClient(Exception e) {
        ClientUgcResponse ugcResponse = new ClientUgcResponse();
        ugcResponse.setSuccess(false);
        FeMsgs feMsgs = new FeMsgs();
        FeMsgs.ErrMsg1 errMsg1 = new FeMsgs.ErrMsg1();
        FeMsgs.Cta cta = new FeMsgs.Cta();

        String errorCode;
        String errorMessage;
        String errorTitle;

        if (e instanceof LogicalException) {
            LogicalException logicalException = (LogicalException) e;
            errorCode = logicalException.getCode();
            errorMessage = logicalException.getMessage();
            errorTitle = logicalException.getAlternateMessage();
        } else {
            errorCode = UgcError.GENERIC_ERROR.getErrorCode();
            errorMessage = UgcError.GENERIC_ERROR.getErrorMsg();
            errorTitle = UgcError.GENERIC_ERROR.getTitle();
        }

        UgcError ugcError = UgcError.resolve(errorCode);
        if (ugcError == null) {
            ugcError = UgcError.GENERIC_ERROR;
        }

        cta.setText(polyglotService.getTranslatedData(ConstantsTranslation.CTA_TEXT));
        cta.setDeeplink(polyglotService.getTranslatedData(ConstantsTranslation.CTA_DEEPLINK_URL));

        errMsg1.setCode(ugcError.getErrorCode());
        errMsg1.setTitle(polyglotService.getTranslatedData(errorTitle));
        errMsg1.setMsg(polyglotService.getTranslatedData(errorMessage));
        errMsg1.setCta(cta);
        feMsgs.setErrMsg1(errMsg1);
        ugcResponse.setFeMsgs(feMsgs);
        return ugcResponse;
    }

}
