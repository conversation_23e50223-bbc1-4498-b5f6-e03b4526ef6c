package com.mmt.hotels.clientgateway.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.PoliciesRequest;
import com.mmt.hotels.clientgateway.response.PoliciesResponse;
import com.mmt.hotels.clientgateway.restexecutors.HESRestExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.PolicyFactory;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;

import java.util.Map;

@Component
public class PolicyService {
	
	@Autowired
	private HESRestExecutor restExecutor;
	
	@Autowired
	private PolicyFactory policyFactory;
	
	public PoliciesResponse getPolicies(PoliciesRequest policiesRequest, Map<String, String[]> parameterMap) throws ClientGatewayException {
		PersistanceMultiRoomResponseEntity txnDataEntity = restExecutor.getTxnData(policiesRequest.getCorrelationKey(), parameterMap, policiesRequest.getTxnKey());
		return policyFactory.getResponseService(policiesRequest.getClient())
				.transformPolicyResponse(policiesRequest,txnDataEntity);
		
	}

}
