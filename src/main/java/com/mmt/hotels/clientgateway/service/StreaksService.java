package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.StreaksUserRequest;
import com.mmt.hotels.clientgateway.response.streaks.balance.StreaksUserEarningResponse;
import com.mmt.hotels.clientgateway.restexecutors.StreaksExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class StreaksService {
    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    StreaksExecutor streaksExecutor;

    @Autowired
    private MetricErrorLogger metricErrorLogger;


    private static final Logger logger = LoggerFactory.getLogger(StreaksService.class);

    public StreaksUserEarningResponse streaksUserEarning(StreaksUserRequest streaksUserRequest, Map<String, String[]> parameterMap,
                                                         Map<String, String> httpHeaderMap
    ) throws ClientGatewayException {


        try {
            //USER SERVICE CLASS
            String authToken = commonHelper.getAuthToken(httpHeaderMap);
            UserServiceResponse userServiceRsp = commonHelper.getUserDetails(authToken, "", "", "", streaksUserRequest.getCorrelationKey(), Constants.STREAK_USER_BALANCE, null, null, httpHeaderMap);

            String uuid = Constants.LOGGED_OUT_UUID;
            if (userServiceRsp != null && userServiceRsp.getResult() != null && userServiceRsp.getResult().getExtendedUser() != null) {
                UserServiceResult result = userServiceRsp.getResult();
                uuid = result.getExtendedUser().getUuid();
            }
            streaksUserRequest.setUuid(uuid);
            //In case user service didn't respond, the logged out case call should be made
            if (!"".equalsIgnoreCase(uuid)) {
                return streaksExecutor.getStreaksUserEarning(streaksUserRequest, parameterMap, httpHeaderMap);
            }

        }
         catch (Throwable e) {
            logger.error("error occured in streaksUserEarning: ", e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

        return new StreaksUserEarningResponse();
    }

}
