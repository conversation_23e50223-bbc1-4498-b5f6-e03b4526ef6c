package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class HotelMetaDataService {

	private static final Logger LOGGER = LoggerFactory.getLogger(HotelMetaDataService.class);

	@Value("${MOBILE.LOCATION.COUNT}")
	private int locCount;

	@Value("${MOBILE.FACILITY.COUNT}")
	private int facCount;

	@Autowired
	private CommonHelper commonHelper;

	private static final String CONST_METADATA = "metadata";
	private static final String CONST_HOTELFAC = "hotelfacilities";
	private static final String CONST_HOTELLAC = "hotellocations";

	/**
	 * This function is being used to show configured loc and facility.if
	 * filtered code is one.
	 * 
	 * @param metaResponseJSON
	 * @param filterCode
	 * @return
	 */
	public String filterLocationAndFaclity(String metaResponseJSON, String filterCode) {
		String modifiedResponse = metaResponseJSON;
		try {

			if ("1".equalsIgnoreCase(filterCode) && commonHelper.isJsonString(metaResponseJSON)) {

				JSONObject rootObject = new JSONObject(metaResponseJSON);
				if (rootObject.has(CONST_METADATA) && rootObject.get(CONST_METADATA)!=null) {

					if (rootObject.getJSONObject(CONST_METADATA).has(CONST_HOTELFAC)) {
						JSONArray facJSONArr = rootObject.getJSONObject(CONST_METADATA).getJSONArray(CONST_HOTELFAC);
						int totalFac = facJSONArr.length();
						if (totalFac > facCount) {
							for (int i = totalFac - 1; i >= facCount; i--)

							{
								facJSONArr.remove(i);

							}
							rootObject.getJSONObject(CONST_METADATA).put(CONST_HOTELFAC, facJSONArr);

						}

					}

					if (rootObject.getJSONObject(CONST_METADATA).has(CONST_HOTELLAC)) {
						JSONArray locJSONArr = rootObject.getJSONObject(CONST_METADATA).getJSONArray(CONST_HOTELLAC);
						int totalLoc = locJSONArr.length();
						if (locJSONArr.length() > locCount) {
							for (int i = totalLoc - 1; i >= locCount; i--)

							{
								locJSONArr.remove(i);

							}
							rootObject.getJSONObject(CONST_METADATA).put(CONST_HOTELLAC, locJSONArr);

						}

					}

					modifiedResponse = rootObject.toString();
				}
			}

		} catch (Exception e) {
			LOGGER.debug("Error in filterLocationAndFaclity", e);
		}

		return modifiedResponse;

	}

}
