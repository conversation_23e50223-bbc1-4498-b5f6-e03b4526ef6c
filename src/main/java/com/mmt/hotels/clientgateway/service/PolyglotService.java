package com.mmt.hotels.clientgateway.service;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.ServerFailException;
import com.mmt.hotels.clientgateway.restexecutors.PolyglotRestExecutor;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.polyglot.LanguageData;
import com.mmt.hotels.model.polyglot.PolyglotTranslation;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.PostConstruct;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;

@Service
@EnableCaching
@EnableScheduling
public class PolyglotService {

    @Autowired
    PolyglotRestExecutor polyglotRestExecutor;

    @Autowired
    CacheManager cacheManager;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Value("${polyglot.cache.cg.backup}")
    private String polyglotCacheBackup;

    @Value("${polyglot.mob.gen.cache.cg.backup}")
    private String polyglotMobGenCacheBackup;

    private static final Logger LOGGER = LoggerFactory.getLogger(PolyglotService.class);

    @PostConstruct
    public void init() throws Exception {
        hitPolyglotAndUpdateCache();
        hitPolyglotAndUpdateMobGenCache();
    }

    public String getTranslatedData(String key){
        if (StringUtils.isBlank(key)){
            return null;
        }

        return String.valueOf(MapUtils.isNotEmpty(getTranslatedData()) ? getTranslatedData().get(key) : EMPTY_STRING);
    }

    public String getSafeTranslatedData(String key){
        String translatedData = getTranslatedData(key);
        if(translatedData == null){
            return "";
        }
       return translatedData;
    }

    public String getTranslatedDataInLang(String key, String lang){
        if (StringUtils.isBlank(key)){
            return null;
        }

        return String.valueOf(MapUtils.isNotEmpty(getTranslatedDataInLang(lang)) ? getTranslatedDataInLang(lang).get(key) : EMPTY_STRING);
    }

    public String getTranslatedData(String key,String funnelSource){
        if (StringUtils.isBlank(key)){
            return null;
        }
        Object translatedData=null;
        if(StringUtils.isNotBlank(funnelSource))
            translatedData= MapUtils.isNotEmpty(getTranslatedData()) ? getTranslatedData().get(key+"_"+funnelSource.toUpperCase()) : EMPTY_STRING;
        if(translatedData==null || ObjectUtils.isEmpty(translatedData))
            return String.valueOf(MapUtils.isNotEmpty(getTranslatedData()) ? getTranslatedData().get(key) : EMPTY_STRING);
        return String.valueOf(translatedData);
    }

    public Map<String, Object> getTranslatedData() {
        String lang = MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue());
        Cache cache = cacheManager.getCache(Constants.TRANSLATION_CACHE);
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        metricAspect.addToCounterCache(lang , region, "CG_TOTAL_CACHE_ACCESS");

        if (cache.get(lang) == null) {
            updateTranslationCache();
            metricAspect.addToCounterCache(lang , region, "CG_CACHE_MISS");

        }else{
            metricAspect.addToCounterCache(lang , region, "CG_CACHE_HIT");
        }
        return cache.get(lang, Map.class);
    }
    public Map<String, Object> getTranslatedDataInLang(String lang) {
        Cache cache = cacheManager.getCache(Constants.TRANSLATION_CACHE);
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        metricAspect.addToCounterCache(lang , region, "CG_TOTAL_CACHE_ACCESS");

        if (cache.get(lang) == null) {
            updateTranslationCache();
            metricAspect.addToCounterCache(lang , region, "CG_CACHE_MISS");

        }else{
            metricAspect.addToCounterCache(lang , region, "CG_CACHE_HIT");
        }
        return cache.get(lang, Map.class);
    }

    @Scheduled(cron = "0 */30 * ? * *")
    private void updateTranslationCache() {
        try {

            hitPolyglotAndUpdateCache();
            hitPolyglotAndUpdateMobGenCache();
        } catch (Exception e) {
            LOGGER.error("Error in fetching data from polyglot api", e);
        }
    }

    public void hitPolyglotAndUpdateCache() throws Exception{
        LOGGER.debug("Populating Cache from polyglot");
        boolean cacheUpdated = false;
        int retryCount =3;
        while (retryCount>0) {


            PolyglotTranslation polyglotTranslation = polyglotRestExecutor.getPolyglotTranslation();
            Map<String, LanguageData> backupLanguageDataMap = getBackupPolyglotData(polyglotCacheBackup,false);
            LOGGER.debug("Polyglot Backup map size is: ",backupLanguageDataMap.size());
            if (polyglotTranslation.getSuccess()) {

                Map<String, LanguageData> languageDataMap = polyglotTranslation.getData().getAssets().getTranslationConstant().getLanguageDataMap();
                if (org.apache.commons.collections.MapUtils.isNotEmpty(languageDataMap)) {
                    for (Map.Entry<String, LanguageData> entry : languageDataMap.entrySet()) {
                        String key = entry.getKey();
                        LanguageData value = entry.getValue();
                        updateCache(key, value,backupLanguageDataMap.get(key));
                    }
                    cacheUpdated = true;
                }
                retryCount = 0;

            }
            retryCount--;
        }

        if (!cacheUpdated){
            LOGGER.warn("Fetch from polyglot failed");
            throw new ServerFailException("Polyglot fetch failed");
        }
    }

    public void hitPolyglotAndUpdateMobGenCache() throws Exception{
        LOGGER.debug("Populating MobGenCache from polyglot");
        boolean cacheUpdated = false;
        int retryCount =3;
        while (retryCount>0) {


            PolyglotTranslation polyglotTranslation = polyglotRestExecutor.getMobGenPolyglotTranslation();
            Map<String, LanguageData> backupLanguageDataMap = getBackupPolyglotData(polyglotMobGenCacheBackup,true);
            LOGGER.debug("Polyglot Backup map size is: ",backupLanguageDataMap.size());
            if (polyglotTranslation.getSuccess()) {

                Map<String, LanguageData> languageDataMap = polyglotTranslation.getData().getAssets().getTranslationClient().getLanguageDataMap();
                if (org.apache.commons.collections.MapUtils.isNotEmpty(languageDataMap)) {
                    for (Map.Entry<String, LanguageData> entry : languageDataMap.entrySet()) {
                        String key = entry.getKey();
                        LanguageData value = entry.getValue();
                        updateMobGenCache(key, value,backupLanguageDataMap.get(key));
                    }
                    cacheUpdated = true;
                }
                retryCount = 0;

            }
            retryCount--;
        }

        if (!cacheUpdated){
            LOGGER.warn("Fetch from polyglot failed");
            throw new ServerFailException("Polyglot fetch failed");
        }
    }

    private void updateCache(String lang, LanguageData languageData,LanguageData backupLanguageData) {

        Cache cache = cacheManager.getCache(Constants.TRANSLATION_CACHE);
        Integer langVersion = cache.get(lang + "-v", Integer.class);
        if (langVersion != null && languageData.getVersion() <= langVersion) {
            return;
        }

        Map<String, Object> langMap = cache.get(lang, Map.class);
        if (MapUtils.isEmpty(langMap)) {
            langMap = new HashMap<>();
        }
        Map<String,String> polyglotMap = languageData.getTranslatedData();
        if(backupLanguageData != null && backupLanguageData.getTranslatedData().size()>0){
            Map<String, String> backupMap = backupLanguageData.getTranslatedData();
            for(String k : backupMap.keySet()){
                if(!polyglotMap.containsKey(k)){
                    polyglotMap.put(k,backupMap.get(k));
                }
            }
        }
        if (MapUtils.isNotEmpty(polyglotMap)) {
            polyglotMap.forEach(langMap::put);
        }
        cache.put(lang + "-v", languageData.getVersion());
        cache.put(lang, langMap);

    }


    public Map<String , CacheStats> getCacheStats(){

        Map<String , CacheStats> statsMap = new HashMap<>();
        Cache cache = cacheManager.getCache(Constants.TRANSLATION_CACHE);
        com.github.benmanes.caffeine.cache.Cache nativeCGCache =(com.github.benmanes.caffeine.cache.Cache) cache.getNativeCache();
        statsMap.put(Constants.TRANSLATION_CACHE , nativeCGCache.stats());
        return statsMap;
    }

    private void updateMobGenCache(String lang, LanguageData languageData,LanguageData backupLanguageData) {

        Cache cache = cacheManager.getCache(Constants.MOBGEN_CACHE);
        Integer langVersion = cache.get(lang + "-v", Integer.class);
        if (langVersion != null && languageData.getVersion() <= langVersion) {
            return;
        }

        Map<String, String> langMap = cache.get(lang, Map.class);
        if (MapUtils.isEmpty(langMap)) {
            langMap = new HashMap<>();
        }
        Map<String,String> polyglotMap = languageData.getTranslatedData();
        if(backupLanguageData != null && backupLanguageData.getTranslatedData().size()>0){
            Map<String, String> backupMap = backupLanguageData.getTranslatedData();
            for(String k : backupMap.keySet()){
                if(!polyglotMap.containsKey(k)) {
                    polyglotMap.put(k, backupMap.get(k));
                }
            }
        }
        if (MapUtils.isNotEmpty(polyglotMap)) {
            polyglotMap.forEach(langMap::put);
        }
        cache.put(lang + "-v", languageData.getVersion());
        cache.put(lang, langMap);
    }

    private Map<String,LanguageData> getBackupPolyglotData(String backupFileName, boolean isMobConfig) throws IOException, JsonParseException {
        Map<String, LanguageData> backupLanguageDataMap = new HashMap<>();
        try{
            String polyglotBackup = FileUtils.readFileToString(ResourceUtils.getFile(backupFileName));
            PolyglotTranslation backupPolyglotTranslation = objectMapperUtil.getObjectFromJson(polyglotBackup,PolyglotTranslation.class,DependencyLayer.POLYGLOT);
            if(isMobConfig){
                backupLanguageDataMap = backupPolyglotTranslation.getData().getAssets().getTranslationClient().getLanguageDataMap();
            }else{
                backupLanguageDataMap = backupPolyglotTranslation.getData().getAssets().getTranslationConstant().getLanguageDataMap();
            }
            return backupLanguageDataMap;
        }
        catch (Exception e){
            LOGGER.error("Error in Generating Polyglot Backup", e);
        }
        return backupLanguageDataMap;
    }
}
