package com.mmt.hotels.clientgateway.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;

@Component
public class LandingService {

	@Autowired
	private MobLandingExecutor mobLandingExecutor;
	
	   public String getLatLngFromGooglePlaceId(String placeId,Double lat, Double lng) throws ClientGatewayException {
		  return  mobLandingExecutor.getLatLngFromGooglePlaceId(placeId, lat, lng);
	    }

}
