package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Enums;
import com.google.common.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.LocationType;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.HotelMobConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.hermes.HermesPriceApiResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.StayTypeInfo;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.restexecutors.*;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.StaticDetailFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.*;

@Component
public class DetailService {
	
	@Autowired
	private SearchRoomsFactory searchRoomsFactory;
	
	@Autowired
	private SearchRoomsExecutor searchRoomsExecutor;
	
	@Autowired
	private StaticDetailFactory staticDetailFactory;
	
	@Autowired
	private StaticDetailExecutor staticDetailExecutor;

	@Autowired
	private UpdatedPriceFactory updatedPriceFactory;

	@Autowired
	private UpdatedPriceExecutor updatedPriceExecutor;
	
	@Autowired
    private MetricErrorLogger metricErrorLogger;
	
	@Autowired
	private OldToNewerRequestTransformer oldToNewerRequestTransformer;
	
	@Autowired
	private CommonHelper commonHelper;

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	private Utility utility;

	@Autowired
	CalendarExecutor calendarExecutor;

	@Autowired
	StreaksExecutor streaksExecutor;

	@Autowired
	private OrchDetailService orchDetailService;

	private boolean calendarRearch;

	@Autowired
	PropertyManager propManager;

	private static final Logger logger = LoggerFactory.getLogger(DetailService.class);

	@PostConstruct
	public void init() {
		CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
		calendarRearch = commonConfig.calendarRearch();
	}

	public SearchRoomsResponse searchRooms(SearchRoomsRequest searchRoomsRequest, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			long startTime = new Date().getTime();
			/*
			 * This method will update the old pax (RoomStayCandidate generated by client) to new RoomStayCandidate
			 * this is done because of no common rate plan between HES and hermes
			 * RoomStayCandidate generation logic is different in MMT and GI so we were getting different rate plans from Pricer
			 */
			commonHelper.updateRoomStayCandidate(searchRoomsRequest);
			// END of update RoomStayCandidate logic
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRoomsRequest.getSearchCriteria(), searchRoomsRequest, httpHeaderMap);
			if (utility.isDetailPageRearchFlow(true, searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getRequestId() : "", searchRoomsRequest.getExpDataMap())) {
				return orchDetailService.searchRooms(searchRoomsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
			}
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
			PriceByHotelsRequestBody priceByHotelsRequestBody = searchRoomsFactory.
					getRequestService(searchRoomsRequest.getClient()).convertSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);
			startTime = new Date().getTime();
			RoomDetailsResponse roomDetailsResponse;
			HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity;
			HotelImage hotelImage;
			int totalCandidates=0;
			try {
				if (searchRoomsRequest.getSearchCriteria() != null) {
					utility.setLoggingParametersToMDC(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(), searchRoomsRequest.getSearchCriteria().getCheckIn(),
							searchRoomsRequest.getSearchCriteria().getCheckOut());
					totalCandidates=utility.getTotalAdultsFromRequest(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates())+utility.getTotalChildrenFromRequest(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());
				}
				CountDownLatch countDownLatchAsync = utility.getCountDownLatch(3);
				Future<RoomDetailsResponse> roomDetailsResponseFuture = searchRoomsExecutor.getRoomPrices(priceByHotelsRequestBody, parameterMap, httpHeaderMap, countDownLatchAsync);
				String MMTorGIHotelId = Utility.getMMTorGIHotelId(searchRoomsRequest.getSearchCriteria().getHotelId(), searchRoomsRequest.getSearchCriteria().getGiHotelId());
				Future<HotelsRoomInfoResponseEntity> hotelsRoomInfoResponseEntityFuture = searchRoomsExecutor.getRoomStaticDetails(MMTorGIHotelId, searchRoomsRequest.getSearchCriteria().getVcId(), priceByHotelsRequestBody.getBrand(), totalCandidates, searchRoomsRequest.getCorrelationKey(), parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL,null, searchRoomsRequest.getSearchCriteria().getLocationId());
				Future<HotelImage> hotelImageFuture = searchRoomsExecutor.getHotelImages(searchRoomsRequest, parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL);
				countDownLatchAsync.await();
				roomDetailsResponse = roomDetailsResponseFuture.get();
				hotelsRoomInfoResponseEntity = hotelsRoomInfoResponseEntityFuture.get();
				hotelImage = hotelImageFuture.get();
			} catch (ExecutionException ex) {
				logger.warn("ExecutionException occurred in searchRooms:", ex);
				throw ex.getCause();
			} catch (Exception ex) {
				logger.warn("Exception occurred in searchRooms:", ex);
				throw ex;
			} finally {
				metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRoomsTotal", new Date().getTime() - startTime);
			}

			/*
			* myPartner change log :
			* 	Overloaded convertSearchRoomsResponse created with commonModifierResponse
			* 	The other method is used to keep the test cases intact. All calls to that from test cases is forwarded to the new overloaded method
			* */
			Map <String, String> expData = searchRoomsRequest.getExpDataMap();
			if (expData != null) {
				expData.put("recomFlow", Utility.searchRoomRecomValue(roomDetailsResponse));
			}
			searchRoomsRequest.setExpDataMap(expData);
			return searchRoomsFactory.getResponseService(searchRoomsRequest.getClient())
					.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage,
							searchRoomsRequest.getExpDataMap(), searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(),
							searchRoomsRequest.getSearchCriteria(),searchRoomsRequest.getFilterCriteria(),
							searchRoomsRequest.getRequestDetails(),commonModifierResponse);

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("downstream error occurred in searchRooms: {} ", e.getMessage());
				logger.debug("error occurred in searchRooms: {}", e.getMessage(), e);
			} else
				logger.error("error occurred in searchRooms: {}", e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			if ( exceptionHandlerResponse.getClientGatewayException() != null && exceptionHandlerResponse.getClientGatewayException().getDependencyLayer() != null &&
					StringUtils.equalsIgnoreCase(exceptionHandlerResponse.getClientGatewayException().getDependencyLayer().getCode() , "11") &&
					exceptionHandlerResponse.getClientGatewayException().getErrorType() != null &&
					StringUtils.equalsIgnoreCase(exceptionHandlerResponse.getClientGatewayException().getErrorType().getCode() , "6") &&
					StringUtils.equalsIgnoreCase(exceptionHandlerResponse.getClientGatewayException().getCode(),"01")) {
				logger.warn("CG request for no common rate plan {}", objectMapperUtil.getJsonFromObject(searchRoomsRequest, DependencyLayer.ORCHESTRATOR) );
			}
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

    public WishListedHotelsDetailResponse wishListedStaticDetail(WishListedHotelsDetailRequest wishListedHotelsDetailRequest, Map<String, String[]> parameterMap,
																 Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            long startTime = new Date().getTime();
			SearchCriteria searchCriteria = buildSearchCriteria(wishListedHotelsDetailRequest);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchCriteria, wishListedHotelsDetailRequest, httpHeaderMap);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, WISHLISTED_STATIC_DETAILS, System.currentTimeMillis() - startTime);

			HotStoreHotelsRequestBody hotStoreHotelRequestBody = staticDetailFactory.getRequestService(wishListedHotelsDetailRequest.getClient())
					.getHotStoreHotelsRequest(wishListedHotelsDetailRequest, commonModifierResponse);

			FlyfishReviewRequestBody flyfishReviewRequestBody = staticDetailFactory.getRequestService(wishListedHotelsDetailRequest.getClient())
					.getFlyfishReviewRequest(wishListedHotelsDetailRequest, commonModifierResponse);

			startTime = new Date().getTime();
			HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse = null;
			FlyfishReviewWrapperResponse flyfishReviewWrapperResponse = null;
            try {
				 hotStoreHotelsWrapperResponse = staticDetailExecutor.getWishListedHotelsFromHotStore(hotStoreHotelRequestBody, httpHeaderMap);
				 flyfishReviewWrapperResponse = staticDetailExecutor.getFlyFishReviewSummary(flyfishReviewRequestBody, httpHeaderMap);
			} catch (Exception ex) {
                throw ex;
            } finally {
                metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "wishListedStaticDetailTotal", new Date().getTime() - startTime);
            }

            return staticDetailFactory.getResponseService(wishListedHotelsDetailRequest.getClient()).
					convertWishListedStaticDetailResponse(hotStoreHotelsWrapperResponse, flyfishReviewWrapperResponse,
							wishListedHotelsDetailRequest.getClient(), wishListedHotelsDetailRequest, commonModifierResponse);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in wishListedStaticDetail: " + e.getMessage());
                logger.debug("error occurred in wishListedStaticDetail: " + e.getMessage(), e);
            } else
                logger.error("error occurred in wishListedStaticDetail: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

	public SearchCriteria buildSearchCriteria(WishListedHotelsDetailRequest wishListedHotelsDetailRequest) {
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCityCode(wishListedHotelsDetailRequest.getSearchCriteria().getCityCode());
		searchCriteria.setLocationId(wishListedHotelsDetailRequest.getSearchCriteria().getLocationId());
		searchCriteria.setLocationType(wishListedHotelsDetailRequest.getSearchCriteria().getLocationType());
		return searchCriteria;
	}

	public StaticDetailResponse staticDetail(StaticDetailRequest staticDetailRequest, Map<String, String[]> parameterMap,
			Map<String, String> httpHeaderMap) throws ClientGatewayException{
		try {
			commonHelper.updateRoomStayCandidate(staticDetailRequest);
			logger.warn("CG-GI static details headers: {}", httpHeaderMap);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(staticDetailRequest.getSearchCriteria(), staticDetailRequest, httpHeaderMap);
			HotelDetailsMobRequestBody hotelDetailsMobRequestBody = staticDetailFactory.
					getRequestService(staticDetailRequest.getClient()).convertStaticDetailRequest(staticDetailRequest, commonModifierResponse);
			//Adding Log to check LocationType for SWAT-7266394 will revert this after someTime
			if(staticDetailRequest.getSearchCriteria() != null && StringUtils.isNotEmpty(staticDetailRequest.getSearchCriteria().getLocationType()) && !Enums.getIfPresent(LocationType.class,staticDetailRequest.getSearchCriteria().getLocationType()).isPresent()){
				logger.warn("Getting Strange Location type in Static Detail request from Client {}",objectMapperUtil.getJsonFromObject(staticDetailRequest, DependencyLayer.CLIENTGATEWAY));
			}

			StaticDetailResponse staticDetailResponse = null;
			if (utility.shouldRedirectStaticDetailToOrch(staticDetailRequest)) {
				staticDetailResponse =  orchDetailService.staticDetails(staticDetailRequest,parameterMap, httpHeaderMap, commonModifierResponse);
			} else {

				HotelDetailWrapperResponse hotelDetailWrapperResponse = staticDetailExecutor.
						getStaticDetail(hotelDetailsMobRequestBody, parameterMap, httpHeaderMap);

				staticDetailResponse = staticDetailFactory.getResponseService(staticDetailRequest.getClient()).
						convertStaticDetailResponse(hotelDetailWrapperResponse, staticDetailRequest.getClient(), staticDetailRequest, commonModifierResponse);
			}

			if (utility.checkIfGoStaysProperty(staticDetailResponse)) {
				StreaksUserInfoResponse streaksUserInfoResponse = utility.getUserInfoResponse(commonModifierResponse.getExtendedUser() != null ? commonModifierResponse.getExtendedUser().getUuid(): "",
						commonModifierResponse.getExpDataMap(), staticDetailRequest.getCorrelationKey(), parameterMap, httpHeaderMap);
				if (streaksUserInfoResponse != null) {
					utility.suppressUnnecessaryFieldsInStreaksResponseInStaticDetails(streaksUserInfoResponse);
					staticDetailResponse.setUserStreaksInfoResponse(streaksUserInfoResponse);
				}
			}
			return staticDetailResponse;

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in staticDetail: " + e.getMessage());
        		logger.debug("error occurred in staticDetail: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in staticDetail: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}


	public UpdatePriceResponse updatePrice(UpdatePriceRequest updatePriceRequest, Map<String, String[]> parameterMap,
			Map<String, String> httpHeaderMap) throws ClientGatewayException{
		try {

			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(updatePriceRequest.getSearchCriteria(), updatePriceRequest, httpHeaderMap);

			// Check for rearchFlow condition similar to searchRooms
			boolean rearchFlow = utility.isDetailPageRearchFlow(true, updatePriceRequest.getRequestDetails().getRequestId(), commonModifierResponse.getExpDataMap());

			if (rearchFlow) {
				return orchDetailService.updatePrice(updatePriceRequest, parameterMap, httpHeaderMap, commonModifierResponse);
			}

			PriceByHotelsRequestBody priceByHotelsRequestBody = updatedPriceFactory.
					getRequestService(updatePriceRequest.getClient()).convertUpdatedPriceRequest(updatePriceRequest, commonModifierResponse);
			PriceBreakDownResponse priceBreakDownResponse = updatedPriceExecutor.updatedPrice(priceByHotelsRequestBody, parameterMap, httpHeaderMap);

			return updatedPriceFactory.getResponseService(updatePriceRequest.getClient()).convertUpdatedPriceResponse(priceBreakDownResponse, updatePriceRequest,commonModifierResponse);

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in updatePrice: " + e.getMessage());
        		logger.debug("error occured in updatePrice: " + e.getMessage(), e);
        	} else
        		logger.error("error occured in updatePrice: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
		}
	}
	
	public String searchPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			SearchRoomsRequest searchRoomsRequest = oldToNewerRequestTransformer.updateSearchRoomsRequest(priceByHotelsRequestBody);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRoomsRequest.getSearchCriteria(), searchRoomsRequest, httpHeaderMap);
			PriceByHotelsRequestBody priceByHotelsRequestBodyModified = searchRoomsFactory.
					getRequestService(searchRoomsRequest.getClient()).convertSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);

			return searchRoomsExecutor.getRoomPricesOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);

		} catch (Throwable e) {
			if (e instanceof AuthenticationException){
				return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
			}
			else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in searchPriceOld: " + e.getMessage());
        		logger.debug("error occurred in searchPriceOld: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in searchPriceOld: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}
	
	public String alternateDatesPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try{
			SearchRoomsRequest searchRoomsRequest = oldToNewerRequestTransformer.updateSearchRoomsRequest(priceByHotelsRequestBody);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRoomsRequest.getSearchCriteria(), searchRoomsRequest, httpHeaderMap);
			PriceByHotelsRequestBody priceByHotelsRequestBodyModified = searchRoomsFactory.
					getRequestService(searchRoomsRequest.getClient()).convertSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);

			if(calendarRearch) {
				return orchDetailService.alternateDatesPrice(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap, commonModifierResponse);
			}

			return searchRoomsExecutor.alternateDatesPriceOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);

		} catch (Throwable e) {
			if (e instanceof AuthenticationException){
				return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
			}
			else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in alternateDatesPriceOld: " + e.getMessage());
        		logger.debug("error occurred in alternateDatesPriceOld: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in alternateDatesPriceOld: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}


	public String getStaticDetailsResponse(HotelDetailsMobRequestBody request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException{
        try {
			SearchRoomsRequest searchRequest = oldToNewerRequestTransformer.updateSearchRoomRequest(request);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRequest.getSearchCriteria(), searchRequest, httpHeaderMap);
			HotelDetailsMobRequestBody hotelDetailsMobRequestBody = staticDetailFactory.
					getRequestService(searchRequest.getClient()).convertStaticDetailRequest(searchRequest, commonModifierResponse, request);

			return staticDetailExecutor.getStaticDetailsResponse(hotelDetailsMobRequestBody, parameterMap, httpHeaderMap);

        } catch (Throwable e) {
			if (e instanceof AuthenticationException){
				return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
			} else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in getStaticDetailsResponse: " + e.getMessage());
        		logger.debug("error occurred in getStaticDetailsResponse: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in getStaticDetailsResponse: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}


	public CityGuideResponse cityGuideData(com.mmt.hotels.clientgateway.request.CityGuideRequest cityGuideRequest, Map<String, String[]> parameterMap,
										   Map<String, String> httpHeaderMap, String correlationKey) throws ClientGatewayException{
		try {
			CityGuideRequest request = staticDetailFactory.
					getRequestService(cityGuideRequest.getClient()).convertCityGuideRequest(cityGuideRequest, correlationKey);
			return staticDetailExecutor.
					getCityGuildeResponse(request, parameterMap, httpHeaderMap);
		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("error occurred in cityGuide: " + e.getMessage());
				logger.debug("error occurred in cityGuide: " + e.getMessage(), e);
			} else
				logger.error("error occurred in cityGuide: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public CalendarAvailabilityResponse fetchCalendarAvailability(CalendarAvailabilityRequest calendarAvailabilityRequest, String correlationKey,
																  Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			CommonModifierResponse commonModifierResponse = commonHelper
					.processRequest(calendarAvailabilityRequest.getSearchCriteria(), calendarAvailabilityRequest, httpHeaderMap);

			if(calendarRearch) {
				return orchDetailService.calendarAvailability(calendarAvailabilityRequest, correlationKey , parameterMap, httpHeaderMap, commonModifierResponse);
			}

			PriceByHotelsRequestBody calendarAvailabilityRequestModified = searchRoomsFactory.getRequestService(calendarAvailabilityRequest.getClient())
					.convertSearchRoomsRequest(calendarAvailabilityRequest, commonModifierResponse);
			calendarAvailabilityRequestModified.setCalendarCriteria(buildCalendarCriteria(calendarAvailabilityRequest));
			com.mmt.hotels.model.response.CalendarAvailabilityResponse calendarAvailabilityResponse = calendarExecutor
					.getCalendarAvailability(calendarAvailabilityRequestModified, correlationKey, parameterMap, httpHeaderMap);
			return searchRoomsFactory.getResponseService(calendarAvailabilityRequest.getClient()).convertCalendarAvailabilityResponse(calendarAvailabilityResponse);

		} catch(Throwable e){
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("error occurred in fetchCalendarAvailability : " + e.getMessage());
				logger.debug("error occurred in fetchCalendarAvailability : " + e.getMessage(), e);
			} else {
				logger.error("error occurred in fetchCalendarAvailability : " + e.getMessage(), e);
			}
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public CalendarCriteria buildCalendarCriteria(CalendarAvailabilityRequest calendarAvailabilityRequest) {
		if(null == calendarAvailabilityRequest.getCalendarCriteria())
			return null;
		com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria calendarCriteria = new com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria();
		calendarCriteria.setAdvanceDays(calendarAvailabilityRequest.getCalendarCriteria().getAdvanceDays());
		calendarCriteria.setAvailable(calendarAvailabilityRequest.getCalendarCriteria().isAvailable());
		calendarCriteria.setMaxDate(calendarAvailabilityRequest.getCalendarCriteria().getMaxDate());
		calendarCriteria.setMlos(calendarAvailabilityRequest.getCalendarCriteria().getMlos());
		return calendarCriteria;
	}

	public DayUseRoomsResponse searchSlots(DayUseRoomsRequest dayUseRoomsRequest, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			long startTime = new Date().getTime();
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(dayUseRoomsRequest.getSearchCriteria(), dayUseRoomsRequest, httpHeaderMap);
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_SLOTS, System.currentTimeMillis() - startTime);
			PriceByHotelsRequestBody priceByHotelsRequestBody = searchRoomsFactory.
					getRequestService(dayUseRoomsRequest.getClient()).convertSearchRoomsRequest(dayUseRoomsRequest, commonModifierResponse);
			boolean rearchFlow = utility.isDetailPageRearchFlow(true, dayUseRoomsRequest.getRequestDetails() != null ? dayUseRoomsRequest.getRequestDetails().getRequestId() : "", commonModifierResponse.getExpDataMap());
			startTime = new Date().getTime();
			RoomDetailsResponse roomDetailsResponse;
			HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity;
			HotelImage hotelImage;
			int totalCandidates=0;
			try {
				if (rearchFlow) {
					return orchDetailService.searchSlots(dayUseRoomsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
				}
				parameterMap = utility.addFunnelSourceToParameterMap(dayUseRoomsRequest.getRequestDetails().getFunnelSource(), parameterMap, commonModifierResponse.getExpDataMap());
				String MMTorGIHotelId = Utility.getMMTorGIHotelId(dayUseRoomsRequest.getSearchCriteria().getHotelId(), dayUseRoomsRequest.getSearchCriteria().getGiHotelId());
				CountDownLatch countDownLatchAsync = utility.getCountDownLatch();
				Future<RoomDetailsResponse> roomDetailsResponseFuture = searchRoomsExecutor.getRoomPrices(priceByHotelsRequestBody, parameterMap, httpHeaderMap, countDownLatchAsync);
				Future<HotelsRoomInfoResponseEntity> hotelsRoomInfoResponseEntityFuture = searchRoomsExecutor.getRoomStaticDetails(MMTorGIHotelId, dayUseRoomsRequest.getSearchCriteria().getVcId(), priceByHotelsRequestBody.getBrand(), totalCandidates, dayUseRoomsRequest.getCorrelationKey(), parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL,null, dayUseRoomsRequest.getSearchCriteria().getLocationId());
				Future<HotelImage> hotelImageFuture = searchRoomsExecutor.getHotelImages(dayUseRoomsRequest, parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL);
				countDownLatchAsync.await();

				roomDetailsResponse = roomDetailsResponseFuture.get();
				hotelsRoomInfoResponseEntity = hotelsRoomInfoResponseEntityFuture.get();
				hotelImage = hotelImageFuture.get();
			} catch (ExecutionException ex) {
				logger.warn("ExecutionException occurred in searchSlots:", ex);
				throw ex.getCause();
			} catch (Exception ex) {
				logger.warn("Exception occurred in searchSlots:", ex);
				throw ex;
			} finally {
				metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchSlotsTotal", new Date().getTime() - startTime);
			}

			return searchRoomsFactory.getResponseService(dayUseRoomsRequest.getClient())
					.convertSearchSlotsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, dayUseRoomsRequest, commonModifierResponse);

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("downstream error occurred in searchSlots: {} ", e.getMessage());
				logger.debug("error occurred in searchSlots: {}", e.getMessage(), e);
			} else
				logger.error("error occurred in searchSlots: {}", e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}

	}

}
