package com.mmt.hotels.clientgateway.service;

import java.util.Map;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.SearchAddonsRequest;
import com.mmt.hotels.clientgateway.response.SearchAddonsResponse;
import com.mmt.hotels.clientgateway.restexecutors.AddonsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.AddonsServiceFactory;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.response.addon.AddOnEntity;

@Component
public class AddonsService {

	@Autowired
	private CommonHelper commonHelper;

	@Autowired
	private AddonsExecutor addonsExecutor;
	
	@Autowired
	private AddonsServiceFactory addonsServiceFactory;

	@Autowired
	private OldToNewerRequestTransformer oldRequestTransformer;
	
	public SearchAddonsResponse getAddons(SearchAddonsRequest searchAddonsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchAddonsRequest.getSearchCriteria(),searchAddonsRequest, httpHeaderMap);
		GetAddonsRequest getAddonsRequest = addonsServiceFactory.getRequestService(searchAddonsRequest.getClient()).convertSearchAddonsRequest(searchAddonsRequest,commonModifierResponse);
		AddOnEntity response = addonsExecutor.getAddonsResponse(getAddonsRequest, parameterMap,AddOnEntity.class);
		if (response != null && response.getResponseErrors() != null
				&& CollectionUtils.isNotEmpty(response.getResponseErrors().getErrorList())) {
			com.mmt.hotels.model.response.errors.Error error = response.getResponseErrors().getErrorList().get(0);
			throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
					error.getErrorCode(), error.getErrorMessage());
		}
		return  addonsServiceFactory.getResponseService(searchAddonsRequest.getClient()).convertSearchAddonsResponse(response);
	}

	public String getAddons(GetAddonsRequest getAddonsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		SearchAddonsRequest searchAddonsRequest = oldRequestTransformer.updateAddonsRequest(getAddonsRequest);
		CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchAddonsRequest.getSearchCriteria(),searchAddonsRequest, httpHeaderMap);
		getAddonsRequest = addonsServiceFactory.getRequestService(searchAddonsRequest.getClient()).convertSearchAddonsRequest(searchAddonsRequest,commonModifierResponse);
		String getAddonsResponse = addonsExecutor.getAddonsResponse(getAddonsRequest, parameterMap, String.class);
		return  getAddonsResponse;
	}

}
