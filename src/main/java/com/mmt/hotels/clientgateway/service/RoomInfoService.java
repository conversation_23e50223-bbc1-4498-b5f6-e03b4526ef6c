package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.RoomInfoRequest;
import com.mmt.hotels.clientgateway.response.RoomInfoResponse;
import com.mmt.hotels.clientgateway.restexecutors.HESRestExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.RoomInfoFactory;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RoomInfoService {

    @Autowired
    private HESRestExecutor restExecutor;

    @Autowired
    private RoomInfoFactory roomInfoFactory;

    public RoomInfoResponse getRoomInfo(RoomInfoRequest roomInfoRequest, Map<String, String[]> parameterMap) throws ClientGatewayException {
        /* Maps to OLD cg/room-info API */
        PersistanceMultiRoomResponseEntity txnDataEntity = restExecutor.getTxnData(roomInfoRequest.getCorrelationKey(), parameterMap, roomInfoRequest.getTxnKey());
        return roomInfoFactory.getResponseService(roomInfoRequest.getClient())
                .transformRoomInfoResponse(roomInfoRequest,txnDataEntity);
    }

    public RoomInfoResponse getRoomInfos(RoomInfoRequest roomInfoRequest, Map<String, String[]> parameterMap) throws ClientGatewayException {
        /* Maps to NEW cg/room-infos API */
        PersistanceMultiRoomResponseEntity txnDataEntity = restExecutor.getTxnData(roomInfoRequest.getCorrelationKey(), parameterMap, roomInfoRequest.getTxnKey());
        return roomInfoFactory.getResponseService(roomInfoRequest.getClient())
                .transformRoomInfoResponses(roomInfoRequest,txnDataEntity);

    }

}
