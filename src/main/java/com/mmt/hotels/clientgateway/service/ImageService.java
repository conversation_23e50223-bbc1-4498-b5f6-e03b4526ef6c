package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaV2;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.restexecutors.ImageDataExecutor;
import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.pojo.request.image.HotelImageRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class ImageService {
    @Autowired
    ImageDataExecutor imageDataExecutor;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    @Qualifier("staticDetailResponseTransformerDesktop")
    private StaticDetailResponseTransformer staticDetailResponseTransformer;

    private static final Logger logger = LoggerFactory.getLogger(ImageService.class);



    public MediaV2 travellerImageResponse(HotelImageRequest hotelImageRequest, String client) {
        try {
            long startTime = System.currentTimeMillis();
            HotelImage hotelimage = imageDataExecutor.getStaticImageDetailsResponse(hotelImageRequest);
            MediaV2 mediaV2 = staticDetailResponseTransformer.buildMediaV2(hotelimage, null, null, false, false, client);
            metricAspect.addToTime(DependencyLayer.CLIENTGATEWAY.name(), "travellerimage", System.currentTimeMillis() - startTime);
            return mediaV2;
        } catch (Exception e) {
            logger.error("Exception occured in image service : {}", e.getMessage(), e);
            return null;
        }

    }
}
