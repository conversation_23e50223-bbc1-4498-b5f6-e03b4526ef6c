package com.mmt.hotels.clientgateway.service;

import java.util.List;
import java.util.Map;
import com.mmt.hotels.clientgateway.constants.Constants;

import com.mmt.hotels.clientgateway.response.BNPLDetails;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.TotalPriceResponse;
import com.mmt.hotels.clientgateway.transformer.response.HCouponApplicableTextCreator;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.request.UserLocation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.ValidateCouponRequest;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.restexecutors.DiscountServiceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.DiscountServiceFactory;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;

@Component
public class DiscountService {
	
    private static final Logger LOGGER = LoggerFactory.getLogger(DiscountService.class);
    
	@Autowired
	private DiscountServiceFactory discountServiceFactory;
	
	@Autowired
	private DiscountServiceExecutor discountServiceExecutor;
	
    @Autowired
    private MetricErrorLogger metricErrorLogger;
    
	@Autowired
	private ObjectMapperUtil objectMapperUtil;

	@Autowired
	private CommonHelper commonHelper;

	@Autowired
	private PolyglotService polyglotService;

	public ValidateCouponResponseBody validateCoupon(ValidateCouponRequest validateCouponRequest,  Map<String, String[]> parameterMap,
			Map<String, String> httpHeaderMap, String client) throws ClientGatewayException{
		
		ValidateCouponResponseBody response;
		
		try {
        	String request = objectMapperUtil.getJsonFromObject(validateCouponRequest, DependencyLayer.CLIENTGATEWAY);
        	LOGGER.warn("Client Request for Validate Coupon:- " + request);
			ValidateCouponRequestBody req  = discountServiceFactory.getRequestService(client).convertValidateCouponRequest(validateCouponRequest);

			/*GIHTL-12941 If user location details like city,state and country
			 * is empty in the request,set the value from Akamai Headers.*/
			if(req.getUserLocation()==null) {
				UserLocation userLocation = commonHelper.buildUserLocationFromHeader(httpHeaderMap);
				req.setUserLocation(userLocation);
			}
			ValidateCouponResponse validateCouponResponse = discountServiceExecutor.getValidateCouponResponse(req,parameterMap);

			boolean showBnplCard = Utility.isShowBnplCard(validateCouponRequest.getFeatureFlags());

			response = discountServiceFactory.getResponseService(client).convertValidateCouponResponse(validateCouponResponse, validateCouponRequest.getExpData(), validateCouponRequest.getCountryCode(), showBnplCard,client);

			if (CollectionUtils.isEmpty(validateCouponResponse.getHydraSegments()) &&
					!client.equalsIgnoreCase(Constants.DEVICE_IOS) && !client.equalsIgnoreCase(Constants.DEVICE_OS_ANDROID)) {
				// If LoggedOut Bnpl experiment is true, we send all the bnpl details
				if (validateCouponResponse.getLoggedOutBnplWebExp()) {
					if (response.getBnplDetails() != null) response.getBnplDetails().setLoggedOutBnplWebExp(true);
				} else {
					BNPLDetails bnplDetails = new BNPLDetails();
					bnplDetails.setBnplVariant(response.getBnplDetails() != null ? response.getBnplDetails().getBnplVariant() : BNPLVariant.BNPL_NOT_APPLICABLE.toString());
					response.setBnplDetails(bnplDetails);
				}
			}
			updateCouponsWithApplicabilityText(response);
		} catch (Exception e) {
        	if (e instanceof ErrorResponseFromDownstreamException) {
        		LOGGER.error("error occurred in validateCoupon: " + e.getMessage());
        		LOGGER.debug("error occurred in validateCoupon: " + e.getMessage(), e);
        	}else 
        		LOGGER.error("error occurred in validateCoupon: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
		}

		return response;
	}

	private void updateCouponsWithApplicabilityText(ValidateCouponResponseBody validateCouponResponseBody) {
		if(validateCouponResponseBody.getTotalPricing()!=null){
			List<Coupon> couponsList = validateCouponResponseBody.getTotalPricing().getCoupons();
			if (couponsList != null && !couponsList.isEmpty()) {
				HCouponApplicableTextCreator couponApplicableTextCreator = new HCouponApplicableTextCreator();
				couponsList.forEach(coupon -> {
					String applicabilityText = couponApplicableTextCreator.getText(validateCouponResponseBody.getBnplDetails(), coupon.isGiftCardAllowed(), coupon.isBnplAllowed(), polyglotService);
					coupon.setCouponApplicabilityText(applicabilityText);
				});
			}
		}
	}

}
