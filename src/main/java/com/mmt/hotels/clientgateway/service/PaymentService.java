package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.OTPAuthenticationException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.OfferDetailsRequest;
import com.mmt.hotels.clientgateway.request.payment.PaymentRequestClient;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.restexecutors.HydraExecutor;
import com.mmt.hotels.clientgateway.restexecutors.PaymentExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.transformer.factory.PaymentFactory;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse;
import com.mmt.propertymanager.config.PropertyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.enums.MarshallingErrors.NO_DATA_FOUND;

@Component
public class PaymentService {
    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private PropertyManager propertyManager;

    @Autowired
    private ObjectMapperUtil mapper;

    @Value("${payment.checkout.url}")
    private String paymenturl;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    UserServiceExecutor userService;

    @Autowired
    PaymentExecutor paymentExecutor;

    @Autowired
    HydraExecutor hydraExecutor;

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    PaymentFactory paymentFactory;
    
    @Autowired
    ObjectMapperUtil objectMapperUtil;

    private static final Logger logger = LoggerFactory.getLogger(PaymentService.class);

    public PaymentCheckoutResponse paymentCheckoutOld(BeginCheckoutReqBody paymentRequest,
                                                      HttpServletRequest httpServletRequest) throws ClientGatewayException {

        PaymentCheckoutResponse paymentResponse = null;
        try {
            BeginCheckoutReqBody beginCheckoutReqBody = paymentFactory.getRequestService("").modifyPaymentRequest(paymentRequest,httpServletRequest);
            paymentResponse = paymentExecutor.beginPaymentCheckout(beginCheckoutReqBody, HeadersUtil.getHeadersFromServletRequest(httpServletRequest));
            if (paymentResponse == null) {
                logger.error("Empty Response found from the webapi");
            }
        }catch (OTPAuthenticationException e){
            logger.error("OTP Validation required" , e.getMessage());
            return populateOtpFailedErrorResponse();

        }catch (Throwable e) {
            logger.error("error occured in paymentCheckout: " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return paymentResponse;
    }

    public PaymentResponse paymentCheckout(PaymentRequestClient paymentRequest, HttpServletRequest httpRequest, String client , String version) throws ClientGatewayException{

        PaymentResponse paymentResponse = null;
        try {
        	String request = objectMapperUtil.getJsonFromObject(paymentRequest, DependencyLayer.CLIENTGATEWAY);
            logger.warn("Client Request for Payment Checkout:- " + request);
            BeginCheckoutReqBody beginCheckoutReqBody = paymentFactory.getRequestService(client).modifyPaymentRequest(paymentRequest, httpRequest, client);
            PaymentCheckoutResponse response = paymentExecutor.beginPaymentCheckout(beginCheckoutReqBody, HeadersUtil.getHeadersFromServletRequest(httpRequest));
            paymentResponse =  paymentFactory.getResponseService(client).processResponse(response, beginCheckoutReqBody);
            if (paymentResponse == null) {
                logger.error("Empty Response found from the webapi");
            }
        }catch (OTPAuthenticationException e){
            logger.error("OTP Validation required" , e.getMessage());
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();

        } catch (Throwable e) {
            logger.error("error occured in paymentCheckout: " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

        return  paymentResponse;
    }




    private PaymentCheckoutResponse populateOtpFailedErrorResponse() {

        PaymentCheckoutResponse paymentResponse = new PaymentCheckoutResponse();
        List<Error> erroList = new ArrayList<Error>();
        erroList.add(new Error.Builder().buildErrorCode("3999","This user required OTP validation").build());
        ResponseErrors responseErrors = new ResponseErrors.Builder().buildErrorList(erroList).build();
        paymentResponse.setResponseErrors(responseErrors);
        return paymentResponse;
    }

    /**
     * This method is used to get the offer details
     * @param offerDetailsRequest offer details
     * @param httpRequest http request
     * @return offer details response
     */
    public OfferDetailsResponse getOfferDetails(OfferDetailsRequest offerDetailsRequest, HttpServletRequest httpRequest) throws JsonParseException, RestConnectorException {
        Map<String, String> headerMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
        UserServiceResponse userServiceResponse = paymentHelper.getUserServiceResponseFromUUID(offerDetailsRequest, headerMap);
        if (userServiceResponse == null || userServiceResponse.getResult() == null || userServiceResponse.getResult().getExtendedUser() == null) {
            return OfferDetailsResponse.builder()
                    .responseErrors(new ResponseErrors.Builder()
                            .buildErrorList(Collections.singletonList(new Error.Builder()
                                    .buildErrorCode(NO_DATA_FOUND.getErrorCode(), "Error from User service")
                                    .build())).build())
                    .build();
        }
        HydraResponse hydraResponse = commonHelper.executeHydraService(null, null, offerDetailsRequest.getCorrelationKey(), null, 1, null, userServiceResponse.getResult().getExtendedUser(), headerMap, null);
        return paymentExecutor.getOfferDetails(offerDetailsRequest.getRequester(), hydraResponse, userServiceResponse.getResult().getExtendedUser(), headerMap, offerDetailsRequest.getBookingId());
    }
}