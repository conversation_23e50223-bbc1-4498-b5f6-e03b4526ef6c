package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class HydraLastBookedEntity {
	
	@JsonProperty("to_city")
	private String toCity;
	@JsonProperty("from_city")
	private String fromCity;
	@JsonProperty("departure_date")
	private long depDateTimeStamp;

}
