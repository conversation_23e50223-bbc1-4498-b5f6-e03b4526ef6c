package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Hover {

    private String tooltipType;
    private Object data;
    private String headingText;
    private PersuasionStyle style;
    private String ctaText;
    private Integer openHoverThreshold;
    private PersuasionStyle headingStyle;
    private String ctaColor;

    public String getTooltipType() {
        return tooltipType;
    }

    public void setTooltipType(String tooltipType) {
        this.tooltipType = tooltipType;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }


}
