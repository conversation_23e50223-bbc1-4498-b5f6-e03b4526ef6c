package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PokusExperimentDetails {
	
	@JsonProperty("exp_experiment_id")
    private int experimentId;
    @JsonProperty("exp_experiment_version")
    private int experimentVersion;
    @JsonProperty("exp_experiment_variant_id")
    private int variantId;

}
