package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PersuasionData {
    private String id;
    private String persuasionType;
    private String text;
    private boolean hasAction;
    private boolean html;
    private String iconurl;
    private PersuasionStyle style;
    private PersuasionStyle topLevelStyle; //New node added for Homestays Persuasion
    private String icontype;
    private Hover hover;
    private String actionType;
}
