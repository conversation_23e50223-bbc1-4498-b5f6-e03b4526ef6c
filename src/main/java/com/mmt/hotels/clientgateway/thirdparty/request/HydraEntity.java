package com.mmt.hotels.clientgateway.thirdparty.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class HydraEntity {
	
	@JsonProperty("user_lob")
	private List<String> userLob;
	
	@JsonProperty("user_lob_city")
	private List<String> userLobCity;
	
	@JsonProperty("user_lob_locuscity")
	private List<String> userLobLocusCity;
	
	@JsonProperty("user_lob_region")
	private List<String> userLobRegion;
	
	@JsonProperty("device_lob")
	private List<String> deviceLob;
	
	@JsonProperty("device_lob_city")
	private List<String> deviceLobCity;
	
	@JsonProperty("device_lob_locuscity")
	private List<String> deviceLobLocusCity;
	
	@JsonProperty("device_lob_region")
	private List<String> deviceLobRegion;

}
