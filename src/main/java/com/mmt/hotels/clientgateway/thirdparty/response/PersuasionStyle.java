package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PersuasionStyle {
    List<String> styleClasses;
    String textColor;
    private String fontType;
    private String fontSize;
    private String borderColor;
    private String bgColor;
    private String bgUrl;
    private Integer iconWidth;
    private Integer iconHeight;
    private String iconUrl;
    private BorderGradient bgGradient;
    private String cornerRadii;
}
