package com.mmt.hotels.clientgateway.thirdparty.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class HydraUserStateRequest {

    private HydraUserIdDTO userId;

    private List<String> lobs;

    @JsonIgnore
    private String couponCode;

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @JsonIgnore
    private String correlationKey;

    public String getCorrelationKey() {
        return correlationKey;
    }

    public void setCorrelationKey(String correlationKey) {
        this.correlationKey = correlationKey;
    }

    @JsonProperty("user_id")
    public HydraUserIdDTO getUserId() {
        return userId;
    }

    @JsonProperty("user_id")
    public void setUserId(HydraUserIdDTO userId) {
        this.userId = userId;
    }

    public List<String> getLobs() {
        return lobs;
    }

    public void setLobs(List<String> lobs) {
        this.lobs = lobs;
    }

    @Override
    public String toString() {
        return "HydraUserStateRequest [userId=" + userId + ", lobs=" + lobs + ", couponCode=" + couponCode + ", correlationKey=" + correlationKey + "]";
    }

}