package com.mmt.hotels.clientgateway.thirdparty.response;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PokusAssignVariantResponse {
	
	private Map<String,Object> metadataValues;
    private String variantKey;
    @JsonProperty("exp_experiment_details_list")
    private List<PokusExperimentDetails> experimentDetailsList;

}
