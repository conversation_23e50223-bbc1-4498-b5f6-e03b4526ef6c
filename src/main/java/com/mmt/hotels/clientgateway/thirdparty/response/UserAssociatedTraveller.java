package com.mmt.hotels.clientgateway.thirdparty.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class UserAssociatedTraveller {

	private Integer travellerId;
    private Integer age;
    private Integer dateOfBirth;
    private String gender;
    private UserName name;
    private List<UserTravelDocument> travelDocuments = null;
    
}
