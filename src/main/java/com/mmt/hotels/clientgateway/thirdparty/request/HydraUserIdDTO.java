package com.mmt.hotels.clientgateway.thirdparty.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class HydraUserIdDTO {

    private String deviceId;
    private String uuid;
    private String profileType;

    @JsonProperty("visitor_id")
    private String visitorId; //same as mcid, hydra calls it visitor_id

    @JsonProperty("device_id")
    public String getDeviceId() {
        return deviceId;
    }

    @JsonProperty("device_id")
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @JsonProperty("profile_type")
    public String getProfileType() {
        return profileType;
    }

    @JsonProperty("profile_type")
    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getVisitorId() {
        return visitorId;
    }

    public void setVisitorId(String visitorId) {
        this.visitorId = visitorId;
    }

    @Override
    public String toString() {
        return "HydraUserIdDTO [deviceId=" + deviceId + ", uuid=" + uuid + ", profileType=" + profileType + "]";
    }

}