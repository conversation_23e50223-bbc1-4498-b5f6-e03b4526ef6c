<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
	<id>bin</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<dependencySets>
		<dependencySet>
			<outputDirectory>opt/mmtwebapps8/${project.artifactId}</outputDirectory>
			<unpack>true</unpack>
			<scope>runtime</scope>
			<includes>
				<include>${project.groupId}:${project.artifactId}:*:${project.version}</include>
			</includes>
		</dependencySet>
	</dependencySets>
	<files>
		<file>
			<source>releasenotes.txt</source>
			<outputDirectory>opt/mmtwebapps8/${project.artifactId}</outputDirectory>
			<filtered>true</filtered>
		</file>
	</files>
</assembly>