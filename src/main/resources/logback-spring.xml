<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<appender name="clientGatewayLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>/opt/logs/tomcat/hotels-gi-clientgateway.log</file>
		<append>true</append>
		<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
			<fileNamePattern>/opt/logs/tomcat/hotels-gi-clientgateway.log.%i.gz</fileNamePattern>
		</rollingPolicy>
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>500MB</maxFileSize>
		</triggeringPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%date [client:%X{clientIP}] [%thread] %-5level %logger{36} - %msg [unique_request_id:%X{correlation}]%n</pattern>
		</encoder>
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="com.mmt.hotels.clientgateway.util.logging.PatternMaskingLayout">
				<maskPattern>(\w+\.?\w+@\w+[\.-]?\w+\.\w+)</maskPattern>
				<maskPattern>[\"\'](\d{10}|\d{12})[\"\']</maskPattern>
				<maskPattern>([A-Z]{5}[0-9]{4}[A-Z]{1})</maskPattern>
				<maskPattern>\"firstName\"\s*:\s*\"(.*?)\"</maskPattern>
				<maskPattern>\"lastName\"\s*:\s*\"(.*?)\"</maskPattern>
				<maskPattern>\"middleName\"\s*:\s*\"(.*?)\"</maskPattern>
				<pattern>%date [client:%X{clientIP}] [%thread] %-5level %logger{36} - %msg [unique_request_id:%X{correlation}]%n</pattern>
			</layout>
		</encoder>
	</appender>

	<appender name="AsyncLog" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="clientGatewayLog" />
	</appender>


	<springProfile name="dev">
		<logger name="com.mmt.hotels" level="debug" additivity="false">
			<appender-ref ref="clientGatewayLog" />
		</logger>
	</springProfile>

	<springProfile name="!dev">
		<logger name="com.mmt.hotels" level="warn" additivity="false">
			<appender-ref ref="clientGatewayLog" />
		</logger>
	</springProfile>
</configuration>
