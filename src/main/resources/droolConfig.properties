sequencing.drool.cache.path=/opt/tomcat/tomcat8/rulebase/cache/SequencingExperiments
sequencing.drools.agent.scan.resources=true
sequencing.drools.agent.scan.directories=true
sequencing.drools.resource.scanner.interval=1800
sequencing.drools.agent.monitor.change.set.events=true
sequencing.drools.knowledge.agent.name=Hotel_Sequencing_Knowledge_agent
sequencing.drools.base.knowledge.agent.change.set.path=HotelSequencing.xml


persuasion.drool.cache.path=/opt/tomcat/tomcat8/rulebase/cache/Persuasion
persuasion.drools.agent.scan.resources=true
persuasion.drools.agent.scan.directories=true
persuasion.drools.resource.scanner.interval=1800
persuasion.drools.agent.monitor.change.set.events=true
persuasion.drools.knowledge.agent.name=Persuasion_Knowledge_agent
persuasion.drools.base.knowledge.agent.change.set.path=Persuasion.xml


fraudscore.drool.cache.path=/opt/tomcat/tomcat8/rulebase/cache/SequencingExperiments
fraudscore.drools.agent.scan.resources=true
fraudscore.drools.agent.scan.directories=true
fraudscore.drools.resource.scanner.interval=1800
fraudscore.drools.agent.monitor.change.set.events=true
fraudscore.drools.knowledge.agent.name=Fraud_Score_Knowledge_agent
fraudscore.drools.base.knowledge.agent.change.set.path=FraudScore.xml


cbAffiliateId.drool.cache.path=/opt/tomcat/tomcat8/rulebase/cache/SequencingExperiments
cbAffiliateId.drools.agent.scan.resources=true
cbAffiliateId.drools.agent.scan.directories=true
cbAffiliateId.drools.resource.scanner.interval=1800
cbAffiliateId.drools.agent.monitor.change.set.events=true
cbAffiliateId.drools.knowledge.agent.name=affiliate_ID_Knowledge_agent
cbAffiliateId.drools.base.knowledge.agent.change.set.path=CBHotelAffiliateId.xml

cbPersuasionSegment.drool.cache.path=/opt/tomcat/tomcat8/rulebase/cache/Persuasion
cbPersuasionSegment.drools.agent.scan.resources=true
cbPersuasionSegment.drools.agent.scan.directories=true
cbPersuasionSegment.drools.resource.scanner.interval=1800
cbPersuasionSegment.drools.agent.monitor.change.set.events=true
cbPersuasionSegment.drools.knowledge.agent.name=persuasion_segment_Knowledge_agent
cbPersuasionSegment.drools.base.knowledge.agent.change.set.path=CBPersuasionSegment.xml
        
cbListingPersonalization.drool.cache.path=/opt/tomcat/tomcat8/rulebase/cache/SequencingExperiments
cbListingPersonalization.drools.agent.scan.resources=true
cbListingPersonalization.drools.agent.scan.directories=true
cbListingPersonalization.drools.resource.scanner.interval=1800
cbListingPersonalization.drools.agent.monitor.change.set.events=true
cbListingPersonalization.drools.knowledge.agent.name=listPersonalization_Knowledge_agent
cbListingPersonalization.drools.base.knowledge.agent.change.set.path=CBListingPersonalization.xml

droolCollection=sequencing,persuasion,fraudscore,cbAffiliateId,cbPersuasionSegment,cbListingPersonalization

#Drools/kie-workbench common properties
drools.workbench.projects.group.name=com.mmt.hotels

#Drools project specific properties
drools.affiliate.kjar.artifact.name=CBHotelAffiliateId
drools.affiliate.kjar.artifact.update.interval=1800000

drools.listing.personalization.kjar.artifact.name=CBListingPersonalization
drools.listing.personalization.kjar.artifact.update.interval=1800000

drools.fraud.score.kjar.artifact.name=CBFraudBucket
drools.fraud.score.kjar.artifact.update.interval=1800000

drools.sequencing.kjar.artifact.name=CBHotelSequencing
drools.sequencing.kjar.artifact.update.interval=1800000
