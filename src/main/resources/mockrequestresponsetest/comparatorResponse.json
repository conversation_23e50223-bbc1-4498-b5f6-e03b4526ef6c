{"hotelSearchResponse": {"hotelList": [{"addOnAvailable": false, "addOnAvailableOnLowest": false, "address": {"area": ["Connaught Place", "Central Delhi", "Barakhamba"], "line1": "Barakhamba Avenue, Connaught Place, City center, New Delhi", "line2": "Connaught Place"}, "altAcco": false, "alternateDatesAvailable": false, "avgTP": 0.0, "bedCount": 0, "bedRoomCount": 0, "bestPriceGuaranteed": false, "blackAccelerated": false, "blackPackageAvailable": false, "bnplBaseAmount": 1.0, "bnplPersuasionMsg": "Free Cancellation, Zero Payment Now", "breakFast": true, "breakFastAvailable": true, "cancellationTimeline": {"bookingDate": "06 Aug", "cancellationDate": "24 Sep", "cardChargeDate": "22 Sep", "cardChargeDateLong": "22 Sep, 2020", "cardChargeText": "Credit Card is charged", "checkInDate": "25 Sep", "freeCancellationText": "Free Cancellation till 24 Sep 02:00 PM", "subTitle": "Free Cancellation, Zero Payment Now", "title": "STAY FLEXIBLE WITH"}, "categories": ["Chain", "HighFiveV2_Chain", "MySafety - Safe and Hygienic Stays", "Culinary", "Great Value Deals", "MMT Assured", "Premium", "Child Friendly", "MyBiz Assured", "MMTFest", "Couple Friendly", "Spa and Wellness", "Luxurious Business Stays", "DEALS", "City Center Hotels"], "categoryDetails": {"MySafety - Safe and Hygienic Stays": {"data": ["Hygienic Rooms", "Trained Staff", "Sanitized Indoors", "Safe Dining"], "iconUrl": "https://promos.makemytrip.com/COVID/safe.png", "itemIconType": "", "title": "This property is following safety and hygiene measures"}}, "cityCode": "CTDEL", "cityName": "Delhi", "countryCode": "IN", "countryName": "India", "couponAutoApply": true, "currencyCode": {"id": "INR", "value": "INR"}, "displayFare": {"aajKaBhaoApplied": "NA", "actualPrice": {"averagePriceWithTax": 8000.0, "avgeragePriceNoTax": 8000.0, "maxFraction": 0.0, "sellingPriceNoTax": 8000.0, "sellingPriceWithTax": 8000.0, "value": 8000.0}, "apPromotionDiscount": 0.0, "conversionFactor": 0.0, "couponReceived": false, "couponSkipped": false, "dateOfDelayedPayment": 1600763400000, "ddApplied": false, "ddmu": 0.0, "displayPriceBreakDown": {"addonPrice": 0.0, "affiliateFee": 0.0, "basePrice": 8000.0, "brinInclusivePrice": 0.0, "cdfDiscount": 0.0, "displayPrice": 8000.0, "displayPriceAlternateCurrency": 0.0, "effectivePrice": 8000.0, "extraPaxPrice": 0.0, "hotelServiceCharge": 0.0, "hotelTax": 1440.0, "metaDiscount": 0.0, "mmtDiscount": 0.0, "mmtServiceCharge": 0.0, "nonDiscountedAddonPrice": 0.0, "nonDiscountedPrice": 8000.0, "pricingDivisor": 1, "pricingKey": "s9rHC9RN7n/fpdE/4yI1w2tWICQex2PEq2Vd4Gwgf1jQAD0ilJuSasGpr0ADa1Cd3xMSCL2g+DPSHhNXxVI5azc9GekMipdK9uf+7rCWW+X0JYR7i3n8CFAaBkVIr63nRnxoh2PALNKN+LMkahIQym5TKkG8zpyTcGF1y6simfnM9dZFxwWnHMSNBAuuQyMn5IxaUnbFAXYVO0GTokUhrvAAq23Q8UYPaXCxJDAUe6mHx6oPhop5CC90M5w0fmCD", "savingPerc": 0.0, "taxIncluded": false, "totalAmount": 8000.0, "totalSaving": 0.0, "totalTax": 1440.0, "wallet": 0.0}, "extraAdult": {"value": 0.0}, "hotelTax": 1440.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": true, "originalBNPL": true, "segmentId": "1120", "slashedPrice": {"averagePriceWithTax": 8000.0, "avgeragePriceNoTax": 8000.0, "maxFraction": 0.0, "sellingPriceNoTax": 8000.0, "sellingPriceWithTax": 8000.0, "value": 8000.0}, "tax": {"value": 0.0}, "taxExcluded": false, "totalRoomCount": 1}, "dynamicFilters": ["Utility", "USP", "NightLife/Dining", "Location"], "facilityCategorization": [{"name": "hotel", "subcat": "Thermal screening at entry and exit points"}, {"name": "hotel", "subcat": "Sanitizers installed"}, {"name": "hotel", "subcat": "Wifi"}, {"name": "hotel", "subcat": "Room service"}, {"name": "hotel", "subcat": "Power backup"}, {"name": "hotel", "subcat": "Elevator/ Lift"}, {"name": "hotel", "subcat": "Air Conditioning"}, {"name": "hotel", "subcat": "Swimming Pool"}, {"name": "hotel", "subcat": "Smoke detector"}, {"name": "hotel", "subcat": "<PERSON><PERSON><PERSON>"}, {"name": "hotel", "subcat": "Parking"}, {"name": "hotel", "subcat": "Intercom"}, {"name": "hotel", "subcat": "Refrigerator"}, {"name": "hotel", "subcat": "Housekeeping"}, {"name": "hotel", "subcat": "Newspaper"}, {"name": "hotel", "subcat": "Public restrooms"}, {"name": "hotel", "subcat": "Telephone"}, {"name": "hotel", "subcat": "Ironing services"}, {"name": "hotel", "subcat": "Bathroom"}, {"name": "hotel", "subcat": "Shuttle Service"}, {"name": "hotel", "subcat": "Airport Transfers"}, {"name": "hotel", "subcat": "Childcare service"}, {"name": "hotel", "subcat": "Restaurant"}, {"name": "hotel", "subcat": "Bar"}, {"name": "hotel", "subcat": "Cafe"}, {"name": "hotel", "subcat": "ATM"}, {"name": "hotel", "subcat": "Currency Exchange"}, {"name": "hotel", "subcat": "Safe"}, {"name": "hotel", "subcat": "Security"}, {"name": "hotel", "subcat": "Gym/ Fitness centre"}, {"name": "hotel", "subcat": "Night Club"}, {"name": "hotel", "subcat": "Events"}, {"name": "hotel", "subcat": "Electrical Adapters Available"}, {"name": "hotel", "subcat": "Electrical chargers"}, {"name": "hotel", "subcat": "Luggage storage"}, {"name": "hotel", "subcat": "Concierge"}, {"name": "hotel", "subcat": "Wheelchair"}, {"name": "hotel", "subcat": "Multilingual Staff"}, {"name": "hotel", "subcat": "Mail services"}, {"name": "hotel", "subcat": "Luggage assistance"}, {"name": "hotel", "subcat": "Ticket/ Tour Assistance"}, {"name": "hotel", "subcat": "Bellboy service"}, {"name": "hotel", "subcat": "Wake-up Call / Service"}, {"name": "hotel", "subcat": "Electrical Sockets"}, {"name": "hotel", "subcat": "Salon"}, {"name": "hotel", "subcat": "Snorkelling"}, {"name": "hotel", "subcat": "Lawn"}, {"name": "hotel", "subcat": "Reception"}, {"name": "hotel", "subcat": "Library"}, {"name": "hotel", "subcat": "Fireplace"}, {"name": "hotel", "subcat": "Shops"}, {"name": "hotel", "subcat": "Conference room"}, {"name": "hotel", "subcat": "Photocopying"}, {"name": "hotel", "subcat": "Business Center"}, {"name": "hotel", "subcat": "Banquet"}, {"name": "hotel", "subcat": "Fax service"}], "facilityHighlights": ["Childcare Services", "Free Wi-Fi", "Restaurant", "Conference Room", "Room Service", "Power Backup", "Lawn", "Elevator/Lift", "Bar", "Cafe", "Air Conditioning", "Swimming Pool", "Gym"], "featured": "N", "flyfishReviewSummary": {"MMT": {"crawledData": false, "cumulativeRating": 4.3, "sortingCriterion": ["Latest first", "Helpful first", "Positive first", "Negative first"], "totalRatingCount": 3370, "totalReviewsCount": 2187, "travellerRatingSummary": {"hotelSummary": [{"concept": "Safety", "heroTag": true, "reviewCount": 1, "show": true, "value": 4.0}, {"concept": "Security", "heroTag": false, "reviewCount": 1, "show": true, "value": 5.0}, {"concept": "Location", "heroTag": false, "reviewCount": 1059, "show": true, "subConcepts": [{"priorityScore": 443, "relatedReviewCount": 443, "sentiment": "POSITIVE", "subConcept": "Location", "tagType": "BASE"}, {"priorityScore": 99, "relatedReviewCount": 99, "sentiment": "POSITIVE", "subConcept": "Central Location", "tagType": "BASE"}, {"priorityScore": 38, "relatedReviewCount": 38, "sentiment": "POSITIVE", "subConcept": "Connectivity", "tagType": "BASE"}, {"priorityScore": 22, "relatedReviewCount": 22, "sentiment": "POSITIVE", "subConcept": "Market", "tagType": "BASE"}, {"priorityScore": 16, "relatedReviewCount": 16, "sentiment": "POSITIVE", "subConcept": "Nightlife", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Business District", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "NEUTRAL", "subConcept": "Eating Options", "tagType": "BASE"}, {"priorityScore": 442, "relatedReviewCount": 442, "sentiment": "POSITIVE", "subConcept": "Good Location", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 99, "relatedReviewCount": 99, "sentiment": "POSITIVE", "subConcept": "Centrally Located", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 36, "relatedReviewCount": 36, "sentiment": "POSITIVE", "subConcept": "Well Connected", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 22, "relatedReviewCount": 22, "sentiment": "POSITIVE", "subConcept": "Near Market", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 13, "relatedReviewCount": 13, "sentiment": "POSITIVE", "subConcept": "Near Pubs & Bars", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.7}, {"concept": "Cleanliness", "heroTag": false, "reviewCount": 2183, "show": true, "subConcepts": [{"priorityScore": 122, "relatedReviewCount": 122, "sentiment": "POSITIVE", "subConcept": "Room Cleanliness", "tagType": "BASE"}, {"priorityScore": 17, "relatedReviewCount": 17, "sentiment": "POSITIVE", "subConcept": "Hotel Cleanliness", "tagType": "BASE"}, {"priorityScore": 7, "relatedReviewCount": 7, "sentiment": "POSITIVE", "subConcept": "Pool Cleanliness", "tagType": "BASE"}, {"priorityScore": 4, "relatedReviewCount": 4, "sentiment": "POSITIVE", "subConcept": "Bathroom Cleanliness", "tagType": "BASE"}, {"priorityScore": 4, "relatedReviewCount": 4, "sentiment": "NEUTRAL", "subConcept": "Linen Cleanliness", "tagType": "BASE"}, {"priorityScore": 4, "relatedReviewCount": 4, "sentiment": "POSITIVE", "subConcept": "Food Cleanliness", "tagType": "BASE"}, {"priorityScore": 3, "relatedReviewCount": 3, "sentiment": "POSITIVE", "subConcept": "Environment Cleanliness", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Property Cleanliness", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Area Cleanliness", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Place Cleanliness", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Toilet Cleanliness", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "NEGATIVE", "subConcept": "<PERSON><PERSON><PERSON>", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Stay Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Ambience Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Restaurant Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Atmosphere Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Lobby Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Location Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Bath Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Staff Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Bed Cleanliness", "tagType": "BASE"}, {"priorityScore": 10000, "relatedReviewCount": 121, "sentiment": "POSITIVE", "subConcept": "Clean Room", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 9900, "relatedReviewCount": 17, "sentiment": "POSITIVE", "subConcept": "Clean Property", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 7, "relatedReviewCount": 7, "sentiment": "POSITIVE", "subConcept": "Clean Pool", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.5}, {"concept": "Hospitality", "heroTag": false, "reviewCount": 1987, "show": true, "subConcepts": [{"priorityScore": 523, "relatedReviewCount": 523, "sentiment": "POSITIVE", "subConcept": "Staff Courtesy", "tagType": "BASE"}, {"priorityScore": 357, "relatedReviewCount": 357, "sentiment": "POSITIVE", "subConcept": "Service Quality", "tagType": "BASE"}, {"priorityScore": 20, "relatedReviewCount": 20, "sentiment": "POSITIVE", "subConcept": "Check-in", "tagType": "BASE"}, {"priorityScore": 508, "relatedReviewCount": 508, "sentiment": "POSITIVE", "subConcept": "Courteous Staff", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 295, "relatedReviewCount": 295, "sentiment": "POSITIVE", "subConcept": "Good Service", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 13, "relatedReviewCount": 13, "sentiment": "POSITIVE", "subConcept": "Good Check-In", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.3}, {"concept": "Room", "heroTag": false, "reviewCount": 1226, "show": true, "subConcepts": [{"priorityScore": 288, "relatedReviewCount": 288, "sentiment": "POSITIVE", "subConcept": "Room Quality", "tagType": "BASE"}, {"priorityScore": 127, "relatedReviewCount": 127, "sentiment": "POSITIVE", "subConcept": "Space in Rooms", "tagType": "BASE"}, {"priorityScore": 33, "relatedReviewCount": 33, "sentiment": "NEUTRAL", "subConcept": "Bed Quality", "tagType": "BASE"}, {"priorityScore": 10, "relatedReviewCount": 10, "sentiment": "NEGATIVE", "subConcept": "Room Amenities", "tagType": "BASE"}, {"priorityScore": 8, "relatedReviewCount": 8, "sentiment": "NEUTRAL", "subConcept": "Bathroom Hygiene", "tagType": "BASE"}, {"priorityScore": 374, "relatedReviewCount": 374, "sentiment": "POSITIVE", "subConcept": "Good Room", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 13, "relatedReviewCount": 13, "sentiment": "POSITIVE", "subConcept": "Comfortable Bed", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 8, "relatedReviewCount": 8, "sentiment": "NEGATIVE", "subConcept": "Poor In-Room Amenities", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 7, "relatedReviewCount": 7, "sentiment": "POSITIVE", "subConcept": "Clean Bathroom", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.3}, {"concept": "Facilities", "heroTag": false, "reviewCount": 1318, "show": true, "subConcepts": [{"priorityScore": 48, "relatedReviewCount": 48, "sentiment": "POSITIVE", "subConcept": "Pool", "tagType": "BASE"}, {"priorityScore": 15, "relatedReviewCount": 15, "sentiment": "POSITIVE", "subConcept": "Gym", "tagType": "BASE"}, {"priorityScore": 11, "relatedReviewCount": 11, "sentiment": "POSITIVE", "subConcept": "Spa/Massage", "tagType": "BASE"}, {"priorityScore": 10, "relatedReviewCount": 10, "sentiment": "NEUTRAL", "subConcept": "<PERSON><PERSON>", "tagType": "BASE"}, {"priorityScore": 9, "relatedReviewCount": 9, "sentiment": "NEUTRAL", "subConcept": "Wifi", "tagType": "BASE"}, {"priorityScore": 3, "relatedReviewCount": 3, "sentiment": "NEGATIVE", "subConcept": "Room Heating", "tagType": "BASE"}, {"priorityScore": 3, "relatedReviewCount": 3, "sentiment": "POSITIVE", "subConcept": "In-House Activities", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Airport Transfers", "tagType": "BASE"}, {"priorityScore": 45, "relatedReviewCount": 45, "sentiment": "POSITIVE", "subConcept": "Good Pool", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 15, "relatedReviewCount": 15, "sentiment": "POSITIVE", "subConcept": "Good Gym", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 6, "relatedReviewCount": 6, "sentiment": "POSITIVE", "subConcept": "Good Wifi", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.3}, {"concept": "Food", "heroTag": false, "reviewCount": 1514, "show": true, "subConcepts": [{"priorityScore": 312, "relatedReviewCount": 312, "sentiment": "POSITIVE", "subConcept": "Food", "tagType": "BASE"}, {"priorityScore": 172, "relatedReviewCount": 172, "sentiment": "POSITIVE", "subConcept": "Breakfast", "tagType": "BASE"}, {"priorityScore": 38, "relatedReviewCount": 38, "sentiment": "NEUTRAL", "subConcept": "Restaurant", "tagType": "BASE"}, {"priorityScore": 4, "relatedReviewCount": 4, "sentiment": "POSITIVE", "subConcept": "Vegetarian Food", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "European Food", "tagType": "BASE"}, {"priorityScore": 301, "relatedReviewCount": 301, "sentiment": "POSITIVE", "subConcept": "Good Food", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 162, "relatedReviewCount": 162, "sentiment": "POSITIVE", "subConcept": "Good Breakfast", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 37, "relatedReviewCount": 37, "sentiment": "POSITIVE", "subConcept": "Good Restaurant", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.2}, {"concept": "Value for Money", "heroTag": false, "reviewCount": 911, "show": true, "subConcepts": [{"priorityScore": 98, "relatedReviewCount": 98, "sentiment": "POSITIVE", "subConcept": "Value for Money", "tagType": "BASE"}, {"priorityScore": 18, "relatedReviewCount": 18, "sentiment": "NEUTRAL", "subConcept": "Luxury", "tagType": "BASE"}, {"priorityScore": 98, "relatedReviewCount": 98, "sentiment": "POSITIVE", "subConcept": "Value for Money", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.2}, {"concept": "Child friendliness", "heroTag": false, "reviewCount": 179, "show": true, "value": 4.1}], "roomSummary": {"1171538": {"best": [{"crawledData": false, "id": "P3GKA0DC3ZTVVC6PF54FMLUJ3BQPUKNUQ5IEOUVVCDLFKXUGKT0NS4KU64AZDUZKSXRF0OIRMTM3", "isUpvoted": false, "publishDate": "Feb 18, 2020", "rating": 5.0, "reviewText": "Extremely courteous staff, plenty of options in The breakfast and clean and awesome rooms. Worth a visit.", "title": "The Lalit New Delhi review", "travelType": "COUPLE", "travellerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"crawledData": false, "id": "P29KUZNIXNHPKAQ3UE9S3NBK9BV8U43UX3HPYSNJI4ZTVRAYNT5EHN2I62A0ZBV3SJYF4QIX8TXG", "isUpvoted": false, "publishDate": "Feb 24, 2020", "rating": 5.0, "reviewText": "Very clean room with great location in The heart of The Delhi.", "title": "Good food and staff", "travelType": "COUPLE", "travellerName": "<PERSON><PERSON>"}], "cumulativeRating": 0.0}, "13634": {"cumulativeRating": 0.0}, "16522": {"cumulativeRating": 0.0}, "168536": {"cumulativeRating": 0.0}, "169502": {"cumulativeRating": 0.0}, "169504": {"cumulativeRating": 0.0}, "169506": {"cumulativeRating": 0.0}, "169508": {"cumulativeRating": 0.0}, "17492": {"cumulativeRating": 0.0}, "193324": {"cumulativeRating": 0.0}, "193326": {"cumulativeRating": 0.0}, "200": {"cumulativeRating": 0.0}, "*********": {"cumulativeRating": 0.0}, "*********": {"cumulativeRating": 0.0}, "*********": {"cumulativeRating": 0.0}, "*********": {"cumulativeRating": 0.0}, "*********": {"cumulativeRating": 0.0}, "207": {"cumulativeRating": 0.0}, "239": {"cumulativeRating": 0.0}, "3": {"cumulativeRating": 0.0}, "322": {"cumulativeRating": 0.0}, "3354": {"cumulativeRating": 0.0}, "353": {"cumulativeRating": 0.0}, "45000001065": {"cumulativeRating": 0.0, "insights": [{"category": "LIKE_DISLIKE", "sentiment": "POSITIVE", "subConcept": "Cleanliness"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "All rooms come with great wifi reception", "tip": "All rooms come with great wifi reception"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "Clean bathrooms get a thumbs up from our guests", "tip": "Clean bathrooms get a thumbs up from our guests"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Carry your own toiletries as they could be missing in the room", "tip": "Carry your own toiletries as they could be missing in the room"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Complimentary water bottles may not be provided in the room", "tip": "Complimentary water bottles may not be provided in the room"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Don’t rely on the TV for entertainment as it is too small", "tip": "Don’t rely on the TV for entertainment as it is too small"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "You might face charging hassles due to fewer plug points in the room", "tip": "You might face charging hassles due to fewer plug points in the room"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "The room feels spacious even with an extra bed", "tip": "The room feels spacious even with an extra bed"}]}, "45000006491": {"cumulativeRating": 0.0, "insights": [{"category": "LIKE_DISLIKE", "sentiment": "POSITIVE", "subConcept": "Cleanliness"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Don’t rely on the TV for entertainment as it is too small", "tip": "Don’t rely on the TV for entertainment as it is too small"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "Clean bathrooms get a thumbs up from our guests", "tip": "Clean bathrooms get a thumbs up from our guests"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Carry your own toiletries as they could be missing in the room", "tip": "Carry your own toiletries as they could be missing in the room"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "You might face charging hassles due to fewer plug points in the room", "tip": "You might face charging hassles due to fewer plug points in the room"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "All rooms come with great wifi reception", "tip": "All rooms come with great wifi reception"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "The room feels spacious even with an extra bed", "tip": "The room feels spacious even with an extra bed"}]}, "45000006492": {"cumulativeRating": 0.0, "insights": [{"category": "LIKE_DISLIKE", "sentiment": "POSITIVE", "subConcept": "Cleanliness"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "All rooms come with great wifi reception", "tip": "All rooms come with great wifi reception"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "Clean bathrooms get a thumbs up from our guests", "tip": "Clean bathrooms get a thumbs up from our guests"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Don’t rely on the TV for entertainment as it is too small", "tip": "Don’t rely on the TV for entertainment as it is too small"}]}, "45000006493": {"cumulativeRating": 0.0, "insights": [{"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "All rooms come with great wifi reception", "tip": "All rooms come with great wifi reception"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Don’t rely on the TV for entertainment as it is too small", "tip": "Don’t rely on the TV for entertainment as it is too small"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "Clean bathrooms get a thumbs up from our guests", "tip": "Clean bathrooms get a thumbs up from our guests"}]}, "45000015997": {"cumulativeRating": 0.0, "insights": [{"category": "LIKE_DISLIKE", "sentiment": "POSITIVE", "subConcept": "Cleanliness"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "All rooms come with great wifi reception", "tip": "All rooms come with great wifi reception"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "Clean bathrooms get a thumbs up from our guests", "tip": "Clean bathrooms get a thumbs up from our guests"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Don’t rely on the TV for entertainment as it is too small", "tip": "Don’t rely on the TV for entertainment as it is too small"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "The room feels spacious even with an extra bed", "tip": "The room feels spacious even with an extra bed"}]}, "45000015998": {"cumulativeRating": 0.0, "insights": [{"category": "LIKE_DISLIKE", "sentiment": "POSITIVE", "subConcept": "Cleanliness"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "All rooms come with great wifi reception", "tip": "All rooms come with great wifi reception"}, {"category": "ROOM_TIP", "sentiment": "POSITIVE", "subConcept": "Clean bathrooms get a thumbs up from our guests", "tip": "Clean bathrooms get a thumbs up from our guests"}, {"category": "ROOM_TIP", "sentiment": "NEGATIVE", "subConcept": "Don’t rely on the TV for entertainment as it is too small", "tip": "Don’t rely on the TV for entertainment as it is too small"}]}, "45000015999": {"cumulativeRating": 0.0}, "45000016001": {"cumulativeRating": 0.0}, "45000016002": {"cumulativeRating": 0.0}, "45000016005": {"cumulativeRating": 0.0}, "45000083232": {"cumulativeRating": 0.0}, "475": {"cumulativeRating": 0.0}, "54379": {"cumulativeRating": 0.0}, "549": {"cumulativeRating": 0.0}, "557": {"cumulativeRating": 0.0}, "558": {"cumulativeRating": 0.0}, "559": {"cumulativeRating": 0.0}, "568": {"cumulativeRating": 0.0}, "56957": {"cumulativeRating": 0.0}, "646": {"cumulativeRating": 0.0}, "79": {"cumulativeRating": 0.0}, "793": {"cumulativeRating": 0.0}, "874": {"cumulativeRating": 0.0}, "968": {"cumulativeRating": 0.0}, "970": {"cumulativeRating": 0.0}}}}, "TA": {"crawledData": true, "cumulativeRating": 4.5, "totalRatingCount": 5517}}, "freeCancellationAvailable": true, "freeCancellationText": "Free Cancellation", "geoLocation": {"latitude": "28.631245", "longitude": "77.2274"}, "hasCollections": true, "hotelChainCode": "630962875", "hotelChainName": "The Lalit Groups", "hotelFacilities": "Safety and Hygiene,Basic Facilities,Transfers,Family and kids,Food and Drinks,Payment Services,Safety and Security,Health and wellness,Entertainment,Media and technology,General Services,Beauty and Spa,Outdoor Activities and Sports,Common Area,Shopping,Business Center and Conferences", "hotelVideos": [{"sequence": 0, "text": "Enjoy a residence-like stay at this centrally-located grand 5-star hotel", "thumbnailUrl": "https://media.makemytrip.com/mmt-s3-media/stream-media/200701211455342954/thumbnail/thumbnail.jpeg", "title": "An Award Winning Hotel", "url": "https://media.makemytrip.com/mmt-s3-media/stream-media/200701211455342954/htl_video/master.m3u8"}], "id": "200701211455342954", "ignoreEntireProperty": true, "isAbsorptionApplied": false, "isBNPLAvailable": true, "isFreeCancellation": true, "isFreeWifiAvail": true, "isMTKeyEnabledSearch": false, "isPAHAvailable": false, "isPAHTariffAvailable": false, "isShortlisted": false, "isSoldOut": false, "isValuePlus": false, "lastBooked": false, "listingType": "Room By Room", "lowestBlackPackage": false, "lowestNetRate": false, "lowestRateBnpl": true, "lowestRatePlanMealDiff": 0.0, "lowestRatePlanPahDiff": 0.0, "lowestRateStaycation": false, "lowestRateSupplierCode": "INGO", "lowestRoomAvailCount": 1, "lowestRoomCodesRatePlans": "1045473--990000001095:MSE:1120:MSE:ING", "mainImages": ["//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/200701211455342954-05055f5a7efe11e58eff001ec9b85d13.jpg?&output-quality=75&downsize=583:388&crop=583:388;0,87&output-format=jpg", "//r1imghtlak.mmtcdn.com/5835aef6acb411e7a63e02755708f0b3.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/5c653500acb411e793e70224510f5e5b.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/5b227252acb411e7818402755708f0b3.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/5b5d4f76acb411e79b5702755708f0b3.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/565b79eeacb411e7b5e10a209fbd0127.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/54f415b6acb411e7959e02755708f0b3.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/54c7ef54acb411e7a1eb0a209fbd0127.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/597b1c9cacb411e79b5702755708f0b3.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r1imghtlak.mmtcdn.com/5a14e8e0acb411e7bc990224510f5e5b.jpg?&output-quality=75&downsize=583:388&output-format=jpg"], "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "maxOccupancy": 0, "mealPlanIncluded": {"code": "CP", "cost": null, "desc": "Breakfast"}, "mtkey": "defaultMtkey", "name": "The LaLiT New Delhi", "netRateAvailable": false, "notInterested": false, "pahWalletApplicable": false, "pahmode": "PAS", "pahxApplicable": false, "pahxAvailable": false, "paymentMode": "PAS", "poiTag": "2.8 km from New Delhi Railway Station", "poiTagList": [{"bestPoiPriority": 0, "drivingDistance": "2750.45", "drivingDistanceText": "2.75 km", "drivingTimeText": "", "hotelId": "200701211455342954", "poiCategory": "Railway", "poiId": "POI47734", "poiName": "New Delhi Railway Station", "poiPriority": "0"}], "priceDifferenceWithPivot": 0.0, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotionsCount": 0}, "propertyActivities": [], "propertyRules": [], "propertyType": "Hotel", "recommendCouponBit": true, "recommendedMultiRoom": false, "roomCount": 0, "segments": {"segmentList": {"1120": {"channelSegment": "ALL", "id": "1120", "userSegment": "REGULAR"}, "1121": {"channelSegment": "MOB", "id": "1121", "userSegment": "REGULAR"}, "1126": {"channelSegment": "ALL", "id": "1126", "userSegment": "LOGGEDIN"}}, "segmentsCount": 3}, "shortDescription": "Location: In the heart of Connaught Place, Delhi's prime commercial and entertainment hub, Lalit hotel is the ideal base to expl ", "starRating": 5, "stateCode": "STDEL", "stateName": "Delhi", "stayType": "Hotel", "staycationAvailable": false, "totalRoomAvailCount": 22, "userEligiblePayMode": "PAS", "usp": {"Location": {"subTags": null, "sub_title": "A prime location in Connaught Place - New Delhi's shopping & entertainment hub", "title": "Location"}, "NightLife/Dining": {"subTags": null, "sub_title": "<PERSON>: a chic and innovative night club", "title": "NightLife/Dining"}, "USP": {"subTags": null, "sub_title": "An on-site art gallery with works of more than 5000 artists", "title": "USP"}, "Utility": {"subTags": null, "sub_title": "Oko: a Pan-Asian restaurant at level 28 with a panoramic view of Delhi's skyline", "title": "Utility"}}, "walletEntity": {"defaultWalletBurn": {"bonusPercentage": 0, "maxBonus": 4000, "minBookingAmount": 0}, "status": "N"}}, {"addOnAvailable": false, "addOnAvailableOnLowest": false, "address": {"area": ["Mahipalpur Village", "Block RZ", "Near IGI Airport", "Mahipalpur"], "line1": "Road no 4,Block A, Mahipalpur,Near Delhi Airport,New Delhi", "line2": "Mahipalpur"}, "altAcco": false, "alternateDatesAvailable": false, "avgTP": 0.0, "bedCount": 0, "bedRoomCount": 0, "bestPriceGuaranteed": false, "blackAccelerated": false, "blackPackageAvailable": false, "bnplBaseAmount": 1.0, "bnplPersuasionMsg": "Free Cancellation, Zero Payment Now", "breakFast": false, "breakFastAvailable": true, "cancellationTimeline": {"bookingDate": "06 Aug", "cancellationDate": "25 Sep", "cardChargeDate": "23 Sep", "cardChargeDateLong": "23 Sep, 2020", "cardChargeText": "Credit Card is charged", "checkInDate": "25 Sep", "freeCancellationText": "Free Cancellation till 25 Sep 12:00 PM", "subTitle": "Free Cancellation, Zero Payment Now", "title": "STAY FLEXIBLE WITH"}, "categories": ["MyBiz Assured", "MySafety - Safe and Hygienic Stays", "Couple Friendly", "Great Value Deals", "MMT Assured", "Inmarket"], "categoryDetails": {"MySafety - Safe and Hygienic Stays": {"data": ["Hygienic Rooms", "Trained Staff", "Sanitized Indoors", "Safe Dining"], "iconUrl": "https://promos.makemytrip.com/COVID/safe.png", "itemIconType": "", "title": "This property is following safety and hygiene measures"}}, "cityCode": "CTDEL", "cityName": "Delhi", "countryCode": "IN", "countryName": "India", "couponAutoApply": true, "currencyCode": {"id": "INR", "value": "INR"}, "displayFare": {"aajKaBhaoApplied": "NA", "actualPrice": {"averagePriceWithTax": 2899.0, "avgeragePriceNoTax": 2899.0, "maxFraction": 0.0, "sellingPriceNoTax": 2899.0, "sellingPriceWithTax": 2899.0, "value": 2899.0}, "apPromotionDiscount": 0.0, "bestCoupon": {"autoApplicable": true, "blockPhoneNumber": false, "bnplAllowed": true, "brinUnlocked": false, "couponCode": "BESTBUYMMT2", "couponDetails": [{"cashbackAmount": 0.0, "cdfValidated": false, "discount": 259.0, "discountType": "Instant", "discountTypeCode": "INSTANT", "ecoupon": false, "moreVerificationRequired": false, "timeOfCredit": "Booking"}], "ddApplicable": false, "description": "Get INR 259 OFF", "discountAmount": 259.0, "doubleDiscountEnabled": false, "hybridDiscounts": {"INSTANT": 259.0}, "paymentModel": "PAS", "refundPolicy": "<PERSON><PERSON><PERSON>", "showCouponExpiry": false, "specialPromoCoupon": false, "tncUrl": "https://www.makemytrip.com/promos/dh-hdfc-18032019.html", "type": "ECOUPON", "walletAmountApplicable": 0.0}, "conversionFactor": 0.0, "couponReceived": false, "couponSkipped": false, "dateOfDelayedPayment": 1600842600000, "ddApplied": false, "ddmu": 0.0, "displayPriceBreakDown": {"addonPrice": 0.0, "affiliateFee": 0.0, "basePrice": 2899.0, "brinInclusivePrice": 0.0, "cdfDiscount": 259.0, "couponInfo": {"autoApplicable": true, "blockPhoneNumber": false, "bnplAllowed": true, "brinUnlocked": false, "couponCode": "BESTBUYMMT2", "ddApplicable": false, "description": "Get INR 259 OFF", "discountAmount": 259.0, "doubleDiscountEnabled": false, "hybridDiscounts": {"INSTANT": 259.0}, "paymentModel": "PAS", "refundPolicy": "<PERSON><PERSON><PERSON>", "showCouponExpiry": false, "specialPromoCoupon": false, "tncUrl": "https://www.makemytrip.com/promos/dh-hdfc-18032019.html", "type": "ECOUPON", "walletAmountApplicable": 0.0}, "displayPrice": 1480.0, "displayPriceAlternateCurrency": 0.0, "effectivePrice": 1480.0, "extraPaxPrice": 0.0, "hotelServiceCharge": 0.0, "hotelTax": 209.0, "metaDiscount": 0.0, "mmtDiscount": 1160.0, "mmtServiceCharge": 104.0, "nonDiscountedAddonPrice": 0.0, "nonDiscountedPrice": 2899.0, "pricingDivisor": 1, "pricingKey": "s9rHC9RN7n9Q5/LXIhT68FGZ9//nV0NhbsFRmwKMmxKD2puKBO1pZg8qpmkMGe1QMAy8GHSoqoTAkZi9+vBmwbMH97hb/qjAFUWfe7rQ0gjUwoPyJAa8zmfywhKEEjVgSc0Lg92xKeBkXcGTelRGuqYNbG3c62M53YlukGRBgBeZM8WUaw2fN4nAkrM3XJbwBdbrMPbNFdvY/BX8Iz0RUt0cOsCSrsVe5pm1HzxAwiyH3lGe0daZZ3kwN+QeJVgmkxo2Gk2slvHNyvl+3oAhzQ==", "savingPerc": 49.0, "taxIncluded": false, "totalAmount": 1480.0, "totalSaving": 1419.0, "totalTax": 313.0, "wallet": 0.0}, "extraAdult": {"value": 0.0}, "hotelTax": 208.73, "hotelierServiceCharge": 0.0, "isBNPLApplicable": true, "originalBNPL": true, "segmentId": "1120", "slashedPrice": {"averagePriceWithTax": 1739.4, "avgeragePriceNoTax": 1739.4, "maxFraction": 0.0, "sellingPriceNoTax": 1739.4, "sellingPriceWithTax": 1739.4, "value": 1739.4}, "tax": {"value": 0.0}, "taxExcluded": false, "totalRoomCount": 1}, "dynamicFilters": ["Location"], "extraMeals": {"code": "MAP", "cost": null, "desc": "2 meals -  Breakfast & Lunch or Dinner"}, "facilityCategorization": [{"name": "hotel", "subcat": "Wifi"}, {"name": "hotel", "subcat": "Parking"}, {"name": "hotel", "subcat": "Room service"}, {"name": "hotel", "subcat": "Power backup"}, {"name": "hotel", "subcat": "Elevator/ Lift"}, {"name": "hotel", "subcat": "Air Conditioning"}, {"name": "hotel", "subcat": "Dry Cleaning services"}, {"name": "hotel", "subcat": "Smoking rooms"}, {"name": "hotel", "subcat": "<PERSON><PERSON><PERSON>"}, {"name": "hotel", "subcat": "Intercom"}, {"name": "hotel", "subcat": "Refrigerator"}, {"name": "hotel", "subcat": "Housekeeping"}, {"name": "hotel", "subcat": "Public restrooms"}, {"name": "hotel", "subcat": "Umbrellas"}, {"name": "hotel", "subcat": "Newspaper"}, {"name": "hotel", "subcat": "Bathroom"}, {"name": "hotel", "subcat": "LAN"}, {"name": "hotel", "subcat": "Ironing services"}, {"name": "hotel", "subcat": "Telephone"}, {"name": "hotel", "subcat": "Smoke detector"}, {"name": "hotel", "subcat": "Pickup/ Drop"}, {"name": "hotel", "subcat": "Shuttle Service"}, {"name": "hotel", "subcat": "Bus Station transfers"}, {"name": "hotel", "subcat": "Railway Station Transfers"}, {"name": "hotel", "subcat": "Airport Transfers"}, {"name": "hotel", "subcat": "Childcare service"}, {"name": "hotel", "subcat": "Children's play area"}, {"name": "hotel", "subcat": "Restaurant"}, {"name": "hotel", "subcat": "Dining Area"}, {"name": "hotel", "subcat": "ATM"}, {"name": "hotel", "subcat": "Currency Exchange"}, {"name": "hotel", "subcat": "CCTV"}, {"name": "hotel", "subcat": "Fire extinguishers"}, {"name": "hotel", "subcat": "Safety and Security"}, {"name": "hotel", "subcat": "Security"}, {"name": "hotel", "subcat": "Safe"}, {"name": "hotel", "subcat": "First-aid services"}, {"name": "hotel", "subcat": "Electrical Adapters Available"}, {"name": "hotel", "subcat": "Electrical chargers"}, {"name": "hotel", "subcat": "TV"}, {"name": "hotel", "subcat": "Doctor on call"}, {"name": "hotel", "subcat": "Luggage storage"}, {"name": "hotel", "subcat": "Luggage assistance"}, {"name": "hotel", "subcat": "Medical centre"}, {"name": "hotel", "subcat": "Ticket/ Tour Assistance"}, {"name": "hotel", "subcat": "Bellboy service"}, {"name": "hotel", "subcat": "Caretaker"}, {"name": "hotel", "subcat": "Wake-up Call / Service"}, {"name": "hotel", "subcat": "Specially abled assistance"}, {"name": "hotel", "subcat": "Electrical Sockets"}, {"name": "hotel", "subcat": "Postal services"}, {"name": "hotel", "subcat": "Fireplace"}, {"name": "hotel", "subcat": "Living Room"}, {"name": "hotel", "subcat": "Reception"}, {"name": "hotel", "subcat": "Seating Area"}, {"name": "hotel", "subcat": "Grocery"}, {"name": "hotel", "subcat": "Shops"}, {"name": "hotel", "subcat": "Conference room"}, {"name": "hotel", "subcat": "Printer"}, {"name": "hotel", "subcat": "Photocopying"}, {"name": "hotel", "subcat": "Business services"}, {"name": "hotel", "subcat": "Business Center"}], "facilityHighlights": ["Doctor on Call", "Childcare Services", "Free Wi-Fi", "Free Parking", "Restaurant", "Conference Room", "Kids Play Area", "24-hour Room Service", "Power Backup", "Elevator/Lift", "Air Conditioning"], "featured": "N", "flyfishReviewSummary": {"MMT": {"crawledData": false, "cumulativeRating": 3.8, "sortingCriterion": ["Latest first", "Helpful first", "Positive first", "Negative first"], "totalRatingCount": 525, "totalReviewsCount": 388, "travellerRatingSummary": {"hotelSummary": [{"concept": "Room", "heroTag": false, "reviewCount": 209, "show": true, "subConcepts": [{"priorityScore": 79, "relatedReviewCount": 79, "sentiment": "POSITIVE", "subConcept": "Room Quality", "tagType": "BASE"}, {"priorityScore": 42, "relatedReviewCount": 42, "sentiment": "POSITIVE", "subConcept": "Space in Rooms", "tagType": "BASE"}, {"priorityScore": 14, "relatedReviewCount": 14, "sentiment": "NEUTRAL", "subConcept": "Bathroom Hygiene", "tagType": "BASE"}, {"priorityScore": 8, "relatedReviewCount": 8, "sentiment": "NEUTRAL", "subConcept": "Bed Quality", "tagType": "BASE"}, {"priorityScore": 5, "relatedReviewCount": 5, "sentiment": "NEUTRAL", "subConcept": "Room Amenities", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Balcony", "tagType": "BASE"}, {"priorityScore": 108, "relatedReviewCount": 108, "sentiment": "POSITIVE", "subConcept": "Good Room", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 11, "relatedReviewCount": 11, "sentiment": "POSITIVE", "subConcept": "Clean Bathroom", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 7, "relatedReviewCount": 7, "sentiment": "POSITIVE", "subConcept": "Comfortable Bed", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 4, "relatedReviewCount": 4, "sentiment": "NEGATIVE", "subConcept": "Poor In-Room Amenities", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.3}, {"concept": "Value for Money", "heroTag": false, "reviewCount": 244, "show": true, "subConcepts": [{"priorityScore": 19, "relatedReviewCount": 19, "sentiment": "NEUTRAL", "subConcept": "Value for Money", "tagType": "BASE"}, {"priorityScore": 8, "relatedReviewCount": 8, "sentiment": "POSITIVE", "subConcept": "Value for Money", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.1}, {"concept": "Cleanliness", "heroTag": false, "reviewCount": 407, "show": true, "subConcepts": [{"priorityScore": 59, "relatedReviewCount": 59, "sentiment": "POSITIVE", "subConcept": "Room Cleanliness", "tagType": "BASE"}, {"priorityScore": 14, "relatedReviewCount": 14, "sentiment": "POSITIVE", "subConcept": "Bathroom Cleanliness", "tagType": "BASE"}, {"priorityScore": 11, "relatedReviewCount": 11, "sentiment": "POSITIVE", "subConcept": "Hotel Cleanliness", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Restaurant Cleanliness", "tagType": "BASE"}, {"priorityScore": 2, "relatedReviewCount": 2, "sentiment": "POSITIVE", "subConcept": "Linen Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "NEUTRAL", "subConcept": "Staff Cleanliness", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "NEUTRAL", "subConcept": "<PERSON><PERSON><PERSON>", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Place Cleanliness", "tagType": "BASE"}, {"priorityScore": 10000, "relatedReviewCount": 58, "sentiment": "POSITIVE", "subConcept": "Clean Room", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 14, "relatedReviewCount": 14, "sentiment": "POSITIVE", "subConcept": "Clean Bathroom", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 9900, "relatedReviewCount": 11, "sentiment": "POSITIVE", "subConcept": "Clean Property", "tagType": "WHAT_GUESTS_SAY"}], "value": 4.0}, {"concept": "Location", "heroTag": false, "reviewCount": 129, "show": true, "subConcepts": [{"priorityScore": 39, "relatedReviewCount": 39, "sentiment": "NEUTRAL", "subConcept": "Location", "tagType": "BASE"}, {"priorityScore": 9, "relatedReviewCount": 9, "sentiment": "POSITIVE", "subConcept": "Connectivity", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Market", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Central Location", "tagType": "BASE"}, {"priorityScore": 32, "relatedReviewCount": 32, "sentiment": "POSITIVE", "subConcept": "Good Location", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 9, "relatedReviewCount": 9, "sentiment": "POSITIVE", "subConcept": "Well Connected", "tagType": "WHAT_GUESTS_SAY"}], "value": 3.9}, {"concept": "Hospitality", "heroTag": false, "reviewCount": 109, "show": true, "subConcepts": [{"priorityScore": 110, "relatedReviewCount": 110, "sentiment": "POSITIVE", "subConcept": "Staff Courtesy", "tagType": "BASE"}, {"priorityScore": 51, "relatedReviewCount": 51, "sentiment": "NEUTRAL", "subConcept": "Service Quality", "tagType": "BASE"}, {"priorityScore": 104, "relatedReviewCount": 104, "sentiment": "POSITIVE", "subConcept": "Courteous Staff", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 37, "relatedReviewCount": 37, "sentiment": "POSITIVE", "subConcept": "Good Service", "tagType": "WHAT_GUESTS_SAY"}], "value": 3.5}, {"concept": "Security", "heroTag": false, "reviewCount": 34, "show": true, "value": 3.2}, {"concept": "Food", "heroTag": false, "reviewCount": 113, "show": true, "subConcepts": [{"priorityScore": 29, "relatedReviewCount": 29, "sentiment": "POSITIVE", "subConcept": "Food", "tagType": "BASE"}, {"priorityScore": 28, "relatedReviewCount": 28, "sentiment": "POSITIVE", "subConcept": "Breakfast", "tagType": "BASE"}, {"priorityScore": 6, "relatedReviewCount": 6, "sentiment": "POSITIVE", "subConcept": "Restaurant", "tagType": "BASE"}, {"priorityScore": 25, "relatedReviewCount": 25, "sentiment": "POSITIVE", "subConcept": "Good Food", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 28, "relatedReviewCount": 28, "sentiment": "POSITIVE", "subConcept": "Good Breakfast", "tagType": "WHAT_GUESTS_SAY"}, {"priorityScore": 6, "relatedReviewCount": 6, "sentiment": "POSITIVE", "subConcept": "Good Restaurant", "tagType": "WHAT_GUESTS_SAY"}], "value": 3.1}, {"concept": "Facilities", "heroTag": false, "reviewCount": 25, "show": true, "subConcepts": [{"priorityScore": 7, "relatedReviewCount": 7, "sentiment": "NEUTRAL", "subConcept": "Wifi", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "NEUTRAL", "subConcept": "<PERSON><PERSON>", "tagType": "BASE"}, {"priorityScore": 1, "relatedReviewCount": 1, "sentiment": "POSITIVE", "subConcept": "Airport Transfers", "tagType": "BASE"}, {"priorityScore": 4, "relatedReviewCount": 4, "sentiment": "NEGATIVE", "subConcept": "Poor Wifi", "tagType": "WHAT_GUESTS_SAY"}], "value": 2.8}, {"concept": "Safety", "heroTag": false, "reviewCount": 4, "show": true, "value": 2.8}, {"concept": "Child friendliness", "heroTag": false, "reviewCount": 24, "show": true, "value": 2.7}]}}}, "freeCancellationAvailable": true, "freeCancellationText": "Free Cancellation", "geoLocation": {"latitude": "28.549435", "longitude": "77.13031"}, "hasCollections": false, "hotelFacilities": "Basic Facilities,Transfers,Family and kids,Food and Drinks,Payment Services,Safety and Security,Health and wellness,Media and technology,General Services,Common Area,Shopping,Business Center and Conferences", "id": "201902131233091519", "ignoreEntireProperty": true, "isAbsorptionApplied": false, "isBNPLAvailable": true, "isFreeCancellation": true, "isFreeWifiAvail": true, "isMTKeyEnabledSearch": false, "isPAHAvailable": false, "isPAHTariffAvailable": true, "isShortlisted": false, "isSoldOut": false, "isValuePlus": false, "lastBooked": false, "listingType": "Room By Room", "lowestBlackPackage": false, "lowestNetRate": false, "lowestRateBnpl": true, "lowestRatePlanMealDiff": 0.0, "lowestRatePlanPahDiff": 0.0, "lowestRateStaycation": false, "lowestRateSupplierCode": "INGO", "lowestRoomAvailCount": 1, "lowestRoomCodesRatePlans": "4--990001277014:MSE:1120:MSE:ING", "mainImages": ["//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-fe95660e82bb11e9bfb30242ac110004.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-e2e1d2ac837a11e98f0b0242ac110002.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-eed8a4c8837a11e9a2120242ac110002.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-20d249107df011e9b5c50242ac110003.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-d1d8567a837a11e987b60242ac110003.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-24f26ebc7df011e9b4100242ac110002.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-27403df27df011e9ac310242ac110002.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-27c8eee07df011e991cc0242ac11000b.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-29d1d4e07df011e996a90242ac110006.jpg?&output-quality=75&downsize=583:388&output-format=jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902131233091519-3065910c7df011e993b90242ac110003.jpg?&output-quality=75&downsize=583:388&output-format=jpg"], "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "maxOccupancy": 0, "mtkey": "defaultMtkey", "name": "Hotel SRV Plaza", "netRateAvailable": false, "notInterested": false, "pahWalletApplicable": false, "pahmode": "PAS", "pahxApplicable": false, "pahxAvailable": false, "paymentMode": "PAS", "poiTag": "4.7 km from T1 - Delhi Airport (IGI)", "poiTagList": [{"bestPoiPriority": 0, "drivingDistance": "4680.79", "drivingDistanceText": "4.68 km", "drivingTimeText": "", "hotelId": "201902131233091519", "poiCategory": "Airport", "poiId": "POITERM", "poiName": "T1 - Delhi Airport (IGI)", "poiPriority": "0"}], "priceDifferenceWithPivot": 0.0, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotionsCount": 0}, "propertyActivities": [], "propertyRules": [], "propertyType": "Hotel", "recommendCouponBit": true, "recommendedMultiRoom": false, "roomCount": 0, "segments": {"segmentList": {"1120": {"channelSegment": "ALL", "id": "1120", "userSegment": "REGULAR"}, "1121": {"channelSegment": "MOB", "id": "1121", "userSegment": "REGULAR"}, "1126": {"channelSegment": "ALL", "id": "1126", "userSegment": "LOGGEDIN"}}, "segmentsCount": 3}, "shortDescription": "Hotel Srv plaza offers room in New Delhi,the property is around 3 kms from Delhi Airport (IGI).All rooms at the hotel are equipp ", "starRating": 3, "stateCode": "STDEL", "stateName": "Delhi", "stayType": "Hotel", "staycationAvailable": false, "totalRoomAvailCount": 13, "userEligiblePayMode": "PAS", "usp": {"Location": {"subTags": null, "sub_title": "The hotel is located 100 m away from NH-8, 4.1 km away from Delhi Airport  and 1.2 km away from Aerocity Metro", "title": "Location"}}, "walletEntity": {"defaultWalletBurn": {"bonusPercentage": 3, "maxBonus": 4000, "minBookingAmount": 0}, "status": "N"}}]}}