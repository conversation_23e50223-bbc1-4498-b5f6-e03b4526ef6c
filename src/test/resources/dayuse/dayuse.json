{"total": 1, "hotelRates": [{"showAddOn": false, "currencyCode": "INR", "cityName": "<PERSON><PERSON><PERSON>", "cityCode": "CTXBH", "countryCode": "IN", "name": "Kings Cottage", "id": "201411211336321525", "addressLines": ["padampuri road near khutani bhimtal, Uttarakhand", "Mahargaon"], "checkInTime": "12 PM", "checkOutTime": "10 AM", "hotelIcon": "https://gos3.ibcdn.com/5601a3f242af11ecbce20a58a9feac02.jpg", "categories": ["MMT OFFER", "Festive Weekend Deals", "Epic Workations", "Villas and Apartments", "villas and apartments", "Safety_Hygiene", "Parties Allowed", "MMT Value Stays", "Workation Homes", "<PERSON><PERSON>owed", "Star Host", "gohomes", "Hills", "Romantic Stays", "Cottage", "Couple Friendly", "Pet Friendly"], "isPAHTariffAvailable": false, "isPAHAvailable": false, "chatEnabled": false, "lowestRate": {"discountedLowestRate": 2999.0, "extraAdult": 0.0, "lowestRate": 2999.0, "tax": 2.3, "hotelTax": 719.76, "ddmu": 0.0, "hotelierCouponDiscount": 599.8, "gstDiscountHotelierCoupon": 71.98, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "segmentId": "1120", "supplierCode": "INGOHS", "totalRoomCounts": 2, "pahx": false, "aajKaBhaoApplied": "NA", "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "markup": 323.9, "hotelierServiceCharge": 0.0, "lowestNetRate": false, "mealPlanIncluded": {"desc": "Room Only", "code": "EP", "cost": null}, "availDetails": {"status": "B", "count": 2, "numOfRooms": 1, "occupancyDetails": {"adult": 5, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "lowestRoomCodesRatePlans": "2312--************:MSE:1120:MSE:INGOHS,28--************:MSE:1120:MSE:INGOHS", "recommendedSet": true}, "mtKey": "defaultMtkey", "soldOut": false, "searchRoomDeeplinkUrl": "https://www.makemytrip.com/hotels/hotel-select-room?hotelId=201411211336321525&checkin=04142022&checkout=04152022&country=IN&city=CTGOI&_uCurrency=INR&roomStayQualifier=5e0e&locusId=CTGOI&locusType=city&hotelName=Kings+Cottage&countryName=India&cityName=Bhimtal&isEntireProperty=false&funnelName=DAYUSE", "detailDeeplinkUrl": "https://www.makemytrip.com/hotels/hotel-details?hotelId=201411211336321525&checkin=04142022&checkout=04152022&country=IN&city=CTGOI&openDetail=true&currency=INR&roomStayQualifier=5e0e&locusId=CTGOI&locusType=city&checkAvailability=true&region=in&viewType=BUDGET", "recommendedRoomTypeDetails": {"roomType": {"2312": {"type": "RoomType", "ratePlanList": {"************:MSE:1120:MSE:INGOHS": {"type": "RatePlan", "inclusions": [], "mealPlans": [{"startDate": 1649874600000, "endDate": 1649961000000, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 0, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": null, "cancelPenaltiesRule": null, "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": null, "description": "This booking is non-refundable and the tariff cannot be cancelled with zero fee."}, "tillDate": null, "sponsorer": null, "freeCancellationText": null, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 2499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2499.0, "averagePriceWithTax": 2499.0, "sellingPriceNoTax": 2499.0, "sellingPriceWithTax": 2499.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 2499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2499.0, "averagePriceWithTax": 2499.0, "sellingPriceNoTax": 2499.0, "sellingPriceWithTax": 2499.0, "basePriceWoCommision": 2499.0, "actualTax": 299.88}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 2499.0, "subTotal": 2499.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 299.88, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 134.95, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 2499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2499.0, "hotelTax": 300.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 135.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8KC2hUgBZk35QGQG9CZnUPk1TJkzhUJB0EtdlmQ+I38wmGwxjHiUUEURjnDIjrNd4LSKbKKIQGm1Hp3CxsViTaDbPKQiNLGMVNJE9Tt3bE606MPcaAVptAArivY7i9b3KYc4eqGhT+bz7CxwI+26qTJt0tS/dZTETZAIdKqt6OSGOHZtPSc5A8N22ue9YQcQJXxFY+uhAGS1VfzSXgzwb7BXadVJXQ17qiQtRjxLMUbbgPjsHAMmyCLmMijqp8HPI=", "pricingDivisor": 1, "totalTax": 435.0, "effectivePrice": 2499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 2499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2499.0, "hotelTax": 300.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 135.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8KC2hUgBZk35QGQG9CZnUPk1TJkzhUJB0EtdlmQ+I38wmGwxjHiUUEURjnDIjrNd4LSKbKKIQGm1Hp3CxsViTaDbPKQiNLGMVNJE9Tt3bE606MPcaAVptAArivY7i9b3KYc4eqGhT+bz7CxwI+26qTJt0tS/dZTETZAIdKqt6OSGOHZtPSc5A8N22ue9YQcQJXxFY+uhAGS1VfzSXgzwb7BXadVJXQ17qiQtRjxLMUbbgPjsHAMmyCLmMijqp8HPI=", "pricingDivisor": 1, "totalTax": 435.0, "effectivePrice": 2499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "hotelTax": 299.88, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGOHS", "costPrice": 1959.21, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 2499.0, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 2499.0, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2022-04-14", "endDate": "2022-04-15", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_2312-_45000219796-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_lJp-_4pt-_2Ax-_0-_41u-_Tz-_.-_bt4-_6Wq-_0-_INR", "promotionsList": [null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 134.95}], "ratePlanDesc": "Standard Room", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "inclusionAndPolicyAvailVar": true, "suppressPas": false, "reviewDeeplinkUrl": "https://www.makemytrip.com/hotels/hotel-review?hotelId=201411211336321525&checkin=04142022&checkout=04152022&country=IN&city=CTGOI&_uCurrency=INR&roomStayQualifier=5e0e&locusId=CTGOI&locusType=city&hotelName=Kings+Cottage&countryName=India&cityName=Bhimtal&isEntireProperty=false&funnelName=DAYUSE&roomCriteria=2312~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~2e0e~~~28~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~3e0e~~&searchType=R&mtKey=0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_2312-_45000219796-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_lJp-_4pt-_2Ax-_0-_41u-_Tz-_.-_bt4-_6Wq-_0-_INR&suppDetail=INGOHS&payMode=PAS", "ratePlanCode": "************:MSE:1120:MSE:INGOHS", "suppressed": false, "notices": [], "bnplExtended": false, "pahx": false, "rpcc": "************", "groupModifiedRatePlanCode": "************:MSE:1120:MSE:INGOHS", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "allInclusiveRate": false, "vendorRoomCode": "45000219796", "packageRoomRatePlan": false}}, "roomTypeName": "Standard Room", "roomTypeCode": "2312", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": null, "roomImages": null, "selectedAmenities": null}, "28": {"type": "RoomType", "ratePlanList": {"************:MSE:1120:MSE:INGOHS": {"type": "RatePlan", "inclusions": [], "mealPlans": [{"startDate": 1649874600000, "endDate": 1649961000000, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 0, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 1, "bedCount": 1}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": null, "cancelPenaltiesRule": null, "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": null, "description": "This booking is non-refundable and the tariff cannot be cancelled with zero fee."}, "tillDate": null, "sponsorer": null, "freeCancellationText": null, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 3499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 3499.0, "averagePriceWithTax": 3499.0, "sellingPriceNoTax": 3499.0, "sellingPriceWithTax": 3499.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 3499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 3499.0, "averagePriceWithTax": 3499.0, "sellingPriceNoTax": 3499.0, "sellingPriceWithTax": 3499.0, "basePriceWoCommision": 3499.0, "actualTax": 419.88}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 3499.0, "subTotal": 3499.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 419.88, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 188.95, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 3499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 3499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 3499.0, "hotelTax": 420.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 189.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/VD5nBFExcUIjyu/QRr1O/E7ehzdFfOgQ4RDAh8yudiy1uuzMqHUnISKQytrytB53ij4lev1Uek9qsqd8ZnBT5ZpNQ1lwQfIi6pjwCfy5XAtug9s1ezJT92oKHvPfvQPeMI0wu+9+CsiqKR9s5mp46B4apyyt3Hmmcmn9xVJm80hC1pVWkVPac8u+tbhXKjW801UxEDoTiNxo8IqpIqv173IXdlYqtmOS3eKfDq46tEtJRKXCQDjHFrFZ8HJetsf2u/DTykcI+BA==", "pricingDivisor": 1, "totalTax": 609.0, "effectivePrice": 3499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 3499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 3499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 3499.0, "hotelTax": 420.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 189.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/VD5nBFExcUIjyu/QRr1O/E7ehzdFfOgQ4RDAh8yudiy1uuzMqHUnISKQytrytB53ij4lev1Uek9qsqd8ZnBT5ZpNQ1lwQfIi6pjwCfy5XAtug9s1ezJT92oKHvPfvQPeMI0wu+9+CsiqKR9s5mp46B4apyyt3Hmmcmn9xVJm80hC1pVWkVPac8u+tbhXKjW801UxEDoTiNxo8IqpIqv173IXdlYqtmOS3eKfDq46tEtJRKXCQDjHFrFZ8HJetsf2u/DTykcI+BA==", "pricingDivisor": 1, "totalTax": 609.0, "effectivePrice": 3499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "hotelTax": 419.88, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGOHS", "costPrice": 2743.21, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 3499.0, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 3499.0, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2022-04-14", "endDate": "2022-04-15", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_28-_45000423308-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_14Fp-_6lt-_32x-_0-_5du-_fz-_.-_r34-_98q-_0-_INR", "promotionsList": [null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 188.95}], "ratePlanDesc": "family room", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "inclusionAndPolicyAvailVar": true, "suppressPas": false, "reviewDeeplinkUrl": "https://www.makemytrip.com/hotels/hotel-review?hotelId=201411211336321525&checkin=04142022&checkout=04152022&country=IN&city=CTGOI&_uCurrency=INR&roomStayQualifier=5e0e&locusId=CTGOI&locusType=city&hotelName=Kings+Cottage&countryName=India&cityName=Bhimtal&isEntireProperty=false&funnelName=DAYUSE&roomCriteria=2312~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~2e0e~~~28~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~3e0e~~&searchType=R&mtKey=0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_28-_45000423308-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_14Fp-_6lt-_32x-_0-_5du-_fz-_.-_r34-_98q-_0-_INR&suppDetail=INGOHS&payMode=PAS", "ratePlanCode": "************:MSE:1120:MSE:INGOHS", "suppressed": false, "notices": [], "bnplExtended": false, "pahx": false, "rpcc": "************", "groupModifiedRatePlanCode": "************:MSE:1120:MSE:INGOHS", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "allInclusiveRate": false, "vendorRoomCode": "45000423308", "extraPaxDetails": {"extraAdults": [{"buyingPrice": 410.0, "sellingPrice": 500.0, "promoAmount": 0.0, "tax": 60.0, "serviceFee": 0.0, "bedAvailable": true, "bedType": "<PERSON><PERSON>"}]}, "extraGuestDetail": {"hotelDetailExtraBedText": "Extra bed for 1 adult included in the price.", "roomSelectionExtraBedText": "Existing bed(s) can accommodate 2 adults", "roomSelectionDetailExtraBedText": "Existing bed(s) can accommodate 2 adults#The type of extra bed provided by the property is Mattress. (Subject to availability).", "ratePlanExtraBedText": "Room Only included for 3 guests", "ratePlanDetailExtraBedText": "Room Only included for 3 guests"}, "packageRoomRatePlan": false}}, "roomTypeName": "family room", "roomTypeCode": "28", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": null, "roomImages": null, "selectedAmenities": null}}, "totalDisplayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 5998.0, "maxFraction": 0.0, "avgeragePriceNoTax": 5998.0, "averagePriceWithTax": 5998.0, "sellingPriceNoTax": 5998.0, "sellingPriceWithTax": 5998.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 5998.0, "maxFraction": 0.0, "avgeragePriceNoTax": 5998.0, "averagePriceWithTax": 5998.0, "sellingPriceNoTax": 5998.0, "sellingPriceWithTax": 5998.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 5998.0, "subTotal": 5998.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 2, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 719.76, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 323.9, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 5998.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5998.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 5998.0, "hotelTax": 720.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 324.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8ALU+Q0yVpDhTCUpVCZdrjQH5yY7NXd6BUJEsow4AZOgTxIE2mBaxd0yHJgnTY0Of6Cm8rxZQc+kwDlG81dlS6O6e4G9eoRXQQ4QvLrnwudPovohLNizXNAZLsUKXMD0f3OUjttbi+5onZN557Ep2pO7f1AUHY9rdbeCdwV/d2p4FbYRE+apVJMvJBgeHkH9+75KRZoypnsP3liwXwITF90syzhx9JWQ2D3uX5jd6HSOi4SZyhMFzmVdU0DoOWPA0=", "pricingDivisor": 1, "totalTax": 1044.0, "effectivePrice": 5998.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 599.8, "gstDiscountHotelierCoupon": 71.98, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 5998.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5998.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 5998.0, "hotelTax": 720.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 324.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8ALU+Q0yVpDhTCUpVCZdrjQH5yY7NXd6BUJEsow4AZOgTxIE2mBaxd0yHJgnTY0Of6Cm8rxZQc+kwDlG81dlS6O6e4G9eoRXQQ4QvLrnwudPovohLNizXNAZLsUKXMD0f3OUjttbi+5onZN557Ep2pO7f1AUHY9rdbeCdwV/d2p4FbYRE+apVJMvJBgeHkH9+75KRZoypnsP3liwXwITF90syzhx9JWQ2D3uX5jd6HSOi4SZyhMFzmVdU0DoOWPA0=", "pricingDivisor": 1, "totalTax": 1044.0, "effectivePrice": 5998.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 599.8, "gstDiscountHotelierCoupon": 71.98, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 599.8, "gstDiscountHotelierCoupon": 71.98, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "occupancyDetails": {"adult": 5, "child": 0, "infant": 0, "numOfRooms": 2, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 1, "bedCount": 1}, "comboMealPlan": "EP", "staycationDeal": false, "comboPayMode": "PAS", "baseCombo": false}, "occupencyLessRoomTypeDetails": {"roomType": {"2312": {"type": "RoomType", "ratePlanList": {"************:MSE:1120:MSE:INGOHS:MSE:2$MSE$0$MSE$0": {"type": "RatePlan", "inclusions": [], "mealPlans": [{"startDate": 1649874600000, "endDate": 1649961000000, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 2, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": null, "cancelPenaltiesRule": null, "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": null, "description": "This booking is non-refundable and the tariff cannot be cancelled with zero fee."}, "tillDate": null, "sponsorer": null, "freeCancellationText": null, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 2499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2499.0, "averagePriceWithTax": 2499.0, "sellingPriceNoTax": 2499.0, "sellingPriceWithTax": 2499.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 2499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2499.0, "averagePriceWithTax": 2499.0, "sellingPriceNoTax": 2499.0, "sellingPriceWithTax": 2499.0, "basePriceWoCommision": 2049.18, "actualTax": 299.88}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 2499.0, "subTotal": 2499.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 299.88, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 134.95, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 2499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2499.0, "hotelTax": 300.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 135.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n9bHz8E2Mc7E4KIEdZQmbbmePsPxTP+vKNA6aJqAYo0dhCEi5Ti/yWMp5lmgQX+6dKX4UL/cgpG8gzSp0GZEKSW+Rs6b/sC4RSmykYQTFnaetKVCNO1FcHalpfNVM7fW+m5n0yFuEhLEPMBpHfmTjyhQ2hfblOl7xA0Gnu1/6R3W13NvWVmRBoJIXbFGUTDGba3v4Sl9shbmBn2IKhyKCA35ASnCK4bOaqi0x0DPvUZiVvOLKFW0m4CGzdMAiUODmoEqVrrQV9EwxVDPEWLM1lew7++fv/OEoeyrdfBpJx6Kg==", "pricingDivisor": 1, "totalTax": 435.0, "effectivePrice": 2499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 2499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2499.0, "hotelTax": 300.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 135.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n9bHz8E2Mc7E4KIEdZQmbbmePsPxTP+vKNA6aJqAYo0dhCEi5Ti/yWMp5lmgQX+6dKX4UL/cgpG8gzSp0GZEKSW+Rs6b/sC4RSmykYQTFnaetKVCNO1FcHalpfNVM7fW+m5n0yFuEhLEPMBpHfmTjyhQ2hfblOl7xA0Gnu1/6R3W13NvWVmRBoJIXbFGUTDGba3v4Sl9shbmBn2IKhyKCA35ASnCK4bOaqi0x0DPvUZiVvOLKFW0m4CGzdMAiUODmoEqVrrQV9EwxVDPEWLM1lew7++fv/OEoeyrdfBpJx6Kg==", "pricingDivisor": 1, "totalTax": 435.0, "effectivePrice": 2499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "hotelTax": 299.88, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGOHS", "costPrice": 1959.21, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 2499.0, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 2499.0, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2022-04-14", "endDate": "2022-04-15", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_2312-_45000219796-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS%3AMSE%3A2%24MSE%240%24MSE%240-_.-_EP-_INGOHS-_.-_lJp-_4pt-_2Ax-_0-_41u-_Tz-_.-_bt4-_6Wq-_0-_INR", "promotionsList": [null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 134.95}], "ratePlanDesc": "Standard Room", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "inclusionAndPolicyAvailVar": true, "suppressPas": false, "ratePlanCode": "************:MSE:1120:MSE:INGOHS:MSE:2$MSE$0$MSE$0", "suppressed": false, "notices": [], "bnplExtended": false, "pahx": false, "rpcc": "************", "groupModifiedRatePlanCode": "************:MSE:1120:MSE:INGOHS:MSE:1$MSE$0$MSE$0", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "allInclusiveRate": false, "vendorRoomCode": "45000219796", "packageRoomRatePlan": false}}, "roomTypeName": "Standard Room", "roomTypeCode": "2312", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": null, "roomImages": null, "selectedAmenities": null}, "28": {"type": "RoomType", "ratePlanList": {"************:MSE:1120:MSE:INGOHS:MSE:2$MSE$0$MSE$0": {"type": "RatePlan", "inclusions": [], "mealPlans": [{"startDate": 1649874600000, "endDate": 1649961000000, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": null, "cancelPenaltiesRule": null, "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": null, "description": "This booking is non-refundable and the tariff cannot be cancelled with zero fee."}, "tillDate": null, "sponsorer": null, "freeCancellationText": null, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 2999.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2999.0, "averagePriceWithTax": 2999.0, "sellingPriceNoTax": 2999.0, "sellingPriceWithTax": 2999.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 2999.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2999.0, "averagePriceWithTax": 2999.0, "sellingPriceNoTax": 2999.0, "sellingPriceWithTax": 2999.0, "basePriceWoCommision": 2459.18, "actualTax": 359.88}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 2999.0, "subTotal": 2999.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 359.88, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 161.95, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 2999.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2999.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2999.0, "hotelTax": 360.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 162.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8snq82J8jB9sLd3A+hGxdk7ZlExfngV5gUNpjr4cnHQxp3BtInUxOCV+cZlBaEDoBaWqAj0FjYkBmtx60jtRlyW+zhG2IRxCe6KkcmN9w/fHLYsJkbTLGofRFa/sHOAsPMLSOmT8CybsPpPsQyKpgy2yVOASlhZBDJVlAP5PoP4R3V9G84dS8cHFp37nPcLGaRfBTjOppLCa5be9CNZB0It0S15CJNpngyw5YjGs9dfHa2HqUxxJ8OVWiw6lM9rGeZOrp/OlMw2+PqFJMJ8lXcMmMHTrRCMWl/ZiHfjKkBig==", "pricingDivisor": 1, "totalTax": 522.0, "effectivePrice": 2999.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 299.9, "gstDiscountHotelierCoupon": 35.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 2999.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2999.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2999.0, "hotelTax": 360.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 162.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8snq82J8jB9sLd3A+hGxdk7ZlExfngV5gUNpjr4cnHQxp3BtInUxOCV+cZlBaEDoBaWqAj0FjYkBmtx60jtRlyW+zhG2IRxCe6KkcmN9w/fHLYsJkbTLGofRFa/sHOAsPMLSOmT8CybsPpPsQyKpgy2yVOASlhZBDJVlAP5PoP4R3V9G84dS8cHFp37nPcLGaRfBTjOppLCa5be9CNZB0It0S15CJNpngyw5YjGs9dfHa2HqUxxJ8OVWiw6lM9rGeZOrp/OlMw2+PqFJMJ8lXcMmMHTrRCMWl/ZiHfjKkBig==", "pricingDivisor": 1, "totalTax": 522.0, "effectivePrice": 2999.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 299.9, "gstDiscountHotelierCoupon": 35.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 299.9, "gstDiscountHotelierCoupon": 35.99, "hotelTax": 359.88, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGOHS", "costPrice": 2351.21, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 2999.0, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 2999.0, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2022-04-14", "endDate": "2022-04-15", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_28-_45000423308-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS%3AMSE%3A2%24MSE%240%24MSE%240-_.-_EP-_INGOHS-_.-_ump-_5nt-_2bx-_0-_4pu-_Zz-_.-_jT4-_7pq-_0-_INR", "promotionsList": [null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 161.95}], "ratePlanDesc": "family room", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "inclusionAndPolicyAvailVar": true, "suppressPas": false, "ratePlanCode": "************:MSE:1120:MSE:INGOHS:MSE:2$MSE$0$MSE$0", "suppressed": false, "notices": [], "bnplExtended": false, "pahx": false, "rpcc": "************", "groupModifiedRatePlanCode": "************:MSE:1120:MSE:INGOHS:MSE:2$MSE$0$MSE$0", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "allInclusiveRate": false, "vendorRoomCode": "45000423308", "packageRoomRatePlan": false}, "************:MSE:1120:MSE:INGOHS:MSE:3$MSE$0$MSE$0": {"type": "RatePlan", "inclusions": [], "mealPlans": [{"startDate": 1649874600000, "endDate": 1649961000000, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": null, "cancelPenaltiesRule": null, "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": null, "description": "This booking is non-refundable and the tariff cannot be cancelled with zero fee."}, "tillDate": null, "sponsorer": null, "freeCancellationText": null, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 3499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 3499.0, "averagePriceWithTax": 3499.0, "sellingPriceNoTax": 3499.0, "sellingPriceWithTax": 3499.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 3499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 3499.0, "averagePriceWithTax": 3499.0, "sellingPriceNoTax": 3499.0, "sellingPriceWithTax": 3499.0, "basePriceWoCommision": 2869.18, "actualTax": 419.88}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 3499.0, "subTotal": 3499.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 419.88, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 188.95, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 3499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 3499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 3499.0, "hotelTax": 420.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 189.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n9mb5l1HnRkcXiMw1NFvMBK0INxeJzfz2W0G3OexMqL+89cxpqD/BwifzYyXzr7aZCAVhsRgX56H4VgQ5PPOaRVlmt+rZF45qC2AMMDRELft7yLkzzIRyIxLQk3P+gYyn3JpHGG/JnLKeNW1XCEOQrrQMBP5iUp3gxAemul55aOqYM4tqGg4sfHeKz6ReK+MCTg4aSDETvlOHoGO2ZB9wL4v+w9kgrE2ag70lwsW2NM/epl2BYwhxlLEWD5/v5bJ7pscHe7qYRYrJZQd6ZDnnS3OgB4xk2u6y0moQN8pvjbOA==", "pricingDivisor": 1, "totalTax": 609.0, "effectivePrice": 3499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 3499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 3499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 3499.0, "hotelTax": 420.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 189.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n9mb5l1HnRkcXiMw1NFvMBK0INxeJzfz2W0G3OexMqL+89cxpqD/BwifzYyXzr7aZCAVhsRgX56H4VgQ5PPOaRVlmt+rZF45qC2AMMDRELft7yLkzzIRyIxLQk3P+gYyn3JpHGG/JnLKeNW1XCEOQrrQMBP5iUp3gxAemul55aOqYM4tqGg4sfHeKz6ReK+MCTg4aSDETvlOHoGO2ZB9wL4v+w9kgrE2ag70lwsW2NM/epl2BYwhxlLEWD5/v5bJ7pscHe7qYRYrJZQd6ZDnnS3OgB4xk2u6y0moQN8pvjbOA==", "pricingDivisor": 1, "totalTax": 609.0, "effectivePrice": 3499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "hotelTax": 419.88, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGOHS", "costPrice": 2743.21, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 3499.0, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 3499.0, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2022-04-14", "endDate": "2022-04-15", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_28-_45000423308-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS%3AMSE%3A3%24MSE%240%24MSE%240-_.-_EP-_INGOHS-_.-_14Fp-_6lt-_32x-_0-_5du-_fz-_.-_r34-_98q-_0-_INR", "promotionsList": [null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 188.95}], "ratePlanDesc": "family room", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "inclusionAndPolicyAvailVar": true, "suppressPas": false, "ratePlanCode": "************:MSE:1120:MSE:INGOHS:MSE:3$MSE$0$MSE$0", "suppressed": false, "notices": [], "bnplExtended": false, "pahx": false, "rpcc": "************", "groupModifiedRatePlanCode": "************:MSE:1120:MSE:INGOHS:MSE:1$MSE$2$MSE$0", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "allInclusiveRate": false, "vendorRoomCode": "45000423308", "extraPaxDetails": {"extraAdults": [{"buyingPrice": 410.0, "sellingPrice": 500.0, "promoAmount": 0.0, "tax": 60.0, "serviceFee": 0.0, "bedAvailable": true, "bedType": "<PERSON><PERSON>"}]}, "extraGuestDetail": {"hotelDetailExtraBedText": "Extra bed for 1 adult included in the price.", "roomSelectionExtraBedText": "Existing bed(s) can accommodate 2 adults", "roomSelectionDetailExtraBedText": "Existing bed(s) can accommodate 2 adults#The type of extra bed provided by the property is Mattress. (Subject to availability).", "ratePlanExtraBedText": "Room Only included for 3 guests", "ratePlanDetailExtraBedText": "Room Only included for 3 guests"}, "packageRoomRatePlan": false}}, "roomTypeName": "family room", "roomTypeCode": "28", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": null, "roomImages": null, "selectedAmenities": null}}, "staycationDeal": false, "baseCombo": false}, "userEligiblePayMode": "PAS", "pahAuthenticationEnabled": false, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotionsCount": 0}, "segments": {"segmentList": {"1120": {"id": "1120", "channelSegment": "ALL", "userSegment": "REGULAR"}}, "segmentsCount": 1}, "countryName": "India", "showFcBanner": false, "taxExcluded": true, "isBNPLAvailable": false, "bnplBaseAmount": 0.0, "pahWalletApplicable": true, "bestPriceGuaranteed": false, "pahxAvailable": false, "addOnAvailableOnLowest": false, "addOnAvailable": false, "blackAccelerated": false, "preApplyCoupon": true, "mappedToDirectSupplier": false, "firstTimeUser": false, "blackEligible": false, "blackEligibilityDays": 0, "panCardRequired": false, "pnAvlbl": false, "maxAdultAtSamePrice": 0, "bedRoomCount": 1, "bedCount": 1, "maxChildAtSamePrice": 0, "showOriginalPolicyToDbUsr": false, "alternateCurrencyConversionFactor": 0.0, "starRating": 0, "propertyType": "Cottage", "walletBucketId": "HotelWB21", "corprateCdfSkip": false, "corporatePAHSkip": false, "netRateAvailable": false, "stateCode": "STUKND", "stateName": "Uttarakhand", "lat": 29.36723, "lng": 79.55088, "corpRateAvailable": false, "corpRateExpected": false, "corpSupressChain": false, "emiBucket": "", "houseRules": {"commonRules": [{"category": "Safety and Hygiene", "id": "SafetyandHygiene", "showInHost": true, "hostCatHeading": "COVID-19 related queries", "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Quarantine protocols are being followed as per local government authorities"}, {"text": "Guests from containment zones are allowed"}, {"text": "Shared resources in common areas are properly sanitized"}, {"text": "Property staff is trained on hygiene guidelines"}, {"text": "Guests with fever are allowed"}, {"text": "Only those guests with safe status on Aarogya Setu app are allowed"}, {"text": "Hand sanitizer is provided in guest accommodation and common areas"}, {"text": "Thermal screening is done at entry and exit points"}]}, {"category": "Guest Profile", "id": "Guest<PERSON><PERSON><PERSON><PERSON>", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Unmarried couples allowed"}, {"text": "Bachelors allowed"}, {"text": "Guests below 18 years of age are allowed"}, {"text": "Suitable for children"}]}, {"category": "Room Safety and Hygiene", "id": "RoomSafetyandHygiene", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "All rooms are disinfected using bleach or other disinfectant"}, {"text": "Linens, towels, and laundry are washed as per local guidelines"}, {"text": "Rooms are properly sanitized between stays"}, {"text": "Hand sanitizers are available in the rooms."}]}, {"category": "Payment Related", "id": "PaymentRelated", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Credit/Debit cards not accepted at the property"}]}, {"category": "Food Arrangement", "id": "FoodArrangement", "showInHost": true, "hostCatHeading": "Food Arrangements at the property", "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Guests can access a shared kitchen for cooking small meals or specific purposes"}, {"text": "Local, Chinese and Indian dishes are prepared by a professional chef"}, {"text": "A helper is available for dishwashing"}, {"text": "Outside food is allowed"}, {"text": "Non veg food is allowed"}]}, {"category": "Food and Drinks Hygiene", "id": "FoodandDrinksHygiene", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "COVID-19 guidelines for Food Hygiene is followed as per government guidelines"}, {"text": "Social distancing is followed in restaurants"}, {"text": "Serveware and supplies are sanitized before they are brought to the kitchen"}, {"text": "Masks and hairnets are mandatory for staff in restaurants"}]}, {"category": "Smoking/Alcohol consumption Rules", "id": "SmokingAlcoholconsumptionRules", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Smoking within the premises is not allowed"}, {"text": "There are no restrictions on alcohol consumption."}]}, {"category": "Property Layout & Neighbourhood", "id": "PropertyLayoutNeighbourhood", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "The property is spread across 2-3 floors"}, {"text": "The property is in a quiet residential area or gated society"}, {"text": "Supermarket, Vegetable market, Restaurant, Pharmacy, Kids Play Area, ATM, Public Park and Bus/ Taxi Stand  are located within 5 kms from the property."}, {"text": "Hosts share common space with guests"}]}, {"category": "Caretaker Information", "id": "CaretakerInformation", "showInHost": true, "hostCatHeading": "Caretaker", "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Caretaker does not stay on the property"}]}, {"category": "Property Accessibility", "id": "PropertyAccessibility", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Not suitable for Elderly/Disabled"}, {"text": "Bed height is accessible"}, {"text": "The entire unit is not accessible by wheelchair"}, {"text": "The property has a wide entryway"}, {"text": "Property is accessible by stairs or steps"}, {"text": "We do not have a well-lit path to the entrance"}, {"text": "Property has a private entry for guests"}, {"text": "Upper Floors are not accessible by Lift"}, {"text": "Upper Floors are accessible by Stairs"}]}, {"category": "Pet(s) Related", "id": "PetsRelated", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Pets are allowed"}, {"text": "There are no pets living on the property"}]}, {"category": "Physical Distancing", "id": "PhysicalDistancing", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Social distancing protocols are followed"}, {"text": "Contactless Check-In and Checkout service is available"}, {"text": "Contactless Room service is available"}, {"text": "Physical Barriers are deployed at appropriate places"}, {"text": "Cashless Payment is available"}]}, {"category": "ID Proof Related", "id": "IDProofRelated", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Passport, Aadhar, Driving License and Govt. ID are accepted as ID proof(s)"}, {"text": "Office ID, PAN Card and Non-Govt IDs are not accepted as ID proof(s)"}, {"text": "Local ids are allowed"}]}, {"category": "Directions to reach the property", "id": "Directionstoreachtheproperty", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "The property is located at a distance of 90.0 KMs from the Pantnagar Airport. Take a taxi from Pantnagar Airport and de-board at Bhimtal. The estimated travel fare from the Pantnagar Airport to the property is 4000.0 INR and total travel time is 100 minutes."}]}, {"category": "Finding keys to the property", "id": "Findingkeystotheproperty", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Self check-in via Smart Door is not available"}, {"text": "Host Greets You & Helps You Check-in"}, {"text": "Caretaker Greets You & Helps You Check-in"}]}, {"category": "Other Rules", "id": "OtherRules", "showInHost": false, "showInDetailHome": false, "expandRules": false, "rules": [{"text": "Allows private parties or events"}, {"text": "There are no restrictions on entry and exit timings."}, {"text": "Visitors are allowed"}, {"text": "Early check in or Late Check out is subject to availability and may be chargeable by the Hotel Directly.\nIt is mandatory for Guest to Present valid photo Address proof ID upon Checkin.. "}]}], "contextRules": {"category": "Couple Friendly", "title": "<PERSON><PERSON><PERSON>, Bachelor Rules", "desc": "Unmarried couples/guests with Local IDs are allowed.", "tag": "Couple Friendly", "ruleIcon": "https://promos.makemytrip.com/Hotels_product/Details/Couplefriendly2x.png"}}, "mustReadRules": ["Office ID, PAN Card and Non-Govt IDs are not accepted as ID proof(s)", "Passport, Aadhar, Driving License and Govt. ID are accepted as ID proof(s)", "Property staff is trained on hygiene guidelines", "Credit/Debit cards not accepted at the property", "Smoking within the premises is not allowed", "Allows private parties or events", "Quarantine protocols are being followed as per local government authorities"], "listingType": "room", "foreignTravel": false, "losDealAvailable": false, "requestToBook": false, "rtbPreApproved": false, "rtbAutoCharge": false, "preApprovalExpired": false, "couponValidated": false, "sameRoomModification": false, "samePayModeModification": false, "mmtHotelCategory": "BUDGET", "stayTypeWithSize": "Room (150 sq.ft) in a Cottage", "anyRateAllinclusive": false, "lowestRateAllInclusive": false, "entireProperty": false, "groupBookingPrice": false, "maskedPrice": false, "currencyConvFactorINR": 1.0, "bestOffersEnabled": true, "showScrolldown": false, "freeCancellation": false, "altAcco": true, "budgetHotel": true, "rtbratePlanPreApproved": false, "suppressedPAHHotel": false, "allTarrifPAH": false, "recommendCouponBit": true, "breakFast": false, "breakFastAvailable": false, "userGCCAndMmtExclusive": false, "freeCancellationAvailable": false, "isBudgetHotel": true}], "correlationKey": "7779ce6c-d509-4c6d-8fa4-b677f144305e", "locusData": {"locusId": "CTGOI", "locusType": "city", "locusName": "Goa"}, "expData": {"HPI": "true", "ADC": "t", "OCCFCNR": "f", "EFF": "f", "VIDEO": "0", "APT": "t", "CHPC": "t", "LSTNRBY": "t", "AARI": "t", "RCPN": "t", "GBE": "f", "MRS": "t", "ADDON": "t", "RRR": "3", "NLP": "Y", "FBP": "t", "HAFC": "t", "BNPL": "t", "MMRVER": "V3", "BLACK": "t", "PDO": "PN", "DPCR": "1", "CGC": "t", "HRNB": "3", "FLTRPRCBKT": "t", "EMI": "f", "ST": "t", "SOC": "t", "AIP": "t", "SPCR": "2", "detailV3": "t", "ADDV": "f", "HIS": "1234", "CRF": "B", "APE": "10", "IAO": "t", "PAH": "5", "HSCFS": "4", "PAH5": "f", "LSOF": "t"}, "dayUsePersuasions": [{"id": "COUPLE_FRIENDLY", "text": "<font color=\"#000000\">Couple Friendly | Local ID accepted</font>", "icon": "", "iconType": ""}], "dayUseRecommendedRoomTypeDetails": [{"roomType": {"2312": {"type": "RoomType", "ratePlanList": {"************:MSE:1120:MSE:INGOHS": {"type": "RatePlan", "inclusions": [], "mealPlans": [{"startDate": 1649874600000, "endDate": 1649961000000, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 0, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": null, "cancelPenaltiesRule": null, "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": null, "description": "This booking is non-refundable and the tariff cannot be cancelled with zero fee."}, "tillDate": null, "sponsorer": null, "freeCancellationText": null, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 2499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2499.0, "averagePriceWithTax": 2499.0, "sellingPriceNoTax": 2499.0, "sellingPriceWithTax": 2499.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 2499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2499.0, "averagePriceWithTax": 2499.0, "sellingPriceNoTax": 2499.0, "sellingPriceWithTax": 2499.0, "basePriceWoCommision": 2499.0, "actualTax": 299.88}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 2499.0, "subTotal": 2499.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 299.88, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 134.95, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 2499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2499.0, "hotelTax": 300.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 135.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8KC2hUgBZk35QGQG9CZnUPk1TJkzhUJB0EtdlmQ+I38wmGwxjHiUUEURjnDIjrNd4LSKbKKIQGm1Hp3CxsViTaDbPKQiNLGMVNJE9Tt3bE606MPcaAVptAArivY7i9b3KYc4eqGhT+bz7CxwI+26qTJt0tS/dZTETZAIdKqt6OSGOHZtPSc5A8N22ue9YQcQJXxFY+uhAGS1VfzSXgzwb7BXadVJXQ17qiQtRjxLMUbbgPjsHAMmyCLmMijqp8HPI=", "pricingDivisor": 1, "totalTax": 435.0, "effectivePrice": 2499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 2499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 2499.0, "hotelTax": 300.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 135.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8KC2hUgBZk35QGQG9CZnUPk1TJkzhUJB0EtdlmQ+I38wmGwxjHiUUEURjnDIjrNd4LSKbKKIQGm1Hp3CxsViTaDbPKQiNLGMVNJE9Tt3bE606MPcaAVptAArivY7i9b3KYc4eqGhT+bz7CxwI+26qTJt0tS/dZTETZAIdKqt6OSGOHZtPSc5A8N22ue9YQcQJXxFY+uhAGS1VfzSXgzwb7BXadVJXQ17qiQtRjxLMUbbgPjsHAMmyCLmMijqp8HPI=", "pricingDivisor": 1, "totalTax": 435.0, "effectivePrice": 2499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 249.9, "gstDiscountHotelierCoupon": 29.99, "hotelTax": 299.88, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGOHS", "costPrice": 1959.21, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 2499.0, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 2499.0, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2022-04-14", "endDate": "2022-04-15", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_2312-_45000219796-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_lJp-_4pt-_2Ax-_0-_41u-_Tz-_.-_bt4-_6Wq-_0-_INR", "promotionsList": [null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 134.95}], "ratePlanDesc": "Standard Room", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "inclusionAndPolicyAvailVar": true, "suppressPas": false, "reviewDeeplinkUrl": "https://www.makemytrip.com/hotels/hotel-review?hotelId=201411211336321525&checkin=04142022&checkout=04152022&country=IN&city=CTGOI&_uCurrency=INR&roomStayQualifier=5e0e&locusId=CTGOI&locusType=city&hotelName=Kings+Cottage&countryName=India&cityName=Bhimtal&isEntireProperty=false&funnelName=DAYUSE&roomCriteria=2312~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~2e0e~~~28~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~3e0e~~&searchType=R&mtKey=0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_2312-_45000219796-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_lJp-_4pt-_2Ax-_0-_41u-_Tz-_.-_bt4-_6Wq-_0-_INR&suppDetail=INGOHS&payMode=PAS", "ratePlanCode": "************:MSE:1120:MSE:INGOHS", "suppressed": false, "notices": [], "bnplExtended": false, "pahx": false, "rpcc": "************", "groupModifiedRatePlanCode": "************:MSE:1120:MSE:INGOHS", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "allInclusiveRate": false, "vendorRoomCode": "45000219796", "packageRoomRatePlan": false}}, "roomTypeName": "Standard Room", "roomTypeCode": "2312", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": null, "roomImages": null, "selectedAmenities": null}, "28": {"type": "RoomType", "ratePlanList": {"************:MSE:1120:MSE:INGOHS": {"type": "RatePlan", "inclusions": [], "mealPlans": [{"startDate": 1649874600000, "endDate": 1649961000000, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 0, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 1, "bedCount": 1}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": null, "cancelPenaltiesRule": null, "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": null, "description": "This booking is non-refundable and the tariff cannot be cancelled with zero fee."}, "tillDate": null, "sponsorer": null, "freeCancellationText": null, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 3499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 3499.0, "averagePriceWithTax": 3499.0, "sellingPriceNoTax": 3499.0, "sellingPriceWithTax": 3499.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 3499.0, "maxFraction": 0.0, "avgeragePriceNoTax": 3499.0, "averagePriceWithTax": 3499.0, "sellingPriceNoTax": 3499.0, "sellingPriceWithTax": 3499.0, "basePriceWoCommision": 3499.0, "actualTax": 419.88}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 3499.0, "subTotal": 3499.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 419.88, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 188.95, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 3499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 3499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 3499.0, "hotelTax": 420.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 189.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/VD5nBFExcUIjyu/QRr1O/E7ehzdFfOgQ4RDAh8yudiy1uuzMqHUnISKQytrytB53ij4lev1Uek9qsqd8ZnBT5ZpNQ1lwQfIi6pjwCfy5XAtug9s1ezJT92oKHvPfvQPeMI0wu+9+CsiqKR9s5mp46B4apyyt3Hmmcmn9xVJm80hC1pVWkVPac8u+tbhXKjW801UxEDoTiNxo8IqpIqv173IXdlYqtmOS3eKfDq46tEtJRKXCQDjHFrFZ8HJetsf2u/DTykcI+BA==", "pricingDivisor": 1, "totalTax": 609.0, "effectivePrice": 3499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 3499.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 3499.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 3499.0, "hotelTax": 420.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 189.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/VD5nBFExcUIjyu/QRr1O/E7ehzdFfOgQ4RDAh8yudiy1uuzMqHUnISKQytrytB53ij4lev1Uek9qsqd8ZnBT5ZpNQ1lwQfIi6pjwCfy5XAtug9s1ezJT92oKHvPfvQPeMI0wu+9+CsiqKR9s5mp46B4apyyt3Hmmcmn9xVJm80hC1pVWkVPac8u+tbhXKjW801UxEDoTiNxo8IqpIqv173IXdlYqtmOS3eKfDq46tEtJRKXCQDjHFrFZ8HJetsf2u/DTykcI+BA==", "pricingDivisor": 1, "totalTax": 609.0, "effectivePrice": 3499.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 349.9, "gstDiscountHotelierCoupon": 41.99, "hotelTax": 419.88, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGOHS", "costPrice": 2743.21, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 3499.0, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 3499.0, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2022-04-14", "endDate": "2022-04-15", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_28-_45000423308-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_14Fp-_6lt-_32x-_0-_5du-_fz-_.-_r34-_98q-_0-_INR", "promotionsList": [null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 188.95}], "ratePlanDesc": "family room", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "inclusionAndPolicyAvailVar": true, "suppressPas": false, "reviewDeeplinkUrl": "https://www.makemytrip.com/hotels/hotel-review?hotelId=201411211336321525&checkin=04142022&checkout=04152022&country=IN&city=CTGOI&_uCurrency=INR&roomStayQualifier=5e0e&locusId=CTGOI&locusType=city&hotelName=Kings+Cottage&countryName=India&cityName=Bhimtal&isEntireProperty=false&funnelName=DAYUSE&roomCriteria=2312~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~2e0e~~~28~%7C~************%3AMSE%3A1120%3AMSE%3AINGOHS~%7C~3e0e~~&searchType=R&mtKey=0-_201411211336321525-_5e0e-_2022-04-14-_2022-04-15-_312379-_7779ce6c-d509-4c6d-8fa4-b677f144305e-_f-_1649057678093-_HOTEL-_28-_45000423308-_org%23b2c%23nil%23nil%23b2c-_.-_************%3AMSE%3A1120%3AMSE%3AINGOHS-_.-_EP-_INGOHS-_.-_14Fp-_6lt-_32x-_0-_5du-_fz-_.-_r34-_98q-_0-_INR&suppDetail=INGOHS&payMode=PAS", "ratePlanCode": "************:MSE:1120:MSE:INGOHS", "suppressed": false, "notices": [], "bnplExtended": false, "pahx": false, "rpcc": "************", "groupModifiedRatePlanCode": "************:MSE:1120:MSE:INGOHS", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "allInclusiveRate": false, "vendorRoomCode": "45000423308", "extraPaxDetails": {"extraAdults": [{"buyingPrice": 410.0, "sellingPrice": 500.0, "promoAmount": 0.0, "tax": 60.0, "serviceFee": 0.0, "bedAvailable": true, "bedType": "<PERSON><PERSON>"}]}, "extraGuestDetail": {"hotelDetailExtraBedText": "Extra bed for 1 adult included in the price.", "roomSelectionExtraBedText": "Existing bed(s) can accommodate 2 adults", "roomSelectionDetailExtraBedText": "Existing bed(s) can accommodate 2 adults#The type of extra bed provided by the property is Mattress. (Subject to availability).", "ratePlanExtraBedText": "Room Only included for 3 guests", "ratePlanDetailExtraBedText": "Room Only included for 3 guests"}, "packageRoomRatePlan": false}}, "roomTypeName": "family room", "roomTypeCode": "28", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": null, "roomImages": null, "selectedAmenities": null}}, "totalDisplayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 5998.0, "maxFraction": 0.0, "avgeragePriceNoTax": 5998.0, "averagePriceWithTax": 5998.0, "sellingPriceNoTax": 5998.0, "sellingPriceWithTax": 5998.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "actualPrice": {"value": 5998.0, "maxFraction": 0.0, "avgeragePriceNoTax": 5998.0, "averagePriceWithTax": 5998.0, "sellingPriceNoTax": 5998.0, "sellingPriceWithTax": 5998.0, "basePriceWoCommision": 0.0, "actualTax": 0.0}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 5998.0, "subTotal": 5998.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 2, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 719.76, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 323.9, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 5998.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5998.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 5998.0, "hotelTax": 720.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 324.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "couponInfo": {"couponCode": "MMTBESTBUYAP", "type": "ECOUPON", "description": "Exclusive offer for Early Bookers. Get INR 3 Off", "discountAmount": 3, "doubleDiscountEnabled": false, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 3}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 4.000000000000001, "configId": "Config:22455:2022-06-06T18:35:16", "configPriority": 180, "configType": "FLAT_DISCOUNTER", "disabled": false}, "pricingKey": "s9rHC9RN7n8ALU+Q0yVpDhTCUpVCZdrjQH5yY7NXd6BUJEsow4AZOgTxIE2mBaxd0yHJgnTY0Of6Cm8rxZQc+kwDlG81dlS6O6e4G9eoRXQQ4QvLrnwudPovohLNizXNAZLsUKXMD0f3OUjttbi+5onZN557Ep2pO7f1AUHY9rdbeCdwV/d2p4FbYRE+apVJMvJBgeHkH9+75KRZoypnsP3liwXwITF90syzhx9JWQ2D3uX5jd6HSOi4SZyhMFzmVdU0DoOWPA0=", "pricingDivisor": 1, "totalTax": 1044.0, "effectivePrice": 5998.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 599.8, "gstDiscountHotelierCoupon": 71.98, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "displayPriceBreakDownList": [{"displayPrice": 5998.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5998.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 5998.0, "hotelTax": 720.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 324.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8ALU+Q0yVpDhTCUpVCZdrjQH5yY7NXd6BUJEsow4AZOgTxIE2mBaxd0yHJgnTY0Of6Cm8rxZQc+kwDlG81dlS6O6e4G9eoRXQQ4QvLrnwudPovohLNizXNAZLsUKXMD0f3OUjttbi+5onZN557Ep2pO7f1AUHY9rdbeCdwV/d2p4FbYRE+apVJMvJBgeHkH9+75KRZoypnsP3liwXwITF90syzhx9JWQ2D3uX5jd6HSOi4SZyhMFzmVdU0DoOWPA0=", "pricingDivisor": 1, "totalTax": 1044.0, "effectivePrice": 5998.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 599.8, "gstDiscountHotelierCoupon": 71.98, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}], "conversionFactor": 1.0, "hotelierCouponDiscount": 599.8, "gstDiscountHotelierCoupon": 71.98, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "occupancyDetails": {"adult": 5, "child": 0, "infant": 0, "numOfRooms": 2, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 1, "bedCount": 1}, "comboMealPlan": "EP", "staycationDeal": false, "comboPayMode": "PAS", "baseCombo": false, "slot": {"duration": 3, "timeSlot": "10"}}]}