{"persistedData": {"hotelList": [{"hotelid": "201402021911303910", "hotelInfo": {"hotelIdContext": "MSE", "hotelId": "201402021911303910", "name": "The Piccadily", "chainCode": null, "chainName": null, "cityCode": "CTDEL", "cityName": "Delhi", "countryCode": "IN", "ratePlanCode": null, "roomTypeCode": null, "pahApplicable": false, "ratePlanDesc": null, "segmentId": null, "ratePlanType": null, "roomTypeName": null, "roomTypeDescription": null, "addressLines": ["Janakpuri, District Centre Complex, Near Janak Puri West Metro Station", "Janakpuri"], "checkInTime": "2 PM", "checkOutTime": "12 PM", "hotelIcon": "https://gos3.ibcdn.com/558950a0734c11e7b21e0a4cef95d023.jpg", "categories": ["MySafety - Safe and Hygienic Stays", "Culinary", "Couple Friendly", "Great Value Deals", "HighFiveV2_NonChain_Target", "Premium", "Inmarket", "City Center Hotels"], "checkin24Hr": false, "availDetails": null, "roomsRequested": 0, "altAcco": false, "mappedActualCity": null, "taxExcluded": "false", "primeHotel": true, "isBNPLHotel": true, "bnplBaseAmount": 1.0, "bestPriceGuaranteed": false, "locationId": "CTGOI", "accelerated": false, "gdsHotelCode": "1000009130", "starRating": 5, "propertyType": "Hotel", "addOnBucketId": "ADDON_FLT1_OLA,EAZY_DINE_ADDON,PAH_HOTELIER_ADDON", "acmeTags": null, "acmeCDFBucket": "HTLADDON", "doubleBlackInfo": null, "specialRequestAvailable": {"categories": [{"code": "101", "name": "Smoking room", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "102", "name": "Late check-in", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1021", "name": "CHECK-IN TIME", "type": "DROPDOWN", "placeholder": null, "values": ["04:00 PM", "05:00 PM", "06:00 PM", "07:00 PM", "08:00 PM", "09:00 PM", "10:00 PM", "11:00 PM"], "subCategories": null}]}, {"code": "103", "name": "Early check-in", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1031", "name": "CHECK-IN TIME", "type": "DROPDOWN", "placeholder": null, "values": ["06:00 AM", "07:00 AM", "08:00 AM", "09:00 AM", "10:00 AM", "11:00 AM", "12:00 AM"], "subCategories": null}]}, {"code": "104", "name": "Room on a high floor", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "105", "name": "Large bed", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "106", "name": "Twin beds", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "107", "name": "Airport transfer", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1071", "name": "PICKUP TIME", "type": "DROPDOWN", "placeholder": null, "values": ["12:00 AM", "01:00 AM", "02:00 AM", "03:00 AM", "04:00 AM", "05:00 AM", "06:00 AM", "07:00 AM", "08:00 AM", "09:00 AM", "10:00 AM", "11:00 AM", "12:00 PM", "01:00 PM", "02:00 PM", "03:00 PM", "04:00 PM", "05:00 PM", "06:00 PM", "07:00 PM", "08:00 PM", "09:00 PM", "10:00 PM", "11:00 PM"], "subCategories": null}, {"code": "108", "name": "You may be charged for this trip by the hotel.", "type": "LABEL", "placeholder": "In case of any issues, Please get in touch with the hotelier directly.", "values": null, "subCategories": null}]}, {"code": "109", "name": "Any other request?", "type": "INPUTBOX", "placeholder": "In case of any issues, Please get in touch with the hotelier directly.", "values": null, "subCategories": null}], "disclaimer": "Special requests are subject to each hotel's availability, may be chargeable & can't be guaranteed."}, "specialRequest": null, "hotelBucket": "<PERSON><PERSON>_<PERSON><PERSON>,<PERSON><PERSON><PERSON>,ARR7,DH_MMT,META_MMT,MB_ASSURED,INC_HPA,ASP,HPA3,TA3,TVG3,GOSTAY_DEC,B2C_DND,MOB_DND,HTL_DEC2,ECOM25,DH_ECOM,DHSF,GHCP", "pahAuthenticationEnabled": false, "gstin": "07AACCP3205F1ZW", "oldCityCode": "DEL", "oldCountryCode": "IN", "areaIdList": ["ARJANA", "ARWESTD", "ARJANAKPURID"], "regionCodeList": ["RGNCR"], "mandatoryCharges": null, "notices": null, "countryName": "India", "stateCode": "STDEL", "stateName": "Delhi", "locusCityCode": "CTDEL", "locusCountryCode": "IN", "locusData": {"locusId": "CTGOI", "locusType": "city", "locusName": "Goa"}, "lat": 28.629517, "lng": 77.07811, "corpRateAvailable": false, "corpRateExpected": false, "emiBucket": "", "policyToMessagesMap": {"Extra Bed": [""], "Hotel Policy": ["According to government regulations, a valid Photo ID has to be carried by every person above the age of 18 staying at The Piccadily. The identification proofs accepted are Drivers License, Voters Card, Passport, Ration Card. Without valid ID the guest will not be allowed to check in.", "The primary guest checking in to the hotel must be at least 18 years of age.", "Early check-in or late check-out is subject to availability and may be chargeable by The Piccadily. The standard check-in time is 2 PM and the standard check-out time is 12 PM. After booking you will be sent an email confirmation with hotel phone number. You can contact the hotel directly for early check-in or late check-out.", "The room tariff includes all taxes. The amount paid for the room does not include charges for optional services and facilities (such as room service, mini bar, snacks or telephone calls). These will be charged at the time of check-out from the Hotel.", "MakeMyTrip will not be responsible for any check-in denied by the Hotel due to the aforesaid reason. ", "The Piccadily reserves the right of admission. Accommodation can be denied to guests posing as a 'couple' if suitable proof of identification is not presented at check-in.MakeMyTrip will not be responsible for any check-in denied by the Hotel due to the aforesaid reason. ", "The Piccadily reserves the right of admission for local residents. Accommodation can be denied to guests residing in the same city.MakeMyTrip will not be responsible for any check-in denied by the Hotel due to the aforesaid reason. ", "For any update, User shall pay applicable cancellation/modification charges.", "Modified bookings will be subject to availability and revised booking policy of the Hotel.", "The cancellation/modification charges are standard and any waiver is on the hotel's discretion.", "Number of modifications possible on a booking will be on the discretion of MakemyTrip.", "Selective offers of MakeMyTrip will not be valid on a cancellation or modification of booking.", "Any e-coupon discount on the original booking shall be forfeited in the event of cancellation or modification."], "Cancellation Policy": ["Cancellation and prepayment policies vary according to room type. Please check the Fare policy associated with your room."], "Payment Mode": ["You can pay now or you can pay at the hotel if your selected room type has this option."], "Check In/out": ["Hotel Check-in Time is 2 PM, Check-out Time is 12 PM."]}, "houseRules": {"commonRules": [{"category": "Guest Profile", "id": "Guest<PERSON><PERSON><PERSON><PERSON>", "rules": [{"text": "Suitable for children"}, {"text": "Bachelors allowed"}, {"text": "Unmarried couples allowed"}]}, {"category": "Safety and Hygiene", "id": "SafetyandHygiene", "rules": [{"text": "Social distancing measures are present in restaurant(s)"}, {"text": "Utensils, crockery and supplies are sanitized before entering the kitchen"}, {"text": "Masks and hair nets are mandatory for staff in restaurants"}, {"text": "Quarantine protocols are being followed as per local government authorities"}, {"text": "All rooms are disinfected using bleach or other disinfectant"}, {"text": "Guests with fever are not allowed"}, {"text": "Only those guests with safe status on Aarogra Setu app are allowed"}, {"text": "Guests coming from containment zones are not allowed"}]}, {"category": "Payment Related", "id": "PaymentRelated", "rules": [{"text": "No security deposit is required"}, {"text": "Credit/debit cards are accepted"}]}, {"category": "Food Arrangement", "id": "FoodArrangement", "rules": [{"text": "Outside food is not allowed in property premises"}]}, {"category": "Smoking/Alcohol consumption Rules", "id": "SmokingAlcoholconsumptionRules", "rules": [{"text": "Smoking within the premises is allowed"}, {"text": "There are some restrictions on alcohol consumption."}, {"text": "Alcohol consumption is allowed in the room"}]}, {"category": "Space Related", "id": "SpaceRelated", "rules": [{"text": "Guests wont have to climb stairs to reach the room"}, {"text": "There are no restrictions on food for guests"}]}, {"category": "Pet(s) Related", "id": "PetsRelated", "rules": [{"text": "Pets are not allowed."}]}, {"category": "Society Rules", "id": "SocietyRules", "rules": [{"text": "Only State level Travel Restrictions applicable"}]}, {"category": "ID Proof Related", "id": "IDProofRelated", "rules": [{"text": "Passport, Driving License, Aadhar and Govt. ID are accepted as ID proof(s)"}, {"text": "Non-Govt ID is not accepted as ID proof(s)"}]}, {"category": "Directions to reach the property", "id": "Directionstoreachtheproperty", "rules": [{"text": "Nearest Airport - \tIndira Gandhi International Airport, 19 Kms, 50 mins\nNearest Railway Station - \tNew Delhi Railway Station, 19.9 Kms, 50 mins\nNearest Bus Station - \tInter State Bus Terminal - Kashmere Gate, 25 Kms, 66 mins\nNearest Metro Station  - \tJanakpuri West Metro Station, 0.6 kms, 2 mins\n"}]}]}, "mustReadRules": ["Guests with fever are not allowed", "Only those guests with safe status on Aarogra Setu app are allowed", "Guests coming from containment zones are not allowed", "Non-Govt ID is not accepted as ID proof(s)", "Passport, Driving License, Aadhar and Govt. ID are accepted as ID proof(s)", "Pets are not allowed.", "Outside food is not allowed in property premises", "All rooms are disinfected using bleach or other disinfectant", "Masks and hair nets are mandatory for staff in restaurants", "Social distancing measures are present in restaurant(s)", "Utensils, crockery and supplies are sanitized before entering the kitchen", "Quarantine protocols are being followed as per local government authorities"], "listingType": "Room By Room", "pahxavailable": false, "couponApplicable": true}, "walletPahApplicable": true, "tariffInfoList": [{"ratePlanCode": "************:MSE:1120:MSE:INGO", "roomCode": "2312", "roomTypeName": "Standard Room", "absorptionInfo": null, "promotions": [{"description": "discount 5 percentage for b2c segment .", "code": "**********,", "priceSlasher": true, "promoLevel": "tariffLevel"}, {"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 365.75}], "inclusions": [{"id": null, "value": "20 % Discount On Other F&B Services : Services - Food & Beverage Service", "code": "20 % Discount On Other F&B Services : Services - Food & Beverage Service", "imageURL": null, "tags": null, "category": null, "tagList": null, "segmentIdentifier": null}], "mealPlans": [], "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelPenaltiesRule": null, "penaltyDescription": {"name": null, "description": "From booking date to 2020-08-19 14:00:00,0% penalty will be charged.From 2020-08-19 14:00:00 to 2020-08-19 14:00:00,100% penalty will be charged.In case of no show : no refund.Booking cannot be cancelled/modified on or after the check in date and time mentioned in the Hotel Confirmation Voucher. All time mentioned above is in destination time."}, "tillDate": "19-Aug-2020 14:00", "sponsorer": null, "freeCancellationText": "Free Cancellation before 19-Aug-2020 14:00", "mostRestrictive": "Y"}], "originalCancelPenaltyList": null, "childPolicy": null, "displayFare": {"tax": {"value": 365.75}, "slashedPrice": {"value": 5852.0, "maxFraction": 0.0, "avgeragePriceNoTax": 5852.0, "averagePriceWithTax": 6217.75, "sellingPriceNoTax": 5852.0, "sellingPriceWithTax": 6217.75}, "actualPrice": {"value": 6127.0, "maxFraction": 0.0, "avgeragePriceNoTax": 6127.0, "averagePriceWithTax": 6492.75, "sellingPriceNoTax": 6127.0, "sellingPriceWithTax": 6492.75}, "bestCouponByPaymode": {"PAS": {"couponCode": "BESTBUYMMT1", "type": "ECOUPON", "description": "Get inr 313.00 OFF", "discountAmount": 313.0, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 313.0, "cashbackAmount": 0.0, "cdfValidated": false, "ecoupon": false}], "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 313.0}, "tncUrl": "https://www.makemytrip.com/promos/dh-hdfc-18032019.html", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 6217.75, "subTotal": 6127.0, "discount": 275.0, "taxes": 365.75, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "taxDetails": {"hotelTax": 627.0, "markup": 365.75, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 365.75, "ddMarkUp": 0.0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 6218.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 6493.0, "savingPerc": 9.0, "totalSaving": 588.0, "basePrice": 5500.0, "hotelTax": 627.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 366.0, "mmtDiscount": 275.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "couponInfo": {"couponCode": "BESTBUYMMT1", "type": "ECOUPON", "description": "Get inr 313.00 OFF", "discountAmount": 313.0, "doubleDiscountEnabled": false, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 313.0}, "tncUrl": "https://www.makemytrip.com/promos/dh-hdfc-18032019.html", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}, "pricingKey": "s9rHC9RN7n9e3xqVftDLc9B2QTczV1pADHyPU4N7ZW2MLrqi41q4ELAjYbRnBnjgwdbpjzroZBcVFZiCPve8iA6bhdVoN+Lf4P8lwtGtTTgaz+yOIYhvXyiyDERbG+LygTdfx6zLK7FQ3pkb1pfKmPOvZiuSNb7P1AGJSy2Xmz0g1uj4RnJ27UdkDBUuheYxTHGb4cOYca7T2LMk/S7moY5gy49mjwPplXQ2SvFcLDb7LthsF7yUDspI5AJ8agOFy0CAdnWDvIo=", "pricingDivisor": 1, "totalTax": 993.0, "effectivePrice": 5905.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "blackFactored": false, "taxIncluded": true}, "commission": 1149.5, "conversionFactor": 1.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": true, "originalBNPL": true, "dateOfDelayedPayment": 1597653000000, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 4977.5, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "************", "accessCode": null}, "roomTariff": [{"perNightPrice": 6127.0, "extraPrice": 0.0, "taxes": 365.75, "totalPricePerRoom": 6127.0, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 275.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-08-19", "endDate": "2020-08-20", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "specialInstructionList": null, "skuDetail": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}, "availDetails": {"status": "B", "count": 32, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "hotelReservationIDList": null, "paymentMode": "PAS", "overriddenCancelPolicy": false, "markUpAdjustment": null, "bnplExtended": false, "otherDetails": {"profit": 1515.25, "commission": 1149.5}, "corpMetaData": null, "checkinPolicy": null, "confirmationPolicy": {"mostRestrictive": "Y", "value": "test"}, "instantConfirmation": "NOT_APPLICABLE", "segmentId": "1120", "corpRate": false, "sellableType": "room", "roomDetails": {"roomSize": "144 sq.ft.", "beds": [{"type": "Standard Bed", "count": 1}], "maxGuestCount": 4, "maxAdultCount": 3, "maxChildCount": 2, "highlightedFacilities": [{"name": "Popular with Guests", "facilities": [{"name": "Room Service", "type": "room", "sequence": -20073, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Room service", "displayType": null, "highlightedName": "Room Service", "childAttributes": []}, {"name": "Air Conditioning", "type": "room", "sequence": -20070, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Air Conditioning", "displayType": null, "highlightedName": "Air Conditioning", "childAttributes": []}]}, {"name": "Room Features", "facilities": [{"name": "Charging Points", "type": "room", "sequence": -20058, "categoryName": "Room Features", "tags": null, "attributeName": "Charging points", "displayType": null, "highlightedName": "Charging Points", "childAttributes": []}]}], "facilityWithGrp": [{"name": "Basic Facilities", "facilities": [{"name": "Room Service", "type": "hotel", "sequence": -10144, "categoryName": "Basic Facilities", "tags": null, "attributeName": "Room service", "displayType": null, "highlightedName": "Room Service", "childAttributes": []}, {"name": "Free Parking", "type": "hotel", "sequence": -10163, "categoryName": "Basic Facilities", "tags": null, "attributeName": "Parking", "displayType": "2", "highlightedName": "Free Parking", "childAttributes": [{"name": "Free", "subAttributes": [{"name": "Onsite"}]}]}, {"name": "Salon", "type": "hotel", "sequence": 1942, "categoryName": "Beauty and Spa", "tags": null, "attributeName": "Salon", "displayType": "1", "highlightedName": "Salon", "childAttributes": [{"name": "Hair Cut", "subAttributes": []}, {"name": "Waxing", "subAttributes": []}]}]}], "images": ["//gos3.ibcdn.com/a53c8e6ad1ba11e7bd1a0224510f5e5b.jpg", "//gos3.ibcdn.com/c4e8c81c64d211e996580242ac110003.jpg", "//gos3.ibcdn.com/c8c8526864d211e9ae2c0242ac110003.jpg", "//gos3.ibcdn.com/cbf9da9c64d211e9816d0242ac110003.jpg", "//gos3.ibcdn.com/cf1561d864d211e9adec0242ac110003.jpg"]}, "pahx": false}], "rpcKey": "ERM_ST_2312_RPC_************:MSE:1120:MSE:INGO"}], "paymentInfo": {"expiryDate": null, "nameOnCard": null, "cardNumber": null, "cardCode": null, "amountAfterTax": 6217.75, "amountBeforeTax": 5225.0, "paymentMode": null, "billingCity": null, "billingState": null, "billingCountry": null, "billingPinCode": null, "payModeOption": null, "amountCharged": 0.0, "cardChargePolicy": null, "remainingAmount": 0.0, "amountChargedByOtherMedium": 0.0, "palBooking": false, "easyPayUrl": null, "payLaterTillDate": null, "amountChargedByWallet": 0.0, "chargedCurrency": null, "paymentModeType": null}, "bookingMetaInfo": {"soaResponseReferenceKey": null, "requestorType": null, "requestorId": null, "requestorIdContext": null, "requestorChannel": null, "token": null, "bookingId": null, "currencyCode": null, "earnedPoints": null, "loggedInUserName": null, "clientTokens": {"serverIps": "*********"}}, "couponInfo": {"RECOMMEND": [{"blockPhoneNumber": false, "amount": null, "promoType": null, "couponCode": "BESTBUYMMT1", "refKey": null, "pahApplicable": false, "successMessage": null, "successApplyMessage": null, "blockedPaymentOptions": null, "tncUrl": null, "hintEmail": null, "doubleDiscountEnabled": false, "cashbackAmount": 0.0, "cashbackProcessDays": null, "cdfValidated": false, "discountTypeCode": null, "email": null, "moreVerificationRequired": false, "paymentMsg": null, "dealCode": null, "currencyCode": null, "ecoupon": false, "hybridDiscounts": null, "couponDetails": [{"moreVerificationRequired": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 313.0, "cashbackAmount": 0.0, "cdfValidated": false, "ecoupon": false}], "adminValidated": false, "originalDiscount": 0.0, "walletIntensityPercentage": 0.0, "walletAmountDeducted": 0.0, "walletAmountApplicable": 0.0, "userWalletAmount": 0.0, "couponAdjustment": 0.0, "walletPlusDeducted": 0.0, "extraAmount": 0.0, "walletApplicableDeducted": 0.0, "brinAmount": 0.0, "wpmDeducted": 0.0, "ddApplicable": false, "gr_sp_discount": 0.0, "cdfHitRequired": false, "ddCoupon": null, "policyId": null, "policyVersion": null, "experimentId": null, "ddApplied": null, "paymentCoupon": false, "differentialAmt": 0.0, "couponFromPayment": false}], "REMOVED": []}, "availReqBody": {"affiliateId": "312379", "applicationId": "310", "channel": "Native", "checkin": "2020-08-19", "checkout": "2020-08-20", "cityCode": null, "countryCode": "IN", "currency": "inr", "guestRecommendationEnabled": {"maxRecommendations": "1", "text": "true"}, "hotelIds": ["201402021911303910"], "idContext": "B2C", "roomStayCandidates": null, "token": null, "requestType": "B2CAgent", "promotionReferenceCodes": null, "roomCode": null, "ratePlanCode": null, "mtKey": null, "visitorId": "00555421455074779786161380701755230313", "visitNumber": "1", "cdfContextId": null, "bookingDevice": "android", "deviceId": "6410d9524bcdd210", "couponCount": 3, "numberOfAddons": 0, "appVersion": "8.1.1.RC3", "lob": "android", "domain": "B2C", "pageContext": "REVIEW", "expiryRequired": false, "responseFilterFlags": {"soldOutInfoReq": null, "staticData": false, "roomLevelDetails": true, "freeCancellationAvail": null, "bestCoupon": true, "priceInfoReq": true, "applyAbsorption": true, "shortlistRequired": false, "topRatedRequired": null, "topRatedCommentRequired": null, "trustYouBadgeRequired": null, "walletRequired": true, "cityTaxExclusive": true, "addOnRequired": true, "mmtPrime": false, "persuasionSeg": null, "populateAllRates": null, "seekSummaryRequired": false, "flyfishSummaryRequired": false, "dealOfTheDayRequired": null, "checkAvailibility": false, "brinRequired": null, "doubleBlackUser": false, "blackUser": false, "newCorp": false, "locus": true, "unmodifiedAmenities": false, "poisRequiredOnMap": false, "hotelCatAndPropNotRequiredInMeta": false, "extraAltAccoPropertiesRequired": false, "limitedFilterCall": false, "corporateMMRLocationsRequired": false}, "experimentData": "{\"HPI\":\"true\",\"OCCFCNR\":\"t\",\"REV\":\"4\",\"CHPC\":\"t\",\"AARI\":\"t\",\"RCPN\":\"t\",\"HLMV\":\"t\",\"HBH\":\"f\",\"MRS\":\"t\",\"SRR\":\"t\",\"ADDON\":\"t\",\"FBP\":\"f\",\"MC\":\"t\",\"BNPL\":\"t\",\"PLRS\":\"t\",\"PDO\":\"PN\",\"DPCR\":\"0\",\"FLTRPRCBKT\":\"t\",\"EMI\":\"f\",\"SPCR\":\"2\",\"BRIN\":\"110\",\"ADDV\":\"f\",\"HRSRFHS\":\"true\",\"HIS\":\"DEFAULT\",\"WSP\":\"t\",\"APE\":\"35\",\"PAH\":\"5\",\"HSCFS\":\"4\",\"LVD\":\"\",\"PAH5\":\"f\",\"LCS\":\"t\",\"LVI\":\"\",\"isWBTH\":\"false\",\"newReviewPageBottomToolBarEnabled\":\"false\",\"hotelDetailCardOrder\":\"rc0,hfc1,qb2,man3,ws4,brn5,blk6,gr7,rr8,ef9,mbg10,ex11,ps12,aac13,alpd14,pd15,au16,db17,bpg18,am19,lcn20,aga21,ta22,ty23,cc24,fb25\",\"isPayLaterAnimationEnabled\":\"false\",\"allowPayLater\":\"false\",\"FilterVisibilityDesktop\":\"true\",\"bookingReviewV2\":\"true\",\"htlPolarisCard\":\"true\",\"multicurrencyEnabled \":\"false\",\"enableGuidedSearch\":\"false\",\"corpApprovalUI\":\"true\",\"AATEST\":\"0\",\"pwaCheckoutBtnSticky\":\"false\",\"gstOnTripTags\":\"true\",\"htlAltAccoCollection\":\"true\",\"checkoutReviewDaysCount\":\"3\",\"desktopImgSizeFactorDetail\":\"1.0\",\"enablePolarisIntl\":\"false\",\"htlEnableQuickDatesAltAcco\":\"false\",\"newListingHotelCorporate\":\"false\",\"pwaImgSizeFactorDetail\":\"1.0\",\"hotel_new_ugc_card\":\"false\",\"recommendationOnSoldout\":\"true\",\"IAB_Details\":\"IAB_F\",\"desktopImgSizeFactorListing\":\"1.0\",\"filterBasePricing\":\"false\",\"corplEnableSavedLocations\":\"true\",\"newListingAltaccoDom\":\"true\",\"travellerImageBucketsSectionExp\":\"false\",\"newListingCorporate\":\"true\",\"pwaShowNewReviewPg\":\"false\",\"enableNewAboutProperty\":\"false\",\"forkFcnr\":\"true\",\"specialInvalidCorporate\":\"false\",\"test_config_str\":\"test\",\"htl_locus_enabled_altAcco\":\"false\",\"LocusDTFunnel\":\"true\",\"hotelNewMapViewShowRegion\":\"true\",\"enableCorpMultiroom\":\"false\",\"locusLocationFilterEnabled\":\"true\",\"enableNewPaymentScreen\":\"true\",\"shouldLaunchGamification\":\"false\",\"htlShowAcmeFrgament\":\"true\",\"review_intent_show\":\"true\",\"whichAreaGuide\":\"LOCATION_FILTER\",\"htlQuickBookRatingBar\":\"1\",\"showVideoOnDetail\":\"true\",\"htlNewHeaderFilterUi\":\"true\",\"showAltAccoFilterintl\":\"false\",\"htlReviewNewUI\":\"true\",\"apiExperiment\":\"35\",\"DynamicDiscount\":\"10\",\"payLaterTimeLine\":\"true\",\"homePageReviewCollectionPopUp\":\"true\",\"newAboutPropertyCard\":\"false\",\"imgSizeFactorExp\":\"false\",\"hotelNewMapView\":\"true\",\"specialRequestCorporate\":\"true\",\"htl_show_pan_card_optional\":\"1\",\"BNVsSR\":\"100\",\"altAccoNewLanding\":\"false\",\"htlGCCThankYouV2\":\"false\",\"NewHeader\":\"false\",\"LocusDTFunnelHotels\":\"true\",\"bookingReviewFooterV2\":\"true\",\"enableMultiCurrency\":\"false\",\"htlShowNearByHotels\":\"true\",\"newListingHotelDom\":\"true\",\"htl_brin_new_ui\":\"false\",\"mobGenConfigVariant\":\"A\",\"AAEXP\":\"1\",\"reviewDetailCardOrderB2C\":\"pd,dbc,pr,bpb,cd,af,td,sr,tc,pb\",\"enablePolarisFlow\":\"true\",\"showMapCorporate\":\"false\",\"forcedLogin\":\"DEFAULT_FLOW\",\"customerReviewNewDesign\":\"false\",\"showNewBrinAnimation\":\"false\",\"listingHeaderWithMapIconExp\":\"false\",\"htl_new_thank_you_corp\":\"false\",\"showEnhancedSeekReviews \":\"true\",\"shouldshowQBRReviewTags\":\"false\",\"htlPricingExperiment\":\"PN\",\"htlDetailNewRatingCard\":\"true\",\"htl_new_thank_you_gcc\":\"false\",\"htlB2BThankYouV2\":\"false\",\"pwaImgSizeFactorListing\":\"1.0\",\"imageDeviceResolution\":\"true\",\"mapExploration\":\"false\",\"videoPositionOnListing\":\"0\",\"mapSearchBar\":\"true\",\"htlDarkFilterUiWithMapIcon\":\"true\",\"corpHotelDetailCardOrder\":\"rc0,hfc1,qb2,man3,ws4,brn5,blk6,gr7,rr8,ef9,mbg10,ex11,ps12,aac13,alpd14,pd15,au16,db17,bpg18,am19,lcn20,aga21,ta22,ty23,cc24,fb25\",\"newListingHotelIntl\":\"true\",\"corpPayLaterTimeLine\":\"true\",\"rmStarRatingAltAcco\":\"true\",\"htlLocusEnabledAltAcco\":\"true\",\"EMIDT_NEW\":\"1\",\"locusLocationFilterDisabledUI\":\"false\",\"personalEnableSavedLocations\":\"true\",\"htlLocusEnabledCorp\":\"false\",\"showVideos\":\"false\",\"newReviewPageEnabled\":\"false\",\"htl_Cross_Sell_Comparator\":\"2\",\"AndroidImageE2Hotels\":\"true\",\"htlAltAccoWidgetType\":\"1\",\"isPayLaterEnabled\":\"false\",\"payLaterListingAnimation\":\"true\",\"altAccoLandingTravelTypeView\":\"1\",\"corporateMultiroom\":\"false\",\"htlB2CThankYouV2\":\"true\",\"hotelDetailCardorder_ios\":\"RC1,MAN2,WPM3,BRN4,WOO5,QB6,RD7,MBG8,AAH9,SA10,EF11,WBH12,HRP13,TA14,HO15,AM16,ATH17,DB18,HMD19,BPL20,AAM21,HPC22\",\"htlPolarisCardIntl\":\"false\",\"hotelWidgetType\":\"3\",\"APEINTL\":\"36\",\"isMoCharter\":\"true\",\"newCorpApprovalPage\":\"true\",\"3.\\thtlNewHeaderFilterUi\":\"false\",\"shouldHideAltAccoSearchDateSelection\":\"true\",\"PAYNEW\":\"true\",\"enableLocusLocationFilter\":\"false\",\"imageParamChanges\":\"true\",\"selectroomvariant\":\"3\",\"newHeaderFilterCorp\":\"true\",\"htldetailCompareWidget\":\"1\",\"consumeManualFlyFishDom\":\"true\",\"payAtHotelB2B\":\"true\",\"allowNRRatesCorporate\":\"true\",\"shouldEnableNearByHotelList\":\"false\",\"listingMap\":\"true\",\"hotelImageCount\":\"100\",\"imagePrefetchEnabled\":\"true\",\"myBizHotelPricing\":\"false\",\"consumeManualFlyFishIntl\":\"true\",\"galleryDesign\":\"true\",\"htlNewHeaderFilterUiWithMapIcon\":\"true\",\"corpShowSimilarHtls\":\"false\",\"hotelSequenceCorporate\":\"sign-hsq130\",\"htlCorpWidgetType\":\"1\",\"newListingAltaccoIntl\":\"true\",\"myBizHotelPricingiOS\":\"false\",\"corpFCtimeline\":\"true\",\"listingHeaderFooterFilterExp\":\"false\",\"htlBrinV2\":\"110\",\"FilterFlow\":\"OLD\",\"htl_locus_enabled_corp\":\"false\",\"policyvariant\":\"false\",\"checkoutReviewSessionCount\":\"3\",\"imageDeviceResolutionFactor\":\"1.0\",\"hotelMobConfigVariant\":\"A\",\"NewHotelMaps\":\"false\",\"subConceptsNewDesign\":\"false\",\"autoPlayVideo\":\"false\",\"showNewListing\":\"true\",\"showPancard\":\"false\",\"Pay Later Desktop\":\"false\",\"htlDummyPanCardExperiment\":\"1\",\"htl_new_thank_you_b2c\":\"false\",\"isPwaForcedLogin\":\"true\",\"showNewAltAccoFilterIntl\":\"false\",\"myBizHotelPricingAndroid\":\"false\"}", "correlationKey": "fec89d03-6f73-42e3-9bbb-3e572408b3ee", "isWalletRequired": null, "nationalityCode": null, "os": null, "supplierCode": "SNXX0002", "payMode": "PAS", "roomCriteria": [{"hotelId": "201402021911303910", "mtKey": "N$$s9rHC9RN7n%2FSlJQ9515yZbTSg8%2F%2BVeXhzREOUOwAN6VoiU5NB5dm%2FB41%2F9tqKlOPQKd3U3kJeGDgyQmR7VHPtIr6oJdiVHMNsKv0NqAD8o5xxwYIh1nkSNjuklABk8qxhtJ82BH49T%2FbCB5MVw7yDDqC%2FF8fP2pMiu%2B9K1kW4LVcaILy6Xn5sOHdudySMZSRUqquQSBJGv1fvijOMJIl2SpwkfhXkbDeqeh36gNu%2F3LkbQrQv%2FqYJLHLOfmx3%2Bdzows9BUILrblR1KBNOORaYG0qysFfV9cW%2Fclson8c9m1akHmDaTXPqkPn%2BA9AIQham0cwgdfoMPhGtLVAvTYZo21BOuHDkb%2FC0XplZfZEYa0%3D", "ratePlanCode": "************:MSE:1120:MSE:INGO", "roomCode": "2312", "roomStayCandidates": [{"guestCounts": [{"count": "2", "ageQualifyingCode": "10", "ages": null}]}], "supplierCode": "INGO", "pricingKey": "s9rHC9RN7n9GqPUNj6+qQ6ci6yfF5ISRHQD57u3QjGCoQ0zL1qT+YYGZwqp8UMQhp7a3sjqPkISW05GA7WdcE+1Z3IIc4K1NgmqrlLnnaiHcI4VKOIkCTLlZKDfnSmTsmSkUNQlFFtH1i0tf0L+VUlKTFqUGr7ABg+DaRuNjkIoHUcnn47IF2Z6fan8BAEta4kRwtDROVRyTwivfQQ2YD8IN1xklC8du1bREMjGAFY00RZHCRjK8YqtqbIIyPAJ+WvcjuxXI2Jf8ziZyPWBNRg==", "rpcc": "************"}], "userAgent": "PostmanRuntime/7.26.2", "deviceUserName": "", "extraInfo": {"searchType": "E"}, "firstTimeUser": false, "srLat": null, "srLng": null, "authToken": null, "profileType": null, "uuid": null, "emailCommId": null, "phoneCommId": null, "trafficSource": null, "notifCoupon": null, "paymentChannel": "Native", "deviceType": "Mobile", "bestOffersRequired": null, "bestOffersLimit": 0, "pricingKey": null, "ebizDetails": null, "netRateSelected": false, "hydraSegments": null, "flightBooker": false, "wizardUser": false, "pnAvlbl": false, "quoteId": null, "emiDetails": null, "locationId": "CTGOI", "locationType": "city", "srCon": null, "srCty": null, "funnelSource": "HOTELS", "travelerDetailsList": null, "mcid": null, "corpSuppressHotelChains": null, "siteDomain": "in", "domainCountry": null, "domainLanguage": null, "appliedFilterMap": null, "corpUserID": null, "mobile": null, "email": null, "corpAuthCode": null, "firstTimeUserState": 0, "loggedIn": false}, "referenceKey": "99465976-72d9-47bc-b37f-e3b8b455aa6e", "totalDisplayFare": {"tax": {"value": 365.75}, "slashedPrice": {"value": 5852.0, "maxFraction": 0.0, "avgeragePriceNoTax": 5852.0, "averagePriceWithTax": 6217.75, "sellingPriceNoTax": 5852.0, "sellingPriceWithTax": 6217.75}, "actualPrice": {"value": 6127.0, "maxFraction": 0.0, "avgeragePriceNoTax": 6127.0, "averagePriceWithTax": 6492.75, "sellingPriceNoTax": 6127.0, "sellingPriceWithTax": 6492.75}, "bestCouponByPaymode": {"PAS": {"couponCode": "BESTBUYMMT1", "type": "ECOUPON", "description": "Get inr 313.00 OFF", "discountAmount": 313.0, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 313.0, "cashbackAmount": 0.0, "cdfValidated": false, "ecoupon": false}], "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 313.0}, "tncUrl": "https://www.makemytrip.com/promos/dh-hdfc-18032019.html", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 6217.75, "subTotal": 6127.0, "discount": 275.0, "taxes": 365.75, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "taxDetails": {"hotelTax": 627.0, "markup": 365.75, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 365.75, "ddMarkUp": 0.0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "dda": "0.0", "displayPriceBreakDown": {"displayPrice": 6218.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 6493.0, "savingPerc": 9.0, "totalSaving": 588.0, "basePrice": 5500.0, "hotelTax": 627.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 366.0, "mmtDiscount": 275.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "couponInfo": {"couponCode": "BESTBUYMMT1", "type": "ECOUPON", "description": "Get inr 313.00 OFF", "discountAmount": 313.0, "doubleDiscountEnabled": false, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 313.0}, "tncUrl": "https://www.makemytrip.com/promos/dh-hdfc-18032019.html", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}, "pricingKey": "s9rHC9RN7n9mhWlttJzSj2tV1ISfQxo//A5kVEeL7CqA9nWnbmS2lNwkkAu5f2x1bCfBzTUAhEAz5uz/qCKWnjEvj6QRfiGsF6CajGPqEYWLw1hOo1sJEaGoqaFeZ9S+9RuMbiSrSqByYTMocTus98VhpxCBNlbiwfBINXsypIGMCAIicovbQQrs34q205aSESU8xF/6oYprqej/8dbMaCchRelPOtXNocBZ+dhT8w8aYaoY83RsybBHBCMJW8fCOmI+hsSI4yQ=", "pricingDivisor": 1, "totalTax": 993.0, "effectivePrice": 5905.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "blackFactored": false, "taxIncluded": true}, "commission": 1149.5, "conversionFactor": 1.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": true, "originalBNPL": true, "dateOfDelayedPayment": 1597653000000, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "suppressPas": false, "doubleBlackValidated": false, "recommendCouponReq": "{\"numberOfCoupons\":3,\"transactionKey\":\"fec89d03-6f73-42e3-9bbb-3e572408b3ee\",\"travellerCount\":2,\"lob\":\"HTL\",\"domain\":\"B2C\",\"isLoggedIn\":false,\"bookingDevice\":\"android\",\"checkInDate\":\"19-08-2020\",\"checkOutDate\":\"20-08-2020\",\"countryCode\":\"IN\",\"cityCode\":\"CTGOI\",\"cmpId\":\"312379\",\"adultCount\":2,\"infantCount\":0,\"childCount\":0,\"expiryRequired\":false,\"pageContext\":\"REVIEW\",\"deviceId\":\"6410d9524bcdd210\",\"numRooms\":1,\"appVersion\":\"8.1.1.RC3\",\"hotelDetails\":[{\"hotelId\":\"201402021911303910\",\"bucketId\":\"<PERSON><PERSON>_<PERSON><PERSON>,<PERSON>MB<PERSON>,ARR7,DH_MMT,META_MMT,MB_ASSURED,INC_HPA,ASP,HPA3,TA3,<PERSON>G3,GOSTAY_DEC,B2C_DND,MOB_DND,HTL_DEC2,ECOM25,DH_ECOM,DHSF,GHCP\",\"cmpId\":\"312379\",\"starRating\":5,\"transactionAmountPreTax\":5225.0,\"transactionAmount\":6218.0,\"paymentModel\":[\"PAS\",\"Default\"],\"adultCount\":0,\"childCount\":0,\"numRooms\":0,\"refundPolicy\":[\"Default\",\"Default\"],\"rateType\":\"B2C\",\"supplier\":\"INGO\",\"addOnDiscountMeta\":{\"discount\":0.0,\"discountType\":\"INSTANT\"},\"wltApplBonusAmt\":0.0,\"hotelPriceDetails\":{\"commission\":1149.5,\"markup\":365.75,\"mmtDiscount\":0.0,\"hotelTax\":627.0},\"roomType\":\"2312\",\"mealPlan\":\"EP\",\"prc_sell_amt_wo_tax\":5852.0,\"prc_hotelier_promotion\":275.0}],\"currency\":{\"type\":\"inr\",\"conversionRate\":1.0},\"sessionId\":\"6410d9524bcdd210~1\",\"htlSearch\":false,\"isFirstTimeUser\":false}", "recommendedAddOns": {"CHARITY_ID": {"addOnType": "ADDON", "id": "CHARITY_ID", "type": "STATIC", "category": "ADDON_CHARITY_DOB", "value": 5, "price": 5, "alternateCurrencyPrice": 0.0, "lob": "DONATION", "title": "Donate Rs. 5 for COVID -19 Relief and Other Charity Initiatives", "validFrom": "2020-08-10", "expiry": "2020-08-12", "description": "Create an impact by donating to MakeMyTrip", "tnc": {"tncList": ["The amount received as donation will be used for the specified charitable causes. MakeMyTrip will donate the collected amount to MakeMyTrip Foundation (a public trust registered with charitable objects) or similar charitable organizations to help create a social impact "]}, "doubleRedemptionAllowed": true, "availableUnits": 1, "paymentMode": ["PAS"], "bucketId": "ADDON_CHARITY_DOB", "shortDesc": "ADDON_CHARITY_DOB", "basePrice": 1000, "imageMap": {"bigIcon": "https://promos.makemytrip.com/images/deal.png"}, "priceMap": {"adult": {"displayPrice": 5.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5.0, "savingPerc": 0.0, "totalSaving": 0.0}}, "mobiusAddOn": false, "descriptions": [{"titleText": "Support COVID-19 relief work and safety initiatives.", "iconUrl": "https://promos.makemytrip.com/COVID/charity.png", "itemIconType": "charity_covid", "charityDescription": "Support COVID-19 relief work and safety initiatives. <a href=\"https://www.makemytrip.com/csr/covid-19-relief-efforts.html\">Know More</a>"}, {"titleText": " Offset your carbon footprint by contributing to our green initiative.", "iconUrl": "https://imgak.mmtcdn.com/flights/assets/media/dt/review/charity_1.png?v=1", "itemIconType": "sprout_charity"}, {"titleText": "Ensure responsible tourism to restore, develop and protect heritage sites and monuments.", "iconUrl": "https://imgak.mmtcdn.com/flights/assets/media/dt/review/charity_2.png?v=1", "itemIconType": ""}], "tncUrl": "https://promos.makemytrip.com/charity-deduction-16112017.html", "hasSlot": false}}, "surgePercent": 0.0, "ddExperimentId": "35", "ddTxnData": {"ddExpName": null, "ddExpId": "35", "txnId": null, "version": null, "surgePercent": 0.0, "policyId": null, "applyOn": null, "policy": null, "updatedOn": null, "userWalletAmount": 0.0}, "ddTxnDataMap": {"DYNAMIC_DISCOUNTING": [{"ddExpName": null, "ddExpId": "35", "txnId": null, "version": null, "surgePercent": 0.0, "policyId": null, "applyOn": null, "policy": null, "updatedOn": null, "userWalletAmount": 0.0}]}, "walletAmountPaid": 0.0, "walletRuleRequest": "{\"outputType\":\"WALLET_SURGE\",\"lob\":[\"HTL\"],\"attributes\":{\"APP\":{\"platform\":\"android\",\"appVersion\":\"8.1.1.RC3\"},\"HTL\":{\"pax\":2,\"rooms\":1,\"night\":1,\"countryCode\":\"IN\",\"adultCount\":2,\"childCount\":0,\"checkOutDate\":1597861800000,\"checkInDate\":1597775400000,\"details\":[{\"id\":\"201402021911303910\",\"bucketId\":[\"Hotel5STWB21\"],\"cmpId\":\"312379\",\"starRating\":5,\"transactionAmountPreTax\":5225.0,\"transactionAmount\":5590.75,\"paymentModel\":[\"PAS\",\"Default\"],\"refundPolicy\":[\"Default\"]}],\"ap\":9}},\"commonAttributes\":{\"deviceId\":\"6410d9524bcdd210\",\"visitorId\":\"00555421455074779786161380701755230313\"}}", "sessionId": "6410d9524bcdd210~1", "cpSessionId": "", "blackEligible": false, "blackEligibilityDays": 0, "blackRegSuccess": false, "regForBlack": false, "altAccoBooking": false, "showOriginalPolicyToDbUsr": false, "panCardRequired": false, "alternateCurrencyConversionFactor": 0.0, "alternateCurrencySelected": false, "hydraSegments": [], "blackUser": false, "netRateSelected": false, "wizardUser": false, "pnAvlbl": false, "emiBucket": "", "expData": {"HPI": "true", "OCCFCNR": "t", "REV": "4", "CHPC": "t", "AARI": "t", "RCPN": "t", "HLMV": "t", "HBH": "f", "MRS": "t", "SRR": "t", "ADDON": "t", "FBP": "f", "MC": "t", "BNPL": "t", "PLRS": "t", "PDO": "PN", "DPCR": "0", "FLTRPRCBKT": "t", "EMI": "f", "SPCR": "2", "BRIN": "110", "ADDV": "f", "HRSRFHS": "true", "HIS": "DEFAULT", "WSP": "t", "APE": "35", "PAH": "5", "HSCFS": "4", "LVD": "", "PAH5": "f", "LCS": "t", "LVI": "", "isWBTH": "false", "newReviewPageBottomToolBarEnabled": "false", "hotelDetailCardOrder": "rc0,hfc1,qb2,man3,ws4,brn5,blk6,gr7,rr8,ef9,mbg10,ex11,ps12,aac13,alpd14,pd15,au16,db17,bpg18,am19,lcn20,aga21,ta22,ty23,cc24,fb25", "isPayLaterAnimationEnabled": "false", "allowPayLater": "false", "FilterVisibilityDesktop": "true", "bookingReviewV2": "true", "htlPolarisCard": "true", "multicurrencyEnabled ": "false", "enableGuidedSearch": "false", "corpApprovalUI": "true", "AATEST": "0", "pwaCheckoutBtnSticky": "false", "gstOnTripTags": "true", "htlAltAccoCollection": "true", "checkoutReviewDaysCount": "3", "desktopImgSizeFactorDetail": "1.0", "enablePolarisIntl": "false", "htlEnableQuickDatesAltAcco": "false", "newListingHotelCorporate": "false", "pwaImgSizeFactorDetail": "1.0", "hotel_new_ugc_card": "false", "recommendationOnSoldout": "true", "IAB_Details": "IAB_F", "desktopImgSizeFactorListing": "1.0", "filterBasePricing": "false", "corplEnableSavedLocations": "true", "newListingAltaccoDom": "true", "travellerImageBucketsSectionExp": "false", "newListingCorporate": "true", "pwaShowNewReviewPg": "false", "enableNewAboutProperty": "false", "forkFcnr": "true", "specialInvalidCorporate": "false", "test_config_str": "test", "htl_locus_enabled_altAcco": "false", "LocusDTFunnel": "true", "hotelNewMapViewShowRegion": "true", "enableCorpMultiroom": "false", "locusLocationFilterEnabled": "true", "enableNewPaymentScreen": "true", "shouldLaunchGamification": "false", "htlShowAcmeFrgament": "true", "review_intent_show": "true", "whichAreaGuide": "LOCATION_FILTER", "htlQuickBookRatingBar": "1", "showVideoOnDetail": "true", "htlNewHeaderFilterUi": "true", "showAltAccoFilterintl": "false", "htlReviewNewUI": "true", "apiExperiment": "35", "DynamicDiscount": "10", "payLaterTimeLine": "true", "homePageReviewCollectionPopUp": "true", "newAboutPropertyCard": "false", "imgSizeFactorExp": "false", "hotelNewMapView": "true", "specialRequestCorporate": "true", "htl_show_pan_card_optional": "1", "BNVsSR": "100", "altAccoNewLanding": "false", "htlGCCThankYouV2": "false", "NewHeader": "false", "LocusDTFunnelHotels": "true", "bookingReviewFooterV2": "true", "enableMultiCurrency": "false", "htlShowNearByHotels": "true", "newListingHotelDom": "true", "htl_brin_new_ui": "false", "mobGenConfigVariant": "A", "AAEXP": "1", "reviewDetailCardOrderB2C": "pd,dbc,pr,bpb,cd,af,td,sr,tc,pb", "enablePolarisFlow": "true", "showMapCorporate": "false", "forcedLogin": "DEFAULT_FLOW", "customerReviewNewDesign": "false", "showNewBrinAnimation": "false", "listingHeaderWithMapIconExp": "false", "htl_new_thank_you_corp": "false", "showEnhancedSeekReviews ": "true", "shouldshowQBRReviewTags": "false", "htlPricingExperiment": "PN", "htlDetailNewRatingCard": "true", "htl_new_thank_you_gcc": "false", "htlB2BThankYouV2": "false", "pwaImgSizeFactorListing": "1.0", "imageDeviceResolution": "true", "mapExploration": "false", "videoPositionOnListing": "0", "mapSearchBar": "true", "htlDarkFilterUiWithMapIcon": "true", "corpHotelDetailCardOrder": "rc0,hfc1,qb2,man3,ws4,brn5,blk6,gr7,rr8,ef9,mbg10,ex11,ps12,aac13,alpd14,pd15,au16,db17,bpg18,am19,lcn20,aga21,ta22,ty23,cc24,fb25", "newListingHotelIntl": "true", "corpPayLaterTimeLine": "true", "rmStarRatingAltAcco": "true", "htlLocusEnabledAltAcco": "true", "EMIDT_NEW": "1", "locusLocationFilterDisabledUI": "false", "personalEnableSavedLocations": "true", "htlLocusEnabledCorp": "false", "showVideos": "false", "newReviewPageEnabled": "false", "htl_Cross_Sell_Comparator": "2", "AndroidImageE2Hotels": "true", "htlAltAccoWidgetType": "1", "isPayLaterEnabled": "false", "payLaterListingAnimation": "true", "altAccoLandingTravelTypeView": "1", "corporateMultiroom": "false", "htlB2CThankYouV2": "true", "hotelDetailCardorder_ios": "RC1,<PERSON><PERSON>2,WPM3,<PERSON><PERSON>4,WOO5,QB6,RD7,<PERSON>G8,AAH9,SA10,EF11,WBH12,HRP13,TA14,HO15,AM16,ATH17,DB18,HMD19,<PERSON>L20,AAM21,HPC22", "htlPolarisCardIntl": "false", "hotelWidgetType": "3", "APEINTL": "36", "isMoCharter": "true", "newCorpApprovalPage": "true", "3.\thtlNewHeaderFilterUi": "false", "shouldHideAltAccoSearchDateSelection": "true", "PAYNEW": "true", "enableLocusLocationFilter": "false", "imageParamChanges": "true", "selectroomvariant": "3", "newHeaderFilterCorp": "true", "htldetailCompareWidget": "1", "consumeManualFlyFishDom": "true", "payAtHotelB2B": "true", "allowNRRatesCorporate": "true", "shouldEnableNearByHotelList": "false", "listingMap": "true", "hotelImageCount": "100", "imagePrefetchEnabled": "true", "myBizHotelPricing": "false", "consumeManualFlyFishIntl": "true", "galleryDesign": "true", "htlNewHeaderFilterUiWithMapIcon": "true", "corpShowSimilarHtls": "false", "hotelSequenceCorporate": "sign-hsq130", "htlCorpWidgetType": "1", "newListingAltaccoIntl": "true", "myBizHotelPricingiOS": "false", "corpFCtimeline": "true", "listingHeaderFooterFilterExp": "false", "htlBrinV2": "110", "FilterFlow": "OLD", "htl_locus_enabled_corp": "false", "policyvariant": "false", "checkoutReviewSessionCount": "3", "imageDeviceResolutionFactor": "1.0", "hotelMobConfigVariant": "A", "NewHotelMaps": "false", "subConceptsNewDesign": "false", "autoPlayVideo": "false", "showNewListing": "true", "showPancard": "false", "Pay Later Desktop": "false", "htlDummyPanCardExperiment": "1", "htl_new_thank_you_b2c": "false", "isPwaForcedLogin": "true", "showNewAltAccoFilterIntl": "false", "myBizHotelPricingAndroid": "false"}, "corporateCdfSkip": false, "paxCount": 2, "bnplPersuasionMsg": "Free Cancellation, Zero Payment Now", "bnplPolicyText": "Use credit card to book this hotel now. Full amount will be charged on 17 Aug.", "cancellationTimeline": {"checkInDate": "19 Aug", "cancellationDate": "19 Aug", "cardChargeDate": "17 Aug", "cardChargeDateLong": "17 Aug, 2020", "cardChargeText": "Credit Card is charged", "subTitle": "Free Cancellation, Zero Payment Now", "freeCancellationText": "Free Cancellation till 19 Aug 02:00 PM", "title": "STAY FLEXIBLE WITH", "bookingDate": "10 Aug"}, "packageRequest": false, "cdfddapplied": false}, "contactUsInfo": {"emailId": null, "phoneNo": ["0124 4628747", "0124 4628748"]}}