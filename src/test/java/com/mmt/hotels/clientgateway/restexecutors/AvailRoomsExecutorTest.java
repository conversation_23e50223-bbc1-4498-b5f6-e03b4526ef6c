package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.PayLaterEligibilityResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;
import java.util.Map;

import org.json.simple.JSONObject;
import org.json.simple.parser.*;

@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsExecutorTest {


    @InjectMocks
    AvailRoomsExecutor availRoomsExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private HeadersUtil headersUtil;

    @Spy
    private MetricAspect metricAspect;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(availRoomsExecutor, "availRoomsUrl", "abc");
    }

    @Test
    public void availRoomsTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respAvail").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());
        }
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<String, String>());

        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsOld() throws ClientGatewayException {
        String resp = "{\"total\":0}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = availRoomsExecutor.availRoomsOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }
    
    @Test
    public void testupdatedPriceOccuLessOld() throws ClientGatewayException {
        String resp = "availResponse";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = availRoomsExecutor.updatedPriceOccuLessOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }
    
    @Test
    public void getTotalPricingDetailsTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respAvail").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());
        }
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "123", new HashMap<String, String>());

        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibility() throws ClientGatewayException {
        String resp = "{\"amount\":1000, \"eligible\":true}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "mmt-auth123");
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), new HashMap<>(),"", headers);
        Assert.assertNotNull(response);
    }
}
