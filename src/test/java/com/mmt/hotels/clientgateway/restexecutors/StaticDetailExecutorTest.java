package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;
import java.util.UUID;

@RunWith(MockitoJUnitRunner.class)
public class StaticDetailExecutorTest {

    @InjectMocks
    StaticDetailExecutor staticDetailExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Mock
    private MetricAspect metricAspect;

    @Spy
    private HeadersUtil headersUtil;

    @Mock
    private PricingEngineHelper pricingEngineHelper;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(staticDetailExecutor, "staticDetailUrl", "abc");
        ReflectionTestUtils.setField(staticDetailExecutor, "hotelsFromHotStoreUrl", "v2.0/hotels/hotstore?correlationKey=%s");
        ReflectionTestUtils.setField(staticDetailExecutor, "hotelsReviewSummaryUrl", "v2.0/hotels/review-summary?correlationKey=%s");
    }

    @Test
    public void staticDetailTest() throws ClientGatewayException {


        String resp= null;

        try {


            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respStatic").toString();

        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resp);
        HotelDetailWrapperResponse response = staticDetailExecutor.getStaticDetail(new HotelDetailsMobRequestBody(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);

    }

    @Test
    public void testGetStaticDetailResponse() throws ClientGatewayException {
    	HotelDetailsMobRequestBody hotelDetailsMobRequestBody = new HotelDetailsMobRequestBody();
        Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("");
		String resp = staticDetailExecutor.getStaticDetailsResponse(hotelDetailsMobRequestBody, new HashMap<>(), new HashMap<>());
		Assert.assertNotNull(resp);
    }

    @Test
    public void testGetCityGuildeResponse() throws ClientGatewayException {
        CityGuideRequest cityGuideRequest = new CityGuideRequest();
        Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
        CityGuideResponse resp = staticDetailExecutor.getCityGuildeResponse(cityGuideRequest, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(resp);
    }

    @Test
    public void testGetWishListedHotelsFromHotStore() throws ClientGatewayException {
        HotStoreHotelsRequestBody hotStoreHotelsRequestBody = new HotStoreHotelsRequestBody();
        hotStoreHotelsRequestBody.setCorrelationKey(UUID.randomUUID().toString());
        Mockito.when(restConnectorUtil.performHotelsHotStorePost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
        HotStoreHotelsWrapperResponse wishListedHotelsFromHotStore = staticDetailExecutor.getWishListedHotelsFromHotStore(hotStoreHotelsRequestBody, new HashMap<>());
        Assert.assertNotNull(wishListedHotelsFromHotStore);
    }

    @Test
    public void testGetFlyFishReviewSummary() throws ClientGatewayException {
        FlyfishReviewRequestBody flyfishReviewRequestBody = new FlyfishReviewRequestBody();
        flyfishReviewRequestBody.setCorrelationKey(UUID.randomUUID().toString());
        Mockito.when(restConnectorUtil.performHotelsReviewSummaryPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
        FlyfishReviewWrapperResponse flyFishReviewSummary = staticDetailExecutor.getFlyFishReviewSummary(flyfishReviewRequestBody, new HashMap<>());
        Assert.assertNotNull(flyFishReviewSummary);
    }

}
