package com.mmt.hotels.clientgateway.restexecutors;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.PoliciesRequest;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.response.policy.HotelPolicyResponseEntityBO;

@RunWith(MockitoJUnitRunner.class)
public class PolicyExecutorTest {

	@InjectMocks
	private PolicyExecutor policyExecutor;
	
	@Mock
	private RestConnectorUtil restConnectorUtil;
	
    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(policyExecutor, "getPoliciesURL", "abc");
    }

    
    @Test
    public void testGetPolicies() throws ClientGatewayException, JsonProcessingException{
    	Mockito.when(restConnectorUtil.performPoliciesGet(Mockito.anyMap(), Mockito.anyString()))
    	.thenReturn("{}");
    	HotelPolicyResponseEntityBO respBO = policyExecutor.getPolicies(new PoliciesRequest());
    	Assert.assertNotNull(respBO);
    }
    
    @Test(expected=ClientGatewayException.class)
    public void testGetPoliciesException() throws ClientGatewayException, JsonProcessingException{
    	Mockito.when(restConnectorUtil.performPoliciesGet(Mockito.anyMap(), Mockito.anyString()))
    	.thenReturn("{\"responseErrors\":{\"errorList\":[{\"errorCode\":\"3000\",\"errorMessage\":\"11\",\"errorAdditionalInfo\":null}]}}");
    	policyExecutor.getPolicies(new PoliciesRequest());
    }
}
