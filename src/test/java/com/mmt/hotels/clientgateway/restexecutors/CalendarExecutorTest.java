package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.CalendarAvailabilityRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.CalendarAvailabilityResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class CalendarExecutorTest {

    @InjectMocks
    CalendarExecutor calendarExecutor;

    @Mock
    RestConnectorUtil restConnectorUtil;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    MetricAspect metricAspect;

    @Before
    public void init() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
    }

    @Test
    public void getCalendarAvailabilityTest_Success() throws ClientGatewayException {
        PriceByHotelsRequestBody calendarAvailabilityRequest = new PriceByHotelsRequestBody();
        String respStr = "{\"responseErrors\":{\"errorList\":null},\"correlationKey\":\"82b8e4b3-4b2f-41d5-85e3-448418913813\"}";
        Mockito.when(restConnectorUtil.performCalendarAvailabilityPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(respStr);
        CalendarAvailabilityResponse resp = calendarExecutor.getCalendarAvailability(calendarAvailabilityRequest, "", new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(resp);
    }

    @Test(expected = Exception.class)
    public void getCalendarAvailabilityTest_Failure() throws ClientGatewayException {
        PriceByHotelsRequestBody calendarAvailabilityRequest = new PriceByHotelsRequestBody();
        String respStr = "{\"correlationKey\":\"82b8e4b3-4b2f-41d5-85e3-448418913813\",\"responseErrors\":{\"errorList\":[{\"errorCode\":\"401824\",\"errorAdditionalInfo\":null,\"errorMessage\":\"alternated dates not retuned from AP\"}]}}";
        Mockito.when(restConnectorUtil.performCalendarAvailabilityPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(respStr);
        CalendarAvailabilityResponse resp = calendarExecutor.getCalendarAvailability(calendarAvailabilityRequest, "", new HashMap<>(), new HashMap<>());
    }
}