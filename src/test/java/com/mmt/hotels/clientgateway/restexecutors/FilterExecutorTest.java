package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class FilterExecutorTest {

    @InjectMocks
    FilterExecutor filterExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;
    
    @Spy
    private HeadersUtil headersUtil;

    @Mock
    PricingEngineHelper pricingEngineHelper;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(filterExecutor, "filterCountUrl", "abc");
    }

    @Test
    public void filterCountTest() throws ClientGatewayException {

        String resp= null;

        try {
        	
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respFilter").toString();

        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }


        Mockito.when(restConnectorUtil.performFilterCountPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
         HashMap<String, String> header =  new HashMap<String, String>();

        FilterSearchMetaDataResponse response = filterExecutor.filterCount(new SearchWrapperInputRequest(), header, FilterSearchMetaDataResponse.class);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getFilterDataMap().containsKey(FilterGroup.BLACKDEALS));
        Assert.assertTrue(response.getFilterDataMap().containsKey(FilterGroup.FREE_BREAKFAST));
        Assert.assertEquals("BLACKDEALS_AVAIL",response.getFilterDataMap().get(FilterGroup.BLACKDEALS).get(0).getFilterValue());
        Assert.assertEquals("BREAKFAST",response.getFilterDataMap().get(FilterGroup.FREE_BREAKFAST).get(0).getFilterValue());

        header.put("mmt-auth","abcd");
        String str = filterExecutor.filterCount(new SearchWrapperInputRequest(), header, String.class);
        Assert.assertNotNull(str);

    }
}
