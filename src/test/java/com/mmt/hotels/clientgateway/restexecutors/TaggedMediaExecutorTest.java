package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
@RunWith(MockitoJUnitRunner.class)
public class TaggedMediaExecutorTest {

    @InjectMocks
    private TaggedMediaExecutor taggedMediaExecutor;
    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Test
    public void executeGetTaggedMediaRequestTest() throws Exception {
        Mockito.when(restConnectorUtil.performGetTaggedMedia(Mockito.any(), Mockito.anyString())).thenReturn("{}");
        Assert.assertNotNull(taggedMediaExecutor.executeGetTaggedMediaRequest(new HashMap<>(),"destinationURL"));
    }

    @Test
    public void executeGetMediaByTagIdRequestTest() throws Exception {
        Mockito.when(restConnectorUtil.performGetMediaByTagId(Mockito.any(), Mockito.any())).thenReturn("{}");
        Assert.assertNotNull(taggedMediaExecutor.executeGetMediaByTagIdRequest(new HashMap<>(),"destinationURL"));
    }

}
