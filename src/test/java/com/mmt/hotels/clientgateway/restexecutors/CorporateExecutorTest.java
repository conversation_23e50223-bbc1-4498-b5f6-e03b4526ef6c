package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.DoubleBlackResponse;
import com.mmt.hotels.clientgateway.response.corporate.WorkflowInfoResponse;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.corporate.GetApprovalRequest;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.corporate.UpdateApprovalRequest;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateRequest;

import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import com.mmt.hotels.model.response.corporate.GetApprovalsResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class CorporateExecutorTest {


    @InjectMocks
    CorporateExecutor corporateExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private HeadersUtil headersUtil;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(corporateExecutor, "corporateWorkflowUrl", "abc");
        ReflectionTestUtils.setField(corporateExecutor, "corporateUpdateApprovalUrl", "abc");
        ReflectionTestUtils.setField(corporateExecutor, "corporateGetWorkflowInfoUrl", "abc");
        ReflectionTestUtils.setField(corporateExecutor, "corporateGetApprovalsInfoUrl", "abc");
        ReflectionTestUtils.setField(corporateExecutor, "corporateUpdatePolicyUrl", "url");
        ReflectionTestUtils.setField(corporateExecutor, "corporateUpdateApprovalsUrl", "abc");
    }

    @Test
    public void getWorkflowInfoByAuthcodeTest() throws Exception {
        String resp = "{\"errorEntity\":{\"msg\":\"There is a problem in processing your request\",\"errorCode\":\"3409\"}}";
        Mockito.when(restConnectorUtil.performCorporateWorkflowGet(Mockito.any(), Mockito.any())).thenReturn(resp);
//        Mockito.when(objectMapperUtil.getObjectFromJson(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new WorkflowInfoResponse());

        WorkflowInfoResponse response = corporateExecutor.getWorkflowInfoByAuthCode("abcd1234", new HashMap<>(), "abc");
        Assert.assertNotNull(response);
    }

    @Test
    public void getUpdateApprovalResponseTets() throws Exception {
        String resp = "{\"errorEntity\":{\"msg\":\"There is a problem in processing your request\",\"errorCode\":\"3409\"}}";

        Mockito.when(restConnectorUtil.performCorporateApprovalPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(String.valueOf(resp));
//        Mockito.when(objectMapperUtil.getObjectFromJson(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new CGServerResponse());


        CGServerResponse response = corporateExecutor.getUpdateApprovalResponse(new UpdateApprovalRequest(), new HashMap<>(), "id", new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void workflowInfoByAuthcodeTest() throws Exception {
        String resp = "{\"errorEntity\":{\"msg\":\"There is a problem in processing your request\",\"errorCode\":\"3409\"}}";
        Mockito.when(restConnectorUtil.performWorkflowInfoByAuthcodePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
//        Mockito.when(objectMapperUtil.getObjectFromJson(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new CGServerResponse());

        CGServerResponse response = corporateExecutor.workflowInfoByAuthcode(new GetApprovalRequest(), new HashMap<>(), "123", "id", new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void getApprovalsInfoTest() throws Exception {
        String resp = "{\"approvalDetails\":null,\"priceByHotelsRequestBody\":null,\"roomDetailsResponse\":null}";
        Mockito.when(restConnectorUtil.performApprovalsInfoPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        GetApprovalsResponse response = corporateExecutor.getApprovalsInfo(new GetApprovalRequest(), new HashMap<>(), "123", new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testRequestApproval() throws Exception{
        String resp = "{\"errorEntity\":{\"msg\":\"There is a problem in processing your request\",\"errorCode\":\"3409\"}}";
    	Mockito.when(restConnectorUtil.performCorporateInitApprovalPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
    	CGServerResponse response = corporateExecutor.requestApproval(new InitApprovalRequest(), new HashMap<>(), "abc", new HashMap<String, String>());
    	Assert.assertNotNull(response);
    }
    
    @Test
    public void testUpdateCorpPolicy() throws Exception{
    	Mockito.when(restConnectorUtil.performUpdatePolicyPost(Mockito.any(),Mockito.anyMap(), Mockito.any())).thenReturn("");
    	String resp = corporateExecutor.updateCorpPolicy(new CorpPolicyUpdateRequest(), new HashMap<>(), new HashMap<String, String>(), "");
    	Assert.assertNotNull(resp);
    }

    @Test
    public void testUpdatePolicy() throws Exception{
        String respString = "{\"corpMetaInfo\":{\"approvalRequired\":true,\"corporateTariff\":false,\"validationPayload\":{\"approvalRequired\":true,\"blockOopBooking\":0,\"blockSkipApproval\":1,\"failureReasons\":[\"The maximum budget per night is INR 3000.00 per person according to your travel policy.\",\"Hotels can be booked at most 3 days in advance of the travel date according to your travel policy.\"],\"paymentMethodsAllowed\":[\"WALLET\"],\"preferred\":false,\"visible\":true,\"withinPolicy\":false}}}";
        Mockito.when(restConnectorUtil.performUpdatePolicyPost(Mockito.any(),Mockito.anyMap(), Mockito.any())).thenReturn(respString);
        CorpPolicyUpdateResponse resp = corporateExecutor.updatePolicy(new UpdatePolicyRequest(), new HashMap<>(), new HashMap<String, String>(), "","");
        Assert.assertNotNull(resp);
    }

    @Test
    public void testUpdateApproval() throws Exception {
        String resp = "{\"corpMetaInfo\":{\"approvalRequired\":true,\"corporateTariff\":false,\"validationPayload\":{\"approvalRequired\":true,\"blockOopBooking\":0,\"blockSkipApproval\":1,\"failureReasons\":[\"The maximum budget per night is INR 3000.00 per person according to your travel policy.\",\"Hotels can be booked at most 3 days in advance of the travel date according to your travel policy.\"],\"paymentMethodsAllowed\":[\"WALLET\"],\"preferred\":false,\"visible\":true,\"withinPolicy\":false}}}";
        Mockito.when(restConnectorUtil.performCorporateInitApprovalPost(Mockito.any(), Mockito.anyMap(), Mockito.any())).thenReturn(resp);
        Assert.assertNotNull(corporateExecutor.updateApproval(new UpdateApprovalRequest(), "workflow_123", new HashMap<>(), "", new HashMap<>()));
    }

    @Test
    public void testUpdateApprovals() throws Exception {
        String resp = "{\"corpMetaInfo\":{\"approvalRequired\":true,\"corporateTariff\":false,\"validationPayload\":{\"approvalRequired\":true,\"blockOopBooking\":0,\"blockSkipApproval\":1,\"failureReasons\":[\"The maximum budget per night is INR 3000.00 per person according to your travel policy.\",\"Hotels can be booked at most 3 days in advance of the travel date according to your travel policy.\"],\"paymentMethodsAllowed\":[\"WALLET\"],\"preferred\":false,\"visible\":true,\"withinPolicy\":false}}}";
        Mockito.when(restConnectorUtil.performCorporateInitApprovalPost(Mockito.any(),Mockito.anyMap(),Mockito.anyString())).thenReturn(resp);
        Assert.assertNotNull(corporateExecutor.updateApprovals(new UpdateApprovalRequest(), new HashMap<>(), "123", new HashMap<>()));
    }
}
