package com.mmt.hotels.clientgateway.restexecutors;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class HESRestExecutorTest {

	@InjectMocks
	private HESRestExecutor hesRestExecutor;
	
	@Mock
	private RestConnectorUtil restConnectorUtil;
	
    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(hesRestExecutor, "txnDataGetUrl", "abc");
    }
    
    @Test
    public void testGetPolicies() throws ClientGatewayException, JsonProcessingException {
    	Mockito.when(restConnectorUtil.performThankYouGet(Mockito.anyMap(), Mockito.anyString()))
    	.thenReturn("{}");
    	PersistanceMultiRoomResponseEntity respBO = hesRestExecutor.getTxnData("", new HashMap<>(), "");
    	Assert.assertNotNull(respBO);
    }
    
    @Test(expected=ClientGatewayException.class)
    public void testGetPoliciesException() throws ClientGatewayException, JsonProcessingException {
    	Mockito.when(restConnectorUtil.performThankYouGet(Mockito.anyMap(), Mockito.anyString()))
    	.thenReturn("{\"responseErrors\":{\"errorList\":[{\"errorCode\":\"3000\",\"errorMessage\":\"11\",\"errorAdditionalInfo\":null}]}}");
    	hesRestExecutor.getTxnData("", new HashMap<>(), "");
    }
}
