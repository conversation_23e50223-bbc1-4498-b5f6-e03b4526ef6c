package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class EmiDetailExecutorTest {

    @InjectMocks
    EmiDetailExecutor emiExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

    @Before
    public void init() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(emiExecutor, "getUpdatedEmiUrl", "abc");
    }

    @Test
    public void getUpdatedEmiDetailsTest() throws Exception {
        Mockito.when(restConnectorUtil.performEmiDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("rsp");
        String response = emiExecutor.getUpdatedEmiDetails(new UpdateEmiDetailRequest(), new HashMap<>(), new HashMap<String, String>(), "corKey");
        Assert.assertNotNull(response);
    }

    @Test
    public void getUpdatedEmiTest() throws Exception {
        String resp = null;
        String respStatic = null;

        try {


            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp = jo.get("updatedEMI").toString();

        } catch (Exception e) {
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.when(restConnectorUtil.performEmiDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resp);
        RoomDetailsResponse response = emiExecutor.getUpdatedEmi(new UpdateEmiDetailRequest(), new HashMap<>(), new HashMap<String, String>(), "corKey");
        Assert.assertNotNull(response);
    }


}
