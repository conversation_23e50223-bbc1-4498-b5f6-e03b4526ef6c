package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.enums.ConnectivityErrors;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.model.request.upsell.UpsellHotelRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsExecutorTest {

    @InjectMocks
    SearchRoomsExecutor searchRoomsExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private HeadersUtil headersUtil;

    @Spy
    private MetricAspect metricAspect;

    @Mock
    private PricingEngineHelper pricingEngineHelper;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

    private static ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();

    static {
        pool.setCorePoolSize(10);
        pool.setMaxPoolSize(10);
        pool.setQueueCapacity(10);
        pool.setThreadNamePrefix("filterConfigPoolExecutor");
        pool.initialize();
    }


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(searchRoomsExecutor, "searchRoomsUrl", "abc");
        ReflectionTestUtils.setField(searchRoomsExecutor,"hotelImageUrl","abc");
        ReflectionTestUtils.setField(searchRoomsExecutor,"staticRoomsUrl","abc");
        ReflectionTestUtils.setField(searchRoomsExecutor, "detailServiceThreadPool", pool);

    }

    @Test
    public void searchRoomsTest() throws ClientGatewayException, ExecutionException, InterruptedException {

        String resp= null;
        String respStatic= null;

        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respSearchRooms").toString();
            respStatic =  jo.get("respSearchRooms").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.when(restConnectorUtil.performSearchRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Mockito.when(restConnectorUtil.performStaticRoomDetailGet(Mockito.any(),Mockito.any())).thenReturn(respStatic);
        Future<RoomDetailsResponse> response = searchRoomsExecutor.getRoomPrices(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<String, String>(), new CountDownLatch(1));
        Future<HotelsRoomInfoResponseEntity> responseStatic = searchRoomsExecutor.getRoomStaticDetails("1234", null, null, 1,"123", new HashMap<>(), new HashMap<String, String>(), new CountDownLatch(1), "DETAIL",null, null);

        Assert.assertNotNull(response.get());
        Assert.assertNotNull(responseStatic.get());
    }

    @Test
    public void searchRoomsTestReturnsNull() throws ClientGatewayException, ExecutionException, InterruptedException {

        String resp= null;

        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respSearchRooms").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.lenient().when(restConnectorUtil.performSearchRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Mockito.lenient().when(restConnectorUtil.performStaticRoomDetailGet(Mockito.any(),Mockito.any())).thenThrow(new NullPointerException());
        Future<HotelsRoomInfoResponseEntity> responseStatic = searchRoomsExecutor.getRoomStaticDetails("1234", null, null, 0,"123", new HashMap<>(), new HashMap<String, String>(), new CountDownLatch(1), "DETAIL",null, null);
        Assert.assertNotNull(responseStatic);
    }

    @Test
    public void testGetRoomPricesOld() throws ClientGatewayException {
        String resp = "{\"total\":0}";
        Mockito.when(restConnectorUtil.performSearchRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = searchRoomsExecutor.getRoomPricesOld(new PriceByHotelsRequestBody(),new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testGetComparatorOld() throws ClientGatewayException {

        Mockito.when(restConnectorUtil.performSearchRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn("{}");
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth","test");
        Assert.assertNotNull(searchRoomsExecutor.getComparatorOld(new UpsellHotelRequest(), new HashMap<>(), headers, UpsellHotelDetailResponse.class));
        Assert.assertNotNull(searchRoomsExecutor.getComparatorOld(new UpsellHotelRequest(), new HashMap<>(), new HashMap<String, String>(), String.class));

    }
    
    @Test
    public void testAlternateDatesPriceOld() throws ClientGatewayException {
        String resp = "test";
        Mockito.when(restConnectorUtil.performAlternateDatesPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = searchRoomsExecutor.alternateDatesPriceOld(new PriceByHotelsRequestBody(),new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);

    }

    @Test
    public void testGetHotelImages() throws RestConnectorException, JsonParseException {
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        searchRoomsRequest.getSearchCriteria().setHotelId("123");
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.getDeviceDetails().setNetworkType("wifi");
        searchRoomsRequest.setExpData("{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,EMIDT:3}");
        Mockito.lenient().when(restConnectorUtil.performStaticRoomDetailGet(Mockito.anyMap(), Mockito.anyString())).thenReturn("{\"images\":[{\"hotelId\":\"317511410297898\",\"imageDetails\":{\"traveller\":{},\"professional\":{\"R\":[{\"title\":\"Grand Luxury Ste Burj View King Bed 155 Sq Mt Comp Basic Wifi Club Lounge Access\",\"url\":\"//r1imghtlak.mmtcdn.com/be163b9e49f511e8a9c10a82ade5b310.jfif?&output-quality=75&output-format=jpg\",\"imageSequencing\":null,\"imageFilterInfo\":null,\"catCode\":\"340784\",\"thumbnailURL\":null,\"seekTags\":null}]},\"panoramic\":{}}}],\"failureReason\":{},\"responseErrors\":null}");

        Assert.assertNotNull(searchRoomsExecutor.getHotelImages(searchRoomsRequest, new HashMap<>(), new HashMap<>(), new CountDownLatch(1), "DETAIL"));
    }

    @Test
    public void testGetHotelImagesReturnsNull() throws RestConnectorException, JsonParseException {
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        searchRoomsRequest.getSearchCriteria().setHotelId("123");
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.getDeviceDetails().setNetworkType("wifi");
        searchRoomsRequest.setExpData("{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,EMIDT:3}");

        Mockito.lenient().when(restConnectorUtil.performStaticRoomDetailGet(Mockito.anyMap(), Mockito.anyString())).thenReturn(null);
        Assert.assertNotNull(searchRoomsExecutor.getHotelImages(searchRoomsRequest,new HashMap<>(), new HashMap<>(), new CountDownLatch(1), "DETAIL"));
    }

    @Test
    public void testGetHotelImagesThrowsException() throws RestConnectorException, JsonParseException {
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        searchRoomsRequest.getSearchCriteria().setHotelId("123");
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.getDeviceDetails().setNetworkType("wifi");
        searchRoomsRequest.setExpData("{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,EMIDT:3}");
        Mockito.lenient().when(restConnectorUtil.performStaticRoomDetailGet(Mockito.anyMap(), Mockito.anyString())).thenThrow(new RestConnectorException(DependencyLayer.ORCHESTRATOR, ErrorType.CONNECTIVITY,
                ConnectivityErrors.IO_ERROR.getErrorCode(), ""));
        Assert.assertNotNull(searchRoomsExecutor.getHotelImages(searchRoomsRequest, new HashMap<>(), new HashMap<>(), new CountDownLatch(1), "DETAIL"));
    }


}
