package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class ByPassExecutorTest {
    @InjectMocks
    private ByPassExecutor byPassExecutor;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Test
    public void executeByPassRequestTest() throws Exception {
        Mockito.when(restConnectorUtil.performPostByPass(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn("{}");
        Assert.assertNotNull(byPassExecutor.executeByPassRequest("requestBody", new HashMap<>(), "destinationURL", "correlationKey"));
    }

    @Test
    public void executeByPassRequestFlyfishTest() throws Exception {
        Mockito.when(restConnectorUtil.performPostByPass(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn("{}");
        Assert.assertNotNull(byPassExecutor.executeByPassRequestFlyfish("requestBody", new HashMap<>(), "destinationURL", "correlationKey"));
    }

    @Test
    public void executeGetByPassRequestTest() throws Exception {
        Mockito.when(restConnectorUtil.performGetByPass(Mockito.any(), Mockito.anyString())).thenReturn("{}");
        Assert.assertNotNull(byPassExecutor.executeGetByPassRequest(new HashMap<>(),"destinationURL", "correlationKey"));
    }
}
