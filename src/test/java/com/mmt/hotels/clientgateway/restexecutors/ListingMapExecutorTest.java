package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class ListingMapExecutorTest {

    @InjectMocks
    ListingMapExecutor listingMapExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private HeadersUtil headersUtil;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(listingMapExecutor, "listingMapUrl", "abc");
    }

    @Test
    public void listingMapTest() throws ClientGatewayException {

        String resp= null;

        try {


            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respListingMap").toString();

        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.when(restConnectorUtil.performListingMapPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        HotelListingMapResponse response = listingMapExecutor.listingMap(new SearchWrapperInputRequest(), new HashMap<String, String>());
        Assert.assertNotNull(response);

    }


}
