package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.CreateQuoteRequest;
import com.mmt.hotels.clientgateway.request.UpdateAffiliateFeeRequest;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.affiliate.CreateQuoteResponse;
import com.mmt.hotels.model.affiliate.GetQuoteRequest;
import com.mmt.hotels.model.affiliate.GetQuoteResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import com.mmt.hotels.model.request.CreateQuoteRequestBody;
import com.mmt.hotels.model.request.UpdateAffiliateFeeReqBody;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.response.addon.AddOnEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class AffiliateExecutorTest {

    @InjectMocks
    AffiliateExecutor affiliateExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    private String updateFeeUrl = "http://localhost:8090/api/v2.0/hotels/affiliate/updateFeel";

    private String createQuoteUrl = "http://localhost:8090/api/v2.0/hotels/affiliate/createQuote";

    private String getMergedQuoteDataUrl = "http://localhost:8090/api/v2.0/hotels/affiliate/getQuoteDataMerged?correlationKey={0}";

    @Before
    public void init() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(affiliateExecutor, "updateFeeUrl", updateFeeUrl);
        ReflectionTestUtils.setField(affiliateExecutor, "createQuoteUrl", createQuoteUrl);
        ReflectionTestUtils.setField(affiliateExecutor, "getMergedQuoteDataUrl", getMergedQuoteDataUrl);
    }

    @Test
    public void testGetUpdatedAffiliateFeeResponse() throws ClientGatewayException, UnsupportedEncodingException {
        UpdateAffiliateFeeRequest updateAffiliateFeeRequest = new UpdateAffiliateFeeRequest();
        updateAffiliateFeeRequest.setTxnKey("abcd");
        Mockito.when(restConnectorUtil.performAffiliateFeeUpdatePost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");

        UpdateAffiliateFeeResponse updatedAffiliateFeeResponse = affiliateExecutor.getUpdatedAffiliateFeeResponse(updateAffiliateFeeRequest, new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(updatedAffiliateFeeResponse);
    }

    @Test
    public void testGetCreateQuoteResponse() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(restConnectorUtil.performAffiliateCreateQuotePost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");

        CreateQuoteResponse createQuoteResponse = affiliateExecutor.getCreateQuoteResponse(new CreateQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(createQuoteResponse);
    }

    @Test
    public void testGetPersistedQuoteDataMerged() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(restConnectorUtil.performGetQuoteDataMerged(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");

        GetQuoteResponse persistedQuoteDataMerged = affiliateExecutor.getPersistedQuoteDataMerged(new GetQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(persistedQuoteDataMerged);
    }
    
    @Test
    public void testGetUpdatedAffiliateFeeOldResponse() throws UnsupportedEncodingException, ClientGatewayException {
    	Mockito.when(restConnectorUtil.performAffiliateFeeUpdatePost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
    	String result = affiliateExecutor.getUpdatedAffiliateFeeOldResponse(new UpdateAffiliateFeeReqBody(), new HashMap<>(), new HashMap<>(), "test");
    	Assert.assertNotNull(result);
    }
    
    @Test
    public void testGetCreateQuoteResponseOld() throws UnsupportedEncodingException, ClientGatewayException {
    	Mockito.when(restConnectorUtil.performAffiliateCreateQuotePost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
    	String result = affiliateExecutor.getCreateQuoteResponseOld(new CreateQuoteRequestBody(), new HashMap<>(), new HashMap<>(), "test");
    	Assert.assertNotNull(result);
    	
    }
}
