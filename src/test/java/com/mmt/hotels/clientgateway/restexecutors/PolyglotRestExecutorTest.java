package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.polyglot.LanguageData;
import com.mmt.hotels.model.polyglot.PolyglotTranslation;
import org.apache.commons.collections4.MapUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class PolyglotRestExecutorTest {

    @InjectMocks
    private PolyglotRestExecutor polyglotRestExecutor;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Before
    public void setUp() throws RestConnectorException {
        objectMapperUtil.init();
        Path path = Paths.get("src/test/resources/mock-polyglot.json");
        String result = null;
        try {
            Stream<String> lines = Files.lines(path);
            result = lines.collect(Collectors.joining("\n"));
            lines.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        ReflectionTestUtils.setField(polyglotRestExecutor, "polyglotUrl", "fake.url.com");
        ReflectionTestUtils.setField(polyglotRestExecutor, "polyglotMobGenUrl", "fake.url.com");
        Mockito.when(restConnectorUtil.fetchTranslatedData(Mockito.anyMap(), Mockito.anyString())).thenReturn(result);
    }

    @Test
    public void getPolyglotTranslationTest() throws ClientGatewayException {

        PolyglotTranslation polyglotTranslation = polyglotRestExecutor.getPolyglotTranslation();
        Assert.assertNotNull(polyglotTranslation);
        Map<String, LanguageData> languageDataMap = polyglotTranslation.getData().getAssets().getTranslationConstant().getLanguageDataMap();
        Assert.assertTrue(languageDataMap.containsKey("eng"));
        Assert.assertTrue(MapUtils.isNotEmpty(languageDataMap.get("eng").getTranslatedData()));
        Assert.assertTrue(languageDataMap.containsKey("hin"));
        Assert.assertTrue(MapUtils.isNotEmpty(languageDataMap.get("hin").getTranslatedData()));

    }

    @Test
    public void getMobGenPolyglotTranslationTest() throws ClientGatewayException {
        PolyglotTranslation polyglotTranslation = polyglotRestExecutor.getMobGenPolyglotTranslation();
        Assert.assertNotNull(polyglotTranslation);
        Map<String, LanguageData> languageDataMap = polyglotTranslation.getData().getAssets().getTranslationConstant().getLanguageDataMap();
        Assert.assertTrue(languageDataMap.containsKey("eng"));
        Assert.assertTrue(MapUtils.isNotEmpty(languageDataMap.get("eng").getTranslatedData()));
        Assert.assertTrue(languageDataMap.containsKey("hin"));
        Assert.assertTrue(MapUtils.isNotEmpty(languageDataMap.get("hin").getTranslatedData()));
    }
}