package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.enums.Country;
import com.gommt.hotels.orchestrator.detail.enums.Funnel;
import com.gommt.hotels.orchestrator.detail.enums.SiteDomain;
import com.gommt.hotels.orchestrator.detail.enums.TrafficType;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse;
import com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse;
import com.gommt.hotels.orchestrator.detail.model.state.RequestDetails;

import com.gommt.hotels.orchestrator.detail.enums.Country;
import com.gommt.hotels.orchestrator.detail.enums.Funnel;
import com.gommt.hotels.orchestrator.detail.enums.Region;
import com.gommt.hotels.orchestrator.detail.enums.SiteDomain;
import com.gommt.hotels.orchestrator.detail.enums.TrafficSource;
import com.gommt.hotels.orchestrator.detail.enums.TrafficType;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.HeaderConstants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class OrchDetailExecutorTest {

    @InjectMocks
    OrchDetailExecutor orchDetailExecutor;

    @Mock
    MetricAspect metricAspect;

    @Mock
    ObjectMapperUtil objectMapperUtil;

    @Mock
    RestConnectorUtil restConnectorUtil;

    private ObjectMapper mapper;
    private DetailRequest detailRequest;
    private Map<String, String[]> parameterMap;
    private Map<String, String> headers;
    private UpdatePriceResponse updatePriceResponse;
    private ClientDetails clientDetails;
    private RequestDetails requestDetails;

    @Before
    public void init() {
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(orchDetailExecutor, "orchSearchRooms", "http://test.url/search-rooms");
        ReflectionTestUtils.setField(orchDetailExecutor, "orchUpdatePrice", "http://test.url/update-price");

        detailRequest = new DetailRequest();
        parameterMap = new HashMap<>();
        parameterMap.put("test", new String[]{"value"});
        headers = new HashMap<>();
        headers.put("Authorization", "Bearer token");
        updatePriceResponse = new UpdatePriceResponse();
    }

    @Before
    public void setUp() {
        // Set up properties
        ReflectionTestUtils.setField(orchDetailExecutor, "orchStaticDetailsUrl", "http://test.com/staticDetails");

        // Create test data
        setupTestData();
    }

    private void setupTestData() {
        detailRequest = new DetailRequest();

        clientDetails = new ClientDetails();
        requestDetails = new RequestDetails();
        requestDetails.setRequestId("test-request-id");
        requestDetails.setJourneyId("test-journey-id");
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);

        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);

        parameterMap = new HashMap<>();
        parameterMap.put("existingParam", new String[]{"value1"});

        headers = new HashMap<>();
        headers.put("Authorization", "Bearer token");
        headers.put("User-Agent", "TestAgent");
    }

    // ===================== SEARCH ROOMS METHOD TESTS =====================

    @Test
    public void testSearchRooms_ValidRequestWithFullClientDetails_ShouldReturnResponse() throws Exception {
        // Given
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("existing", new String[]{"value"});
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"test\":\"json\"}";
        String responseJson = "{\"response\":\"json\"}";
        HotelDetailsResponse expectedResponse = createValidHotelDetailsResponse();

        // Mock
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchRoomsPost(eq(requestJson), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelDetailsResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(expectedResponse);

        // When
        HotelDetailsResponse result = orchDetailExecutor.searchRooms(detailRequest, parameterMap, headers);

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        // Verify all interactions
        verify(objectMapperUtil).getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);
        verify(restConnectorUtil).performOrchestratorSearchRoomsPost(eq(requestJson), any(Map.class), anyString());
        verify(objectMapperUtil).getObjectFromJson(responseJson, HotelDetailsResponse.class, DependencyLayer.ORCHESTRATOR_NEW);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchSearchRooms"), anyLong());
    }


    @Test
    public void testSearchRooms_DownstreamErrorResponse_ShouldThrowException() throws Exception {
        // Given
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"error\":\"request\"}";
        String responseJson = "{\"error\":\"response\"}";
        HotelDetailsResponse errorResponse = createHotelDetailsResponseWithError();

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelDetailsResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(errorResponse);

        // When & Then
        try {
            orchDetailExecutor.searchRooms(detailRequest, parameterMap, headers);
            fail("Expected ErrorResponseFromDownstreamException to be thrown");
        } catch (ErrorResponseFromDownstreamException exception) {
            assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertNotNull(exception.getMessage());
        }

        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchSearchRooms"), anyLong());
    }

    @Test
    public void testSearchRooms_JsonSerializationException_ShouldThrowException() throws Exception {
        // Given
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenThrow(new RuntimeException("JSON serialization failed"));

        // When & Then
        try {
            orchDetailExecutor.searchRooms(detailRequest, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON serialization failed", exception.getMessage());
        }

        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchSearchRooms"), anyLong());
    }

    @Test
    public void testSearchRooms_RestCallException_ShouldThrowException() throws Exception {
        // Given
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"rest\":\"exception\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenThrow(new RuntimeException("REST call failed"));

        // When & Then
        try {
            orchDetailExecutor.searchRooms(detailRequest, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("REST call failed", exception.getMessage());
        }

        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchSearchRooms"), anyLong());
    }

    // ===================== UPDATE PRICE METHOD TESTS =====================

    @Test
    public void testUpdatePrice_SuccessfulResponse() throws Exception {
        // Given
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // When
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);

        // Then
        assertNotNull(result);
        assertEquals(updatePriceResponse, result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        verify(objectMapperUtil).getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);
        verify(restConnectorUtil).performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString());
    }

    @Test
    public void testUpdatePrice_NullDetailRequest() {
        // When & Then
        try {
            orchDetailExecutor.updatePrice(null, parameterMap, headers);
            fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            assertEquals(DependencyLayer.CLIENTGATEWAY, exception.getDependencyLayer());
            assertEquals(ErrorType.VALIDATION, exception.getErrorType());
            assertEquals("INVALID_REQUEST", exception.getCode());
            assertEquals("DetailRequest cannot be null", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_NullParameterMap() throws Exception {
        // Given
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // When
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, null, headers);

        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
    }

    @Test
    public void testUpdatePrice_NullClientDetails() throws Exception {
        // Given
        detailRequest.setClientDetails(null);
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // When
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);

        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
    }

    @Test
    public void testUpdatePrice_PartialNullFields() throws Exception {
        // Given
        ClientDetails clientDetails = new ClientDetails();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setRequestId(null);
        requestDetails.setCountry(null);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId(null);
        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);

        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // When
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);

        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
    }

    @Test
    public void testUpdatePrice_NullResponse() throws Exception {
        // Given
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(null);

        // When & Then
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertEquals("EMPTY_RESPONSE", exception.getCode());
            assertEquals("Received null or empty response from orchestrator updatePrice API", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_EmptyResponse() throws Exception {
        // Given
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn("   ");

        // When & Then
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertEquals("EMPTY_RESPONSE", exception.getCode());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_ParseError() throws Exception {
        // Given
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(null);

        // When & Then
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertEquals("PARSE_ERROR", exception.getCode());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_ErrorResponseFromDownstream() throws Exception {
        // Given
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        ErrorResponse error = new ErrorResponse();
        error.setCode("ERROR_CODE");
        error.setMessage("Error message");
        error.setDescription("Error description");
        updatePriceResponse.setError(error);

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // When & Then
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            fail("Expected ErrorResponseFromDownstreamException to be thrown");
        } catch (ErrorResponseFromDownstreamException exception) {
            assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertEquals("ERROR_CODE", exception.getCode());
            assertEquals("Error message", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_JsonConversionException() throws Exception {
        // Given
        setUpCompleteDetailRequest();

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenThrow(new RuntimeException("JSON conversion error"));

        // When & Then
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON conversion error", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_RestConnectorException() throws Exception {
        // Given
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenThrow(new RuntimeException("REST connector error"));

        // When & Then
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("REST connector error", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_WithAllNullFieldsInRequest() throws Exception {
        // Given
        ClientDetails clientDetails = new ClientDetails();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);

        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorSearchHotelsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // When
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);

        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
    }

    // ===================== BUILD HEADER MAP TESTS =====================

    @Test
    public void testBuildHeaderMap_ValidHeaders_ShouldIncludeRequiredHeaders() throws Exception {
        // Given
        Map<String, String> inputHeaders = new HashMap<>();
        inputHeaders.put("X-Custom-Header", "custom-value");

        // Use reflection to access private method
        java.lang.reflect.Method method = OrchDetailExecutor.class.getDeclaredMethod("buildHeaderMap", Map.class);
        method.setAccessible(true);

        // When
        @SuppressWarnings("unchecked")
        Map<String, String> result = (Map<String, String>) method.invoke(orchDetailExecutor, inputHeaders);

        // Then
        assertNotNull(result);
        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("gzip", result.get("Accept-Encoding"));
    }

    @Test
    public void testBuildHeaderMap_NullHeaders_ShouldReturnDefaultHeaders() throws Exception {
        // Use reflection to access private method
        java.lang.reflect.Method method = OrchDetailExecutor.class.getDeclaredMethod("buildHeaderMap", Map.class);
        method.setAccessible(true);

        // When
        @SuppressWarnings("unchecked")
        Map<String, String> result = (Map<String, String>) method.invoke(orchDetailExecutor, (Map<String, String>) null);

        // Then
        assertNotNull(result);
        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("gzip", result.get("Accept-Encoding"));
    }

    // ===================== HELPER METHODS =====================

    private DetailRequest createValidDetailRequest() {
        DetailRequest detailRequest = new DetailRequest();
        ClientDetails clientDetails = new ClientDetails();

        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setRequestId("default-request-123");
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId("default-journey-123");

        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);

        return detailRequest;
    }

    private DetailRequest createDetailRequestWithClientDetails() {
        DetailRequest detailRequest = new DetailRequest();
        ClientDetails clientDetails = new ClientDetails();

        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setRequestId("test-request-123");
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId("journey-123");

        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);

        return detailRequest;
    }

    private HotelDetailsResponse createValidHotelDetailsResponse() {
        HotelDetailsResponse response = new HotelDetailsResponse();
        return response;
    }

    private HotelDetailsResponse createHotelDetailsResponseWithError() {
        HotelDetailsResponse response = new HotelDetailsResponse();
        ErrorResponse error = new ErrorResponse();
        error.setCode("ERROR_CODE");
        error.setMessage("Error message");
        error.setDescription("Error description");
        response.setError(error);
        return response;
    }

    private void setUpCompleteDetailRequest() {
        ClientDetails clientDetails = new ClientDetails();
        RequestDetails requestDetails = new RequestDetails();

        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setRequestId("test-request-id");
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId("test-journey-id");

        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);
    }

    // ========== STATIC DETAILS METHOD TESTS ==========

//    @Test
//    public void testStaticDetails_withValidInputs_returnsHotelStaticContentResponse() throws Exception {
//        // Arrange
//        String requestJson = "{\"hotelId\":\"123\"}";
//        String responseJson = "{\"hotelDetails\":{}}";
//        HotelStaticContentResponse expectedResponse = new HotelStaticContentResponse();
//
//        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenReturn(requestJson);
//        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), anyMap(), anyString())).thenReturn(responseJson);
//        when(objectMapperUtil.getObjectFromJson(eq(responseJson), eq(HotelStaticContentResponse.class), eq(DependencyLayer.ORCHESTRATOR_DETAIL))).thenReturn(expectedResponse);
//
//        // Act
//        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(expectedResponse, result);
//        verify(objectMapperUtil).getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);
//        verify(restConnectorUtil).performOrchestratorStaticDetailsPost(eq(requestJson), anyMap(), anyString());
//        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchSearchHotels"), anyLong());
//    }

//    @Test
//    public void testStaticDetails_withNullError_returnsSuccessfully() throws Exception {
//        // Arrange
//        String requestJson = "{\"hotelId\":\"123\"}";
//        String responseJson = "{\"hotelDetails\":{}}";
//        HotelStaticContentResponse expectedResponse = new HotelStaticContentResponse();
//        expectedResponse.setError(null);
//
//        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenReturn(requestJson);
//        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), anyMap(), anyString())).thenReturn(responseJson);
//        when(objectMapperUtil.getObjectFromJson(eq(responseJson), eq(HotelStaticContentResponse.class), eq(DependencyLayer.ORCHESTRATOR_DETAIL))).thenReturn(expectedResponse);
//
//        // Act
//        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(expectedResponse, result);
//    }

    @Test(expected = Exception.class)
    public void testStaticDetails_withException_throwsException() throws Exception {
        // Arrange
        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenThrow(new RuntimeException("JSON conversion failed"));

        // Act
        orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
    }

//    @Test
//    public void testStaticDetails_withNullJourneyId_handlesGracefully() throws Exception {
//        // Arrange
//        requestDetails.setJourneyId(null);
//        String requestJson = "{\"hotelId\":\"123\"}";
//        String responseJson = "{\"hotelDetails\":{}}";
//        HotelStaticContentResponse expectedResponse = new HotelStaticContentResponse();
//
//        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenReturn(requestJson);
//        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), anyMap(), anyString())).thenReturn(responseJson);
//        when(objectMapperUtil.getObjectFromJson(eq(responseJson), eq(HotelStaticContentResponse.class), eq(DependencyLayer.ORCHESTRATOR_DETAIL))).thenReturn(expectedResponse);
//
//        // Act
//        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(expectedResponse, result);
//    }

    // ========== PRIVATE METHOD TESTS ==========

    @Test
    public void testBuildHeaderMap_returnsCorrectHeaders() throws Exception {
        // Arrange
        Map<String, String> inputHeaders = new HashMap<>();
        inputHeaders.put("Custom-Header", "CustomValue");

        // Use reflection to access private method
        Method buildHeaderMapMethod = OrchDetailExecutor.class.getDeclaredMethod("buildHeaderMap", Map.class);
        buildHeaderMapMethod.setAccessible(true);

        // Act
        @SuppressWarnings("unchecked")
        Map<String, String> result = (Map<String, String>) buildHeaderMapMethod.invoke(orchDetailExecutor, inputHeaders);

        // Assert
        assertNotNull(result);
        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("gzip", result.get("Accept-Encoding"));
    }

    @Test
    public void testBuildHeaderMap_withNullInput_returnsDefaultHeaders() throws Exception {
        // Use reflection to access private method
        Method buildHeaderMapMethod = OrchDetailExecutor.class.getDeclaredMethod("buildHeaderMap", Map.class);
        buildHeaderMapMethod.setAccessible(true);

        // Act
        @SuppressWarnings("unchecked")
        Map<String, String> result = (Map<String, String>) buildHeaderMapMethod.invoke(orchDetailExecutor, (Map<String, String>) null);

        // Assert
        assertNotNull(result);
        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("gzip", result.get("Accept-Encoding"));
    }

//    @Test
//    public void testStaticDetails_withEmptyHeaders_handlesGracefully() throws Exception {
//        // Arrange
//        Map<String, String> emptyHeaders = new HashMap<>();
//        String requestJson = "{\"hotelId\":\"123\"}";
//        String responseJson = "{\"hotelDetails\":{}}";
//        HotelStaticContentResponse expectedResponse = new HotelStaticContentResponse();
//
//        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenReturn(requestJson);
//        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), anyMap(), anyString())).thenReturn(responseJson);
//        when(objectMapperUtil.getObjectFromJson(eq(responseJson), eq(HotelStaticContentResponse.class), eq(DependencyLayer.ORCHESTRATOR_DETAIL))).thenReturn(expectedResponse);
//
//        // Act
//        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, emptyHeaders);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(expectedResponse, result);
//    }
//
//    @Test
//    public void testStaticDetails_parameterMapModification_preservesOriginal() throws Exception {
//        // Arrange
//        Map<String, String[]> originalParameterMap = new HashMap<>();
//        originalParameterMap.put("original", new String[]{"value"});
//        String requestJson = "{\"hotelId\":\"123\"}";
//        String responseJson = "{\"hotelDetails\":{}}";
//        HotelStaticContentResponse expectedResponse = new HotelStaticContentResponse();
//
//        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenReturn(requestJson);
//        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), anyMap(), anyString())).thenReturn(responseJson);
//        when(objectMapperUtil.getObjectFromJson(eq(responseJson), eq(HotelStaticContentResponse.class), eq(DependencyLayer.ORCHESTRATOR_DETAIL))).thenReturn(expectedResponse);
//
//        // Act
//        orchDetailExecutor.staticDetails(detailRequest, originalParameterMap, headers);
//
//        // Assert - Original parameter map should not be modified by new parameter addition
//        assertEquals(1, originalParameterMap.size());
//        assertTrue(originalParameterMap.containsKey("original"));
//    }

    @Test
    public void testStaticDetails_withCompletelyNullFields_handlesGracefully() throws Exception {
        // Arrange
        DetailRequest nullDetailRequest = new DetailRequest();
        // Even for "completely null" test, we need minimal setup for required enum fields
        ClientDetails clientDetails = new ClientDetails();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setRequestId("test-request-id");
        requestDetails.setJourneyId("test-journey-id");
        clientDetails.setRequestDetails(requestDetails);
        nullDetailRequest.setClientDetails(clientDetails);

        String requestJson = "{\"hotelId\":\"123\"}";
        String responseJson = "{\"hotelDetails\":{}}";
        HotelStaticContentResponse expectedResponse = new HotelStaticContentResponse();

        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), anyMap(), anyString())).thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(eq(responseJson), eq(HotelStaticContentResponse.class), eq(DependencyLayer.ORCHESTRATOR_DETAIL))).thenReturn(expectedResponse);

        // Act
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(nullDetailRequest, parameterMap, headers);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResponse, result);
    }

//    @Test
//    public void testStaticDetails_metricsAreRecorded() throws Exception {
//        // Arrange
//        String requestJson = "{\"hotelId\":\"123\"}";
//        String responseJson = "{\"hotelDetails\":{}}";
//        HotelStaticContentResponse expectedResponse = new HotelStaticContentResponse();
//
//        when(objectMapperUtil.getJsonFromObject(any(), eq(DependencyLayer.CLIENTGATEWAY))).thenReturn(requestJson);
//        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), anyMap(), anyString())).thenReturn(responseJson);
//        when(objectMapperUtil.getObjectFromJson(eq(responseJson), eq(HotelStaticContentResponse.class), eq(DependencyLayer.ORCHESTRATOR_DETAIL))).thenReturn(expectedResponse);
//
//        // Act
//        orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
//
//        // Assert
//        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchSearchHotels"), anyLong());
//    }
    // ===================== CALENDAR AVAILABILITY METHOD TESTS =====================

    @Test
    public void testGetCalendarAvailability_SuccessfulResponse_WithMmtAuthHeader() throws Exception {
        // Given - Testing the happy path with mmt-auth header (Line 359-360)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("test", new String[]{"value"});
        Map<String, String> headers = new HashMap<>();
        headers.put("mmt-auth", "test-auth-token"); // Line 359-360 branch test

        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse =
                createValidConsolidatedCalendarResponse();

        // Set up the calendarAvailabilityUrl field
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        // Mock - Line 362: JSON conversion
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // Mock - Line 364: URL construction (we can't easily mock static methods, so we'll rely on the actual implementation)

        // Mock - Line 367: REST call
        when(restConnectorUtil.performCalendarAvailabilityPost(
                eq(requestJson),
                any(Map.class),
                anyString()))
                .thenReturn(responseJson);

        // Mock - Line 368: Response conversion
        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);

        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result =
                orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        // Verify all interactions and line coverage
        verify(objectMapperUtil).getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY); // Line 362
        verify(restConnectorUtil).performCalendarAvailabilityPost(eq(requestJson), any(Map.class), anyString()); // Line 367
        verify(objectMapperUtil).getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR); // Line 368
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong()); // Line 378

        // Verify header map contains mmt-auth (Line 359-360)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) ->
                headerMap.containsKey("mmt-auth") && "test-auth-token".equals(headerMap.get("mmt-auth"))), anyString());
    }

    @Test
    public void testGetCalendarAvailability_SuccessfulResponse_WithoutMmtAuthHeader() throws Exception {
        // Given - Testing without mmt-auth header (Line 359 false condition)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        // No mmt-auth header to test the false branch of Line 359

        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse =
                createValidConsolidatedCalendarResponse();

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);

        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result =
                orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        // Verify header map does NOT contain mmt-auth (Line 359 false branch)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) ->
                !headerMap.containsKey("mmt-auth")), anyString());

        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_EmptyMmtAuthHeader() throws Exception {
        // Given - Testing with empty mmt-auth header (Line 359 false condition due to StringUtils.isNotEmpty)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("mmt-auth", ""); // Empty string should trigger false in StringUtils.isNotEmpty

        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse =
                createValidConsolidatedCalendarResponse();

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);

        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result =
                orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);

        // Then
        assertNotNull(result);

        // Verify header map does NOT contain mmt-auth due to empty string (Line 359 false branch)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) ->
                !headerMap.containsKey("mmt-auth")), anyString());
    }

    @Test
    public void testGetCalendarAvailability_ResponseWithErrors_ShouldThrowException() throws Exception {
        // Given - Testing error response handling (Line 369-374)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"error\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse errorResponse =
                createConsolidatedCalendarResponseWithErrors();

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(errorResponse);

        // When & Then - Should throw ErrorResponseFromDownstreamException (Line 370-374)
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected ErrorResponseFromDownstreamException to be thrown");
        } catch (ErrorResponseFromDownstreamException exception) {
            // Verify exception details match Lines 370-374
            assertEquals(DependencyLayer.ORCHESTRATOR, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertEquals("CAL_ERROR_001", exception.getCode());
            assertEquals("Calendar error message", exception.getMessage());
        }

        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_ResponseWithNullErrors_ShouldReturnResponse() throws Exception {
        // Given - Testing response with null error list (Line 369 false condition)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse responseWithNullErrors =
                createConsolidatedCalendarResponseWithNullErrors();

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(responseWithNullErrors);

        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result =
                orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);

        // Then - Should return response (Line 376)
        assertNotNull(result);
        assertEquals(responseWithNullErrors, result);

        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_JsonSerializationException_ShouldThrowException() throws Exception {
        // Given - Testing JSON serialization exception (Line 362)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenThrow(new RuntimeException("JSON serialization failed"));

        // When & Then
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON serialization failed", exception.getMessage());
        }

        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_RestCallException_ShouldThrowException() throws Exception {
        // Given - Testing REST call exception (Line 367)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"calendar\":\"request\"}";

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenThrow(new RuntimeException("REST call failed"));

        // When & Then
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("REST call failed", exception.getMessage());
        }

        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_JsonDeserializationException_ShouldThrowException() throws Exception {
        // Given - Testing JSON deserialization exception (Line 368)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"malformed\":\"json\"}";

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenThrow(new RuntimeException("JSON deserialization failed"));

        // When & Then
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON deserialization failed", exception.getMessage());
        }

        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_NullResponse_ShouldHandleGracefully() throws Exception {
        // Given - Testing null response handling
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(null);

        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result =
                orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);

        // Then - Should return null (Line 376)
        assertNull(result);

        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_MetricsRecording_ShouldAlwaysRecordMetrics() throws Exception {
        // Given - Testing finally block metrics recording (Line 378)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();

        String requestJson = "{\"calendar\":\"metrics\"}";
        String responseJson = "{\"calendar\":\"metrics\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse =
                createValidConsolidatedCalendarResponse();

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);

        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result =
                orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);

        // Then
        assertNotNull(result);

        // Verify metrics are recorded with correct parameters (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_AllLines_ComprehensiveCoverage() throws Exception {
        // Given - Comprehensive test to ensure all lines are covered
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});
        Map<String, String> headers = new HashMap<>();
        headers.put("mmt-auth", "comprehensive-test-token");
        headers.put("other-header", "other-value");

        String requestJson = "{\"comprehensive\":\"test\"}";
        String responseJson = "{\"comprehensive\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse =
                createValidConsolidatedCalendarResponse();

        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");

        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        // URL construction uses actual implementation

        when(restConnectorUtil.performCalendarAvailabilityPost(eq(requestJson), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);

        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result =
                orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        // Verify all method calls and line coverage
        verify(objectMapperUtil).getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY); // Line 362
        verify(restConnectorUtil).performCalendarAvailabilityPost(eq(requestJson), any(Map.class), anyString()); // Line 367
        verify(objectMapperUtil).getObjectFromJson(responseJson,
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class,
                DependencyLayer.ORCHESTRATOR); // Line 368
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong()); // Line 378

        // Verify header construction (Lines 356-361)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) -> {
            return headerMap.containsKey("Accept-Encoding") && "gzip".equals(headerMap.get("Accept-Encoding")) &&
                    headerMap.containsKey("Content-Type") && "application/json".equals(headerMap.get("Content-Type")) &&
                    headerMap.containsKey("mmt-auth") && "comprehensive-test-token".equals(headerMap.get("mmt-auth"));
        }), anyString());
    }

    // ===================== HELPER METHODS FOR CALENDAR AVAILABILITY TESTS =====================

    private com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse createValidConsolidatedCalendarResponse() {
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse response =
                new com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse();
        // Set up a valid response with empty error list
        response.setErrorList(new java.util.ArrayList<>());
        return response;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse createConsolidatedCalendarResponseWithErrors() {
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse response =
                new com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse();

        // Create error list with one error for testing Lines 369-374
        java.util.List<com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse> errorList =
                new java.util.ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse error =
                new com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse();
        error.setCode("CAL_ERROR_001");
        error.setMessage("Calendar error message");
        errorList.add(error);

        response.setErrorList(errorList);
        return response;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse createConsolidatedCalendarResponseWithNullErrors() {
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse response =
                new com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse();
        // Leave errorList as null to test Line 369 false condition
        response.setErrorList(null);
        return response;
    }
}