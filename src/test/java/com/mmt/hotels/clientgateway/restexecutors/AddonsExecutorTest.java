package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.response.addon.AddOnEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class AddonsExecutorTest {

    @InjectMocks
    AddonsExecutor addonsExecutor;
    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Value("${webapi.addons.get.url}")
    private String getAddonsURL;

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(addonsExecutor, "getAddonsURL", "abc");
    }

    @Test
    public void testGetAddonsResponse() throws ClientGatewayException {

        Mockito.when(restConnectorUtil.performSearchRoomsPost(Mockito.any(),
                Mockito.anyMap(), Mockito.anyString())).thenReturn("{\"addOns\":[]}");
        String resp = addonsExecutor.getAddonsResponse(new GetAddonsRequest(),new HashMap<>(),String.class);
        Assert.assertNotNull(resp);

        Mockito.when(restConnectorUtil.performSearchRoomsPost(Mockito.any(),
                Mockito.anyMap(), Mockito.anyString())).thenReturn("{\"addOns\":[]}");
        AddOnEntity response = addonsExecutor.getAddonsResponse(new GetAddonsRequest(),new HashMap<>(), AddOnEntity.class);
        Assert.assertNotNull(response.getAddOns());

    }
}
