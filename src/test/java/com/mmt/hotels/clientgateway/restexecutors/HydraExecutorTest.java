package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraUserSegmentRequest;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class HydraExecutorTest {
	
	@InjectMocks
	HydraExecutor hydraExecutor;
	
	@Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;
    
    @Mock
    private CommonConfigHelper commonConfigHelper;
    
    @Spy
    private HeadersUtil headersUtil;
    
    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(hydraExecutor, "hydraUrl", "abc");
        ReflectionTestUtils.setField(hydraExecutor, "hydraUserSegmentUrl", "abc");
    }


    @Test
    public void testGetHydraMatchedSegment() throws ClientGatewayException {
        Assert.assertNull(hydraExecutor.getHydraMatchedSegment(new HydraUserSegmentRequest(), "test"));
    }

    @Test(expected = ClientGatewayException.class)
    public void getHydraLastBookedFlightResponseTest() throws ClientGatewayException {
        hydraExecutor.getHydraLastBookedFlightResponse(new HydraRequest(), "test");
    }
}
