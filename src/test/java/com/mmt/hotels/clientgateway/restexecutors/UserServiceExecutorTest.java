package com.mmt.hotels.clientgateway.restexecutors;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserLoginInfo;
import com.mmt.hotels.clientgateway.thirdparty.response.UserName;
import com.mmt.hotels.clientgateway.thirdparty.response.UserPersonalDetail;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;

@RunWith(MockitoJUnitRunner.class)
public class UserServiceExecutorTest {
	
	@InjectMocks
	UserServiceExecutor userServiceExecutor;
	
	@Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;
    
    @Mock
    private CommonConfigHelper commonConfigHelper;

    @Mock
    MetricAspect metricAspect;
    
    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(userServiceExecutor, "userServiceUrl", "abc");
        ReflectionTestUtils.setField(userServiceExecutor, "userServiceGuestUserUrl", "abc");
        Mockito.doNothing().when(metricAspect).addToTime(Mockito.any(),Mockito.any(),Mockito.anyLong());


    }
	
	@Test(expected = ClientGatewayException.class)
    public void getUserServiceResponse_TestException()throws  ClientGatewayException{
        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("test", "test");
        userServiceExecutor.getUserServiceResponse("test", "test", "test", "test", "test","","","", httpHeaderMap);
    }

    @Test
	public void getUserServiceResponse_TestResponse() throws ClientGatewayException {
		Map<String, String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("test", "test");

        String errorResponse = "{\"code\":400,\"message\":\"User-Identifier : Header is missing\"}";
        Mockito.when(restConnectorUtil.performUserServicePost(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(errorResponse);
        UserServiceResponse response = userServiceExecutor.getUserServiceResponse("test", "test", "test", "test", "test","","","", httpHeaderMap);
        Assert.assertNotNull(response);

		String res = "{\"result\":{\"extendedUser\":{\"accountId\":\"0\",\"travelDocuments\":[],\"associatedTravellers\":[{\"travelDocuments\":[],\"name\":{\"firstName\":\"Test\",\"lastName\":\"test\",\"middleName\":null,\"title\":\"Mr\"},\"travellerId\":6,\"updatedAt\":*************}],\"profileType\":\"PERSONAL\",\"primaryEmailId\":\"<EMAIL>\",\"profileId\":\"abc\",\"personalDetails\":{\"gender\":\"NS\",\"name\":{}},\"uuid\":\"abc\",\"loginInfoList\":[{\"loginId\":\"********\",\"loginType\":\"MOBILE\",\"countryCode\":\"91\",\"verified\":true},{\"loginId\":\"<EMAIL>\",\"loginType\":\"EMAIL\"}]}}}";
        Mockito.when(restConnectorUtil.performUserServicePost(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(res);
        response = userServiceExecutor.getUserServiceResponse("test", "test", "test", "test", "test","","","", httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getResult());
        Assert.assertNotNull(response.getResult().getExtendedUser());
	}

	@Test
    public void test_createUserServiceHeaderMap() throws Exception {
        Method method = userServiceExecutor.getClass().getDeclaredMethod("createUserServiceHeaderMap", String.class, String.class, String.class, String.class, String.class, String.class, Map.class);
        method.setAccessible(true);
        Mockito.when(commonConfigHelper.getCommonUserServiceAuthToken()).thenReturn("abc");
        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("test", "test");

        Map<String,String> headerMapInResponse;

        headerMapInResponse = (HashMap) method.invoke(userServiceExecutor, "auth123","test","test","test","test","test",httpHeaderMap);
        Assert.assertNotNull(headerMapInResponse);
        Assert.assertNotNull(headerMapInResponse.get("user-identifier"));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("auth")));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("auth123")));

        headerMapInResponse = (HashMap) method.invoke(userServiceExecutor, null,null,null,null,null,"uuid123",httpHeaderMap);
        Assert.assertNotNull(headerMapInResponse);
        Assert.assertNotNull(headerMapInResponse.get("user-identifier"));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("uuid")));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("uuid123")));

        headerMapInResponse = (HashMap) method.invoke(userServiceExecutor, null,"<EMAIL>",null,null,null,null,httpHeaderMap);
        Assert.assertNotNull(headerMapInResponse);
        Assert.assertNotNull(headerMapInResponse.get("user-identifier"));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("login-id")));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("profileType")));

        headerMapInResponse = (HashMap) method.invoke(userServiceExecutor, null,null,"124",null,null,null,httpHeaderMap);
        Assert.assertNotNull(headerMapInResponse);
        Assert.assertNotNull(headerMapInResponse.get("user-identifier"));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("login-id")));
        Assert.assertTrue((headerMapInResponse.get(("user-identifier")).contains("124")));
    }

    @Test
    public void createGuestUserTest() throws ClientGatewayException {
        String resp = "{\"result\":{\"data\":{\"profileId\":\"1234\"},\"extendedUser\":{\"primaryEmailId\":\"<EMAIL>\",\"uuid\":\"abcd\"},\"success\":true}}";

        Mockito.when(restConnectorUtil.performUserServicePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        UserServiceResponse response = userServiceExecutor.createGuestUser("first", "last", "<EMAIL>", 2, "abcd", "in","8860936763","+91");
        Assert.assertNotNull(response);
    }
	
    @Test
    public void testGetUserDetails() {
    	Map<String, String> headers = new HashMap<>();
        headers.put("auth", "test");
        headers.put("org", "MMT");
        headers.put("backup_auth", "mmtAuthtest");
    	UserDetailsDTO userDetailsDto =userServiceExecutor.getUserDetails("IOS", headers, "test", "IN");
    	Assert.assertNull(userDetailsDto);
        userDetailsDto =userServiceExecutor.getUserDetails("ANDROID", headers, "test", "IN");
    	Assert.assertNull(userDetailsDto);
    }
    
    @Test
    public void parseUserServiceResponseTest() {
    	ExtendedUser extendedUser = new ExtendedUser();
    	extendedUser.setPersonalDetails(new UserPersonalDetail());
    	extendedUser.getPersonalDetails().setName(new UserName());
    	extendedUser.setLoginInfoList(new ArrayList<>());
    	extendedUser.getLoginInfoList().add(new UserLoginInfo());
    	extendedUser.getLoginInfoList().get(0).setLoginType("MOBILE");
    	Assert.assertNotNull(userServiceExecutor.parseUserServiceResponse(extendedUser));
    }

}
