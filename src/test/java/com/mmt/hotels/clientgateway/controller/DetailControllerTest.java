package com.mmt.hotels.clientgateway.controller;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.DetailService;
import com.mmt.hotels.clientgateway.service.EmiService;
import com.mmt.hotels.clientgateway.util.CustomValidator;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class DetailControllerTest {

    @InjectMocks
    private DetailController detailController;

    @Mock
    private DetailService detailService;

    @Spy
    private MDCHelper mdcHelper;

    @Spy
    private CustomValidator customValidator;

    @Mock
    private MetricAspect metricAspect;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    private EmiService emiService;

    @Test
    public void searchRoomsAPITest() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "trinatest");
        Mockito.when(detailService.searchRooms(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new SearchRoomsResponse());
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setRequestDetails(new RequestDetails());
        SearchRoomsCriteria searchRoomsCriteria = new SearchRoomsCriteria();
        searchRoomsCriteria.setHotelId("123");
        searchRoomsRequest.setSearchCriteria(searchRoomsCriteria);
        ResponseEntity<ResponseWrapper<SearchRoomsResponse>> resp = detailController.searchRooms("PWA", "1.0.0", searchRoomsRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }
    
    @Test
    public  void staticDetailAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(detailService.staticDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new StaticDetailResponse());
        StaticDetailRequest staticDetailRequest = new StaticDetailRequest();
        staticDetailRequest.setRequestDetails(new RequestDetails());
        staticDetailRequest.setSearchCriteria(new StaticDetailCriteria());
        staticDetailRequest.getSearchCriteria().setHotelId("1234");
        ResponseEntity<ResponseWrapper<StaticDetailResponse>>  resp = detailController.staticDetail("PWA","1.0.0",staticDetailRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }
    
    @Test
    public  void updatePriceAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "trinatest");
        Mockito.when(detailService.updatePrice(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new UpdatePriceResponse());
        UpdatePriceRequest updatePriceRequest = new UpdatePriceRequest();
        updatePriceRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<UpdatePriceResponse>> resp = detailController.updatePrice("PWA", "1.0.0", updatePriceRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void getUpdatedEmiTest() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(emiService.updatedEmiDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyMap())).thenReturn(new SearchRoomsResponse());
        UpdatedEmiRequest updatedEmiRequest = new UpdatedEmiRequest();
        updatedEmiRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<SearchRoomsResponse>> resp = detailController.getUpdatedEmi("PWA", "1.0.0", updatedEmiRequest, "123", "123", request, response);
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public void getCityGuideTest() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(detailService.cityGuideData(Mockito.any(), Mockito.any(), Mockito.anyMap(),Mockito.any())).thenReturn(new CityGuideResponse());
        ResponseEntity<ResponseWrapper<CityGuideResponse>> resp = detailController.getCityGuide("PWA", "1.0.0", new CityGuideRequest(), "123", "123", request, response);
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }


    @Test
    public void testWishListedStaticDetail() throws Exception{
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "qwerty");
        WishListedHotelsDetailRequest wishListedHotelsDetailRequest = getWishListedHotelsDetailRequest();
        Mockito.when(detailService.wishListedStaticDetail(Mockito.any(WishListedHotelsDetailRequest.class), Mockito.anyMap(), Mockito.anyMap()))
                .thenReturn(new WishListedHotelsDetailResponse());
        ResponseEntity<ResponseWrapper<WishListedHotelsDetailResponse>> responseEntity =
                detailController.wishListedStaticDetail("ANDROID", "1.0.0", wishListedHotelsDetailRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

    private WishListedHotelsDetailRequest getWishListedHotelsDetailRequest() {
        return new Gson().fromJson("{\"deviceDetails\":{\"appVersion\":\"8.6.4.RC1\",\"bookingDevice\":\"ANDROID\",\"deviceId\":\"311c8e73900a8d98\",\"deviceType\":\"Mobile\"," +
                "\"networkType\":\"WiFi\",\"resolution\":\"xhdpi\"},\"requestDetails\":{\"channel\":\"Native\",\"couponCount\":3,\"funnelSource\":\"HOTELS\"," +
                "\"idContext\":\"B2C\",\"loggedIn\":true,\"pageContext\":\"DETAIL\",\"siteDomain\":\"in\",\"visitNumber\":1," + "\"visitorId\":\"74856825370536007467679936889328709665\"}," +
                "\"searchCriteria\":{\"hotelIds\":[\"201412311314381986\",\"200803050906255532\",\"201007011525075813\",\"201904081604352311\"],\"visitNumber\":1," +
                "\"visitorId\":\"74856825370536007467679936889328709665\",\"cityCode\":\"CTDEL\",\"locationId\":\"CTDEL\",\"locationType\":\"city\",\"countryCode\":\"IN\"," +
                "\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[],\"positionsForAgeLessThan1\":[]}]}," +
                "\"imageDetails\":{\"categories\":[{\"count\":4,\"height\":252,\"imageFormat\":\"webp\",\"type\":\"H\",\"width\":459}],\"types\":[\"professional\",\"traveller\"]}," +
                "\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\",\"MANUAL\",\"OTHER\",\"EXT\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]}}", WishListedHotelsDetailRequest.class);
    }

    @Test
    public void calendarAvailabilityTest() throws ClientGatewayException {
        MockHttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        httpServletRequest.addHeader("tid", "test");
        CalendarAvailabilityRequest calendarAvailabilityRequest = new CalendarAvailabilityRequest();
        Mockito.doReturn(new CalendarAvailabilityResponse()).when(detailService).fetchCalendarAvailability(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        ResponseEntity<ResponseWrapper<CalendarAvailabilityResponse>> responseEntity  = detailController
                .calendarAvailability(calendarAvailabilityRequest, "ANDROID", httpServletRequest, httpServletResponse, "ck", "rId");
        Assert.assertEquals(HttpStatus.OK , responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

}
