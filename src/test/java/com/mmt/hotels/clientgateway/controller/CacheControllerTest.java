package com.mmt.hotels.clientgateway.controller;

import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.CacheStatistics;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class CacheControllerTest {

    @InjectMocks
    private CacheController cacheController;

    @Mock
    private PolyglotService polyglotService;

    @Test
    public void testPostByPass() {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();

        Map<String , CacheStats> map = new HashMap<>();
        CacheStats cacheStats = new CacheStats(100L, 1L , 0L, 0L, 0L, 0L);
        map.put(Constants.TRANSLATION_CACHE , cacheStats);
        Mockito.when(polyglotService.getCacheStats()).thenReturn(map);

        Map<String, CacheStatistics> stats = cacheController.getCacheStats(request , response);
        Assert.assertNotNull(stats.get(Constants.TRANSLATION_CACHE));

    }

}
