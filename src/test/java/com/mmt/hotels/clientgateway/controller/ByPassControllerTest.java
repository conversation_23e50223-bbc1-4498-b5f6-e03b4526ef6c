package com.mmt.hotels.clientgateway.controller;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mmt.hotels.clientgateway.util.RequestHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import com.mmt.hotels.clientgateway.constants.ByPassUrls;

import junit.framework.Assert;

@RunWith(MockitoJUnitRunner.class)
public class ByPassControllerTest {
	
	@InjectMocks
	private ByPassController byPassController;

	@Spy
	private RequestHandler requestHandler;
	
	@Test(expected = Exception.class)
	public void testPostByPass() {
		HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
		byPassController.postByPass("test", "test", "test", request, response);
	}
	
	@Test(expected = Exception.class)
	public void testGetByPass() {
		HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
		byPassController.getByPass("test", "test", request, response);
	}
	
	@Test
	public void testGetDestinationUrl() throws UnsupportedEncodingException {
		Map<String, String[]> parameterMap = new HashMap<String, String[]>();
		parameterMap.put("test", new String[1]);
		parameterMap.get("test")[0] = "test";
		
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_GET_FLYFISH_UPVOTE_DOWNVOTE_URL, 
				parameterMap, "test",null));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_OTP_GENERATE_URL,
				parameterMap, "test",null));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_OTP_VALIDATE_URL,
				parameterMap, "test",null));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_GET_TOTAL_PRICING_URL,
				parameterMap, "test",null));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_VALIDATE_COUPON_URL,
				parameterMap, "test",null));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_EMI_DETAILS,
				parameterMap, "test",null));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_STATIC_POLICIES,
				parameterMap, "test",null));
	}
}
