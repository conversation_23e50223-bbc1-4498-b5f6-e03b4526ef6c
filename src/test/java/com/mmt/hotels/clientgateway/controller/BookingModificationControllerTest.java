package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.request.modification.ProBookingRequest;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.response.modification.ProBookingResponse;
import com.mmt.hotels.clientgateway.response.modification.RatePreviewResponse;
import com.mmt.hotels.clientgateway.service.BookingModificationService;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class BookingModificationControllerTest {

    @InjectMocks
    BookingModificationController bkgModController;

    @Mock
    BookingModificationService bkgModService;

    @Mock
    RequestHandler requestHandler;

    @Mock
    Utility utility;


    @Test
    public void fetchDetailPriceTest() throws  Exception{
       RatePreviewRequest rateReviewRequest = new RatePreviewRequest();
       String version = "1";
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        Tuple<String, Map<String, String>> tup = new Tuple<>("abcdfd", new HashMap<>());
        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any())).thenReturn(tup);
        Mockito.when(bkgModService.fetchDetailPrice(rateReviewRequest, tup.getY())).thenReturn(new RatePreviewResponse());
        ResponseEntity<RatePreviewResponse> resp = bkgModController.fetchDetailPrice(rateReviewRequest,version,httpServletRequest,httpServletResponse);
        Assert.assertNotNull(resp);
        Assert.assertEquals(200, resp.getStatusCode().value());
    }

    @Test
    public void fetchReviewPriceTest() throws  Exception{
        RatePreviewRequest rateReviewRequest = new RatePreviewRequest();
        String version = "1";
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        Tuple<String, Map<String, String>> tup = new Tuple<>("abcdfd", new HashMap<>());
        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any())).thenReturn(tup);
        Mockito.when(bkgModService.fetchReviewPrice(rateReviewRequest, tup.getY())).thenReturn(new RatePreviewResponse());
        ResponseEntity<RatePreviewResponse> resp = bkgModController.fetchReviewPrice(rateReviewRequest,version,httpServletRequest,httpServletResponse);
        Assert.assertNotNull(resp);
        Assert.assertEquals(200, resp.getStatusCode().value());
    }

    @Test
    public void createProBooking(){
        ProBookingRequest proBookingRequest = new ProBookingRequest();
        String version = "1";
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        Tuple<String, Map<String, String>> tup = new Tuple<>("abcdfd", new HashMap<>());
        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any(),  Mockito.any())).thenReturn(tup);
        Mockito.when(bkgModService.createProBooking(Mockito.any(),Mockito.any())).thenReturn(new ProBookingResponse());
        ResponseEntity<ProBookingResponse> resp = bkgModController.createProBooking(proBookingRequest,version,httpServletRequest,httpServletResponse);
        Assert.assertNotNull(resp);
        Assert.assertEquals(200, resp.getStatusCode().value());
    }
}
