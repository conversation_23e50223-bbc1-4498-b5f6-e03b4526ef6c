package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.util.RequestHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class AltAccoControllerCBTest {


    @InjectMocks
    private AltAccoControllerCB altAccoControllerCB;

    @Spy
    private RequestHandler requestHandler;

    @Test(expected = Exception.class)
    public void testGetByPass() {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        altAccoControllerCB.getAltAccoBenefits("a","b", "", "Desktop","test", "test", request, response);
    }

}
