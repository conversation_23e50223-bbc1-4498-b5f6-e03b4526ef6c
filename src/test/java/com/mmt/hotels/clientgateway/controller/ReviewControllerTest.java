package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.response.ugc.ClientUgcResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.*;
import com.mmt.hotels.clientgateway.util.CustomValidator;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;

import com.mmt.hotels.model.response.flyfish.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class ReviewControllerTest {

    @InjectMocks
    private ReviewController reviewController;

    @Mock
    private ReviewService reviewService;

    @Spy
    private MDCHelper mdcHelper;

    @Mock
    private MetricAspect metricAspect;

    @Spy
    private CustomValidator customValidator;
    
    @Spy
    private RequestHandler requestHandler;

    @Mock
    private DiscountService discountService;

    @Mock
    private AddonsService addonsService;

    @Mock
    private PolicyService policyService;

    @Mock
    private RoomInfoService roomInfoService;

    @Test
    public  void availRoomsAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(reviewService.availRooms(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new AvailRoomsResponse());
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setRequestDetails(new RequestDetails());
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        ResponseEntity<ResponseWrapper<AvailRoomsResponse>>  resp = reviewController.availRooms("PWA","1.0.0",availRoomsRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void testValidateCoupon() throws ClientGatewayException {
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        ValidateCouponRequest validateCouponRequest = new ValidateCouponRequest();
        Mockito.when(discountService.validateCoupon(Mockito.any(),Mockito.any(),Mockito.anyMap(),Mockito.anyString()))
                .thenReturn(new ValidateCouponResponseBody());
        ResponseEntity<ResponseWrapper<ValidateCouponResponseBody>> resp = reviewController.validateCoupon(validateCouponRequest, "PWA", "1",
                httpServletRequest, httpServletResponse, null, null);
        Assert.assertNotNull(resp.getBody());
    }

    @Test
    public void testSearchAddons() throws ClientGatewayException {
		/*Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any());*/
        Mockito.when(addonsService.getAddons(Mockito.any(SearchAddonsRequest.class),Mockito.anyMap(),Mockito.any())).thenReturn(new SearchAddonsResponse());
        SearchAddonsRequest searchAddonsRequest = new SearchAddonsRequest();
        searchAddonsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<SearchAddonsResponse>> respEntity = reviewController.searchAddons(
                "PWA", "1",searchAddonsRequest ,new MockHttpServletRequest(), new MockHttpServletResponse(), null, null);
        Assert.assertNotNull(respEntity.getBody());
    }


    @Test
    public  void totalPricingAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(reviewService.getTotalPrice(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new TotalPriceResponse());
        ResponseEntity<ResponseWrapper<TotalPriceResponse>>  resp = reviewController.getTotalPrice(new TotalPricingRequest(),"PWA","1.0.0", request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void testGetPolicies() throws ClientGatewayException {
        PoliciesRequest policiesRequest = new PoliciesRequest();
        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(policyService.getPolicies(Mockito.any(),Mockito.any())).thenReturn(new PoliciesResponse());
        ResponseEntity<PoliciesResponse> responseEntity = reviewController
                .getPolicies("PWA", "1", policiesRequest,new MockHttpServletRequest(), new MockHttpServletResponse(), null, null);
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public void testRoomInfo() throws ClientGatewayException {
        RoomInfoRequest roomInfoRequest = new RoomInfoRequest();
        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(roomInfoService.getRoomInfo(Mockito.any(),Mockito.any())).thenReturn(new RoomInfoResponse());
        ResponseEntity<RoomInfoResponse> responseEntity = reviewController
                .getRoomInfo("PWA", "1", roomInfoRequest,new MockHttpServletRequest(), new MockHttpServletResponse(), null, null);
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public void testRoomInfos() throws ClientGatewayException {
        RoomInfoRequest roomInfoRequest = new RoomInfoRequest();
        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(roomInfoService.getRoomInfos(Mockito.any(),Mockito.any())).thenReturn(new RoomInfoResponse());
        ResponseEntity<ResponseWrapper<RoomInfoResponse>> responseEntity = reviewController
                .getRoomInfos("PWA", "1", roomInfoRequest, new MockHttpServletRequest(), new MockHttpServletResponse(), null, null);
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public  void payLaterEligibilityAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(reviewService.fetchPayLaterEligibility(Mockito.any(),Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PayLaterEligibilityResponse());
        PayLaterEligibilityRequest payLaterEligibilityRequest = new PayLaterEligibilityRequest();
        ResponseEntity<ResponseWrapper<PayLaterEligibilityResponse>> resp = reviewController.checkPayLaterEligibility(payLaterEligibilityRequest, "android",
                "2", request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void getUgcReviewsGITest() throws ClientGatewayException {
        UgcReviewRequest ugcReviewsRequest = new UgcReviewRequest();
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
//        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
//                Mockito.any(), Mockito.any(), Mockito.any());
        UgcReviewResponseData ugcReviewResponseData = new UgcReviewResponseData();
        UgcReviewResponse ugcReviewResponse = new UgcReviewResponse();
        ugcReviewResponse.setGiReviewCount(1);
        ugcReviewResponseData.setResponse(ugcReviewResponse);
        Mockito.when(reviewService.getUgcReviewsFromHes(Mockito.any(), Mockito.any())).thenReturn(ugcReviewResponseData);
        ResponseEntity<UgcReviewResponseData> responseEntity = reviewController.getUgcReviewsGI(ugcReviewsRequest, new MockHttpServletRequest(), new MockHttpServletResponse(), "123", "V1", "android", "IN","");
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public void getUgcSummaryGITest() throws ClientGatewayException {
        UgcSummaryRequest ugcSummaryRequest = new UgcSummaryRequest();
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        UGCPlatformReviewSummaryDTO ugcReviewResponseData = new UGCPlatformReviewSummaryDTO();
        ugcReviewResponseData.setCityCode("123");
        Mockito.when(reviewService.getUgcSummaryFromHes(Mockito.any())).thenReturn(ugcReviewResponseData);
        ResponseEntity<UGCPlatformReviewSummaryDTO> responseEntity = reviewController.getUgcSummaryGI(ugcSummaryRequest, request, response, "123", "V1", "android", "IN","");
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public void testGetPoliciesWithMissingHeaders() throws ClientGatewayException {
        PoliciesRequest policiesRequest = new PoliciesRequest();
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(policyService.getPolicies(Mockito.any(), Mockito.any())).thenReturn(new PoliciesResponse());
        ResponseEntity<PoliciesResponse> responseEntity = reviewController.getPolicies("PWA", "1", policiesRequest, request, response, null, null);
        Assert.assertNotNull(responseEntity.getBody());
        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    }

    @Test
    public void testSubmitAnswersGi() throws ClientGatewayException {
        ClientSubmitApiRequest submitRequest = new ClientSubmitApiRequest();
        MultipartRequest multipartRequest = Mockito.mock(MultipartRequest.class);
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(reviewService.submitAnswers(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ClientUgcResponse());
        ResponseEntity<ResponseWrapper<ClientUgcResponse>> responseEntity = reviewController.submitAnswersGi("PWA", submitRequest, multipartRequest, request, response, null, null);
        Assert.assertNotNull(responseEntity.getBody());
        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    }
}
