package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.service.ThankYouService;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class ThankYouControllerCBTest {

    @InjectMocks
    private ThankYouControllerCB thankYouControllerCB;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    private ThankYouService thankYouService;

    @Test
    public void testGetStaticDetailsResponse() throws ClientGatewayException {
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        HotelDetailsMobRequestBody request = new HotelDetailsMobRequestBody();

        Mockito.when(thankYouService.getThankYouResponseOld(Mockito.any(), Mockito.anyMap(), Mockito.any()))
                .thenReturn("");


        String resp = thankYouControllerCB.getTxnDataForMultiRoom(httpServletRequest, httpServletResponse, "");
        Assert.assertNotNull(resp);
    }

}
