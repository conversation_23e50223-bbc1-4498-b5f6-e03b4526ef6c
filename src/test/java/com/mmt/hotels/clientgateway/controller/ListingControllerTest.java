package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponseV2;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.util.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class ListingControllerTest {

    @InjectMocks
    private ListingController listingController;

    @Mock
    private ListingService listingService;

    @Spy
    private MDCHelper mdcHelper;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private Utility utility;

    @Spy
    private CustomValidator customValidator;
    
    @Spy
    private RequestHandler requestHandler;

    @Test
    public  void filterCountAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(listingService.filterCount(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FilterResponse());
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FilterResponse>>  resp = listingController.filterCount("PWA","1.0.0",filterCountRequest, true, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void batchFilterAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.batchFilterResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FilterResponse());
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FilterResponse>>  resp = listingController.batchFilter("PWA","1.0.0",filterCountRequest, true, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }
    
    @Test
    public  void searchHotelsAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(listingService.searchHotels(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new SearchHotelsResponse());
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<SearchHotelsResponse>>  resp = listingController.searchHotels("PWA","1.0.0",searchHotelsRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }
    
    @Test
    public  void mobLandingAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(listingService.mobLanding(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new MobLandingResponse());
        MobLandingRequest mobLandingRequest = new MobLandingRequest();
        mobLandingRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<MobLandingResponse>>  resp = listingController.mobLanding("PWA","1.0.0",mobLandingRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }
    
    @Test
    public  void listingMapAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(listingService.listingMap(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ListingMapResponse());
        ListingMapRequest listingMapRequest = new ListingMapRequest();
        listingMapRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<ListingMapResponse>>  resp = listingController.listingMap("PWA","1.0.0",listingMapRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void fetchCollectionsAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(listingService.fetchCollections(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FetchCollectionResponse());
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FetchCollectionResponse>>  resp = listingController.fetchCollection(fetchCollectionRequest,"ANDROID","1.0.0", request, response, "ck", "rId");
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void fetchCollectionV2Test() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","rahultest");
        Mockito.when(listingService.fetchCollectionsV2(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FetchCollectionResponseV2());
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FetchCollectionResponseV2>>  resp = listingController.fetchCollectionV2(fetchCollectionRequest,"ANDROID","1.0.0", request, response, "ck", "rId");
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void testWishListedHotels() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "qwerty");
        Mockito.when(listingService.searchHotels(Mockito.any(), Mockito.anyMap(), Mockito.anyMap())).thenReturn(new SearchHotelsResponse());
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<SearchHotelsResponse>> responseEntity = listingController.wishListedHotels("ANDROID", "1.0.0", searchHotelsRequest, request, response, null, null);
        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

}
