package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class MediaTagControllerTest {

    @InjectMocks
    private MediaTagController mediaTagController;

    @Mock
    Utility utility;

    @Spy
    private RequestHandler requestHandler;

    @SneakyThrows
    @Test(expected = Exception.class)
    public void testPostByPass() {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        mediaTagController.getTaggedMedia("correlationkey", "requestId","voyid", "mHtlId","android",1850,true, request, response);
    }

    @SneakyThrows
    @Test(expected = Exception.class)
    public void testGetByPass() {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        mediaTagController.getMediaDataByID("correlationkey", "requestId","voyid", "[1]", "0", "10", "mHtlId", request, response);
    }
}
