package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.payment.PaymentRequestClient;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.service.PaymentService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class PaymentControllerTest {

    @InjectMocks
    PaymentController paymentController;

    @Mock
    PaymentService paymentService;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    PaymentHelper paymentHelper;

    @Test
    public void paymentCheckoutExceptionTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(paymentService.paymentCheckoutOld(Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        PaymentCheckoutResponse responseData = paymentController.paymentCheckout("site-domain", "INR", "eng", "","", new BeginCheckoutReqBody()
        ,request , new MockHttpServletResponse());
        Assert.assertNotNull(responseData.getResponseErrors());
        Assert.assertEquals("3409", responseData.getResponseErrors().getErrorList().get(0).getErrorCode());
    }

    @Test
    public void paymentCheckoutTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(paymentService.paymentCheckoutOld(Mockito.any(), Mockito.any())).thenReturn(new PaymentCheckoutResponse());
        PaymentCheckoutResponse responseData = paymentController.paymentCheckout("site-domain", "INR", "eng", "","", new BeginCheckoutReqBody()
                ,request , new MockHttpServletResponse());
        Assert.assertNotNull(responseData);
    }


    @Test
    public void paymentCheckoutV2Test() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(paymentService.paymentCheckout(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new PaymentResponse());
        PaymentResponse responseData = paymentController.paymentCheckoutV2("site-domain", "rId","ANDROID", "1",  new PaymentRequestClient()
                ,request , new MockHttpServletResponse());
        Assert.assertNotNull(responseData);
    }

}
