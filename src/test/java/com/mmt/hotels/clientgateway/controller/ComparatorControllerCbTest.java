
package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.service.*;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.HotelComparatorResponse;
import com.mmt.hotels.pojo.response.detail.upsell.UpsellSimilarResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class ComparatorControllerCbTest {
    @InjectMocks
    ComparatorControllerCB comparatorControllerCB;

    @Mock
    ComparatorService comparatorService;

    @Spy
    private MDCHelper mdcHelper;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    private MetricAspect metricAspect;


    @Test
    public void testGetHotelComparisonResponse() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(comparatorService.comparatorOld(Mockito.any(), Mockito.anyMap(), Mockito.any(), Mockito.any())).thenReturn(new HotelComparatorResponse());
        HotelComparatorResponse comparatorResponse = comparatorControllerCB.getHotelComparisonResponse(new HotelDetailsMobRequestBody(),
                "test", "INR", "EN", "123", "123","type", request, response);
        Assert.assertNotNull(comparatorResponse);
        comparatorResponse = comparatorControllerCB.getHotelComparisonResponse(new HotelDetailsMobRequestBody(),
                "test", "INR", "EN", "", "","type",request, response);
        Assert.assertNotNull(comparatorResponse);
        Mockito.when(comparatorService.comparatorOld(Mockito.any(), Mockito.anyMap(), Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
        comparatorResponse = comparatorControllerCB.getHotelComparisonResponse(new HotelDetailsMobRequestBody(),
                "test", "INR", "EN", "", "","type",request, response);
        Assert.assertNotNull(comparatorResponse.getResponseErrors());

    }
}

