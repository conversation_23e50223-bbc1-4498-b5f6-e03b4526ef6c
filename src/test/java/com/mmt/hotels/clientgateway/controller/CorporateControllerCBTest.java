package com.mmt.hotels.clientgateway.controller;

import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mmt.hotels.clientgateway.util.MetricAspect;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.service.CorporateService;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.corporate.UpdateApprovalRequest;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateRequest;
import com.mmt.hotels.util.Tuple;

@RunWith(MockitoJUnitRunner.class)
public class CorporateControllerCBTest {

    @InjectMocks
    CorporateControllerCB corporateControllerCB;

    @Mock
    CorporateService corporateService;

    @Mock
    CorporateHelper corporateHelper;
    
    @Mock
	RequestHandler requestHandler;

    @Mock
    private MetricAspect metricAspect;
    
    @Before
    public void init() {
        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("abcd", new HashMap<>()));
    }

    @Test
    public void updateApprovalByAuthcodeTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(corporateService.updateApprovalByAuthcode(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new CGServerResponse());
        CGServerResponse responseData = corporateControllerCB.updateApprovalByAuthcode(new UpdateApprovalRequest(),
                "test","abcd", "abcd", request);
        Assert.assertNotNull(responseData);
    }

    @Test
    public void updateApprovalByAuthcodeErrorTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(corporateHelper.generateInvalidApprovalErrorRsp()).thenReturn(new com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse());
        CGServerResponse responseData = corporateControllerCB.updateApprovalByAuthcode(new UpdateApprovalRequest(),
                "","abcd","abcd", request);
        Assert.assertNotNull(responseData.getResponseErrors());
    }






    @Test
    public void getWorkflowInfoByAuthcodeTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(corporateService.workflowInfoByAuthcode(Mockito.any(),Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CGServerResponse());
        CGServerResponse responseData = corporateControllerCB.getWorkflowInfoByAuthcode("abcd", "test",
                "test","abcd", "", request);
        Assert.assertNotNull(responseData);
    }

    @Test
    public void getWorkflowInfoByAuthcodeErrorTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(corporateHelper.generateInvalidApprovalErrorRsp()).thenReturn(new com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse());

        CGServerResponse responseData = corporateControllerCB.getWorkflowInfoByAuthcode("", "test",
                "test","abcd", "", request);
        Assert.assertNotNull(responseData.getResponseErrors());
    }

    @Test
    public void testRequestApproval() throws Exception{
        HttpServletRequest httpRequest = new MockHttpServletRequest();
        HttpServletResponse httpResponse = new MockHttpServletResponse();
    	InitApprovalRequest approvalRequest = new InitApprovalRequest();
    	    	
    	Mockito.when(corporateService.requestApproval(Mockito.any(), Mockito.any(), Mockito.anyMap(),Mockito.any())).thenReturn(new CGServerResponse());
    	CGServerResponse resp = corporateControllerCB.requestApproval(approvalRequest , "", "", httpRequest, httpResponse);
    	Assert.assertNotNull(resp);
    	
    	//Exception case
    	Mockito.when(corporateService.requestApproval(Mockito.any(), Mockito.any(), Mockito.anyMap(),Mockito.any())).thenThrow(new RuntimeException(""));
    	Mockito.when(corporateHelper.getErrorResponseFromCBError(Mockito.any())).thenReturn(new com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse());
    	resp = corporateControllerCB.requestApproval(approvalRequest , "", "", httpRequest, httpResponse);
    	Assert.assertNotNull(resp.getResponseErrors());

    }
    
    @Test
    public void testUpdateCorpPolicy() throws Exception{
        HttpServletRequest httpRequest = new MockHttpServletRequest();
        HttpServletResponse httpResponse = new MockHttpServletResponse();
        CorpPolicyUpdateRequest request = new CorpPolicyUpdateRequest();
        
        Mockito.when(corporateService.updateCorpPolicy(Mockito.any(), Mockito.any(), Mockito.anyMap(),Mockito.any()))
        .thenReturn("{}");
		String resp = corporateControllerCB.updateCorpPolicy(request , "","","", httpRequest, httpResponse);
		Assert.assertNotNull(resp);
		
		//Exception case
        Mockito.when(corporateService.updateCorpPolicy(Mockito.any(), Mockito.any(), Mockito.anyMap(),Mockito.any())).thenThrow(new RuntimeException(""));
		resp = corporateControllerCB.updateCorpPolicy(request , "","","", httpRequest, httpResponse);
		Assert.assertNotNull(resp);

    }
    
}
