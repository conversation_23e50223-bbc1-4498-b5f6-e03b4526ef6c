package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.CreateQuoteRequest;
import com.mmt.hotels.clientgateway.request.GetQuoteRequest;
import com.mmt.hotels.clientgateway.request.UpdateAffiliateFeeRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse;
import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.AffiliateService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.CreateQuoteRequestBody;
import com.mmt.hotels.model.request.UpdateAffiliateFeeReqBody;
import com.mmt.hotels.model.response.GetQuoteResponseBody;
import com.mmt.hotels.util.Tuple;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class AffiliateControllerTest {

    @InjectMocks
    private AffiliateController affiliateController;

    @Mock
    private RequestHandler requestHandler;

    @Mock
    private AffiliateService affiliateService;

    @Mock
    private MetricAspect metricAspect;

    @Before
    public void init() {
        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("abcd", new HashMap<>()));
    }

    @Test
    public void testUpdateAffiliateFee() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(affiliateService.getUpdateAffiliateFeeResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UpdatedAffiliateFeeResponse());
        ResponseEntity<ResponseWrapper<UpdatedAffiliateFeeResponse>> resp = affiliateController.updateAffiliateFee("PWA", "1.0.0", new UpdateAffiliateFeeRequest(), request, response, "ck", "rId");
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public void testCreateQuote() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(affiliateService.createQuote(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CreateQuoteResponse());
        ResponseEntity<ResponseWrapper<CreateQuoteResponse>> resp = affiliateController.createQuote("PWA", "1.0.0", new CreateQuoteRequest(), request, response, "ck", "rId");
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public void testGetQuote() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(affiliateService.getQuote(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new AvailRoomsResponse());
        ResponseEntity<ResponseWrapper<AvailRoomsResponse>> resp = affiliateController.getQuote("PWA", "1.0.0", new GetQuoteRequest(), request, response, "ck", "rId");
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public void testUpdateAffiliateFeeOld() throws UnsupportedEncodingException, ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(affiliateService.getUpdateAffiliateFeeResponseOld(Mockito.any(),Mockito.anyMap(),Mockito.anyMap(),Mockito.anyString())).thenReturn("test");
        Assert.assertNotNull(affiliateController.updateAffiliateFee(request,response,new UpdateAffiliateFeeReqBody(),"1.0.0","123", "123"));
    }

    @Test
    public void testCreateQuoteOld() throws UnsupportedEncodingException, ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(affiliateService.createQuoteOld(Mockito.any(),Mockito.anyMap(),Mockito.anyMap(),Mockito.anyString())).thenReturn("test");
        Assert.assertNotNull(affiliateController.createQuote(request,response,new CreateQuoteRequestBody(),"PWA","in","INR","en","123", "123"));
    }

    @Test
    public void testGetQuoteOld() throws UnsupportedEncodingException, ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(affiliateService.getQuoteOld(Mockito.any(),Mockito.anyMap(),Mockito.anyMap(),Mockito.anyString())).thenReturn(new GetQuoteResponseBody());
        Assert.assertNotNull(affiliateController.getQuote(request,response,new com.mmt.hotels.model.affiliate.GetQuoteRequest(),"PWA","123", "123"));
    }
}
