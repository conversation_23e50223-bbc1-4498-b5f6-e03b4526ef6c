package com.mmt.hotels.clientgateway.controller;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class HealthCheckControllerTest {

    @InjectMocks
    private HealthCheckController healthCheckController;

    @Test
    public void testGetTest() {
        Assert.assertEquals("Server is running..", healthCheckController.getTest());
    }

    @Test
    public void monitorHealthTest() {
        Assert.assertEquals("{\"Status\": \"UP\"}", healthCheckController.monitorHealth());
    }

    @Test
    public void monitorHealthCBTest() {
        Assert.assertEquals("{\"Status\": \"UP\"}", healthCheckController.monitorHealthCB());
    }
}
