package com.mmt.hotels.clientgateway.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mmt.hotels.clientgateway.util.MetricAspect;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.service.DetailService;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;

@RunWith(MockitoJUnitRunner.class)
public class DetailControllerCBTest {

	@InjectMocks
	private DetailControllerCB detailControllerCB;
	
	@Spy
    private RequestHandler requestHandler;

	@Mock
	private DetailService detailService;

    @Mock
    private MetricAspect metricAspect;
	
	@Test
	public void testGetStaticDetailsResponse() throws ClientGatewayException{
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        HotelDetailsMobRequestBody request = new HotelDetailsMobRequestBody();
        
        Mockito.when(detailService.getStaticDetailsResponse(Mockito.any(),Mockito.any(),Mockito.anyMap()))
        .thenReturn("");
        String resp = detailControllerCB.getStaticDetailsResponse(request , "in",
        		"INR", "", "","", httpServletRequest, httpServletResponse);
        Assert.assertNotNull(resp);
        
        Mockito.when(detailService.getStaticDetailsResponse(Mockito.any(),Mockito.any(),Mockito.anyMap()))
        .thenThrow(new RuntimeException());
         resp = detailControllerCB.getStaticDetailsResponse(request , "in",
        		"INR", "", "","", httpServletRequest, httpServletResponse);
        Assert.assertNotNull(resp);
	}
	
}
