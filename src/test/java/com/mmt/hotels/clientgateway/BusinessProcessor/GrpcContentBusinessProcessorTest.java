package com.mmt.hotels.clientgateway.BusinessProcessor;

import com.gommt.hotels.content.proto.hotelsdatav2.HotelData;
import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Request;
import com.gommt.hotels.content.proto.hotelsdatav2.HotelsDataV2Response;
import com.gommt.hotels.content.proto.mediaid.MediaIdRequest;
import com.gommt.hotels.content.proto.mediaid.MediaIdResponse;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaRequest;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.grpcexecutors.StaticContentGrpcExecutor;
import com.mmt.hotels.clientgateway.response.gi.mediabyid.TaggedMediaByIdResponse;
import com.mmt.hotels.clientgateway.transformer.request.GrpcContentRequestBuilder;
import com.mmt.hotels.clientgateway.transformer.response.GrpcContentResponseBuilder;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class GrpcContentBusinessProcessorTest {

    @InjectMocks
    private GrpcContentBusinessProcessor grpcContentBusinessProcessor;

    @Mock
    private GrpcContentRequestBuilder grpcContentRequestBuilder;

    @Mock
    private GrpcContentResponseBuilder grpcContentResponseBuilder;

    @Mock
    private StaticContentGrpcExecutor staticContentGrpcExecutor;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    private static final String CORRELATION_KEY = "test-correlation-key";
    private static final String VOYAGER_ID = "test-voyager-id";
    private static final String MMT_HOTEL_ID = "test-mmt-hotel-id";
    private static final String GI = "test-gi";
    private static final String TAG_IDS = "tag1,tag2";
    private static final String OFFSET = "0";
    private static final String LIMIT = "10";
    private static final String EXPECTED_JSON_RESPONSE = "{\"success\":true}";

    @Before
    public void setUp() {
        // Reset mocks before each test
        Mockito.reset(grpcContentRequestBuilder, grpcContentResponseBuilder, staticContentGrpcExecutor, metricAspect, objectMapperUtil);
    }

    @Test
    public void testProcessTaggedMedia_Success_WithMmtHotelId() throws Exception {
        // Arrange
        TaggedMediaRequest mockRequest = TaggedMediaRequest.getDefaultInstance();
        TaggedMediaResponse mockGrpcResponse = TaggedMediaResponse.getDefaultInstance();
        com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse mockTransformedResponse = 
            new com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse();

        when(grpcContentRequestBuilder.buildTaggedMediaRequest(CORRELATION_KEY, MMT_HOTEL_ID, GI, true))
                .thenReturn(mockRequest);
        when(staticContentGrpcExecutor.getTaggedMedia(mockRequest, ""))
                .thenReturn(mockGrpcResponse);
        when(grpcContentResponseBuilder.buildTaggedMediaResponse(mockGrpcResponse))
                .thenReturn(mockTransformedResponse);
        when(objectMapperUtil.getJsonFromObjectWithView(eq(mockTransformedResponse), eq(DependencyLayer.HSC), eq(com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse.class)))
                .thenReturn(EXPECTED_JSON_RESPONSE);

        // Act
        String result = grpcContentBusinessProcessor.processTaggedMedia(CORRELATION_KEY, VOYAGER_ID, MMT_HOTEL_ID, GI, true);

        // Assert
        Assert.notNull(result);
        Assert.isTrue(EXPECTED_JSON_RESPONSE.equals(result), "Response should match expected JSON");
        verify(grpcContentRequestBuilder).buildTaggedMediaRequest(CORRELATION_KEY, MMT_HOTEL_ID, GI, true);
        verify(staticContentGrpcExecutor).getTaggedMedia(mockRequest, "");
        verify(grpcContentResponseBuilder).buildTaggedMediaResponse(mockGrpcResponse);
        verify(objectMapperUtil).getJsonFromObjectWithView(eq(mockTransformedResponse), eq(DependencyLayer.HSC), eq(com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse.class));
        verify(metricAspect).addToTime(anyLong(), anyString(), eq(""), eq(""), isNull(), eq(""));
    }

    @Test
    public void testProcessTaggedMedia_Success_WithoutMmtHotelId() throws Exception {
        // Arrange
        HotelsDataV2Request mockHotelMappingRequest = HotelsDataV2Request.newBuilder()
                .addVoyIds(VOYAGER_ID)
                .build();
        HotelsDataV2Response mockHotelMappingResponse = createMockHotelMappingResponse(MMT_HOTEL_ID);
        TaggedMediaRequest mockRequest = TaggedMediaRequest.getDefaultInstance();
        TaggedMediaResponse mockGrpcResponse = TaggedMediaResponse.getDefaultInstance();
        com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse mockTransformedResponse = 
            new com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse();

        when(grpcContentRequestBuilder.getHotelIdMappingRequest(VOYAGER_ID, CORRELATION_KEY))
                .thenReturn(mockHotelMappingRequest);
        when(staticContentGrpcExecutor.getHotelIdMapping(mockHotelMappingRequest, ""))
                .thenReturn(mockHotelMappingResponse);
        when(grpcContentRequestBuilder.buildTaggedMediaRequest(CORRELATION_KEY, MMT_HOTEL_ID, GI, false))
                .thenReturn(mockRequest);
        when(staticContentGrpcExecutor.getTaggedMedia(mockRequest, ""))
                .thenReturn(mockGrpcResponse);
        when(grpcContentResponseBuilder.buildTaggedMediaResponse(mockGrpcResponse))
                .thenReturn(mockTransformedResponse);
        when(objectMapperUtil.getJsonFromObjectWithView(eq(mockTransformedResponse), eq(DependencyLayer.HSC), eq(com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse.class)))
                .thenReturn(EXPECTED_JSON_RESPONSE);

        // Act
        String result = grpcContentBusinessProcessor.processTaggedMedia(CORRELATION_KEY, VOYAGER_ID, "", GI, false);

        // Assert
        Assert.notNull(result);
        Assert.isTrue(EXPECTED_JSON_RESPONSE.equals(result), "Response should match expected JSON");
        verify(grpcContentRequestBuilder).getHotelIdMappingRequest(VOYAGER_ID, CORRELATION_KEY);
        verify(staticContentGrpcExecutor).getHotelIdMapping(mockHotelMappingRequest, "");
        verify(grpcContentRequestBuilder).buildTaggedMediaRequest(CORRELATION_KEY, MMT_HOTEL_ID, GI, false);
        verify(metricAspect).addToTime(anyLong(), anyString(), eq(""), eq(""), isNull(), eq(""));
    }

    @Test
    public void testProcessTaggedMedia_Failure_NoHotelIdMapping() throws Exception {
        // Arrange
        HotelsDataV2Request mockHotelMappingRequest = HotelsDataV2Request.newBuilder()
                .addVoyIds(VOYAGER_ID)
                .build();
        HotelsDataV2Response mockHotelMappingResponse = HotelsDataV2Response.getDefaultInstance(); // Empty response

        when(grpcContentRequestBuilder.getHotelIdMappingRequest(VOYAGER_ID, CORRELATION_KEY))
                .thenReturn(mockHotelMappingRequest);
        when(staticContentGrpcExecutor.getHotelIdMapping(mockHotelMappingRequest, ""))
                .thenReturn(mockHotelMappingResponse);
        when(objectMapperUtil.getJsonFromObjectWithView(any(com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse.class), eq(DependencyLayer.HSC), eq(com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse.class)))
                .thenReturn("{\"success\":false}");

        // Act
        String result = grpcContentBusinessProcessor.processTaggedMedia(CORRELATION_KEY, VOYAGER_ID, "", GI, true);

        // Assert
        Assert.notNull(result);
        Assert.isTrue(result.contains("false"), "Response should indicate failure");
        verify(grpcContentRequestBuilder, never()).buildTaggedMediaRequest(anyString(), anyString(), anyString(), anyBoolean());
        verify(staticContentGrpcExecutor, never()).getTaggedMedia(any(), anyString());
    }

    @Test
    public void testProcessTaggedMedia_Exception() throws Exception {
        // Arrange
        when(grpcContentRequestBuilder.buildTaggedMediaRequest(anyString(), anyString(), anyString(), anyBoolean()))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        String result = grpcContentBusinessProcessor.processTaggedMedia(CORRELATION_KEY, VOYAGER_ID, MMT_HOTEL_ID, GI, true);

        // Assert
        Assert.isNull(result, "Result should be null when exception occurs");
        verify(metricAspect).addToTime(anyLong(), isNull(), eq(""), eq(""), isNull(), eq(""));
    }

    @Test
    public void testProcessMediaIdDetails_Success_WithMmtHotelId() throws Exception {
        // Arrange
        MediaIdRequest mockRequest = MediaIdRequest.getDefaultInstance();
        MediaIdResponse mockGrpcResponse = MediaIdResponse.getDefaultInstance();
        TaggedMediaByIdResponse mockTransformedResponse = new TaggedMediaByIdResponse();

        when(grpcContentRequestBuilder.buildMediaIdDetailsRequest(CORRELATION_KEY, MMT_HOTEL_ID, TAG_IDS, GI, OFFSET, LIMIT))
                .thenReturn(mockRequest);
        when(staticContentGrpcExecutor.getMediaId(mockRequest, ""))
                .thenReturn(mockGrpcResponse);
        when(grpcContentResponseBuilder.buildTaggedMediaByIdResponse(mockRequest, mockGrpcResponse))
                .thenReturn(mockTransformedResponse);
        when(objectMapperUtil.getJsonFromObjectWithView(mockTransformedResponse, DependencyLayer.HSC, TaggedMediaByIdResponse.class))
                .thenReturn(EXPECTED_JSON_RESPONSE);

        // Act
        String result = grpcContentBusinessProcessor.processMediaIdDetails(CORRELATION_KEY, VOYAGER_ID, MMT_HOTEL_ID, TAG_IDS, GI, OFFSET, LIMIT);

        // Assert
        Assert.notNull(result);
        Assert.isTrue(EXPECTED_JSON_RESPONSE.equals(result), "Response should match expected JSON");
        verify(grpcContentRequestBuilder).buildMediaIdDetailsRequest(CORRELATION_KEY, MMT_HOTEL_ID, TAG_IDS, GI, OFFSET, LIMIT);
        verify(staticContentGrpcExecutor).getMediaId(mockRequest, "");
        verify(grpcContentResponseBuilder).buildTaggedMediaByIdResponse(mockRequest, mockGrpcResponse);
        verify(objectMapperUtil).getJsonFromObjectWithView(mockTransformedResponse, DependencyLayer.HSC, TaggedMediaByIdResponse.class);
        verify(metricAspect).addToTime(anyLong(), anyString(), eq(""), eq(""), isNull(), eq(""));
    }

    @Test
    public void testProcessMediaIdDetails_Success_WithoutMmtHotelId() throws Exception {
        // Arrange
        HotelsDataV2Request mockHotelMappingRequest = HotelsDataV2Request.newBuilder()
                .addVoyIds(VOYAGER_ID)
                .build();
        HotelsDataV2Response mockHotelMappingResponse = createMockHotelMappingResponse(MMT_HOTEL_ID);
        MediaIdRequest mockRequest = MediaIdRequest.getDefaultInstance();
        MediaIdResponse mockGrpcResponse = MediaIdResponse.getDefaultInstance();
        TaggedMediaByIdResponse mockTransformedResponse = new TaggedMediaByIdResponse();

        when(grpcContentRequestBuilder.getHotelIdMappingRequest(VOYAGER_ID, CORRELATION_KEY))
                .thenReturn(mockHotelMappingRequest);
        when(staticContentGrpcExecutor.getHotelIdMapping(mockHotelMappingRequest, ""))
                .thenReturn(mockHotelMappingResponse);
        when(grpcContentRequestBuilder.buildMediaIdDetailsRequest(CORRELATION_KEY, MMT_HOTEL_ID, TAG_IDS, GI, OFFSET, LIMIT))
                .thenReturn(mockRequest);
        when(staticContentGrpcExecutor.getMediaId(mockRequest, ""))
                .thenReturn(mockGrpcResponse);
        when(grpcContentResponseBuilder.buildTaggedMediaByIdResponse(mockRequest, mockGrpcResponse))
                .thenReturn(mockTransformedResponse);
        when(objectMapperUtil.getJsonFromObjectWithView(mockTransformedResponse, DependencyLayer.HSC, TaggedMediaByIdResponse.class))
                .thenReturn(EXPECTED_JSON_RESPONSE);

        // Act
        String result = grpcContentBusinessProcessor.processMediaIdDetails(CORRELATION_KEY, VOYAGER_ID, null, TAG_IDS, GI, OFFSET, LIMIT);

        // Assert
        Assert.notNull(result);
        Assert.isTrue(EXPECTED_JSON_RESPONSE.equals(result), "Response should match expected JSON");
        verify(grpcContentRequestBuilder).getHotelIdMappingRequest(VOYAGER_ID, CORRELATION_KEY);
        verify(staticContentGrpcExecutor).getHotelIdMapping(mockHotelMappingRequest, "");
        verify(metricAspect).addToTime(anyLong(), anyString(), eq(""), eq(""), isNull(), eq(""));
    }

    @Test
    public void testProcessMediaIdDetails_Failure_NoHotelIdMapping() throws Exception {
        // Arrange
        HotelsDataV2Request mockHotelMappingRequest = HotelsDataV2Request.newBuilder()
                .addVoyIds(VOYAGER_ID)
                .build();
        HotelsDataV2Response mockHotelMappingResponse = HotelsDataV2Response.getDefaultInstance(); // Empty response

        when(grpcContentRequestBuilder.getHotelIdMappingRequest(VOYAGER_ID, CORRELATION_KEY))
                .thenReturn(mockHotelMappingRequest);
        when(staticContentGrpcExecutor.getHotelIdMapping(mockHotelMappingRequest, ""))
                .thenReturn(mockHotelMappingResponse);
        when(objectMapperUtil.getJsonFromObjectWithView(any(TaggedMediaByIdResponse.class), eq(DependencyLayer.HSC), eq(TaggedMediaByIdResponse.class)))
                .thenReturn("{\"success\":false}");

        // Act
        String result = grpcContentBusinessProcessor.processMediaIdDetails(CORRELATION_KEY, VOYAGER_ID, "", TAG_IDS, GI, OFFSET, LIMIT);

        // Assert
        Assert.notNull(result);
        Assert.isTrue(result.contains("false"), "Response should indicate failure");
        verify(grpcContentRequestBuilder, never()).buildMediaIdDetailsRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());
        verify(staticContentGrpcExecutor, never()).getMediaId(any(), anyString());
    }

    @Test
    public void testProcessMediaIdDetails_Exception() throws Exception {
        // Arrange
        when(grpcContentRequestBuilder.buildMediaIdDetailsRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        String result = grpcContentBusinessProcessor.processMediaIdDetails(CORRELATION_KEY, VOYAGER_ID, MMT_HOTEL_ID, TAG_IDS, GI, OFFSET, LIMIT);

        // Assert
        Assert.isNull(result, "Result should be null when exception occurs");
        verify(metricAspect).addToTime(anyLong(), isNull(), eq(""), eq(""), isNull(), eq(""));
    }

    @Test
    public void testProcessMediaIdDetails_NullResponse() throws Exception {
        // Arrange
        MediaIdRequest mockRequest = MediaIdRequest.getDefaultInstance();
        MediaIdResponse mockGrpcResponse = MediaIdResponse.getDefaultInstance();

        when(grpcContentRequestBuilder.buildMediaIdDetailsRequest(CORRELATION_KEY, MMT_HOTEL_ID, TAG_IDS, GI, OFFSET, LIMIT))
                .thenReturn(mockRequest);
        when(staticContentGrpcExecutor.getMediaId(mockRequest, ""))
                .thenReturn(mockGrpcResponse);
        when(grpcContentResponseBuilder.buildTaggedMediaByIdResponse(mockRequest, mockGrpcResponse))
                .thenReturn(null); // Null response

        // Act
        String result = grpcContentBusinessProcessor.processMediaIdDetails(CORRELATION_KEY, VOYAGER_ID, MMT_HOTEL_ID, TAG_IDS, GI, OFFSET, LIMIT);

        // Assert
        Assert.isNull(result, "Result should be null when response builder returns null");
        verify(objectMapperUtil, never()).getJsonFromObjectWithView(any(), any(), any());
        verify(metricAspect).addToTime(anyLong(), anyString(), eq(""), eq(""), isNull(), eq(""));
    }

    @Test
    public void testProcessTaggedMedia_NullResponse() throws Exception {
        // Arrange
        TaggedMediaRequest mockRequest = TaggedMediaRequest.getDefaultInstance();
        TaggedMediaResponse mockGrpcResponse = TaggedMediaResponse.getDefaultInstance();

        when(grpcContentRequestBuilder.buildTaggedMediaRequest(CORRELATION_KEY, MMT_HOTEL_ID, GI, true))
                .thenReturn(mockRequest);
        when(staticContentGrpcExecutor.getTaggedMedia(mockRequest, ""))
                .thenReturn(mockGrpcResponse);
        when(grpcContentResponseBuilder.buildTaggedMediaResponse(mockGrpcResponse))
                .thenReturn(null); // Null response

        // Act
        String result = grpcContentBusinessProcessor.processTaggedMedia(CORRELATION_KEY, VOYAGER_ID, MMT_HOTEL_ID, GI, true);

        // Assert
        Assert.isNull(result, "Result should be null when response builder returns null");
        verify(objectMapperUtil, never()).getJsonFromObjectWithView(any(), any(), any());
        verify(metricAspect).addToTime(anyLong(), anyString(), eq(""), eq(""), isNull(), eq(""));
    }

    // Helper method to create mock hotel mapping response
    private HotelsDataV2Response createMockHotelMappingResponse(String mmtHotelId) {
        HotelsDataV2Response.Builder responseBuilder = HotelsDataV2Response.newBuilder();
        HotelData.Builder hotelDataBuilder = HotelData.newBuilder();
        hotelDataBuilder.setMmtHtlId(mmtHotelId);
        
        Map<String, HotelData> responseMap = new HashMap<>();
        responseMap.put(VOYAGER_ID, hotelDataBuilder.build());
        responseBuilder.putAllResponse(responseMap);
        
        return responseBuilder.build();
    }
} 