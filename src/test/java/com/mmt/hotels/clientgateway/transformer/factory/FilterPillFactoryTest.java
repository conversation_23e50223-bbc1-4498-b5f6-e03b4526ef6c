package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterPillConfigurationWrapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.filter.FilterPill;
import com.mmt.hotels.clientgateway.response.filter.SortList;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.transformer.response.FilterPillConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.request.AccessPoint;
import org.mockito.Mock;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class FilterPillFactoryTest {

    @InjectMocks
    private FilterPillFactory filterPillFactory;

    private FilterPillConfig filterPillsB2C;
    private FilterPillConfig filterPillsIhB2C;
    private FilterPillConfig filterPillsGetaway;
    private FilterPillConfig filterPillsLuxe;
    private FilterPillConfig filterPillsHostel;
    private FilterPillConfig filterPillsDayUse;
    private SortList sortList;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void setup() {
        // Initialize test data
        filterPillsB2C = createTestFilterPillConfig();
        filterPillsIhB2C = createTestFilterPillConfig();
        filterPillsGetaway = createTestFilterPillConfig();
        filterPillsLuxe = createTestFilterPillConfig();
        filterPillsHostel = createTestFilterPillConfig();
        filterPillsDayUse = createTestFilterPillConfig();
        sortList = createTestSortList();

        // Set the test data using reflection
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsB2C", filterPillsB2C);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsIhB2C", filterPillsIhB2C);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsGetaway", filterPillsGetaway);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsLuxe", filterPillsLuxe);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsHostel", filterPillsHostel);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsDayUse", filterPillsDayUse);
        ReflectionTestUtils.setField(filterPillFactory, "sortList", sortList);
    }

    private FilterPillConfig createTestFilterPillConfig() {
        FilterPillConfig config = new FilterPillConfig();
        Map<String, FilterPill> filterPills = new HashMap<>();
        Map<String, Integer> pillSequence = new HashMap<>();

        // Create a test filter pill
        FilterPill filterPill = new FilterPill();
        List<String> categories = new ArrayList<>();
        categories.add("testCategory");
        filterPill.setCategories(categories);
        filterPills.put("testPill", filterPill);
        pillSequence.put("testPill", 1);

        config.setFilterPills(filterPills);
        config.setPillSequence(pillSequence);
        return config;
    }

    private SortList createTestSortList() {
        SortList sortList = new SortList();
        sortList.setTitle("Test Sort");
        List<SortCriteria> sortCriteria = new ArrayList<>();
        SortCriteria criteria = new SortCriteria();
        criteria.setTitle("Test Title");
        criteria.setPillText("Test Pill Text");
        criteria.setField("testField");
        criteria.setOrder("asc");
        sortCriteria.add(criteria);
        sortList.setSortCriteria(sortCriteria);
        return sortList;
    }

    @Test
    public void testGetFilterPillConfiguration_B2C() {
        FilterPillConfigurationWrapper wrapper = filterPillFactory.getFilterPillConfiguration("B2C", "CITY", true, new HashMap<>(),new LinkedHashMap<>());
        assertNotNull(wrapper);
        assertNotNull(wrapper.getFilterPillConfig());
        assertNotNull(wrapper.getSortList());
    }

    @Test
    public void testGetFilterPillConfiguration_Luxe() {
        FilterPillConfigurationWrapper wrapper = filterPillFactory.getFilterPillConfiguration("B2C", Constants.LUXE_LOCATION_TYPE, true, new HashMap<>(),new LinkedHashMap<>());
        assertNotNull(wrapper);
        assertNotNull(wrapper.getFilterPillConfig());
        assertNotNull(wrapper.getSortList());
    }

    @Test
    public void testGetFilterPillConfiguration_Getaway() {
        FilterPillConfigurationWrapper wrapper = filterPillFactory.getFilterPillConfiguration(Constants.FUNNEL_SOURCE_GETAWAY, "CITY", true, new HashMap<>(),new LinkedHashMap<>());
        assertNotNull(wrapper);
        assertNotNull(wrapper.getFilterPillConfig());
        assertNotNull(wrapper.getSortList());
    }

    @Test
    public void testGetFilterPillConfiguration_Hostel() {
        FilterPillConfigurationWrapper wrapper = filterPillFactory.getFilterPillConfiguration(Constants.FUNNEL_SOURCE_HOSTEL, "CITY", true, new HashMap<>(),new LinkedHashMap<>());
        assertNotNull(wrapper);
        assertNotNull(wrapper.getFilterPillConfig());
        assertNotNull(wrapper.getSortList());
    }

    @Test
    public void testGetFilterPillConfiguration_DayUse() {
        FilterPillConfigurationWrapper wrapper = filterPillFactory.getFilterPillConfiguration(Constants.FUNNEL_SOURCE_DAYUSE, "CITY", true, new HashMap<>(),new LinkedHashMap<>());
        assertNotNull(wrapper);
        assertNotNull(wrapper.getFilterPillConfig());
        assertNotNull(wrapper.getSortList());
    }

    @Test
    public void testGetFilterPillConfiguration_International() {
        FilterPillConfigurationWrapper wrapper = filterPillFactory.getFilterPillConfiguration("B2C", "CITY", false, new HashMap<>(),new LinkedHashMap<>());
        assertNotNull(wrapper);
        assertNotNull(wrapper.getFilterPillConfig());
        assertNotNull(wrapper.getSortList());
    }

    @Test
    public void testCopyFilterPillConfigProp() {
        FilterPillConfig original = createTestFilterPillConfig();
        FilterPillConfig copy = filterPillFactory.copyFilterPillConfigProp(original);
        
        assertNotNull(copy);
        assertNotNull(copy.getFilterPills());
        assertNotNull(copy.getPillSequence());
        assertEquals(original.getFilterPills().size(), copy.getFilterPills().size());
        assertEquals(original.getPillSequence().size(), copy.getPillSequence().size());
    }

    @Test
    public void testCopyFilterPillConfigProp_NullInput() {
        FilterPillConfig copy = filterPillFactory.copyFilterPillConfigProp(null);
        assertNull(copy);
    }

    @Test
    public void testGetSortCriteria_ValidAccessPoint() throws Exception {
        // Create test AccessPoint
        AccessPoint accessPoint = new AccessPoint();
        accessPoint.setAccessPointName("Test Airport");
        accessPoint.setPoi("12345");

        String title = "Distance from Test Airport";

        // Use reflection to access the private static method
        Method getSortCriteriaMethod = FilterPillFactory.class.getDeclaredMethod("getSortCriteria", AccessPoint.class, String.class);
        getSortCriteriaMethod.setAccessible(true);

        // Call the method
        SortCriteria result = (SortCriteria) getSortCriteriaMethod.invoke(null, accessPoint, title);

        // Assertions
        assertNotNull(result);
        assertEquals("asc", result.getOrder());
        assertEquals(title, result.getTitle());
        assertEquals(title, result.getPillText());
        assertTrue(result.isAccessPoint());
        assertEquals("accessPointDrivingDistanceSort|12345", result.getField());
    }

    @Test
    public void testGetSortCriteria_NullAccessPoint() throws Exception {
        String title = "Distance from Test Airport";

        // Use reflection to access the private static method
        Method getSortCriteriaMethod = FilterPillFactory.class.getDeclaredMethod("getSortCriteria", AccessPoint.class, String.class);
        getSortCriteriaMethod.setAccessible(true);

        // This should handle null gracefully or throw appropriate exception
        try {
            SortCriteria result = (SortCriteria) getSortCriteriaMethod.invoke(null, null, title);
            // If it doesn't throw exception, verify the result
            if (result != null) {
                assertEquals(title, result.getTitle());
                assertEquals(title, result.getPillText());
                assertEquals("asc", result.getOrder());
            }
        } catch (Exception e) {
            // Expected behavior for null input
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    @Test
    public void testGetSortCriteria_EmptyPoiId() throws Exception {
        // Create test AccessPoint with empty POI
        AccessPoint accessPoint = new AccessPoint();
        accessPoint.setAccessPointName("Test Airport");
        accessPoint.setPoi("");

        String title = "Distance from Test Airport";

        // Use reflection to access the private static method
        Method getSortCriteriaMethod = FilterPillFactory.class.getDeclaredMethod("getSortCriteria", AccessPoint.class, String.class);
        getSortCriteriaMethod.setAccessible(true);

        // Call the method
        SortCriteria result = (SortCriteria) getSortCriteriaMethod.invoke(null, accessPoint, title);

        // Assertions
        assertNotNull(result);
        assertEquals("asc", result.getOrder());
        assertEquals(title, result.getTitle());
        assertEquals(title, result.getPillText());
        assertTrue(result.isAccessPoint());
        assertEquals("accessPointDrivingDistanceSort|", result.getField());
    }
}