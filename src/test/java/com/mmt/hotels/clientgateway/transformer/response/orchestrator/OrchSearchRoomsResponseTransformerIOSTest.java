package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.RatePlanFilter;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchRoomsResponseTransformerIOSTest {

    @InjectMocks
    private OrchSearchRoomsResponseTransformerIOS transformer;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void setUp() {
        // Inject the mocked polyglotService into the transformer
        ReflectionTestUtils.setField(transformer, "polyglotService", polyglotService);
        
        // Setup common mock responses
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Top Rated");
    }

    // ======================
    // createTopRatedPersuasion() Tests
    // ======================

//    @Test
//    public void should_CallCreateTopRatedPersuasionForMobile_When_CreatingTopRatedPersuasion() {
//        // When
//        PersuasionObject result = ReflectionTestUtils.invokeMethod(transformer, "createTopRatedPersuasion");
//
//        // Then
//        assertNotNull("Should return a persuasion object", result);
//        assertEquals("Should use mobile template", "IMAGE_TEXT_H", result.getTemplate());
//        assertEquals("Should use correct placeholder", "SELECT_TOP_R1", result.getPlaceholder());
//        assertNotNull("Should have data list", result.getData());
//        assertTrue("Data should be a list", result.getData() instanceof List);
//    }
//
//    @Test
//    public void should_CreateMobilePersuasionWithCorrectStyling_When_CreatingTopRatedPersuasion() {
//        // When
//        PersuasionObject result = ReflectionTestUtils.invokeMethod(transformer, "createTopRatedPersuasion");
//
//        // Then
//        assertNotNull("Should return a persuasion object", result);
//        assertTrue("Should have data list", result.getData() instanceof List);
//
//        // Verify the method was called to get translated data
//        verify(polyglotService).getTranslatedData("TOP_RATED");
//
//        // The object structure should match mobile persuasion format
//        assertEquals("Should use mobile template", "IMAGE_TEXT_H", result.getTemplate());
//        assertEquals("Should use correct placeholder", "SELECT_TOP_R1", result.getPlaceholder());
//    }
//
//    @Test
//    public void should_HandlePolyglotServiceFailure_When_CreatingTopRatedPersuasion() {
//        // Given
//        when(polyglotService.getTranslatedData(anyString())).thenThrow(new RuntimeException("Service unavailable"));
//
//        // When/Then
//        try {
//            ReflectionTestUtils.invokeMethod(transformer, "createTopRatedPersuasion");
//        } catch (Exception e) {
//            assertTrue("Should propagate polyglot service exception", e.getCause() instanceof RuntimeException);
//        }
//    }

    // ======================
    // buildLoginPersuasion() Tests
    // ======================

    @Test
    public void should_ReturnNull_When_BuildingLoginPersuasion() {
        // When
        LoginPersuasion result = ReflectionTestUtils.invokeMethod(transformer, "buildLoginPersuasion");

        // Then
        assertNull("iOS should not show login persuasion", result);
    }

    // ======================
    // buildLoyaltyCashbackPersuasions() Tests
    // ======================

    @Test
    public void should_DoNothing_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        BestCoupon coupon = new BestCoupon();
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        persuasionMap.put("test", new PersuasionResponse());
        int initialSize = persuasionMap.size();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        assertEquals("Should not modify persuasion map", initialSize, persuasionMap.size());
        assertTrue("Should still contain original entry", persuasionMap.containsKey("test"));
    }

    @Test
    public void should_HandleNullCoupon_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        persuasionMap.put("test", new PersuasionResponse());
        int initialSize = persuasionMap.size();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", null, persuasionMap);

        // Then
        assertEquals("Should not modify persuasion map", initialSize, persuasionMap.size());
    }

    @Test
    public void should_HandleNullPersuasionMap_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        BestCoupon coupon = new BestCoupon();

        // When/Then - Should not throw exception
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, null);
    }

    @Test
    public void should_HandleEmptyPersuasionMap_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        BestCoupon coupon = new BestCoupon();
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        assertTrue("Should remain empty", persuasionMap.isEmpty());
    }

    // ======================
    // buildGroupFilterForDevice() Tests  
    // ======================

    @Test
    public void should_ReturnStaycationFilter_When_StaycationIsTrue() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = Arrays.asList(createFilter("STAYCATION_DEALS"));
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNotNull("Should return staycation filter when staycation is true", result);
        assertEquals("Should have correct text", "GetawayDeals", result.getText());
        assertNotNull("Should have rate plan filter list", result.getRatePlanFilterList());
        assertFalse("Should have filters in list", result.getRatePlanFilterList().isEmpty());
        
        // Verify staycation filter was added
        RatePlanFilter staycationRateFilter = result.getRatePlanFilterList().get(0);
        assertTrue("Should be selected", staycationRateFilter.isSelected());
    }

    @Test
    public void should_ReturnNull_When_StaycationIsFalse() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = new ArrayList<>();
        boolean staycation = false;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNull("Should return null when staycation is false", result);
    }

    @Test
    public void should_CreateUnselectedStaycationFilter_When_FilterCriteriaDoesNotContainStaycation() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = Arrays.asList(createFilter("SOME_OTHER_FILTER"));
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNotNull("Should return staycation filter", result);
        
        // Verify staycation filter was added but not selected
        RatePlanFilter staycationRateFilter = result.getRatePlanFilterList().get(0);
        assertFalse("Should not be selected", staycationRateFilter.isSelected());
    }

    @Test
    public void should_HandleNullFilterCriteria_When_BuildingStaycationFilter() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, null, staycation);

        // Then
        assertNotNull("Should return staycation filter", result);
        
        // Verify staycation filter was added but not selected (due to null filter criteria)
        RatePlanFilter staycationRateFilter = result.getRatePlanFilterList().get(0);
        assertFalse("Should not be selected when filter criteria is null", staycationRateFilter.isSelected());
    }

    @Test
    public void should_HandleEmptyFilterCriteria_When_BuildingStaycationFilter() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = new ArrayList<>();
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNotNull("Should return staycation filter", result);
        
        // Verify staycation filter was added but not selected
        RatePlanFilter staycationRateFilter = result.getRatePlanFilterList().get(0);
        assertFalse("Should not be selected when filter criteria is empty", staycationRateFilter.isSelected());
    }

    @Test 
    public void should_HandleCaseInsensitiveStaycationFilter_When_CheckingFilterCriteria() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = Arrays.asList(createFilter("staycation_deals")); // lowercase
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNotNull("Should return staycation filter", result);
        
        // Verify staycation filter was added and selected (case insensitive match)
        RatePlanFilter staycationRateFilter = result.getRatePlanFilterList().get(0);
        assertTrue("Should be selected with case insensitive match", staycationRateFilter.isSelected());
    }

    // ======================
    // iOS-Specific Behavior Tests
    // ======================

    @Test
    public void should_FollowIOSPattern_When_ComparingWithAndroidAndDesktop() {
        // Test iOS-specific behavior differences:
        
        // 1. Like Android, returns null for login persuasion
        LoginPersuasion loginPersuasion = ReflectionTestUtils.invokeMethod(transformer, "buildLoginPersuasion");
        assertNull("iOS should not show login persuasion like Android", loginPersuasion);
        
        // 2. Unlike Android and Desktop, does nothing for loyalty/cashback
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        BestCoupon coupon = new BestCoupon();
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);
        assertTrue("iOS should not add loyalty/cashback persuasions", persuasionMap.isEmpty());
        
        // 3. Unlike Desktop (null) but like Android, supports staycation filters
        Map<String, GroupRatePlanFilter> groupFilterMap = createMockGroupFilterMap();
        GroupRatePlanFilter groupFilter = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupFilterMap, new ArrayList<>(), true);
        assertNotNull("iOS should support staycation filters like Android", groupFilter);
        
        // 4. Uses mobile persuasion like Android
        PersuasionObject topRated = ReflectionTestUtils.invokeMethod(transformer, "createTopRatedPersuasion");
        assertEquals("iOS should use mobile template like Android", "IMAGE_TEXT_H", topRated.getTemplate());
    }

    // ======================
    // Helper Methods
    // ======================

    private Map<String, GroupRatePlanFilter> createMockGroupFilterMap() {
        Map<String, GroupRatePlanFilter> map = new HashMap<>();
        
        GroupRatePlanFilter getawayDealsFilter = new GroupRatePlanFilter();
        getawayDealsFilter.setText("GetawayDeals");
        getawayDealsFilter.setRatePlanFilterList(new ArrayList<>());
        
        map.put("GetawayDeals", getawayDealsFilter);
        return map;
    }

    private Filter createFilter(String filterValue) {
        Filter filter = new Filter();
        filter.setFilterValue(filterValue);
        return filter;
    }
} 