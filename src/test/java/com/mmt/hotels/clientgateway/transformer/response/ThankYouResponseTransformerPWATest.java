package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ThankYouResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.staticdata.StayDetail;
import com.mmt.hotels.model.response.txn.*;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class ThankYouResponseTransformerPWATest {

    @Spy
    private DateUtil dateUtil;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private ThankYouResponseTransformerPWA thankYouResponseTransformer;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void setup() {
        ReflectionTestUtils.setField(thankYouResponseTransformer, "maxInclusionsThankyou", 3);
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripsDeeplink", "mmyt://mytrips/hotelDetails");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankyouPaynowUrlGi", "https://www.goibibo.com/mudra/easypay");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "hotelDetailsRawDeepLink", "mmyt://htl/listing/?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&openDetail=true&currency={6}&checkAvailability=true");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripCardDetails", "{\"CONTACT_PROPERTY\":{\"text\":\"Contact Property\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"CANCEL_BOOKING\":{\"text\":\"Cancel Booking\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"ADD_MEAL\":{\"text\":\"Add Meal\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"DIRECTION_MAP\":{\"text\":\"Directions on Map\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"SPECIAL_REQUESTS\":{\"text\":\"Special Requests\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"ADD_GUEST\":{\"text\":\"Add Guest\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"DOWNLOAD_TICKET\":{\"text\":\"Download Ticket\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"MORE_MYTRIPS\":{\"text\":\"More in My Trips\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"CHAT_MYRA\":{\"text\":\"Chat with Myra\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"HOTEL_POLICY\":{\"text\":\"View Hotel Policy\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"}}");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripCardConditions", "{\"FC_AP_0\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"DIRECTION_MAP\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"FC_AP_X\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"NR_AP_0\":[\"CONTACT_PROPERTY\",\"DIRECTION_MAP\",\"CHAT_MYRA\",\"SPECIAL_REQUESTS\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"NR_AP_X\":[\"CONTACT_PROPERTY\",\"HOTEL_POLICY\",\"SPECIAL_REQUESTS\",\"CHAT_MYRA\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"ZP_FC_AP_0\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"DIRECTION_MAP\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"ZP_FC_AP_X\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"]}");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripCardIconUrls", "{\"CONTACT_PROPERTY\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/contactproperty.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/contactproperty.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/contactproperty.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/contactproperty.png\"},\"CANCEL_BOOKING\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/cancelbooking.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/cancelbooking.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/cancelbooking.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/cancelbooking.png\"},\"ADD_MEAL\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/addmeal.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/addmeal.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/addmeal.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/addmeal.png\"},\"DIRECTION_MAP\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/directionsonmap.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/directionsonmap.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/directionsonmap.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/directionsonmap.png\"},\"SPECIAL_REQUESTS\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/specialrequest.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/specialrequest.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/specialrequest.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/specialrequest.png\"},\"ADD_GUEST\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/addguest.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/addguest.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/addguest.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/addguest.png\"},\"DOWNLOAD_TICKET\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/downloadvoucher.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/downloadvoucher.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/downloadvoucher.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/downloadvoucher.png\"},\"MORE_MYTRIPS\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/moreactions.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/moreactions.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/moreactions.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/moreactions.png\"},\"CHAT_MYRA\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/myrachat.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/myrachat.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/myrachat.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/myrachat.png\"},\"HOTEL_POLICY\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/hotelpolicy.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/hotelpolicy.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/hotelpolicy.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/hotelpolicy.png\"}}");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "mealPlanMapPolyglot", new HashMap<>());
        List<String> groupBookingCardKeys = new ArrayList<>();
        groupBookingCardKeys.add("POST_BOOKING_CARD");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "groupBookingCardKeys", groupBookingCardKeys);
        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));
        MockitoAnnotations.initMocks(this);
        CommonConfig commonConfig = Mockito.mock(CommonConfig.class);
        Mockito.when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        this.thankYouResponseTransformer.init();
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
    }

    @Test
    public void test_convertThankYouResponse_Success() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/successTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("sample key", "sample value"));

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getTotalAmount());
        Assert.assertNotNull(thankYouResponse.getLogData());
        Assert.assertEquals(thankYouResponse.getLogData().get("wallet") ,"0.0");
        Assert.assertNotNull(thankYouResponse.getMetaChannelInfo());
        Assert.assertEquals((double) ((Map) thankYouResponse.getMetaChannelInfo()).get("tag_one"), 5.0, 0.0);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getInsuranceInfo());
    }

    @Test
    public void test_convertThankYouResponseMyPartner_Success() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/successTxnDataMyPartner.json")),
                PersistanceMultiRoomResponseEntity.class);
        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("sample key", "sample value"));

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getTotalAmount());
        Assert.assertNotNull(thankYouResponse.getLogData());
        Assert.assertEquals(thankYouResponse.getLogData().get("wallet") ,"0.0");
        Assert.assertNotNull(thankYouResponse.getMetaChannelInfo());
        Assert.assertEquals((double) ((Map) thankYouResponse.getMetaChannelInfo()).get("tag_one"), 5.0, 0.0);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getInsuranceInfo());
    }

    @Test
    public void test_convertThankYouResponse_Pending() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/pendingTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("sample key", "sample value"));

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo());
    }

    @Test
    public void test_convertThankYouResponse_Failed() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/failedTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setSpiderNextBookingDiscountMessage("test");
        Map<String,CardData> thankYouCards = new HashMap<>();
        thankYouCards.put("POST_BOOKING_CARD", new CardData());
        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", thankYouCards);
        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("sample key", "sample value"));
        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getFailedBookingCardInfo());
    }

    @Test
    public void test_convertThankYouResponse_CompletePayment() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/completePaymentTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);

        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("sample key", "sample value"));
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getCompletePaymentCard());

    }

    @Test
    public void test_convertThankYouResponseMyPartner_bookerInfo() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/successTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("sample key", "sample value"));

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, new HashMap<>(), new HashMap<>());
        Assert.assertNull(thankYouResponse.getBookingDetails().getBookerInfo().getMobileNum());
        Assert.assertNull(thankYouResponse.getBookingDetails().getBookerInfo().getEmailId());

        persistanceMultiRoomResponseEntity.getPersistedData().getTravelerInfoList().get(0).setMobileNumber("9858995899");
        persistanceMultiRoomResponseEntity.getPersistedData().getTravelerInfoList().get(0).setEmail("<EMAIL>");

        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity, null, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getBookerInfo().getEmailId());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getBookerInfo().getMobileNum());

        persistanceMultiRoomResponseEntity.getPersistedData().setTravelerInfoList(null);

        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, new HashMap<>(), new HashMap<>());
        Assert.assertNull(thankYouResponse.getBookingDetails().getBookerInfo().getEmailId());
        Assert.assertNull(thankYouResponse.getBookingDetails().getBookerInfo().getMobileNum());
    }

}
