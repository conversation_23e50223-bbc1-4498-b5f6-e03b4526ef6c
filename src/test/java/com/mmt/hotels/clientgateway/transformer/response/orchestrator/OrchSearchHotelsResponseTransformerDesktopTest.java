package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.content.CampaignPojo;
import com.gommt.hotels.orchestrator.model.response.content.IndiannessPersuasion;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
//import com.mmt.hotels.clientgateway.consul.PropertyTextConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.propertymanager.config.PropertyManager;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.SHORTSTAYS_FUNNEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PER_NIGHT_TITLE_TEXT;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerDesktopTest {

    @InjectMocks
    OrchSearchHotelsResponseTransformerDesktop orchSearchHotelsResponseTransformerDesktop;

    @Mock
    PropertyManager propManager;

    @Mock
    PropertyTextConfig propertyTextConfig;

    @Mock
    CommonConfig commonConfig;

    @Mock
    CommonConfigConsul commonConfigConsul;

//    @Mock
//    PropertyTextConfigConsul propertyTextConfigConsul;

    @Mock
    PolyglotHelper polyglotHelper;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Spy
    Utility utility;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Spy
    DateUtil dateUtil;

    @Mock
    PolyglotService polyglotService;

    @Spy
    CommonHelper commonHelper;

    @Mock
    MobConfigHelper mobConfigHelper;

    @Spy
    PersuasionUtil persuasionUtil;


    ObjectMapper mapper;

    @Before
    public void setUp() throws JsonParseException {
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "activeLanguages", "eng,hin,ara");
        ReflectionTestUtils.setField(commonResponseTransformer, "polyglotService", polyglotService);
        when(propManager.getProperty(eq("propertyTextConfig"), eq(PropertyTextConfig.class))).thenReturn(propertyTextConfig);
        when(propManager.getProperty(eq("commonConfig"), eq(CommonConfig.class))).thenReturn(commonConfig);
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(utility, "dateUtil", dateUtil);
        ReflectionTestUtils.setField(commonHelper, "polyglotService", polyglotService);

        String toolTipPersuasions = "{\"HOSTEL\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOSTEL\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"CENTRALLY_LOCATED\",\"GREAT_FOR_SOCIALIZING\",\"YOUTHFUL_CONNECT\"]},\"VILLA\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_VILLA\",\"data\":[\"VALUE_FOR_MONEY\",\"IDEAL_FOR_GROUP_STAY\",\"SUPER_SPACIOUS\",\"COMPLETE_PRIVACY\"]},\"HOMESTAY\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOMESTAY\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"HOMELY_COMFORTS\",\"BEST_LOCAL_GUIDANCE\",\"CENTRALLY_LOCATED\"]},\"COTTAGE\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_COTTAGE\",\"data\":[\"IDEAL_FOR_GROUP_STAY\",\"VALUE_FOR_MONEY\",\"SUPER_SPACIOUS\",\"ACCESS_TO_BEAUTIFUL_OUTDOOR_SPACES\"]},\"APARTMENT\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_APARTMENT\",\"data\":[\"VALUE_FOR_MONEY\",\"CENTRALLY_LOCATED\",\"COMPLETE_ACCESS_TO_PROPERTY\",\"SAVINGS_ON_LAUNDRY_AND_FOOD\"]}}";
        Map<String, Object> desktopToolTipPersuasionsMap = objectMapperUtil.getObjectFromJsonWithType(toolTipPersuasions, new TypeReference<Map<String, Object>>() {}, DependencyLayer.CLIENTGATEWAY);
        List<String> amenetiesWithUrl = Arrays.asList("kids_play_area,24_hour_room_service,airport_transfer,childcare_services,free_airport_transfer,gym,indoor_games,luggage_storage,outdoor_sports,safe,doctor_on_call,restaurant,spa,free_wi_fi,swimming_pool,room_service,living_room,washing_machine,power_backup,air_conditioning,kitchenette,free_parking,parking,conference,caretaker,bonfire,housekeeping,bar,property_disinfection,sanitizers_installed,staff_hygiene,thermal_screening,balcony_terrace,lawn,conference_room,yoga,golf,nearby_beach,private_beach,lounge,butler_services,boat_ride,atm,tours_and_treks,cafe,business_centre,vehicle_rentals,jungle_safari,elevator_lift,hot_spring_bath,hammam,open_air_bath,plunge_pool,canoeing,free_shuttle_service,beach,night_club,casino,cooking_class,jacuzzi,free_drinking_water,steam_and_sauna,laundromat,kids_club".split(","));

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "basicDetailDeeplink", "https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "rootLevelSharingUrl", "https://app.mmyt.co/Xm2V/hotelListingShare?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "rootLevelDeeplink", "https://www.makemytrip.com/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "amenitiesIconUrls", new HashMap<>());
        Mockito.when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM)).thenReturn("Total <b>₹{AMOUNT}</b> for {NIGHT_COUNT} Night, {ROOM_COUNT} Room");
    }


    @Test
    public void init() throws Exception {

        when(propManager.getProperty("propertyTextConfig", PropertyTextConfig.class)).thenReturn(propertyTextConfig);
        when(propertyTextConfig.mySafetyTooltipKeys()).thenReturn(new MySafetyTooltip());
        when(propertyTextConfig.amenetiesWithUrl()).thenReturn(new ArrayList<>());
        when(propertyTextConfig.amenitiesIconUrls()).thenReturn(new HashMap<>());
        when(propManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        when(commonConfig.missingSlotDetails()).thenReturn(new MissingSlotDetail());
        when(commonConfig.thresholdForSlashedAndDefaultHourPrice()).thenReturn(10);

        orchSearchHotelsResponseTransformerDesktop.init();

        Field mySafetyDataTooltipsField = OrchSearchHotelsResponseTransformerDesktop.class.getDeclaredField("mySafetyDataTooltips");
        mySafetyDataTooltipsField.setAccessible(true);
        MySafetyTooltip mySafetyDataTooltips = (MySafetyTooltip) mySafetyDataTooltipsField.get(orchSearchHotelsResponseTransformerDesktop);

        assertNotNull(mySafetyDataTooltips);
        assertNotNull(orchSearchHotelsResponseTransformerDesktop.amenetiesWithUrl);
        assertNotNull(orchSearchHotelsResponseTransformerDesktop.missingSlotDetails);

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "toolTipPersuasions", "{\"HOSTEL\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOSTEL\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"CENTRALLY_LOCATED\",\"GREAT_FOR_SOCIALIZING\",\"YOUTHFUL_CONNECT\"]},\"VILLA\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_VILLA\",\"data\":[\"VALUE_FOR_MONEY\",\"IDEAL_FOR_GROUP_STAY\",\"SUPER_SPACIOUS\",\"COMPLETE_PRIVACY\"]},\"HOMESTAY\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOMESTAY\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"HOMELY_COMFORTS\",\"BEST_LOCAL_GUIDANCE\",\"CENTRALLY_LOCATED\"]},\"COTTAGE\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_COTTAGE\",\"data\":[\"IDEAL_FOR_GROUP_STAY\",\"VALUE_FOR_MONEY\",\"SUPER_SPACIOUS\",\"ACCESS_TO_BEAUTIFUL_OUTDOOR_SPACES\"]},\"APARTMENT\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_APARTMENT\",\"data\":[\"VALUE_FOR_MONEY\",\"CENTRALLY_LOCATED\",\"COMPLETE_ACCESS_TO_PROPERTY\",\"SAVINGS_ON_LAUNDRY_AND_FOOD\"]}}");
        orchSearchHotelsResponseTransformerDesktop.init();


    }

    @Test
    public void convertSearchHotelsResponseTest() throws JsonProcessingException {
        String listingResponseJson = "{\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"RECENTLY_VIEWED_HOTELS\",\"heading\":\"Recently Viewed\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201106120942397721\",\"name\":\"The Ashok\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201106120942397721&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/the_ashok-details-delhi.html\",\"stayType\":\"Entire\",\"threeSixtyViewIconUrl\":\"https://promos.makemytrip.com/Hotels_product/Listing/3603x.png\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Festive Weekend Deals\",\"PREMIND\",\"Deals for Vaccinated Travellers\",\"Child Friendly\",\"Inside BD\",\"Inmarket\",\"Hills\",\"Central Heating\",\"Great Value Packages\",\"kids_stay_free\",\"Premium Properties\",\"Last Minute Deals\",\"premium_hotels\"],\"facilityHighlights\":[\"Spa\",\"Restaurant\",\"Bar\"],\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"media\":{\"images\":[{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-afe9941a2d1811eea3cd0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-3ce394a835be11ee982f0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-21bae8ca35be11eeb7ae0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":3738,\"totalRatingCount\":7157,\"subRatings\":[{\"name\":\"Location\",\"rating\":4.5,\"reviewCount\":851,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":2096,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.8,\"reviewCount\":1106,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"The Ashok\",\"code\":\"201106120942397721\",\"ratePlans\":[{\"code\":\"1618459331976956240\",\"inclusions\":[{\"category\":\"Free Breakfast\",\"name\":\"Complimentary  Breakfast is available.\"}],\"price\":{\"basePrice\":14999.0,\"displayPrice\":12555.0,\"totalTax\":3375.0,\"savingPerc\":0.0,\"couponCode\":\"MMTBESTBUY\",\"couponDiscount\":944.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"MMTBESTBUY\",\"discount\":944.0,\"specialPromoCoupon\":false,\"type\":\"Get INR 944  Off\"}],\"taxBreakUp\":{\"hotelTax\":2430.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":945.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"Free Cancellation (100% refund) if you cancel this booking before 2024-10-24 13:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-24 14:00:00 (destination time) till 2024-10-26 13:59:59 (destination time) - 100% of booking amount.After 2024-10-26 14:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":\"2024-10-24 13:59:59\",\"penaltyValue\":\"FREE_CANCELLATION\",\"penaltyType\":\"F\"}]},\"mealPlans\":[{\"code\":\"CP\",\"value\":\"Breakfast\"}]}]}],\"altAcco\":false}]}],\"lastHotelId\":\"201512161105316614\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"sortCriteria\":{\"field\":\"S_hsq610_dspers_v2_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_hsq610_dspers_v2_LC_Per\"}}";
        ListingResponse listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setMetaInfo(true);
        searchHotelsRequest.getFeatureFlags().setPersuasionSuppression(true);
        CommonModifierResponse commonModifierResponse = getCommonModifierResponse();
        SearchHotelsResponse searchHotelsResponse = orchSearchHotelsResponseTransformerDesktop.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);


        listingResponseJson = "{\"requestId\":\"bca1d743-ea59-4539-a515-0271a43f5284\",\"journeyId\":\"82909517392a9898b-5da3-4458-b986-2741f54ac819\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"NOT_MYBIZ_ASSURED_SHOWN\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201211081220304096\",\"name\":\"Park Plaza, Shahdara\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201211081220304096&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/park_plaza_shahdara-details-delhi.html\",\"stayType\":\"Entire\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Chain\",\"HighFiveV2_Chain\",\"MMTFest\",\"Couple Friendly\",\"Premium PropertiesFlyer Deal\",\"Premium\",\"Workation\",\"MyBiz_Assured\",\"Daily Dhamaka\"],\"facilityHighlights\":[\"Spa\",\"Swimming Pool\",\"Gym\",\"Restaurant\",\"Steam and Sauna\"],\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"media\":{\"images\":[{\"url\":\"http://r1imghtlak.mmtcdn.com/82090bda780511e7bf27025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r1imghtlak.mmtcdn.com/84a383ca780511e78bf8025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":2083,\"totalRatingCount\":4118,\"subRatings\":[{\"name\":\"Location\",\"rating\":3.8,\"reviewCount\":1385,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":1017,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.9,\"reviewCount\":1045,\"show\":true},{\"name\":\"Food\",\"rating\":3.7,\"reviewCount\":836,\"show\":true},{\"name\":\"Room\",\"rating\":4.0,\"reviewCount\":837,\"show\":true},{\"name\":\"Cleanliness\",\"rating\":4.1,\"reviewCount\":2290,\"show\":true},{\"name\":\"Value For Money\",\"rating\":3.9,\"reviewCount\":778,\"show\":true},{\"name\":\"Child Friendliness\",\"rating\":3.5,\"reviewCount\":46,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"Park Plaza, Shahdara\",\"code\":\"201211081220304096\",\"ratePlans\":[{\"code\":\"3028728164758747328\",\"price\":{\"basePrice\":5000.0,\"displayPrice\":4500.0,\"totalTax\":540.0,\"savingPerc\":0.0,\"couponCode\":\"DHCASHBACK\",\"couponDiscount\":225.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"DHCASHBACK\",\"description\":\"Get  INR 225 Cashback to Card on payments via credit/debit cards\",\"discount\":225.0,\"specialPromoCoupon\":true,\"type\":\"Cashback to Card\"}],\"taxBreakUp\":{\"hotelTax\":540.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":0.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"This is a Non-refundable and non-amendable tariff. Cancellations, or no-shows will be subject to a hotel fee equal to the 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":null,\"penaltyValue\":\"NON_REFUNDABLE\",\"penaltyType\":null}]},\"mealPlans\":[{\"code\":\"EP\",\"value\":\"Room Only\"}]}]}],\"calendarCriteria\":{},\"soldOutInfo\":{},\"altAcco\":true}]}],\"lastHotelId\":\"201512151820116224\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"sortCriteria\":{\"field\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\"}}";
        listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        searchHotelsRequest.getRequestDetails().setFunnelSource("GROUP");
        searchHotelsResponse = orchSearchHotelsResponseTransformerDesktop.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);

        listingResponse.setLastFetchedHotelCategory(SectionsType.FILTER_REMOVAL.name());
        searchHotelsRequest.setFilterCriteria(new ArrayList<>());
        Filter filter = new Filter();
        filter.setFilterValue("5");
        filter.setFilterGroup(FilterGroup.STAR_RATING);
        searchHotelsRequest.getFilterCriteria().add(filter);
        searchHotelsRequest.getRequestDetails().setFunnelSource(SHORTSTAYS_FUNNEL);
        searchHotelsResponse = orchSearchHotelsResponseTransformerDesktop.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);

    }


    @Test
    public  void addPersuasionHoverDataTest() throws JsonParseException, JSONException {
        Map<String, Object> desktopToolTipPersuasionsMap = new HashMap<>();
        //desktopToolTipPersuasionsMap.put(TOOL_TIP_VILLA_STAY, "");
        HotelDetails hotelEntity = new HotelDetails();
        CancellationTimeline cancellationTimeline = new CancellationTimeline();
        ListingSearchRequest listingSearchRequest = new ListingSearchRequest();
        listingSearchRequest.setSearchCriteria(new SearchHotelsCriteria());
        listingSearchRequest.getSearchCriteria().setCountryCode("IN");
        listingSearchRequest.setRequestDetails(new RequestDetails());
        listingSearchRequest.getRequestDetails().setPageContext("LISTING");
        JSONObject hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_LUXE\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"BNPL_AVAIL\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"FCZPN\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"FREE_CANCELLATION\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"SAFETY\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"VILLA\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"APARTMENT\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"HOSTEL\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"HOSTEL\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"COTTAGE\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"HOMESTAY\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_MMT_VALUE_STAY\"}");
        hotelEntity.setBudgetHotel(true);
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_MYBIZ_ASSURED\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_LUXE\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_INDIANNESS\"}");
        IndiannessPersuasion indiannessPersuasion = new IndiannessPersuasion();
        List<CampaignPojo> campaignPojoList = new ArrayList<>();
        CampaignPojo campaignPojo = new CampaignPojo();
        campaignPojo.setHeading("Heading");
        campaignPojo.setIconUrl("IconUrl");
        campaignPojoList.add(campaignPojo);
        indiannessPersuasion.setScore(3);
        indiannessPersuasion.setShortSummary(campaignPojoList);
        hotelEntity.setIndianessPersuasion(indiannessPersuasion);
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity);
    }


    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() throws JsonProcessingException {
        Hotel hotelEntity = new Hotel();
        List<String> locationPersuasions = Collections.singletonList("Chanakyapuri");
        LinkedHashSet<String> facilities = new LinkedHashSet<>(Arrays.asList("Spa", "Restaurant", "Bar"));
        TransportPoi nearestGroundTransportPoi = new TransportPoi();
        LocationDetails locusData = new LocationDetails();
        locusData.setCityName("Delhi");
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setPageContext("LISTING");
        String dayUsePersuasionsText = "Day Use";
        String drivingTimeText = "10.0 km drive to T1 - Delhi Airport (IGI Airport)";
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);

        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        //Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);

        locationPersuasions = Arrays.asList("Chanakyapuri", "10.0 km drive to T1 - Delhi Airport (IGI Airport)", "Delhi");
        hotelEntity.setHotelPersuasions(null);
        searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        //Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 3);

        locationPersuasions = Arrays.asList("Chanakyapuri", "10.0 km drive to T1 - Delhi Airport (IGI Airport)", "Delhi", "Test");
        hotelEntity.setHotelPersuasions(null);
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);

        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        hotelEntity.setHotelPersuasions(null);
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        MDC.clear();
    }

    @Test
    public void buildBGColorTest() {
        orchSearchHotelsResponseTransformerDesktop.buildBGColor(null, null, null);
    }

    @Test
    public void addBookingConfirmationPersuasionTest() {
        orchSearchHotelsResponseTransformerDesktop.addBookingConfirmationPersuasion(null);
    }

    @Test
    public void buildStaticCardTest() {
        orchSearchHotelsResponseTransformerDesktop.buildStaticCard(null, null);
    }


    private CommonModifierResponse getCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>(Arrays.asList("r1", "r2", "r3")));
        return commonModifierResponse;
    }

    private SearchHotelsRequest createSearchHotelsRequest() throws JsonProcessingException {


        String requestString = "{\"correlationKey\":null,\"brand\":null,\"client\":null,\"blackInfo\":null,\"deviceDetails\":{\"appVersion\":\"128.0.0.0\",\"deviceId\":\"d7bb97cf-762b-4484-91fc-a224c03cdc96\",\"deviceType\":\"DESKTOP\",\"bookingDevice\":\"DESKTOP\",\"networkType\":\"WiFi\",\"deviceName\":null,\"appVersionIntGi\":null,\"simSerialNo\":null},\"lastProductId\":null,\"limit\":null,\"requestDetails\":{\"visitorId\":\"d23c479b373ee283\",\"visitNumber\":1,\"trafficSource\":null,\"srCon\":null,\"srCty\":null,\"srcState\":null,\"srLat\":null,\"srLng\":null,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"notifCoupon\":null,\"callBackType\":null,\"pushDataToCallToBookQ\":null,\"pushDataToListAllPropQ\":null,\"payMode\":null,\"loggedIn\":true,\"couponCount\":10,\"siteDomain\":\"in\",\"channel\":\"B2Cweb\",\"pageContext\":\"LISTING\",\"firstTimeUserState\":0,\"uuid\":null,\"corpAuthCode\":null,\"corpUserId\":null,\"seoCorp\":false,\"requestor\":null,\"wishCode\":null,\"preApprovedValidity\":null,\"metaInfo\":false,\"zcp\":null,\"requisitionID\":null,\"myBizFlowIdentifier\":null,\"brand\":\"MMT\",\"previousTxnKey\":null,\"oldWorkflowId\":null,\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"sessionId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"promoConsent\":false,\"flyerInfo\":null,\"premium\":false,\"semanticSearchDetails\":null,\"forwardBookingFlow\":false,\"extendedPackageCall\":false,\"isIgnoreSEO\":false,\"isRequestCallBack\":false,\"isListAllPropCall\":false},\"detailDeepLinkUrl\":null,\"sortCriteria\":null,\"filterCriteria\":[],\"appliedBatchKeys\":[],\"filterGroupsToRemove\":null,\"filtersToRemove\":null,\"featureFlags\":{\"staticData\":true,\"reviewSummaryRequired\":true,\"walletRequired\":true,\"shortlistingRequired\":false,\"noOfCoupons\":0,\"noOfAddons\":0,\"noOfPersuasions\":0,\"noOfSoldouts\":0,\"coupon\":true,\"mmtPrime\":false,\"persuationSeg\":null,\"persuasionsEngineHit\":true,\"checkAvailability\":true,\"liteResponse\":false,\"applyAbsorption\":false,\"bestOffersLimit\":0,\"dealOfTheDayRequired\":false,\"addOnRequired\":false,\"roomInfoRequired\":false,\"allInclusions\":false,\"hotelCatAndPropNotRequiredInMeta\":false,\"extraAltAccoRequired\":false,\"limitedFilterCall\":false,\"corpMMRRequired\":false,\"unmodifiedAmenities\":false,\"poisRequiredOnMap\":true,\"persuasionsRequired\":true,\"similarHotel\":false,\"locus\":false,\"comparator\":false,\"originListingMap\":false,\"mostBooked\":false,\"detailMap\":false,\"showUpsell\":false,\"filterRanking\":false,\"quickReview\":false,\"dayUsePersuasion\":false,\"selectiveHotels\":false,\"persuasionSuppression\":false,\"hidePrice\":false,\"showBnplCard\":false,\"modifyBooking\":false,\"cardRequired\":false,\"topCard\":false,\"filters\":false,\"seoDS\":false,\"seoCohort\":null,\"roomPreferenceEnabled\":false,\"flashDealClaimed\":false,\"upsellRateplanRequired\":false,\"orientation\":null,\"elitePackageEnabled\":false,\"premiumThemesCardRequired\":false,\"isGoTribe3_0\":null},\"matchMakerDetails\":{},\"imageDetails\":{\"types\":[\"professional\"],\"categories\":[{\"type\":\"H\",\"count\":1,\"height\":162,\"width\":243,\"imageFormat\":\"webp\"}]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]},\"expData\":\"{\\\"EMIDT\\\":2,\\\"UGCV2\\\":\\\"T\\\",\\\"HFC\\\":\\\"F\\\",\\\"VIDEO\\\":0,\\\"APT\\\":\\\"T\\\",\\\"CHPC\\\":\\\"T\\\",\\\"LSTNRBY\\\":\\\"T\\\",\\\"AARI\\\":\\\"T\\\",\\\"RCPN\\\":\\\"T\\\",\\\"MRS\\\":\\\"T\\\",\\\"ADDON\\\":\\\"T\\\",\\\"NLP\\\":\\\"Y\\\",\\\"PERNEW\\\":\\\"T\\\",\\\"GRPN\\\":\\\"T\\\",\\\"BNPL\\\":\\\"T\\\",\\\"MCUR\\\":\\\"T\\\",\\\"HAFC\\\":\\\"T\\\",\\\"PLRS\\\":\\\"T\\\",\\\"MMRVER\\\":\\\"V3\\\",\\\"PDO\\\":\\\"PN\\\",\\\"BLACK\\\":\\\"T\\\",\\\"CV2\\\":\\\"T\\\",\\\"FLTRPRCBKT\\\":\\\"T\\\",\\\"RTBC\\\":\\\"T\\\",\\\"MLOS\\\":\\\"T\\\",\\\"WPAH\\\":\\\"F\\\",\\\"AIP\\\":\\\"T\\\",\\\"BNPL0\\\":\\\"T\\\",\\\"HIS\\\":\\\"DEFAULT\\\",\\\"APE\\\":10,\\\"PAH\\\":5,\\\"IAO\\\":\\\"F\\\",\\\"CRF\\\":\\\"B\\\",\\\"ALC\\\":\\\"T\\\",\\\"SOU\\\":\\\"T\\\",\\\"PAH5\\\":\\\"T\\\",\\\"rearch\\\":\\\"True\\\"}\",\"expVariantKeys\":null,\"cohertVar\":null,\"multiCityFilter\":null,\"additionalProperties\":null,\"cardId\":null,\"manthanExpDataMap\":null,\"expDataMap\":null,\"contentExpDataMap\":null,\"userLocation\":null,\"clusterId\":null,\"orgId\":null,\"validExpList\":null,\"variantKeys\":null,\"businessIdentificationEnableFromUserService\":false,\"selectedTabId\":null,\"searchCriteria\":{\"checkIn\":\"2024-10-26\",\"checkOut\":\"2024-10-27\",\"countryCode\":\"IN\",\"cityCode\":\"ZNSHIM\",\"cityName\":null,\"locationId\":\"CTDEL\",\"locationType\":\"znshim\",\"lat\":null,\"lng\":null,\"currency\":\"INR\",\"personalCorpBooking\":false,\"rmDHS\":false,\"boostProperty\":null,\"baseRateplanCode\":null,\"selectedRatePlan\":null,\"multiCurrencyInfo\":null,\"preAppliedFilter\":false,\"roomStayCandidates\":[{\"rooms\":1,\"adultCount\":2,\"childAges\":[]}],\"parentLocationId\":null,\"parentLocationType\":null,\"tripType\":null,\"slot\":null,\"giHotelId\":null,\"hotelIds\":null,\"limit\":1,\"lastHotelId\":null,\"lastFetchedWindowInfo\":null,\"lastHotelCategory\":null,\"personalizedSearch\":true,\"nearBySearch\":false,\"wishListedSearch\":false,\"totalHotelsShown\":null,\"sectionsType\":null,\"collectionCriteria\":null,\"bookingForGuest\":false,\"travellerEmailID\":null,\"vcId\":null},\"lastPeekedOnMapHotelIds\":null,\"mapDetails\":null,\"nearbyFilter\":null,\"filterRemovedCriteria\":null}";
        return mapper.readValue(requestString, SearchHotelsRequest.class);
    }
}