package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.PersonalizedSection;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.persuasion.HotelPersuasions;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.HotelPrice;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.staticdata.Address;
import com.mmt.hotels.model.response.staticdata.FacilityCategorization;
import com.mmt.hotels.model.response.staticdata.POITag;
import com.mmt.hotels.model.response.staticdata.VideoInfo;
import com.mmt.hotels.pojo.response.detail.FeaturedAmenity;
import junit.framework.Assert;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsResponseTransformerPWATest {
	
	@InjectMocks
	SearchHotelsResponseTransformerPWA searchHotelsResponseTransformerPWA;

	@Spy
	CommonResponseTransformer commonResponseTransformer;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	private DateUtil dateUtil;

	@Mock
	private MobConfigHelper mobConfigHelper;

	@Mock
	PolyglotService polyglotService;

	Hotel hotel;

	@Mock
	MetricAspect metricAspect;

	@Mock
	PersuasionUtil persuasionUtil;

	private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

	@Before
	public void init() throws IOException {
			ObjectMapper mapper = new ObjectMapper();
			mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
			hotel = mapper.readValue(availPriceRequest, Hotel.class);
			Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
			MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),"eng");

			MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
			myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
			myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
			myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
			ReflectionTestUtils.setField(searchHotelsResponseTransformerPWA,"myBizStaticCard",myBizStaticCard);
	}

	@Test
	public void testConvertSearchHotelsResponse_FromSampleResponse() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/searchHotelsHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);

		CommonModifierResponse commonModifierResponsep = new CommonModifierResponse();
		LinkedHashMap<String, String> expData = new LinkedHashMap<>();
		expData.put("testKey", "testval");
		commonModifierResponsep.setExpDataMap(expData);
		SearchHotelsResponse response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,new SearchHotelsRequest(),commonModifierResponsep);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertTrue(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));
		Assert.assertNotNull(response.getSharingUrl());
		Assert.assertNotNull(response.getExpData());
	}

	private boolean compareSections(List<PersonalizedSection> cgSections, List<PersonalizedResponse<SearchWrapperHotelEntity>> webApiSections){
		if (CollectionUtils.isEmpty(webApiSections) && CollectionUtils.isEmpty(cgSections))
			return true;
		boolean isEquals = true;
		for (PersonalizedResponse<SearchWrapperHotelEntity> section : webApiSections) {
			Optional<PersonalizedSection> optionalSection = cgSections.stream().filter( c -> c.getName().equalsIgnoreCase(section.getSection())).findAny();
			if (!optionalSection.isPresent() || !compareSectionNodes(optionalSection.get(),section)) {
				isEquals = false;
				break;
			}
		}
		return isEquals;
	}

	private boolean compareSectionNodes(PersonalizedSection cgSection,PersonalizedResponse<SearchWrapperHotelEntity> webApiSection) {
		if (StringUtils.isNotBlank(webApiSection.getSection()) && !webApiSection.getSection().equalsIgnoreCase(cgSection.getName()))
			return false;
		if (StringUtils.isNotBlank(webApiSection.getHeading()) && !webApiSection.getHeading().equalsIgnoreCase(cgSection.getHeading()))
			return false;
		if (StringUtils.isNotBlank(webApiSection.getSubHeading()) && !webApiSection.getSubHeading().equalsIgnoreCase(cgSection.getSubHeading()))
			return false;
		if (CollectionUtils.isNotEmpty(webApiSection.getHotels()) && CollectionUtils.isEmpty(cgSection.getHotels()))
			return false;
		if (CollectionUtils.isNotEmpty(cgSection.getHotels()) && cgSection.getMinItemsToShow()!=null && cgSection.getMinItemsToShow() < cgSection.getHotels().size() && !cgSection.isShowMore())
			return false;
		if (CollectionUtils.isEmpty(cgSection.getHotels()) && CollectionUtils.isEmpty(webApiSection.getHotels()))
			return true;

		boolean isEquals = true;
		for (SearchWrapperHotelEntity hotelEntity : webApiSection.getHotels()) {
			Optional<Hotel> optionalHotel = cgSection.getHotels().stream().filter(c -> c.getId().equalsIgnoreCase(hotelEntity.getId())).findAny();
			if (!optionalHotel.isPresent() || !compareHotel(optionalHotel.get(),hotelEntity)) {
				isEquals = false;
				break;
			}
		}
		return isEquals;
	}

	private boolean compareHotel(Hotel cgHotel,SearchWrapperHotelEntity webApiHotel) {
		if (webApiHotel.getIsSoldOut()!=null && cgHotel.isSoldOut()!=webApiHotel.getIsSoldOut())
			return false;
		if (webApiHotel.getDisplayFare()!=null && webApiHotel.getDisplayFare().getDisplayPriceBreakDown()!=null) {
			DisplayPriceBreakDown dpbd = webApiHotel.getDisplayFare().getDisplayPriceBreakDown();
			if (cgHotel.getPriceDetail()==null)
				return false;
			PriceDetail pd = cgHotel.getPriceDetail();
			if (pd.getBasePrice()!=null || pd.getCdfDiscount()!=null || pd.getDiscount()!=null || pd.getDisplayPriceAltCurrency()!=null || pd.getEffectivePrice()!=null)
				return false;
			if (pd.getWallet()!=null || pd.getMmtDiscount()!=null || pd.getHotelTax()!=null)
				return false;
			if (StringUtils.isNotBlank(dpbd.getPricingKey()) && !dpbd.getPricingKey().equalsIgnoreCase(pd.getPricingKey()))
				return false;
		}
		return true;
	}

	@Test
	public void testConvertSearchHotelsResponse_FromCorpSampleResponse() throws IOException {

		Map<String,Integer> map = new HashMap<>();
		map.put("BOOKED_BY_COMPANY",5);
		map.put("LAST_BOOKED_HOTELS",5);
		map.put("MYBIZ_RECOMMENDED_HOTELS",5);
		map.put("PREFERRED_BY_COMPANY",5);
		map.put("RECENTLY_VIEWED_HOTELS",5);
		Mockito.when(mobConfigHelper.getCorpSectionListCount()).thenReturn(map);

		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);

		Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("sample value");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		SearchHotelsResponse response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,searchHotelsRequest,null);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertFalse(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));

		/* Test MyBiz ToolTip */
		Assert.assertTrue(response.getPersonalizedSections().stream().anyMatch(
				personalizedSection -> (Constants.MY_BIZ_ASSURED_SECTION).equalsIgnoreCase(personalizedSection.getName())
															&& personalizedSection.getToolTip() != null));
		Assert.assertNotNull(response.getSharingUrl());
	}
	
	@Test
	public void testConvertSearchHotelsResponse() throws JsonProcessingException {
		ListingPagePersonalizationResponsBO listingResponse = new ListingPagePersonalizationResponsBO();
		listingResponse.setPersonalizedResponse(new ArrayList<>());
		listingResponse.getPersonalizedResponse().add(new PersonalizedResponse<>());
		listingResponse.getPersonalizedResponse().get(0).setHotels(new ArrayList<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().add(buildSearchWrapperHotelEntity());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setMainImages(Collections.singletonList("adc"));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setDisplayFare(new DisplayFare());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getDisplayFare().setDisplayPriceBreakDown(buildDisplayPriceBreakDown());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setAddress(new Address());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getAddress().setArea(Collections.singletonList("area"));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setGeoLocation(new GeoLocation());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setFacilityCategorization(Collections.singletonList(new FacilityCategorization()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setPoiTagList(Collections.singletonList(new POITag()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setAddOns(Collections.singletonList(new AddOnNode()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setSegments(new Segments());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getSegments().setSegmentList(new HashMap<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getSegments().getSegmentList().put("test", new Segment());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setPersuasions(new HashSet<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getPersuasions().add(new HotelPersuasions());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setHotelVideos(Collections.singletonList(new VideoInfo()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getHotelVideos().get(0).setUrl("video_url");
		listingResponse.getPersonalizedResponse().get(0).setCategoryPersuasions(Collections.singletonList(new CategoryPersuasion()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setFlyfishReviewSummary(new HashMap<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setQuickBookInfo(new QuickBookInfo());
		MyBizSimilarToDirectObj myBizSimilarToDirectObj = new MyBizSimilarToDirectObj();
		myBizSimilarToDirectObj.setAmenities(Arrays.asList(new FeaturedAmenity()));
		myBizSimilarToDirectObj.setDistance("test");
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setMyBizSimilarToDirectObj(myBizSimilarToDirectObj);

		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setTrafficSource(new TrafficSource());
		requestDetails.getTrafficSource().setSource("SEO");
		requestDetails.setMetaInfo(true);
		searchHotelsRequest.setRequestDetails(requestDetails);
		searchHotelsRequest.setExpData("anc");
		searchHotelsRequest.setSearchCriteria(buildSearchCriteria());

		String ratingJson = "{\n" +
				"  \"cumulativeRating\": 4.5,\n" +
				"  \"totalReviewsCount\": 89,\n" +
				"  \"totalRatingCount\": 162,\n" +
				"  \"best\": [\n" +
				"    {\n" +
				"      \"title\": \"\"\n" +
				"    }\n" +
				"  ],\n" +
				"  \"sortingCriterion\": [\n" +
				"    \"Latest first\",\n" +
				"    \"Helpful first\",\n" +
				"    \"Positive first\",\n" +
				"    \"Negative first\"\n" +
				"  ]\n" +
				"}";

		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getFlyfishReviewSummary().put(OTA.TA, new ObjectMapper().readTree(ratingJson));
		Assert.assertNotNull(searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(listingResponse, searchHotelsRequest,null));
	}

	private DisplayPriceBreakDown buildDisplayPriceBreakDown(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setDisplayPrice(36000.00);
		displayPriceBreakDown.setSavingPerc(80.00);
		return displayPriceBreakDown;
	}

	private SearchWrapperHotelEntity buildSearchWrapperHotelEntity(){
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setRoomCount(5);
		return searchWrapperHotelEntity;
	}

	private SearchHotelsCriteria buildSearchCriteria(){
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCheckIn("2022-11-10");
		searchCriteria.setCheckOut("2022-11-11");
		return searchCriteria;
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest(){
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
		searchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().size() > 0);
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().get(0).getText().contains(secondaryPersuasion));

	}

	@Test
	public void overridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123456789");
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		matchMakerRequest.setHotels(Collections.singletonList(inputHotel));
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("CORPBUDGET");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("MyBiz Recommended Properties Near This Property");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		assertEquals("MyBiz Recommended Properties Near This Property", personalizedSection.getHeading());
	}

	@Test
	public void doNotOverridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		PersonalizedSection personalizedSection = new PersonalizedSection();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		org.junit.Assert.assertNull(personalizedSection.getHeading());
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		org.junit.Assert.assertNotNull(searchHotelsResponseTransformerPWA.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public void addSeoTextPersuasionTest() throws JsonProcessingException {
		Hotel hotel = new Hotel();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		boolean oddHotel = false;
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
		searchHotelsRequest.getRequestDetails().getTrafficSource().setSource("trivago");
		searchHotelsResponseTransformerPWA.addSeoTextPersuasion(hotel, hotelEntity, oddHotel, searchHotelsRequest, null);
		org.junit.Assert.assertNull(hotel.getHotelPersuasions());

		searchHotelsRequest.getRequestDetails().getTrafficSource().setSource(Constants.TRAFFIC_SOURCE_SEO);
		searchHotelsRequest.getRequestDetails().setSiteDomain("IN");
		searchHotelsResponseTransformerPWA.addSeoTextPersuasion(hotel, hotelEntity, oddHotel, searchHotelsRequest, null);
		org.junit.Assert.assertNotNull(hotel.getHotelPersuasions());
		org.junit.Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.PLACEHOLDER_CARD_SEO).getData());

		hotelEntity.setStarRating(3);
		hotelEntity.setCountryCode("IN");
		hotelEntity.setAddress(new Address());
		hotelEntity.getAddress().setLine1("Ist line addr");
		hotelEntity.getAddress().setLine2("2nd line addr");
		hotelEntity.setCityName("GOA");
		hotelEntity.setPropertyType("Hotel");
		hotelEntity.setFlyfishReviewSummary(new HashMap<>());
		JsonNode jsonNode = objectMapper.readTree(
				"{\"cumulativeRating\":4.5,\"totalReviewsCount\":226,\"totalRatingCount\":313,\"travellerRatingSummary\":{\"hotelSummary\":[{\"concept\":\"Safety and Hygiene\",\"value\":4.4,\"show\":true,\"heroTag\":true,\"reviewCount\":65},{\"concept\":\"Security\",\"value\":5.0,\"show\":true,\"reviewCount\":1},{\"concept\":\"Location\",\"value\":4.6,\"show\":true,\"reviewCount\":50,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Location\",\"relatedReviewCount\":53,\"tagType\":\"BASE\",\"priorityScore\":53},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Distance from Beach\",\"relatedReviewCount\":28,\"tagType\":\"BASE\",\"priorityScore\":28},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach\",\"relatedReviewCount\":23,\"tagType\":\"BASE\",\"priorityScore\":23},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Connectivity\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Central Location\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Location\",\"relatedReviewCount\":52,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":52},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Near Beach\",\"relatedReviewCount\":51,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":51}]},{\"concept\":\"Hospitality\",\"value\":4.5,\"show\":true,\"reviewCount\":167,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Courtesy\",\"relatedReviewCount\":76,\"tagType\":\"BASE\",\"priorityScore\":76},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Service Quality\",\"relatedReviewCount\":16,\"tagType\":\"BASE\",\"priorityScore\":16},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Courteous Staff\",\"relatedReviewCount\":75,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":75},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Service\",\"relatedReviewCount\":14,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":14}]},{\"concept\":\"Room\",\"value\":4.4,\"show\":true,\"reviewCount\":61,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Quality\",\"relatedReviewCount\":56,\"tagType\":\"BASE\",\"priorityScore\":56},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Space in Rooms\",\"relatedReviewCount\":7,\"tagType\":\"BASE\",\"priorityScore\":7},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Balcony\",\"relatedReviewCount\":6,\"tagType\":\"BASE\",\"priorityScore\":6},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Bathroom Hygiene\",\"relatedReviewCount\":4,\"tagType\":\"BASE\",\"priorityScore\":4},{\"sentiment\":\"NEGATIVE\",\"subConcept\":\"Bed Quality\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Amenities\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Room\",\"relatedReviewCount\":60,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":60},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Balcony\",\"relatedReviewCount\":6,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":6}]},{\"concept\":\"Cleanliness\",\"value\":4.2,\"show\":true,\"reviewCount\":222,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Cleanliness\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Resort Cleanliness\",\"relatedReviewCount\":10,\"tagType\":\"BASE\",\"priorityScore\":10},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Bathroom Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach Cleanliness\",\"relatedReviewCount\":3,\"tagType\":\"BASE\",\"priorityScore\":3},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Hotel Cleanliness\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Surroundings Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Garden Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Area Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Amenity Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Place Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Environment Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Room\",\"relatedReviewCount\":17,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":10000},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Property\",\"relatedReviewCount\":9,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":9900},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Bathroom\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Pool\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Facilities\",\"value\":4.2,\"show\":true,\"reviewCount\":96,\"subConcepts\":[{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Patio\",\"relatedReviewCount\":12,\"tagType\":\"BASE\",\"priorityScore\":12},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool\",\"relatedReviewCount\":8,\"tagType\":\"BASE\",\"priorityScore\":8},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"In-House Activities\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Pool\",\"relatedReviewCount\":8,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":8}]},{\"concept\":\"Value for Money\",\"value\":4.2,\"show\":true,\"reviewCount\":32,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Luxury\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Food\",\"value\":4.1,\"show\":true,\"reviewCount\":153,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Food\",\"relatedReviewCount\":38,\"tagType\":\"BASE\",\"priorityScore\":38},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Breakfast\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Local Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Continental Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Mediterranean Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Food\",\"relatedReviewCount\":34,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":34},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Breakfast\",\"relatedReviewCount\":15,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":15},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Child friendliness\",\"value\":4.0,\"show\":true,\"reviewCount\":21}],\"roomSummary\":{\"45000297254\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"View\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"566\":{\"cumulativeRating\":0.0},\"567\":{\"cumulativeRating\":0.0},\"24133\":{\"cumulativeRating\":0.0,\"best\":[{\"publishDate\":\"Feb 21, 2021\",\"travellerName\":\"Aditya Surana\",\"title\":\"Waooooo. Really worth staying\",\"rating\":5.0,\"reviewText\":\"Waooo, what a place what a location and above all the beach view rooms are the best\\nthe breakfast varity was good event the service but taste 5/10, rest a must visit resort if u visit goa\",\"id\":\"P9J5FYGIY2HN4A8EU55CO0ULNI9XHEDUV5S0DIQOINYFGGC5KTDNFR6IZYAVRU0XH5XTQ4IQJT0M\",\"travelType\":\"COUPLE\",\"crawledData\":false}]},\"45000308605\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Spacious Room\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068630\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068632\":{\"cumulativeRating\":0.0},\"560\":{\"cumulativeRating\":0.0}}},\"crawledData\":false,\"cityCode\":\"GOI\",\"sortingCriterion\":[\"Latest first\",\"Helpful first\",\"Positive first\",\"Negative first\"],\"ratingText\":\"Excellent\",\"postLockdownData\":{\"rating\":4.5,\"totalReviewCount\":64,\"ratingCount\":10,\"textCount\":47,\"imageCount\":2,\"imageTextCount\":5}}");
		hotelEntity.getFlyfishReviewSummary().put(OTA.MMT, jsonNode);
		LinkedHashSet<String> set = new LinkedHashSet<>();
		set.add("Cleanliness");
		set.add("Hygenic");
		hotelEntity.setFacilityHighlights(set);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerPWA, "objectMapperUtil", objectMapperUtil);
		Mockito.when(objectMapperUtil.getObjectFromJsonNode(Mockito.any(), Mockito.any())).thenReturn(
				objectMapper.readValue(jsonNode.get("travellerRatingSummary").toString(), TravellerRatingSummaryDTO.class));
		searchHotelsResponseTransformerPWA.addSeoTextPersuasion(hotel, hotelEntity, oddHotel, searchHotelsRequest, null);
		org.junit.Assert.assertNotNull(hotel.getHotelPersuasions());
		org.junit.Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.PLACEHOLDER_CARD_SEO).getData());

		searchHotelsResponseTransformerPWA.addSeoTextPersuasion(hotel, hotelEntity, !oddHotel, searchHotelsRequest, null);
		org.junit.Assert.assertNotNull(hotel.getHotelPersuasions());
		org.junit.Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.PLACEHOLDER_CARD_SEO).getData());

		hotelEntity.setCountryCode("AE");
		hotelEntity.getFlyfishReviewSummary().put(OTA.TA, jsonNode);
		searchHotelsResponseTransformerPWA.addSeoTextPersuasion(hotel, hotelEntity, !oddHotel, searchHotelsRequest, null);
		org.junit.Assert.assertNotNull(hotel.getHotelPersuasions());
		org.junit.Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.PLACEHOLDER_CARD_SEO).getData());
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerPWA.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}
	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		hotelEntity.setDetailDeeplinkUrl("test");
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerPWA.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		org.junit.Assert.assertNotNull(staticCard);
		assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}

	@Test
	public void updateTopLevelHoverTest() throws ClientGatewayException {

		Hotel hotel = new Hotel();

		try {
			org.json.JSONObject jo = new JSONObject("{\"data\":{\"titleText\":\"Holdthisbookingforfreetillmidnightofduedate\",\"subText\":\"LimitedBookingsubjecttoavailability\"},\"tooltipType\":\"MP_FARE_HOLD\"}");
			org.json.JSONArray ja = new JSONArray("[{\"hasAction\":false,\"icontype\":\"holdHotelIconSmall\",\"persuasionType\":\"BNPL\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"appendLeft3\"]},\"html\":false,\"id\":\"630cdde601a1251b91e0a11d\",\"multiPersuasionPriority\":0},{\"hasAction\":false,\"timer\":{\"expiry\":1665383340000},\"persuasionType\":\"BNPL\",\"persuasionKey\":\"BOOK_NOW_PERSUASION_TITLE\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"pc__holdBookingTooltip__bookNowText\",\"latoBold\"]},\"html\":false,\"id\":\"630c630a1c05b343d18680e7\",\"text\":\"BookNow@\\u20b90\",\"multiPersuasionPriority\":1},{\"hasAction\":false,\"icontype\":\"holdBookingInfoGrey\",\"persuasionType\":\"BNPL\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"appendRight3\"]},\"html\":false,\"id\":\"630cdd9d01a1251b91e0a11c\",\"multiPersuasionPriority\":2}]");
			Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("abc");
			Mockito.when(dateUtil.convertEpochToDateTime(Mockito.anyLong(), Mockito.anyString())).thenReturn("abc");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA,"updateTopLevelHover",jo,ja);
		}catch (Exception e){
			logger.error("error occured in getting file", e.getMessage());

		}
	}

	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerPWA.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdPWA()));
	}



	@DisplayName("Should build price detail for day use when display fare is not null and slashed price is present")
@Test
public void shouldBuildPriceDetailForDayUseWithSlashedPrice() {
    DisplayFare displayFare = new DisplayFare();


		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceWithTax(0100.012);
		slashedPrice.setSellingPriceNoTax(100.123);
		displayFare.setSlashedPrice(slashedPrice);


		PriceDetail result = searchHotelsResponseTransformerPWA.buildPriceDetailForDayUse(displayFare);
		assertEquals(Double.valueOf(100), result.getPrice());
		assertEquals(Double.valueOf(100), result.getPriceWithTax());

		slashedPrice.setSellingPriceWithTax(-100.012);
		slashedPrice.setSellingPriceNoTax(-100.123);
		 result = searchHotelsResponseTransformerPWA.buildPriceDetailForDayUse(displayFare);
		assertEquals(Double.valueOf(-101), result.getPrice());
		assertEquals(Double.valueOf(-101), result.getPriceWithTax());


		slashedPrice.setSellingPriceWithTax(00.012);
		slashedPrice.setSellingPriceNoTax(00.123);
		result = searchHotelsResponseTransformerPWA.buildPriceDetailForDayUse(displayFare);
		assertEquals(Double.valueOf(0), result.getPrice());
		assertEquals(Double.valueOf(0), result.getPriceWithTax());


		slashedPrice.setSellingPriceWithTax(0);
		slashedPrice.setSellingPriceNoTax(0);


		result = searchHotelsResponseTransformerPWA.buildPriceDetailForDayUse(displayFare);


		assertEquals(Double.valueOf(0), result.getPrice());
		assertEquals(Double.valueOf(0), result.getPriceWithTax());
	}

}