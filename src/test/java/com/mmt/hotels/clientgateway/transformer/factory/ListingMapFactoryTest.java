package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.ListingMapRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.ListingMapRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.ListingMapRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.ListingMapRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.ListingMapRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.ListingMapResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.ListingMapResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.ListingMapResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.ListingMapResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ListingMapResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ListingMapFactoryTest {


    @InjectMocks
    ListingMapFactory listingMapFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(listingMapFactory,"listingMapResponseTransformerPWA" , new ListingMapResponseTransformerPWA());
        ReflectionTestUtils.setField(listingMapFactory,"listingMapResponseTransformerDesktop" , new ListingMapResponseTransformerDesktop());
        ReflectionTestUtils.setField(listingMapFactory,"listingMapResponseTransformerAndroid" , new ListingMapResponseTransformerAndroid());
        ReflectionTestUtils.setField(listingMapFactory,"listingMapResponseTransformerIOS" , new ListingMapResponseTransformerIOS());
        ReflectionTestUtils.setField(listingMapFactory,"listingMapRequestTransformerPWA" , new ListingMapRequestTransformerPWA());
        ReflectionTestUtils.setField(listingMapFactory,"listingMapRequestTransformerDesktop" , new ListingMapRequestTransformerDesktop());
        ReflectionTestUtils.setField(listingMapFactory,"listingMapRequestTransformerAndroid" , new ListingMapRequestTransformerAndroid());
        ReflectionTestUtils.setField(listingMapFactory,"listingMapRequestTransformerIOS" , new ListingMapRequestTransformerIOS());
        
    }
    
    @Test
    public void getRequestServiceTest(){
        ListingMapRequestTransformer resp = listingMapFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof ListingMapRequestTransformerPWA  );
        resp = listingMapFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof ListingMapRequestTransformerDesktop  );
        resp = listingMapFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof ListingMapRequestTransformerAndroid  );
        resp = listingMapFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof ListingMapRequestTransformerIOS  );
        resp = listingMapFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(listingMapFactory.getRequestService("test") instanceof  ListingMapRequestTransformerDesktop);
    }

    @Test
    public void getResponseServiceTest(){
        ListingMapResponseTransformer resp = listingMapFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof ListingMapResponseTransformerPWA  );
        resp = listingMapFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof ListingMapResponseTransformerDesktop  );
        resp = listingMapFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof ListingMapResponseTransformerAndroid  );
        resp = listingMapFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof ListingMapResponseTransformerIOS  );
        resp = listingMapFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(listingMapFactory.getResponseService("test") instanceof  ListingMapResponseTransformerDesktop);
    }

}
