package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.response.desktop.MobLandingResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.MobLandingResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MobLandingResponseTransformerDesktopTest {
    @InjectMocks
    MobLandingResponseTransformerDesktop mobLandingResponseTransformerDesktop;
    @Mock
    SearchHotelsFactory searchHotelsFactory;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Mock
    PersuasionUtil persuasionUtil;

    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        List<String> locationPersuasion = new ArrayList<>();
        searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
        mobLandingResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
        locationPersuasion.add("test1");
        searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
        mobLandingResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
        locationPersuasion.add("test2");
        mobLandingResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
    }

    @Test
    public void addPersuasionsForHiddenGemCardTest() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        searchWrapperHotelEntity.setHotelPersuasions(new HashMap<>());
        searchWrapperHotelEntity.setHiddenGem(true);
        searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
        HomeStayDetails homeStayDetails = new HomeStayDetails();
        homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
        homeStayDetails.setStayTypeInfo("Home Stay Info Test");

        PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
        List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
        homeStaysTitlePersuasionList.add(new PersuasionData());
        homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

        PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
        homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

        when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
        when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
        when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
        when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
        mobLandingResponseTransformerDesktop.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
        Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
        Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdDesktop()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdDesktop()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdDesktop()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdDesktop()));
    }
}
