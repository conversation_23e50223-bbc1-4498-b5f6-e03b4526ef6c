package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomBanner;
import com.mmt.hotels.clientgateway.service.PolyglotService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SearchRoomsBannerHelper in GI project
 * Comprehensive test coverage following patterns from CG version but adapted for GI implementation
 * GI version focuses only on amenity-based banners without FlexiCancel support
 */
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsBannerHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private SearchRoomsBannerHelper searchRoomsBannerHelper;

    // Test constants
    private static final String AMENITY_BANNER_1 = "{ROOM_NAME} has {AMENITY_1}";
    private static final String AMENITY_BANNER_2 = "{ROOM_NAME} has {AMENITY_1} and {AMENITY_2}";
    private static final String AMENITY_BANNER_3 = "{ROOM_NAME} has {AMENITY_1}, {AMENITY_2} and {AMENITY_3}";
    private static final String ROOM_NAME_PLACEHOLDER = "{ROOM_NAME}";
    private static final String AMENITY_1_PLACEHOLDER = "{AMENITY_1}";
    private static final String AMENITY_2_PLACEHOLDER = "{AMENITY_2}";
    private static final String AMENITY_3_PLACEHOLDER = "{AMENITY_3}";

    @Before
    public void setUp() {
        // Setup polyglot service responses for banner templates
        when(polyglotService.getTranslatedData(SELECT_ROOM_1_AMENITIES_BANNER))
                .thenReturn(AMENITY_BANNER_1);
        when(polyglotService.getTranslatedData(SELECT_ROOM_2_AMENITIES_BANNER))
                .thenReturn(AMENITY_BANNER_2);
        when(polyglotService.getTranslatedData(SELECT_ROOM_3_AMENITIES_BANNER))
                .thenReturn(AMENITY_BANNER_3);
    }

    // ==================== buildBanner Tests - Main Public Method ====================

    @Test
    public void should_ReturnNull_When_HotelDetailsIsNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_SearchRoomsResponseIsNull() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithRooms();

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(null, hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BothParametersAreNull() {
        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(null, null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_NoValidRoomsFound() {
        // Given
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        HotelDetails hotelDetails = new HotelDetails();

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    // ==================== ExactRooms Flow Tests ====================

    @Test
    public void should_BuildBanner_When_ExactRoomsHasValidRoomWithMostAmenities() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList("WiFi"), 
                        Arrays.asList("WiFi", "Pool", "Gym")
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Deluxe Room has WiFi, Pool and Gym", result.getTitle());
        assertEquals(Constants.SELECT_ROOM_BANNER_TYPE, result.getRedirectType());
        assertEquals("R2", result.getRedirectLink());
        assertEquals(Constants.SELECT_ROOM_BANNER_ICON_URL, result.getIconUrl());
        assertEquals(Constants.SELECT_ROOM_BANNER_BG_COLOR, result.getBgColor());
    }

    @Test
    public void should_BuildBannerWithSingleAmenity_When_ExactRoomsHasRoomWithOneAmenity() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList(), 
                        Arrays.asList("WiFi")
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Deluxe Room has WiFi", result.getTitle());
        assertEquals(Constants.SELECT_ROOM_BANNER_TYPE, result.getRedirectType());
        assertEquals("R2", result.getRedirectLink());
    }

    @Test
    public void should_BuildBannerWithTwoAmenities_When_ExactRoomsHasRoomWithTwoAmenities() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList(), 
                        Arrays.asList("WiFi", "Pool")
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Deluxe Room has WiFi and Pool", result.getTitle());
        assertEquals("R2", result.getRedirectLink());
    }

    @Test
    public void should_ReturnNull_When_ExactRoomsExcludesOnlyRoomWithAmenities() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1"), 
                Arrays.asList(
                        Arrays.asList("WiFi", "Pool")
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_ExactRoomsHasNoRoomsWithAmenities() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList(), 
                        Arrays.asList()
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_ExactRoomsListIsEmpty() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms();
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1"), 
                Arrays.asList(Arrays.asList("WiFi"))
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HotelDetailsRoomsListIsEmpty() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(new ArrayList<>());

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    // ==================== OccupancyRooms Flow Tests ====================

    @Test
    public void should_BuildBanner_When_OccupancyRoomsHasValidRoomWithMostAmenities() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithOccupancyRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList("WiFi"), 
                        Arrays.asList("WiFi", "Pool", "Spa")
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Deluxe Room has WiFi, Pool and Spa", result.getTitle());
        assertEquals("R2", result.getRedirectLink());
    }

    @Test
    public void should_ReturnNull_When_OccupancyRoomsHasNoValidRooms() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithOccupancyRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1"), 
                Arrays.asList(Arrays.asList("WiFi"))
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    // ==================== RecommendedCombos Flow Tests ====================

    @Test
    public void should_BuildBanner_When_RecommendedCombosHasValidRoomWithMostAmenities() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombos("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomCombosAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList("WiFi"), 
                        Arrays.asList("WiFi", "Pool")
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Deluxe Room has WiFi and Pool", result.getTitle());
        assertEquals("R2", result.getRedirectLink());
    }

    @Test
    public void should_ReturnNull_When_RecommendedCombosRoomCodeMatchesExcludedRoom() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombos("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomCombosAndAmenities(
                Arrays.asList("R1"), 
                Arrays.asList(Arrays.asList("WiFi", "Pool"))
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_RecommendedCombosHasNoRoomCombos() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombos("R1");
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(new ArrayList<>());

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    // ==================== Room Name Handling Tests ====================

    @Test
    public void should_UseDefaultRoomName_When_RoomNameIsNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList(), 
                        Arrays.asList("WiFi")
                )
        );
        // Set room name to null
        hotelDetails.getRooms().get(1).getRoomInfo().setRoomName(null);

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Room has WiFi", result.getTitle());
    }

    @Test
    public void should_UseDefaultRoomName_When_RoomNameIsBlank() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList(), 
                        Arrays.asList("WiFi")
                )
        );
        // Set room name to blank
        hotelDetails.getRooms().get(1).getRoomInfo().setRoomName("");

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Room has WiFi", result.getTitle());
    }

    @Test
    public void should_UseDefaultRoomName_When_RoomInfoIsNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRooms();
        
        Rooms roomWithoutInfo = new Rooms();
        roomWithoutInfo.setCode("R2");
        roomWithoutInfo.setRoomInfo(null);
        hotelDetails.getRooms().add(roomWithoutInfo);

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result); // No amenities available
    }

    // ==================== Error Handling Tests ====================

//    @Test
//    public void should_HandlePolyglotServiceException_When_TranslationFails() {
//        // Given
//        when(polyglotService.getTranslatedData(anyString())).thenThrow(new RuntimeException("Translation failed"));
//
//        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
//        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
//                Arrays.asList("R1", "R2"),
//                Arrays.asList(
//                        Arrays.asList(),
//                        Arrays.asList("WiFi")
//                )
//        );
//
//        // When
//        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);
//
//        // Then
//        assertNull(result); // Should handle exception gracefully
//    }

    @Test
    public void should_HandleNullRoomCode_When_RoomCodeIsNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRooms();
        
        Rooms roomWithNullCode = new Rooms();
        roomWithNullCode.setCode(null);
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomName("Test Room");
        roomInfo.setAmenities(Arrays.asList(createAmenityGroup("WiFi")));
        roomWithNullCode.setRoomInfo(roomInfo);
        hotelDetails.getRooms().add(roomWithNullCode);

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result); // Should filter out rooms with null codes
    }

    @Test
    public void should_HandleNullAmenities_When_AmenitiesListIsNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRooms();
        
        Rooms roomWithNullAmenities = new Rooms();
        roomWithNullAmenities.setCode("R2");
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomName("Test Room");
        roomInfo.setAmenities(null);
        roomWithNullAmenities.setRoomInfo(roomInfo);
        hotelDetails.getRooms().add(roomWithNullAmenities);

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result); // Should filter out rooms with null amenities
    }

    @Test
    public void should_HandleEmptyAmenities_When_AmenitiesListIsEmpty() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRooms();
        
        Rooms roomWithEmptyAmenities = new Rooms();
        roomWithEmptyAmenities.setCode("R2");
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomName("Test Room");
        roomInfo.setAmenities(new ArrayList<>());
        roomWithEmptyAmenities.setRoomInfo(roomInfo);
        hotelDetails.getRooms().add(roomWithEmptyAmenities);

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result); // Should filter out rooms with empty amenities
    }

    // ==================== Room Selection Priority Tests ====================

    @Test
    public void should_SelectRoomWithMostAmenities_When_MultipleRoomsAvailable() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2", "R3", "R4"), 
                Arrays.asList(
                        Arrays.asList("WiFi"), // R1 - excluded
                        Arrays.asList("WiFi", "Pool"), // R2 - 2 amenities
                        Arrays.asList("WiFi"), // R3 - 1 amenity
                        Arrays.asList("WiFi", "Pool", "Gym", "Spa") // R4 - 4 amenities (should be selected)
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Deluxe Room has WiFi, Pool and Gym", result.getTitle()); // Only first 3 amenities shown
        assertEquals("R4", result.getRedirectLink());
    }

    @Test
    public void should_HandleTieInAmenityCount_When_MultipleRoomsHaveSameAmenityCount() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRooms("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndAmenities(
                Arrays.asList("R1", "R2", "R3"), 
                Arrays.asList(
                        Arrays.asList("WiFi"), // R1 - excluded
                        Arrays.asList("WiFi", "Pool"), // R2 - 2 amenities
                        Arrays.asList("Gym", "Spa") // R3 - 2 amenities (tie)
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        // Should select one of the tied rooms (stream.max() returns the first max element encountered)
        assertTrue(result.getRedirectLink().equals("R2") || result.getRedirectLink().equals("R3"));
    }

    // ==================== Room Combo Flow Specific Tests ====================

    @Test
    public void should_BuildBannerFromRoomCombos_When_RecommendedCombosAndRoomCombosAvailable() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombos("R1");
        HotelDetails hotelDetails = createHotelDetailsWithRoomCombosAndAmenities(
                Arrays.asList("R1", "R2"), 
                Arrays.asList(
                        Arrays.asList("WiFi"), 
                        Arrays.asList("WiFi", "Pool", "Gym")
                )
        );

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNotNull(result);
        assertEquals("Deluxe Room has WiFi, Pool and Gym", result.getTitle());
        assertEquals("R2", result.getRedirectLink());
    }

    @Test
    public void should_HandleNullRoomCombo_When_RoomComboIsNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombos("R1");
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(Arrays.asList((RoomCombo) null));

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_HandleNullRoomsInCombo_When_RoomsListIsNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombos("R1");
        HotelDetails hotelDetails = new HotelDetails();
        
        RoomCombo roomCombo = new RoomCombo();
        roomCombo.setRooms(null);
        hotelDetails.setRoomCombos(Arrays.asList(roomCombo));

        // When
        SelectRoomBanner result = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        // Then
        assertNull(result);
    }

    // ==================== Helper Methods for Test Data Creation ====================

    private SearchRoomsResponse createSearchRoomsResponseWithExactRooms(String... roomCodes) {
        SearchRoomsResponse response = new SearchRoomsResponse();
        List<RoomDetails> exactRooms = new ArrayList<>();
        
        for (String roomCode : roomCodes) {
            RoomDetails roomDetails = new RoomDetails();
            roomDetails.setRoomCode(roomCode);
            exactRooms.add(roomDetails);
        }
        
        response.setExactRooms(exactRooms);
        return response;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithOccupancyRooms(String... roomCodes) {
        SearchRoomsResponse response = new SearchRoomsResponse();
        List<RoomDetails> occupancyRooms = new ArrayList<>();
        
        for (String roomCode : roomCodes) {
            RoomDetails roomDetails = new RoomDetails();
            roomDetails.setRoomCode(roomCode);
            occupancyRooms.add(roomDetails);
        }
        
        response.setOccupancyRooms(occupancyRooms);
        return response;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithRecommendedCombos(String... roomCodes) {
        SearchRoomsResponse response = new SearchRoomsResponse();
        List<RecommendedCombo> recommendedCombos = new ArrayList<>();
        
        RecommendedCombo combo = new RecommendedCombo();
        List<RoomDetails> roomDetailsList = new ArrayList<>();
        
        for (String roomCode : roomCodes) {
            RoomDetails roomDetails = new RoomDetails();
            roomDetails.setRoomCode(roomCode);
            roomDetailsList.add(roomDetails);
        }
        
        combo.setRooms(roomDetailsList);
        recommendedCombos.add(combo);
        response.setRecommendedCombos(recommendedCombos);
        return response;
    }

    private HotelDetails createHotelDetailsWithRooms() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(new ArrayList<>());
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithRoomsAndAmenities(List<String> roomCodes, List<List<String>> amenitiesPerRoom) {
        HotelDetails hotelDetails = new HotelDetails();
        List<Rooms> rooms = new ArrayList<>();
        
        for (int i = 0; i < roomCodes.size(); i++) {
            String roomCode = roomCodes.get(i);
            List<String> amenities = i < amenitiesPerRoom.size() ? amenitiesPerRoom.get(i) : new ArrayList<>();
            
            Rooms room = new Rooms();
            room.setCode(roomCode);
            
            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setRoomName("Deluxe Room");
            roomInfo.setAmenities(createAmenityGroups(amenities));
            room.setRoomInfo(roomInfo);
            
            rooms.add(room);
        }
        
        hotelDetails.setRooms(rooms);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithRoomCombosAndAmenities(List<String> roomCodes, List<List<String>> amenitiesPerRoom) {
        HotelDetails hotelDetails = new HotelDetails();
        List<RoomCombo> roomCombos = new ArrayList<>();
        
        for (int i = 0; i < roomCodes.size(); i++) {
            String roomCode = roomCodes.get(i);
            List<String> amenities = i < amenitiesPerRoom.size() ? amenitiesPerRoom.get(i) : new ArrayList<>();
            
            RoomCombo roomCombo = new RoomCombo();
            
            Rooms room = new Rooms();
            room.setCode(roomCode);
            
            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setRoomName("Deluxe Room");
            roomInfo.setAmenities(createAmenityGroups(amenities));
            room.setRoomInfo(roomInfo);
            
            roomCombo.setRooms(Arrays.asList(room));
            roomCombos.add(roomCombo);
        }
        
        hotelDetails.setRoomCombos(roomCombos);
        return hotelDetails;
    }

    private List<AmenityGroup> createAmenityGroups(List<String> amenityNames) {
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        
        for (String amenityName : amenityNames) {
            amenityGroups.add(createAmenityGroup(amenityName));
        }
        
        return amenityGroups;
    }

    private AmenityGroup createAmenityGroup(String name) {
        AmenityGroup amenityGroup = new AmenityGroup();
        amenityGroup.setName(name);
        return amenityGroup;
    }
} 