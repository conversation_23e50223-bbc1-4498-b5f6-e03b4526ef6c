package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.PaymentRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.PaymentResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.desktop.PaymentResponseTransformerDesktop;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PaymentFactoryTest {

    @InjectMocks
    PaymentFactory paymentFactory;

    @Mock
    PaymentRequestTransformer paymentRequestTransformer;

    @Mock
    PaymentResponseTransformer paymentResponseTransformer;

    @Mock
    PaymentResponseTransformerDesktop paymentResponseTransformerDesktop;

    @Test
    public  void getRequestServiceTest(){
        Assert.assertTrue(paymentFactory.getRequestService("") instanceof  PaymentRequestTransformer);
        Assert.assertTrue(paymentFactory.getRequestService("android") instanceof  PaymentRequestTransformer);
        Assert.assertTrue(paymentFactory.getRequestService("IOS") instanceof  PaymentRequestTransformer);
        Assert.assertTrue(paymentFactory.getRequestService("PWA") instanceof  PaymentRequestTransformer);
        Assert.assertTrue(paymentFactory.getRequestService("DESKTOP") instanceof  PaymentRequestTransformer);
        Assert.assertTrue(paymentFactory.getRequestService("trina") instanceof PaymentRequestTransformer );

    }


    @Test
    public  void getResponseServiceTest(){
        Assert.assertTrue(paymentFactory.getResponseService("") instanceof  PaymentResponseTransformer);
        Assert.assertTrue(paymentFactory.getResponseService("android") instanceof  PaymentResponseTransformer);
        Assert.assertTrue(paymentFactory.getResponseService("IOS") instanceof  PaymentResponseTransformer);
        Assert.assertTrue(paymentFactory.getResponseService("PWA") instanceof  PaymentResponseTransformerDesktop);
        Assert.assertTrue(paymentFactory.getResponseService("DESKTOP") instanceof  PaymentResponseTransformerDesktop);
        Assert.assertTrue(paymentFactory.getResponseService("trina") instanceof PaymentResponseTransformer );

    }
}
