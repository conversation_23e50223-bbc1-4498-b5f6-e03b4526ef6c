package com.mmt.hotels.clientgateway.transformer.response;

import com.gommt.hotels.content.proto.mediaid.MediaIdRequest;
import com.gommt.hotels.content.proto.mediaid.MediaIdResponse;
import com.gommt.hotels.content.proto.mediaobj.MediaObj;
import com.gommt.hotels.content.proto.status.ErrorDetails;
import com.gommt.hotels.content.proto.status.Status;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaObj;
import com.gommt.hotels.content.proto.taggedmedia.TaggedMediaResponse;
import com.mmt.hotels.clientgateway.response.gi.mediabyid.TaggedMediaByIdResponse;
import com.mmt.hotels.clientgateway.response.gi.taggedmedia.*;
import com.mmt.hotels.model.response.errors.Error;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;

class GrpcContentResponseBuilderTest {

    private GrpcContentResponseBuilder grpcContentResponseBuilderUnderTest;

    @BeforeEach
    void setUp() {
        grpcContentResponseBuilderUnderTest = new GrpcContentResponseBuilder();
    }

    @Test
    void testInit() {
        // Setup
        // Run the test
        grpcContentResponseBuilderUnderTest.init();

        // Verify the results
    }

    @Test
    void testBuildTaggedMediaResponse() {
        // Setup
        final TaggedMediaResponse grpcTaggedMediaResponse = TaggedMediaResponse.newBuilder()
                .setStatus(Status.newBuilder()
                        .setCode(0)
                        .setMessage("message")
                        .setErrorDetails(ErrorDetails.newBuilder()
                                .setReason("reason")
                                .build())
                        .build())
                .addTaggedMedia(TaggedMediaObj.newBuilder()
                        .setTitle("id")
                        .setCount(0)
                        .addMedia(MediaObj.newBuilder()
                                .setImageUrl("imageUrl")
                                .setVideoUrl("videoUrl")
                                .build())
                        .setType("type")
                        .build())
                .build();
        final com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse expectedResult = new com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse();
        expectedResult.setSuccess(false);
        final TaggedMediaData data = new TaggedMediaData();
        final HotelMedia hotelMedia = new HotelMedia();
        final TaggedMedia taggedMedia = new TaggedMedia();
        taggedMedia.setId("id");
        taggedMedia.setTitle("id");
        final Media media = new Media();
        media.setType("image");
        final ProcessedVideos processedVideos = new ProcessedVideos();
        final FormatUrl mp4Format = new FormatUrl();
        mp4Format.setUrl("videoUrl");
        processedVideos.setMp4Format(mp4Format);
        processedVideos.setSnapshotUrl("imageUrl");
        media.setProcessedVideos(processedVideos);
        media.setBigUrl("bigUrl");
        media.setGalleryUrl("galleryUrl");
        media.setThumbUrl("thumbUrl");
        media.setMobileThumbUrl("mobileThumbUrl");
        taggedMedia.setMedia(Arrays.asList(media));
        taggedMedia.setAllVideos(false);
        taggedMedia.setCount(0);
        hotelMedia.setTaggedMediaV3(Arrays.asList(taggedMedia));
        data.setHotelMedia(hotelMedia);
        expectedResult.setData(data);
        expectedResult.setResponseError(new Error());

        // Run the test
        final com.mmt.hotels.clientgateway.response.gi.taggedmedia.TaggedMediaResponse result = grpcContentResponseBuilderUnderTest.buildTaggedMediaResponse(
                grpcTaggedMediaResponse);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testBuildTaggedMediaByIdResponse() {
        // Setup
        final MediaIdRequest mediaIdRequest = MediaIdRequest.newBuilder()
                .setTag("tag")
                .build();
        final MediaIdResponse mediaIdResponse = MediaIdResponse.newBuilder()
                .setStatus(Status.newBuilder()
                        .setCode(0)
                        .setMessage("message")
                        .setErrorDetails(ErrorDetails.newBuilder()
                                .setReason("reason")
                                .build())
                        .build())
                .addMedia(MediaObj.newBuilder()
                        .setImageUrl("imageUrl")
                        .setVideoUrl("videoUrl")
                        .build())
                .build();
        final TaggedMediaByIdResponse expectedResult = new TaggedMediaByIdResponse();
        expectedResult.setSuccess(false);
        expectedResult.setData(new HashMap<>());
        expectedResult.setResponseError(new Error());

        // Run the test
        final TaggedMediaByIdResponse result = grpcContentResponseBuilderUnderTest.buildTaggedMediaByIdResponse(
                mediaIdRequest, mediaIdResponse);

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }
}
