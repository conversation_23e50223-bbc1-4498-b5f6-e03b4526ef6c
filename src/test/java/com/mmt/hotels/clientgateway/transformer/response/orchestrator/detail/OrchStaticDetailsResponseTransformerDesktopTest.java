package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail;

import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.TitleData;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerDesktop;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class OrchStaticDetailsResponseTransformerDesktopTest {

    @InjectMocks
    private OrchStaticDetailsResponseTransformerDesktop transformer;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private PolyglotHelper polyglotHelper;

    @Mock
    private CommonConfig commonConfig;

    @Mock
    private ValueStaysTooltip valueStaysTooltipDom;

    @Mock
    private ValueStaysTooltip valueStaysTooltipIntl;

    @Mock
    private StaffInfo orchestratorStaffInfo;

    @Mock
    private com.mmt.hotels.model.response.staticdata.StaffInfo clientGatewayStaffInfo;

    @Mock
    private HotelStaticContentResponse hotelStaticContentResponse;

    @Mock
    private StaticDetailRequest staticDetailRequest;

    @Mock
    private CommonModifierResponse commonModifierResponse;

    @Before
    public void setUp() {
        // Set up @Value injected properties
        ReflectionTestUtils.setField(transformer, "valueStatysTitleIcon", "http://example.com/title-icon.png");
        ReflectionTestUtils.setField(transformer, "valueStatysTitleIconGcc", "http://example.com/title-icon-gcc.png");
        ReflectionTestUtils.setField(transformer, "starHostIconDesktop", "http://example.com/star-host-desktop.png");
        ReflectionTestUtils.setField(transformer, "mmtValueStaysCategoryIconUrlDesktop", "http://example.com/category-icon-desktop.png");

        // Set up mock behavior
        setupMockBehavior();
    }

    private void setupMockBehavior() {
        when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        when(commonConfig.mmtValueStaysTooltipDom()).thenReturn(valueStaysTooltipDom);
        when(commonConfig.mmtValueStaysTooltipIntl()).thenReturn(valueStaysTooltipIntl);
        
        // Mock for addPropertyChangeListener calls
        doNothing().when(commonConfig).addPropertyChangeListener(anyString(), any());
    }

    // ========== @POSTCONSTRUCT TESTS ==========

    @Test
    public void testInit_withValidCommonConfig_initializesTooltips() {
        // Act
        transformer.init();

        // Assert
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
        verify(commonConfig).mmtValueStaysTooltipDom();
        verify(commonConfig).mmtValueStaysTooltipIntl();
        verify(commonConfig, times(2)).addPropertyChangeListener(anyString(), any());
    }

    @Test
    public void testInit_withException_logsErrorAndContinues() {
        // Arrange
        when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenThrow(new RuntimeException("PMS error"));

        // Act - should not throw exception
        transformer.init();

        // Assert
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
        // The method should handle exception gracefully and log error
    }

    @Test
    public void testInit_withNullCommonConfig_handlesGracefully() {
        // Arrange
        when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(null);

        // Act - should not throw exception, but may cause NPE in implementation
        try {
            transformer.init();
        } catch (Exception e) {
            // Expected if commonConfig is null
        }

        // Assert
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
    }

    @Test
    public void testInit_withPropertyChangeListenerException_handlesGracefully() {
        // Arrange
        when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        when(commonConfig.mmtValueStaysTooltipDom()).thenReturn(valueStaysTooltipDom);
        when(commonConfig.mmtValueStaysTooltipIntl()).thenReturn(valueStaysTooltipIntl);
        doThrow(new RuntimeException("Property listener error")).when(commonConfig).addPropertyChangeListener(anyString(), any());

        // Act - should handle exception gracefully
        try {
            transformer.init();
        } catch (Exception e) {
            // Exception handling depends on implementation
        }

        // Assert
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
    }

    // ========== ABSTRACT METHOD IMPLEMENTATIONS TESTS ==========

    @Test
    public void testBuildCardTitleMap_returnsNull() {
        // Act
        Map<String, String> result = transformer.buildCardTitleMap();

        // Assert
        assertNull("buildCardTitleMap should return null for desktop implementation", result);
    }

    @Test
    public void testGetLuxeIcon_returnsDesktopConstant() {
        // Act
        String result = transformer.getLuxeIcon();

        // Assert
        assertEquals("Should return desktop luxe icon constant", Constants.LUXE_ICON_DESKTOP, result);
    }

    // ========== ADD TITLE DATA TESTS ==========

    @Test
    public void testAddTitleData_withValidHotelResult_addsTitleData() {
        // Arrange
        HotelResult hotelResult = new HotelResult();
        String countryCode = "IN";
        
        // Set up real tooltips to avoid serialization issues
        ValueStaysTooltip realTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", realTooltipDom);

        // Act
        transformer.addTitleData(hotelResult, countryCode);

        // Assert
        assertNotNull("TitleData should be set", hotelResult.getTitleData());
        TitleData titleData = hotelResult.getTitleData();
        assertNotNull("Title icon should be set", titleData.getTitleIcon());
    }

    @Test
    public void testAddTitleData_withNullHotelResult_returnsEarly() {
        // Act - should not throw exception
        transformer.addTitleData(null, "IN");

        // Assert - No exceptions should be thrown, method should return early
    }

    @Test
    public void testAddTitleData_withBlankCountryCode_handlesCorrectly() {
        // Arrange
        HotelResult hotelResult = new HotelResult();
        String countryCode = "";
        
        // Set up real tooltip to avoid serialization issues
        ValueStaysTooltip realTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", realTooltipDom);

        // Act
        transformer.addTitleData(hotelResult, countryCode);

        // Assert
        assertNotNull("TitleData should be set", hotelResult.getTitleData());
        assertNotNull("Title icon should be set", hotelResult.getTitleData().getTitleIcon());
    }

    @Test
    public void testAddTitleData_withNullCountryCode_handlesCorrectly() {
        // Arrange
        HotelResult hotelResult = new HotelResult();
        String countryCode = null;
        
        // Set up real tooltip to avoid serialization issues
        ValueStaysTooltip realTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", realTooltipDom);

        // Act
        transformer.addTitleData(hotelResult, countryCode);

        // Assert
        assertNotNull("TitleData should be set", hotelResult.getTitleData());
        assertNotNull("Title icon should be set", hotelResult.getTitleData().getTitleIcon());
    }

    @Test
    public void testAddTitleData_withInternationalCountryCode_handlesCorrectly() {
        // Arrange
        HotelResult hotelResult = new HotelResult();
        String countryCode = "US";
        
        // Set up real tooltip to avoid serialization issues
        ValueStaysTooltip realTooltipIntl = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipIntl", realTooltipIntl);

        // Act
        transformer.addTitleData(hotelResult, countryCode);

        // Assert
        assertNotNull("TitleData should be set", hotelResult.getTitleData());
        assertNotNull("Title icon should be set", hotelResult.getTitleData().getTitleIcon());
    }

    // ========== CONVERT STAFF INFO TESTS ==========

    @Test
    public void testConvertStaffInfo_withStarHostTrue_setsDesktopIcon() {
        // Arrange
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);

        // Act
        transformer.convertStaffInfo(orchestratorStaffInfo);

        // Assert
        verify(orchestratorStaffInfo).setStarHostIconUrl("http://example.com/star-host-desktop.png");
    }

    @Test
    public void testConvertStaffInfo_withStarHostFalse_doesNotSetIcon() {
        // Arrange
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(false);

        // Act
        transformer.convertStaffInfo(orchestratorStaffInfo);

        // Assert
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
    }

    @Test
    public void testConvertStaffInfo_withStarHostNull_doesNotSetIcon() {
        // Arrange
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(null);

        // Act
        transformer.convertStaffInfo(orchestratorStaffInfo);

        // Assert
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
    }

    @Test
    public void testConvertStaffInfo_withNullStaffInfo_handlesGracefully() {
        // Act & Assert - Should not throw exception
        transformer.convertStaffInfo(null);
    }

    // ========== CONVERT STATIC DETAIL RESPONSE TESTS ==========

    @Test
    public void testConvertStaticDetailResponse_withValidInputsAndCategoryIcon_updatesCategoryIcon() {
        // Arrange
        StaticDetailResponse mockStaticDetailResponse = createWorkingStaticDetailResponse(true, false);
        
        OrchStaticDetailsResponseTransformerDesktop transformerSpy = spy(transformer);
        doReturn(mockStaticDetailResponse).when((OrchStaticDetailResponseTransformer) transformerSpy)
            .convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Act
        StaticDetailResponse result = transformerSpy.convertStaticDetailResponse(
            staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Assert
        assertNotNull("Result should not be null", result);
    }

    @Test
    public void testConvertStaticDetailResponse_withHiddenGemCategory_doesNotUpdateIcon() {
        // Arrange
        StaticDetailResponse mockStaticDetailResponse = createWorkingStaticDetailResponse(true, true);
        
        OrchStaticDetailsResponseTransformerDesktop transformerSpy = spy(transformer);
        doReturn(mockStaticDetailResponse).when((OrchStaticDetailResponseTransformer) transformerSpy)
            .convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Act
        StaticDetailResponse result = transformerSpy.convertStaticDetailResponse(
            staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Assert
        assertNotNull("Result should not be null", result);
    }

    @Test
    public void testConvertStaticDetailResponse_withNullStaticDetailResponse_returnsNull() {
        // Arrange
        OrchStaticDetailsResponseTransformerDesktop transformerSpy = spy(transformer);
        doReturn(null).when((OrchStaticDetailResponseTransformer) transformerSpy)
            .convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Act
        StaticDetailResponse result = transformerSpy.convertStaticDetailResponse(
            staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Assert
        assertNull("Result should be null when super returns null", result);
    }

    @Test
    public void testConvertStaticDetailResponse_withBlankCategoryIconUrl_doesNotUpdateIcon() {
        // Arrange
        ReflectionTestUtils.setField(transformer, "mmtValueStaysCategoryIconUrlDesktop", "");
        StaticDetailResponse mockStaticDetailResponse = createWorkingStaticDetailResponse(true, false);
        
        OrchStaticDetailsResponseTransformerDesktop transformerSpy = spy(transformer);
        doReturn(mockStaticDetailResponse).when((OrchStaticDetailResponseTransformer) transformerSpy)
            .convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Act
        StaticDetailResponse result = transformerSpy.convertStaticDetailResponse(
            staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Assert
        assertNotNull("Result should not be null", result);
    }

    // ========== PRIVATE METHOD TESTS ==========

    @Test
    public void testIsNotAHiddenGemIcon_withHiddenGemCategory_returnsFalse() throws Exception {
        // Arrange
        StaticDetailResponse staticDetailResponse = createWorkingStaticDetailResponse(true, true);
        
        // Use reflection to access private method
        Method method = OrchStaticDetailsResponseTransformerDesktop.class.getDeclaredMethod("isNotAHiddenGemIcon", StaticDetailResponse.class);
        method.setAccessible(true);

        // Act
        boolean result = (boolean) method.invoke(transformer, staticDetailResponse);

        // Assert
        assertFalse("Should return false for hidden gem property", result);
    }

    @Test
    public void testIsNotAHiddenGemIcon_withoutHiddenGemCategory_returnsTrue() throws Exception {
        // Arrange
        StaticDetailResponse staticDetailResponse = createWorkingStaticDetailResponse(true, false);
        
        // Use reflection to access private method
        Method method = OrchStaticDetailsResponseTransformerDesktop.class.getDeclaredMethod("isNotAHiddenGemIcon", StaticDetailResponse.class);
        method.setAccessible(true);

        // Act
        boolean result = (boolean) method.invoke(transformer, staticDetailResponse);

        // Assert
        assertTrue("Should return true for non-hidden gem property", result);
    }

    @Test
    public void testIsNotAHiddenGemIcon_withNullResponse_returnsTrue() throws Exception {
        // Use reflection to access private method
        Method method = OrchStaticDetailsResponseTransformerDesktop.class.getDeclaredMethod("isNotAHiddenGemIcon", StaticDetailResponse.class);
        method.setAccessible(true);

        // Act
        boolean result = (boolean) method.invoke(transformer, (StaticDetailResponse) null);

        // Assert
        assertTrue("Should return true for null response", result);
    }

    @Test
    public void testIsNotAHiddenGemIcon_withNullHotelDetails_returnsTrue() throws Exception {
        // Arrange
        StaticDetailResponse staticDetailResponse = new StaticDetailResponse();
        staticDetailResponse.setHotelDetails(null);
        
        // Use reflection to access private method
        Method method = OrchStaticDetailsResponseTransformerDesktop.class.getDeclaredMethod("isNotAHiddenGemIcon", StaticDetailResponse.class);
        method.setAccessible(true);

        // Act
        boolean result = (boolean) method.invoke(transformer, staticDetailResponse);

        // Assert
        assertTrue("Should return true for null hotel details", result);
    }

    @Test
    public void testIsNotAHiddenGemIcon_withNullCategories_returnsTrue() throws Exception {
        // Arrange
        StaticDetailResponse staticDetailResponse = createWorkingStaticDetailResponse(false, false);
        try {
            staticDetailResponse.getHotelDetails().setCategories(null);
        } catch (Exception e) {
            // If setting categories fails, create a new response
            staticDetailResponse = new StaticDetailResponse();
        }
        
        // Use reflection to access private method
        Method method = OrchStaticDetailsResponseTransformerDesktop.class.getDeclaredMethod("isNotAHiddenGemIcon", StaticDetailResponse.class);
        method.setAccessible(true);

        // Act
        boolean result = (boolean) method.invoke(transformer, staticDetailResponse);

        // Assert
        assertTrue("Should return true for null categories", result);
    }

    // ========== HELPER METHODS ==========

    private StaticDetailResponse createWorkingStaticDetailResponse(boolean hasCategoryIcon, boolean isHiddenGem) {
        StaticDetailResponse staticDetailResponse = new StaticDetailResponse();
        
        try {
            // Create a simple HotelResult as HotelDetails
            HotelResult hotelDetails = new HotelResult();
            
            if (hasCategoryIcon) {
                hotelDetails.setCategoryIcon("original-icon.png");
            }
            
            List<String> categories;
            if (isHiddenGem) {
                categories = Arrays.asList("Category1", Constants.HIDDEN_GEM, "Category2");
            } else {
                categories = Arrays.asList("Category1", "Category2");
            }
            hotelDetails.setCategories(categories);
            
            // Use HotelResult as HotelDetails for simplicity
            staticDetailResponse.setHotelDetails(hotelDetails);
            return staticDetailResponse;
            
        } catch (Exception e) {
            // Fallback - create a basic response
            StaticDetailResponse fallbackResponse = new StaticDetailResponse();
            HotelResult basicHotelDetails = new HotelResult();
            fallbackResponse.setHotelDetails(basicHotelDetails);
            return fallbackResponse;
        }
    }

    // ========== INTEGRATION TESTS ==========

    @Test
    public void testCompleteFlow_initToConvertStaticDetailResponse() {
        // Arrange
        transformer.init();
        
        StaticDetailResponse mockStaticDetailResponse = createWorkingStaticDetailResponse(true, false);
        OrchStaticDetailsResponseTransformerDesktop transformerSpy = spy(transformer);
        doReturn(mockStaticDetailResponse).when((OrchStaticDetailResponseTransformer) transformerSpy)
            .convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Act
        StaticDetailResponse result = transformerSpy.convertStaticDetailResponse(
            staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Assert
        assertNotNull("Complete flow should work", result);
    }

    @Test
    public void testAbstractMethodsReturnExpectedValues() {
        // Test all abstract method implementations
        assertNull("buildCardTitleMap should return null", transformer.buildCardTitleMap());
        assertEquals("getLuxeIcon should return desktop constant", Constants.LUXE_ICON_DESKTOP, transformer.getLuxeIcon());
    }

    // ========== EDGE CASE TESTS ==========

    @Test
    public void testInit_propertyChangeListenerFunctionality() {
        // Act
        transformer.init();

        // Assert
        verify(commonConfig).addPropertyChangeListener(eq("mmtValueStaysTooltipDom"), any());
        verify(commonConfig).addPropertyChangeListener(eq("mmtValueStaysTooltipIntl"), any());
    }

    @Test
    public void testConvertStaticDetailResponse_withNullHotelDetails_handlesGracefully() {
        // Arrange
        StaticDetailResponse mockStaticDetailResponse = new StaticDetailResponse();
        mockStaticDetailResponse.setHotelDetails(null);
        
        OrchStaticDetailsResponseTransformerDesktop transformerSpy = spy(transformer);
        doReturn(mockStaticDetailResponse).when((OrchStaticDetailResponseTransformer) transformerSpy)
            .convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Act
        StaticDetailResponse result = transformerSpy.convertStaticDetailResponse(
            staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Assert
        assertNotNull("Result should not be null", result);
        assertNull("HotelDetails should remain null", result.getHotelDetails());
    }

    // ========== MULTIPLE INVOCATION CONSISTENCY TESTS ==========

    @Test
    public void testGetLuxeIcon_multipleInvocations_consistentResults() {
        // Act & Assert
        String result1 = transformer.getLuxeIcon();
        String result2 = transformer.getLuxeIcon();
        
        assertEquals("Multiple calls should return same result", result1, result2);
        assertEquals("Should always return desktop constant", Constants.LUXE_ICON_DESKTOP, result1);
    }

    @Test
    public void testBuildCardTitleMap_multipleInvocations_consistentResults() {
        // Act & Assert
        Map<String, String> result1 = transformer.buildCardTitleMap();
        Map<String, String> result2 = transformer.buildCardTitleMap();
        
        assertNull("Should always return null", result1);
        assertNull("Should always return null", result2);
    }

    // ========== BOUNDARY VALUE TESTS ==========

    @Test
    public void testAddTitleData_withEmptyCountryCode_usesDomesticLogic() {
        // Arrange
        HotelResult hotelResult = new HotelResult();
        String countryCode = "";
        
        // Set up real tooltip to avoid serialization issues
        ValueStaysTooltip realTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", realTooltipDom);

        // Act
        transformer.addTitleData(hotelResult, countryCode);

        // Assert
        assertNotNull("TitleData should be set for empty country code", hotelResult.getTitleData());
    }

    @Test
    public void testConvertStaffInfo_withBooleanFalse_doesNotSetIcon() {
        // Arrange - test Boolean.FALSE specifically
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(Boolean.FALSE);

        // Act
        transformer.convertStaffInfo(orchestratorStaffInfo);

        // Assert
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
    }

    @Test
    public void testConvertStaffInfo_withBooleanTrue_setsIcon() {
        // Arrange - test Boolean.TRUE specifically
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(Boolean.TRUE);

        // Act
        transformer.convertStaffInfo(orchestratorStaffInfo);

        // Assert
        verify(orchestratorStaffInfo).setStarHostIconUrl("http://example.com/star-host-desktop.png");
    }

    // ========== COVERAGE OPTIMIZATION TESTS ==========

    @Test
    public void testConvertStaticDetailResponse_withNullCategoryIcon_doesNotUpdateIcon() {
        // Arrange
        StaticDetailResponse mockStaticDetailResponse = createWorkingStaticDetailResponse(false, false);
        
        OrchStaticDetailsResponseTransformerDesktop transformerSpy = spy(transformer);
        doReturn(mockStaticDetailResponse).when((OrchStaticDetailResponseTransformer) transformerSpy)
            .convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Act
        StaticDetailResponse result = transformerSpy.convertStaticDetailResponse(
            staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        // Assert
        assertNotNull("Result should not be null", result);
    }

    @Test
    public void testAddTitleData_withVariousCountryCodes_exercisesDifferentPaths() {
        // Test multiple country codes to exercise different paths
        String[] countryCodes = {"IN", "US", "AE", "", null, "GB"};
        
        for (String countryCode : countryCodes) {
            HotelResult hotelResult = new HotelResult();
            
            // Set up real tooltips to avoid serialization issues
            ValueStaysTooltip realTooltipDom = new ValueStaysTooltip();
            ValueStaysTooltip realTooltipIntl = new ValueStaysTooltip();
            ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", realTooltipDom);
            ReflectionTestUtils.setField(transformer, "valueStaysTooltipIntl", realTooltipIntl);

            // Act
            transformer.addTitleData(hotelResult, countryCode);

            // Assert
            assertNotNull("TitleData should be set for country: " + countryCode, hotelResult.getTitleData());
        }
    }
} 