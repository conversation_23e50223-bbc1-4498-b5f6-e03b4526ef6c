package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.request.flyfish.FlyfishReviewData;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.dayuse.Slot;
import com.mmt.hotels.model.response.flyfish.ReviewSummary;
import com.mmt.hotels.model.response.persuasion.HotelPersuasions;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.HotelPrice;
import com.mmt.hotels.model.response.pricing.RoomTypeDetails;
import com.mmt.hotels.model.response.pricing.Tax;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsResponseTransformerAndroidTest {
	
	@InjectMocks
	SearchHotelsResponseTransformerAndroid searchHotelsResponseTransformerAndroid;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	private PolyglotService polyglotService;

	Hotel hotel;

	@Mock
	private Utility utility;

	@Mock
	PersuasionUtil persuasionUtil;

	@Before
	public void init() throws IOException {
			ObjectMapper mapper = new ObjectMapper();
			mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
			hotel = mapper.readValue(availPriceRequest, Hotel.class);
			MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
			myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
			myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
			myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
			ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid,"myBizStaticCard",myBizStaticCard);
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest(){
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
		searchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().size() > 0);
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().get(0).getText().contains(secondaryPersuasion));
	}

	@Test
	public void testBuildQuickBookCard() {
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.buildQuickBookCard(new QuickBookInfo()));
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public  void testBuildSlotDetails(){
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		List<RoomTypeDetails> roomTypeDetailsList = new ArrayList<>();
		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
		roomTypeDetailsList.add(roomTypeDetails);
		searchWrapperHotelEntity.setRecommendedRoomTypeDetails(roomTypeDetailsList);
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.buildSlotDetails(searchWrapperHotelEntity, "", new ListingSearchRequest()));
	}

	@Test
	public  void testWithBuildSlotDetails(){
		ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "commonResponseTransformer", mock(CommonResponseTransformer.class));
		ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "missingSlotDetails", mock(MissingSlotDetail.class));
		com.mmt.hotels.clientgateway.request.dayuse.Slot slot = new com.mmt.hotels.clientgateway.request.dayuse.Slot();
		slot.setTimeSlot(10);
		slot.setDuration(3);
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setSlot(slot);
		ListingSearchRequest listingSearchRequest = new ListingSearchRequest();
		listingSearchRequest.setSearchCriteria(searchCriteria);
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		List<RoomTypeDetails> roomTypeDetailsList = new ArrayList<>();
		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
		Slot roomSlot = new Slot();
		roomSlot.setDuration(3);
		roomSlot.setTimeSlot("10");
		roomTypeDetails.setSlot(roomSlot);
		roomTypeDetailsList.add(roomTypeDetails);
		searchWrapperHotelEntity.setRecommendedRoomTypeDetails(roomTypeDetailsList);
		searchWrapperHotelEntity.getRecommendedRoomTypeDetails().get(0).setTotalDisplayFare(new DisplayFare());

		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setDisplayPrice(12d);
		displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
		displayPriceBreakDown.setNonDiscountedPrice(12d);
		displayPriceBreakDown.setSavingPerc(5.05);
		displayPriceBreakDown.setBasePrice(13.05);
		displayPriceBreakDown.setHotelTax(4d);
		displayPriceBreakDown.setMmtDiscount(1d);
		displayPriceBreakDown.setCdfDiscount(1d);
		displayPriceBreakDown.setWallet(12d);
		displayPriceBreakDown.setPricingKey("key");
		displayPriceBreakDown.setCouponInfo(new BestCoupon());
		displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
		displayPriceBreakDown.getCouponInfo().setCouponCode("code");
		displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
		displayPriceBreakDown.getCouponInfo().setType("promotional");
		displayPriceBreakDown.getCouponInfo().setDiscountAmount(100.0);

		List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
		displayPriceBreakDownList.add(new DisplayPriceBreakDown());
		displayPriceBreakDownList.get(0).setDisplayPrice(12d);
		displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
		displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
		displayPriceBreakDownList.get(0).setSavingPerc(5.05);
		displayPriceBreakDownList.get(0).setBasePrice(13.05);
		displayPriceBreakDownList.get(0).setHotelTax(4d);
		displayPriceBreakDownList.get(0).setMmtDiscount(1d);
		displayPriceBreakDownList.get(0).setCdfDiscount(1d);
		displayPriceBreakDownList.get(0).setWallet(12d);
		displayPriceBreakDownList.get(0).setPricingKey("key");
		displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
		displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
		displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
		displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
		displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
		displayPriceBreakDownList.get(0).getCouponInfo().setDiscountAmount(100.0);

		searchWrapperHotelEntity.getRecommendedRoomTypeDetails().get(0).getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
		searchWrapperHotelEntity.getRecommendedRoomTypeDetails().get(0).getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.buildSlotDetails(searchWrapperHotelEntity, "", listingSearchRequest));
	}

	@Test
	public void buildPriceDetailForDayUseTest(){
		DisplayFare displayFare = new DisplayFare();
		Tax tax = new Tax();
		tax.setValue(1.2);
		displayFare.setTax(tax);
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceNoTax(1.2);
		slashedPrice.setSellingPriceWithTax(1.3);
		displayFare.setSlashedPrice(slashedPrice);
		PriceDetail priceDetail = searchHotelsResponseTransformerAndroid.buildPriceDetailForDayUse(displayFare);
		Assert.assertNotNull(priceDetail);
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerAndroid.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}

	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		hotelEntity.setDetailDeeplinkUrl("test");
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerAndroid.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		Assert.assertNotNull(staticCard);
		assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}

	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerAndroid.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		junit.framework.Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		junit.framework.Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps()));
	}
	@Test
		public void fetchHydraSegment_ReturnsSegmentsFromFirstSource_WhenFirstSourceHasSegments() {
			// Given
			ListingPagePersonalizationResponsBO listingPageResponseBO = mock(ListingPagePersonalizationResponsBO.class);
			CommonModifierResponse commonModifierResponse = mock(CommonModifierResponse.class);
			List<String> expectedSegments = Arrays.asList("segment1", "segment2");
			when(listingPageResponseBO.getHydraSegments()).thenReturn(expectedSegments);

			// When
			List<String> result = searchHotelsResponseTransformerAndroid.fetchHydraSegment(listingPageResponseBO, commonModifierResponse);

			// Then
			assertEquals(expectedSegments, result);
		}

		@Test
		public void fetchHydraSegment_ReturnsSegmentsFromSecondSource_WhenFirstSourceHasNoSegments() {
			// Given
			ListingPagePersonalizationResponsBO listingPageResponseBO = mock(ListingPagePersonalizationResponsBO.class);
			CommonModifierResponse commonModifierResponse = mock(CommonModifierResponse.class);
			HydraResponse hydraResponse = mock(HydraResponse.class);
			List<String> expectedSegments = Arrays.asList("segment1", "segment2");
			when(listingPageResponseBO.getHydraSegments()).thenReturn(null);
			when(commonModifierResponse.getHydraResponse()).thenReturn(hydraResponse);
			when(hydraResponse.getHydraMatchedSegment()).thenReturn(expectedSegments.stream().collect(Collectors.toSet()));

			// When
			List<String> result = searchHotelsResponseTransformerAndroid.fetchHydraSegment(listingPageResponseBO, commonModifierResponse);

			// Then
			boolean matchedSegments = expectedSegments.containsAll(result) && result.containsAll(expectedSegments);
			assertTrue(matchedSegments);
		}

		@Test
		public void fetchHydraSegment_ReturnsNull_WhenBothSourcesHaveNoSegments() {
			// Given
			ListingPagePersonalizationResponsBO listingPageResponseBO = mock(ListingPagePersonalizationResponsBO.class);
			CommonModifierResponse commonModifierResponse = mock(CommonModifierResponse.class);
			when(listingPageResponseBO.getHydraSegments()).thenReturn(null);
			when(commonModifierResponse.getHydraResponse()).thenReturn(null);

			// When
			List<String> result = searchHotelsResponseTransformerAndroid.fetchHydraSegment(listingPageResponseBO, commonModifierResponse);

			// Then
			assertNull(result);
		}

	@Test
	public void testBuildReviewSummaryForCombineOTAFlow_TRUE() {
		// Arrange
		ReviewSummary reviewSummaryUgc = createReviewSummaryUgc();
		CommonModifierResponse commonModifierResponse = mock(CommonModifierResponse.class);
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put(COMBINED_OTA_FLOW, TRUE);
		when(commonModifierResponse.getExpDataMap()).thenReturn(expDataMap);

		// Act
		FlyfishReviewData result = searchHotelsResponseTransformerAndroid.buildReviewSummary(reviewSummaryUgc, commonModifierResponse);

		// Assert
		assertReviewSummary(result, "GI_EXP");
	}

	@Test
	public void testBuildReviewSummaryForCombineOTAFlow_FALSE() {
		// Arrange
		ReviewSummary reviewSummaryUgc = createReviewSummaryUgc();
		CommonModifierResponse commonModifierResponse = mock(CommonModifierResponse.class);
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put(COMBINED_OTA_FLOW, FALSE);
		when(commonModifierResponse.getExpDataMap()).thenReturn(expDataMap);

		// Act
		FlyfishReviewData result = searchHotelsResponseTransformerAndroid.buildReviewSummary(reviewSummaryUgc, commonModifierResponse);

		// Assert
		assertReviewSummary(result, "GI");
	}

	private ReviewSummary createReviewSummaryUgc() {
		ReviewSummary reviewSummaryUgc = new ReviewSummary();
		reviewSummaryUgc.setHotelRating(4.5);
		reviewSummaryUgc.setReviewCount(100);
		reviewSummaryUgc.setOta("GI_EXP");
		reviewSummaryUgc.setRatingCount(50);
		reviewSummaryUgc.setRatingWiseCount(new HashMap<>());
		reviewSummaryUgc.setIsNewListing(true);
		return reviewSummaryUgc;
	}

	private void assertReviewSummary(FlyfishReviewData result, String expectedOta) {
		assertNotNull(result);
		assertEquals(4.5, result.getHotelRating(), 0);
		assertEquals(100, result.getReviewCount());
		assertEquals(expectedOta, result.getOta());
		assertEquals(50, result.getRatingCount());
	}

}