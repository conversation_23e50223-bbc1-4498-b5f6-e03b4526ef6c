package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.enums.AvailabilityStatus;
import com.gommt.hotels.orchestrator.detail.enums.RoomType;
import com.gommt.hotels.orchestrator.detail.model.response.*;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.common.RangePrice;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.*;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.da.AvailDetails;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AdditionalFees;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.RoomUpsellConfig;
import com.mmt.hotels.clientgateway.pms.RoomViewFilterConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.*;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.clientgateway.util.Utility;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for OrchSearchRoomsResponseTransformer in GI project
 * 
 * This test class provides extensive coverage for:
 * - Main transformation methods
 * - Static method testing 
 * - Utility method validation
 * - Core business logic verification
 * - Edge case handling
 * - Integration scenarios
 * 
 * Coverage includes both positive and negative test cases to ensure robust error handling
 * and proper business logic implementation.
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchRoomsResponseTransformerTest {

    @Mock
    private Utility utility;
    
    @Mock
    private DateUtil dateUtil;
    
    @Mock
    private MobConfigHelper mobConfigHelper;
    
    @Mock
    private SearchRoomsMediaHelper searchRoomsMediaHelper;
    
    @Mock
    private RoomInfoHelper roomInfoHelper;
    
    @Mock
    private SearchRoomsPriceHelper searchRoomsPriceHelper;

    @InjectMocks
    private OrchSearchRoomsResponseTransformerAndroid transformer;

    @Mock
    private ReArchUtility reArchUtility;

    private HotelDetailsResponse validHotelDetailsResponse;
    private SearchRoomsRequest validSearchRoomsRequest;
    private SearchCriteria validSearchCriteria;
    private RequestDetails validRequestDetails;
    private CommonModifierResponse validCommonModifierResponse;

    @Before
    public void setUp() {
        transformer = new OrchSearchRoomsResponseTransformerAndroid();
        
        // Set up reflective fields for testing
        ReflectionTestUtils.setField(transformer, "utility", utility);
        ReflectionTestUtils.setField(transformer, "dateUtil", dateUtil);
        ReflectionTestUtils.setField(transformer, "mobConfigHelper", mobConfigHelper);
        ReflectionTestUtils.setField(transformer, "searchRoomsMediaHelper", searchRoomsMediaHelper);
        ReflectionTestUtils.setField(transformer, "roomInfoHelper", roomInfoHelper);
        ReflectionTestUtils.setField(transformer, "searchRoomsPriceHelper", searchRoomsPriceHelper);
        ReflectionTestUtils.setField(transformer, "reArchUtility", reArchUtility);
        
        // Mock basic utility behaviors
//        when(utility.isLuxeHotel(any())).thenReturn(false);
//        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
//        when(searchRoomsMediaHelper.extractRoomImagesFromMedia(any(), anyString())).thenReturn(Arrays.asList("image1.jpg", "image2.jpg"));
//        when(searchRoomsMediaHelper.populateMedia(any(), any(), anyString())).thenReturn(Arrays.asList(createBasicMediaData()));
//        when(roomInfoHelper.transformRoomHighlights(any(), any(), anyBoolean(), anyBoolean())).thenReturn(Arrays.asList(createBasicRoomHighlight()));
        
        setupValidTestData();
    }

    // ==================== Setup Helper Methods ====================
    
    private void setupValidTestData() {
        validHotelDetailsResponse = createValidHotelDetailsResponse();
        validSearchRoomsRequest = createValidSearchRoomsRequest();
        validSearchCriteria = createValidSearchCriteria();
        validRequestDetails = createValidRequestDetails();
        validCommonModifierResponse = createValidCommonModifierResponse();
    }
    
    private HotelDetailsResponse createValidHotelDetailsResponse() {
        HotelDetailsResponse response = new HotelDetailsResponse();
        response.setHotelDetails(createValidHotelDetails());
        return response;
    }
    
    private HotelDetails createValidHotelDetails() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("HOTEL");
        hotelDetails.setListingType("ENTIRE");
        hotelDetails.setRooms(Arrays.asList(createValidRoom()));
        hotelDetails.setHotelRateFlags(createValidHotelRateFlags());
        hotelDetails.setCategories(new HashSet<>());
        
        return hotelDetails;
    }
    
    private Rooms createValidRoom() {
        Rooms room = new Rooms();
        room.setCode("R001");
        room.setName("Deluxe Room");
        room.setDesc("Comfortable deluxe room");
        room.setType("EXACT");
        room.setBaseRoom(true);
        room.setRoomInfo(createValidRoomInfo());
        room.setRatePlans(Arrays.asList(createValidRatePlan()));
        return room;
    }
    
         private com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo createValidRoomInfo() {
         com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo roomInfo = new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo();
        roomInfo.setMaxGuestCount(4);
        roomInfo.setMaxAdultCount(2);
        roomInfo.setMaxChildCount(2);
        roomInfo.setBedCount(1);
        roomInfo.setBedRoomCount(1);
        roomInfo.setExtraBedCount(0);
        roomInfo.setRoomSize("25 sqm");
        roomInfo.setSellableType("ROOM");
        roomInfo.setRoomViewName("City View");
        roomInfo.setRoomFlags(createValidRoomFlags());
        roomInfo.setRoomArrangementMap(createValidRoomArrangementMap());
        roomInfo.setHighlightedAmenities(Arrays.asList(createValidAmenityGroup()));
        
        return roomInfo;
    }
    
    private RoomFlags createValidRoomFlags() {
        RoomFlags flags = new RoomFlags();
        flags.setMasterRoom(true);
        return flags;
    }
    
    private Map<String, List<ArrangementInfo>> createValidRoomArrangementMap() {
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        
        List<ArrangementInfo> bedArrangements = new ArrayList<>();
        ArrangementInfo bedInfo = new ArrangementInfo();
        bedInfo.setType("King Bed");
        bedInfo.setCount(1);
        bedArrangements.add(bedInfo);
        
        arrangementMap.put("BEDS", bedArrangements);
        return arrangementMap;
    }
    
    private AmenityGroup createValidAmenityGroup() {
        AmenityGroup amenityGroup = new AmenityGroup();
        amenityGroup.setAmenities(Arrays.asList(createValidAmenity()));
        return amenityGroup;
    }
    
    private Amenity createValidAmenity() {
        Amenity amenity = new Amenity();
        amenity.setAttributeName("Free WiFi");
        return amenity;
    }
    
    private RatePlan createValidRatePlan() {
        RatePlan ratePlan = new RatePlan();
        ratePlan.setCode("RP001");
                 ratePlan.setDescription("Standard Rate");
         ratePlan.setPrice(createValidPriceDetail());
         ratePlan.setAvailDetail(createValidAvailDetails());
        return ratePlan;
    }
    
    private PriceDetail createValidPriceDetail() {
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setPricePerOccupancy(Arrays.asList(createValidOccupancyDetails()));
        return priceDetail;
    }
    
         private OccupancyDetails createValidOccupancyDetails() {
         OccupancyDetails occupancyDetails = new OccupancyDetails();
         occupancyDetails.setAdult(2);
         occupancyDetails.setChild(0);
         return occupancyDetails;
     }
     
     private AvailDetails createValidAvailDetails() {
         AvailDetails availDetails = new AvailDetails();
         availDetails.setOccupancyDetails(createValidOccupancyDetails());
         return availDetails;
     }
    
    private HotelRateFlags createValidHotelRateFlags() {
        HotelRateFlags flags = new HotelRateFlags();
        flags.setAltAcco(false);
        flags.setSpotlightApplicable(false);
        return flags;
    }
    
         private SearchRoomsRequest createValidSearchRoomsRequest() {
         SearchRoomsRequest request = new SearchRoomsRequest();
         // Note: SearchRoomsRequest may not have setHotelId method - this is acceptable for test
         return request;
     }
     
     private SearchCriteria createValidSearchCriteria() {
         SearchCriteria criteria = new SearchCriteria();
         criteria.setCheckIn("2024-01-15");
         criteria.setCheckOut("2024-01-17");
         criteria.setCurrency("INR");
         return criteria;
     }
     
     private RequestDetails createValidRequestDetails() {
         RequestDetails details = new RequestDetails();
         details.setFunnelSource("HOTELS");
         return details;
     }
     
     private CommonModifierResponse createValidCommonModifierResponse() {
         CommonModifierResponse response = new CommonModifierResponse();
         response.setExpDataMap(new LinkedHashMap<String, String>());
         return response;
     }
     
     private MediaData createBasicMediaData() {
         MediaData mediaData = new MediaData();
         // Note: MediaData may have different method for setting images
         return mediaData;
     }
    
    private RoomHighlight createBasicRoomHighlight() {
        RoomHighlight highlight = new RoomHighlight();
        highlight.setText("Free WiFi");
        return highlight;
    }

    // ==================== Static Method Tests ====================

    @Test
    public void should_ReturnTrue_When_PropertyTypeIsHotel() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("HOTEL");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertTrue("Should return true for hotel property type", result);
    }

    @Test
    public void should_ReturnTrue_When_PropertyTypeIsResort() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("RESORT");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertTrue("Should return true for resort property type", result);
    }

    @Test
    public void should_ReturnFalse_When_PropertyTypeIsApartment() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("APARTMENT");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertFalse("Should return false for apartment property type", result);
    }

    @Test
    public void should_ReturnFalse_When_PropertyTypeIsVilla() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("VILLA");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertFalse("Should return false for villa property type", result);
    }

    @Test
    public void should_ReturnFalse_When_PropertyTypeIsHostel() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("HOSTEL");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertFalse("Should return false for hostel property type", result);
    }

    @Test
    public void should_ReturnFalse_When_PropertyTypeIsNull() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType(null);

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertFalse("Should return false for null property type", result);
    }

    @Test
    public void should_ReturnFalse_When_PropertyTypeIsEmpty() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertFalse("Should return false for empty property type", result);
    }

    @Test
    public void should_ReturnFalse_When_HotelDetailsIsNull() {
        // Given
        HotelDetails hotelDetails = null;

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertFalse("Should return false for null hotel details", result);
    }

    @Test
    public void should_HandleCaseInsensitivity_When_PropertyTypeIsLowercase() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("hotel"); // lowercase

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertTrue("Should return true for lowercase hotel (case insensitive)", result);
    }

    @Test
    public void should_HandleCaseInsensitivity_When_PropertyTypeIsMixedCase() {
        // Given
        HotelDetails hotelDetails1 = new HotelDetails();
        hotelDetails1.setPropertyType("Hotel");
        
        HotelDetails hotelDetails2 = new HotelDetails();
        hotelDetails2.setPropertyType("RESORT");
        
        HotelDetails hotelDetails3 = new HotelDetails();
        hotelDetails3.setPropertyType("resort");

        // When & Then
        assertTrue("Should return true for mixed case Hotel", 
            OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails1));
        assertTrue("Should return true for uppercase RESORT", 
            OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails2));
        assertTrue("Should return true for lowercase resort", 
            OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails3));
    }

    // ==================== setCanTranslateFlag Static Method Tests ====================

    @Test
    public void should_SetCanTranslateFlag_When_SupplierCodeMatches() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList("SUPPLIER1", "SUPPLIER2", "SUPPLIER3");
        String supplierCode = "SUPPLIER2";

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, supplierCode);

        // Then
        assertTrue("Should set canTranslate to true when supplier code matches", ratePlan.isCanTranslate());
    }

    @Test
    public void should_NotSetCanTranslateFlag_When_SupplierCodeDoesNotMatch() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList("SUPPLIER1", "SUPPLIER2", "SUPPLIER3");
        String supplierCode = "SUPPLIER4";

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, supplierCode);

        // Then
        assertFalse("Should not set canTranslate when supplier code doesn't match", ratePlan.isCanTranslate());
    }

    @Test
    public void should_HandleNullSupplierCode_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList("SUPPLIER1", "SUPPLIER2");
        String supplierCode = null;

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, supplierCode);

        // Then
        assertFalse("Should not set canTranslate when supplier code is null", ratePlan.isCanTranslate());
    }

    @Test
    public void should_HandleEmptySupplierCodeList_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList(); // Empty list
        String supplierCode = "SUPPLIER1";

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, supplierCode);

        // Then
        assertFalse("Should not set canTranslate when supplier codes list is empty", ratePlan.isCanTranslate());
    }

    @Test
    public void should_HandleNullSupplierCodeList_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = null;
        String supplierCode = "SUPPLIER1";

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, supplierCode);

        // Then
        assertFalse("Should not set canTranslate when supplier codes list is null", ratePlan.isCanTranslate());
    }

    @Test
    public void should_HandleNullRatePlan_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = null;
        List<String> translateEnabledSupplierCodes = Arrays.asList("SUPPLIER1");
        String supplierCode = "SUPPLIER1";

        // When
        try {
            OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, supplierCode);
            // Should not throw exception - documents graceful handling
        } catch (Exception e) {
            fail("Should handle null ratePlan gracefully, but threw: " + e.getMessage());
        }

        // Then
        // Test passes if no exception is thrown
    }

    @Test
    public void should_BeCaseSensitive_When_MatchingSupplierCodes() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList("SUPPLIER1", "SUPPLIER2");
        String supplierCode = "supplier1"; // lowercase

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, supplierCode);

        // Then
        assertFalse("Should be case-sensitive when matching supplier codes", ratePlan.isCanTranslate());
    }

    // ==================== Main Conversion Method Tests ====================

    @Test
    public void should_ReturnEmptyResponse_When_HotelDetailsResponseIsNull() {
        // When
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
            validSearchRoomsRequest, null, new HashMap<>(), new ArrayList<>(),
            validSearchCriteria, new ArrayList<>(), validRequestDetails, validCommonModifierResponse);

        // Then
        assertNotNull("Should return a response object", result);
        assertNull("Should have null exact rooms", result.getExactRooms());
    }

    @Test
    public void should_ReturnEmptyResponse_When_HotelDetailsIsNull() {
        // Given
        HotelDetailsResponse response = new HotelDetailsResponse();
        response.setHotelDetails(null);

        // When
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
            validSearchRoomsRequest, response, new HashMap<>(), new ArrayList<>(),
            validSearchCriteria, new ArrayList<>(), validRequestDetails, validCommonModifierResponse);

        // Then
        assertNotNull("Should return a response object", result);
        assertNull("Should have null exact rooms", result.getExactRooms());
    }

    // ==================== amenitiesFilter Method Tests ====================

    @Test
    public void should_ReturnEmptyList_When_AmenityGroupIsNull() {
        // Given
        when(mobConfigHelper.getRoomUpsellConfig()).thenReturn(createValidRoomUpsellConfig());

        // When
        List<String> result = ReflectionTestUtils.invokeMethod(transformer, "amenitiesFilter", (AmenityGroup) null);

        // Then
        assertNotNull("Should return a list", result);
        assertTrue("Should return empty list when amenity group is null", result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_RoomUpsellConfigIsNull() {
        // Given
        when(mobConfigHelper.getRoomUpsellConfig()).thenReturn(null);

        // When
        List<String> result = ReflectionTestUtils.invokeMethod(transformer, "amenitiesFilter", createValidAmenityGroup());

        // Then
        assertNotNull("Should return a list", result);
        assertTrue("Should return empty list when room upsell config is null", result.isEmpty());
    }

    private RoomUpsellConfig createValidRoomUpsellConfig() {
        RoomUpsellConfig config = new RoomUpsellConfig();
        config.roomViewsFilterToggle = Arrays.asList(createValidRoomViewFilterConfig());
        return config;
    }

    private RoomViewFilterConfig createValidRoomViewFilterConfig() {
        RoomViewFilterConfig filterConfig = new RoomViewFilterConfig();
        filterConfig.setFilterCode("FREE_WIFI");
        return filterConfig;
    }

    // ==================== buildBedInfoText Method Tests ====================

    @Test
    public void should_ReturnNull_When_RoomInfoIsNull() {
        // When
        String result = transformer.buildBedInfoText(null);

        // Then
        assertNull("Should return null when room info is null", result);
    }

    @Test
    public void should_ReturnNull_When_NoBedInfoAvailable() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        // No bed info set

        // When
        String result = transformer.buildBedInfoText(roomInfo);

        // Then
        assertNull("Should return null when no bed info available", result);
    }

    @Test
    public void should_HandleZeroBedCount_When_BuildingBedInfoText() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        
        Map<String, List<ArrangementInfo>> roomArrangementMap = new HashMap<>();
        List<ArrangementInfo> bedsList = new ArrayList<>();
        
        ArrangementInfo bedArrangement = new ArrangementInfo();
        bedArrangement.setType("Sofa Bed");
        bedArrangement.setCount(0); // Zero count
        bedsList.add(bedArrangement);
        
        roomArrangementMap.put("BEDS", bedsList);
        roomInfo.setRoomArrangementMap(roomArrangementMap);

        // When
        String result = transformer.buildBedInfoText(roomInfo);

        // Then
        assertNull("Should return null when bed count is zero", result);
    }

    @Test
    public void should_HandleNullBedType_When_BuildingBedInfoText() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        
        Map<String, List<ArrangementInfo>> roomArrangementMap = new HashMap<>();
        List<ArrangementInfo> bedsList = new ArrayList<>();
        
        ArrangementInfo bedArrangement = new ArrangementInfo();
        bedArrangement.setType(null); // Null type
        bedArrangement.setCount(1);
        bedsList.add(bedArrangement);
        
        roomArrangementMap.put("BEDS", bedsList);
        roomInfo.setRoomArrangementMap(roomArrangementMap);

        // When
        String result = transformer.buildBedInfoText(roomInfo);

        // Then
        assertNull("Should return null when bed type is null", result);
    }

    @Test
    public void should_HandleEmptyBedType_When_BuildingBedInfoText() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        
        Map<String, List<ArrangementInfo>> roomArrangementMap = new HashMap<>();
        List<ArrangementInfo> bedsList = new ArrayList<>();
        
        ArrangementInfo bedArrangement = new ArrangementInfo();
        bedArrangement.setType(""); // Empty type
        bedArrangement.setCount(1);
        bedsList.add(bedArrangement);
        
        roomArrangementMap.put("BEDS", bedsList);
        roomInfo.setRoomArrangementMap(roomArrangementMap);

        // When
        String result = transformer.buildBedInfoText(roomInfo);

        // Then
        assertNull("Should return null when bed type is empty", result);
    }

    // ==================== buildGoTribeInfo Method Tests ====================

    @Test
    public void should_ReturnNull_When_BlackInfoIsNull() {
        // Given
        BlackInfo blackInfo = null;
        String title = "Test Title";
        String subTitle = "Test Subtitle";

        // When
        GoTribeInfo result = transformer.buildGoTribeInfo(blackInfo, title, subTitle);

        // Then
        assertNull("Should return null when black info is null", result);
    }

    @Test
    public void should_ReturnNull_When_InclusionsListIsEmpty() {
        // Given
        BlackInfo blackInfo = new BlackInfo();
        blackInfo.setInclusionsList(new ArrayList<>());
        String title = "Test Title";
        String subTitle = "Test Subtitle";

        // When
        GoTribeInfo result = transformer.buildGoTribeInfo(blackInfo, title, subTitle);

        // Then
        assertNull("Should return null when inclusions list is empty", result);
    }

    @Test
    public void should_ReturnNull_When_InclusionsListIsNull() {
        // Given
        BlackInfo blackInfo = new BlackInfo();
        blackInfo.setInclusionsList(null);
        String title = "Test Title";
        String subTitle = "Test Subtitle";

        // When
        GoTribeInfo result = transformer.buildGoTribeInfo(blackInfo, title, subTitle);

        // Then
        assertNull("Should return null when inclusions list is null", result);
    }

    private com.mmt.hotels.model.response.pricing.Inclusion createValidBookedInclusion() {
        com.mmt.hotels.model.response.pricing.Inclusion inclusion = new com.mmt.hotels.model.response.pricing.Inclusion();
        inclusion.setValue("Free WiFi");
        inclusion.setIconUrl("wifi-icon.png");
        return inclusion;
    }

    // ==================== buildUpSellDetails Method Tests ====================

    @Test
    public void should_ReturnNull_When_DiffAmountIsZero() {
        // When
        UpSellDetails result = transformer.buildUpSellDetails("EP", true, "CP", true, 0.0);

        // Then
        assertNull("Should return null when diff amount is zero", result);
    }

    @Test
    public void should_ReturnNull_When_CheapestMealPlanCodeIsEmpty() {
        // When
        UpSellDetails result = transformer.buildUpSellDetails("", true, "CP", true, 100.0);

        // Then
        assertNull("Should return null when cheapest meal plan code is empty", result);
    }

    @Test
    public void should_ReturnNull_When_CurrentMealPlanCodeIsEmpty() {
        // When
        UpSellDetails result = transformer.buildUpSellDetails("EP", true, "", true, 100.0);

        // Then
        assertNull("Should return null when current meal plan code is empty", result);
    }

    @Test
    public void should_ReturnNull_When_CheapestMealPlanCodeIsNull() {
        // When
        UpSellDetails result = transformer.buildUpSellDetails(null, true, "CP", true, 100.0);

        // Then
        assertNull("Should return null when cheapest meal plan code is null", result);
    }

    @Test
    public void should_ReturnNull_When_CurrentMealPlanCodeIsNull() {
        // When
        UpSellDetails result = transformer.buildUpSellDetails("EP", true, null, true, 100.0);

        // Then
        assertNull("Should return null when current meal plan code is null", result);
    }

    @Test
    public void should_TransformOffersCorrectly_When_ValidOffersProvided() {
        // Given
        List<RangePrice> offers = Arrays.asList(createValidRangePrice());

        // When
        List<OfferDetail> result = ReflectionTestUtils.invokeMethod(transformer, "transformOffers", offers);

        // Then
        assertNotNull("Should return a list", result);
        assertFalse("Should have transformed offers", result.isEmpty());
        
        OfferDetail offerDetail = result.get(0);
        assertEquals("Should have correct long text", "Long offer text", offerDetail.getLongText());
        assertEquals("Should have correct short text", "Short text", offerDetail.getShortText());
    }

    private RangePrice createValidRangePrice() {
        RangePrice offer = new RangePrice();
        offer.setLongText("Long offer text");
        offer.setShortText("Short text");
        offer.setOfferType("DISCOUNT");
        offer.setPriority(1);
        offer.setTncLink("http://tnc.com");
        offer.setIconUrl("http://icon.com");
        return offer;
    }

    @Test
    public void should_HandleWhitespaceInPropertyTypes_When_CheckingHotelOrResort() {
        // Test property types with whitespace
        String[] propertyTypesWithWhitespace = {
            " HOTEL", "HOTEL ", " HOTEL ", "HO TEL", 
            " RESORT", "RESORT ", " RESORT ", "RE SORT"
        };

        for (String propertyType : propertyTypesWithWhitespace) {
            HotelDetails hotelDetails = new HotelDetails();
            hotelDetails.setPropertyType(propertyType);
            boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);
            
            // Document behavior with whitespace
            assertFalse("Should return false for property type with whitespace: '" + propertyType + "'", result);
        }
    }

    @Test
    public void should_HandleSpecialCharactersInSupplierCodes_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList("SUPPLIER-1", "SUPPLIER_2", "SUPPLIER.3");
        
        // Test matching with special characters
        String[] testCodes = {"SUPPLIER-1", "SUPPLIER_2", "SUPPLIER.3", "SUPPLIER/4"};
        boolean[] expectedResults = {true, true, true, false};

        for (int i = 0; i < testCodes.length; i++) {
            SelectRoomRatePlan testRatePlan = new SelectRoomRatePlan();
            OrchSearchRoomsResponseTransformer.setCanTranslateFlag(testRatePlan, translateEnabledSupplierCodes, testCodes[i]);
            
            assertEquals("Special character handling for: " + testCodes[i], 
                        expectedResults[i], testRatePlan.isCanTranslate());
        }
    }

    // ==================== Performance and Boundary Tests ====================

    @Test
    public void should_HandleLargeSupplierCodeList_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        
        // Create a large list of supplier codes
        List<String> largeSupplierCodeList = new java.util.ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            largeSupplierCodeList.add("SUPPLIER_" + i);
        }
        
        String supplierCode = "SUPPLIER_999"; // Last in list

        // When
        long startTime = System.currentTimeMillis();
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, largeSupplierCodeList, supplierCode);
        long endTime = System.currentTimeMillis();

        // Then
        assertTrue("Should find supplier code in large list", ratePlan.isCanTranslate());
        assertTrue("Should complete within reasonable time (< 100ms)", (endTime - startTime) < 100);
    }

    @Test
    public void should_HandleEmptyStringsInSupplierCodeList_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> supplierCodesWithEmpties = Arrays.asList("", "SUPPLIER1", "", "SUPPLIER2", "");
        String supplierCode = "SUPPLIER1";

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, supplierCodesWithEmpties, supplierCode);

        // Then
        assertTrue("Should handle empty strings in list and find valid supplier code", ratePlan.isCanTranslate());
    }

    @Test
    public void should_HandleDuplicateSupplierCodes_When_SettingCanTranslateFlag() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<String> supplierCodesWithDuplicates = Arrays.asList("SUPPLIER1", "SUPPLIER2", "SUPPLIER1", "SUPPLIER3");
        String supplierCode = "SUPPLIER1";

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, supplierCodesWithDuplicates, supplierCode);

        // Then
        assertTrue("Should handle duplicate supplier codes correctly", ratePlan.isCanTranslate());
    }

    // ==================== Documentation Tests ====================

    @Test
    public void should_DocumentPropertyTypeConstants_When_CheckingHotelOrResort() {
        // This test documents the exact property types that are considered hotels or resorts
        String[] validHotelResortTypes = {"HOTEL", "RESORT"};
        String[] invalidPropertyTypes = {
            "APARTMENT", "VILLA", "HOSTEL", "GUESTHOUSE", "BED_AND_BREAKFAST",
            "MOTEL", "INN", "LODGE", "COTTAGE", "CABIN", "BOUTIQUE_HOTEL"
        };

        // Test valid types
        for (String validType : validHotelResortTypes) {
            HotelDetails hotelDetails = new HotelDetails();
            hotelDetails.setPropertyType(validType);
            boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);
            assertTrue("Property type '" + validType + "' should be considered hotel or resort", result);
        }

        // Test invalid types
        for (String invalidType : invalidPropertyTypes) {
            HotelDetails hotelDetails = new HotelDetails();
            hotelDetails.setPropertyType(invalidType);
            boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);
            assertFalse("Property type '" + invalidType + "' should NOT be considered hotel or resort", result);
        }
    }

    // ==================== Integration Tests with Real World Scenarios ====================

    private HotelDetails createComplexHotelDetails() {
        HotelDetails hotelDetails = createValidHotelDetails();
        
        // Add multiple rooms with different configurations
        List<Rooms> rooms = new ArrayList<>();
        rooms.add(createRoomWithType("R001", "Standard Room", "EXACT"));
        rooms.add(createRoomWithType("R002", "Deluxe Room", "EXACT"));
        rooms.add(createRoomWithType("R003", "Suite", "EXACT"));
        
        hotelDetails.setRooms(rooms);
        return hotelDetails;
    }

    private Rooms createRoomWithType(String code, String name, String type) {
        Rooms room = createValidRoom();
        room.setCode(code);
        room.setName(name);
        room.setType(type);
        return room;
    }

    @Test
    public void testConvertSearchRoomsResponse_NullHotelDetailsResponse() {
        // Arrange
        SearchRoomsRequest searchRoomsRequest = createBasicSearchRoomsRequest();
        SearchCriteria searchCriteria = createBasicSearchCriteria();
        RequestDetails requestDetails = createBasicRequestDetails();

        // Act
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, null, new HashMap<>(), new ArrayList<>(),
                searchCriteria, new ArrayList<>(),
                requestDetails, null
        );

        // Assert
        assertNotNull(result);
        assertNull(result.getExactRooms());
    }

    private SearchRoomsRequest createBasicSearchRoomsRequest() {
        SearchRoomsRequest request = new SearchRoomsRequest();
        request.setRequestDetails(createBasicRequestDetails());
        return request;
    }

    private HotelDetailsResponse createBasicHotelDetailsResponse() {
        HotelDetailsResponse response = new HotelDetailsResponse();
        HotelDetails hotelDetails = new HotelDetails();

        // Set basic required fields to prevent NPEs
        hotelDetails.setCurrencyCode("USD");
        hotelDetails.setListingType("ROOM"); // Not ENTIRE to avoid property layout logic
        hotelDetails.setRoomCount(1);
        hotelDetails.setPropertyType("Hotel");

        // Initialize empty collections to prevent NPEs
        hotelDetails.setRooms(new ArrayList<>());
        hotelDetails.setRoomCombos(new ArrayList<>());
        hotelDetails.setOffers(new ArrayList<>());
        hotelDetails.setDealBenefits(new ArrayList<>());
        hotelDetails.setAlternateDatePriceDetails(new ArrayList<>());
        hotelDetails.setCategories(new HashSet<>());

        // Set up tracking info
        TrackingInfo trackingInfo = new TrackingInfo();
        trackingInfo.setTrackingText("test-tracking");
        hotelDetails.setTrackingInfo(trackingInfo);

        // Set up hotel rate flags
        HotelRateFlags hotelRateFlags = new HotelRateFlags();
        hotelRateFlags.setAltAcco(false);
        hotelRateFlags.setHighSellingAltAcco(false);
        hotelRateFlags.setSpotlightApplicable(false);
        hotelRateFlags.setAnyRateAllInclusive(false);
        hotelDetails.setHotelRateFlags(hotelRateFlags);

        response.setHotelDetails(hotelDetails);
        return response;
    }

    private HotelDetailsResponse createHotelDetailsResponseWithExactRooms() {
        HotelDetailsResponse response = createBasicHotelDetailsResponse();
        List<Rooms> roomsList = new ArrayList<>();
        Rooms room = new Rooms();
        room.setType(RoomType.EXACT.name());
        room.setRatePlans(new ArrayList<>()); // Initialize empty rate plans
        roomsList.add(room);
        response.getHotelDetails().setRooms(roomsList);
        return response;
    }

    private SearchCriteria createBasicSearchCriteria() {
        SearchCriteria criteria = new SearchCriteria();
        criteria.setLocationId("12345");
        criteria.setCheckIn("2024-01-15");
        criteria.setCheckOut("2024-01-18");
        criteria.setCurrency("USD");
        return criteria;
    }

    private RequestDetails createBasicRequestDetails() {
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setVisitorId("visitor123");
        requestDetails.setVisitNumber(1);
        requestDetails.setFunnelSource("DIRECT");
        requestDetails.setIdContext("TEST");
        requestDetails.setSiteDomain("www.makemytrip.com");
        return requestDetails;
    }

    // ===================== CONVERT CALENDAR AVAILABILITY RESPONSE TESTS =====================

    @Test
    public void testConvertCalendarAvailabilityResponse_SuccessfulConversion_WithValidData() {
        // Given - Testing successful conversion with valid data (Lines 1192-1205)
        ConsolidatedCalendarAvailabilityResponse input = createValidConsolidatedCalendarResponse();
        String currency = "USD";

        // Mock reArchUtility.getPriceColorForPriceDrop method (Line 1199)
        when(reArchUtility.getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP)).thenReturn("#007E7D");
        when(reArchUtility.getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE)).thenReturn("#FF4444");
        when(reArchUtility.getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL)).thenReturn("#666666");

        // When
        CalendarAvailabilityResponse result = transformer.convertCalendarAvailabilityResponse(input, currency);

        // Then - Verify all fields are correctly set
        assertNotNull(result); // Line 1192
        assertEquals(currency, result.getCurrency()); // Line 1204
        assertNotNull(result.getDates()); // Line 1203
        assertEquals(3, result.getDates().size()); // Verify all 3 dates are processed

        // Verify date transformation (Lines 1195-1201)
        CalendarBO date1 = result.getDates().get("2024-01-01");
        assertNotNull(date1);
        assertEquals("AVAILABLE", date1.getStatus()); // Line 1197 - status name conversion
        assertEquals(Double.valueOf(100.0), date1.getPrice()); // Line 1198
        assertEquals("#007E7D", date1.getPriceColor()); // Line 1199 - price color from utility

        CalendarBO date2 = result.getDates().get("2024-01-02");
        assertNotNull(date2);
        assertEquals("NOT_AVAILABLE", date2.getStatus());
        assertEquals(Double.valueOf(150.0), date2.getPrice());
        assertEquals("#FF4444", date2.getPriceColor());

        CalendarBO date3 = result.getDates().get("2024-01-03");
        assertNotNull(date3);
        assertEquals("AVAILABLE", date3.getStatus());
        assertEquals(Double.valueOf(80.0), date3.getPrice());
        assertEquals("#666666", date3.getPriceColor());

        // Verify mock interactions
        verify(reArchUtility).getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP);
        verify(reArchUtility).getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE);
        verify(reArchUtility).getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL);
    }

    @Test
    public void testConvertCalendarAvailabilityResponse_EmptyDatesMap() {
        // Given - Testing with empty dates map (Line 1194 false condition)
        ConsolidatedCalendarAvailabilityResponse input = new ConsolidatedCalendarAvailabilityResponse();
        input.setDates(Collections.emptyMap()); // Empty map to test MapUtils.isNotEmpty false condition
        String currency = "EUR";

        // When
        CalendarAvailabilityResponse result = transformer.convertCalendarAvailabilityResponse(input, currency);

        // Then
        assertNotNull(result); // Line 1192
        assertEquals(currency, result.getCurrency()); // Line 1204
        assertNotNull(result.getDates()); // Line 1203 - should be empty but not null
        assertTrue(result.getDates().isEmpty()); // Verify no dates processed due to empty input

        // Verify reArchUtility is not called when dates are empty
        verify(reArchUtility, never()).getPriceColorForPriceDrop(any(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.class));
    }

    @Test
    public void testConvertCalendarAvailabilityResponse_NullDatesMap() {
        // Given - Testing with null dates map (Line 1194 false condition)
        ConsolidatedCalendarAvailabilityResponse input = new ConsolidatedCalendarAvailabilityResponse();
        input.setDates(null); // Null map to test MapUtils.isNotEmpty false condition
        String currency = "GBP";

        // When
        CalendarAvailabilityResponse result = transformer.convertCalendarAvailabilityResponse(input, currency);

        // Then
        assertNotNull(result); // Line 1192
        assertEquals(currency, result.getCurrency()); // Line 1204
        assertNotNull(result.getDates()); // Line 1203 - should be empty LinkedHashMap
        assertTrue(result.getDates().isEmpty()); // Verify no dates processed due to null input

        // Verify reArchUtility is not called when dates are null
        verify(reArchUtility, never()).getPriceColorForPriceDrop(any(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.class));
    }

    @Test
    public void testConvertCalendarAvailabilityResponse_NullCurrency() {
        // Given - Testing with null currency
        ConsolidatedCalendarAvailabilityResponse input = createValidConsolidatedCalendarResponse();
        String currency = null;

        when(reArchUtility.getPriceColorForPriceDrop(any(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.class))).thenReturn("#DEFAULT");

        // When
        CalendarAvailabilityResponse result = transformer.convertCalendarAvailabilityResponse(input, currency);

        // Then
        assertNotNull(result);
        assertNull(result.getCurrency()); // Line 1204 - null currency is preserved
        assertNotNull(result.getDates());
        assertEquals(3, result.getDates().size());
    }

    @Test
    public void testConvertCalendarAvailabilityResponse_DifferentPriceVariationTypes() {
        // Given - Testing all PriceVariationType values for complete coverage
        ConsolidatedCalendarAvailabilityResponse input = new ConsolidatedCalendarAvailabilityResponse();
        Map<String, CalendarDate> dates = new LinkedHashMap<>();

        // Create dates with all different PriceVariationType values
        dates.put("2024-01-01", createCalendarDate(AvailabilityStatus.AVAILABLE, 100.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP));
        dates.put("2024-01-02", createCalendarDate(AvailabilityStatus.NOT_AVAILABLE, 150.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE));
        dates.put("2024-01-03", createCalendarDate(AvailabilityStatus.AVAILABLE, 120.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL));
        // Test with null PriceVariationType
        dates.put("2024-01-04", createCalendarDate(AvailabilityStatus.AVAILABLE, 90.0, null));

        input.setDates(dates);
        String currency = "CAD";

        // Mock all possible price variation types
        when(reArchUtility.getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP)).thenReturn("#GREEN");
        when(reArchUtility.getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE)).thenReturn("#RED");
        when(reArchUtility.getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL)).thenReturn("#BLUE");
        when(reArchUtility.getPriceColorForPriceDrop((com.gommt.hotels.orchestrator.detail.enums.PriceVariationType) null)).thenReturn("#DEFAULT");

        // When
        CalendarAvailabilityResponse result = transformer.convertCalendarAvailabilityResponse(input, currency);

        // Then
        assertNotNull(result);
        assertEquals(4, result.getDates().size());

        // Verify each price variation type is handled correctly
        assertEquals("#GREEN", result.getDates().get("2024-01-01").getPriceColor());
        assertEquals("#RED", result.getDates().get("2024-01-02").getPriceColor());
        assertEquals("#BLUE", result.getDates().get("2024-01-03").getPriceColor());
        assertEquals("#DEFAULT", result.getDates().get("2024-01-04").getPriceColor());

        // Verify all variations are called
        verify(reArchUtility).getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP);
        verify(reArchUtility).getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE);
        verify(reArchUtility).getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL);
        verify(reArchUtility).getPriceColorForPriceDrop((com.gommt.hotels.orchestrator.detail.enums.PriceVariationType) null);
    }

    @Test
    public void testConvertCalendarAvailabilityResponse_NullCalendarDateInMap() {
        // Given - Testing with null CalendarDate values in the map (edge case)
        ConsolidatedCalendarAvailabilityResponse input = new ConsolidatedCalendarAvailabilityResponse();
        Map<String, CalendarDate> dates = new LinkedHashMap<>();

        dates.put("2024-01-01", createCalendarDate(AvailabilityStatus.AVAILABLE, 100.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP));
        dates.put("2024-01-02", null); // Null date to test robustness
        dates.put("2024-01-03", createCalendarDate(AvailabilityStatus.NOT_AVAILABLE, 150.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE));

        input.setDates(dates);
        String currency = "JPY";

        when(reArchUtility.getPriceColorForPriceDrop(any(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.class))).thenReturn("#TEST");

        // When
        CalendarAvailabilityResponse result = transformer.convertCalendarAvailabilityResponse(input, currency);

        // Then
        assertNotNull(result);
        assertEquals(currency, result.getCurrency());
        assertNotNull(result.getDates());

        // Only valid dates should be processed (forEach will skip null values)
        assertTrue(result.getDates().containsKey("2024-01-01"));
        assertFalse(result.getDates().containsKey("2024-01-02")); // Null date should be skipped
        assertTrue(result.getDates().containsKey("2024-01-03"));
    }

    @Test
    public void testConvertCalendarAvailabilityResponse_ComprehensiveLineCoverage() {
        // Given - Comprehensive test to ensure all lines are covered
        ConsolidatedCalendarAvailabilityResponse input = createValidConsolidatedCalendarResponse();
        String currency = "AUD";

        when(reArchUtility.getPriceColorForPriceDrop(any(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.class))).thenReturn("#COMPREHENSIVE");

        // When
        CalendarAvailabilityResponse result = transformer.convertCalendarAvailabilityResponse(input, currency);

        // Then - Verify line-by-line coverage
        assertNotNull(result); // Line 1192 - new CalendarAvailabilityResponse()
        assertNotNull(result.getDates()); // Line 1193 - new LinkedHashMap<>()
        assertEquals(3, result.getDates().size()); // Line 1194-1202 - forEach processing
        assertEquals(currency, result.getCurrency()); // Line 1204 - setCurrency

        // Verify internal processing (Lines 1196-1201)
        for (CalendarBO calendarBO : result.getDates().values()) {
            assertNotNull(calendarBO); // Line 1196 - new CalendarBO()
            assertNotNull(calendarBO.getStatus()); // Line 1197 - setStatus
            assertNotNull(calendarBO.getPrice()); // Line 1198 - setPrice
            assertEquals("#COMPREHENSIVE", calendarBO.getPriceColor()); // Line 1199 - setPriceColor
        }

        // Line 1205 - return statement verified by assertNotNull(result)
        verify(reArchUtility, atLeast(3)).getPriceColorForPriceDrop(any(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.class));
    }

    // ===================== HELPER METHODS FOR CALENDAR AVAILABILITY TESTS =====================

    private ConsolidatedCalendarAvailabilityResponse createValidConsolidatedCalendarResponse() {
        ConsolidatedCalendarAvailabilityResponse response = new ConsolidatedCalendarAvailabilityResponse();
        Map<String, CalendarDate> dates = new LinkedHashMap<>();

        // Create test data with different availability statuses and price variations
        dates.put("2024-01-01", createCalendarDate(AvailabilityStatus.AVAILABLE, 100.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP));
        dates.put("2024-01-02", createCalendarDate(AvailabilityStatus.NOT_AVAILABLE, 150.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE));
        dates.put("2024-01-03", createCalendarDate(AvailabilityStatus.AVAILABLE, 80.0, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL));

        response.setDates(dates);
        return response;
    }

    private CalendarDate createCalendarDate(AvailabilityStatus status, Double price, com.gommt.hotels.orchestrator.detail.enums.PriceVariationType priceVariationType) {
        CalendarDate calendarDate = new CalendarDate();
        calendarDate.setStatus(status);
        calendarDate.setPrice(price);
        calendarDate.setPriceVariationType(priceVariationType);
        return calendarDate;
    }
} 