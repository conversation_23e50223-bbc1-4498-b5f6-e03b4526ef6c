package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.transformer.response.desktop.PaymentResponseTransformerDesktop;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PaymentResponseTransformerDesktopTest {

    @InjectMocks
    PaymentResponseTransformerDesktop prTransformerD;

    @Test
    public void processResponseTest(){
        PaymentCheckoutResponse paymentCheckoutResponse = new PaymentCheckoutResponse.Builder()
                .buildPaymentResponseMessage("SUCCESS")
                .buildAlternateCurrencyCode("USD")
                .buildAlternateCurrencyConversionFactor(0.5)
                .buildAlternateCurrencySelected(false)
                .buildBookingID("NH1234")
                .buildCurrency("INR")
                .buildDisplayPriceAlternateCurrency("200.0")
                .buildPaymentParams(new HashMap<>())
                .buildTotalAmount("100.0")
                .build();
        paymentCheckoutResponse.setThankYouURL("thankyouurl");
        paymentCheckoutResponse.setCorrelationKey("corr");

        paymentCheckoutResponse.getPaymentParams().put("checkoutId","C1234");
        paymentCheckoutResponse.getPaymentParams().put("checkoutUrl","Curl");
        paymentCheckoutResponse.getPaymentParams().put("paymentPlatform","FK123");

        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setTransactionKey("txn123");
        PaymentResponse response = prTransformerD.processResponse(paymentCheckoutResponse,beginCheckoutReqBody);
        Assert.assertNotNull(response);
        Assert.assertEquals("FK123",response.getFkToken());
        Assert.assertEquals("thankyouurl",response.getThankYouUrl());
        Assert.assertEquals("Curl",response.getCheckoutUrl());
        Assert.assertEquals("100.0",response.getTotalAmount());
        Assert.assertEquals("SUCCESS",response.getPaymentRespMessage());
        Assert.assertEquals("INR",response.getCurrency());
        Assert.assertEquals("corr",response.getCorrelationKey());
        Assert.assertEquals("NH1234",response.getBookingID());
        Assert.assertEquals("txn123",response.getTransactionKey());
        Assert.assertEquals("C1234",response.getCheckoutId());

    }

    @Test
    public void processResponseAlternateTest(){
        PaymentCheckoutResponse paymentCheckoutResponse = new PaymentCheckoutResponse.Builder()
                .buildPaymentResponseMessage("SUCCESS")
                .buildAlternateCurrencyCode("USD")
                .buildAlternateCurrencyConversionFactor(0.5)
                .buildAlternateCurrencySelected(true)
                .buildBookingID("NH1234")
                .buildCurrency("INR")
                .buildDisplayPriceAlternateCurrency("200.0")
                .buildPaymentParams(new HashMap<>())
                .buildTotalAmount("100.0")
                .build();
        paymentCheckoutResponse.setThankYouURL("thankyouurl");
        paymentCheckoutResponse.setCorrelationKey("corr");

        paymentCheckoutResponse.getPaymentParams().put("checkoutId","C1234");
        paymentCheckoutResponse.getPaymentParams().put("checkoutUrl","Curl");
        paymentCheckoutResponse.getPaymentParams().put("paymentPlatform","FK123");
        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setTransactionKey("txn123");
        PaymentResponse response = prTransformerD.processResponse(paymentCheckoutResponse,beginCheckoutReqBody);
        Assert.assertNotNull(response);
        Assert.assertEquals("FK123",response.getFkToken());
        Assert.assertEquals("thankyouurl",response.getThankYouUrl());
        Assert.assertEquals("Curl",response.getCheckoutUrl());
        Assert.assertEquals("200.0",response.getTotalAmount());
        Assert.assertEquals("SUCCESS",response.getPaymentRespMessage());
        Assert.assertEquals("USD",response.getCurrency());
        Assert.assertEquals("corr",response.getCorrelationKey());
        Assert.assertEquals("NH1234",response.getBookingID());
        Assert.assertEquals("txn123",response.getTransactionKey());
        Assert.assertEquals("C1234",response.getCheckoutId());

    }

    @Test
    public void processResponseErrorTest(){
        List<Error> errors = new ArrayList<>();
        errors.add(new Error());
        PaymentCheckoutResponse paymentCheckoutResponse = new PaymentCheckoutResponse.Builder()
               .buildResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build())
                .build();
        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setTransactionKey("txn123");
        PaymentResponse response = prTransformerD.processResponse(paymentCheckoutResponse,beginCheckoutReqBody);
        Assert.assertNotNull(response.getError());

    }
}
