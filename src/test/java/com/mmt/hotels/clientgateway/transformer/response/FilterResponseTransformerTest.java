package com.mmt.hotels.clientgateway.transformer.response;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.*;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.ContextDetails;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.filter.FilterRange;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import junit.framework.Assert;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import org.slf4j.MDC;
@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class FilterResponseTransformerTest {
	
	@InjectMocks
	FilterResponseTransformer filterResponseTransformerPWA;

	@Mock
	private FilterHelper filterHelper;
	@Mock
	PolyglotService polyglotService;

	@Spy
	Utility utility;
	@Mock
	private DateUtil dateUtil;
	@Spy
	private CommonResponseTransformer commonResponseTransformer;

	private FilterPillConfig filterPillConfig;


	@Before
	public  void init(){
		Map<String,List<Integer>> priceHistConfig = new HashMap<>();
		List<Integer> config = new ArrayList<>();
		config.add(10);
		config.add(1000);
		priceHistConfig.put("INR",config);

		Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfig = new HashMap<>();
		AmenityCategory guestsLoveCategory = new AmenityCategory();
		guestsLoveCategory.setTitle("Guests Love");
		guestsLoveCategory.setAmenities(new ArrayList<String>(){{add("Swimming Pool"); add("Fireplace");}});

		AmenityCategory generalCategory = new AmenityCategory();
		generalCategory.setTitle("General");
		generalCategory.setAmenities(new ArrayList<String>(){{add("Outdoor Sports"); add("Bonfire");}});

		AmenityCategory transferCategory = new AmenityCategory();
		transferCategory.setTitle("Transfers");
		transferCategory.setAmenities(new ArrayList<String>(){{add("Railway Station Transfers"); add("Airport Transfers");}});


		Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfigPolyGlot = new HashMap<>();
		AmenityCategory guestsLoveCategory2 = new AmenityCategory();
		guestsLoveCategory2.setTitle("GUESTS_LOVE");
		guestsLoveCategory2.setAmenities(new ArrayList<String>(){{add("Swimming Pool"); add("Fireplace");}});

		AmenityCategory generalCategory2 = new AmenityCategory();
		generalCategory2.setTitle("GENERAL");
		generalCategory2.setAmenities(new ArrayList<String>(){{add("Outdoor Sports"); add("Bonfire");}});

		AmenityCategory transferCategory2 = new AmenityCategory();
		transferCategory.setTitle("TRANSFERS");
		transferCategory2.setAmenities(new ArrayList<String>(){{add("Railway Station Transfers"); add("Airport Transfers");}});

		amenitiesCategoryConfig.put("HOMESTAY", new HashMap<String, AmenityCategory>(){{put("GUESTS_LOVE", guestsLoveCategory); put("GENERAL", generalCategory); put("TRANSFERS", transferCategory);}});
		amenitiesCategoryConfigPolyGlot.put("HOMESTAY", new HashMap<String, AmenityCategory>(){{put("GUESTS_LOVE", guestsLoveCategory2); put("GENERAL", generalCategory2); put("TRANSFERS", transferCategory2);}});
		ReflectionTestUtils.setField(filterResponseTransformerPWA,"defaultPriceHistConfig" , priceHistConfig);
		ReflectionTestUtils.setField(filterResponseTransformerPWA,"defaultPriceHistConfigCorp" , priceHistConfig);
		ReflectionTestUtils.setField(filterResponseTransformerPWA,"amenitiesCategoryConfig" , amenitiesCategoryConfig);
		ReflectionTestUtils.setField(filterResponseTransformerPWA,"amenitiesCategoryConfigPolyGlot" , amenitiesCategoryConfigPolyGlot);
		ReflectionTestUtils.setField(filterResponseTransformerPWA, "filterHelper", filterHelper);
		List<String> amenitiesList = new ArrayList<>();
		amenitiesList.add("Kitchen");
		ReflectionTestUtils.setField(filterResponseTransformerPWA, "altAccoAmenities",amenitiesList);
		Gson gson = new Gson();
		String pillConfig = "{\"filterPills\":{\"GOSTAY\":{\"id\":\"GOSTAYS\",\"title\":\"goStays\",\"type\":\"filter\",\"categories\":[\"GOSTAY\"]},\"PAY_LATER\":{\"id\":\"PAY_LATER\",\"title\":\"Book@0\",\"type\":\"filter\",\"categories\":[\"PAY_LATER\"]},\"DEAL_OF_THE_DAY\":{\"id\":\"DEAL_OF_THE_DAY\",\"title\":\"Daily Steal Deal\",\"type\":\"filter\",\"categories\":[\"DEAL_OF_THE_DAY\"]},\"EARLY_BIRD\":{\"id\":\"DEALS\",\"title\":\"Early Bird Deal\",\"type\":\"filter\",\"categories\":[\"DEALS\"]},\"LAST_MINUTE\":{\"id\":\"DEALS\",\"title\":\"Last Minute Deal\",\"type\":\"filter\",\"categories\":[\"DEALS\"]},\"Couple Friendly\":{\"id\":\"HOTEL_CATEGORY\",\"title\":\"Couple Friendly\",\"type\":\"filter\",\"categories\":[\"HOTEL_CATEGORY\"]},\"CANCELLATION_AVAIL\":{\"id\":\"FREE_CANCELLATION_AVAIL\",\"title\":\"Free Cancellation\",\"type\":\"filter\",\"categories\":[\"FREE_CANCELLATION_AVAIL\"]},\"BUSINESS_FRIENDLY\":{\"id\":\"BUSINESS_FRIENDLY\",\"title\":\"Business Friendly\",\"type\":\"filter\",\"categories\":[\"BUSINESS_FRIENDLY\"]},\"WOMEN_TRAVELLERS\":{\"id\":\"WOMEN_TRAVELLERS\",\"title\":\"Women Travellers\",\"type\":\"filter\",\"categories\":[\"WOMEN_TRAVELLERS\"]},\"BLACKDEALS_TIER1\":{\"id\":\"BLACKDEALS\",\"title\":\"GoTribe Deals\",\"type\":\"filter\",\"categories\":[\"BLACKDEALS\"]}},\"pillSequence\":{\"GOSTAY\":1,\"PAY_LATER\":2,\"DEAL_OF_THE_DAY\":3,\"EARLY_BIRD\":4,\"LAST_MINUTE\":5,\"Couple Friendly\":6,\"CANCELLATION_AVAIL\":7,\"BUSINESS_FRIENDLY\":8,\"WOMEN_TRAVELLERS\":9,\"BLACKDEALS_TIER1\":10}}\n";
		filterPillConfig = gson.fromJson(pillConfig, new TypeToken<FilterPillConfig>() {
		}.getType());

		ReflectionTestUtils.setField(filterResponseTransformerPWA,"filterMinItemsLimit" ,7);


	}
	@Test
	public void convertFilterResponseTest() {
		//Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("{filter_text}"); Not requried for cg-gi
		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<FilterGroup, List<Filter>>());
		response.getFilterDataMap().put(FilterGroup.STAR_RATING, new ArrayList<>());
		FilterConfiguration filterConfiguration = new FilterConfiguration();
		filterConfiguration.setConditions(new LinkedHashMap<>());
		filterConfiguration.getConditions().put("test", new ArrayList<String>());
		filterConfiguration.setFilters(new LinkedHashMap<>());
		filterConfiguration.getFilters().put("STAR_RATING", new FilterConfigCategory());
		filterConfiguration.getFilters().get("STAR_RATING").setGroups(new LinkedHashMap<>());
		filterConfiguration.getFilters().get("STAR_RATING").getGroups().put("STAR_RATING", new LinkedHashMap<>());
		filterConfiguration.getFilters().get("STAR_RATING").getGroups().get("STAR_RATING").put("STAR_RATING", new FilterConfigDetail());
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCurrency("INR");
		LinkedHashMap<String,String>expDataMap = new LinkedHashMap<>();
		filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		filterConfiguration.getFilters().get("STAR_RATING").getGroups().put("STAR_RATING", null);
		filterConfiguration.getFilters().put("HOTEL_PRICE", new FilterConfigCategory());
		filterConfiguration.getFilters().get("HOTEL_PRICE").setGroups(new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE").getGroups().put("HOTEL_PRICE", new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE").getGroups().get("HOTEL_PRICE").put("HOTEL_PRICE", new FilterConfigDetail());
		filterConfiguration.getFilters().put("HOTEL_PRICE_BUCKET", new FilterConfigCategory());
		filterConfiguration.getFilters().get("HOTEL_PRICE_BUCKET").setGroups(new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE_BUCKET").getGroups().put("HOTEL_PRICE_BUCKET", new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE_BUCKET").getGroups().get("HOTEL_PRICE_BUCKET").put("HOTEL_PRICE_BUCKET", new FilterConfigDetail());

		Assert.assertNotNull(filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap,new CommonModifierResponse(), new FilterPillConfigurationWrapper()));

		filterConfiguration.getFilters().put("HOUSE_RULES",new FilterConfigCategory());
		filterConfiguration.getFilters().get("HOUSE_RULES").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOUSE_RULES", null);}});
		Assert.assertNotNull(filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper()));



		filterConfiguration.getFilters().put("ROOMS_AND_BEDS",new FilterConfigCategory());
		filterConfiguration.getFilters().get("ROOMS_AND_BEDS").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{
			put("BED", new LinkedHashMap<String, FilterConfigDetail>(){{put("BED", new FilterConfigDetail());}});
			put("BEDROOM", new LinkedHashMap<String, FilterConfigDetail>(){{put("BEDROOM", new FilterConfigDetail());}});
		}});
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put("ROOMS_AND_BEDS", 1);}});
		filterCountRequest.setExpData("{HAFC:T,CRF:B}");
		expDataMap.put("HAFC","T"); expDataMap.put("CRF","B");
		CommonModifierResponse commonModifierResponsep = new CommonModifierResponse();
		commonModifierResponsep.setExpDataMap(expDataMap);

		FilterResponse filterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, commonModifierResponsep, new FilterPillConfigurationWrapper());
		Assert.assertNotNull(filterResponse);
		Assert.assertEquals(filterResponse.getFilterList().size(), 1);
		Assert.assertTrue(filterResponse.getFilterList().stream().filter(filterCategory -> "ROOMS_AND_BEDS".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
		Assert.assertNotNull(filterResponse.getExpData());



		filterConfiguration.getFilters().put("AMENITIES",new FilterConfigCategory());
		filterConfiguration.getFilters().get("AMENITIES").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("AMENITIES", null);}});
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put("AMENITIES", 2);}});
		expDataMap.clear();expDataMap.put("HAFC","T");
		filterCountRequest.setExpData("{HAFC:T}");
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOMESTAY");
		filterCountRequest.setRequestDetails(requestDetails);
		Filter guestLoveFilter = new Filter();
		guestLoveFilter.setFilterValue("Swimming Pool");
		guestLoveFilter.setFilterGroup(FilterGroup.AMENITIES);
		Filter generalFilter = new Filter();
		generalFilter.setFilterValue("Outdoor Sports");
		generalFilter.setFilterGroup(FilterGroup.AMENITIES);
		Filter transferFilter = new Filter();
		transferFilter.setFilterValue("Railway Station Transfers");
		transferFilter.setFilterGroup(FilterGroup.AMENITIES);
		response.getFilterDataMap().put(FilterGroup.AMENITIES, new ArrayList<Filter>(){{add(guestLoveFilter); add(generalFilter); add(transferFilter);}});
		response.setContextDetails(new ContextDetails());
		response.getContextDetails().setContext("all|all|all|all");
		response.getContextDetails().setAltAccoIntent(true);
		response.setLocationName("Test");
		FilterResponse amenitiesFilterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertNotNull(amenitiesFilterResponse);
		Assert.assertTrue(amenitiesFilterResponse.getFilterList().stream().filter(filterCategory -> "AMENITIES".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
		Assert.assertEquals(amenitiesFilterResponse.getFilterList().stream().filter(filterCategory -> "AMENITIES".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().get().getFilters().size(),3);


		Mockito.when(filterHelper.fetchPriceTitle(Mockito.any())).thenReturn("");

		filterConfiguration.getFilters().put("PRICE",new FilterConfigCategory());
		filterConfiguration.getFilters().get("PRICE").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOTEL_PRICE", null);}});
		filterConfiguration.getFilters().put("POPULAR",new FilterConfigCategory());
		filterConfiguration.getFilters().get("POPULAR").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOTEL_PRICE", null);}});
		filterConfiguration.getFilters().put("PRICE_BUCKET",new FilterConfigCategory());
		filterConfiguration.getFilters().get("PRICE_BUCKET").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOTEL_PRICE_BUCKET", null);}});
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put("PRICE", 1); put("PRICE_BUCKET", 2);}});
		filterCountRequest.setClient("DESKTOP");

		Filter priceFilter = new Filter();
		priceFilter.setFilterRange(new FilterRange());
		priceFilter.getFilterRange().setMinValue(0);
		priceFilter.getFilterRange().setMinValue(500);
		priceFilter.setRangeFilter(true);
		priceFilter.setFilterGroup(FilterGroup.HOTEL_PRICE);

		response.getFilterDataMap().put(FilterGroup.HOTEL_PRICE, new ArrayList<Filter>(){{add(priceFilter);}});

		Filter priceBucketFilter = new Filter();
		priceBucketFilter.setFilterRange(new FilterRange());
		priceBucketFilter.getFilterRange().setMinValue(0);
		priceBucketFilter.getFilterRange().setMinValue(500);
		priceBucketFilter.setRangeFilter(true);
		priceBucketFilter.setFilterGroup(FilterGroup.HOTEL_PRICE_BUCKET);

		response.getFilterDataMap().put(FilterGroup.HOTEL_PRICE_BUCKET, new ArrayList<Filter>(){{add(priceBucketFilter);}});

		expDataMap.clear(); expDataMap.put("CRF","B");
		filterCountRequest.setExpData("{CRF:B}");
		FilterResponse priceFilterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertTrue(priceFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
		Assert.assertFalse(priceFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE_BUCKET".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());

		expDataMap.put("HAFC","T"); expDataMap.put("CRF","B");
		filterCountRequest.setExpData("{HAFC:T,CRF:B}");
		FilterResponse priceBucketFilterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertTrue(priceBucketFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE_BUCKET".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
		Assert.assertFalse(priceBucketFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());

		Mockito.lenient().when(commonResponseTransformer.buildFilterCG(Mockito.any())).thenReturn(new com.mmt.hotels.clientgateway.response.filter.Filter());
		Map<String, List<Filter>> filterCategoryMap = new HashMap<>();
		List<Filter> filterList = new ArrayList<>();
		filterList.add(new Filter());
		filterCategoryMap.put("AMENITIES", filterList);
		response.setFilterCategoryMap(filterCategoryMap);
		response.setMatchmakerFilterList(new ArrayList<>());
		response.getMatchmakerFilterList().add(new Filter());
		response.setLocationName("");
		org.junit.Assert.assertNotNull(filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper()));


		FilterResponse localityFilterResponse;
		filterConfiguration.getFilters().put(Constants.LOCALITY_GROUP,new FilterConfigCategory());
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put(Constants.LOCALITY_GROUP, 3);}});
		filterCountRequest.setFeatureFlags(new FeatureFlags());

		filterCountRequest.getFeatureFlags().setFilterRanking(true);
		localityFilterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertTrue(localityFilterResponse.getFilterList().stream().filter(filterCategory -> Constants.LOCALITY_GROUP.equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());

		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "PWA");
		filterCountRequest.getFeatureFlags().setFilterRanking(false);
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(new ExtendedUser());
		commonModifierResponse.getExtendedUser().setProfileType("CTA");
		commonModifierResponse.getExtendedUser().setAffiliateId("MYPARTNER");
		localityFilterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, commonModifierResponse, new FilterPillConfigurationWrapper());
		Assert.assertFalse(localityFilterResponse.getFilterList().stream().filter(filterCategory -> Constants.LOCALITY_GROUP.equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
		// When locationtype is country, LOCALITY will be the first filter in the list
		filterCountRequest.getSearchCriteria().setLocationType("country");
		Map<String, List<Filter>> localityFilterCateogryMap = new HashMap<>();
		List<Filter> localityFilter = new ArrayList<>();
		localityFilter.add(new Filter());
		filterCountRequest.setFeatureFlags(null);
		filterCategoryMap.put("LOCALITY", localityFilter);
		filterConfiguration.getRankOrder().put("ROOMS_AND_BEDS", 3);
		filterConfiguration.getRankOrder().put("LOCALITY", 2);
		filterConfiguration.getRankOrder().put("AMENITIES",1);
		localityFilterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, commonModifierResponse, new FilterPillConfigurationWrapper());
		Assert.assertTrue(localityFilterResponse.getFilterList().get(0).getCategoryName().equalsIgnoreCase("LOCALITY"));
	}

	@Test
	public void convertBatchFilterResponseTest() throws IOException {
		FilterSearchMetaDataResponse response = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/batchFilterResponse.json")),
				FilterSearchMetaDataResponse.class);
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Title");
		org.junit.Assert.assertNotNull(filterResponseTransformerPWA.convertBatchFilterResponse(response));
	}

	@Test
	public void appendFilterWithCountZeroAtLastTest(){
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter filter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter.setFilterGroup("test");
		filter.setCount(0);
		filterList.add(filter);
		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "appendFilterWithCountZeroAtLast", filterList);
	}

	@Test
	public void createFilterTest() {
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		Filter filter = new Filter(FilterGroup.HOTEL_PRICE, 0, 100);
		filter.setCount(2);
		Assert.assertNotNull(filterResponseTransformerPWA.createFilter(filterConfigDetail, filter, "CORP", "SPACE", null, null));
	}

	@Test
	public void createLocationFilterTest() {
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		Filter filter = new Filter(FilterGroup.LOCATION, 0, 100);
		filter.setFilterValue("test");
		com.mmt.hotels.clientgateway.response.filter.Filter f = filterResponseTransformerPWA.createFilter(filterConfigDetail, filter, "CORP", "SPACE", null, null);
		Assert.assertTrue(f.getFilterExists());
	}

	@Test
	public void testAddAllFilterFromCategoryMap() {
		// Arrange
		FilterResponseTransformer filterResponseTransformer = new FilterResponseTransformer();
		String idContext = "testContext";
		String filterConfigKey = "TEST_CATEGORY";
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterCategoryMap(new HashMap<>());
		List<com.mmt.hotels.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.filter.Filter testFilter = new com.mmt.hotels.filter.Filter();
		testFilter.setFilterValue("Test Filter Value");
		testFilter.setFilterGroup(FilterGroup.CATEGORY);
		testFilter.setCount(8);
		filterList.add(testFilter);
		filterResponseHES.getFilterCategoryMap().put("TEST_CATEGORY", filterList);

		List<com.mmt.hotels.clientgateway.response.filter.Filter> result = filterResponseTransformer.addAllFilterFromCategoryMap(filterResponseHES, idContext, filterConfigKey, expDataMap, filterCountRequest, 7);

	}

	@Test
	public void createFilterSubGroupTest() {
		List<FilterGroup> filterHESSubGroupList = new ArrayList<>();
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		FilterConfiguration filterConfig = new FilterConfiguration();
		filterHESSubGroupList.add(FilterGroup.AMENITIES);
		filterResponseHES.setFilterDataMap(new HashMap<>());
		filterResponseHES.getFilterDataMap().put(FilterGroup.AMENITIES, new ArrayList<Filter>());
		filterResponseHES.getFilterDataMap().get(FilterGroup.AMENITIES).add(new Filter(FilterGroup.AMENITIES, "Wifi"));
		filterConfig.setFilters(new LinkedHashMap<>());
		filterConfig.getFilters().put("AMENITIES", new FilterConfigCategory());
		filterConfig.getFilters().get("AMENITIES").setGroups(new LinkedHashMap<>());
		filterConfig.getFilters().get("AMENITIES").getGroups().put("AMENITIES", new LinkedHashMap<>());
		Assert.assertNotNull(filterResponseTransformerPWA.createFilterSubGroup(filterHESSubGroupList, filterResponseHES, filterConfig, "CORP", "SPACE", null));
	}

	@Test
	public void getMaxBudgetPriceForCityTest(){
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
		searchHotelsCriteria.setCityCode("CTDEL");
		filterCountRequest.setSearchCriteria(searchHotelsCriteria);
		Map<Integer, Set<String>> budgetHotelCityConfig = new HashMap<>();
		Set<String> set = new HashSet<>();
		set.add("CTDEL");
		budgetHotelCityConfig.put(3000, set);
		ReflectionTestUtils.setField(filterResponseTransformerPWA,"budgetHotelCityConfig" , budgetHotelCityConfig);
		int budget = filterResponseTransformerPWA.getMaxBudgetPriceForCity(filterCountRequest);
		org.junit.Assert.assertEquals(budget, 3000);
	}

	@Test
	public void buildPricingOptionTest(){

		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn(LocalDate.now().toString());
		filterCountRequest.getSearchCriteria().setCheckOut(LocalDate.now().plusDays(2).toString());

		FilterPricingOption pricingOption = new FilterPricingOption();
		pricingOption.setHeader("PRICING_OPTION_HEADER");

		List<PricingOptionFilter> filterList = new ArrayList<PricingOptionFilter>();
		PricingOptionFilter filter1 = new PricingOptionFilter();
		filter1.setTitle("PRICING_OPTION_FILTER_TITLE_PN");
		filter1.setExpValue("PN");
		filterList.add(filter1);
		PricingOptionFilter filter2 = new PricingOptionFilter();
		filter2.setTitle("PRICING_OPTION_FILTER_TITLE_TP");
		filter2.setExpValue("TP");
		filterList.add(filter2);

		pricingOption.setFilters(filterList);

		Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
		Mockito.when(dateUtil.getDaysDiff(Mockito.any(LocalDate.class),Mockito.any(LocalDate.class))).thenReturn(2);
		Assert.assertNotNull(filterResponseTransformerPWA.buildPricingOption(filterCountRequest,pricingOption));
	}

	@Test
	public void getCategoryNamesFromFilterResponseTest(){
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("test");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);

		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA,"getCategoryNamesFromFilterResponse",filterResponse);
	}

	@Test
	public void translateSortPillDataTest(){
		SortList sortList = new SortList();
		sortList.setTitle("KEY");
		SortCriteria sortCriteria = new SortCriteria();
		sortCriteria.setTitle("TITLE_KEY");
		sortCriteria.setPillText("PILL_TEXT_KEY");
		List<SortCriteria> sortCriteriaList = new ArrayList<>();
		sortCriteriaList.add(sortCriteria);
		sortList.setSortCriteria(sortCriteriaList);


		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "translateSortPillData", sortList);

	}
	@Test
	public void buildFilterPillsTest(){
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		List<FilterGroup> filterHESSubGroupList = new ArrayList<>();
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		FilterConfiguration filterConfig = new FilterConfiguration();
		filterHESSubGroupList.add(FilterGroup.AMENITIES);
		filterResponseHES.setFilterDataMap(new HashMap<>());
		filterResponseHES.getFilterDataMap().put(FilterGroup.AMENITIES, new ArrayList<Filter>());
		filterResponseHES.getFilterDataMap().get(FilterGroup.AMENITIES).add(new Filter(FilterGroup.AMENITIES, "Wifi"));
		LinkedHashMap<String,String>expDataMap = new LinkedHashMap<>();


		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "buildFilterPills", filterResponse, filterPillConfig, filterResponseHES, expDataMap, null);

	}

	@Test
	public void buildPillCategoriesTest(){
		List<String> filterCategories = Arrays.asList("POPULAR");
		List<FilterGroup> filterHESSubGroupList = new ArrayList<>();
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		FilterConfiguration filterConfig = new FilterConfiguration();
		filterHESSubGroupList.add(FilterGroup.AMENITIES);
		filterResponseHES.setFilterDataMap(new HashMap<>());
		filterResponseHES.getFilterDataMap().put(FilterGroup.AMENITIES, new ArrayList<Filter>());
		filterResponseHES.getFilterDataMap().get(FilterGroup.AMENITIES).add(new Filter(FilterGroup.AMENITIES, "Wifi"));
		LinkedHashMap<String,String>expDataMap = new LinkedHashMap<>();

		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "buildPillCategories", filterCategories, "GOSTAY", filterPillConfig, filterResponseHES, expDataMap, null);

	}

	@Test
	public void buildFilterCtaTextTest() {

		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Show {filtered_hotel_count} hotels");
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setFilterCriteria(new ArrayList<>());
		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "buildFilterCtaText", 10, filterCountRequest);
		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "buildFilterCtaText", 0, null);
	}

	@Test
	public void updateMinItemsToShowTest() {
		FilterCategory filterCategory = new FilterCategory();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		for (int i = 0; i < 3; i++) {
			filters.add(new com.mmt.hotels.clientgateway.response.filter.Filter());
		}
		filterCategory.setFilters(filters);
		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "updateMinItemsToShowAndShowMore", filterCategory, null);
		for (int i = 0; i < 5; i++) {
			filters.add(new com.mmt.hotels.clientgateway.response.filter.Filter());
		}
		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "updateMinItemsToShowAndShowMore", filterCategory, null);


	}

	@Test
	public void testModifyFilterViewType() {

		Map<String, String> viewMap = new HashMap<>();
		viewMap.put("STAR_CATEGORY", "flex");
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(new ArrayList<>());
		filterResponse.getFilterList().add(new FilterCategory());
		filterResponse.getFilterList().get(0).setCategoryName("STAR_CATEGORY");
		filterResponse.getFilterList().get(0).setViewType("tile");

		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "modifyFilterCategoryViewType", viewMap, filterResponse);


	}

	@Test
	public void removeFilterCategoriesFromResponseTest() {
		List<String> categoriesToRemove = new ArrayList<>();
		FilterResponse filterResponse = new FilterResponse();
		FilterCategory fc1 = new FilterCategory();
		fc1.setCategoryName("test1");
		FilterCategory fc2 = new FilterCategory();
		fc2.setCategoryName("test2");
		categoriesToRemove.add("test1");

		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "removeFilterCategoriesFromResponse", categoriesToRemove, filterResponse);
	}

	@Test
	public void modifyShowImageUrlMergePropertyTest() {

		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setShowImageUrl(false);

		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA, "modifyShowImageUrlMergePropertyType", filterCategories);
	}

	@Test
	public void testFiltersFromFilterCategory() {
		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<>());
		response.setFilterCategoryMap(new HashMap<>());
		response.getFilterCategoryMap().put("TEST_CATEGORY", new ArrayList<>());
		Filter hesFilter = new Filter();
		hesFilter.setFilterValue("Test Filter Value");
		hesFilter.setFilterGroup(FilterGroup.CATEGORY);
		hesFilter.setCount(8);
		response.getFilterCategoryMap().get("TEST_CATEGORY").add(hesFilter);
		FilterConfiguration filterConfiguration = new FilterConfiguration();
		filterConfiguration.setFilters(new LinkedHashMap<>());
		filterConfiguration.setRankOrder(new LinkedHashMap<>());
		filterConfiguration.getFilters().put("TEST_CATEGORY", new FilterConfigCategory());
		filterConfiguration.getFilters().get("TEST_CATEGORY").setGroups(new LinkedHashMap<>());
		filterConfiguration.getFilters().get("TEST_CATEGORY").getGroups().put("1", new LinkedHashMap<>());
		filterConfiguration.getRankOrder().put("TEST_CATEGORY", 1);
		filterConfiguration.setSuggestedFiltersThreshold(7);

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		LinkedHashMap<String,String>expDataMap = new LinkedHashMap<>();
		FilterResponse filterResponse = filterResponseTransformerPWA.convertFilterResponse(response, filterConfiguration,
				filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertEquals(1, filterResponse.getFilterList().size());
	}

	@Test
	public void testIsCoupleFriendlyFilterApplicable() {
		SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
		searchHotelsCriteria.setRoomStayCandidates(new ArrayList<>());
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setAdultCount(2);
		searchHotelsCriteria.getRoomStayCandidates().add(roomStayCandidate);
		ReflectionTestUtils.invokeMethod(filterResponseTransformerPWA,  "isCoupleFriendlyFilterApplicable" , searchHotelsCriteria);
	}


}
