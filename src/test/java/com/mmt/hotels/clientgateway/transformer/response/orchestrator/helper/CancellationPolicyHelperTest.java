package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.pricing.CancelRulesDescription;
import com.mmt.hotels.model.response.pricing.CancellationRules;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CancellationPolicyHelper in GI project
 * Comprehensive test coverage following patterns from CG version but adapted for GI implementation
 */
@RunWith(MockitoJUnitRunner.class)
public class CancellationPolicyHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private CancellationPolicyHelper cancellationPolicyHelper;

    // Test constants
    private static final String FREE_CANCELLATION_TEXT = "Free Cancellation";
    private static final String NON_REFUNDABLE_TEXT = "Non-Refundable";
    private static final String NON_REFUNDABLE_SUBTEXT = "No refund on cancellation";
    private static final String RATEPLAN_CANCELLATION_POLICY = "Cancellation Policy";
    private static final String ICON_URL = "https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/policy.png";

    @Before
    public void setUp() {
        // Setup common polyglot service responses
        when(polyglotService.getTranslatedData("FREE_CANCELLATION_TEXT")).thenReturn(FREE_CANCELLATION_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT)).thenReturn(NON_REFUNDABLE_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT)).thenReturn(NON_REFUNDABLE_SUBTEXT);
    }

    // ==================== transformCancellationPolicy Tests ====================

    @Test
    public void should_ReturnNull_When_PolicyIsNull() {
        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(null, null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_TransformFreeCancellationPolicy_When_PolicyTypeIsFC() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", "Free cancellation until 24 hours");

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.FC, result.getType());
        assertEquals(IconType.DOUBLETICK, result.getIconType());
        assertEquals(ICON_URL, result.getIconUrl());
        assertEquals("Free cancellation until 24 hours", result.getText());
        assertNotNull(result.getCancelRules());
        assertEquals(1, result.getCancelRules().size());
    }

    @Test
    public void should_TransformFreeCancellationPolicy_When_PolicyTypeIsFREE_CANCELLATION() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FREE_CANCELLATION", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.FC, result.getType());
        assertEquals(IconType.DOUBLETICK, result.getIconType());
        assertEquals(FREE_CANCELLATION_TEXT, result.getText());
    }

    @Test
    public void should_TransformNonRefundablePolicy_When_PolicyTypeIsNR() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.NR, result.getType());
        assertEquals(IconType.BIGCROSS, result.getIconType());
        assertEquals(ICON_URL, result.getIconUrl());
        assertEquals(NON_REFUNDABLE_TEXT, result.getText());
        assertEquals(NON_REFUNDABLE_SUBTEXT, result.getSubText());
    }

    @Test
    public void should_TransformNonRefundablePolicy_When_PolicyTypeIsNON_REFUNDABLE() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NON_REFUNDABLE", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.NR, result.getType());
        assertEquals(IconType.BIGCROSS, result.getIconType());
        assertEquals(NON_REFUNDABLE_TEXT, result.getText());
    }

    @Test
    public void should_SetDefaultIconType_When_AdvancePurchaseIsLessThanLimit() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, 1);

        // Then
        assertNotNull(result);
        assertEquals(IconType.DEFAULT, result.getIconType());
    }

    @Test
    public void should_SetBigCrossIconType_When_AdvancePurchaseIsGreaterThanOrEqualToLimit() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, 2);

        // Then
        assertNotNull(result);
        assertEquals(IconType.BIGCROSS, result.getIconType());
    }

    @Test
    public void should_HandleEmptyPenalties_When_TransformingPolicy() {
        // Given
        CancellationPolicy policy = new CancellationPolicy();
        policy.setPenalties(new ArrayList<>());

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.NR, result.getType());
        assertEquals(NON_REFUNDABLE_TEXT, result.getText());
        assertEquals(NON_REFUNDABLE_SUBTEXT, result.getSubText());
        assertNull(result.getCancelRules());
    }

    @Test
    public void should_UseFallbackText_When_FreeCancellationTextIsBlank() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", "");

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(FREE_CANCELLATION_TEXT, result.getText());
    }

    @Test
    public void should_UseFallbackText_When_FreeCancellationTextIsNull() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(FREE_CANCELLATION_TEXT, result.getText());
    }

    @Test
    public void should_HandlePolyglotServiceFailure_When_TranslationFails() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", null);
        when(polyglotService.getTranslatedData("FREE_CANCELLATION_TEXT")).thenThrow(new RuntimeException("Translation failed"));

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals("Free Cancellation", result.getText()); // Should use fallback
    }

    @Test
    public void should_HandlePolyglotServiceReturningKey_When_TranslationNotFound() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", null);
        when(polyglotService.getTranslatedData("FREE_CANCELLATION_TEXT")).thenReturn("FREE_CANCELLATION_TEXT");

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals("Free Cancellation", result.getText()); // Should use fallback when key is returned
    }

    // ==================== buildCancellationTimelineFromOrchV2 Tests ====================

    @Test
    public void should_ReturnNull_When_TimelineDetailsIsNull() {
        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildCancellationTimeline_When_ValidTimelineDetails() {
        // Given
        CancellationTimelineDetails timelineDetails = createCancellationTimelineDetails();

        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails);

        // Then
        assertNotNull(result);
        assertEquals("2023-12-01", result.getBookingDate());
        assertEquals("2023-12-10", result.getCancellationDate());
        assertEquals("2023-12-10T10:00:00", result.getCancellationDateTime());
        assertEquals("2023-12-11", result.getCardChargeDate());
        assertEquals("2023-12-11T12:00:00", result.getCardChargeDateTime());
        assertEquals("dd/MM/yyyy", result.getDateFormat());
        assertEquals("Card will be charged", result.getCardChargeText());
        assertEquals("Booking amount: $100", result.getBookingAmountText());
        assertEquals("2023-12-15", result.getCheckInDate());
        assertEquals("2023-12-15T15:00:00", result.getCheckInDateTime());
        assertEquals("Free cancellation available", result.getFreeCancellationText());
        assertEquals("Cancellation details", result.getSubTitle());
    }

//    @Test
//    public void should_TransformFreeCancellationBenefits_When_BenefitsExist() {
//        // Given
//        CancellationTimelineDetails timelineDetails = createCancellationTimelineDetailsWithBenefits();
//
//        // When
//        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails);
//
//        // Then
//        assertNotNull(result);
//        assertNotNull(result.getFreeCancellationBenefits());
//        assertEquals(3, result.getFreeCancellationBenefits().size());
//
//        FCBenefit fczpnBenefit = result.getFreeCancellationBenefits().get(0);
//        assertEquals("FCZPN benefit", fczpnBenefit.getText());
//        assertEquals(IconType.DOUBLETICK, fczpnBenefit.getIconType());
//
//        FCBenefit defaultBenefit = result.getFreeCancellationBenefits().get(1);
//        assertEquals("Regular benefit", defaultBenefit.getText());
//        assertEquals(IconType.SINGLETICK, defaultBenefit.getIconType());
//
//        FCBenefit nullTypeBenefit = result.getFreeCancellationBenefits().get(2);
//        assertEquals("Benefit with null type", nullTypeBenefit.getText());
//        assertEquals(IconType.SINGLETICK, nullTypeBenefit.getIconType());
//    }
//
//    @Test
//    public void should_HandleNullBenefitType_When_TransformingBenefits() {
//        // Given
//        CancellationTimelineDetails timelineDetails = new CancellationTimelineDetails();
//        List<FreeCancellationBenefitDetails> benefits = new ArrayList<>();
//        FreeCancellationBenefitDetails benefit = new FreeCancellationBenefitDetails();
//        benefit.setText("Benefit with null type");
//        benefit.setType(null);
//        benefits.add(benefit);
//        timelineDetails.setFreeCancellationBenefits(benefits);
//
//        // When
//        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails);
//
//        // Then
//        assertNotNull(result);
//        assertNotNull(result.getFreeCancellationBenefits());
//        assertEquals(1, result.getFreeCancellationBenefits().size());
//        assertEquals(IconType.SINGLETICK, result.getFreeCancellationBenefits().get(0).getIconType());
//    }

    @Test
    public void should_HandleEmptyBenefitsList_When_BenefitsListIsEmpty() {
        // Given
        CancellationTimelineDetails timelineDetails = createCancellationTimelineDetails();
        timelineDetails.setFreeCancellationBenefits(new ArrayList<>());

        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails);

        // Then
        assertNotNull(result);
        assertNull(result.getFreeCancellationBenefits());
    }

    @Test
    public void should_HandleNullBenefitsList_When_BenefitsListIsNull() {
        // Given
        CancellationTimelineDetails timelineDetails = createCancellationTimelineDetails();
        timelineDetails.setFreeCancellationBenefits(null);

        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails);

        // Then
        assertNotNull(result);
        assertNull(result.getFreeCancellationBenefits());
    }

    // ==================== buildCancellationPolicyTimelineFromOrchV2 Tests ====================

    @Test
    public void should_ReturnNull_When_TimelineDetailsIsNullForPolicyTimeline() {
        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_PolicyTimelineListIsEmpty() {
        // Given
        CancellationTimelineDetails timelineDetails = new CancellationTimelineDetails();
        timelineDetails.setCancellationPolicyTimelineList(new ArrayList<>());

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_PolicyTimelineListIsNull() {
        // Given
        CancellationTimelineDetails timelineDetails = new CancellationTimelineDetails();
        timelineDetails.setCancellationPolicyTimelineList(null);

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildPolicyTimeline_When_ValidTimelineDetails() {
        // Given
        CancellationTimelineDetails timelineDetails = createTimelineDetailsWithPolicyTimeline();

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails);

        // Then
        assertNotNull(result);
        assertEquals("2023-12-10", result.getCancellationDate());
        assertEquals("2023-12-10T10:00:00", result.getCancellationDateTime());
        assertEquals("dd/MM/yyyy", result.getDateFormat());
        assertEquals("Card will be charged", result.getCardChargeText());
        assertEquals("Card charge title", result.getCardChargeTextTitle());
        assertEquals("Card charge message", result.getCardChargeTextMsg());
        assertEquals("BNPL title text", result.getBnplTitleText());
        assertEquals("Booking amount: $100", result.getBookingAmountText());
        assertNotNull(result.getTimeline());
        assertEquals(2, result.getTimeline().size());
    }

    @Test
    public void should_TransformPolicyTimelineCorrectly_When_MultipleTimelinesExist() {
        // Given
        CancellationTimelineDetails timelineDetails = createTimelineDetailsWithPolicyTimeline();

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails);

        // Then
        assertNotNull(result);
        assertNotNull(result.getTimeline());
        assertEquals(2, result.getTimeline().size());

        com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline timeline1 = result.getTimeline().get(0);
        assertTrue(timeline1.isRefundable());
        assertEquals("Full refund available", timeline1.getText());
        assertEquals("2023-12-01", timeline1.getStartDate());
        assertEquals("2023-12-08", timeline1.getEndDate());
        assertEquals("2023-12-08T23:59:59", timeline1.getEndDateTime());

        com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline timeline2 = result.getTimeline().get(1);
        assertFalse(timeline2.isRefundable());
        assertEquals("No refund", timeline2.getText());
        assertEquals("2023-12-09", timeline2.getStartDate());
        assertEquals("2023-12-15", timeline2.getEndDate());
        assertEquals("2023-12-15T23:59:59", timeline2.getEndDateTime());
    }

    @Test
    public void should_TransformFreeCancellationBenefitsInPolicyTimeline_When_BenefitsExist() {
        // Given
        CancellationTimelineDetails timelineDetails = createTimelineDetailsWithPolicyTimelineAndBenefits();

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFreeCancellationBenefits());
        assertEquals(3, result.getFreeCancellationBenefits().size());

        FCBenefit fczpnBenefit = result.getFreeCancellationBenefits().get(0);
        assertEquals("FCZPN benefit", fczpnBenefit.getText());
        assertEquals(IconType.DOUBLETICK, fczpnBenefit.getIconType());

        FCBenefit defaultBenefit = result.getFreeCancellationBenefits().get(1);
        assertEquals("Regular benefit", defaultBenefit.getText());
        assertEquals(IconType.SINGLETICK, defaultBenefit.getIconType());

        FCBenefit nullTypeBenefit = result.getFreeCancellationBenefits().get(2);
        assertEquals("Benefit with null type", nullTypeBenefit.getText());
        assertEquals(IconType.SINGLETICK, nullTypeBenefit.getIconType());
    }

    // ==================== getCancellationPolicyType Tests ====================

    @Test
    public void should_ReturnFC_When_PenaltyTypeIsFC() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    @Test
    public void should_ReturnFC_When_PenaltyTypeIsFREE_CANCELLATON() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FREE_CANCELLATON", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    @Test
    public void should_ReturnFC_When_PenaltyTypeIsFREE_CANCELLATION() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FREE_CANCELLATION", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    @Test
    public void should_ReturnFCZPN_When_PenaltyTypeIsPR() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("PR", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FCZPN, result);
    }

    @Test
    public void should_ReturnFCZPN_When_PenaltyTypeIsPARTIAL_REFUNDABLE() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("PARTIAL_REFUNDABLE", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FCZPN, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsNR() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsNON_REFUNDABLE() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NON_REFUNDABLE", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsUnknown() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("UNKNOWN_TYPE", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsBlank() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltiesListIsEmpty() {
        // Given
        CancellationPolicy policy = new CancellationPolicy();
        policy.setPenalties(new ArrayList<>());

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltiesListIsNull() {
        // Given
        CancellationPolicy policy = new CancellationPolicy();
        policy.setPenalties(null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_HandleCaseInsensitivePenaltyTypes_When_TypeIsLowercase() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("fc", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    @Test
    public void should_HandleCaseInsensitivePenaltyTypes_When_TypeIsMixedcase() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("Fc", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    // ==================== Helper methods for creating test data ====================

    private CancellationPolicy createCancellationPolicy(String penaltyType, String freeCancellationText) {
        CancellationPolicy policy = new CancellationPolicy();
        
        List<CancellationPenalty> penalties = new ArrayList<>();
        CancellationPenalty penalty = new CancellationPenalty();
        penalty.setType(penaltyType);
        penalty.setFreeCancellationText(freeCancellationText);
        
        // Add cancel rules for testing
        List<CancelRules> cancelRules = new ArrayList<>();
        CancelRules rule = new CancelRules();
        rule.setText("Cancel rule text");
        
        List<CancelRulesDesc> descText = new ArrayList<>();
        CancelRulesDesc desc = new CancelRulesDesc();
        desc.setFeeText("Fee: $50");
        desc.setDateText("Before 24 hours");
        descText.add(desc);
        rule.setDescText(descText);
        
        cancelRules.add(rule);
        penalty.setCancelRules(cancelRules);
        
        penalties.add(penalty);
        policy.setPenalties(penalties);
        
        return policy;
    }

    private CancellationTimelineDetails createCancellationTimelineDetails() {
        CancellationTimelineDetails details = new CancellationTimelineDetails();
        details.setBookingDate("2023-12-01");
        details.setCancellationDate("2023-12-10");
        details.setCancellationDateTime("2023-12-10T10:00:00");
        details.setCardChargeDate("2023-12-11");
        details.setCardChargeDateTime("2023-12-11T12:00:00");
        details.setDateFormat("dd/MM/yyyy");
        details.setCardChargeText("Card will be charged");
        details.setBookingAmountText("Booking amount: $100");
        details.setCheckInDate("2023-12-15");
        details.setCheckInDateTime("2023-12-15T15:00:00");
        details.setFreeCancellationText("Free cancellation available");
        details.setSubTitle("Cancellation details");
        return details;
    }

    private CancellationTimelineDetails createCancellationTimelineDetailsWithBenefits() {
        CancellationTimelineDetails details = createCancellationTimelineDetails();
        
        List<FreeCancellationBenefitDetails> benefits = new ArrayList<>();
        
        FreeCancellationBenefitDetails fczpnBenefit = new FreeCancellationBenefitDetails();
        fczpnBenefit.setText("FCZPN benefit");
        fczpnBenefit.setType("FCZPN");
        benefits.add(fczpnBenefit);
        
        FreeCancellationBenefitDetails defaultBenefit = new FreeCancellationBenefitDetails();
        defaultBenefit.setText("Regular benefit");
        defaultBenefit.setType("OTHER");
        benefits.add(defaultBenefit);
        
        FreeCancellationBenefitDetails nullTypeBenefit = new FreeCancellationBenefitDetails();
        nullTypeBenefit.setText("Benefit with null type");
        nullTypeBenefit.setType(null);
        benefits.add(nullTypeBenefit);
        
        details.setFreeCancellationBenefits(benefits);
        return details;
    }

    private CancellationTimelineDetails createTimelineDetailsWithPolicyTimeline() {
        CancellationTimelineDetails details = createCancellationTimelineDetails();
        details.setCardChargeTextTitle("Card charge title");
        details.setCardChargeTextMsg("Card charge message");
        details.setBnplTitleText("BNPL title text");
        
        List<CancellationPolicyTimelineDetails> policyTimelineList = new ArrayList<>();
        
        CancellationPolicyTimelineDetails timeline1 = new CancellationPolicyTimelineDetails();
        timeline1.setRefundable(true);
        timeline1.setText("Full refund available");
        timeline1.setStartDate("2023-12-01");
        timeline1.setEndDate("2023-12-08");
        timeline1.setEndDateTime("2023-12-08T23:59:59");
        policyTimelineList.add(timeline1);
        
        CancellationPolicyTimelineDetails timeline2 = new CancellationPolicyTimelineDetails();
        timeline2.setRefundable(false);
        timeline2.setText("No refund");
        timeline2.setStartDate("2023-12-09");
        timeline2.setEndDate("2023-12-15");
        timeline2.setEndDateTime("2023-12-15T23:59:59");
        policyTimelineList.add(timeline2);
        
        details.setCancellationPolicyTimelineList(policyTimelineList);
        return details;
    }

    private CancellationTimelineDetails createTimelineDetailsWithPolicyTimelineAndBenefits() {
        CancellationTimelineDetails details = createTimelineDetailsWithPolicyTimeline();
        
        List<FreeCancellationBenefitDetails> benefits = new ArrayList<>();
        
        FreeCancellationBenefitDetails fczpnBenefit = new FreeCancellationBenefitDetails();
        fczpnBenefit.setText("FCZPN benefit");
        fczpnBenefit.setType("FCZPN");
        benefits.add(fczpnBenefit);
        
        FreeCancellationBenefitDetails defaultBenefit = new FreeCancellationBenefitDetails();
        defaultBenefit.setText("Regular benefit");
        defaultBenefit.setType("OTHER");
        benefits.add(defaultBenefit);
        
        FreeCancellationBenefitDetails nullTypeBenefit = new FreeCancellationBenefitDetails();
        nullTypeBenefit.setText("Benefit with null type");
        nullTypeBenefit.setType(null);
        benefits.add(nullTypeBenefit);
        
        details.setFreeCancellationBenefits(benefits);
        return details;
    }
} 