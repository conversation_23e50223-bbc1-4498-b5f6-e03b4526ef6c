package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.MobLandingRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.MobLandingRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.MobLandingRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.MobLandingRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.MobLandingRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.MobLandingResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.MobLandingResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.MobLandingResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.MobLandingResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.MobLandingResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class MobLandingFactoryTest {


    @InjectMocks
    MobLandingFactory mobLandingFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingResponseTransformerPWA" , new MobLandingResponseTransformerPWA());
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingResponseTransformerDesktop" , new MobLandingResponseTransformerDesktop());
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingResponseTransformerAndroid" , new MobLandingResponseTransformerAndroid());
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingResponseTransformerIOS" , new MobLandingResponseTransformerIOS());
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingRequestTransformerPWA" , new MobLandingRequestTransformerPWA());
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingRequestTransformerDesktop" , new MobLandingRequestTransformerDesktop());
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingRequestTransformerAndroid" , new MobLandingRequestTransformerAndroid());
        ReflectionTestUtils.setField(mobLandingFactory,"mobLandingRequestTransformerIOS" , new MobLandingRequestTransformerIOS());
        
    }
    
    @Test
    public void getRequestServiceTest(){
        MobLandingRequestTransformer resp = mobLandingFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof MobLandingRequestTransformerPWA  );
        resp = mobLandingFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof MobLandingRequestTransformerDesktop  );
        resp = mobLandingFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof MobLandingRequestTransformerAndroid  );
        resp = mobLandingFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof MobLandingRequestTransformerIOS  );
        resp = mobLandingFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(mobLandingFactory.getRequestService("test") instanceof  MobLandingRequestTransformerDesktop );
    }

    @Test
    public void getResponseServiceTest(){
        MobLandingResponseTransformer resp = mobLandingFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof MobLandingResponseTransformerPWA  );
        resp = mobLandingFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof MobLandingResponseTransformerDesktop  );
        resp = mobLandingFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof MobLandingResponseTransformerAndroid  );
        resp = mobLandingFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof MobLandingResponseTransformerIOS  );
        resp = mobLandingFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(mobLandingFactory.getResponseService("test") instanceof MobLandingResponseTransformerDesktop);
    }

}
