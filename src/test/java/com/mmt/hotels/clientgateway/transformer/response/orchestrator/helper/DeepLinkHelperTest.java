package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.HotelRateFlags;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.FilterRange;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomStayCandidate;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DeepLinkHelperTest {

    @Mock
    private DateUtil dateUtil;

    @Mock
    private Utility utility;

    @InjectMocks
    private DeepLinkHelper deepLinkHelper;

    // Test constants
    private static final String BASIC_DETAIL_DEEPLINK = "https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}";
    private static final String BASIC_DETAIL_DEEPLINK_GLOBAL = "https://www.makemytrip.com/hotels/hotel-details-global?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}";
    private static final String SEARCH_ROOMS_DEEPLINK = "https://www.makemytrip.com/hotels/hotel-search-rooms?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}";
    private static final String SEARCH_ROOMS_DEEPLINK_GLOBAL = "https://www.makemytrip.com/hotels/hotel-search-rooms-global?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}";
    
    private static final String HOTEL_ID = "202001031606575931";
    private static final String CHECK_IN_DATE = "2024-12-15";
    private static final String CHECK_OUT_DATE = "2024-12-16";
    private static final String FORMATTED_CHECK_IN = "12152024";
    private static final String FORMATTED_CHECK_OUT = "12162024";
    private static final String CURRENCY = "INR";
    private static final String COUNTRY_CODE = "IN";
    private static final String LOCATION_ID = "CTDEL";
    private static final String LOCATION_TYPE = "city";
    private static final String HOTEL_NAME = "Test Hotel";
    private static final String CITY_NAME = "Delhi";
    private static final String COUNTRY_NAME = "India";
    private static final String PROPERTY_TYPE = "Entire Apartment";
    private static final String HOTEL_CATEGORY = "BUDGET";
    private static final String FUNNEL_SOURCE = "HOTELS";

    @Before
    public void setUp() {
        // Set up @Value injected properties using ReflectionTestUtils
        ReflectionTestUtils.setField(deepLinkHelper, "basicDetailDeeplink", BASIC_DETAIL_DEEPLINK);
        ReflectionTestUtils.setField(deepLinkHelper, "basicDetailDeeplinkGlobal", BASIC_DETAIL_DEEPLINK_GLOBAL);
        ReflectionTestUtils.setField(deepLinkHelper, "searchRoomsDeeplink", SEARCH_ROOMS_DEEPLINK);
        ReflectionTestUtils.setField(deepLinkHelper, "searchRoomsDeeplinkGlobal", SEARCH_ROOMS_DEEPLINK_GLOBAL);
        
        // Setup common mock behaviors
        lenient().when(dateUtil.getDateFormatted(eq(CHECK_IN_DATE), eq(DateUtil.YYYY_MM_DD), eq(DateUtil.MMDDYYYY)))
                .thenReturn(FORMATTED_CHECK_IN);
        lenient().when(dateUtil.getDateFormatted(eq(CHECK_OUT_DATE), eq(DateUtil.YYYY_MM_DD), eq(DateUtil.MMDDYYYY)))
                .thenReturn(FORMATTED_CHECK_OUT);
        
        // Clear MDC before each test
        MDC.clear();
    }

    // ============ buildDetailDeepLinkUrl(HotelDetails, SearchRoomsRequest) Tests ============

    @Test
    public void should_BuildDetailDeepLink_When_BasicFlow() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.contains(HOTEL_ID));
        assertTrue(result.contains(FORMATTED_CHECK_IN));
        assertTrue(result.contains(FORMATTED_CHECK_OUT));
        assertTrue(result.contains(CURRENCY));
        assertTrue(result.contains("false")); // MPN parameter
    }

    @Test
    public void should_BuildDetailDeepLink_When_GlobalEntityWithSARegion() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequestWithGlobalEntity(Constants.REGION_SA);
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("sa.makemytrip.com"));
    }

    @Test
    public void should_BuildDetailDeepLink_When_GlobalEntityWithAERegion() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequestWithGlobalEntity(Constants.REGION_AE);
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("ae.makemytrip.com"));
    }

    @Test
    public void should_BuildDetailDeepLink_When_GlobalEntityWithOtherRegion() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequestWithGlobalEntity("OTHER");
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("www.makemytrip.com"));
    }

    // ============ buildSearchRoomsDeepLinkUrl Tests ============

//    @Test
//    public void should_BuildSearchRoomsDeepLink_When_BasicFlow() {
//        // Given
//        HotelDetails hotelDetails = createValidHotelDetails();
//        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
//
//        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);
//
//        // When
//        String result = deepLinkHelper.buildSearchRoomsDeepLinkUrl(hotelDetails, searchRoomsRequest);
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("hotel-search-rooms"));
//        assertTrue(result.contains(HOTEL_ID));
//        assertTrue(result.contains(HOTEL_NAME.replace(" ", "%20")));
//        assertTrue(result.contains(CITY_NAME));
//        assertTrue(result.contains(COUNTRY_NAME));
//    }

    @Test
    public void should_BuildSearchRoomsDeepLink_When_GlobalEntity() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequestWithGlobalEntity(Constants.REGION_SA);
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildSearchRoomsDeepLinkUrl(hotelDetails, searchRoomsRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("hotel-search-rooms-global"));
        assertTrue(result.contains("sa.makemytrip.com"));
    }

    // ============ buildDetailDeepLinkUrl Main Implementation Tests ============

//    @Test
//    public void should_BuildDeepLink_When_RoomStayDistributionEnabled() {
//        // Given
//        HotelDetails hotelDetails = createValidHotelDetails();
//        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
//        List<RoomStayCandidate> distributedList = createDistributedRoomStayCandidates();
//
//        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(true);
//        when(utility.buildRscValue(anyList())).thenReturn("2e7e2e12e7e");
//        when(utility.buildRoomStayDistribution(anyList(), anyMap())).thenReturn(distributedList);
//
//        // When
//        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("rsc=2e7e2e12e7e"));
//    }

    @Test
    public void should_BuildDeepLink_When_NullDates() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequestWithNullDates();
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("checkin=&checkout="));
    }

//    @Test
//    public void should_BuildDeepLink_When_NullSearchCriteria() {
//        // Given
//        HotelDetails hotelDetails = createValidHotelDetails();
//        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequestWithNullSearchCriteria();
//
//        when(utility.isDistributeRoomStayCandidates(any(), anyMap())).thenReturn(false);
//
//        // When
//        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);
//
//        // Then
//        assertNotNull(result);
//        // Null/empty fields result in empty query parameters
//        assertTrue(result.contains("hotelId=" + HOTEL_ID));
//        assertTrue(result.contains("checkin=&"));     // Empty checkin
//        assertTrue(result.contains("checkout=&"));    // Empty checkout
//        assertTrue(result.contains("currency=&"));    // Empty currency
//        assertTrue(result.contains("openDetail=true"));
//        assertTrue(result.contains("locusId=" + LOCATION_ID));
//        assertTrue(result.contains("viewType=" + HOTEL_CATEGORY));
//        assertTrue(result.contains("region=IN"));
//        assertTrue(result.contains("mpn=false"));
//    }

    @Test
    public void should_BuildDeepLink_When_NullLocationInHotelDetails() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithNullLocation();
        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("locusId=" + LOCATION_ID));
        assertTrue(result.contains("locusType=" + LOCATION_TYPE));
    }

//    @Test
//    public void should_BuildDeepLink_When_MDCRegionPresent() {
//        // Given
//        HotelDetails hotelDetails = createValidHotelDetails();
//        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
//        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "US");
//
//        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);
//
//        // When
//        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("region=us"));
//    }

//    @Test
//    public void should_BuildDeepLink_When_MDCRegionNotPresent() {
//        // Given
//        HotelDetails hotelDetails = createValidHotelDetails();
//        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
//        // MDC region is not set (cleared in setUp)
//
//        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);
//
//        // When
//        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("region=" + Constants.WALLET_REGION_IND));
//    }
//
//    @Test
//    public void should_BuildDeepLink_When_HotelCategoryPresent() {
//        // Given
//        HotelDetails hotelDetails = createValidHotelDetails();
//        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
//
//        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);
//
//        // When
//        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("viewType=" + HOTEL_CATEGORY));
//    }

    @Test
    public void should_BuildDeepLink_When_FunnelSourcePresent() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("funnelName=" + FUNNEL_SOURCE));
    }

    @Test
    public void should_BuildDeepLink_When_CheckInTimeSlotPresent() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequestWithTimeSlot();
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, false);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("checkInTime=14"));
    }

    @Test
    public void should_BuildDeepLink_When_PropertyDetailsRequested() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        SearchRoomsRequest searchRoomsRequest = createValidSearchRoomsRequest();
        
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(false);

        // When
        String result = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, BASIC_DETAIL_DEEPLINK, true);

        // Then
        System.out.println("DEBUG - PropertyDetails result: '" + result + "'");
        assertNotNull(result);
        // Check that property details are added when deepLinkWithPropertyDetails=true
        assertTrue(result.contains("hotelName="));
        assertTrue(result.contains("cityName="));
        assertTrue(result.contains("countryName="));
        assertTrue(result.contains("isEntireProperty="));
    }

    // ============ appendFiltersDataToDeepLink Tests ============

    @Test
    public void should_ReturnOriginalDeepLink_When_FilterCriteriaIsNull() {
        // Given
        String originalDeepLink = "https://www.makemytrip.com/hotels";

        // When
        String result = deepLinkHelper.appendFiltersDataToDeepLink(null, originalDeepLink);

        // Then
        assertEquals(originalDeepLink, result);
    }

    @Test
    public void should_ReturnOriginalDeepLink_When_FilterCriteriaIsEmpty() {
        // Given
        String originalDeepLink = "https://www.makemytrip.com/hotels";
        List<Filter> emptyFilters = new ArrayList<>();

        // When
        String result = deepLinkHelper.appendFiltersDataToDeepLink(emptyFilters, originalDeepLink);

        // Then
        assertEquals(originalDeepLink, result);
    }

    @Test
    public void should_AppendFilters_When_NonPriceFiltersPresent() {
        // Given
        String originalDeepLink = "https://www.makemytrip.com/hotels";
        List<Filter> filters = createNonPriceFilters();

        // When
        String result = deepLinkHelper.appendFiltersDataToDeepLink(filters, originalDeepLink);

        // Then
        assertNotNull(result);
        // Actual format: 'https://www.makemytrip.com/hotels&filterData=STAR_RATING%7C4%2C5%5EAMENITIES%7CWIFI'
        assertTrue(result.contains("filterData="));
        assertTrue(result.contains("STAR_RATING%7C4%2C5"));  // URL encoded: STAR_RATING|4,5
        assertTrue(result.contains("AMENITIES%7CWIFI"));     // URL encoded: AMENITIES|WIFI
        assertTrue(result.contains("%5E"));  // URL encoded group splitter ^
    }

    @Test
    public void should_AppendPriceFilters_When_PriceFiltersPresent() {
        // Given
        String originalDeepLink = "https://www.makemytrip.com/hotels";
        List<Filter> filters = createPriceFilters();

        // When
        String result = deepLinkHelper.appendFiltersDataToDeepLink(filters, originalDeepLink);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("filterData="));
        assertTrue(result.contains("HOTEL_PRICE"));
        assertTrue(result.contains("1000-5000"));
    }

    @Test
    public void should_SkipFilter_When_FilterGroupIsNull() {
        // Given
        String originalDeepLink = "https://www.makemytrip.com/hotels";
        List<Filter> filters = createFiltersWithNullGroup();

        // When
        String result = deepLinkHelper.appendFiltersDataToDeepLink(filters, originalDeepLink);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("filterData="));
        assertTrue(result.contains("STAR_RATING"));
        // Should not contain the filter with null group
    }

    @Test
    public void should_HandlePriceFilter_When_InvalidRange() {
        // Given
        String originalDeepLink = "https://www.makemytrip.com/hotels";
        List<Filter> filters = createPriceFiltersWithInvalidRange();

        // When
        String result = deepLinkHelper.appendFiltersDataToDeepLink(filters, originalDeepLink);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("filterData="));
        assertTrue(result.contains("HOTEL_PRICE"));
        // Should skip invalid price range
    }

    // ============ getEncodedUrl Tests ============

//    @Test
//    public void should_EncodeUrl_When_ValidString() {
//        // Given
//        String input = "Test Hotel & Spa";
//
//        // When
//        String result = DeepLinkHelper.getEncodedUrl(input);
//
//        // Then
//        assertNotNull(result);
//        assertEquals("Test%20Hotel%20%26%20Spa", result);
//    }

    @Test
    public void should_ReturnEmpty_When_NullUrl() {
        // When
        String result = DeepLinkHelper.getEncodedUrl(null);

        // Then
        assertEquals("", result);
    }

    @Test
    public void should_ReturnEmpty_When_BlankUrl() {
        // When
        String result = DeepLinkHelper.getEncodedUrl("");

        // Then
        assertEquals("", result);
    }

    @Test
    public void should_ReturnEmpty_When_WhitespaceUrl() {
        // When
        String result = DeepLinkHelper.getEncodedUrl("   ");

        // Then
        assertEquals("", result);
    }

    // ============ getQueryParameter Tests ============

    @Test
    public void should_BuildQueryParameter_When_ValidInput() {
        // Given
        String queryParam = "region";
        String value = "in";

        // When
        String result = deepLinkHelper.getQueryParameter(queryParam, value);

        // Then
        assertEquals("&region=in", result);
    }

    @Test
    public void should_BuildQueryParameter_When_EmptyValue() {
        // Given
        String queryParam = "currency";
        String value = "";

        // When
        String result = deepLinkHelper.getQueryParameter(queryParam, value);

        // Then
        assertEquals("&currency=", result);
    }

    // ============ buildRoomStayCandidateFromSearchWrapperS Tests ============

    @Test
    public void should_ReturnEmpty_When_NullRoomStayCandidates() {
        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapperS(null);

        // Then
        assertEquals("", result);
    }

    @Test
    public void should_ReturnEmpty_When_EmptyRoomStayCandidates() {
        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapperS(new ArrayList<>());

        // Then
        assertEquals("", result);
    }

    @Test
    public void should_BuildRoomStayParam_When_ValidRoomStayCandidates() {
        // Given
        List<RoomStayCandidate> roomStayCandidates = createValidRoomStayCandidates();

        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapperS(roomStayCandidates);

        // Then
        assertNotNull(result);
        // Actual format: '2e1e6e1e1e8e' (2 adults, 1 child age 6, then 1 adult, 1 child age 8)
        assertTrue(result.contains("2e1e6e"));  // First room: 2 adults, 1 child, age 6
        assertTrue(result.contains("1e1e8e"));  // Second room: 1 adult, 1 child, age 8
    }

    @Test
    public void should_HandleNullGuestCount_When_BuildingRoomStayParam() {
        // Given
        List<RoomStayCandidate> roomStayCandidates = createRoomStayCandidatesWithNullGuestCount();

        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapperS(roomStayCandidates);

        // Then
        assertNotNull(result);
        // Should skip null guest counts
    }

    @Test
    public void should_FilterChildAges_When_BuildingRoomStayParam() {
        // Given
        List<RoomStayCandidate> roomStayCandidates = createRoomStayCandidatesWithInvalidChildAges();

        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapperS(roomStayCandidates);

        // Then
        System.out.println("DEBUG - FilterChildAges result: '" + result + "'");
        assertNotNull(result);
        // Should filter out age 15 (only ages 0-12 are valid)
        assertTrue(result.contains("6e")); // Should include age 6
        assertFalse(result.contains("15e")); // Should not include age 15
    }

    // ============ buildRoomStayCandidateFromSearchWrapper Tests ============

    @Test
    public void should_ReturnEmpty_When_NullClientRoomStayCandidates() {
        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapper(null);

        // Then
        assertEquals("", result);
    }

    @Test
    public void should_ReturnEmpty_When_EmptyClientRoomStayCandidates() {
        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapper(new ArrayList<>());

        // Then
        assertEquals("", result);
    }

    @Test
    public void should_BuildRoomStayParam_When_ValidClientRoomStayCandidates() {
        // Given
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates = createValidClientRoomStayCandidates();

        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapper(roomStayCandidates);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("2e1e"));
        assertTrue(result.contains("6e"));
    }

    @Test
    public void should_HandleNullChildAges_When_BuildingClientRoomStayParam() {
        // Given
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates = createClientRoomStayCandidatesWithNullChildAges();

        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapper(roomStayCandidates);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("2e0e"));
    }

    @Test
    public void should_FilterClientChildAges_When_BuildingRoomStayParam() {
        // Given
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates = createClientRoomStayCandidatesWithInvalidChildAges();

        // When
        String result = deepLinkHelper.buildRoomStayCandidateFromSearchWrapper(roomStayCandidates);

        // Then
        System.out.println("DEBUG - FilterClientChildAges result: '" + result + "'");
        assertNotNull(result);
        // Should filter out age 15 (only ages 0-12 are valid)
        assertTrue(result.contains("8e")); // Should include age 8
        assertFalse(result.contains("15e")); // Should not include age 15
    }

    // ============ Helper methods for creating test data ============

    private HotelDetails createValidHotelDetails() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setId(HOTEL_ID);
        hotelDetails.setName(HOTEL_NAME);
        hotelDetails.setHotelCategory(HOTEL_CATEGORY);
        hotelDetails.setPropertyType(PROPERTY_TYPE);
        
        LocationDetails location = new LocationDetails();
        location.setId(LOCATION_ID);
        location.setType(LOCATION_TYPE);
        location.setCityName(CITY_NAME);
        location.setCountryName(COUNTRY_NAME);
        hotelDetails.setLocation(location);
        
        HotelRateFlags hotelRateFlags = new HotelRateFlags();
        hotelRateFlags.setMaskedPropertyName(false);
        hotelDetails.setHotelRateFlags(hotelRateFlags);
        
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithNullLocation() {
        HotelDetails hotelDetails = createValidHotelDetails();
        hotelDetails.setLocation(null);
        return hotelDetails;
    }

    private SearchRoomsRequest createValidSearchRoomsRequest() {
        SearchRoomsRequest request = new SearchRoomsRequest();
        
        SearchRoomsCriteria searchCriteria = new SearchRoomsCriteria();
        searchCriteria.setCheckIn(CHECK_IN_DATE);
        searchCriteria.setCheckOut(CHECK_OUT_DATE);
        searchCriteria.setCurrency(CURRENCY);
        searchCriteria.setLocationId(LOCATION_ID);
        searchCriteria.setLocationType(LOCATION_TYPE);
        searchCriteria.setCountryCode(COUNTRY_CODE);
        searchCriteria.setRoomStayCandidates(createValidClientRoomStayCandidates());
        request.setSearchCriteria(searchCriteria);
        
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource(FUNNEL_SOURCE);
        request.setRequestDetails(requestDetails);
        
        request.setFilterCriteria(new ArrayList<>());
        request.setExpDataMap(new HashMap<>());
        
        return request;
    }

    private SearchRoomsRequest createSearchRoomsRequestWithGlobalEntity(String region) {
        SearchRoomsRequest request = createValidSearchRoomsRequest();
        
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        userGlobalInfo.setEntityName(Constants.GLOBAL_ENTITY);
        request.getSearchCriteria().setUserGlobalInfo(userGlobalInfo);
        
        request.getRequestDetails().setSiteDomain(region);
        
        return request;
    }

    private SearchRoomsRequest createSearchRoomsRequestWithNullDates() {
        SearchRoomsRequest request = createValidSearchRoomsRequest();
        request.getSearchCriteria().setCheckIn(null);
        request.getSearchCriteria().setCheckOut(null);
        return request;
    }

    private SearchRoomsRequest createSearchRoomsRequestWithNullSearchCriteria() {
        SearchRoomsRequest request = new SearchRoomsRequest();
        // Create empty search criteria instead of null to avoid NPE
        SearchRoomsCriteria searchCriteria = new SearchRoomsCriteria();
        searchCriteria.setCheckIn(null);
        searchCriteria.setCheckOut(null);
        searchCriteria.setCurrency(null);
        searchCriteria.setLocationId(null);
        searchCriteria.setLocationType(null);
        searchCriteria.setCountryCode(null);
        request.setSearchCriteria(searchCriteria);
        request.setRequestDetails(new RequestDetails());
        request.setFilterCriteria(new ArrayList<>());
        request.setExpDataMap(new HashMap<>());
        return request;
    }

    private SearchRoomsRequest createSearchRoomsRequestWithTimeSlot() {
        SearchRoomsRequest request = createValidSearchRoomsRequest();
        
        Slot slot = new Slot();
        slot.setTimeSlot(14);
        request.getSearchCriteria().setSlot(slot);
        
        return request;
    }

    private List<RoomStayCandidate> createDistributedRoomStayCandidates() {
        List<RoomStayCandidate> candidates = new ArrayList<>();
        
        RoomStayCandidate candidate = new RoomStayCandidate();
        List<GuestCount> guestCounts = new ArrayList<>();
        
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("2");
        guestCount.setAges(Arrays.asList(6, 8));
        guestCounts.add(guestCount);
        
        candidate.setGuestCounts(guestCounts);
        candidates.add(candidate);
        
        return candidates;
    }

    private List<RoomStayCandidate> createValidRoomStayCandidates() {
        List<RoomStayCandidate> candidates = new ArrayList<>();
        
        RoomStayCandidate candidate1 = new RoomStayCandidate();
        List<GuestCount> guestCounts1 = new ArrayList<>();
        GuestCount guestCount1 = new GuestCount();
        guestCount1.setCount("2");
        guestCount1.setAges(Arrays.asList(6));
        guestCounts1.add(guestCount1);
        candidate1.setGuestCounts(guestCounts1);
        candidates.add(candidate1);
        
        RoomStayCandidate candidate2 = new RoomStayCandidate();
        List<GuestCount> guestCounts2 = new ArrayList<>();
        GuestCount guestCount2 = new GuestCount();
        guestCount2.setCount("1");
        guestCount2.setAges(Arrays.asList(8));
        guestCounts2.add(guestCount2);
        candidate2.setGuestCounts(guestCounts2);
        candidates.add(candidate2);
        
        return candidates;
    }

    private List<RoomStayCandidate> createRoomStayCandidatesWithNullGuestCount() {
        List<RoomStayCandidate> candidates = new ArrayList<>();
        
        RoomStayCandidate candidate = new RoomStayCandidate();
        List<GuestCount> guestCounts = new ArrayList<>();
        guestCounts.add(null); // Add null guest count
        
        GuestCount validGuestCount = new GuestCount();
        validGuestCount.setCount("2");
        validGuestCount.setAges(Arrays.asList(6));
        guestCounts.add(validGuestCount);
        
        candidate.setGuestCounts(guestCounts);
        candidates.add(candidate);
        
        return candidates;
    }

    private List<RoomStayCandidate> createRoomStayCandidatesWithInvalidChildAges() {
        List<RoomStayCandidate> candidates = new ArrayList<>();
        
        RoomStayCandidate candidate = new RoomStayCandidate();
        List<GuestCount> guestCounts = new ArrayList<>();
        
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("2");
        guestCount.setAges(Arrays.asList(6, 15)); // Age 15 should be filtered out
        guestCounts.add(guestCount);
        
        candidate.setGuestCounts(guestCounts);
        candidates.add(candidate);
        
        return candidates;
    }

    private List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> createValidClientRoomStayCandidates() {
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> candidates = new ArrayList<>();
        
        com.mmt.hotels.clientgateway.request.RoomStayCandidate candidate1 = 
            new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        candidate1.setAdultCount(2);
        candidate1.setChildAges(Arrays.asList(6));
        candidates.add(candidate1);
        
        com.mmt.hotels.clientgateway.request.RoomStayCandidate candidate2 = 
            new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        candidate2.setAdultCount(1);
        candidate2.setChildAges(Arrays.asList(8));
        candidates.add(candidate2);
        
        return candidates;
    }

    private List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> createClientRoomStayCandidatesWithNullChildAges() {
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> candidates = new ArrayList<>();
        
        com.mmt.hotels.clientgateway.request.RoomStayCandidate candidate = 
            new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(null);
        candidates.add(candidate);
        
        return candidates;
    }

    private List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> createClientRoomStayCandidatesWithInvalidChildAges() {
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> candidates = new ArrayList<>();
        
        com.mmt.hotels.clientgateway.request.RoomStayCandidate candidate = 
            new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(Arrays.asList(8, 15)); // Age 15 should be filtered out
        candidates.add(candidate);
        
        return candidates;
    }

    private List<Filter> createNonPriceFilters() {
        List<Filter> filters = new ArrayList<>();
        
        Filter starFilter = new Filter();
        starFilter.setFilterGroup(FilterGroup.STAR_RATING);
        starFilter.setFilterValue("4,5");
        filters.add(starFilter);
        
        Filter amenityFilter = new Filter();
        amenityFilter.setFilterGroup(FilterGroup.AMENITIES);
        amenityFilter.setFilterValue("WIFI");
        filters.add(amenityFilter);
        
        return filters;
    }

    private List<Filter> createPriceFilters() {
        List<Filter> filters = new ArrayList<>();
        
        Filter priceFilter = new Filter();
        priceFilter.setFilterGroup(FilterGroup.HOTEL_PRICE);
        FilterRange range = new FilterRange();
        range.setMinValue(1000);
        range.setMaxValue(5000);
        priceFilter.setFilterRange(range);
        filters.add(priceFilter);
        
        return filters;
    }

    private List<Filter> createFiltersWithNullGroup() {
        List<Filter> filters = new ArrayList<>();
        
        Filter nullGroupFilter = new Filter();
        nullGroupFilter.setFilterGroup(null);
        nullGroupFilter.setFilterValue("INVALID");
        filters.add(nullGroupFilter);
        
        Filter validFilter = new Filter();
        validFilter.setFilterGroup(FilterGroup.STAR_RATING);
        validFilter.setFilterValue("4");
        filters.add(validFilter);
        
        return filters;
    }

    private List<Filter> createPriceFiltersWithInvalidRange() {
        List<Filter> filters = new ArrayList<>();
        
        Filter priceFilter = new Filter();
        priceFilter.setFilterGroup(FilterGroup.HOTEL_PRICE);
        // No range set, should be skipped
        filters.add(priceFilter);
        
        return filters;
    }
} 