package com.mmt.hotels.clientgateway.transformer.request;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import org.assertj.core.util.Arrays;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.Assert;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchHotelsRequestTransformerPWA;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsRequestTransformerPWATest {

	@InjectMocks
	SearchHotelsRequestTransformerPWA searchHotelsRequestTransformerPWA;

	@Mock
	MetricAspect metricAspect;

	@Mock
	Utility utility;

	@Test
	public void testConvertSearchHotelsRequest() {
		Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setDeviceDetails(new DeviceDetails());
		searchHotelsRequest.getDeviceDetails().setBookingDevice(Constants.DEVICE_OS_ANDROID);
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setSrLat(28d);
		searchHotelsRequest.getRequestDetails().setSrLng(28d);
		searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
		searchHotelsRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
		searchHotelsRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
		searchHotelsRequest.getSearchCriteria().setLat(28d);
		searchHotelsRequest.getSearchCriteria().setLng(28d);
		searchHotelsRequest.setImageDetails(new ImageDetails());
		searchHotelsRequest.getImageDetails().setCategories(new ArrayList<ImageCategory>());
		searchHotelsRequest.getImageDetails().getCategories().add(new ImageCategory());
		searchHotelsRequest.getImageDetails().getCategories().get(0).setCount(2);
		searchHotelsRequest.getImageDetails().getCategories().get(0).setHeight(2);
		searchHotelsRequest.getImageDetails().getCategories().get(0).setWidth(2);
		searchHotelsRequest.setFeatureFlags(new FeatureFlags());
		searchHotelsRequest.setSortCriteria(new SortCriteria());
		searchHotelsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
		searchHotelsRequest.setFilterGroupsToRemove(new ArrayList<>());
		searchHotelsRequest.getFilterGroupsToRemove().add(FilterGroup.AMENITIES);
		searchHotelsRequest.setFiltersToRemove(new ArrayList<>());
		searchHotelsRequest.getFiltersToRemove().add(new Filter(FilterGroup.AMENITIES, "test"));
		searchHotelsRequest.setFilterCriteria(new ArrayList<>());
		searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.AMENITIES, "test"));
		searchHotelsRequest.setReviewDetails(new ReviewDetails());
		searchHotelsRequest.getReviewDetails().setOtas(new ArrayList<>());
		searchHotelsRequest.getReviewDetails().setTagTypes(new ArrayList<>());
		searchHotelsRequest.setMapDetails(new MapDetails());
		searchHotelsRequest.getMapDetails().setLngSegments(5);
		searchHotelsRequest.getMapDetails().setLatSegments(5);
		searchHotelsRequest.getMapDetails().setLatLngBounds(new LatLngBounds());
		searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.VILLA_AND_APPT, "VILLA_AND_APPT"));
		SearchWrapperInputRequest searchWrapperInputRequest = searchHotelsRequestTransformerPWA.convertSearchRequest(searchHotelsRequest, new CommonModifierResponse());
		Assert.isTrue(searchWrapperInputRequest.getAppliedFilterMap().containsKey(
				com.mmt.hotels.filter.FilterGroup.ALT_ACCO_PROPERTY));
		Assert.notNull(searchWrapperInputRequest);
	}
}
