package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.AvailRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.AvailRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.AvailRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.AvailRoomsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.AvailRoomsResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsFactoryTest {


    @InjectMocks
    AvailRoomsFactory availRoomsFactory;
    
    @Mock
    AvailRoomsRequestTransformer availRoomsRequestTransformer;

    @Mock
    private AvailRoomsResponseTransformerPWA availRoomsResponseTransformerPWA;

    @Mock
    private AvailRoomsResponseTransformerDesktop availRoomsResponseTransformerDesktop;

    @Mock
    private AvailRoomsResponseTransformerAndroid availRoomsResponseTransformerAndroid;

    @Mock
    private AvailRoomsResponseTransformerIOS availRoomsResponseTransformerIOS;


    @Test
    public  void getRequestServiceTest(){
        Assert.assertTrue(availRoomsFactory.getRequestService("") instanceof  AvailRoomsRequestTransformer);
        Assert.assertTrue(availRoomsFactory.getRequestService("android") instanceof  AvailRoomsRequestTransformer);
        Assert.assertTrue(availRoomsFactory.getRequestService("IOS") instanceof  AvailRoomsRequestTransformer);
        Assert.assertTrue(availRoomsFactory.getRequestService("PWA") instanceof  AvailRoomsRequestTransformer);
        Assert.assertTrue(availRoomsFactory.getRequestService("DESKTOP") instanceof  AvailRoomsRequestTransformer);
        Assert.assertTrue(availRoomsFactory.getRequestService("trina") instanceof  AvailRoomsRequestTransformer );

    }

    @Test
    public void getResponseServiceTest(){
        AvailRoomsResponseTransformer resp = availRoomsFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof AvailRoomsResponseTransformerPWA);
        resp = availRoomsFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof AvailRoomsResponseTransformerDesktop);
        resp = availRoomsFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof AvailRoomsResponseTransformerAndroid);
        resp = availRoomsFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof AvailRoomsResponseTransformerIOS );
        resp = availRoomsFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(availRoomsFactory.getResponseService("test") instanceof AvailRoomsResponseTransformerDesktop);
    }

}
