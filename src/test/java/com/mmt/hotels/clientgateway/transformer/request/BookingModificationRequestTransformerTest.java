package com.mmt.hotels.clientgateway.transformer.request;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.modification.ProBookingRequest;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Ref;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class BookingModificationRequestTransformerTest {

    @InjectMocks
    BookingModRequestTransformer bkgModReqTransfmr;

    @Mock
    PaymentHelper payHelper;

    @Spy
    CommonHelper commonHelper;

    @Mock
    UserServiceExecutor userService;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(payHelper,"commonHelper", commonHelper);
        ReflectionTestUtils.setField(payHelper,"userService",userService);
        ReflectionTestUtils.setField(commonHelper,"userServiceExecutor",userService);
    }

    @Test
    public  void convertPriceRequestTest() throws Exception{
        RatePreviewRequest ratePreviewRequest = new ObjectMapper().readValue("{\"channel\":\"B2C\",\"checkin\":\"2021-04-05\",\"checkout\":\"2021-04-06\",\"city_code\":\"CTGOI\",\"country_code\":\"IN\",\"currency\":\"INR\",\"hash_key\":\"\",\"hotel_ids\":[\"201811201259373755\"],\"id_context\":\"CORP\",\"booking_id\":\"HTLQW576HP\",\"flavour\":\"DESKTOP\",\"app_version\":\"7.8.1\",\"device_id\":\"bc123ed132f\",\"room_criteria\":[{\"room_stay_candidates\":[{\"guest_counts\":[{\"count\":\"2\"}]}],\"room_code\":\"***********\",\"rate_plan_code\":\"************\",\"supplier_id\":\"ingoibibo\",\"sub_vendor\":\"\"}],\"traveller_email_commId\":[\"adasfa\"]}"
                ,RatePreviewRequest.class);
        CommonModifierResponse cmdResp  = new CommonModifierResponse();
        cmdResp.setExtendedUser(new ExtendedUser());
        cmdResp.getExtendedUser().setUuid("ABC");
        cmdResp.getExtendedUser().setProfileType("BUSINESS");
        cmdResp.getExtendedUser().setAccountId("ACDFd");
        cmdResp.getExtendedUser().setProfileId("MMT1231");
        cmdResp.getExtendedUser().setLoginInfoList(new ArrayList<>());
        cmdResp.getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        cmdResp.getExtendedUser().getLoginInfoList().get(0).setLoginId("************");
        cmdResp.getExtendedUser().getLoginInfoList().get(0).setLoginType(Constants.LOGIN_TYPE_MOBILE);
        cmdResp.setHydraResponse(new HydraResponse());
        cmdResp.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        cmdResp.getHydraResponse().setFlightBooker(true);
        PriceByHotelsRequestBody request = bkgModReqTransfmr.convertPriceRequest(ratePreviewRequest,cmdResp , "DETAIL"  );
        Assert.assertNotNull(request);
        Assert.assertNull(request.getRoomCriteria());
        Assert.assertNotNull(request.getRoomStayCandidates());
        Assert.assertNotNull(request.getSupplierCode());
        Assert.assertNotNull(request.getCheckin());
        Assert.assertNotNull(request.getCheckout());
        Assert.assertNotNull(request.getUuid());
        Assert.assertNotNull(request.getProfileType());


        request = bkgModReqTransfmr.convertPriceRequest(ratePreviewRequest,cmdResp , "REVIEW"  );
        Assert.assertNotNull(request);
        Assert.assertNotNull(request.getRoomCriteria());
    }

    @Test(expected = ClientGatewayException.class)
    public  void convertPaymentRequestExceptionTest() throws Exception{
        ProBookingRequest request = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue("{\"old_booking_id\":\"GOHTLDV3PTVHXM2\",\"auth_token\":\"MAT16aaa99ff4cc23528c812675490e4b21788a544421e70eaa38b88f9c91aa58913ffb22ca51266c17dd0eb37b7a67f2831P\",\"hash_key\":\"txnkey\",\"currency\":\"INR\",\"detail_page_url\":\"\",\"payment_detail\":{\"mode\":\"PAS\",\"channel\":\"B2C\",\"bnpl\":false},\"flavour\":\"ANDROID\",\"traveler_details\":[{\"title\":1,\"master_pax\":false,\"first_name\":\"ujjwal\",\"last_name\":\"tak\",\"email_id\":\"<EMAIL>\",\"mobile_no\":\"122323\",\"isd_code\":\"91\",\" room_no \":1}],\"gst_detail\":{\"gst_no\":\"somegstnum\",\"gst_company_name\":\"some name\",\"gst_company_address\":\"some address\"}}", ProBookingRequest.class);
        Map<String,String> headers = new HashMap<>();

        Mockito.doCallRealMethod().when(payHelper).getUserServiceResponse(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).populateAndCheckUserData(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).getPhoneCommId(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(Map.class));
        Mockito.doCallRealMethod().when(payHelper).modifyTravelerDetails(Mockito.any(BeginCheckoutReqBody.class));
        BeginCheckoutReqBody beginCheckoutReqBody = bkgModReqTransfmr.convertPaymentRequest(request, headers);

    }

    @Test
    public  void convertPaymentRequestTest() throws Exception{
        ProBookingRequest request = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue("{\"old_booking_id\":\"GOHTLDV3PTVHXM2\",\"auth_token\":\"MAT16aaa99ff4cc23528c812675490e4b21788a544421e70eaa38b88f9c91aa58913ffb22ca51266c17dd0eb37b7a67f2831P\",\"hash_key\":\"txnkey\",\"currency\":\"INR\",\"detail_page_url\":\"abc\",\"payment_detail\":{\"mode\":\"PAS\",\"channel\":\"B2C\",\"bnpl\":false},\"flavour\":\"ANDROID\",\"traveler_details\":[{\"title\":1,\"master_pax\":false,\"first_name\":\"ujjwal\",\"last_name\":\"tak\",\"email_id\":\"<EMAIL>\",\"mobile_no\":\"122323\",\"isd_code\":\"91\",\" room_no \":1}],\"gst_detail\":{\"gst_no\":\"somegstnum\",\"gst_company_name\":\"some name\",\"gst_company_address\":\"some address\"}}", ProBookingRequest.class);
        Map<String,String> headers = new HashMap<>();
        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
        userServiceResponse.getResult().getExtendedUser().setUuid("ABC");
        userServiceResponse.getResult().getExtendedUser().setProfileType("BUSINESS");
        userServiceResponse.getResult().getExtendedUser().setAccountId("ACDFd");
        userServiceResponse.getResult().getExtendedUser().setProfileId("MMT1231");
        userServiceResponse.getResult().getExtendedUser().setLoginInfoList(new ArrayList<>());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setVerified(true);
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginId("************");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginType(Constants.LOGIN_TYPE_MOBILE);
        Mockito.when(userService.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(userServiceResponse);

        Mockito.doCallRealMethod().when(payHelper).checkUserOTPValidated(Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).populateUUID(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).getUserServiceResponse(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).populateAndCheckUserData(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyTravelerDetails(Mockito.any(BeginCheckoutReqBody.class));
        Mockito.doCallRealMethod().when(payHelper).getPhoneCommId(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(Map.class));
        Mockito.doCallRealMethod().when(payHelper).sanitizeTimeOutURL(Mockito.any());
        BeginCheckoutReqBody beginCheckoutReqBody = bkgModReqTransfmr.convertPaymentRequest(request, headers);
        Assert.assertNotNull(beginCheckoutReqBody);
        Assert.assertNotNull(beginCheckoutReqBody.getTravelerDetailsList());
        Assert.assertTrue(beginCheckoutReqBody.getTravelerDetailsList().size() > 0);
        Assert.assertNotNull(beginCheckoutReqBody.getPaymentDetail().getMode());
        Assert.assertNotNull(beginCheckoutReqBody.getUserDetail());

    }

    @Test
    public  void convertRequestApprovalTest() throws  Exception{
        ProBookingRequest request = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue("{\"old_booking_id\":\"GOHTLDV3PTVHXM2\",\"auth_token\":\"MAT16aaa99ff4cc23528c812675490e4b21788a544421e70eaa38b88f9c91aa58913ffb22ca51266c17dd0eb37b7a67f2831P\",\"hash_key\":\"txnkey\",\"currency\":\"INR\",\"detail_page_url\":\"abc\",\"payment_detail\":{\"mode\":\"PAS\",\"channel\":\"B2C\",\"bnpl\":false},\"flavour\":\"ANDROID\",\"traveler_details\":[{\"title\":1,\"master_pax\":false,\"first_name\":\"ujjwal\",\"last_name\":\"tak\",\"email_id\":\"<EMAIL>\",\"mobile_no\":\"122323\",\"isd_code\":\"91\",\" room_no \":1}],\"gst_detail\":{\"gst_no\":\"somegstnum\",\"gst_company_name\":\"some name\",\"gst_company_address\":\"some address\"},\"workflow_status\":\"PENDING\",\"modification_reason\":\"dummy\"}", ProBookingRequest.class);
        Map<String,String> headers = new HashMap<>();
        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
        userServiceResponse.getResult().getExtendedUser().setUuid("ABC");
        userServiceResponse.getResult().getExtendedUser().setProfileType("BUSINESS");
        userServiceResponse.getResult().getExtendedUser().setAccountId("ACDFd");
        userServiceResponse.getResult().getExtendedUser().setProfileId("MMT1231");
        userServiceResponse.getResult().getExtendedUser().setLoginInfoList(new ArrayList<>());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setVerified(true);
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginId("************");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginType(Constants.LOGIN_TYPE_MOBILE);
        Mockito.when(userService.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(userServiceResponse);

        InitApprovalRequest approvalRequest = bkgModReqTransfmr.convertRequestApproval(request,headers);
        Assert.assertNotNull(approvalRequest);
        Assert.assertNotNull(approvalRequest.getTravelerDetailsList());
        Assert.assertTrue(approvalRequest.getTravelerDetailsList().size() > 0);
        Assert.assertNotNull(approvalRequest.getTxnKey());
        Assert.assertNotNull(approvalRequest.getUuid());


    }



}
