package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail;

// Orchestrator imports (input types)
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.PersuasionValue;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.Button;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.Style;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.Hover;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.TImer;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.BgGradient;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.BorderGradient;

// Client Gateway imports (output types) 
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.ChatBotPersuasionHelper;
import com.mmt.hotels.model.persuasion.response.PersuasionData;
import com.mmt.hotels.model.persuasion.response.StyleResponseBO;
import com.mmt.hotels.model.persuasion.response.HoverResponseBO;
import com.mmt.hotels.model.persuasion.response.Persuasion;

import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class ChatBotPersuasionHelperTest {

    @InjectMocks
    private ChatBotPersuasionHelper chatBotPersuasionHelper;

    @Before
    public void setup() {
        // No specific setup needed for this mapper
    }

    // ==================== MAIN METHOD TESTS ====================

    @Test
    public void testMapChatBotPersuasions_withNullInput_returnsNull() {
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(null);
        
        Assert.assertNull("Should return null for null input", result);
    }

    @Test
    public void testMapChatBotPersuasions_withEmptyMap_returnsEmptyMap() {
        Map<String, HotelPersuasionData> emptyMap = new HashMap<>();
        
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(emptyMap);
        
        Assert.assertNotNull("Should return valid map", result);
        Assert.assertTrue("Should return empty map", result.isEmpty());
    }

    @Test
    public void testMapChatBotPersuasions_withSingleEntry_mapsCorrectly() {
        Map<String, HotelPersuasionData> chatBotPersuasion = createSingleEntryPersuasionMap();
        
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(chatBotPersuasion);
        
        Assert.assertNotNull("Should return valid result", result);
        Assert.assertEquals("Should have one entry", 1, result.size());
        Assert.assertTrue("Should contain expected key", result.containsKey("location"));
        
        Persuasion persuasion = result.get("location");
        Assert.assertNotNull("Should have valid persuasion", persuasion);
        Assert.assertEquals("Should map placeholder", "LOC_PLACEHOLDER", persuasion.getPlaceholder());
        Assert.assertEquals("Should map template", "IMAGE_TEXT", persuasion.getTemplate());
        Assert.assertEquals("Should map template type", "HORIZONTAL", persuasion.getTemplateType());
        Assert.assertEquals("Should map top level text", "Great Location", persuasion.getTopLevelText());
        Assert.assertEquals("Should map separator", "|", persuasion.getSeparator());
    }

    @Test
    public void testMapChatBotPersuasions_withMultipleEntries_mapsAllEntries() {
        Map<String, HotelPersuasionData> chatBotPersuasion = createMultipleEntryPersuasionMap();
        
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(chatBotPersuasion);
        
        Assert.assertNotNull("Should return valid result", result);
        Assert.assertEquals("Should have three entries", 3, result.size());
        Assert.assertTrue("Should contain location key", result.containsKey("location"));
        Assert.assertTrue("Should contain amenities key", result.containsKey("amenities"));
        Assert.assertTrue("Should contain deals key", result.containsKey("deals"));
    }

    @Test
    public void testMapChatBotPersuasions_withNullHotelPersuasionData_handlesGracefully() {
        Map<String, HotelPersuasionData> chatBotPersuasion = new HashMap<>();
        chatBotPersuasion.put("location", createBasicHotelPersuasionData());
        chatBotPersuasion.put("null_entry", null);
        chatBotPersuasion.put("amenities", createBasicHotelPersuasionData());
        
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(chatBotPersuasion);
        
        Assert.assertNotNull("Should return valid result", result);
        Assert.assertEquals("Should have three entries (null data still creates entry)", 3, result.size());
        
        // Entry with null data should still exist but have null fields
        Persuasion nullPersuasion = result.get("null_entry");
        Assert.assertNotNull("Should have persuasion object for null data", nullPersuasion);
    }

    @Test
    public void testMapChatBotPersuasions_withCompletePersuasionData_mapsAllFields() {
        Map<String, HotelPersuasionData> chatBotPersuasion = createCompletePersuasionMap();
        
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(chatBotPersuasion);
        
        Assert.assertNotNull("Should return valid result", result);
        Assert.assertEquals("Should have one entry", 1, result.size());
        
        Persuasion persuasion = result.get("complete");
        Assert.assertNotNull("Should have valid persuasion", persuasion);
        Assert.assertNotNull("Should map data list", persuasion.getData());
        Assert.assertNotNull("Should map style", persuasion.getStyle());
        Assert.assertNotNull("Should map hover", persuasion.getHover());
        Assert.assertNotNull("Should map extra details", persuasion.getExtraDetails());
        Assert.assertEquals("Should have correct number of data items", 2, persuasion.getData().size());
    }

    @Test
    public void testMapChatBotPersuasions_withExceptionInMapping_logsErrorAndContinues() {
        // This test is hard to trigger an exception in the current implementation
        // since it's mostly straightforward field mapping. The try-catch is defensive.
        Map<String, HotelPersuasionData> chatBotPersuasion = createSingleEntryPersuasionMap();
        
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(chatBotPersuasion);
        
        Assert.assertNotNull("Should return valid result even if internal errors occur", result);
        Assert.assertEquals("Should still process valid entries", 1, result.size());
    }

    // ==================== STYLE MAPPING TESTS ====================

    @Test
    public void testMapStyleToStyleResponseBO_withNullStyle_returnsNull() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        Object result = method.invoke(chatBotPersuasionHelper, (Style) null);
        
        Assert.assertNull("Should return null for null style", result);
    }

    @Test
    public void testMapStyleToStyleResponseBO_withBasicFields_mapsAllStringFields() throws Exception {
        Style style = createStyleWithBasicFields();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        Assert.assertNotNull("Should return valid style response", result);
        Assert.assertEquals("Should map text color", "#000000", result.getTextColor());
        Assert.assertEquals("Should map bg color", "#FFFFFF", result.getBgColor());
        Assert.assertEquals("Should map secondary bg color", "#F0F0F0", result.getSecondaryBgColor());
        Assert.assertEquals("Should map font size", "14px", result.getFontSize());
        Assert.assertEquals("Should map border color", "#CCCCCC", result.getBorderColor());
        Assert.assertEquals("Should map font type", "Arial", result.getFontType());
        Assert.assertEquals("Should map bg URL", "https://test.com/bg.png", result.getBgUrl());
        Assert.assertEquals("Should map corner radii", "8px", result.getCornerRadii());
        Assert.assertEquals("Should map separator type", "LINE", result.getSeparatorType());
        Assert.assertEquals("Should map image URL", "https://test.com/image.png", result.getImageUrl());
        Assert.assertEquals("Should map gravity", "CENTER", result.getGravity());
        Assert.assertEquals("Should map title color", "#333333", result.getTitleColor());
        Assert.assertEquals("Should map title", "Test Title", result.getTitle());
    }

    @Test
    public void testMapStyleToStyleResponseBO_withIntegerFields_mapsAllIntegerFields() throws Exception {
        Style style = createStyleWithIntegerFields();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        Assert.assertNotNull("Should return valid style response", result);
        Assert.assertEquals("Should map border size", (Integer) 2, result.getBorderSize());
        Assert.assertEquals("Should map icon width", (Integer) 24, result.getIconWidth());
        Assert.assertEquals("Should map icon height", (Integer) 24, result.getIconHeight());
        Assert.assertEquals("Should map horizontal space", (Integer) 8, result.getHorizontalSpace());
        Assert.assertEquals("Should map vertical space", (Integer) 4, result.getVerticalSpace());
        Assert.assertEquals("Should map max lines", (Integer) 3, result.getMaxLines());
        Assert.assertEquals("Should map max count", (Integer) 5, result.getMaxCount());
        Assert.assertEquals("Should map image width", (Integer) 100, result.getImageWidth());
        Assert.assertEquals("Should map image height", (Integer) 80, result.getImageHeight());
    }

    @Test
    public void testMapStyleToStyleResponseBO_withListFields_mapsAllListFields() throws Exception {
        Style style = createStyleWithListFields();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        Assert.assertNotNull("Should return valid style response", result);
        Assert.assertNotNull("Should map style classes", result.getStyleClasses());
        Assert.assertEquals("Should have correct number of style classes", 2, result.getStyleClasses().size());
        Assert.assertTrue("Should contain first style class", result.getStyleClasses().contains("class1"));
        Assert.assertTrue("Should contain second style class", result.getStyleClasses().contains("class2"));
        
        Assert.assertNotNull("Should map corners", result.getCorners());
        Assert.assertEquals("Should have correct number of corners", 3, result.getCorners().size());
        
        Assert.assertNotNull("Should map border style", result.getBorderStyle());
        Assert.assertEquals("Should have correct number of border styles", 2, result.getBorderStyle().size());
    }

    @Test
    public void testMapStyleToStyleResponseBO_withBgGradient_mapsBgGradient() throws Exception {
        Style style = createStyleWithBgGradient();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        Assert.assertNotNull("Should return valid style response", result);
        Assert.assertNotNull("Should map bg gradient", result.getBgGradient());
        Assert.assertEquals("Should map gradient angle", "45deg", result.getBgGradient().getAngle());
        Assert.assertEquals("Should map gradient start", "#FF0000", result.getBgGradient().getStart());
        Assert.assertEquals("Should map gradient end", "#00FF00", result.getBgGradient().getEnd());
    }

    @Test
    public void testMapStyleToStyleResponseBO_withTextGradient_mapsTextGradient() throws Exception {
        Style style = createStyleWithTextGradient();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        Assert.assertNotNull("Should return valid style response", result);
        Assert.assertNotNull("Should map text gradient", result.getTextGradient());
        Assert.assertEquals("Should map text gradient angle", "90deg", result.getTextGradient().getAngle());
        Assert.assertEquals("Should map text gradient start", "#000000", result.getTextGradient().getStart());
        Assert.assertEquals("Should map text gradient end", "#FFFFFF", result.getTextGradient().getEnd());
    }

    @Test
    public void testMapStyleToStyleResponseBO_withBorderGradient_mapsBorderGradient() throws Exception {
        Style style = createStyleWithBorderGradient();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        Assert.assertNotNull("Should return valid style response", result);
        // Border gradient is set via reflection, so we can't directly verify it was set
        // The method should complete without exception
    }

    @Test
    public void testMapStyleToStyleResponseBO_withAllGradients_mapsAllGradientTypes() throws Exception {
        Style style = createStyleWithAllGradients();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        Assert.assertNotNull("Should return valid style response", result);
        Assert.assertNotNull("Should map bg gradient", result.getBgGradient());
        Assert.assertNotNull("Should map text gradient", result.getTextGradient());
        // Border gradient set via reflection, no direct verification possible
    }

    // ==================== HOVER MAPPING TESTS ====================

    @Test
    public void testMapHoverToHoverResponseBO_withNullHover_returnsNull() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        Object result = method.invoke(chatBotPersuasionHelper, (Hover) null);
        
        Assert.assertNull("Should return null for null hover", result);
    }

    @Test
    public void testMapHoverToHoverResponseBO_withBasicFields_mapsAllFields() throws Exception {
        Hover hover = createHoverWithBasicFields();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, hover);
        
        Assert.assertNotNull("Should return valid hover response", result);
        Assert.assertEquals("Should map heading text", "Hover Heading", result.getHeadingText());
        Assert.assertEquals("Should map tooltip type", "TOOLTIP", result.getTooltipType());
        Assert.assertEquals("Should map cta text", "Click Here", result.getCtaText());
        Assert.assertEquals("Should map cta color", "#0066CC", result.getCtaColor());
        Assert.assertEquals("Should map open hover threshold", (Integer) 3, result.getOpenHoverThreshold());
    }

    @Test
    public void testMapHoverToHoverResponseBO_withStyle_mapsStyleCorrectly() throws Exception {
        Hover hover = createHoverWithStyle();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, hover);
        
        Assert.assertNotNull("Should return valid hover response", result);
        Assert.assertNotNull("Should map style", result.getStyle());
        Assert.assertEquals("Should map style text color", "#000000", result.getStyle().getTextColor());
    }

    @Test
    public void testMapHoverToHoverResponseBO_withHeadingStyle_mapsHeadingStyle() throws Exception {
        Hover hover = createHoverWithHeadingStyle();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, hover);
        
        Assert.assertNotNull("Should return valid hover response", result);
        Assert.assertNotNull("Should map heading style", result.getHeadingStyle());
        Assert.assertEquals("Should map heading style bg color", "#F5F5F5", result.getHeadingStyle().getBgColor());
    }

    @Test
    public void testMapHoverToHoverResponseBO_withDataObject_mapsDataDirectly() throws Exception {
        Hover hover = createHoverWithDataObject();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, hover);
        
        Assert.assertNotNull("Should return valid hover response", result);
        Assert.assertNotNull("Should map data object", result.getData());
        Assert.assertEquals("Should map data object directly", "Test Data", result.getData());
    }

    @Test
    public void testMapHoverToHoverResponseBO_withCompleteHover_mapsAllFields() throws Exception {
        Hover hover = createCompleteHover();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, hover);
        
        Assert.assertNotNull("Should return valid hover response", result);
        Assert.assertNotNull("Should have heading text", result.getHeadingText());
        Assert.assertNotNull("Should have style", result.getStyle());
        Assert.assertNotNull("Should have heading style", result.getHeadingStyle());
        Assert.assertNotNull("Should have data", result.getData());
    }

    // ==================== EXTRA DETAILS MAPPING TESTS ====================

    @Test
    public void testMapExtraDetailsToExtraDetails_withNullExtraDetails_returnsNull() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapExtraDetailsToExtraDetails", 
            com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails.class);
        method.setAccessible(true);
        
        Object result = method.invoke(chatBotPersuasionHelper,
            (com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails) null);
        
        Assert.assertNull("Should return null for null extra details", result);
    }

    @Test
    public void testMapExtraDetailsToExtraDetails_withBasicFields_mapsAllFields() throws Exception {
        com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails extraDetails = 
            createBasicOrchestratorExtraDetails();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapExtraDetailsToExtraDetails", 
            com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.ExtraDetails result = 
            (com.mmt.hotels.model.persuasion.response.ExtraDetails) method.invoke(chatBotPersuasionHelper, extraDetails);
        
        Assert.assertNotNull("Should return valid extra details", result);
        Assert.assertEquals("Should map icon URL", "https://test.com/icon.png", result.getIconUrl());
        Assert.assertEquals("Should map action type", "CLICK", result.getActionType());
        Assert.assertEquals("Should map title", "Extra Title", result.getTitle());
    }

    @Test
    public void testMapExtraDetailsToExtraDetails_withStyle_mapsStyleCorrectly() throws Exception {
        com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails extraDetails = 
            createOrchestratorExtraDetailsWithStyle();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapExtraDetailsToExtraDetails", 
            com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.ExtraDetails result = 
            (com.mmt.hotels.model.persuasion.response.ExtraDetails) method.invoke(chatBotPersuasionHelper, extraDetails);
        
        Assert.assertNotNull("Should return valid extra details", result);
        Assert.assertNotNull("Should map style", result.getStyle());
        Assert.assertEquals("Should map style font size", "12px", result.getStyle().getFontSize());
    }

    // ==================== PERSUASION VALUE MAPPING TESTS ====================

    @Test
    public void testMapPersuasionValueToPersuasionData_withNullValue_returnsNull() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        Object result = method.invoke(chatBotPersuasionHelper, (PersuasionValue) null);
        
        Assert.assertNull("Should return null for null value", result);
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withBasicStringFields_mapsAllStrings() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithStringFields();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertEquals("Should map ID", "PERS1", result.getId());
        Assert.assertEquals("Should map text", "Persuasion Text", result.getText());
        Assert.assertEquals("Should map subtext", "Persuasion Subtext", result.getSubtext());
        Assert.assertEquals("Should map persuasion type", "LOCATION", result.getPersuasionType());
        Assert.assertEquals("Should map icon URL", "https://test.com/icon.png", result.getIconurl());
        Assert.assertEquals("Should map image URL", "https://test.com/image.png", result.getImageUrl());
        Assert.assertEquals("Should map action URL", "https://test.com/action", result.getActionurl());
        Assert.assertEquals("Should map icon type", "ROUND", result.getIcontype());
        Assert.assertEquals("Should map separator", " | ", result.getSeparator());
        Assert.assertEquals("Should map action type", "NAVIGATE", result.getActionType());
        Assert.assertEquals("Should map persuasion key", "PERS_KEY", result.getPersuasionKey());
        Assert.assertEquals("Should map outer level persuasion text key", "OUTER_KEY", result.getOuterLevelPersuasionTextKey());
        Assert.assertEquals("Should map persuasion template", "TEMPLATE1", result.getPersuasionTemplate());
        Assert.assertEquals("Should map persuasion text", "Template Text", result.getPersuasionText());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withBooleanFields_mapsAllBooleans() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithBooleanFields();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertTrue("Should map has action", result.isHasAction());
        Assert.assertFalse("Should map html", result.isHtml());
        Assert.assertTrue("Should map horizontal", result.isHorizontal());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withIntegerFields_mapsIntegerFields() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithIntegerFields();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertEquals("Should map multi persuasion count", (Integer) 5, result.getMultiPersuasionCount());
        Assert.assertEquals("Should map multi persuasion priority", (Integer) 1, result.getMultiPersuasionPriority());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withComplexObjects_mapsAllComplexObjects() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithComplexObjects();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertNotNull("Should map style", result.getStyle());
        Assert.assertNotNull("Should map subtext style", result.getSubtextStyle());
        Assert.assertNotNull("Should map hover", result.getHover());
        Assert.assertNotNull("Should map button", result.getButton());
        Assert.assertNotNull("Should map timer", result.getTimer());
        Assert.assertNotNull("Should map inclusions", result.getInclusions());
        Assert.assertNotNull("Should map persuasion title", result.getPersuasionTitle());
        Assert.assertNotNull("Should map extra data", result.getExtraData());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withButton_mapsButtonCorrectly() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithButton();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertNotNull("Should map button", result.getButton());
        Assert.assertEquals("Should map button text", "Button Text", result.getButton().getText());
        Assert.assertEquals("Should map button action URL", "https://test.com/button", result.getButton().getActionUrl());
        Assert.assertTrue("Should map button has action", result.getButton().isHasAction());
        Assert.assertEquals("Should map button action type", "BUTTON_ACTION", result.getButton().getActionType());
        Assert.assertNotNull("Should map button style", result.getButton().getStyle());
        Assert.assertNotNull("Should map button hover", result.getButton().getHover());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withTimer_mapsTimerCorrectly() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithTimer();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertNotNull("Should map timer", result.getTimer());
        Assert.assertEquals("Should map timer expiry", Long.valueOf(1704067199000L), result.getTimer().getExpiry());
        Assert.assertEquals("Should map timer expiry format", "ISO_INSTANT", result.getTimer().getExpiryFormat());
        Assert.assertNotNull("Should map timer style", result.getTimer().getStyle());
        Assert.assertNotNull("Should map timer hover", result.getTimer().getHover());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withInclusions_mapsInclusionsList() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithInclusions();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertNotNull("Should map inclusions", result.getInclusions());
        Assert.assertEquals("Should have correct number of inclusions", 3, result.getInclusions().size());
        Assert.assertTrue("Should contain first inclusion", result.getInclusions().contains("Free WiFi"));
        Assert.assertTrue("Should contain second inclusion", result.getInclusions().contains("Free Breakfast"));
        Assert.assertTrue("Should contain third inclusion", result.getInclusions().contains("Pool Access"));
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withPersuasionTitle_mapsTitle() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithPersuasionTitle();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertNotNull("Should map persuasion title", result.getPersuasionTitle());
        Assert.assertEquals("Should map title text", "Title Text", result.getPersuasionTitle().getText());
        Assert.assertNotNull("Should map title style", result.getPersuasionTitle().getStyle());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withChildPersuasions_mapsRecursively() throws Exception {
        PersuasionValue persuasionValue = createPersuasionValueWithChildPersuasions();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertNotNull("Should map child persuasions", result.getChildPersuasions());
        Assert.assertEquals("Should have correct number of child persuasions", 2, result.getChildPersuasions().size());
        
        PersuasionData firstChild = result.getChildPersuasions().get(0);
        Assert.assertEquals("Should map child text", "Child 1 Text", firstChild.getText());
        Assert.assertEquals("Should map child ID", "CHILD1", firstChild.getId());
        
        PersuasionData secondChild = result.getChildPersuasions().get(1);
        Assert.assertEquals("Should map second child text", "Child 2 Text", secondChild.getText());
        Assert.assertEquals("Should map second child ID", "CHILD2", secondChild.getId());
    }

    @Test
    public void testMapPersuasionValueToPersuasionData_withComplexNestedStructure_mapsAllLevels() throws Exception {
        PersuasionValue persuasionValue = createComplexNestedPersuasionValue();
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod(
            "mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        PersuasionData result = (PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        Assert.assertNotNull("Should return valid persuasion data", result);
        Assert.assertNotNull("Should map all complex objects", result.getStyle());
        Assert.assertNotNull("Should map hover", result.getHover());
        Assert.assertNotNull("Should map button", result.getButton());
        Assert.assertNotNull("Should map timer", result.getTimer());
        Assert.assertNotNull("Should map child persuasions", result.getChildPersuasions());
        Assert.assertEquals("Should have child persuasions", 1, result.getChildPersuasions().size());
        
        PersuasionData child = result.getChildPersuasions().get(0);
        Assert.assertNotNull("Child should also have complex objects", child.getStyle());
    }

    // ==================== TEST DATA BUILDERS ====================

    private Map<String, HotelPersuasionData> createSingleEntryPersuasionMap() {
        Map<String, HotelPersuasionData> map = new HashMap<>();
        map.put("location", createBasicHotelPersuasionData());
        return map;
    }

    private Map<String, HotelPersuasionData> createMultipleEntryPersuasionMap() {
        Map<String, HotelPersuasionData> map = new HashMap<>();
        map.put("location", createBasicHotelPersuasionData());
        map.put("amenities", createBasicHotelPersuasionData());
        map.put("deals", createBasicHotelPersuasionData());
        return map;
    }

    private Map<String, HotelPersuasionData> createCompletePersuasionMap() {
        Map<String, HotelPersuasionData> map = new HashMap<>();
        map.put("complete", createCompleteHotelPersuasionData());
        return map;
    }

    private HotelPersuasionData createBasicHotelPersuasionData() {
        HotelPersuasionData data = new HotelPersuasionData();
        data.setPlaceholder("LOC_PLACEHOLDER");
        data.setTemplate("IMAGE_TEXT");
        data.setTemplateType("HORIZONTAL");
        data.setTopLevelText("Great Location");
        data.setSeparator("|");
        return data;
    }

    private HotelPersuasionData createCompleteHotelPersuasionData() {
        HotelPersuasionData data = createBasicHotelPersuasionData();
        
        // Add data list
        List<PersuasionValue> dataList = new ArrayList<>();
        dataList.add(createBasicPersuasionValue());
        dataList.add(createBasicPersuasionValue());
        data.setData(dataList);
        
        // Add style
        data.setStyle(createStyleWithBasicFields());
        
        // Add hover
        data.setHover(createHoverWithBasicFields());
        
        // Add extra details
        data.setExtraDetails(createBasicOrchestratorExtraDetails());
        
        return data;
    }

    private PersuasionValue createBasicPersuasionValue() {
        PersuasionValue value = new PersuasionValue();
        value.setId("PERS1");
        value.setText("Persuasion Text");
        value.setSubtext("Persuasion Subtext");
        return value;
    }

    private Style createStyleWithBasicFields() {
        Style style = new Style();
        style.setTextColor("#000000");
        style.setBgColor("#FFFFFF");
        style.setSecondaryBgColor("#F0F0F0");
        style.setFontSize("14px");
        style.setBorderColor("#CCCCCC");
        style.setFontType("Arial");
        style.setBgUrl("https://test.com/bg.png");
        style.setCornerRadii("8px");
        style.setSeparatorType("LINE");
        style.setImageUrl("https://test.com/image.png");
        style.setGravity("CENTER");
        style.setTitleColor("#333333");
        style.setTitle("Test Title");
        return style;
    }

    private Style createStyleWithIntegerFields() {
        Style style = new Style();
        style.setBorderSize(2);
        style.setIconWidth(24);
        style.setIconHeight(24);
        style.setHorizontalSpace(8);
        style.setVerticalSpace(4);
        style.setMaxLines(3);
        style.setMaxCount(5);
        style.setImageWidth(100);
        style.setImageHeight(80);
        return style;
    }

    private Style createStyleWithListFields() {
        Style style = new Style();
        
        List<String> styleClasses = new ArrayList<>();
        styleClasses.add("class1");
        styleClasses.add("class2");
        style.setStyleClasses(styleClasses);
        
        List<Integer> corners = new ArrayList<>();
        corners.add(5);
        corners.add(10);
        corners.add(15);
        style.setCorners(corners);
        
        List<String> borderStyle = new ArrayList<>();
        borderStyle.add("solid");
        borderStyle.add("dashed");
        style.setBorderStyle(borderStyle);
        
        return style;
    }

    private Style createStyleWithBgGradient() {
        Style style = new Style();
        
        BgGradient bgGradient = new BgGradient();
        bgGradient.setAngle("45deg");
        bgGradient.setStart("#FF0000");
        bgGradient.setEnd("#00FF00");
        style.setBgGradient(bgGradient);
        
        return style;
    }

    private Style createStyleWithTextGradient() {
        Style style = new Style();
        
        BgGradient textGradient = new BgGradient();
        textGradient.setAngle("90deg");
        textGradient.setStart("#000000");
        textGradient.setEnd("#FFFFFF");
        style.setTextGradient(textGradient);
        
        return style;
    }

    private Style createStyleWithBorderGradient() {
        Style style = new Style();
        
        BorderGradient borderGradient = new BorderGradient();
        borderGradient.setAngle("180deg");
        borderGradient.setStart("#CCCCCC");
        borderGradient.setEnd("#999999");
        style.setBorderGradient(borderGradient);
        
        return style;
    }

    private Style createStyleWithAllGradients() {
        Style style = createStyleWithBgGradient();
        
        BgGradient textGradient = new BgGradient();
        textGradient.setAngle("90deg");
        textGradient.setStart("#000000");
        textGradient.setEnd("#FFFFFF");
        style.setTextGradient(textGradient);
        
        BorderGradient borderGradient = new BorderGradient();
        borderGradient.setAngle("180deg");
        borderGradient.setStart("#CCCCCC");
        borderGradient.setEnd("#999999");
        style.setBorderGradient(borderGradient);
        
        return style;
    }

    private Hover createHoverWithBasicFields() {
        Hover hover = new Hover();
        hover.setHeadingText("Hover Heading");
        hover.setTooltipType("TOOLTIP");
        hover.setCtaText("Click Here");
        hover.setCtaColor("#0066CC");
        hover.setOpenHoverThreshold(3);
        return hover;
    }

    private Hover createHoverWithStyle() {
        Hover hover = createHoverWithBasicFields();
        hover.setStyle(createStyleWithBasicFields());
        return hover;
    }

    private Hover createHoverWithHeadingStyle() {
        Hover hover = createHoverWithBasicFields();
        
        Style headingStyle = new Style();
        headingStyle.setBgColor("#F5F5F5");
        hover.setHeadingStyle(headingStyle);
        
        return hover;
    }

    private Hover createHoverWithDataObject() {
        Hover hover = createHoverWithBasicFields();
        hover.setData("Test Data");
        return hover;
    }

    private Hover createCompleteHover() {
        Hover hover = createHoverWithBasicFields();
        hover.setStyle(createStyleWithBasicFields());
        hover.setHeadingStyle(createStyleWithBasicFields());
        hover.setData("Complete Data");
        return hover;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails createBasicOrchestratorExtraDetails() {
        com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails extraDetails = 
            new com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails();
        extraDetails.setIconUrl("https://test.com/icon.png");
        extraDetails.setActionType("CLICK");
        extraDetails.setTitle("Extra Title");
        return extraDetails;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails createOrchestratorExtraDetailsWithStyle() {
        com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails extraDetails = 
            createBasicOrchestratorExtraDetails();
        
        Style style = new Style();
        style.setFontSize("12px");
        extraDetails.setStyle(style);
        
        return extraDetails;
    }

    private PersuasionValue createPersuasionValueWithStringFields() {
        PersuasionValue value = new PersuasionValue();
        value.setId("PERS1");
        value.setText("Persuasion Text");
        value.setSubtext("Persuasion Subtext");
        value.setPersuasionType("LOCATION");
        value.setIconurl("https://test.com/icon.png");
        value.setImageUrl("https://test.com/image.png");
        value.setActionurl("https://test.com/action");
        value.setIcontype("ROUND");
        value.setSeparator(" | ");
        value.setActionType("NAVIGATE");
        value.setPersuasionKey("PERS_KEY");
        value.setOuterLevelPersuasionTextKey("OUTER_KEY");
        value.setPersuasionTemplate("TEMPLATE1");
        value.setPersuasionText("Template Text");
        return value;
    }

    private PersuasionValue createPersuasionValueWithBooleanFields() {
        PersuasionValue value = new PersuasionValue();
        value.setHasAction(true);
        value.setHtml(false);
        value.setHorizontal(true);
        return value;
    }

    private PersuasionValue createPersuasionValueWithIntegerFields() {
        PersuasionValue value = new PersuasionValue();
        value.setMultiPersuasionCount(5);
        value.setMultiPersuasionPriority(1);
        return value;
    }

    private PersuasionValue createPersuasionValueWithComplexObjects() {
        PersuasionValue value = createPersuasionValueWithStringFields();
        value.setStyle(createStyleWithBasicFields());
        value.setSubtextStyle(createStyleWithBasicFields());
        value.setHover(createHoverWithBasicFields());
        value.setButton(createButton());
        value.setTimer(createTimer());
        value.setInclusions(createInclusions());
        value.setPersuasionTitle(createPersuasionTitle());
        value.setExtraData(createBasicOrchestratorExtraDetails());
        return value;
    }

    private PersuasionValue createPersuasionValueWithButton() {
        PersuasionValue value = createBasicPersuasionValue();
        value.setButton(createButton());
        return value;
    }

    private PersuasionValue createPersuasionValueWithTimer() {
        PersuasionValue value = createBasicPersuasionValue();
        value.setTimer(createTimer());
        return value;
    }

    private PersuasionValue createPersuasionValueWithInclusions() {
        PersuasionValue value = createBasicPersuasionValue();
        value.setInclusions(createInclusions());
        return value;
    }

    private PersuasionValue createPersuasionValueWithPersuasionTitle() {
        PersuasionValue value = createBasicPersuasionValue();
        value.setPersuasionTitle(createPersuasionTitle());
        return value;
    }

    private PersuasionValue createPersuasionValueWithChildPersuasions() {
        PersuasionValue value = createBasicPersuasionValue();
        
        List<PersuasionValue> children = new ArrayList<>();
        
        PersuasionValue child1 = new PersuasionValue();
        child1.setId("CHILD1");
        child1.setText("Child 1 Text");
        children.add(child1);
        
        PersuasionValue child2 = new PersuasionValue();
        child2.setId("CHILD2");
        child2.setText("Child 2 Text");
        children.add(child2);
        
        value.setChildPersuasions(children);
        return value;
    }

    private PersuasionValue createComplexNestedPersuasionValue() {
        PersuasionValue value = createPersuasionValueWithComplexObjects();
        
        // Add child with its own complex objects
        List<PersuasionValue> children = new ArrayList<>();
        PersuasionValue child = createBasicPersuasionValue();
        child.setStyle(createStyleWithBasicFields());
        children.add(child);
        
        value.setChildPersuasions(children);
        return value;
    }

    private Button createButton() {
        Button button = new Button();
        button.setText("Button Text");
        button.setActionUrl("https://test.com/button");
        button.setHasAction(true);
        button.setActionType("BUTTON_ACTION");
        button.setStyle(createStyleWithBasicFields());
        button.setHover(createHoverWithBasicFields());
        return button;
    }

    private TImer createTimer() {
        TImer timer = new TImer();
        timer.setExpiry(1704067199000L); // Long timestamp instead of String
        timer.setExpiryFormat("ISO_INSTANT");
        timer.setStyle(createStyleWithBasicFields());
        timer.setHover(createHoverWithBasicFields());
        return timer;
    }

    private List<String> createInclusions() {
        List<String> inclusions = new ArrayList<>();
        inclusions.add("Free WiFi");
        inclusions.add("Free Breakfast");
        inclusions.add("Pool Access");
        return inclusions;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.peithos.PersuasionTitle createPersuasionTitle() {
        com.gommt.hotels.orchestrator.detail.model.response.peithos.PersuasionTitle title = 
            new com.gommt.hotels.orchestrator.detail.model.response.peithos.PersuasionTitle();
        title.setText("Title Text");
        title.setStyle(createStyleWithBasicFields());
        return title;
    }
} 