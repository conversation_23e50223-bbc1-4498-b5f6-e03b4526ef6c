package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.availrooms.DoubleBlackInfo;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchRoomsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.pricing.jsonviews.RTBPreApprovedCard;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.model.response.staticdata.ImageType;
import com.mmt.hotels.model.response.staticdata.MediaData;
import com.mmt.hotels.model.response.staticdata.ProfessionalImageEntity;
import com.mmt.hotels.model.response.staticdata.Space;
import com.mmt.hotels.model.response.staticdata.SpaceData;
import com.mmt.hotels.model.response.staticdata.VideoInfo;
import com.mmt.model.*;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.RoomInfo;
import junit.framework.Assert;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.EXP_BNPL_NEW_VARIANT;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_ACC_ONLY;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_ALL_MEALS_AI;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BED_ONLY;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_ROOM_ONLY;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_ACC_ONLY;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_ALL_MEALS_AI;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BED_ONLY;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_PLAN_CODE_ROOM_ONLY;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsResponseTransformerPWATest {

    @InjectMocks
    SearchRoomsResponseTransformerPWA searchRoomsResponseTransformerPWA;

    @InjectMocks
    DateUtil dateUtil;

    @Mock
    CommonResponseTransformer commonResponseTransformer;

    @Mock
    DayUseUtil dayUseUtil;

    @InjectMocks
    private Utility utility;

    @Mock
    MobConfigHelper mobConfigHelper;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;

    @Mock
    OfferCardUtil offerCardUtil;

    ObjectMapperUtil objectMapperUtil = new ObjectMapperUtil();

    @Before
    public void setup() {
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
        utility = Mockito.spy(new Utility());
        MockitoAnnotations.initMocks(this);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        Map<String,Map<String, Map<String,String>>> ratePlanNameMap = new HashMap<>();
        ratePlanNameMap.put(Constants.DEFAULT,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_FC,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_BED_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_NR,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_BED_TYPE,"{NR}");

        ReflectionTestUtils.setField(utility, "ratePlanNameMap", ratePlanNameMap);
        ReflectionTestUtils.setField(utility, "ratePlanNameMapRedesign", ratePlanNameMap);
        ReflectionTestUtils.setField(utility,"apLimitForInclusionIcons",1);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "rtbCardConfigs", new HashMap<>());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "pahGccText", "No prepayment needed, Reserve at AED 0");

        ObjectMapper mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
    }

    @Test
    public void testConvertSearchRoomsResponse() {
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"mealplanFilterEnable",true);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);

        HotelRates hotelRates = new HotelRates();
        hotelRates.setBnplBaseAmount(10d);
        hotelRates.setBlackEligible(false);
        hotelRates.setIsBNPLAvailable(false);
        hotelRates.setPahWalletApplicable(false);
        hotelRates.setPnAvlbl(true);
        hotelRates.setPanCardRequired(true);
        hotelRates.setShowFcBanner(true);
        hotelRates.setSoldOut(false);
        hotelRates.setBreakFast(true);
        hotelRates.setFreeCancellation(true);
        hotelRates.setBreakFastAvailable(true);
        hotelRates.setFreeCancellationAvailable(true);
        hotelRates.setPAHTariffAvailable(true);

        hotelRates.setRecommendedRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRecommendedRoomTypeDetails().setTotalDisplayFare(new DisplayFare());

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(1d);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");
        displayPriceBreakDown.getCouponInfo().setDiscountAmount(100.0);

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(1d);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
        displayPriceBreakDownList.get(0).getCouponInfo().setDiscountAmount(100.0);

        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        hotelRates.getRecommendedRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());
        hotelRates.getRecommendedRoomTypeDetails().setOccupancyDetails(new OccupancyDetails());
        hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().setAdult(2);

        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();

        ratePlanCB.setAvailDetails(new AvailDetails());
        ratePlanCB.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanCB.setCancelPenaltyList(new ArrayList<CancelPenalty>());
        ratePlanCB.getCancelPenaltyList().add(new CancelPenalty());
        ratePlanCB.getCancelPenaltyList().get(0).setPenaltyDescription(new Penalty());
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.setMealPlans(new ArrayList<MealPlan>());
        ratePlanCB.getMealPlans().add(new MealPlan());
        ratePlanCB.getMealPlans().get(0).setCode("SMAP");
        ratePlanCB.getMealPlans().get(0).setValue("abcd");
        ratePlanCB.setPaymentDetails(new PaymentDetails());
        ratePlanCB.getPaymentDetails().setPaymentMode(PaymentMode.PAS);
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        ratePlanCB.setSupplierDetails(new SupplierDetails());
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        ratePlanCB.getSupplierDetails().setCostPrice(19d);
        ratePlanCB.setCampaingText("Free Cancellation till 24 hrs");

        roomType.getRatePlanList().put("abc", ratePlanCB);


        hotelRates.getRecommendedRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOffers(new ArrayList<>());
        hotelRates.getOffers().add(new RangePrice());
        hotelRates.setRtbPreApproved(false);
        hotelRates.setRequestToBook(true);

        List<HotelRates> hotelRatesList = new ArrayList<HotelRates>();
        hotelRatesList.add(hotelRates);

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildHotelRates(hotelRatesList).build();


        List<HtlRmInfo> htlRmInfoList = new ArrayList<HtlRmInfo>();
        htlRmInfoList.add(new HtlRmInfo());
        htlRmInfoList.get(0).setHotelRoomInfo(new HashMap<String, RoomInfo>());
        SpaceData spaceData = new SpaceData();
        Space space = new Space();
        space.setDescriptionText("");
        space.setOpenCardText("test");
        space.setMedia(Arrays.asList(new MediaData()));
        spaceData.setSpaces(Arrays.asList(space));
        htlRmInfoList.get(0).setSharedSpaces(spaceData);

        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaster(true);
        roomInfo.setMaxAdultCount(5);
        roomInfo.setRoomSize("1");
        roomInfo.setRoomViewName("view");
        roomInfo.setBedType("king");
        roomInfo.setRoomSummary(new RoomSummary());
        roomInfo.getRoomSummary().setTopRated(true);

        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed1 = new SleepingArrangement();
        bed1.setCount(3);
        bed1.setType("");
        beds.add(bed1);
        SleepingArrangement bed2 = new SleepingArrangement();
        bed2.setCount(1);
        bed2.setType("");
        beds.add(bed2);
        roomInfo.setBeds(beds);

        htlRmInfoList.get(0).getHotelRoomInfo().put("abc", roomInfo);
        roomInfo.setFacilityWithGrp(new ArrayList<>());
        roomInfo.getFacilityWithGrp().add(new FacilityGroup());
        roomInfo.getFacilityWithGrp().get(0).setName("test");
        roomInfo.getFacilityWithGrp().get(0).setFacilities(new ArrayList<>());
        roomInfo.getFacilityWithGrp().get(0).getFacilities().add(new Facility());
        roomInfo.setRoomLevelVideos(new ArrayList<>());
        roomInfo.getRoomLevelVideos().add(new VideoInfo());
        roomInfo.getRoomLevelVideos().get(0).setTags(new ArrayList<>());
        roomInfo.getRoomLevelVideos().get(0).setUrl("url");

        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity.Builder().buildHtlRmInfo(htlRmInfoList).build();

        Mockito.when(commonResponseTransformer.buildBlackInfo(Mockito.any())).thenReturn(new BlackInfo());
        Mockito.when(commonResponseTransformer.getDoubleBlackInfo(Mockito.any())).thenReturn(new DoubleBlackInfo());

        HotelImage hotelImage = new HotelImage();
        hotelImage.setHotelId("test");
        hotelImage.setImageDetails(new ImageType());
        hotelImage.getImageDetails().setProfessional(new HashMap<String, List<ProfessionalImageEntity>>());
        hotelImage.getImageDetails().getProfessional().put("R", new ArrayList<>());
        hotelImage.getImageDetails().getProfessional().get("R").add(new ProfessionalImageEntity());
        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setCheckIn("2021-01-03");
        criteria.setCheckOut("2021-01-04");
        criteria.setLocationType("region");
        ReflectionTestUtils
                .setField(searchRoomsResponseTransformerPWA, "dateUtil" , dateUtil);
       List<Filter> filterlist = new ArrayList<>();
       filterlist.add(new Filter());
       filterlist.get(0).setFilterGroup(FilterGroup.MEAL_PLAN_AVAIL);
       filterlist.get(0).setFilterValue("TWO_MEAL_AVAIL");
        Map<String,String> expDataMap = new HashMap<>();
        expDataMap.put("plcnew","true");
        expDataMap.put("ratePlanRedesign","true");
        SearchRoomsResponse searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,filterlist,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        ratePlanCB.getMealPlans().get(0).setCode("AP");
        filterlist.get(0).setFilterValue("ALL_MEAL_AVAIL");
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,filterlist,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);

        hotelRates.setOtherRecommendedRooms(new ArrayList<>());
        hotelRates.getOtherRecommendedRooms().add(new RoomTypeDetails());
        hotelRates.getOtherRecommendedRooms().get(0).setRoomType(new HashMap<>());
        hotelRates.getOtherRecommendedRooms().get(0).getRoomType().put("def",roomType);
        hotelRates.getOtherRecommendedRooms().get(0).setTotalDisplayFare(new DisplayFare());
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        LowestRateAPResp lowestRateAPResp = new LowestRateAPResp();
        AvailDetails availDetails = new AvailDetails();
        availDetails.setOccupancyDetails(new OccupancyDetails());
        lowestRateAPResp.setAvailDetails(availDetails);
        hotelRates.setLowestRate(lowestRateAPResp);

        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").setSegmentId(Constants.MYPARTNER_SEGMENT_ID);
        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").getSupplierDetails().setSupplierCode("EPXX0034");

        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getPersuasions());


        hotelRates.setRoomTypeDetails(null); hotelRates.setPropertyType("Hostel");
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        hotelRates.setRecommendedRoomTypeDetails(null);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOccupencyLessRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getOccupencyLessRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getOccupencyLessRoomTypeDetails().getRoomType().put("Def",roomType);
        hotelRates.getOccupencyLessRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.setCategories(new HashSet<String>(){{add("luxury_hotels");}});
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        hotelRates.setRtbPreApprovedCard(new RTBPreApprovedCard());
        hotelRates.setRTBRatePlanPreApproved(true);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        hotelRates.setRTBRatePlanPreApproved(false);
        hotelRates.setRequestToBook(false);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);

        //for package rate plans
        setupForPackgeRatePlans(hotelRates);
        hotelRates.setOccupencyLessRoomTypeDetails(null);
        searchRoomsResponse = searchRoomsResponseTransformerPWA
                                  .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                                                              hotelImage, expDataMap, null, criteria, null,
                                                              new RequestDetails(), new CommonModifierResponse());
        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getPackageRooms());
        Assert.assertTrue(
            searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getFilterCode().contains("PACKAGE_RATE"));
        //if package in locked then LUXE PACKAGE filter should not be present
//        Assert.assertFalse(searchRoomsResponse.getFilters().contains("PACKAGE_RATE"));

        //setting first room and rate plan as package
        hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next().getValue().getRatePlanList()
                  .entrySet().iterator().next().getValue().setPackageRoomRatePlan(true);
        searchRoomsResponse = searchRoomsResponseTransformerPWA
                                  .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                                                              hotelImage, expDataMap, null, criteria, null,
                                                              new RequestDetails(), new CommonModifierResponse());
        //first filter has to be package
//        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        hotelRates.setCategories(new HashSet<String>(){{add("package_hotels");}});

        searchRoomsResponse = searchRoomsResponseTransformerPWA
                .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                        hotelImage, expDataMap, null, criteria, null,
                        new RequestDetails(), new CommonModifierResponse());

//        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        hotelRates.setDetailDeeplinkUrl("detaildeeplinkurl?city=City&checkAvailability=true");
        hotelRates.setName("hotelName");
        hotelRates.setHotelIcon("hotelIcon");

        searchRoomsResponse = searchRoomsResponseTransformerPWA
                .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                        hotelImage, expDataMap, null, criteria, null,
                        new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse.getDetailDeeplinkUrl());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails().getUrl());


    }

    private void setupForPackgeRatePlans(HotelRates hotelRates) {
        hotelRates.setPackageRoomDetails(new RoomTypeDetails());
        hotelRates.getPackageRoomDetails().setRoomType(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().put("packageRoom1", new PackageRoomType());
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setAnimationType("UNLOCKED");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setRecommendText("#MMT Recommends");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setCtaText("Select Package");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").setRatePlanList(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .put("packageRatePlan1", new PackageRoomRatePlan());
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckOutDate("2021-07-24");
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckInDate("2021-07-21");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setDisplayFare(new DisplayFare());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setPackageRoomRatePlan(true);
    }

    @Test
    public void buildBasicRecommendedComboTest(){
        List<RoomDetails> roomDetailList = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetailList.add(roomDetails);

        List<SelectRoomRatePlan> ratePlanList = new ArrayList<>();
        SelectRoomRatePlan roomRatePlan = new SelectRoomRatePlan();
        ratePlanList.add(roomRatePlan);
        roomDetails.setRatePlans(ratePlanList);


        BookedCancellationPolicy cancellationPolicy = new BookedCancellationPolicy();
        cancellationPolicy.setType(BookedCancellationPolicyType.FC);
        roomRatePlan.setCancellationPolicy(cancellationPolicy);

        List<Tariff> tariffList = new ArrayList<>();
        Tariff newTariff = new Tariff();
        newTariff.setCampaignAlert(new Alert());
        newTariff.getCampaignAlert().setText("Free Cancellation Summer Campaign");
        tariffList.add(newTariff);
        newTariff = new Tariff();
        tariffList.add(newTariff);

        roomRatePlan.setTariffs(tariffList);

        RecommendedCombo recommendedCombo = ReflectionTestUtils
            .invokeMethod(searchRoomsResponseTransformerPWA, "buildBasicRecommendedCombo", roomDetailList,
                          "recomendedCombo", false, true, "GROUP", null, null);
        Assert.assertNotNull(recommendedCombo);
        Assert.assertNotNull(recommendedCombo.getComboTariff());
        Assert.assertNotNull(recommendedCombo.getComboTariff().getCampaignAlert());
        Assert.assertEquals(recommendedCombo.getComboTariff().getCampaignAlert().getText(),"Free Cancellation Summer Campaign");

    }

    @Test
    public void reorderInclusionsTest() {
        List<BookedInclusion> inclusions = new ArrayList<>();
        inclusions.add(new BookedInclusion());
        List<BookedInclusion> result = ReflectionTestUtils
                .invokeMethod(utility, "reorderInclusions", inclusions);
        Assert.assertNotNull(result);
        inclusions.get(0).setCategory("ZPN");
        result = ReflectionTestUtils
                .invokeMethod(utility, "reorderInclusions", inclusions);
        Assert.assertNotNull(result);
    }

    @Test
    public void getTotalSearchedPaxCountTest() throws Exception {
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(1);
        List<Integer> childAges = Arrays.asList(5,7);
        roomStayCandidate.setChildAges(childAges);
        roomStayCandidates.add(roomStayCandidate);
        int totalSearchedPaxCount = searchRoomsResponseTransformerPWA.getTotalSearchedPaxCount(roomStayCandidates);
        Assert.assertEquals(totalSearchedPaxCount, 3);
    }

    @Test
    public void testRoomHighlightsGetter() throws IOException, ParseException {
        // Create test data
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("300 sq.ft");
        roomInfo.setRoomViewName("City View");
        ExtraGuestDetail extraGuestDetail = new ExtraGuestDetail();

        List<RoomHighlight> list = searchRoomsResponseTransformerPWA.getRoomHighlights(roomInfo,extraGuestDetail, true, true);
        Assert.assertNotNull(list);
    }

    @Test
    public void testConvertSearchSlotResponse() {
        MissingSlotDetail missingSlotDetail = new MissingSlotDetail();
        missingSlotDetail.setDuration(new HashSet<>(Arrays.asList(3, 6, 9)));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "missingSlotDetails", missingSlotDetail);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("+₹ 2.2 taxes & service fees");
        RoomDetailsResponse roomDetailsResponseFile = null;
        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntityFile = null;
        HotelImage hotelImageFile = null;
        DayUseRoomsRequest dayUseRoomsRequest = null;
        try {
            roomDetailsResponseFile = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/dayuse.json")),RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
            hotelsRoomInfoResponseEntityFile = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/HotelEntity.json")),HotelsRoomInfoResponseEntity.class, DependencyLayer.ORCHESTRATOR);
            hotelImageFile = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/HotelImage.json")),HotelImage.class, DependencyLayer.ORCHESTRATOR);
            dayUseRoomsRequest = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/dayUseRequest.json")),DayUseRoomsRequest.class, DependencyLayer.ORCHESTRATOR);

        } catch (IOException | JsonParseException e) {
            e.printStackTrace();
        }
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("10 AM - 1 PM");
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"mealplanFilterEnable",true);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);

        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "dateUtil" , dateUtil);
        DayUseRoomsResponse dayUseRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchSlotsResponse(roomDetailsResponseFile, hotelsRoomInfoResponseEntityFile, hotelImageFile, dayUseRoomsRequest, new CommonModifierResponse());

        Assert.assertNotNull(dayUseRoomsResponse);
        Assert.assertNotNull(dayUseRoomsResponse.getSlotPlans());
    }
    @Test
    public void testGetComboText(){
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Combo Text");
        String[] mealPlanCodes = {MEAL_PLAN_CODE_ACC_ONLY, MEAL_PLAN_CODE_BED_ONLY, MEAL_PLAN_CODE_ROOM_ONLY, MEAL_PLAN_CODE_BREAKFAST
                , MEAL_PLAN_CODE_BREAKFAST_LUNCH, MEAL_PLAN_CODE_BREAKFAST_DINNER, MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER, MEAL_PLAN_CODE_ALL_MEALS_AI};
        for(String mealPlan : mealPlanCodes){
            String res = searchRoomsResponseTransformerPWA.getComboText(mealPlan, 2);
            Assert.assertNotNull(res);
        }
    }

    @Test
    public void  sortBySellableTypeTest()
    {
        SearchRoomsResponse searchRoomsResponse;
        try {
            searchRoomsResponse  = new ObjectMapper().readValue(ResourceUtils.getFile("classpath:searchRoomsResponse.json"),SearchRoomsResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP,Constants.TRUE);
        searchRoomsResponseTransformerPWA.sortBySellableType(searchRoomsResponse,Constants.PROPERTY_TYPE_HOSTEL,expDataMap);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(0,searchRoomsResponse),5921,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(1,searchRoomsResponse),6736,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(2,searchRoomsResponse),7736,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(3,searchRoomsResponse),4140,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(4,searchRoomsResponse),12540,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(5,searchRoomsResponse),22300,0);

    }

    private double getResponseForSearchRooms(int index,SearchRoomsResponse searchRoomsResponse)
    {
        String defaultPriceKey = searchRoomsResponse.getOccupancyRooms().get(index).getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey();
        return searchRoomsResponse.getOccupancyRooms().get(index).getRatePlans().get(0).getTariffs().get(0).getPriceMap().get(defaultPriceKey).getDetails().stream().filter(e -> Constants.TOTAL_AMOUNT_KEY.equalsIgnoreCase(e.getKey())).findFirst().get().getAmount();
    }

    @Test
    public void  addSellableLabelFromSellableTypeTest()
    {
        SearchRoomsResponse searchRoomsResponse;
        try {
            searchRoomsResponse  = new ObjectMapper().readValue(ResourceUtils.getFile("classpath:searchRoomsResponse.json"),SearchRoomsResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP,Constants.TRUE);
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.BEDS_SELLABLE_LABEL)).thenReturn("DORMITORY BEDS");
        searchRoomsResponseTransformerPWA.addSellableLabelFromSellableType(searchRoomsResponse);
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,0),"DORMITORY BEDS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,1),"DORMITORY BEDS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,2),"DORMITORY BEDS");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_SELLABLE_LABEL)).thenReturn("PRIVATE ROOMS");
        searchRoomsResponseTransformerPWA.addSellableLabelFromSellableType(searchRoomsResponse);
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,3),"PRIVATE ROOMS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,4),"PRIVATE ROOMS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,5),"PRIVATE ROOMS");

    }

    private String getSellableLabel(SearchRoomsResponse searchRoomsResponse,int index)
    {
        return searchRoomsResponse.getOccupancyRooms().get(index).getSellableLabel();
    }
    @Test
    public void testSetBnplNewVariantDetails() {
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put(EXP_BNPL_NEW_VARIANT, "true");
        String bnplNewVariantText = "Book now @ just Rs. 1";
        BookedInclusion bookedInclusion = new BookedInclusion();
        Mockito.when(polyglotService.getTranslatedData(BNPL_NEW_VARIANT_TEXT_GI)).thenReturn(bnplNewVariantText);
        ReflectionTestUtils.invokeMethod(utility, "setInclusionCodeAndText", bookedInclusion, BNPLVariant.BNPL_AT_1, "IN");
        assertNotNull(bookedInclusion);
        assertEquals(bnplNewVariantText, bookedInclusion.getCode());
        assertEquals(bnplNewVariantText, bookedInclusion.getText());
    }
    @Test
    public void testSetBnplZeroVariantDetails() {
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put(EXP_BNPL_NEW_VARIANT, "false");
        String bnplNewVariantText = "Book with 0 Payment";
        BookedInclusion bookedInclusion = new BookedInclusion();
        Mockito.when(polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT_GI_AVAILABLE)).thenReturn(bnplNewVariantText);
        ReflectionTestUtils.invokeMethod(utility, "setInclusionCodeAndText", bookedInclusion, BNPLVariant.BNPL_AT_0, "IN");
        assertNotNull(bookedInclusion);
        assertEquals(bnplNewVariantText, bookedInclusion.getCode());
        assertEquals(bnplNewVariantText, bookedInclusion.getText());
    }

    @Test
    public void testSetBnplOldFlow() {
        Map<String, String> experimentDataMap = new HashMap<>();
        String bnplNewVariantText = "Credit card is charged";
        BookedInclusion bookedInclusion = new BookedInclusion();
        Mockito.when(polyglotService.getTranslatedData(ZERO_PAYMENT_NOW_WITH_CC)).thenReturn(bnplNewVariantText);
        ReflectionTestUtils.invokeMethod(utility, "setInclusionCodeAndText", bookedInclusion, null, "IN");
        assertNotNull(bookedInclusion);
        assertEquals(bnplNewVariantText, bookedInclusion.getCode());
        assertEquals(bnplNewVariantText, bookedInclusion.getText());
    }

    @Test
    public void buildRatePlanPersuasionsMapTest() {

        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        RatePlan ratePlanHes = new RatePlan();
        ratePlanHes.setMpFareHoldStatus(new MpFareHoldStatus());
        ratePlanHes.getMpFareHoldStatus().setHoldEligible(true);

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);

        Map<String, PersuasionResponse> hotelPersuasions = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildRatePlanPersuasionsMap", ratePlanHes,commonModifierResponse);
        org.junit.Assert.assertNotNull(hotelPersuasions.get(Constants.BOOK_NOW_PERSUASION_KEY));
    }

}
