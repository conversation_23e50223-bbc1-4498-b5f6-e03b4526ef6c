package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for OrchSearchRoomsResponseTransformerDesktop
 * 
 * This test class covers:
 * - Desktop-specific PersuasionObject creation with detailed styling
 * - Region-based LoginPersuasion logic (AE vs other regions)
 * - Complex loyalty/cashback persuasion building
 * - Group filter behavior (returns null for desktop)
 * - MDC context handling for region detection
 * - PolyglotService integration for translations
 * - Edge cases and null handling
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchRoomsResponseTransformerDesktopTest {

    // ==================== Mock Dependencies ====================

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private PropertyManager propManager;

    @InjectMocks
    private OrchSearchRoomsResponseTransformerDesktop transformer;

    // ==================== Test Setup ====================

    @Before
    public void setUp() {
        // Set up basic configuration for the transformer
        ReflectionTestUtils.setField(transformer, "groupFilterMap", "{\"test_filter\":{\"code\":\"TEST\"}}");
        
        // Mock PolyglotService common translations
        when(polyglotService.getTranslatedData(ConstantsTranslation.TOP_RATED)).thenReturn("Top Rated");
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_TEXT)).thenReturn("Login to see member prices");
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT)).thenReturn("Sign in for exclusive deals");
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_TEXT_GCC)).thenReturn("تسجيل الدخول لرؤية أسعار الأعضاء");
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT_GCC)).thenReturn("سجل الدخول للحصول على عروض حصرية");
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT)).thenReturn("Loyalty Offer: %s");
        when(polyglotService.getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT)).thenReturn("Get ₹%d cashback");
        
        // Mock PropertyManager to return a basic configuration  
//        doReturn(createMockCommonConfig()).when(propManager).getProperty(anyString(), any());
    }

    @After
    public void tearDown() {
        // Clean up MDC after each test
        MDC.clear();
    }

    // ==================== createTopRatedPersuasion Tests ====================

    @Test
    public void should_CreateDetailedPersuasionObject_When_CreatingTopRatedPersuasion() {
        // When
        PersuasionObject result = transformer.createTopRatedPersuasion();

        // Then
        assertNotNull("Should return non-null PersuasionObject", result);
        assertEquals("Should have correct placeholder", Constants.PC_SELECT_RIGHT_1, result.getPlaceholder());
        assertEquals("Should have correct template", "IMAGE_TEXT_H", result.getTemplate());
        assertNotNull("Should have data list", result.getData());
        assertFalse("Data list should not be empty", result.getData().isEmpty());
        
        // Verify persuasion data structure
        PersuasionData persuasionData = (PersuasionData) result.getData().get(0);
        assertNotNull("Should have persuasion data", persuasionData);
        assertEquals("Should have correct persuasion type", "PEITHO", persuasionData.getPersuasionType());
        assertEquals("Should have translated text", "Top Rated", persuasionData.getText());
        
        // Verify style
        PersuasionStyle style = persuasionData.getStyle();
        assertNotNull("Should have style", style);
        assertNotNull("Should have style classes", style.getStyleClasses());
        assertTrue("Should contain rmType__toprated class", style.getStyleClasses().contains("rmType__toprated"));
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.TOP_RATED);
    }

    @Test
    public void should_CreateConsistentPersuasionObject_When_CalledMultipleTimes() {
        // When
        PersuasionObject result1 = transformer.createTopRatedPersuasion();
        PersuasionObject result2 = transformer.createTopRatedPersuasion();

        // Then
        assertEquals("Should have same placeholder", result1.getPlaceholder(), result2.getPlaceholder());
        assertEquals("Should have same template", result1.getTemplate(), result2.getTemplate());
        assertEquals("Should have same data size", result1.getData().size(), result2.getData().size());
        
        // Verify polyglot service called multiple times
        verify(polyglotService, times(2)).getTranslatedData(ConstantsTranslation.TOP_RATED);
    }

    // ==================== buildLoginPersuasion Tests ====================

    @Test
    public void should_UseGCCTranslations_When_RegionIsAE() {
        // Given
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

        // When
        LoginPersuasion result = transformer.buildLoginPersuasion();

        // Then
        assertNotNull("Should return non-null LoginPersuasion", result);
        assertEquals("Should use GCC login text", "تسجيل الدخول لرؤية أسعار الأعضاء", result.getLoginPersuasionText());
        assertEquals("Should use GCC subtext", "سجل الدخول للحصول على عروض حصرية", result.getLoginPersuasionSubText());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_TEXT_GCC);
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT_GCC);
    }

    @Test
    public void should_UseRegularTranslations_When_RegionIsNotAE() {
        // Given
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");

        // When
        LoginPersuasion result = transformer.buildLoginPersuasion();

        // Then
        assertNotNull("Should return non-null LoginPersuasion", result);
        assertEquals("Should use regular login text", "Login to see member prices", result.getLoginPersuasionText());
        assertEquals("Should use regular subtext", "Sign in for exclusive deals", result.getLoginPersuasionSubText());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_TEXT);
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT);
    }

    @Test
    public void should_UseRegularTranslations_When_RegionIsNull() {
        // Given
        MDC.remove(MDCHelper.MDCKeys.REGION.getStringValue());

        // When
        LoginPersuasion result = transformer.buildLoginPersuasion();

        // Then
        assertNotNull("Should return non-null LoginPersuasion", result);
        assertEquals("Should use regular login text when region is null", "Login to see member prices", result.getLoginPersuasionText());
        assertEquals("Should use regular subtext when region is null", "Sign in for exclusive deals", result.getLoginPersuasionSubText());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_TEXT);
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT);
    }

    @Test
    public void should_UseRegularTranslations_When_RegionIsUS() {
        // Given
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "US");

        // When
        LoginPersuasion result = transformer.buildLoginPersuasion();

        // Then
        assertNotNull("Should return non-null LoginPersuasion", result);
        assertEquals("Should use regular login text for US", "Login to see member prices", result.getLoginPersuasionText());
        assertEquals("Should use regular subtext for US", "Sign in for exclusive deals", result.getLoginPersuasionSubText());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_TEXT);
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT);
    }

    @Test
    public void should_BeCaseInsensitive_When_CheckingAERegion() {
        // Given
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "ae"); // lowercase

        // When
        LoginPersuasion result = transformer.buildLoginPersuasion();

        // Then
        assertNotNull("Should return non-null LoginPersuasion", result);
        assertEquals("Should use GCC login text for lowercase 'ae'", "تسجيل الدخول لرؤية أسعار الأعضاء", result.getLoginPersuasionText());
        assertEquals("Should use GCC subtext for lowercase 'ae'", "سجل الدخول للحصول على عروض حصرية", result.getLoginPersuasionSubText());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_TEXT_GCC);
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT_GCC);
    }

    // ==================== buildLoyaltyCashbackPersuasions Tests ====================

    @Test
    public void should_UseLoyaltyOfferMessage_When_LoyaltyOfferMessageIsPresent() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Special Hero Offer 20% off");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);

        // Then
        assertFalse("Should add persuasion to map", persuasionMap.isEmpty());
        assertTrue("Should contain cashback/hero offer persuasion", 
                   persuasionMap.containsKey(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE));
        
        PersuasionResponse persuasion = persuasionMap.get(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertNotNull("Persuasion should not be null", persuasion);
        assertEquals("Should use loyalty offer text format", "Loyalty Offer: Special Hero Offer 20% off", persuasion.getPersuasionText());
        assertTrue("Should be HTML", persuasion.isHtml());
        assertEquals("Should use hero offer icon type", Constants.HERO_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT);
    }

    @Test
    public void should_UseCashbackOffer_When_LoyaltyOfferMessageIsBlank() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage(""); // Blank message
        
        Map<String, Double> hybridDiscounts = new HashMap<>();
        hybridDiscounts.put("CTW", 150.75); // Will be rounded to 151
        coupon.setHybridDiscounts(hybridDiscounts);
        
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);

        // Then
        assertFalse("Should add persuasion to map", persuasionMap.isEmpty());
        assertTrue("Should contain cashback/hero offer persuasion", 
                   persuasionMap.containsKey(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE));
        
        PersuasionResponse persuasion = persuasionMap.get(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertNotNull("Persuasion should not be null", persuasion);
        assertEquals("Should use cashback text with rounded amount", "Get ₹151 cashback", persuasion.getPersuasionText());
        assertTrue("Should be HTML", persuasion.isHtml());
        assertEquals("Should use cashback offer icon type", Constants.CASHBACK_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT);
    }

    @Test
    public void should_UseCashbackOffer_When_LoyaltyOfferMessageIsNull() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage(null); // Null message
        
        Map<String, Double> hybridDiscounts = new HashMap<>();
        hybridDiscounts.put("CTW", 99.4); // Will be rounded to 99
        coupon.setHybridDiscounts(hybridDiscounts);
        
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);

        // Then
        PersuasionResponse persuasion = persuasionMap.get(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertNotNull("Persuasion should not be null", persuasion);
        assertEquals("Should use cashback text with rounded amount", "Get ₹99 cashback", persuasion.getPersuasionText());
        assertEquals("Should use cashback offer icon type", Constants.CASHBACK_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT);
    }

    @Test
    public void should_RoundCashbackAmountCorrectly_When_BuildingCashbackPersuasion() {
        // Test various rounding scenarios
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        
        // Test case 1: Round down (0.4)
        BestCoupon coupon1 = createCouponWithCashback(100.4);
        transformer.buildLoyaltyCashbackPersuasions(coupon1, persuasionMap);
        PersuasionResponse persuasion1 = persuasionMap.get(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should round down 100.4 to 100", "Get ₹100 cashback", persuasion1.getPersuasionText());
        
        // Test case 2: Round up (0.6)
        persuasionMap.clear();
        BestCoupon coupon2 = createCouponWithCashback(100.6);
        transformer.buildLoyaltyCashbackPersuasions(coupon2, persuasionMap);
        PersuasionResponse persuasion2 = persuasionMap.get(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should round up 100.6 to 101", "Get ₹101 cashback", persuasion2.getPersuasionText());
        
        // Test case 3: Exact round (0.5)
        persuasionMap.clear();
        BestCoupon coupon3 = createCouponWithCashback(100.5);
        transformer.buildLoyaltyCashbackPersuasions(coupon3, persuasionMap);
        PersuasionResponse persuasion3 = persuasionMap.get(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should round 100.5 to 101", "Get ₹101 cashback", persuasion3.getPersuasionText());
    }

    @Test
    public void should_HandleEmptyPersuasionMap_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Test offer");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);

        // Then
        assertEquals("Should add exactly one persuasion", 1, persuasionMap.size());
        assertTrue("Should contain the correct key", 
                   persuasionMap.containsKey(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE));
    }

    @Test
    public void should_OverwriteExistingPersuasion_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("New offer");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        
        // Add existing persuasion
        PersuasionResponse existingPersuasion = new PersuasionResponse();
        persuasionMap.put(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE, existingPersuasion);

        // When
        transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);

        // Then
        assertEquals("Should still have exactly one persuasion", 1, persuasionMap.size());
        PersuasionResponse newPersuasion = persuasionMap.get(Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertNotSame("Should be a different object", existingPersuasion, newPersuasion);
        assertEquals("Should have new persuasion text", "Loyalty Offer: New offer", newPersuasion.getPersuasionText());
    }

    // ==================== buildGroupFilterForDevice Tests ====================

    @Test
    public void should_ReturnNull_When_BuildingGroupFilterForDevice() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = new HashMap<>();
        List<Filter> filterCriteria = new ArrayList<>();
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = transformer.buildGroupFilterForDevice(
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNull("Desktop should return null for group filter", result);
    }

    @Test
    public void should_ReturnNull_When_BuildingGroupFilterForDeviceWithNullInputs() {
        // Test with null inputs - should still return null without throwing exception
        GroupRatePlanFilter result1 = transformer.buildGroupFilterForDevice(null, null, true);
        GroupRatePlanFilter result2 = transformer.buildGroupFilterForDevice(new HashMap<>(), null, false);
        GroupRatePlanFilter result3 = transformer.buildGroupFilterForDevice(null, new ArrayList<>(), true);

        assertNull("Should return null with null groupRatePlanFilterConfMap", result1);
        assertNull("Should return null with null filterCriteria", result2);
        assertNull("Should return null with null groupRatePlanFilterConfMap and valid filterCriteria", result3);
    }

    // ==================== Edge Cases and Error Handling ====================

    @Test
    public void should_HandleNullCoupon_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        BestCoupon coupon = null;
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        try {
            transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
            fail("Should throw exception for null coupon");
        } catch (Exception e) {
            // Expected behavior - method should handle null gracefully or throw appropriate exception
            assertTrue("Should handle null coupon appropriately", true);
        }
    }

    @Test
    public void should_HandleNullPersuasionMap_When_BuildingLoyaltyCashbackPersuasions() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Test");
        Map<String, PersuasionResponse> persuasionMap = null;

        // When
        try {
            transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
            fail("Should throw exception for null persuasion map");
        } catch (Exception e) {
            // Expected behavior - method should handle null gracefully or throw appropriate exception
            assertTrue("Should handle null persuasion map appropriately", true);
        }
    }

    @Test
    public void should_HandleNullHybridDiscounts_When_BuildingCashbackPersuasion() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage(""); // Blank to trigger cashback logic
        coupon.setHybridDiscounts(null); // Null hybrid discounts
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        try {
            transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
            fail("Should throw exception for null hybrid discounts");
        } catch (Exception e) {
            // Expected behavior - method should handle null gracefully or throw appropriate exception
            assertTrue("Should handle null hybrid discounts appropriately", true);
        }
    }

    @Test
    public void should_HandleMissingCTWKey_When_BuildingCashbackPersuasion() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage(""); // Blank to trigger cashback logic
        
        Map<String, Double> hybridDiscounts = new HashMap<>();
        // Not adding "CTW" key
        hybridDiscounts.put("OTHER_KEY", 100.0);
        coupon.setHybridDiscounts(hybridDiscounts);
        
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        try {
            transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
            fail("Should throw exception for missing CTW key");
        } catch (Exception e) {
            // Expected behavior - method should handle missing key gracefully or throw appropriate exception
            assertTrue("Should handle missing CTW key appropriately", true);
        }
    }

    // ==================== Integration Tests ====================

    @Test
    public void should_UsePolyglotService_When_CreatingPersuasions() {
        // Test that all methods properly integrate with PolyglotService
        
        // Test createTopRatedPersuasion
        transformer.createTopRatedPersuasion();
        verify(polyglotService, atLeastOnce()).getTranslatedData(ConstantsTranslation.TOP_RATED);
        
        // Test buildLoginPersuasion
        transformer.buildLoginPersuasion();
        verify(polyglotService, atLeastOnce()).getTranslatedData(anyString());
        
        // Test buildLoyaltyCashbackPersuasions
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Test offer");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
        verify(polyglotService, atLeastOnce()).getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT);
    }

    @Test
    public void should_HandleMDCContext_When_BuildingLoginPersuasion() {
        // Test MDC context handling across multiple calls
        
        // First call with AE region
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        LoginPersuasion aeResult = transformer.buildLoginPersuasion();
        
        // Second call with different region
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "US");
        LoginPersuasion usResult = transformer.buildLoginPersuasion();
        
        // Verify different results based on region
        assertNotEquals("Should return different text for different regions", 
                       aeResult.getLoginPersuasionText(), usResult.getLoginPersuasionText());
    }

    // ==================== Helper Methods ====================

    private BestCoupon createCouponWithCashback(double amount) {
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage(""); // Blank to trigger cashback logic
        
        Map<String, Double> hybridDiscounts = new HashMap<>();
        hybridDiscounts.put("CTW", amount);
        coupon.setHybridDiscounts(hybridDiscounts);
        
        return coupon;
    }

    private Object createMockCommonConfig() {
        // Return a mock CommonConfig that satisfies the PropertyManager.getProperty call
        return new Object() {
            public int thresholdForSlashedAndDefaultHourPrice() { return 0; }
            public Map<String, String> mealPlanMapPolyglot() { return new HashMap<>(); }
            public int ratePlanMoreOptionsLimit() { return 1; }
            public Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic() { return new HashMap<>(); }
            public int apLimitForInclusionIcons() { return 2; }
            public boolean mealplanFilterEnable() { return false; }
            public boolean partnerExclusiveFilterEnable() { return false; }
            public Map<String, String> rtbCardConfigs() { return new HashMap<>(); }
            public String mandatoryChargesAlert() { return ""; }
            public Object allInclusiveCard() { return new Object(); }
            public Map<String, Map<String, List<String>>> supplierToRateSegmentMapping() { return new HashMap<>(); }
            public Object missingSlotDetails() { return new Object(); }
            public Map<String, Object> dayUseFunnelPersuasions() { return new HashMap<>(); }
            public void addPropertyChangeListener(String property, Object listener) { }
        };
    }
} 