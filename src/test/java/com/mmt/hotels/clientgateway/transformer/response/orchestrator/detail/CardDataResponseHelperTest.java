package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail;

import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData;
import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.PersonalizationCards;
import com.gommt.hotels.orchestrator.detail.enums.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.CardDataResponseHelper;
import com.mmt.hotels.model.enums.ToggleAction;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class CardDataResponseHelperTest {

    @InjectMocks
    private CardDataResponseHelper cardDataResponseHelper;

    @Before
    public void setup() {
        // No specific setup needed for this mapper
    }

    // ==================== MAIN METHOD TESTS ====================

    @Test
    public void testBuildListPersonalizationResponse_withNullInput_returnsNull() {
        ListPersonalizationResponse result = cardDataResponseHelper.buildListPersonalizationResponse(null);
        
        Assert.assertNull("Should return null for null input", result);
    }

    @Test
    public void testBuildListPersonalizationResponse_withEmptyCardData_returnsNull() {
        PersonalizationCards personalizationCards = new PersonalizationCards();
        personalizationCards.setCardData(new ArrayList<>());
        
        ListPersonalizationResponse result = cardDataResponseHelper.buildListPersonalizationResponse(personalizationCards);
        
        Assert.assertNull("Should return null for empty card data", result);
    }

    @Test
    public void testBuildListPersonalizationResponse_withNullCardDataList_returnsNull() {
        PersonalizationCards personalizationCards = new PersonalizationCards();
        personalizationCards.setCardData(null);
        
        ListPersonalizationResponse result = cardDataResponseHelper.buildListPersonalizationResponse(personalizationCards);
        
        Assert.assertNull("Should return null for null card data list", result);
    }

    @Test
    public void testBuildListPersonalizationResponse_withValidSingleCard_mapsCorrectly() {
        PersonalizationCards personalizationCards = createPersonalizationCardsWithSingleCard();
        
        ListPersonalizationResponse result = cardDataResponseHelper.buildListPersonalizationResponse(personalizationCards);
        
        Assert.assertNotNull("Should return valid response", result);
        Assert.assertEquals("Should map experiment ID", Integer.valueOf(123), result.getExperimentId());
        Assert.assertEquals("Should map track text", "TRACK_TEXT", result.getTrackText());
        Assert.assertNotNull("Should have card data", result.getCardData());
        Assert.assertEquals("Should have one card", 1, result.getCardData().size());
        Assert.assertEquals("Should set sequence", 1, (int) result.getCardData().get(0).getSequence());
        Assert.assertNotNull("Should have meta", result.getMeta());
        Assert.assertNotNull("Should have saved card tracking", result.getMeta().getSavedCardTracking());
    }

    @Test
    public void testBuildListPersonalizationResponse_withMultipleCards_mapsAllCards() {
        PersonalizationCards personalizationCards = createPersonalizationCardsWithMultipleCards();
        
        ListPersonalizationResponse result = cardDataResponseHelper.buildListPersonalizationResponse(personalizationCards);
        
        Assert.assertNotNull("Should return valid response", result);
        Assert.assertNotNull("Should have card data", result.getCardData());
        Assert.assertEquals("Should have three cards", 3, result.getCardData().size());
        
        // Verify sequence assignment
        Assert.assertEquals("First card should have sequence 1", 1, (int) result.getCardData().get(0).getSequence());
        Assert.assertEquals("Second card should have sequence 2", 2, (int) result.getCardData().get(1).getSequence());
        Assert.assertEquals("Third card should have sequence 3", 3, (int) result.getCardData().get(2).getSequence());
    }

    @Test
    public void testBuildListPersonalizationResponse_withMixedValidInvalidCards_filtersCorrectly() {
        PersonalizationCards personalizationCards = createPersonalizationCardsWithMixedCards();
        
        ListPersonalizationResponse result = cardDataResponseHelper.buildListPersonalizationResponse(personalizationCards);
        
        Assert.assertNotNull("Should return valid response", result);
        Assert.assertNotNull("Should have card data", result.getCardData());
        // Should only include valid cards (nulls are filtered out in mapCardDataFromOrchestrator)
        Assert.assertTrue("Should have filtered valid cards", result.getCardData().size() >= 1);
    }

    @Test
    public void testBuildListPersonalizationResponse_withComplexNestedData_mapsAllFields() {
        PersonalizationCards personalizationCards = createPersonalizationCardsWithComplexData();
        
        ListPersonalizationResponse result = cardDataResponseHelper.buildListPersonalizationResponse(personalizationCards);
        
        Assert.assertNotNull("Should return valid response", result);
        Assert.assertNotNull("Should have card data", result.getCardData());
        Assert.assertNotNull("Should have card info", result.getCardData().get(0).getCardInfo());
        
        // Verify complex nested mappings are present
        CardInfo cardInfo = result.getCardData().get(0).getCardInfo();
        Assert.assertNotNull("Should have card payload", cardInfo.getCardPayload());
        Assert.assertNotNull("Should have card actions", cardInfo.getCardAction());
    }

    // ==================== PRIVATE METHOD TESTS (via Reflection) ====================

    @Test
    public void testMapCardDataFromOrchestrator_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardDataFromOrchestrator", CardData.class, int.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, null, 1);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardDataFromOrchestrator_withValidSource_mapsSequenceAndCardInfo() throws Exception {
        CardData sourceCard = createBasicCardData();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardDataFromOrchestrator", CardData.class, int.class);
        method.setAccessible(true);
        
        com.mmt.hotels.clientgateway.response.moblanding.CardData result = 
            (com.mmt.hotels.clientgateway.response.moblanding.CardData) method.invoke(cardDataResponseHelper, sourceCard, 5);
        
        Assert.assertNotNull("Should return valid card data", result);
        Assert.assertEquals("Should set sequence", 5, (int) result.getSequence());
        Assert.assertNotNull("Should have card info", result.getCardInfo());
    }

    @Test
    public void testMapCardInfoFromOrchestrator_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardInfoFromOrchestrator", CardData.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardInfoFromOrchestrator_withBasicFields_mapsCorrectly() throws Exception {
        CardData sourceCard = createBasicCardData();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardInfoFromOrchestrator", CardData.class);
        method.setAccessible(true);
        
        CardInfo result = (CardInfo) method.invoke(cardDataResponseHelper, sourceCard);
        
        Assert.assertNotNull("Should return valid card info", result);
        Assert.assertEquals("Should map card ID", "CARD123", result.getId());
        Assert.assertEquals("Should map sub type", "PROMO", result.getSubType());
        Assert.assertEquals("Should map title text", "Test Card", result.getTitleText());
        Assert.assertEquals("Should map template ID", "TEMPLATE123", result.getTemplateId());
        Assert.assertEquals("Should map template type", "BASIC", result.getTemplateType());
        Assert.assertEquals("Should map page context", Arrays.asList("DETAIL"), result.getPageContext());
        Assert.assertEquals("Should map icon URL", "https://test.com/icon.png", result.getIconURL());
        Assert.assertTrue("Should map has action", result.getHasAction());
        Assert.assertTrue("Should map claimed", result.isClaimed());
        Assert.assertEquals("Should map sub text", "Test Sub Text", result.getSubText());
        Assert.assertEquals("Should map action text", "Click Here", result.getActionText());
        Assert.assertEquals("Should map heading", "Test Heading", result.getHeading());
        Assert.assertEquals("Should map description", "Test Description", result.getDescription());
    }

    @Test
    public void testMapCardInfoFromOrchestrator_withMinItemsToShow_handlesZeroValue() throws Exception {
        CardData sourceCard = createBasicCardData();
        sourceCard.setMinItemsToShow(0); // Zero value should map to null
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardInfoFromOrchestrator", CardData.class);
        method.setAccessible(true);
        
        CardInfo result = (CardInfo) method.invoke(cardDataResponseHelper, sourceCard);
        
        Assert.assertNotNull("Should return valid card info", result);
        Assert.assertNull("Should map zero minItemsToShow to null", result.getMinItemsToShow());
        
        // Test with positive value
        sourceCard.setMinItemsToShow(5);
        result = (CardInfo) method.invoke(cardDataResponseHelper, sourceCard);
        Assert.assertEquals("Should map positive minItemsToShow", (Integer) 5, result.getMinItemsToShow());
    }

    @Test
    public void testMapCardInfoFromOrchestrator_withExtraDataFields_storesInExtraData() throws Exception {
        CardData sourceCard = createCardDataWithExtraFields();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardInfoFromOrchestrator", CardData.class);
        method.setAccessible(true);
        
        CardInfo result = (CardInfo) method.invoke(cardDataResponseHelper, sourceCard);
        
        Assert.assertNotNull("Should return valid card info", result);
        Assert.assertNotNull("Should have extra data", result.getExtraData());
        Assert.assertEquals("Should store removeCard in extra data", "true", result.getExtraData().get("removeCard"));
        Assert.assertEquals("Should store hasToggle in extra data", "false", result.getExtraData().get("hasToggle"));
        Assert.assertEquals("Should store hasLocation in extra data", "true", result.getExtraData().get("hasLocation"));
        Assert.assertEquals("Should store cardPosition in extra data", "2", result.getExtraData().get("cardPosition"));
    }

    @Test
    public void testMapCardInfoFromOrchestrator_withAllComplexObjects_mapsAll() throws Exception {
        CardData sourceCard = createCardDataWithAllComplexObjects();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardInfoFromOrchestrator", CardData.class);
        method.setAccessible(true);
        
        CardInfo result = (CardInfo) method.invoke(cardDataResponseHelper, sourceCard);
        
        Assert.assertNotNull("Should return valid card info", result);
        Assert.assertNotNull("Should map bg linear gradient", result.getBgLinearGradient());
        Assert.assertNotNull("Should map border gradient", result.getBorderGradient());
        Assert.assertNotNull("Should map icon tags", result.getIconTags());
        Assert.assertNotNull("Should map toggle action", result.getToggleAction());
        Assert.assertNotNull("Should map card sheet", result.getCardSheet());
        Assert.assertNotNull("Should map floating sheet data", result.getFloatingSheetData());
        Assert.assertNotNull("Should map rush deal timer info", result.getRushDealTimerInfo());
        Assert.assertNotNull("Should map card condition", result.getCardCondition());
    }

    // ==================== HELPER MAPPING METHOD TESTS ====================

    @Test
    public void testMapBGLinearGradient_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapBGLinearGradient", CardData.BGLinearGradient.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.BGLinearGradient) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapBGLinearGradient_withValidSource_mapsAllFields() throws Exception {
        CardData.BGLinearGradient source = createBGLinearGradient();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapBGLinearGradient", CardData.BGLinearGradient.class);
        method.setAccessible(true);
        
        com.mmt.hotels.pojo.listing.personalization.BGLinearGradient result = 
            (com.mmt.hotels.pojo.listing.personalization.BGLinearGradient) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid gradient", result);
        Assert.assertEquals("Should map start color", "#FF0000", result.getStart());
        Assert.assertEquals("Should map end color", "#00FF00", result.getEnd());
        Assert.assertEquals("Should map direction", "horizontal", result.getDirection());
        Assert.assertEquals("Should map angle", "horizontal", result.getAngle());
    }

    @Test
    public void testMapBorderGradient_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapBorderGradient", CardData.BorderGradient.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.BorderGradient) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapBorderGradient_withValidSource_mapsColors() throws Exception {
        CardData.BorderGradient source = createBorderGradient();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapBorderGradient", CardData.BorderGradient.class);
        method.setAccessible(true);
        
        com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient result = 
            (com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid border gradient", result);
        Assert.assertEquals("Should map start color", "#000000", result.getStart());
        Assert.assertEquals("Should map end color", "#FFFFFF", result.getEnd());
        Assert.assertEquals("Should map direction", "vertical", result.getDirection());
    }

    @Test
    public void testMapBorderGradient_withColorsList_mapsColorsList() throws Exception {
        CardData.BorderGradient source = createBorderGradientWithColorsList();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapBorderGradient", CardData.BorderGradient.class);
        method.setAccessible(true);
        
        com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient result = 
            (com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid border gradient", result);
        Assert.assertNotNull("Should map colors list", result.getColor());
        Assert.assertEquals("Should have correct number of colors", 3, result.getColor().size());
    }

    @Test
    public void testMapIconTag_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapIconTag", CardData.IconTag.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.IconTag) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapIconTag_withValidSource_mapsTextAndColors() throws Exception {
        CardData.IconTag source = createIconTag();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapIconTag", CardData.IconTag.class);
        method.setAccessible(true);
        
        com.mmt.hotels.pojo.listing.personalization.IconTag result = 
            (com.mmt.hotels.pojo.listing.personalization.IconTag) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid icon tag", result);
        Assert.assertEquals("Should map text", "NEW", result.getText());
        Assert.assertEquals("Should map border color from text color", "#BLUE", result.getBorderColor());
    }

    @Test
    public void testMapIconTag_withBackgroundColor_createsBgGradient() throws Exception {
        CardData.IconTag source = createIconTagWithBackgroundColor();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapIconTag", CardData.IconTag.class);
        method.setAccessible(true);
        
        com.mmt.hotels.pojo.listing.personalization.IconTag result = 
            (com.mmt.hotels.pojo.listing.personalization.IconTag) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid icon tag", result);
        Assert.assertNotNull("Should create bg gradient", result.getBgGradient());
        Assert.assertEquals("Should set start color", "#RED", result.getBgGradient().getStart());
        Assert.assertEquals("Should set end color", "#RED", result.getBgGradient().getEnd());
    }

    @Test
    public void testMapToggleAction_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapToggleAction", CardData.ToggleAction.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.ToggleAction) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapToggleAction_withON_returnsON() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapToggleAction", CardData.ToggleAction.class);
        method.setAccessible(true);
        
        ToggleAction result = (ToggleAction) method.invoke(cardDataResponseHelper, CardData.ToggleAction.ON);
        
        Assert.assertNotNull("Should return valid toggle action", result);
        Assert.assertEquals("Should map ON to ON", ToggleAction.ON, result);
    }

    @Test
    public void testMapToggleAction_withOFF_returnsOFF() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapToggleAction", CardData.ToggleAction.class);
        method.setAccessible(true);
        
        ToggleAction result = (ToggleAction) method.invoke(cardDataResponseHelper, CardData.ToggleAction.OFF);
        
        Assert.assertNotNull("Should return valid toggle action", result);
        Assert.assertEquals("Should map OFF to OFF", ToggleAction.OFF, result);
    }

    @Test
    public void testMapCardActionData_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardActionData", CardData.CardActionData.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.CardActionData) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardActionData_withValidSource_mapsTitle() throws Exception {
        CardData.CardActionData source = createCardActionData();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardActionData", CardData.CardActionData.class);
        method.setAccessible(true);
        
        CardActionData result = (CardActionData) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid card action data", result);
        Assert.assertEquals("Should map title", "Action Title", result.getTitle());
    }

    @Test
    public void testMapCardActionData_withSections_mapsSectionsAndItems() throws Exception {
        CardData.CardActionData source = createCardActionDataWithSections();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardActionData", CardData.CardActionData.class);
        method.setAccessible(true);
        
        CardActionData result = (CardActionData) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid card action data", result);
        Assert.assertNotNull("Should map sections", result.getSections());
        Assert.assertEquals("Should have correct number of sections", 2, result.getSections().size());
        
        Section firstSection = result.getSections().get(0);
        Assert.assertEquals("Should map section title", "Section 1", firstSection.getTitle());
        Assert.assertNotNull("Should map items", firstSection.getItems());
        Assert.assertEquals("Should have correct number of items", 2, firstSection.getItems().size());
        Assert.assertEquals("Should map item text", "Item 1", firstSection.getItems().get(0).getText());
        Assert.assertEquals("Should map item text box title", "Box 1", firstSection.getItems().get(0).getTextBoxTitle());
    }

    @Test
    public void testMapCardActionList_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardActionList", List.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (List<CardData.CardAction>) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardActionList_withEmptyList_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardActionList", List.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, new ArrayList<CardData.CardAction>());
        
        Assert.assertNull("Should return null for empty list", result);
    }

    @Test
    public void testMapCardActionList_withValidList_mapsAllActions() throws Exception {
        List<CardData.CardAction> sourceList = createCardActionList();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardActionList", List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        List<CardAction> result = (List<CardAction>) method.invoke(cardDataResponseHelper, sourceList);
        
        Assert.assertNotNull("Should return valid action list", result);
        Assert.assertEquals("Should have correct number of actions", 2, result.size());
        Assert.assertEquals("Should map first action title", "Action 1", result.get(0).getTitle());
        Assert.assertEquals("Should map second action title", "Action 2", result.get(1).getTitle());
    }

    @Test
    public void testMapCardAction_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardAction", CardData.CardAction.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.CardAction) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardAction_withBasicFields_mapsAllBasicFields() throws Exception {
        CardData.CardAction source = createBasicCardAction();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardAction", CardData.CardAction.class);
        method.setAccessible(true);
        
        CardAction result = (CardAction) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid card action", result);
        Assert.assertEquals("Should map title", "Basic Action", result.getTitle());
        Assert.assertEquals("Should map web view URL", "https://test.com/webview", result.getWebViewUrl());
        Assert.assertEquals("Should map deeplink URL", "app://test", result.getDeeplinkUrl());
        Assert.assertTrue("Should map is login", result.getIsLogin());
        Assert.assertNotNull("Should map categories", result.getCategories());
    }

    @Test
    public void testMapCardAction_withComplexFields_mapsAllComplexFields() throws Exception {
        CardData.CardAction source = createComplexCardAction();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardAction", CardData.CardAction.class);
        method.setAccessible(true);
        
        CardAction result = (CardAction) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid card action", result);
        Assert.assertNotNull("Should map filters", result.getFilters());
        Assert.assertNotNull("Should map action", result.getAction());
        Assert.assertEquals("Should map action title", "More Info", result.getAction().getTitle());
        Assert.assertNotNull("Should map data", result.getData());
    }

    @Test
    public void testMapCardPayloadData_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardPayloadData", CardData.CardPayloadResponse.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.CardPayloadResponse) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardPayloadData_withEmptyGenericCardData_returnsEmptyPayload() throws Exception {
        CardData.CardPayloadResponse source = new CardData.CardPayloadResponse();
        source.setGenericCardData(new ArrayList<>());
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardPayloadData", CardData.CardPayloadResponse.class);
        method.setAccessible(true);
        
        CardPayloadData result = (CardPayloadData) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid payload data", result);
        Assert.assertNull("Should have null generic card data for empty list", result.getGenericCardData());
    }

    @Test
    public void testMapCardPayloadData_withSingleLevelData_mapsCorrectly() throws Exception {
        CardData.CardPayloadResponse source = createCardPayloadWithSingleLevelData();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardPayloadData", CardData.CardPayloadResponse.class);
        method.setAccessible(true);
        
        CardPayloadData result = (CardPayloadData) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid payload data", result);
        Assert.assertNotNull("Should have generic card data", result.getGenericCardData());
        Assert.assertEquals("Should have correct number of items", 2, result.getGenericCardData().size());
        
        GenericCardPayloadDataCG firstItem = result.getGenericCardData().get(0);
        Assert.assertEquals("Should map ID", "ITEM1", firstItem.getId());
        Assert.assertEquals("Should map text", "Item 1 Text", firstItem.getText());
        Assert.assertEquals("Should map tag", "TAG1", firstItem.getTag());
        Assert.assertEquals("Should map title text", "Item 1 Title", firstItem.getTitleText());
        Assert.assertEquals("Should map sub text", "Item 1 Sub", firstItem.getSubText());
        Assert.assertEquals("Should map image URL", "https://test.com/image1.png", firstItem.getImageUrl());
        Assert.assertEquals("Should map action URL", "https://test.com/action1", firstItem.getActionUrl());
        Assert.assertEquals("Should map icon URL", "https://test.com/icon1.png", firstItem.getIconUrl());
        Assert.assertTrue("Should map gallery view", firstItem.getGalleryView());
        Assert.assertEquals("Should map item icon type", "ICON", firstItem.getItemIconType());
    }

    @Test
    public void testMapCardPayloadData_withNestedData_mapsRecursively() throws Exception {
        CardData.CardPayloadResponse source = createCardPayloadWithNestedData();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardPayloadData", CardData.CardPayloadResponse.class);
        method.setAccessible(true);
        
        CardPayloadData result = (CardPayloadData) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid payload data", result);
        Assert.assertNotNull("Should have generic card data", result.getGenericCardData());
        Assert.assertEquals("Should have one parent item", 1, result.getGenericCardData().size());
        
        GenericCardPayloadDataCG parentItem = result.getGenericCardData().get(0);
        Assert.assertNotNull("Should have nested data", parentItem.getData());
        Assert.assertEquals("Should have correct number of nested items", 2, parentItem.getData().size());
        
        GenericCardPayloadDataCG nestedItem = parentItem.getData().get(0);
        Assert.assertEquals("Should map nested ID", "NESTED1", nestedItem.getId());
        Assert.assertEquals("Should map nested text", "Nested 1 Text", nestedItem.getText());
    }

    @Test
    public void testMapCardSheet_withNullSource_returnsNull() throws Exception {
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardSheet", CardData.CardSheet.class);
        method.setAccessible(true);
        
        Object result = method.invoke(cardDataResponseHelper, (CardData.CardSheet) null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardSheet_withTitleAndContent_mapsTopSheet() throws Exception {
        CardData.CardSheet source = createCardSheetWithTitleAndContent();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardSheet", CardData.CardSheet.class);
        method.setAccessible(true);
        
        CardSheet result = (CardSheet) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid card sheet", result);
        Assert.assertNotNull("Should have top sheet", result.getTopSheet());
        Assert.assertEquals("Should map title to text", "Sheet Title", result.getTopSheet().getText());
        Assert.assertEquals("Should map content to sub text", "Sheet Content", result.getTopSheet().getSubText());
    }

    @Test
    public void testMapCardSheet_withItems_mapsBottomSheetWithInfoList() throws Exception {
        CardData.CardSheet source = createCardSheetWithItems();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardSheet", CardData.CardSheet.class);
        method.setAccessible(true);
        
        CardSheet result = (CardSheet) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid card sheet", result);
        Assert.assertNotNull("Should have bottom sheet", result.getBottomSheet());
        Assert.assertNotNull("Should have info list", result.getBottomSheet().getInfoList());
        Assert.assertEquals("Should have correct number of items", 2, result.getBottomSheet().getInfoList().size());
        
        GenericCardPayloadDataCG firstInfo = result.getBottomSheet().getInfoList().get(0);
        Assert.assertEquals("Should map title", "Item 1", firstInfo.getTitleText());
        Assert.assertEquals("Should map description", "Description 1", firstInfo.getSubText());
        Assert.assertEquals("Should map icon URL", "https://test.com/icon1.png", firstInfo.getIconUrl());
        Assert.assertEquals("Should map action URL", "https://test.com/action1", firstInfo.getActionUrl());
    }

    @Test
    public void testMapCardSheet_withBothTitleAndItems_mapsBothSheets() throws Exception {
        CardData.CardSheet source = createCardSheetWithTitleAndItems();
        Method method = CardDataResponseHelper.class.getDeclaredMethod(
            "mapCardSheet", CardData.CardSheet.class);
        method.setAccessible(true);
        
        CardSheet result = (CardSheet) method.invoke(cardDataResponseHelper, source);
        
        Assert.assertNotNull("Should return valid card sheet", result);
        Assert.assertNotNull("Should have top sheet", result.getTopSheet());
        Assert.assertNotNull("Should have bottom sheet", result.getBottomSheet());
        Assert.assertEquals("Should map title", "Both Title", result.getTopSheet().getText());
        Assert.assertNotNull("Should have info list", result.getBottomSheet().getInfoList());
        Assert.assertEquals("Should have items", 1, result.getBottomSheet().getInfoList().size());
    }

    // ==================== PUBLIC HELPER METHOD TESTS ====================

    @Test
    public void testMapFloatingSheetData_withNullSource_returnsNull() {
        FloatingSheetData result = cardDataResponseHelper.mapFloatingSheetData(null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapFloatingSheetData_withBasicFields_mapsCorrectly() {
        CardData.FloatingSheetData source = createFloatingSheetData();
        
        FloatingSheetData result = cardDataResponseHelper.mapFloatingSheetData(source);
        
        Assert.assertNotNull("Should return valid floating sheet data", result);
        Assert.assertEquals("Should map title to text", "Floating Title", result.getText());
        Assert.assertEquals("Should map action text", "Click Me", result.getFlotingActionName());
        Assert.assertNotNull("Should set current timestamp", result.getCurrentTimeStamp());
    }

    @Test
    public void testMapFloatingSheetData_withActionUrl_handlesActionUrlMapping() {
        CardData.FloatingSheetData source = createFloatingSheetDataWithActionUrl();
        
        FloatingSheetData result = cardDataResponseHelper.mapFloatingSheetData(source);
        
        Assert.assertNotNull("Should return valid floating sheet data", result);
        Assert.assertEquals("Should prefer action text over action URL", "Action Text", result.getFlotingActionName());
        
        // Test with null action text
        source.setActionText(null);
        result = cardDataResponseHelper.mapFloatingSheetData(source);
        Assert.assertEquals("Should fallback to action URL", "https://test.com/action", result.getFlotingActionName());
    }

    @Test
    public void testMapRushDealTimerInfo_withNullSource_returnsNull() {
        RushDealTimerInfo result = cardDataResponseHelper.mapRushDealTimerInfo(null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapRushDealTimerInfo_withValidSource_mapsUrgencyAndEndTime() {
        CardData.RushDealTimerInfo source = createRushDealTimerInfo();
        
        RushDealTimerInfo result = cardDataResponseHelper.mapRushDealTimerInfo(source);
        
        Assert.assertNotNull("Should return valid rush deal timer info", result);
        Assert.assertEquals("Should map urgency text to desc", "Limited Time Offer", result.getDesc());
        Assert.assertEquals("Should map end time to validity timestamp", "1640995200000", result.getValidityTimestamp());
    }

    @Test
    public void testMapRushDealTimerInfo_withZeroEndTime_handlesCorrectly() {
        CardData.RushDealTimerInfo source = createRushDealTimerInfo();
        source.setEndTime(0);
        
        RushDealTimerInfo result = cardDataResponseHelper.mapRushDealTimerInfo(source);
        
        Assert.assertNotNull("Should return valid rush deal timer info", result);
        Assert.assertNull("Should not set validity timestamp for zero end time", result.getValidityTimestamp());
    }

    @Test
    public void testMapCardCondition_withNullSource_returnsNull() {
        CardCondition result = cardDataResponseHelper.mapCardCondition(null);
        
        Assert.assertNull("Should return null for null source", result);
    }

    @Test
    public void testMapCardCondition_withCheckIfFilterNotApplied_mapsFilter() {
        CardData.CardCondition source = createCardConditionWithFilter();
        
        CardCondition result = cardDataResponseHelper.mapCardCondition(source);
        
        Assert.assertNotNull("Should return valid card condition", result);
        Assert.assertNotNull("Should map filter", result.getCheckIfFilterNotApplied());
        Assert.assertNotNull("Should map filter group", result.getCheckIfFilterNotApplied().getFilterGroup());
        Assert.assertEquals("Should map filter value", "TEST_VALUE", result.getCheckIfFilterNotApplied().getFilterValue());
    }

    @Test
    public void testMapCardCondition_withNullFilter_handlesGracefully() {
        CardData.CardCondition source = new CardData.CardCondition();
        source.setCheckIfFilterNotApplied(null);
        
        CardCondition result = cardDataResponseHelper.mapCardCondition(source);
        
        Assert.assertNotNull("Should return valid card condition", result);
        Assert.assertNull("Should handle null filter gracefully", result.getCheckIfFilterNotApplied());
    }

    // ==================== TEST DATA BUILDERS ====================

    private PersonalizationCards createPersonalizationCardsWithSingleCard() {
        PersonalizationCards cards = new PersonalizationCards();
        cards.setExperimentId(123);
        cards.setTrackText("TRACK_TEXT");
        
        List<CardData> cardDataList = new ArrayList<>();
        cardDataList.add(createBasicCardData());
        cards.setCardData(cardDataList);
        
        return cards;
    }

    private PersonalizationCards createPersonalizationCardsWithMultipleCards() {
        PersonalizationCards cards = new PersonalizationCards();
        cards.setExperimentId(456);
        cards.setTrackText("MULTI_TRACK");
        
        List<CardData> cardDataList = new ArrayList<>();
        cardDataList.add(createBasicCardData());
        cardDataList.add(createBasicCardData());
        cardDataList.add(createBasicCardData());
        cards.setCardData(cardDataList);
        
        return cards;
    }

    private PersonalizationCards createPersonalizationCardsWithMixedCards() {
        PersonalizationCards cards = new PersonalizationCards();
        cards.setExperimentId(789);
        cards.setTrackText("MIXED_TRACK");
        
        List<CardData> cardDataList = new ArrayList<>();
        cardDataList.add(createBasicCardData());
        cardDataList.add(null); // This should be filtered out
        cardDataList.add(createBasicCardData());
        cards.setCardData(cardDataList);
        
        return cards;
    }

    private PersonalizationCards createPersonalizationCardsWithComplexData() {
        PersonalizationCards cards = new PersonalizationCards();
        cards.setExperimentId(999);
        cards.setTrackText("COMPLEX_TRACK");
        
        List<CardData> cardDataList = new ArrayList<>();
        cardDataList.add(createCardDataWithAllComplexObjects());
        cards.setCardData(cardDataList);
        
        return cards;
    }

    private CardData createBasicCardData() {
        CardData cardData = new CardData();
        cardData.setCardId("CARD123");
        cardData.setCardSubType("PROMO");
        cardData.setTitleText("Test Card");
        cardData.setTemplateId("TEMPLATE123");
        cardData.setTemplateType("BASIC");
        cardData.setPageContext(Arrays.asList("DETAIL"));
        cardData.setIconUrl("https://test.com/icon.png");
        cardData.setHasAction(true);
        cardData.setHasClaimed(true);
        cardData.setSubText("Test Sub Text");
        cardData.setActionText("Click Here");
        cardData.setHeading("Test Heading");
        cardData.setDesc("Test Description");
        cardData.setBgImageUrl("https://test.com/bg.png");
        cardData.setBgColor("#FFFFFF");
        cardData.setTextColor("#000000");
        cardData.setBgGradient("linear-gradient");
        cardData.setBorderColor("#CCCCCC");
        cardData.setMinItemsToShow(3);
        cardData.setDealType("DISCOUNT");
        cardData.setHeaderUrl("https://test.com/header.png");
        cardData.setCouponCode("SAVE20");
        cardData.setTitleTextColor("#333333");
        
        List<String> imageList = Arrays.asList("image1.png", "image2.png");
        cardData.setImageList(imageList);
        
        return cardData;
    }

    private CardData createCardDataWithExtraFields() {
        CardData cardData = createBasicCardData();
        cardData.setRemoveCard(true);
        cardData.setHasToggle(false);
        cardData.setHasLocation(true);
        cardData.setCardPosition(2);
        return cardData;
    }

    private CardData createCardDataWithAllComplexObjects() {
        CardData cardData = createBasicCardData();
        
        // Set complex objects
        cardData.setBgLinearGradient(createBGLinearGradient());
        cardData.setBorderGradient(createBorderGradient());
        cardData.setIconTag(createIconTag());
        cardData.setToggleAction(CardData.ToggleAction.ON);
        cardData.setCardSheet(createCardSheetWithTitleAndContent());
        cardData.setFloatingSheetData(createFloatingSheetData());
        cardData.setRushDealTimerInfo(createRushDealTimerInfo());
        cardData.setCardCondition(createCardConditionWithFilter());
        cardData.setCardPayload(createCardPayloadWithSingleLevelData());
        cardData.setCardAction(createCardActionList());
        
        return cardData;
    }

    private CardData.BGLinearGradient createBGLinearGradient() {
        CardData.BGLinearGradient gradient = new CardData.BGLinearGradient();
        gradient.setStartColor("#FF0000");
        gradient.setEndColor("#00FF00");
        gradient.setDirection("horizontal");
        return gradient;
    }

    private CardData.BorderGradient createBorderGradient() {
        CardData.BorderGradient gradient = new CardData.BorderGradient();
        gradient.setStartColor("#000000");
        gradient.setEndColor("#FFFFFF");
        gradient.setDirection("vertical");
        return gradient;
    }

    private CardData.BorderGradient createBorderGradientWithColorsList() {
        CardData.BorderGradient gradient = createBorderGradient();
        List<String> colors = Arrays.asList("#FF0000", "#00FF00", "#0000FF");
        gradient.setColors(colors);
        return gradient;
    }

    private CardData.IconTag createIconTag() {
        CardData.IconTag iconTag = new CardData.IconTag();
        iconTag.setText("NEW");
        iconTag.setTextColor("#BLUE");
        return iconTag;
    }

    private CardData.IconTag createIconTagWithBackgroundColor() {
        CardData.IconTag iconTag = createIconTag();
        iconTag.setBackgroundColor("#RED");
        return iconTag;
    }

    private CardData.CardActionData createCardActionData() {
        CardData.CardActionData actionData = new CardData.CardActionData();
        actionData.setTitle("Action Title");
        return actionData;
    }

    private CardData.CardActionData createCardActionDataWithSections() {
        CardData.CardActionData actionData = createCardActionData();
        
        List<CardData.CardActionData.Section> sections = new ArrayList<>();
        
        // First section
        CardData.CardActionData.Section section1 = new CardData.CardActionData.Section();
        section1.setTitle("Section 1");
        
        List<CardData.CardActionData.Section.Item> items1 = new ArrayList<>();
        CardData.CardActionData.Section.Item item1 = new CardData.CardActionData.Section.Item();
        item1.setText("Item 1");
        item1.setTextBoxTitle("Box 1");
        items1.add(item1);
        
        CardData.CardActionData.Section.Item item2 = new CardData.CardActionData.Section.Item();
        item2.setText("Item 2");
        item2.setTextBoxTitle("Box 2");
        items1.add(item2);
        
        section1.setItems(items1);
        sections.add(section1);
        
        // Second section
        CardData.CardActionData.Section section2 = new CardData.CardActionData.Section();
        section2.setTitle("Section 2");
        sections.add(section2);
        
        actionData.setSections(sections);
        return actionData;
    }

    private List<CardData.CardAction> createCardActionList() {
        List<CardData.CardAction> actions = new ArrayList<>();
        
        CardData.CardAction action1 = new CardData.CardAction();
        action1.setTitle("Action 1");
        actions.add(action1);
        
        CardData.CardAction action2 = new CardData.CardAction();
        action2.setTitle("Action 2");
        actions.add(action2);
        
        return actions;
    }

    private CardData.CardAction createBasicCardAction() {
        CardData.CardAction action = new CardData.CardAction();
        action.setTitle("Basic Action");
        action.setWebViewUrl("https://test.com/webview");
        action.setDeeplinkUrl("app://test");
        action.setIsLogin(true);
        
        List<String> categories = Arrays.asList("CAT1", "CAT2");
        action.setCategories(categories);
        
        return action;
    }

    private CardData.CardAction createComplexCardAction() {
        CardData.CardAction action = createBasicCardAction();
        
        // Add filters
        CardData.Filters filters = new CardData.Filters();
        action.setFilters(filters);
        
        // Add action
        CardData.MoreInfoAction moreInfoAction = new CardData.MoreInfoAction();
        moreInfoAction.setTitle("More Info");
        moreInfoAction.setActionProp("ACTION_PROP");
        action.setAction(moreInfoAction);
        
        // Add data
        action.setData(createCardActionData());
        
        return action;
    }

    private CardData.CardPayloadResponse createCardPayloadWithSingleLevelData() {
        CardData.CardPayloadResponse payload = new CardData.CardPayloadResponse();
        
        List<CardData.GenericCardPayloadData> genericData = new ArrayList<>();
        
        CardData.GenericCardPayloadData item1 = new CardData.GenericCardPayloadData();
        item1.setId("ITEM1");
        item1.setText("Item 1 Text");
        item1.setTag("TAG1");
        item1.setTitleText("Item 1 Title");
        item1.setSubText("Item 1 Sub");
        item1.setImageUrl("https://test.com/image1.png");
        item1.setActionUrl("https://test.com/action1");
        item1.setIconUrl("https://test.com/icon1.png");
        item1.setGalleryView(true);
        item1.setItemIconType("ICON");
        genericData.add(item1);
        
        CardData.GenericCardPayloadData item2 = new CardData.GenericCardPayloadData();
        item2.setId("ITEM2");
        item2.setText("Item 2 Text");
        item2.setGalleryView(false);
        genericData.add(item2);
        
        payload.setGenericCardData(genericData);
        return payload;
    }

    private CardData.CardPayloadResponse createCardPayloadWithNestedData() {
        CardData.CardPayloadResponse payload = new CardData.CardPayloadResponse();
        
        List<CardData.GenericCardPayloadData> genericData = new ArrayList<>();
        
        CardData.GenericCardPayloadData parentItem = new CardData.GenericCardPayloadData();
        parentItem.setId("PARENT1");
        parentItem.setText("Parent Item");
        
        // Add nested data
        List<CardData.GenericCardPayloadData> nestedData = new ArrayList<>();
        
        CardData.GenericCardPayloadData nested1 = new CardData.GenericCardPayloadData();
        nested1.setId("NESTED1");
        nested1.setText("Nested 1 Text");
        nestedData.add(nested1);
        
        CardData.GenericCardPayloadData nested2 = new CardData.GenericCardPayloadData();
        nested2.setId("NESTED2");
        nested2.setText("Nested 2 Text");
        nestedData.add(nested2);
        
        parentItem.setData(nestedData);
        genericData.add(parentItem);
        
        payload.setGenericCardData(genericData);
        return payload;
    }

    private CardData.CardSheet createCardSheetWithTitleAndContent() {
        CardData.CardSheet sheet = new CardData.CardSheet();
        sheet.setTitle("Sheet Title");
        sheet.setContent("Sheet Content");
        return sheet;
    }

    private CardData.CardSheet createCardSheetWithItems() {
        CardData.CardSheet sheet = new CardData.CardSheet();
        
        List<CardData.CardSheet.SheetItem> items = new ArrayList<>();
        
        CardData.CardSheet.SheetItem item1 = new CardData.CardSheet.SheetItem();
        item1.setTitle("Item 1");
        item1.setDescription("Description 1");
        item1.setIconUrl("https://test.com/icon1.png");
        item1.setActionUrl("https://test.com/action1");
        items.add(item1);
        
        CardData.CardSheet.SheetItem item2 = new CardData.CardSheet.SheetItem();
        item2.setTitle("Item 2");
        item2.setDescription("Description 2");
        items.add(item2);
        
        sheet.setItems(items);
        return sheet;
    }

    private CardData.CardSheet createCardSheetWithTitleAndItems() {
        CardData.CardSheet sheet = new CardData.CardSheet();
        sheet.setTitle("Both Title");
        sheet.setContent("Both Content");
        
        List<CardData.CardSheet.SheetItem> items = new ArrayList<>();
        CardData.CardSheet.SheetItem item = new CardData.CardSheet.SheetItem();
        item.setTitle("Item");
        item.setDescription("Description");
        items.add(item);
        sheet.setItems(items);
        
        return sheet;
    }

    private CardData.FloatingSheetData createFloatingSheetData() {
        CardData.FloatingSheetData data = new CardData.FloatingSheetData();
        data.setTitle("Floating Title");
        data.setActionText("Click Me");
        return data;
    }

    private CardData.FloatingSheetData createFloatingSheetDataWithActionUrl() {
        CardData.FloatingSheetData data = new CardData.FloatingSheetData();
        data.setTitle("Floating Title");
        data.setActionText("Action Text");
        data.setActionUrl("https://test.com/action");
        return data;
    }

    private CardData.RushDealTimerInfo createRushDealTimerInfo() {
        CardData.RushDealTimerInfo timer = new CardData.RushDealTimerInfo();
        timer.setUrgencyText("Limited Time Offer");
        timer.setEndTime(1640995200000L); // Unix timestamp
        timer.setShowTimer(true);
        timer.setTimerText("Hurry Up!");
        timer.setTimerFormat("HH:mm:ss");
        return timer;
    }

    private CardData.CardCondition createCardConditionWithFilter() {
        CardData.CardCondition condition = new CardData.CardCondition();
        
        CardData.CardCondition.CheckIfFilterNotApplied filter = new CardData.CardCondition.CheckIfFilterNotApplied();
        // Using a mock enum value - would need actual enum from orchestrator
        filter.setFilterGroup(createMockFilterGroup());
        filter.setFilterValue("TEST_VALUE");
        
        condition.setCheckIfFilterNotApplied(filter);
        return condition;
    }

    // Mock method to create filter group - uses actual FilterGroup enum
    private FilterGroup createMockFilterGroup() {
        // Return the first available FilterGroup enum value for testing
        // This assumes FilterGroup has at least one enum value
        FilterGroup[] values = FilterGroup.values();
        return values.length > 0 ? values[0] : null;
    }
} 