package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsRequestTransformerPWATest {

    @InjectMocks
    SearchRoomsRequestTransformerPWA searchRoomsRequestTransformerPWA;

    @Mock
    MetricAspect metricAspect;

    @Mock
    Utility utility;

    @Test
    public void testConvertSearchRoomsRequest(){
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setExpData("abc");

        searchRoomsRequest.setFeatureFlags(new FeatureFlags());
        searchRoomsRequest.getFeatureFlags().setStaticData(true);
        searchRoomsRequest.getFeatureFlags().setReviewSummaryRequired(true);
        searchRoomsRequest.getFeatureFlags().setWalletRequired(true);
        searchRoomsRequest.getFeatureFlags().setShortlistingRequired(false);

        searchRoomsRequest.setRequestDetails(new RequestDetails());
        searchRoomsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        searchRoomsRequest.getRequestDetails().setLoggedIn(false);
        searchRoomsRequest.getRequestDetails().setSrLng(10d);
        searchRoomsRequest.getRequestDetails().setSrLat(10d);

        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        searchRoomsRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges().add(1);

        PriceByHotelsRequestBody priceByHotelsRequestBody = searchRoomsRequestTransformerPWA.convertSearchRoomsRequest(searchRoomsRequest, new CommonModifierResponse());
        org.springframework.util.Assert.notNull(priceByHotelsRequestBody);
    }
}
