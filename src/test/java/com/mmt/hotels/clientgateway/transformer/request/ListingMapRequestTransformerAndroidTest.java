package com.mmt.hotels.clientgateway.transformer.request;

import java.util.ArrayList;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.ImageCategory;
import com.mmt.hotels.clientgateway.request.ImageDetails;
import com.mmt.hotels.clientgateway.request.LatLngBounds;
import com.mmt.hotels.clientgateway.request.ListingMapRequest;
import com.mmt.hotels.clientgateway.request.MapDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SortCriteria;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.transformer.request.android.ListingMapRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class ListingMapRequestTransformerAndroidTest {
	@InjectMocks
    ListingMapRequestTransformerAndroid listingMapRequestTransformerAndroid;

    @Mock
    Utility utility;

    @Test
    public void testConvertListingMapRequest() {
        ListingMapRequest listingMapRequest = new ListingMapRequest();
        listingMapRequest.setDeviceDetails(new DeviceDetails());

        listingMapRequest.setRequestDetails(new RequestDetails());
        listingMapRequest.getRequestDetails().setSrLat(28d);
        listingMapRequest.getRequestDetails().setSrLng(28d);

        listingMapRequest.setSearchCriteria(new SearchHotelsCriteria());
        listingMapRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        listingMapRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        listingMapRequest.getSearchCriteria().setLat(28d);
        listingMapRequest.getSearchCriteria().setLng(28d);

        listingMapRequest.setImageDetails(new ImageDetails());
        listingMapRequest.getImageDetails().setCategories(new ArrayList<ImageCategory>());
        listingMapRequest.getImageDetails().getCategories().add(new ImageCategory());
        listingMapRequest.getImageDetails().getCategories().get(0).setCount(2);
        listingMapRequest.getImageDetails().getCategories().get(0).setHeight(2);
        listingMapRequest.getImageDetails().getCategories().get(0).setWidth(2);

        listingMapRequest.setFeatureFlags(new FeatureFlags());

        listingMapRequest.setSortCriteria(new SortCriteria());

        listingMapRequest.getRequestDetails().setTrafficSource(new TrafficSource());

        listingMapRequest.setMapDetails(new MapDetails());
        listingMapRequest.getMapDetails().setLatLngBounds(new LatLngBounds());

        listingMapRequest.setMatchMakerDetails(new MatchMakerRequest());

        listingMapRequest.setExpData("{APE:36,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,EMIDT:2}");

        SearchWrapperInputRequest searchWrapperInputRequest = listingMapRequestTransformerAndroid.convertListingMapRequest(listingMapRequest, new CommonModifierResponse());
        Assert.notNull(searchWrapperInputRequest);
    }
}
