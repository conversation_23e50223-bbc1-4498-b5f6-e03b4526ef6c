package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.rooms.LinkedRatePlan;
import com.mmt.hotels.clientgateway.enums.UpgradeType;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.Facility;
import com.mmt.hotels.clientgateway.response.FacilityGroup;
import com.mmt.hotels.clientgateway.response.GoTribeInclusion;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.enums.InclusionCategory;
import com.mmt.hotels.model.response.pricing.Inclusion;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class SelectRoomV2ResponseTransformerTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private SelectRoomV2ResponseTransformer selectRoomV2ResponseTransformer;

    @Before
    public void setup() {

        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
    }

    private static List<Inclusion> getHESInclusions() {
        List<Inclusion> hesInclusions = new ArrayList<>();

        Inclusion losInclusion = new Inclusion();
        losInclusion.setInclusionType(Constants.INCLUSION_TYPE_LOS);
        losInclusion.setCode("Long stay benefit 1");
        losInclusion.setValue("Long stay benefit 1 value");
        hesInclusions.add(losInclusion);

        Inclusion blackInclusion = new Inclusion();
        blackInclusion.setSegmentIdentifier(Constants.BLACK_SEGMENT_IDENTIFIER);
        blackInclusion.setCode("Black Inclusion 1");
        blackInclusion.setValue("Black Inclusion 1 value");
        hesInclusions.add(blackInclusion);

        Inclusion pricerInclusion = new Inclusion();
        pricerInclusion.setCode("Pricer Inclusion 1");
        pricerInclusion.setValue("Pricer Inclusion 1 value");
        hesInclusions.add(pricerInclusion);
        return hesInclusions;
    }

    private static GoTribeInclusion getGoTribeInclusion() {
        GoTribeInclusion goTribeInclusion = new GoTribeInclusion();
        BookedInclusion roomUpgradeBenefit = new BookedInclusion();
        roomUpgradeBenefit.setLeafCategory(InclusionCategory.ROOM_UPGRADE.getName());
        roomUpgradeBenefit.setText("Room upgrade available");
        roomUpgradeBenefit.setSubText("Room upgrade available");

        BookedInclusion discountBenefit = new BookedInclusion();
        discountBenefit.setLeafCategory(InclusionCategory.DISCOUNT.getName());
        discountBenefit.setText("20% discount on services");
        discountBenefit.setSubText("20% discount on services");
        goTribeInclusion.setBenefitsList(Arrays.asList(roomUpgradeBenefit, discountBenefit));

        goTribeInclusion.setUpgradeType(UpgradeType.ROOM.name());
        return goTribeInclusion;
    }

    @Test
    public void testRoomHighlightsOrder() {
        RoomDetails roomDetails = new RoomDetails();
        RoomHighlight roomHighlight1 = new RoomHighlight();
        roomHighlight1.setSelectRoomRevampOrder(2);
        roomHighlight1.setText("Room Highlight 1");
        roomHighlight1.setIdentifier("RH1");

        RoomHighlight roomHighlight2 = new RoomHighlight();
        roomHighlight2.setSelectRoomRevampOrder(null);
        roomHighlight2.setText("Room Highlight 2");
        roomHighlight2.setIdentifier("RH2");

        RoomHighlight roomHighlight3 = new RoomHighlight();
        roomHighlight3.setSelectRoomRevampOrder(1);
        roomHighlight3.setText("Room Highlight 3");
        roomHighlight3.setIdentifier("RH3");

        roomDetails.setRoomHighlights(Arrays.asList(roomHighlight1, roomHighlight2, roomHighlight3));
        selectRoomV2ResponseTransformer.alterRoomDetailsForSelectRoomRevamp(roomDetails, Constants.STAY_TYPE_HOTEL);

        Assert.assertEquals(2, roomDetails.getRoomHighlights().size());
        Assert.assertEquals("RH3", roomDetails.getRoomHighlights().get(0).getIdentifier());
    }

    @Test
    public void testSignatureAmenities() {
        RoomDetails roomDetails = new RoomDetails();
        FacilityGroup signatureAmenityGroup = new FacilityGroup();
        signatureAmenityGroup.setId(Constants.SIGNATURE_AMENITIES);
        signatureAmenityGroup.setFacilities(Arrays.asList(new Facility(), new Facility(), new Facility()));

        roomDetails.setAmenities(Collections.singletonList(signatureAmenityGroup));
        selectRoomV2ResponseTransformer.alterRoomDetailsForSelectRoomRevamp(roomDetails, Constants.STAY_TYPE_HOTEL);

        Assert.assertEquals(1, roomDetails.getAmenities().size());
    }

    @Test
    public void testRatePlanInclusions() {
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRatePlans(Collections.singletonList(new SelectRoomRatePlan()));
        List<Inclusion> hesInclusions = getHESInclusions();
        GoTribeInclusion goTribeInclusion = getGoTribeInclusion();

        roomDetails.getRatePlans().get(0).setGoTribeInclusion(goTribeInclusion);
        roomDetails.getRatePlans().get(0).setHesInclusions(hesInclusions);
        selectRoomV2ResponseTransformer.alterRoomDetailsForSelectRoomRevamp(roomDetails, Constants.STAY_TYPE_HOTEL);
        Assert.assertEquals(3,roomDetails.getRatePlans().get(0).getInclusionsList().size());
    }

    @Test
    public void testTopLeftPersuasion() {
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRatePlans(Collections.singletonList(new SelectRoomRatePlan()));
        GoTribeInclusion goTribeInclusion = getGoTribeInclusion();

        roomDetails.getRatePlans().get(0).setGoTribeInclusion(goTribeInclusion);
        selectRoomV2ResponseTransformer.alterRoomDetailsForSelectRoomRevamp(roomDetails, Constants.STAY_TYPE_HOTEL);
        Assert.assertNotNull(roomDetails.getRatePlans().get(0).getTopLeftPersuasion());
    }

    @Test
    public void testBookAtZeroInclusion() {
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRatePlans(Collections.singletonList(new SelectRoomRatePlan()));

        roomDetails.getRatePlans().get(0).setCancellationPolicyTimeline(new CancellationPolicyTimeline());
        roomDetails.getRatePlans().get(0).getCancellationPolicyTimeline().setCardChargeTextTitle("Book at 0");
        roomDetails.getRatePlans().get(0).getCancellationPolicyTimeline().setCardChargeTextMsg("avoid auto cancellation");
        selectRoomV2ResponseTransformer.alterRoomDetailsForSelectRoomRevamp(roomDetails, Constants.STAY_TYPE_HOTEL);
        Assert.assertEquals(1,roomDetails.getRatePlans().get(0).getInclusionsList().size());
    }

    @Test
    public void testProcessLinkedRatePlan() throws Exception {

        BookedInclusion inclusion = new BookedInclusion();
        inclusion.setShowOnSelectRoom(false);
        BookedCancellationPolicy cancellationPolicy = new BookedCancellationPolicy();
        cancellationPolicy.setType(BookedCancellationPolicyType.NR);
        SelectRoomRatePlan linkedRatePlanDetails = new SelectRoomRatePlan();
        linkedRatePlanDetails.setInclusionsList(Collections.singletonList(inclusion));
        linkedRatePlanDetails.setCancellationPolicy(cancellationPolicy);
        LinkedRatePlan linkedRatePlan = new LinkedRatePlan();
        linkedRatePlan.setRatePlan(linkedRatePlanDetails);
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setLinkedRatePlans(Collections.singletonList(linkedRatePlan));
        Method method = SelectRoomV2ResponseTransformer.class.getDeclaredMethod("processLinkedRatePlan", SelectRoomRatePlan.class);
        method.setAccessible(true);
        method.invoke(selectRoomV2ResponseTransformer, ratePlan);
        Assert.assertTrue(linkedRatePlanDetails.getInclusionsList().get(0).isShowOnSelectRoom());
        Assert.assertEquals("NON_REFUNDABLE_SHORT_HTML_TEXT", linkedRatePlanDetails.getCancellationPolicy().getShortHtmlText());
    }
}
