package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.response.filter.FilterPill;
import com.mmt.hotels.clientgateway.transformer.response.FilterPillConfig;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class SmartFiltersTest {

    private FilterResponseTransformer filterResponseTransformer;
    private Gson gson;

    @Before
    public void setUp() {
        filterResponseTransformer = new FilterResponseTransformer();
        gson = new Gson();
    }

    @Test
    public void testIsSmartFiltersApplicable_ExperimentEnabled_AndroidClient() {
        // Arrange
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.SMART_FILTERS.getKey(), "true");
        
        FilterCountRequest filterRequest = new FilterCountRequest();
        filterRequest.setClient("ANDROID");
        
        // Act
        boolean result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, 
            "isSmartFiltersApplicable", expDataMap, filterRequest);
        
        // Assert
        assertTrue("Smart filters should be applicable when experiment is enabled and client is ANDROID", result);
    }

    @Test
    public void testIsSmartFiltersApplicable_ExperimentDisabled() {
        // Arrange
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.SMART_FILTERS.getKey(), "false");
        
        FilterCountRequest filterRequest = new FilterCountRequest();
        filterRequest.setClient("ANDROID");
        
        // Act
        boolean result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, 
            "isSmartFiltersApplicable", expDataMap, filterRequest);
        
        // Assert
        assertFalse("Smart filters should not be applicable when experiment is disabled", result);
    }

    @Test
    public void testIsSmartFiltersApplicable_PWAClient() {
        // Arrange
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.SMART_FILTERS.getKey(), "true");
        
        FilterCountRequest filterRequest = new FilterCountRequest();
        filterRequest.setClient("PWA");
        
        // Act
        boolean result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, 
            "isSmartFiltersApplicable", expDataMap, filterRequest);
        
        // Assert
        assertFalse("Smart filters should not be applicable for PWA client", result);
    }

    @Test
    public void testIsSmartFiltersApplicable_IOSClient() {
        // Arrange
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.SMART_FILTERS.getKey(), "true");
        
        FilterCountRequest filterRequest = new FilterCountRequest();
        filterRequest.setClient("IOS");
        
        // Act
        boolean result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, 
            "isSmartFiltersApplicable", expDataMap, filterRequest);
        
        // Assert
        assertTrue("Smart filters should be applicable for IOS client", result);
    }

    @Test
    public void testSmartFiltersConfiguration() {
        // Test that the configuration is properly loaded
        String pillConfigB2C = "{\"filterPills\":{\"smartFilters\":{\"id\":\"smartFilters\",\"title\":\"Smart Filters\",\"type\":\"filter\",\"categories\":[\"smartFilters\"],\"iconList\":[\"https://www.imagecdn.com/smart-filters.jpg\"],\"bottomsheet\":{\"title\":\"Smart Filters\",\"subtitle\":\"Search any filter you like\"}}},\"pillSequence\":{\"smartFilters\":1}}";
        
        FilterPillConfig config = gson.fromJson(pillConfigB2C, new TypeToken<FilterPillConfig>(){}.getType());
        
        assertNotNull("Configuration should not be null", config);
        assertNotNull("Filter pills should not be null", config.getFilterPills());
        assertTrue("Smart filters should be present in configuration", config.getFilterPills().containsKey("smartFilters"));
        assertEquals("Smart filters should be at sequence 1", Integer.valueOf(1), config.getPillSequence().get("smartFilters"));
        assertEquals("Smart filters title should match", "Smart Filters", config.getFilterPills().get("smartFilters").getTitle());
    }
} 