package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail;

import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerIOS;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;
import static junit.framework.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrchStaticDetailsResponseTransformerIOSTest {

    @InjectMocks
    private OrchStaticDetailsResponseTransformerIOS transformer;

    @Mock
    private com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo orchestratorStaffInfo;

    @Mock
    private StaffInfo clientGatewayStaffInfo;

    private String starHostIconApp = "https://test.com/star-host-icon.png";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(transformer, "starHostIconApp", starHostIconApp);
    }

    @Test
    public void testBuildCardTitleMap_returnsNull() {
        // Test buildCardTitleMap method
        Map<String, String> result = transformer.buildCardTitleMap();
        assertNull("buildCardTitleMap should return null", result);
    }

    @Test
    public void testAddTitleData_doesNothing() {
        // Test addTitleData method (empty implementation)
        HotelResult hotelResult = new HotelResult();
        String countryCode = "IN";
        
        // This should not throw any exception
        transformer.addTitleData(hotelResult, countryCode);
        
        // Since the method is empty, we just verify it executes without error
        assertTrue("addTitleData should execute without error", true);
    }

    @Test
    public void testAddTitleData_withNullParams_doesNothing() {
        // Test addTitleData with null parameters
        transformer.addTitleData(null, null);
        
        // Since the method is empty, we just verify it executes without error
        assertTrue("addTitleData with null params should execute without error", true);
    }

    @Test
    public void testGetLuxeIcon_returnsCorrectIcon() {
        // Test getLuxeIcon method
        String result = transformer.getLuxeIcon();
        assertEquals("Should return LUXE_ICON_APPS constant", LUXE_ICON_APPS, result);
    }

    @Test
    public void testConvertStaffInfo_withNullStaffInfo_returnsNull() {
        // Test convertStaffInfo with null input
        StaffInfo result = transformer.convertStaffInfo(null);
        assertNull("Should return null for null staff info", result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostTrue_setsIconUrl() {
        // Test convertStaffInfo when isStarHost is true
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);
        
        // Mock the super.convertStaffInfo call by creating a spy
        OrchStaticDetailsResponseTransformerIOS transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was called
        verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostFalse_doesNotSetIconUrl() {
        // Test convertStaffInfo when isStarHost is false
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(false);
        
        // Mock the super.convertStaffInfo call
        OrchStaticDetailsResponseTransformerIOS transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was NOT called
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostNull_doesNotSetIconUrl() {
        // Test convertStaffInfo when isStarHost is null
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(null);
        
        // Mock the super.convertStaffInfo call
        OrchStaticDetailsResponseTransformerIOS transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was NOT called
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_callsRemoveIcon() {
        // Test that convertStaffInfo always calls removeIcon  
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);
        
        // Call the real method which should internally call removeIcon
        StaffInfo result = transformer.convertStaffInfo(orchestratorStaffInfo);
        
        // Since the implementation calls super.convertStaffInfo which returns the mocked value,
        // we verify by checking the starHostIconUrl is set correctly
        assertNotNull(result);
        verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
    }

    @Test
    public void testConvertStaffInfo_callsSuperConvertStaffInfo() {
        // Test that convertStaffInfo calls super.convertStaffInfo
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(false);
        
        OrchStaticDetailsResponseTransformerIOS transformerSpy = spy(transformer);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(any());
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(any());
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify super.convertStaffInfo was called once
        verify((OrchStaticDetailResponseTransformer) transformerSpy, times(1)).convertStaffInfo(orchestratorStaffInfo);
        assertEquals("Should return result from super method", clientGatewayStaffInfo, result);
    }

    @Test
    public void testStarHostIconAppValue() {
        // Test that the @Value injection works correctly
        String iconUrl = (String) ReflectionTestUtils.getField(transformer, "starHostIconApp");
        assertEquals("starHostIconApp should be set correctly", starHostIconApp, iconUrl);
    }

    @Test
    public void testConvertStaffInfo_edgeCaseScenarios() {
        // Test various edge case scenarios
        
        // Test with different boolean values for isStarHost
        Boolean[] starHostValues = {true, false, null};
        
        for (Boolean starHostValue : starHostValues) {
            // Reset the mock
            reset(orchestratorStaffInfo);
            lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(starHostValue);
            
            OrchStaticDetailsResponseTransformerIOS transformerSpy = spy(transformer);
            lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
            lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
            lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
            
            StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
            
            assertNotNull("Result should not be null for starHost value: " + starHostValue, result);
            
            if (Boolean.TRUE.equals(starHostValue)) {
                verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
            } else {
                verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
            }
        }
    }
} 