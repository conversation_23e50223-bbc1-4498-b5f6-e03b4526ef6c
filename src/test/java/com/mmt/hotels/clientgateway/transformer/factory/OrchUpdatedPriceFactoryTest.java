package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.OrchUpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.OrchUpdatedPriceResponseTransformer;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;

/**
 * Test class for OrchUpdatedPriceFactory in GI project
 * Tests the factory methods for getting orchestrator request and response transformers
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchUpdatedPriceFactoryTest {

    @InjectMocks
    OrchUpdatedPriceFactory orchUpdatedPriceFactory;

    @Mock
    private OrchUpdatedPriceRequestTransformer orchUpdatedPriceRequestTransformer;

    @Mock
    private OrchUpdatedPriceResponseTransformer orchUpdatedPriceResponseTransformer;

    @Before
    public void setUp() {
        // Set up the factory with mock transformers using ReflectionTestUtils
        ReflectionTestUtils.setField(orchUpdatedPriceFactory, "orchUpdatedPriceRequestTransformer", orchUpdatedPriceRequestTransformer);
        ReflectionTestUtils.setField(orchUpdatedPriceFactory, "orchUpdatedPriceResponseTransformer", orchUpdatedPriceResponseTransformer);
    }

    // ==================== getOrchRequestService() TESTS ====================

    @Test
    public void testGetOrchRequestService_PWA() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService("PWA");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void testGetOrchRequestService_DESKTOP() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService("DESKTOP");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void testGetOrchRequestService_ANDROID() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService("ANDROID");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void testGetOrchRequestService_IOS() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService("IOS");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void testGetOrchRequestService_EmptyString() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService("");
        
        // Then
        assertNotNull("Result should not be null for empty string", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void testGetOrchRequestService_NullClient() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(null);
        
        // Then
        assertNotNull("Result should not be null for null client", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void testGetOrchRequestService_UnknownClient() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService("UNKNOWN");
        
        // Then
        assertNotNull("Result should not be null for unknown client", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void testGetOrchRequestService_LowerCaseClient() {
        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService("pwa");
        
        // Then
        assertNotNull("Result should not be null for lowercase client", result);
        assertSame("Should return the same transformer instance for case sensitivity", orchUpdatedPriceRequestTransformer, result);
    }

    // ==================== getOrchResponseService() TESTS ====================

    @Test
    public void testGetOrchResponseService_PWA() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService("PWA");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void testGetOrchResponseService_DESKTOP() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService("DESKTOP");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void testGetOrchResponseService_ANDROID() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService("ANDROID");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void testGetOrchResponseService_IOS() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService("IOS");
        
        // Then
        assertNotNull("Result should not be null", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void testGetOrchResponseService_EmptyString() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService("");
        
        // Then
        assertNotNull("Result should not be null for empty string", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void testGetOrchResponseService_NullClient() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(null);
        
        // Then
        assertNotNull("Result should not be null for null client", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void testGetOrchResponseService_UnknownClient() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService("UNKNOWN");
        
        // Then
        assertNotNull("Result should not be null for unknown client", result);
        assertSame("Should return the same transformer instance", orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void testGetOrchResponseService_LowerCaseClient() {
        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService("desktop");
        
        // Then
        assertNotNull("Result should not be null for lowercase client", result);
        assertSame("Should return the same transformer instance for case sensitivity", orchUpdatedPriceResponseTransformer, result);
    }

    // ==================== EDGE CASE AND CONSISTENCY TESTS ====================

    @Test
    public void testConsistency_SameInstanceReturnedForSameClient() {
        // When
        OrchUpdatedPriceRequestTransformer req1 = orchUpdatedPriceFactory.getOrchRequestService("PWA");
        OrchUpdatedPriceRequestTransformer req2 = orchUpdatedPriceFactory.getOrchRequestService("PWA");
        OrchUpdatedPriceResponseTransformer resp1 = orchUpdatedPriceFactory.getOrchResponseService("PWA");
        OrchUpdatedPriceResponseTransformer resp2 = orchUpdatedPriceFactory.getOrchResponseService("PWA");
        
        // Then
        assertSame("Should return same request transformer instance for multiple calls", req1, req2);
        assertSame("Should return same response transformer instance for multiple calls", resp1, resp2);
    }

    @Test
    public void testAllClientsReturnSameTransformer() {
        // Given
        String[] clients = {"PWA", "DESKTOP", "ANDROID", "IOS", "", null, "UNKNOWN"};
        
        // When & Then
        for (String client : clients) {
            OrchUpdatedPriceRequestTransformer requestResult = orchUpdatedPriceFactory.getOrchRequestService(client);
            OrchUpdatedPriceResponseTransformer responseResult = orchUpdatedPriceFactory.getOrchResponseService(client);
            
            assertSame("All clients should return same request transformer", orchUpdatedPriceRequestTransformer, requestResult);
            assertSame("All clients should return same response transformer", orchUpdatedPriceResponseTransformer, responseResult);
        }
    }

    @Test
    public void testFactoryInstantiation() {
        // Test that the factory itself can be instantiated
        assertNotNull("OrchUpdatedPriceFactory should not be null", orchUpdatedPriceFactory);
    }

    @Test
    public void testWithBlankString() {
        // When
        OrchUpdatedPriceRequestTransformer requestResult = orchUpdatedPriceFactory.getOrchRequestService("   ");
        OrchUpdatedPriceResponseTransformer responseResult = orchUpdatedPriceFactory.getOrchResponseService("   ");
        
        // Then
        assertNotNull("Request transformer should not be null for blank string", requestResult);
        assertNotNull("Response transformer should not be null for blank string", responseResult);
        assertSame("Should return configured request transformer", orchUpdatedPriceRequestTransformer, requestResult);
        assertSame("Should return configured response transformer", orchUpdatedPriceResponseTransformer, responseResult);
    }

    @Test
    public void testWithSpecialCharacters() {
        // When
        OrchUpdatedPriceRequestTransformer requestResult = orchUpdatedPriceFactory.getOrchRequestService("PWA@#$");
        OrchUpdatedPriceResponseTransformer responseResult = orchUpdatedPriceFactory.getOrchResponseService("DESKTOP!@#");
        
        // Then
        assertNotNull("Request transformer should handle special characters", requestResult);
        assertNotNull("Response transformer should handle special characters", responseResult);
        assertSame("Should return configured request transformer", orchUpdatedPriceRequestTransformer, requestResult);
        assertSame("Should return configured response transformer", orchUpdatedPriceResponseTransformer, responseResult);
    }
} 