package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.LogicalErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.LogicalException;
import com.mmt.hotels.clientgateway.helpers.HermesHelper;
import com.mmt.hotels.clientgateway.response.hermes.HermesPriceApiResponse;
import com.mmt.hotels.clientgateway.response.rooms.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InjectHermesResponseTransformerTest {

    @InjectMocks
    private InjectHermesResponseTransformer injectHermesResponseTransformer;

    @Mock
    private HermesHelper hermesHelper;

    private SearchRoomsResponse searchRoomsResponse;
    private HermesPriceApiResponse hermesPriceApiResponse;
    private String uuid;
    private Map<String, String> expData;

    @Before
    public void setUp() {
        uuid = "test-uuid-123";
        expData = new HashMap<>();
        searchRoomsResponse = new SearchRoomsResponse();
        hermesPriceApiResponse = new HermesPriceApiResponse();
    }

    @Test
    public void testInjectHermesRatePlanData_SkipsWhenUnificationReviewV2IsTrue() throws ClientGatewayException {
        // Given
        expData.put("unificationReviewV2", "true");
        
        // When
        SearchRoomsResponse result = injectHermesResponseTransformer.injectHermesRatePlanData(
                searchRoomsResponse, hermesPriceApiResponse, uuid, expData);
        
        // Then
        assertEquals(searchRoomsResponse, result);
    }

    @Test
    public void testInjectHermesRatePlanData_SkipsWhenFlyerRatesLoggedOutExpIsTrue() throws ClientGatewayException {
        // Given
        expData.put(Constants.FlyerRatesLoggedOutExp, "true");
        
        // When
        SearchRoomsResponse result = injectHermesResponseTransformer.injectHermesRatePlanData(
                searchRoomsResponse, hermesPriceApiResponse, uuid, expData);
        
        // Then
        assertEquals(searchRoomsResponse, result);
    }

    @Test(expected = LogicalException.class)
    public void testInjectHermesRatePlanData_ExactRooms_NoRegularRoomsFromHermes() throws ClientGatewayException {
        // Given
        List<RoomDetails> exactRooms = createMockExactRooms();
        searchRoomsResponse.setExactRooms(exactRooms);
        
        // When & Then - Should throw exception when no hermes data
        injectHermesResponseTransformer.injectHermesRatePlanData(
                searchRoomsResponse, hermesPriceApiResponse, uuid, expData);
    }

    @Test(expected = LogicalException.class)
    public void testInjectHermesRatePlanData_NoCommonRecommendedAndOccupancyRooms() throws ClientGatewayException {
        // Given
        List<RecommendedCombo> recommendedCombos = createMockRecommendedCombos();
        List<RoomDetails> occupancyRooms = createMockOccupancyRooms();
        searchRoomsResponse.setRecommendedCombos(recommendedCombos);
        searchRoomsResponse.setOccupancyRooms(occupancyRooms);
        
        // When & Then - Should throw exception when no common rooms found
        injectHermesResponseTransformer.injectHermesRatePlanData(
                searchRoomsResponse, hermesPriceApiResponse, uuid, expData);
    }

    @Test
    public void testInjectHermesRatePlanData_NullHermesDataHandling() throws ClientGatewayException {
        // Given
        List<RoomDetails> exactRooms = createMockExactRooms();
        searchRoomsResponse.setExactRooms(exactRooms);
        
        // Hermes response with null data
        hermesPriceApiResponse.setData(null);
        
        // When & Then
        try {
            injectHermesResponseTransformer.injectHermesRatePlanData(
                    searchRoomsResponse, hermesPriceApiResponse, uuid, expData);
            fail("Expected LogicalException to be thrown");
        } catch (LogicalException e) {
            assertEquals(LogicalErrors.NO_COMMON_RATE_PLAN.getErrorCode(), e.getCode());
        }
    }

    // Helper methods to create mock data

    private List<RoomDetails> createMockExactRooms() {
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("ROOM001");
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setRpc("RATE001");
        
        List<Tariff> tariffs = new ArrayList<>();
        Tariff tariff = new Tariff();
        tariff.setTariffCode("RATE001");
        tariffs.add(tariff);
        ratePlan.setTariffs(tariffs);
        
        ratePlans.add(ratePlan);
        roomDetails.setRatePlans(ratePlans);
        exactRooms.add(roomDetails);
        
        return exactRooms;
    }

    private List<RecommendedCombo> createMockRecommendedCombos() {
        List<RecommendedCombo> recommendedCombos = new ArrayList<>();
        RecommendedCombo combo = new RecommendedCombo();
        combo.setComboMealPlan("MEAL001");
        
        List<RoomDetails> rooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("ROOM001");
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setRpc("RATE001");
        ratePlan.setPayMode("PAY001");
        ratePlan.setSegmentId("SEG001");
        ratePlan.setSupplierCode("SUP001");
        
        List<Tariff> tariffs = new ArrayList<>();
        Tariff tariff = new Tariff();
        tariff.setTariffCode("RATE001");
        tariffs.add(tariff);
        ratePlan.setTariffs(tariffs);
        
        ratePlans.add(ratePlan);
        roomDetails.setRatePlans(ratePlans);
        rooms.add(roomDetails);
        combo.setRooms(rooms);
        
        Tariff comboTariff = new Tariff();
        combo.setComboTariff(comboTariff);
        
        recommendedCombos.add(combo);
        return recommendedCombos;
    }

    private List<RoomDetails> createMockOccupancyRooms() {
        List<RoomDetails> occupancyRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("ROOM001");
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setRpc("RATE001");
        
        List<Tariff> tariffs = new ArrayList<>();
        Tariff tariff = new Tariff();
        tariff.setTariffCode("RATE001_PAX2");
        tariffs.add(tariff);
        ratePlan.setTariffs(tariffs);
        
        ratePlans.add(ratePlan);
        roomDetails.setRatePlans(ratePlans);
        occupancyRooms.add(roomDetails);
        
        return occupancyRooms;
    }
} 