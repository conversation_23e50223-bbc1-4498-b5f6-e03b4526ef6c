package com.mmt.hotels.clientgateway.transformer.request;

import com.gommt.hotels.orchestrator.detail.enums.*;
import com.gommt.hotels.orchestrator.detail.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.detail.model.objects.ExtraInfo;
import com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomCriteria;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails;
import com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.FilterDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.state.ChatbotDetails;
import com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags;
import com.gommt.hotels.orchestrator.detail.model.state.ImageDetails;

import com.gommt.hotels.orchestrator.detail.model.state.UserDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomStayCandidate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OrchUpdatedPriceRequestTransformerTest {

    @InjectMocks
    private OrchUpdatedPriceRequestTransformer transformer;

    @Mock
    private Utility utility;

    private UpdatePriceRequest updatePriceRequest;
    private CommonModifierResponse commonModifierResponse;

    @BeforeEach
    void setUp() {
        updatePriceRequest = createUpdatePriceRequest();
        commonModifierResponse = createCommonModifierResponse();
    }

    // =================================================================
    // MAIN PUBLIC METHOD TESTS
    // =================================================================

    @Test
    @DisplayName("Test buildUpdatePriceRequest - Success")
    void testBuildUpdatePriceRequest_Success() {
        // Arrange
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);
        when(utility.isMyPartner(any())).thenReturn(false);

        // Act
        DetailRequest result = transformer.buildUpdatePriceRequest(updatePriceRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertEquals("hotel123", result.getHotelId());
        assertEquals("2023-12-01", result.getCheckIn());
        assertEquals("2023-12-05", result.getCheckOut());
        assertNotNull(result.getLocation());
        assertNotNull(result.getRooms());
        assertNotNull(result.getExtraInfo());
        assertNotNull(result.getClientDetails());
        assertNotNull(result.getFilters());
        assertNotNull(result.getImageDetails());
        assertEquals("test-exp-data", result.getExperimentData());
    }

    @Test
    @DisplayName("Test buildUpdatePriceRequest - Null UpdatePriceRequest")
    void testBuildUpdatePriceRequest_NullUpdatePriceRequest() {
        // Act & Assert
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                transformer.buildUpdatePriceRequest(null, commonModifierResponse));
        assertEquals("UpdatePriceRequest cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildUpdatePriceRequest - Null CommonModifierResponse")
    void testBuildUpdatePriceRequest_NullCommonModifierResponse() {
        // Act & Assert
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                transformer.buildUpdatePriceRequest(updatePriceRequest, null));
        assertEquals("CommonModifierResponse cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildUpdatePriceRequest - Null SearchCriteria")
    void testBuildUpdatePriceRequest_NullSearchCriteria() {
        // Arrange
        updatePriceRequest.setSearchCriteria(null);

        // Act & Assert
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                transformer.buildUpdatePriceRequest(updatePriceRequest, commonModifierResponse));
        assertEquals("SearchCriteria cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildUpdatePriceRequest - With Coupon Count")
    void testBuildUpdatePriceRequest_WithCouponCount() {
        // Arrange
        updatePriceRequest.getRequestDetails().setCouponCount(3);
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);
        when(utility.isMyPartner(any())).thenReturn(false);

        // Act
        DetailRequest result = transformer.buildUpdatePriceRequest(updatePriceRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.getCouponCount());
    }

    // =================================================================
    // PRIVATE METHOD TESTS (using reflection)
    // =================================================================

    @Test
    @DisplayName("Test buildUpdatePriceLocationDetails")
    void testBuildUpdatePriceLocationDetails() throws Exception {
        // Use reflection to test private method
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUpdatePriceLocationDetails", UpdatePriceRequest.class);
        method.setAccessible(true);

        LocationDetails result = (LocationDetails) method.invoke(transformer, updatePriceRequest);

        assertNotNull(result);
        assertEquals("BLR", result.getCityId());
        assertEquals("IN", result.getCountryId());
        assertEquals("loc123", result.getId());
        assertEquals("city", result.getType());
    }

    @Test
    @DisplayName("Test buildUpdatePriceLocationDetails - Null SearchCriteria")
    void testBuildUpdatePriceLocationDetails_NullSearchCriteria() throws Exception {
        // Arrange
        updatePriceRequest.setSearchCriteria(null);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUpdatePriceLocationDetails", UpdatePriceRequest.class);
        method.setAccessible(true);

        LocationDetails result = (LocationDetails) method.invoke(transformer, updatePriceRequest);

        assertNotNull(result);
        assertNull(result.getCityId());
        assertNull(result.getCountryId());
    }

    @Test
    @DisplayName("Test buildUpdatePriceRoomDetails")
    void testBuildUpdatePriceRoomDetails() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUpdatePriceRoomDetails", UpdatePriceRequest.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(transformer, updatePriceRequest);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).getAdults());
        assertEquals(Arrays.asList(5, 8), result.get(0).getChildrenAges());
    }

    @Test
    @DisplayName("Test buildUpdatePriceRoomDetails - Null RoomCriteria")
    void testBuildUpdatePriceRoomDetails_NullRoomCriteria() throws Exception {
        // Arrange
        updatePriceRequest.getSearchCriteria().setRoomCriteria(null);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUpdatePriceRoomDetails", UpdatePriceRequest.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(transformer, updatePriceRequest);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test buildExtraInfo - With SearchType")
    void testBuildExtraInfo_WithSearchType() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildExtraInfo", String.class);
        method.setAccessible(true);

        ExtraInfo result = (ExtraInfo) method.invoke(transformer, "NORMAL");

        assertNotNull(result);
        assertEquals("NORMAL", result.getSearchType());
    }

    @Test
    @DisplayName("Test buildExtraInfo - Null SearchType")
    void testBuildExtraInfo_NullSearchType() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildExtraInfo", String.class);
        method.setAccessible(true);

        ExtraInfo result = (ExtraInfo) method.invoke(transformer, (Object) null);

        assertNotNull(result);
        assertNull(result.getSearchType());
    }

    @Test
    @DisplayName("Test buildClientDetailsForUpdatePrice")
    void testBuildClientDetailsForUpdatePrice() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildClientDetailsForUpdatePrice", UpdatePriceRequest.class, CommonModifierResponse.class);
        method.setAccessible(true);

        ClientDetails result = (ClientDetails) method.invoke(transformer, updatePriceRequest, commonModifierResponse);

        assertNotNull(result);
        assertEquals("visitor123", result.getVisitorId());
        assertEquals("mc123", result.getMcId());
        assertNotNull(result.getFeatureFlags());
        assertNotNull(result.getRequestDetails());
        assertNotNull(result.getUserDetails());
    }

    @Test
    @DisplayName("Test buildUpdatePriceFilterDetails")
    void testBuildUpdatePriceFilterDetails() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUpdatePriceFilterDetails", UpdatePriceRequest.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<FilterDetails> result = (List<FilterDetails>) method.invoke(transformer, updatePriceRequest);

        assertNotNull(result);
        assertTrue(result.isEmpty()); // Update price typically doesn't have filters
    }

    @Test
    @DisplayName("Test buildUpdatePriceFilterDetails - Null Request")
    void testBuildUpdatePriceFilterDetails_NullRequest() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUpdatePriceFilterDetails", UpdatePriceRequest.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<FilterDetails> result = (List<FilterDetails>) method.invoke(transformer, (Object) null);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test buildUpdatePriceImageDetails")
    void testBuildUpdatePriceImageDetails() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUpdatePriceImageDetails", UpdatePriceRequest.class);
        method.setAccessible(true);

        ImageDetails result = (ImageDetails) method.invoke(transformer, updatePriceRequest);

        assertNotNull(result);
        assertTrue(result.getCategories().isEmpty());
        assertTrue(result.getTypes().isEmpty());
    }

    @Test
    @DisplayName("Test buildFeatureFlagsForUpdatePrice")
    void testBuildFeatureFlagsForUpdatePrice() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildFeatureFlagsForUpdatePrice", UpdatePriceRequest.class, CommonModifierResponse.class);
        method.setAccessible(true);

        FeatureFlags result = (FeatureFlags) method.invoke(transformer, updatePriceRequest, commonModifierResponse);

        assertNotNull(result);
        assertTrue(result.isWalletRequired());
        assertTrue(result.isBestCoupon());
        assertTrue(result.isComparatorHotelRequest());
        assertTrue(result.isCheckAvailability());
        assertFalse(result.isBookingModification());
    }

    @Test
    @DisplayName("Test buildFeatureFlagsForUpdatePrice - Null FeatureFlags")
    void testBuildFeatureFlagsForUpdatePrice_NullFeatureFlags() throws Exception {
        // Arrange
        updatePriceRequest.setFeatureFlags(null);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildFeatureFlagsForUpdatePrice", UpdatePriceRequest.class, CommonModifierResponse.class);
        method.setAccessible(true);

        FeatureFlags result = (FeatureFlags) method.invoke(transformer, updatePriceRequest, commonModifierResponse);

        assertNotNull(result);
        assertFalse(result.isWalletRequired());
        assertFalse(result.isBestCoupon());
        assertFalse(result.isComparatorHotelRequest());
        assertFalse(result.isCheckAvailability());
        assertFalse(result.isBookingModification());
    }

    @Test
    @DisplayName("Test buildRequestDetailsForUpdatePrice")
    void testBuildRequestDetailsForUpdatePrice() throws Exception {
        // Arrange
        when(utility.isMyPartner(any())).thenReturn(false);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildRequestDetailsForUpdatePrice", UpdatePriceRequest.class, CommonModifierResponse.class);
        method.setAccessible(true);

        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = (com.gommt.hotels.orchestrator.detail.model.state.RequestDetails) method.invoke(transformer, updatePriceRequest, commonModifierResponse);

        assertNotNull(result);
        assertEquals("B2CAgent", result.getRequestType());
        assertEquals(Currency.INR, result.getCurrency());
        assertEquals("PAY", result.getPayMode());
        assertEquals(Funnel.HOTELS, result.getFunnelSource());
        assertEquals(PageContext.DETAIL, result.getPageContext());
        assertEquals(TrafficType.B2C, result.getTrafficType());
        assertEquals(Brand.MMT, result.getBrand());
        assertEquals(IdContext.B2C, result.getIdContext());
        assertEquals(SiteDomain.IN, result.getSiteDomain());
        assertEquals(Language.ENGLISH, result.getLanguage());
        assertEquals(Region.IN, result.getRegion());
    }

    @Test
    @DisplayName("Test buildRequestDetailsForUpdatePrice - MyPartner Traffic")
    void testBuildRequestDetailsForUpdatePrice_MyPartnerTraffic() throws Exception {
        // Arrange
        when(utility.isMyPartner(any())).thenReturn(true);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildRequestDetailsForUpdatePrice", UpdatePriceRequest.class, CommonModifierResponse.class);
        method.setAccessible(true);

        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = (com.gommt.hotels.orchestrator.detail.model.state.RequestDetails) method.invoke(transformer, updatePriceRequest, commonModifierResponse);

        assertNotNull(result);
        assertEquals(TrafficType.MYPARTNER, result.getTrafficType());
    }

    @Test
    @DisplayName("Test buildUserDetailsForUpdatePrice")
    void testBuildUserDetailsForUpdatePrice() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUserDetailsForUpdatePrice", UpdatePriceRequest.class, CommonModifierResponse.class);
        method.setAccessible(true);

        UserDetails result = (UserDetails) method.invoke(transformer, updatePriceRequest, commonModifierResponse);

        assertNotNull(result);
        assertEquals("user123", result.getUuid());
        assertTrue(result.isLoggedIn());
        assertEquals(ProfileType.BUSINESS, result.getProfileType());
        assertEquals("auth123", result.getMmtAuth());
    }

    @Test
    @DisplayName("Test buildUserDetailsForUpdatePrice - Null ExtendedUser")
    void testBuildUserDetailsForUpdatePrice_NullExtendedUser() throws Exception {
        // Arrange
        commonModifierResponse.setExtendedUser(null);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildUserDetailsForUpdatePrice", UpdatePriceRequest.class, CommonModifierResponse.class);
        method.setAccessible(true);

        UserDetails result = (UserDetails) method.invoke(transformer, updatePriceRequest, commonModifierResponse);

        assertNotNull(result);
        assertNull(result.getUuid());
        assertFalse(result.isLoggedIn());
        assertEquals("auth123", result.getMmtAuth());
    }

    @Test
    @DisplayName("Test buildChatbotDetailsForUpdatePrice - With MyraMsgId")
    void testBuildChatbotDetailsForUpdatePrice_WithMyraMsgId() throws Exception {
        // Arrange
        updatePriceRequest.getRequestDetails().setMyraMsgId("myra123");
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildChatbotDetailsForUpdatePrice", UpdatePriceRequest.class);
        method.setAccessible(true);

        ChatbotDetails result = (ChatbotDetails) method.invoke(transformer, updatePriceRequest);

        assertNotNull(result);
        assertEquals("myra123", result.getMyraMsgId());
    }

    @Test
    @DisplayName("Test buildChatbotDetailsForUpdatePrice - Null MyraMsgId")
    void testBuildChatbotDetailsForUpdatePrice_NullMyraMsgId() throws Exception {
        // Arrange
        updatePriceRequest.getRequestDetails().setMyraMsgId(null);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildChatbotDetailsForUpdatePrice", UpdatePriceRequest.class);
        method.setAccessible(true);

        ChatbotDetails result = (ChatbotDetails) method.invoke(transformer, updatePriceRequest);

        assertNull(result);
    }

    @Test
    @DisplayName("Test buildBookingDevice")
    void testBuildBookingDevice() throws Exception {
        // Arrange
        DeviceDetails deviceDetails = createDeviceDetails();
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildBookingDevice", DeviceDetails.class);
        method.setAccessible(true);

        BookingDevice result = (BookingDevice) method.invoke(transformer, deviceDetails);

        assertNotNull(result);
        assertEquals("device123", result.getDeviceId());
        assertEquals("Android Device", result.getDeviceName());
        assertEquals(DeviceType.MOBILE, result.getDeviceType());
        assertEquals("1.0.0", result.getAppVersion());
        assertEquals("WIFI", result.getNetworkType());
    }

    @Test
    @DisplayName("Test convertToOrchRoomCriteria")
    void testConvertToOrchRoomCriteria() throws Exception {
        // Arrange
        List<UpdatedPriceRoomCriteria> roomCriteria = createUpdatedPriceRoomCriteria();
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("convertToOrchRoomCriteria", List.class, UpdatePriceRequest.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomCriteria> result = (List<RoomCriteria>) method.invoke(transformer, roomCriteria, updatePriceRequest);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ROOM123", result.get(0).getRoomCode());
        assertEquals("RP123", result.get(0).getRatePlanCode());
        assertEquals("mt123", result.get(0).getMtKey());
        assertEquals("pricing123", result.get(0).getPricingKey());
        assertEquals("SUP123", result.get(0).getSupplierCode());
        assertEquals(1, result.get(0).getCount());
        assertNotNull(result.get(0).getRooms());
    }

    @Test
    @DisplayName("Test convertToOrchRoomCriteria - Null Input")
    void testConvertToOrchRoomCriteria_NullInput() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("convertToOrchRoomCriteria", List.class, UpdatePriceRequest.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomCriteria> result = (List<RoomCriteria>) method.invoke(transformer, null, updatePriceRequest);

        assertNull(result);
    }

    @Test
    @DisplayName("Test convertToOrchRoomCriteria - Empty List")
    void testConvertToOrchRoomCriteria_EmptyList() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("convertToOrchRoomCriteria", List.class, UpdatePriceRequest.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomCriteria> result = (List<RoomCriteria>) method.invoke(transformer, Collections.emptyList(), updatePriceRequest);

        assertNull(result);
    }

    @Test
    @DisplayName("Test buildRoomDetails - Normal Processing")
    void testBuildRoomDetails_NormalProcessing() throws Exception {
        // Arrange
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates = createRoomStayCandidates();
        Map<String, String> expDataMap = new HashMap<>();
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(transformer, roomStayCandidates, expDataMap);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).getAdults());
        assertEquals(Arrays.asList(5, 8), result.get(0).getChildrenAges());
    }

    @Test
    @DisplayName("Test buildRoomDetails - With Room Distribution")
    void testBuildRoomDetails_WithRoomDistribution() throws Exception {
        // Arrange
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates = createRoomStayCandidates();
        Map<String, String> expDataMap = new HashMap<>();
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(true);
        when(utility.buildRoomStayDistribution(any(), any())).thenReturn(createDistributedRoomStayCandidates());
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(transformer, roomStayCandidates, expDataMap);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).getAdults());
    }

    @Test
    @DisplayName("Test buildRoomDetails - Null RoomStayCandidates")
    void testBuildRoomDetails_NullRoomStayCandidates() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(transformer, null, new HashMap<>());

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test buildRoomDetails - Empty RoomStayCandidates")
    void testBuildRoomDetails_EmptyRoomStayCandidates() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(transformer, Collections.emptyList(), new HashMap<>());

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test buildRoomDetails - With Null Candidate in List")
    void testBuildRoomDetails_WithNullCandidateInList() throws Exception {
        // Arrange
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        roomStayCandidates.add(null);
        roomStayCandidates.add(createRoomStayCandidates().get(0));
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(transformer, roomStayCandidates, new HashMap<>());

        assertNotNull(result);
        assertEquals(1, result.size()); // Only non-null candidate should be processed
    }

    @Test
    @DisplayName("Test buildMultiCurrencyInfo")
    void testBuildMultiCurrencyInfo() throws Exception {
        // Arrange
        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo inputMultiCurrencyInfo = 
            new com.mmt.hotels.clientgateway.request.MultiCurrencyInfo();
        inputMultiCurrencyInfo.setUserCurrency("USD");
        inputMultiCurrencyInfo.setRegionCurrency("INR");
        
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildMultiCurrencyInfo", com.mmt.hotels.clientgateway.request.MultiCurrencyInfo.class);
        method.setAccessible(true);

        MultiCurrencyInfo result = (MultiCurrencyInfo) method.invoke(transformer, inputMultiCurrencyInfo);

        assertNotNull(result);
        assertEquals("USD", result.getUserCurrency());
        assertEquals("INR", result.getRegionCurrency());
    }

    @Test
    @DisplayName("Test buildMultiCurrencyInfo - Null Input")
    void testBuildMultiCurrencyInfo_NullInput() throws Exception {
        Method method = OrchUpdatedPriceRequestTransformer.class.getDeclaredMethod("buildMultiCurrencyInfo", com.mmt.hotels.clientgateway.request.MultiCurrencyInfo.class);
        method.setAccessible(true);

        MultiCurrencyInfo result = (MultiCurrencyInfo) method.invoke(transformer, (Object) null);

        assertNull(result);
    }

    @Test
    @DisplayName("Test buildUserGlobalInfo - Public Method")
    void testBuildUserGlobalInfo() {
        // Arrange
        com.mmt.hotels.clientgateway.request.UserGlobalInfo userGlobalInfo = 
            new com.mmt.hotels.clientgateway.request.UserGlobalInfo();
        userGlobalInfo.setUserCountry("IN");
        userGlobalInfo.setEntityName("MMT");

        // Act
        UserGlobalInfo result = transformer.buildUserGlobalInfo(userGlobalInfo);

        // Assert
        assertNotNull(result);
        assertEquals("IN", result.getUserCountry());
        assertEquals("MMT", result.getEntityName());
    }

    // =================================================================
    // TEST DATA HELPER METHODS
    // =================================================================

    private UpdatePriceRequest createUpdatePriceRequest() {
        UpdatePriceRequest request = new UpdatePriceRequest();
        request.setClient("android");
        request.setRequestDetails(createRequestDetails());
        request.setDeviceDetails(createDeviceDetails());
        request.setFeatureFlags(createFeatureFlags());
        request.setExpData("test-exp-data");
        request.setExpDataMap(new HashMap<>());

        UpdatedPriceCriteria criteria = new UpdatedPriceCriteria();
        criteria.setHotelId("hotel123");
        criteria.setCheckIn("2023-12-01");
        criteria.setCheckOut("2023-12-05");
        criteria.setCurrency("INR");
        criteria.setCityCode("BLR");
        criteria.setCountryCode("IN");
        criteria.setLocationId("loc123");
        criteria.setLocationType("city");
        criteria.setSearchType("NORMAL");
        criteria.setRoomCriteria(createUpdatedPriceRoomCriteria());
        criteria.setRoomStayCandidates(createRoomStayCandidates());
        
        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfo = 
            new com.mmt.hotels.clientgateway.request.MultiCurrencyInfo();
        multiCurrencyInfo.setUserCurrency("USD");
        multiCurrencyInfo.setRegionCurrency("INR");
        criteria.setMultiCurrencyInfo(multiCurrencyInfo);
        
        com.mmt.hotels.clientgateway.request.UserGlobalInfo userGlobalInfo = 
            new com.mmt.hotels.clientgateway.request.UserGlobalInfo();
        userGlobalInfo.setUserCountry("IN");
        userGlobalInfo.setEntityName("MMT");
        criteria.setUserGlobalInfo(userGlobalInfo);

        request.setSearchCriteria(criteria);
        return request;
    }

    private List<UpdatedPriceRoomCriteria> createUpdatedPriceRoomCriteria() {
        List<UpdatedPriceRoomCriteria> roomCriteria = new ArrayList<>();
        UpdatedPriceRoomCriteria criteria = new UpdatedPriceRoomCriteria();
        criteria.setRoomCode("ROOM123");
        criteria.setRatePlanCode("RP123");
        criteria.setMtKey("mt123");
        criteria.setPricingKey("pricing123");
        criteria.setSupplierCode("SUP123");
        criteria.setRoomStayCandidates(createRoomStayCandidates());
        roomCriteria.add(criteria);
        return roomCriteria;
    }

    private List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> createRoomStayCandidates() {
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> candidates = new ArrayList<>();
        com.mmt.hotels.clientgateway.request.RoomStayCandidate candidate = 
            new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(Arrays.asList(5, 8));
        candidates.add(candidate);
        return candidates;
    }

    private List<RoomStayCandidate> createDistributedRoomStayCandidates() {
        List<RoomStayCandidate> candidates = new ArrayList<>();
        RoomStayCandidate candidate = new RoomStayCandidate();
        
        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount adultGuest = new GuestCount();
        adultGuest.setCount("2");
        adultGuest.setAges(Arrays.asList(5, 8));
        guestCounts.add(adultGuest);
        
        candidate.setGuestCounts(guestCounts);
        candidates.add(candidate);
        return candidates;
    }

    private RequestDetails createRequestDetails() {
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setRequestId("req123");
        requestDetails.setJourneyId("journey123");
        requestDetails.setVisitorId("visitor123");
        requestDetails.setChannel("MOBILE");
        requestDetails.setSessionId("session123");
        requestDetails.setFunnelSource("HOTELS");
        requestDetails.setPageContext("DETAIL");
        requestDetails.setBrand("MMT");
        requestDetails.setIdContext("B2C");
        requestDetails.setSiteDomain("IN");
        requestDetails.setRequestor("user123");
        requestDetails.setLoggedIn(true);
        requestDetails.setMyraMsgId("myra123");
        requestDetails.setPayMode("PAY");
        requestDetails.setCouponCount(3);
        
        com.mmt.hotels.clientgateway.request.TrafficSource trafficSource = new com.mmt.hotels.clientgateway.request.TrafficSource();
        trafficSource.setSource("DIRECT");
        trafficSource.setType("B2C");
        trafficSource.setFlowType("DIRECT");
        requestDetails.setTrafficSource(trafficSource);
        
        return requestDetails;
    }

    private DeviceDetails createDeviceDetails() {
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceId("device123");
        deviceDetails.setDeviceName("Android Device");
        deviceDetails.setBookingDevice("MOBILE");
        deviceDetails.setAppVersion("1.0.0");
        deviceDetails.setNetworkType("WIFI");
        return deviceDetails;
    }

    private com.mmt.hotels.clientgateway.request.FeatureFlags createFeatureFlags() {
        com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags = 
            new com.mmt.hotels.clientgateway.request.FeatureFlags();
        featureFlags.setWalletRequired(true);
        featureFlags.setCoupon(true);
        featureFlags.setComparator(true);
        featureFlags.setCheckAvailability(true);
        return featureFlags;
    }

    private CommonModifierResponse createCommonModifierResponse() {
        CommonModifierResponse response = new CommonModifierResponse();
        response.setMcId("mc123");
        response.setMmtAuth("auth123");
        response.setApplicationId(1);
        response.setAffiliateId("affiliate123");
        response.setCdfContextId("cdf123");
        
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("user123");
        extendedUser.setProfileType("BUSINESS");
        extendedUser.setAffiliateId("affiliate123");
        response.setExtendedUser(extendedUser);
        
        response.setManthanExpDataMap(new HashMap<>());
        return response;
    }
}