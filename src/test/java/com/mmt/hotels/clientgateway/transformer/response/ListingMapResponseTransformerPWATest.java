package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ListingMapResponseTransformerPWA;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.RatingSummaryDTO;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.searchwrapper.GeoLocation;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SoldOutInfo;
import com.mmt.hotels.model.response.staticdata.Address;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import com.mmt.hotels.pojo.response.ListingHotelMapEntity;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class ListingMapResponseTransformerPWATest {

    @InjectMocks
    ListingMapResponseTransformerPWA listingMapResponseTransformerPWA;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

//    @Test
//    public void testConvertListingMapResponse() throws JsonProcessingException {
//
//        HotelListingMapResponse hotelListingMapResponse = new HotelListingMapResponse();
//        hotelListingMapResponse.setCityCode("DEL");
//        hotelListingMapResponse.setCityName("Delhi");
//        hotelListingMapResponse.setCorrelationKey("12345678");
//        hotelListingMapResponse.setCountryCode("IN");
//        hotelListingMapResponse.setCountryName("IN");
//        hotelListingMapResponse.setLastFetchedHotelId("1234");
//        hotelListingMapResponse.setNoMoreAvailableHotels(true);
////        hotelListingMapResponse.setResponseErrors(new ResponseErrors());
//        hotelListingMapResponse.setStaticHotelCounts(5);
//        hotelListingMapResponse.setTotalHotelCounts(5);
//        hotelListingMapResponse.setHotelList(new ArrayList<>());
//        hotelListingMapResponse.getHotelList().add(new ListingHotelMapEntity());
//        hotelListingMapResponse.getHotelList().get(0).setAddress(new Address());
//        hotelListingMapResponse.getHotelList().get(0).getAddress().setArea(new ArrayList<String>());
//        hotelListingMapResponse.getHotelList().get(0).getAddress().getArea().add("area");
//        hotelListingMapResponse.getHotelList().get(0).getAddress().setLine1("abcd");
//        hotelListingMapResponse.getHotelList().get(0).getAddress().setLine2("abcd");
//
//        hotelListingMapResponse.getHotelList().get(0).setCityCode("1234");
//        hotelListingMapResponse.getHotelList().get(0).setDisplayFare(new DisplayFare());
//        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().setDisplayPriceBreakDown(new DisplayPriceBreakDown());
//        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setDisplayPrice(12d);
//        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setNonDiscountedPrice(12d);
//        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setCouponInfo(new BestCoupon());
//        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setEmiDetails(new Emi());
//        hotelListingMapResponse.getHotelList().get(0).setSoldOutInfo(new SoldOutInfo());
//
//        hotelListingMapResponse.getHotelList().get(0).setGeoLocation(new GeoLocation());
//        hotelListingMapResponse.getHotelList().get(0).getGeoLocation().setDistanceMeter(10d);
//        hotelListingMapResponse.getHotelList().get(0).getGeoLocation().setLatitude("11");
//        hotelListingMapResponse.getHotelList().get(0).getGeoLocation().setLongitude("12");
//
//        hotelListingMapResponse.getHotelList().get(0).setId("id");
//        hotelListingMapResponse.getHotelList().get(0).setMainImages(new ArrayList<String>());
//        hotelListingMapResponse.getHotelList().get(0).getMainImages().add("http://mainImages.com");
//        hotelListingMapResponse.getHotelList().get(0).setName("hotel");
//        hotelListingMapResponse.getHotelList().get(0).setStarRating(3);
//        hotelListingMapResponse.getHotelList().get(0).setIsSoldOut(false);
//        hotelListingMapResponse.getHotelList().get(0).setFlyfishReviewSummary(new HashMap<OTA, JsonNode>());
//        RatingSummaryDTO ratingSummaryDTO = new RatingSummaryDTO();
//        ratingSummaryDTO.setCumulativeRating(new Float(4));
//        ratingSummaryDTO.setTotalRatingCount(10);
//        ratingSummaryDTO.setTotalReviewsCount(7);
//        String ratingJson = "{\n" +
//                "  \"cumulativeRating\": 4.5,\n" +
//                "  \"totalReviewsCount\": 89,\n" +
//                "  \"totalRatingCount\": 162,\n" +
//                "  \"best\": [\n" +
//                "    {\n" +
//                "      \"title\": \"\"\n" +
//                "    }\n" +
//                "  ],\n" +
//                "  \"sortingCriterion\": [\n" +
//                "    \"Latest first\",\n" +
//                "    \"Helpful first\",\n" +
//                "    \"Positive first\",\n" +
//                "    \"Negative first\"\n" +
//                "  ]\n" +
//                "}";
//
//        hotelListingMapResponse.getHotelList().get(0).getFlyfishReviewSummary().put(OTA.MMT, new ObjectMapper().readTree(ratingJson));
//        ListingMapResponse listingMapResponse = listingMapResponseTransformerPWA.convertListingMapResponse(hotelListingMapResponse, Mockito.anyString(),null, null);
//        Assert.assertNotNull(listingMapResponse);
//
//    }

    @Test
    public void addLocationPersuasionToHotelPersuasionsTest(){
        List<String> locations = new ArrayList<>();
        locations.add("This is test location persuasion");
        Hotel hotel = new Hotel();
        hotel.setHotelPersuasions(new HashMap<>());
        listingMapResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations,"HOTELS");
        Assert.assertNotNull(hotel.getHotelPersuasions());
        Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),1);

        locations.add("one more test persuasion");
        hotel.setHotelPersuasions(new HashMap<>());
        listingMapResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations,"HOTELS");
        Assert.assertNotNull(hotel.getHotelPersuasions());
        Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),1);

        //Generate a test where funnelSource = "HOURLY" and location size is 1
        locations = new ArrayList<>();
        locations.add("This is test location persuasion");
        hotel.setHotelPersuasions(new HashMap<>());
        listingMapResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations,"HOURLY");
        Assert.assertNotNull(hotel.getHotelPersuasions());
        Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),1);
    }

}
