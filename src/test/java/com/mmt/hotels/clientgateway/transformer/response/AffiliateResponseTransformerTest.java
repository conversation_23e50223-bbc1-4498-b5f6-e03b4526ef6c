package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.model.affiliate.CreateQuoteResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class AffiliateResponseTransformerTest {
    @InjectMocks
    AffiliateResponseTransformer affiliateResponseTransformer;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Test
    public void testNullInputHandling() {
        // Mock the commonResponseTransformer to return empty list for null input
        Mockito.when(commonResponseTransformer.buildAffiliateFeeDetails(Mockito.any())).thenReturn(new ArrayList<>());
        
        // Create a minimal valid response object
        UpdateAffiliateFeeResponse response = new UpdateAffiliateFeeResponse.Builder().build();
            
        UpdatedAffiliateFeeResponse result = affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(response);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getTotalpricing());
    }

    @Test
    public void convertAffiliateFeeUpdateResponseTest() {
        Mockito.when(commonResponseTransformer.buildAffiliateFeeDetails(Mockito.any())).thenReturn(new ArrayList<>());
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(new UpdateAffiliateFeeResponse.Builder().build());
        Assert.assertNotNull(updatedAffiliateFeeResponse);
    }

    @Test
    public void convertAffiliateCreateQuoteResponseTest() {
        CreateQuoteResponse createQuoteResponse = new CreateQuoteResponse.Builder().buildId("id").build();
        Assert.assertNotNull(affiliateResponseTransformer.convertAffiliateCreateQuoteResponse(createQuoteResponse));
    }
}
