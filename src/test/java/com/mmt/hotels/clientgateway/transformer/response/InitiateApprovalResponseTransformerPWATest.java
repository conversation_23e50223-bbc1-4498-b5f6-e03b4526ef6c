package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.corporate.UpdatePolicyResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.transformer.response.pwa.InitiateApprovalResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class InitiateApprovalResponseTransformerPWATest {

    @Mock
    CommonHelper commonHelper;

    @Mock
    CommonResponseTransformer commonResponseTransformer;


    @InjectMocks
    InitiateApprovalResponseTransformerPWA initiateApprovalResponseTransformerPWA;

    @Test
    public void testConvertInitiateApprovalResponse() {


        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setResponseErrors(new ErrorResponse());
        cgServerResponse.getResponseErrors().setErrorList(new ArrayList<>());
        cgServerResponse.getResponseErrors().getErrorList().add(new GenericErrorEntity("500", "Unexpected error"));


        InitApprovalResponse initApprovalResponse = initiateApprovalResponseTransformerPWA.processResponse(cgServerResponse);
        Assert.assertNotNull(initApprovalResponse);

    }
}