package com.mmt.hotels.clientgateway.transformer.response;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.response.SearchAddonsResponse;
import com.mmt.hotels.model.response.addon.AddOnEntity;

@RunWith(MockitoJUnitRunner.class)
public class AddonsReponseTransformerTest {

	@InjectMocks
	AddonsReponseTransformer addonsReponseTransformer;
	
	@Spy
	CommonResponseTransformer commonResponseTransformer;
	
	@Test
	public void testConvertSearchAddonsResponse(){
		AddOnEntity getAddonsResponse = new AddOnEntity();
		SearchAddonsResponse resp = addonsReponseTransformer.convertSearchAddonsResponse(getAddonsResponse );
		Assert.assertNotNull(resp);
	
	}
	
}
