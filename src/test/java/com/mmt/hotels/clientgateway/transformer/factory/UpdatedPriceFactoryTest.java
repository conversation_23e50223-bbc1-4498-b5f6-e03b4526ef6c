package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.UpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatedPriceResponseTransformer;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class UpdatedPriceFactoryTest {


    @InjectMocks
    UpdatedPriceFactory updatedPriceFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(updatedPriceFactory,"updatedPriceResponseTransformer" , new UpdatedPriceResponseTransformer());
        ReflectionTestUtils.setField(updatedPriceFactory,"updatedPriceRequestTransformer" , new UpdatedPriceRequestTransformer());

    }
    
    @Test
    public void getRequestServiceTest(){
        UpdatedPriceRequestTransformer resp = updatedPriceFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof UpdatedPriceRequestTransformer  );
        resp = updatedPriceFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof UpdatedPriceRequestTransformer  );
        resp = updatedPriceFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof UpdatedPriceRequestTransformer  );
        resp = updatedPriceFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof UpdatedPriceRequestTransformer  );
        resp = updatedPriceFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(updatedPriceFactory.getRequestService("test") instanceof  UpdatedPriceRequestTransformer);
    }

    @Test
    public void getResponseServiceTest(){
        UpdatedPriceResponseTransformer resp = updatedPriceFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof UpdatedPriceResponseTransformer  );
        resp = updatedPriceFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof UpdatedPriceResponseTransformer  );
        resp = updatedPriceFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof UpdatedPriceResponseTransformer  );
        resp = updatedPriceFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof UpdatedPriceResponseTransformer  );
        resp = updatedPriceFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(updatedPriceFactory.getResponseService("test") instanceof UpdatedPriceResponseTransformer);
    }

}
