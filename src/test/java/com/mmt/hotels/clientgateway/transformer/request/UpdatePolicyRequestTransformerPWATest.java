package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class UpdatePolicyRequestTransformerPWATest {

    @Mock
    CommonHelper commonHelper;

    @InjectMocks
    UpdatePolicyRequestTransformerPWA updatePolicyRequestTransformerPWA;

    @Test
    public void testConvertUpdatePolicyRequest() throws ClientGatewayException {


        UpdatePolicyRequest updatePolicyRequest = new UpdatePolicyRequest();
        updatePolicyRequest.setTravellerEmailId(new ArrayList<>());
        updatePolicyRequest.getTravellerEmailId().add("<EMAIL>");
        Mockito.when(commonHelper.getAuthToken(Mockito.any())).thenReturn("abcd");

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
        userServiceResponse.getResult().getExtendedUser().setUuid("ABCD");
        userServiceResponse.getResult().getExtendedUser().setProfileId("123");
        userServiceResponse.getResult().getExtendedUser().setProfileType("qwer");

        Mockito.when(commonHelper.getUserDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userServiceResponse);

        UpdatePolicyRequest resp = updatePolicyRequestTransformerPWA.convertUpdatePolicyRequest(updatePolicyRequest, new HashMap<>(), "1234");
        Assert.notNull(resp);

    }

}
