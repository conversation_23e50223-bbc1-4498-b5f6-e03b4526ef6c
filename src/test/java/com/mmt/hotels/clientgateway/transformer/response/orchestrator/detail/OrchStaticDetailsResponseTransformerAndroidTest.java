package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail;

import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerAndroid;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;
import static junit.framework.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class OrchStaticDetailsResponseTransformerAndroidTest {

    @InjectMocks
    private OrchStaticDetailsResponseTransformerAndroid transformer;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private CommonConfig commonConfig;

    @Mock
    private com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo orchestratorStaffInfo;

    @Mock
    private StaffInfo clientGatewayStaffInfo;

    private String starHostIconApp = "https://test.com/star-host-icon-android.png";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(transformer, "starHostIconApp", starHostIconApp);
    }

    @Test
    public void testInit_successful() throws Exception {
        // Setup
        Map<String, String> cardTitleMap = new HashMap<>();
        cardTitleMap.put("key1", "value1");
        cardTitleMap.put("key2", "value2");

        lenient().when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        lenient().when(commonConfig.cardTitleMap()).thenReturn(cardTitleMap);

        // Execute
        transformer.init();

        // Verify
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
        verify(commonConfig).cardTitleMap();
        verify(commonConfig).addPropertyChangeListener(eq("cardTitleMap"), any());

        // Verify cardTitleMap was set
        Map<String, String> actualCardTitleMap = (Map<String, String>) ReflectionTestUtils.getField(transformer, "cardTitleMap");
        assertEquals("cardTitleMap should be set", cardTitleMap, actualCardTitleMap);
    }

    @Test
    public void testInit_withException() throws Exception {
        // Setup
        lenient().when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenThrow(new RuntimeException("Test exception"));

        // Execute - should not throw exception
        transformer.init();

        // Verify
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
        
        // Verify cardTitleMap remains null
        Map<String, String> actualCardTitleMap = (Map<String, String>) ReflectionTestUtils.getField(transformer, "cardTitleMap");
        assertNull("cardTitleMap should remain null on exception", actualCardTitleMap);
    }

    @Test
    public void testInit_withNullCommonConfig() throws Exception {
        // Setup
        lenient().when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(null);

        // Execute - should not throw exception
        transformer.init();

        // Verify
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
        
        // Verify cardTitleMap remains null
        Map<String, String> actualCardTitleMap = (Map<String, String>) ReflectionTestUtils.getField(transformer, "cardTitleMap");
        assertNull("cardTitleMap should remain null when commonConfig is null", actualCardTitleMap);
    }

    @Test
    public void testBuildCardTitleMap_withNullCardTitleMap_returnsNull() {
        // Setup - cardTitleMap is null
        ReflectionTestUtils.setField(transformer, "cardTitleMap", null);

        // Execute
        Map<String, String> result = transformer.buildCardTitleMap();

        // Verify
        assertNull("Should return null when cardTitleMap is null", result);
    }

    @Test
    public void testBuildCardTitleMap_withEmptyCardTitleMap_returnsNull() {
        // Setup - empty cardTitleMap
        Map<String, String> emptyMap = new HashMap<>();
        ReflectionTestUtils.setField(transformer, "cardTitleMap", emptyMap);

        // Execute
        Map<String, String> result = transformer.buildCardTitleMap();

        // Verify
        assertNull("Should return null when cardTitleMap is empty", result);
    }

    @Test
    public void testBuildCardTitleMap_withValidCardTitleMap_returnsTranslatedMap() {
        // Setup
        Map<String, String> cardTitleMap = new HashMap<>();
        cardTitleMap.put("key1", "value1");
        cardTitleMap.put("key2", "value2");
        ReflectionTestUtils.setField(transformer, "cardTitleMap", cardTitleMap);

        lenient().when(polyglotService.getTranslatedData("value1")).thenReturn("translated_value1");
        lenient().when(polyglotService.getTranslatedData("value2")).thenReturn("translated_value2");

        // Execute
        Map<String, String> result = transformer.buildCardTitleMap();

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should have correct number of entries", 2, result.size());
        assertEquals("Should have translated value for key1", "translated_value1", result.get("key1"));
        assertEquals("Should have translated value for key2", "translated_value2", result.get("key2"));

        verify(polyglotService).getTranslatedData("value1");
        verify(polyglotService).getTranslatedData("value2");
    }

    @Test
    public void testBuildCardTitleMap_withTranslationException_returnsNull() {
        // Setup
        Map<String, String> cardTitleMap = new HashMap<>();
        cardTitleMap.put("key1", "value1");
        ReflectionTestUtils.setField(transformer, "cardTitleMap", cardTitleMap);

        lenient().when(polyglotService.getTranslatedData("value1")).thenThrow(new RuntimeException("Translation error"));

        // Execute
        Map<String, String> result = transformer.buildCardTitleMap();

        // Verify
        assertNull("Should return null when translation throws exception", result);
        verify(polyglotService).getTranslatedData("value1");
    }

    @Test
    public void testBuildCardTitleMap_withSingleEntry() {
        // Setup
        Map<String, String> cardTitleMap = new HashMap<>();
        cardTitleMap.put("singleKey", "singleValue");
        ReflectionTestUtils.setField(transformer, "cardTitleMap", cardTitleMap);

        lenient().when(polyglotService.getTranslatedData("singleValue")).thenReturn("translated_single");

        // Execute
        Map<String, String> result = transformer.buildCardTitleMap();

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should have one entry", 1, result.size());
        assertEquals("Should have translated value", "translated_single", result.get("singleKey"));
    }

    @Test
    public void testAddTitleData_doesNothing() {
        // Test addTitleData method (empty implementation)
        HotelResult hotelResult = new HotelResult();
        String countryCode = "IN";
        
        // This should not throw any exception
        transformer.addTitleData(hotelResult, countryCode);
        
        // Since the method is empty, we just verify it executes without error
        assertTrue("addTitleData should execute without error", true);
    }

    @Test
    public void testAddTitleData_withNullParams_doesNothing() {
        // Test addTitleData with null parameters
        transformer.addTitleData(null, null);
        
        // Since the method is empty, we just verify it executes without error
        assertTrue("addTitleData with null params should execute without error", true);
    }

    @Test
    public void testGetLuxeIcon_returnsCorrectIcon() {
        // Test getLuxeIcon method
        String result = transformer.getLuxeIcon();
        assertEquals("Should return LUXE_ICON_APPS constant", LUXE_ICON_APPS, result);
    }

    @Test
    public void testConvertStaffInfo_withNullStaffInfo_returnsNull() {
        // Test convertStaffInfo with null input
        StaffInfo result = transformer.convertStaffInfo(null);
        assertNull("Should return null for null staff info", result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostTrue_setsIconUrl() {
        // Test convertStaffInfo when isStarHost is true
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);
        
        // Mock the super.convertStaffInfo call and removeIcon method
        OrchStaticDetailsResponseTransformerAndroid transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was called
        verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
        verify((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostFalse_doesNotSetIconUrl() {
        // Test convertStaffInfo when isStarHost is false
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(false);
        
        OrchStaticDetailsResponseTransformerAndroid transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was NOT called
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
        verify((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostNull_doesNotSetIconUrl() {
        // Test convertStaffInfo when isStarHost is null
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(null);
        
        OrchStaticDetailsResponseTransformerAndroid transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was NOT called
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
        verify((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_alwaysCallsRemoveIcon() {
        // Test that convertStaffInfo always calls removeIcon regardless of isStarHost value
        lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);
        
        OrchStaticDetailsResponseTransformerAndroid transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
        
        transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify removeIcon was called
        verify((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
    }

    @Test
    public void testPropertyChangeListener() throws Exception {
        // Setup initial state
        Map<String, String> initialMap = new HashMap<>();
        initialMap.put("initial", "value");
        
        Map<String, String> updatedMap = new HashMap<>();
        updatedMap.put("updated", "value");

        lenient().when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        lenient().when(commonConfig.cardTitleMap()).thenReturn(initialMap).thenReturn(updatedMap);

        // Execute init
        transformer.init();

        // Verify initial setup
        verify(commonConfig).addPropertyChangeListener(eq("cardTitleMap"), any());
        
        // Simulate property change by calling cardTitleMap again (this simulates the listener callback)
        lenient().when(commonConfig.cardTitleMap()).thenReturn(updatedMap);
        
        // The property change listener would update the cardTitleMap
        // Since we can't easily test the actual listener, we verify the setup was correct
        assertTrue("Property change listener should have been added", true);
    }

    @Test
    public void testStarHostIconAppInjection() {
        // Test that the @Value injection works correctly
        String iconUrl = (String) ReflectionTestUtils.getField(transformer, "starHostIconApp");
        assertEquals("starHostIconApp should be set correctly", starHostIconApp, iconUrl);
    }

    @Test
    public void testBuildCardTitleMap_mapUtilsEmptyCheck() {
        // Test MapUtils.isNotEmpty logic specifically
        
        // Test with null map
        ReflectionTestUtils.setField(transformer, "cardTitleMap", null);
        assertNull("Should return null for null map", transformer.buildCardTitleMap());
        
        // Test with empty map
        ReflectionTestUtils.setField(transformer, "cardTitleMap", new HashMap<>());
        assertNull("Should return null for empty map", transformer.buildCardTitleMap());
        
        // Test with map containing null values
        Map<String, String> mapWithNulls = new HashMap<>();
        mapWithNulls.put("key", null);
        ReflectionTestUtils.setField(transformer, "cardTitleMap", mapWithNulls);
        
        lenient().when(polyglotService.getTranslatedData(null)).thenReturn("translated_null");
        
        Map<String, String> result = transformer.buildCardTitleMap();
        assertNotNull("Should not return null for map with null values", result);
        assertEquals("Should translate null value", "translated_null", result.get("key"));
    }

    @Test
    public void testConvertStaffInfo_edgeCases() {
        // Test various Boolean values for comprehensive coverage
        Boolean[] testValues = {Boolean.TRUE, Boolean.FALSE, null, true, false};
        
        for (Boolean testValue : testValues) {
            reset(orchestratorStaffInfo);
            lenient().when(orchestratorStaffInfo.getIsStarHost()).thenReturn(testValue);
            
            OrchStaticDetailsResponseTransformerAndroid transformerSpy = spy(transformer);
            lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
            lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
            lenient().doNothing().when((OrchStaticDetailResponseTransformer) transformerSpy).removeIcon(orchestratorStaffInfo);
            
            StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
            
            assertNotNull("Result should not be null for value: " + testValue, result);
            
            if (Boolean.TRUE.equals(testValue)) {
                verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
            } else {
                verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
            }
        }
    }
} 