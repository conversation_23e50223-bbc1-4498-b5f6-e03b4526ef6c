package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.ArrayList;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class UpdatedPriceRequestTransformerPWATest {


    @InjectMocks
    UpdatedPriceRequestTransformer updatedPriceRequestTransformerPWA;

    @Test
    public void testconvertUpdatePriceRequest(){

        UpdatePriceRequest updatePriceRequest = new UpdatePriceRequest();
        updatePriceRequest.setDeviceDetails(new DeviceDetails());

        updatePriceRequest.setExpData("exp");

        updatePriceRequest.setRequestDetails(new RequestDetails());
        updatePriceRequest.getRequestDetails().setSrLat(1d);
        updatePriceRequest.getRequestDetails().setSrLng(1d);
        updatePriceRequest.getRequestDetails().setLoggedIn(false);
        updatePriceRequest.getRequestDetails().setTrafficSource(new TrafficSource());

        updatePriceRequest.setEmiDetail(new EMIDetail());
        updatePriceRequest.getEmiDetail().setTotalCost(100d);
        updatePriceRequest.getEmiDetail().setInterestRate(5d);
        updatePriceRequest.getEmiDetail().setAmount(50d);
        updatePriceRequest.getEmiDetail().setTotalInterest(5d);

        updatePriceRequest.setSearchCriteria(new UpdatedPriceCriteria());
        updatePriceRequest.getSearchCriteria().setRoomCriteria(new ArrayList<UpdatedPriceRoomCriteria>());
        updatePriceRequest.getSearchCriteria().getRoomCriteria().add(new UpdatedPriceRoomCriteria());
        updatePriceRequest.getSearchCriteria().getRoomCriteria().get(0).setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        updatePriceRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().add(new RoomStayCandidate());
        updatePriceRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        updatePriceRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).getChildAges().add(1);

        PriceByHotelsRequestBody priceByHotelsRequestBody = updatedPriceRequestTransformerPWA.convertUpdatedPriceRequest(updatePriceRequest, new CommonModifierResponse());
        Assert.notNull(priceByHotelsRequestBody);



    }

}
