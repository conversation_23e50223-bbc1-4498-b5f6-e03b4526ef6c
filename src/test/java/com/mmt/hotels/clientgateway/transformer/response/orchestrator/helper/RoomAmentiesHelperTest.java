package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.AttributesFacility;
import com.mmt.hotels.clientgateway.response.Facility;
import com.mmt.hotels.clientgateway.response.FacilityGroup;
import com.mmt.hotels.clientgateway.service.PolyglotService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for RoomAmentiesHelper in GI project
 * Comprehensive test coverage following patterns from CG version but adapted for GI implementation
 */
@RunWith(MockitoJUnitRunner.class)
public class RoomAmentiesHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private RoomAmentiesHelper roomAmentiesHelper;

    // Test constants
    private static final String STAR_FACILITIES_TRANSLATED = "Star Facilities";
    private static final String WIFI_GROUP = "WiFi";
    private static final String PARKING_GROUP = "Parking";
    private static final String PREMIUM_FEATURES = "Premium Features";

    @Before
    public void setUp() {
        // Setup polyglot service responses
        when(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES))
                .thenReturn(STAR_FACILITIES_TRANSLATED);
    }

    // ==================== buildAmenities Tests ====================

    @Test
    public void should_ReturnNull_When_RoomInfoIsNull() {
        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_AmenitiesIsNull() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setAmenities(null);

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_AmenitiesIsEmpty() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setAmenities(new ArrayList<>());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildAmenities_When_ValidAmenitiesProvided() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        FacilityGroup wifiGroup = result.get(0);
        assertEquals(WIFI_GROUP, wifiGroup.getName());
        assertNotNull(wifiGroup.getFacilities());
        assertEquals(1, wifiGroup.getFacilities().size());
        
        Facility wifiFacility = wifiGroup.getFacilities().get(0);
        assertEquals("Free WiFi", wifiFacility.getName());
        assertEquals("WIFI", wifiFacility.getAttributeName());
        assertEquals("Connectivity", wifiFacility.getCategoryName());
        assertEquals("text", wifiFacility.getDisplayType());
        assertEquals(Integer.valueOf(1), wifiFacility.getSequence());
    }

    @Test
    public void should_BuildStarFacilities_When_HighlightedAmenitiesProvided() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithHighlightedAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // First group should be star facilities
        FacilityGroup starGroup = result.get(0);
        assertEquals(STAR_FACILITIES_TRANSLATED, starGroup.getName());
        assertEquals(Constants.BOLD_TYPE, starGroup.getType());
        assertEquals(Constants.SIGNATURE_AMENITIES, starGroup.getId());
        assertNotNull(starGroup.getFacilities());
        assertEquals(1, starGroup.getFacilities().size());
        
        Facility starFacility = starGroup.getFacilities().get(0);
        assertEquals("Premium WiFi", starFacility.getName());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.STAR_FACILITIES);
    }

    @Test
    public void should_SortStarFacilitiesBySequence_When_MultipleStarFacilities() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithMultipleHighlightedAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        FacilityGroup starGroup = result.get(0);
        assertEquals(STAR_FACILITIES_TRANSLATED, starGroup.getName());
        
        List<Facility> starFacilities = starGroup.getFacilities();
        assertEquals(2, starFacilities.size());
        
        // Should be sorted by sequence (1, 2)
        assertEquals(Integer.valueOf(1), starFacilities.get(0).getSequence());
        assertEquals(Integer.valueOf(2), starFacilities.get(1).getSequence());
        assertEquals("First Amenity", starFacilities.get(0).getName());
        assertEquals("Second Amenity", starFacilities.get(1).getName());
    }

    @Test
    public void should_HandleNullSequence_When_SortingStarFacilities() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithNullSequenceAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        FacilityGroup starGroup = result.get(0);
        assertEquals(STAR_FACILITIES_TRANSLATED, starGroup.getName());
        
        List<Facility> starFacilities = starGroup.getFacilities();
        assertEquals(2, starFacilities.size());
        
        // Amenity with sequence should come first, null sequence should be last
        assertEquals(Integer.valueOf(1), starFacilities.get(0).getSequence());
        assertNull(starFacilities.get(1).getSequence());
    }

    @Test
    public void should_HandleChildAttributes_When_AmenityHasChildAttributes() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithChildAttributes();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        FacilityGroup group = result.get(0);
        Facility facility = group.getFacilities().get(0);
        assertNotNull(facility.getChildAttributes());
        assertEquals(2, facility.getChildAttributes().size());
        
        AttributesFacility child1 = facility.getChildAttributes().get(0);
        AttributesFacility child2 = facility.getChildAttributes().get(1);
        assertNotNull(child1);
        assertNotNull(child2);
    }

    @Test
    public void should_HandleEmptyChildAttributes_When_ChildAttributesListIsEmpty() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithEmptyChildAttributes();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        FacilityGroup group = result.get(0);
        Facility facility = group.getFacilities().get(0);
        assertNull(facility.getChildAttributes());
    }

    @Test
    public void should_RemoveHighlightedAmenitiesFromMainGroups_When_HighlightedAmenitiesExist() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithOverlappingAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        
        // Star facilities should contain the overlapping amenity
        FacilityGroup starGroup = result.get(0);
        assertEquals(1, starGroup.getFacilities().size());
        assertEquals("Overlapping Amenity", starGroup.getFacilities().get(0).getName());
        
        // Main group should not contain the overlapping amenity anymore
        FacilityGroup mainGroup = result.get(1);
        assertEquals(1, mainGroup.getFacilities().size());
        assertEquals("Regular Amenity", mainGroup.getFacilities().get(0).getName());
    }

    @Test
    public void should_HandleEmptyFacilityGroups_When_GroupHasNoValidFacilities() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithEmptyAmenityGroup();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Only one valid group should remain
        assertEquals("Valid Group", result.get(0).getName());
    }

    @Test
    public void should_ProcessRemainingStarFacilities_When_NotAllHighlightedAmenitiesInMainGroups() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithExtraHighlightedAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo);

        // Then
        assertNotNull(result);
        
        // Star facilities should contain both the overlapping and extra highlighted amenities
        FacilityGroup starGroup = result.get(0);
        assertEquals(2, starGroup.getFacilities().size());
        
        // Find amenities by name (order might vary due to sorting)
        boolean foundOverlapping = false;
        boolean foundExtra = false;
        for (Facility facility : starGroup.getFacilities()) {
            if ("Overlapping Amenity".equals(facility.getName())) {
                foundOverlapping = true;
            } else if ("Extra Highlighted Amenity".equals(facility.getName())) {
                foundExtra = true;
            }
        }
        assertTrue("Should find overlapping amenity", foundOverlapping);
        assertTrue("Should find extra highlighted amenity", foundExtra);
    }

    // ==================== buildHighlightedAmenities Tests ====================

    @Test
    public void should_ReturnNull_When_RoomInfoIsNullForHighlighted() {
        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(null);

        // Then
        assertNull(result);
    }

//    @Test
//    public void should_ReturnNull_When_HighlightedAmenitiesIsNull() {
//        // Given
//        RoomInfo roomInfo = new RoomInfo();
//        roomInfo.setHighlightedAmenities(null);
//
//        // When
//        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);
//
//        // Then
//        assertNull(result);
//    }

//    @Test
//    public void should_ReturnNull_When_HighlightedAmenitiesIsEmpty() {
//        // Given
//        RoomInfo roomInfo = new RoomInfo();
//        roomInfo.setHighlightedAmenities(new ArrayList<>());
//
//        // When
//        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);
//
//        // Then
//        assertNull(result);
//    }

    @Test
    public void should_BuildHighlightedAmenities_When_ValidHighlightedAmenitiesProvided() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(createHighlightedAmenityGroups());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        FacilityGroup group = result.get(0);
        assertEquals(PREMIUM_FEATURES, group.getName());
        assertNotNull(group.getFacilities());
        assertEquals(2, group.getFacilities().size());
        
        Facility firstFacility = group.getFacilities().get(0);
        assertEquals("Premium WiFi", firstFacility.getName());
        assertEquals("PREMIUM_WIFI", firstFacility.getAttributeName());
        assertEquals("Connectivity", firstFacility.getCategoryName());
        
        Facility secondFacility = group.getFacilities().get(1);
        assertEquals("Concierge Service", secondFacility.getName());
        assertEquals("CONCIERGE", secondFacility.getAttributeName());
        assertEquals("Service", secondFacility.getCategoryName());
    }

    @Test
    public void should_HandleChildAttributesInHighlighted_When_AmenityHasChildAttributes() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(createHighlightedAmenityGroupsWithChildAttributes());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        FacilityGroup group = result.get(0);
        Facility facility = group.getFacilities().get(0);
        assertNotNull(facility.getChildAttributes());
        assertEquals(1, facility.getChildAttributes().size());
        
        AttributesFacility childAttribute = facility.getChildAttributes().get(0);
        assertNotNull(childAttribute);
    }

    @Test
    public void should_BuildMultipleHighlightedGroups_When_MultipleGroupsProvided() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(createMultipleHighlightedAmenityGroups());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        FacilityGroup group1 = result.get(0);
        assertEquals("First Group", group1.getName());
        assertEquals(1, group1.getFacilities().size());
        
        FacilityGroup group2 = result.get(1);
        assertEquals("Second Group", group2.getName());
        assertEquals(1, group2.getFacilities().size());
    }

    @Test
    public void should_SkipEmptyGroups_When_GroupHasNoAmenities() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(createHighlightedGroupsWithEmptyGroup());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Only non-empty group should be included
        assertEquals("Non-Empty Group", result.get(0).getName());
    }

    // ==================== buildFacilityHighlights Tests ====================

    @Test
    public void should_ReturnNull_When_HighlightedAmenitiesIsNull() {
        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HighlightedAmenitiesIsEmpty() {
        // Given
        List<AmenityGroup> highlightedAmenities = new ArrayList<>();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildFacilityHighlights_When_ValidHighlightedAmenitiesProvided() {
        // Given
        List<AmenityGroup> highlightedAmenities = createHighlightedAmenityGroups();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(PREMIUM_FEATURES, result.get(0));
    }

    @Test
    public void should_RemoveDuplicates_When_MultipleGroupsWithSameName() {
        // Given
        List<AmenityGroup> highlightedAmenities = createDuplicateAmenityGroups();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // LinkedHashSet should remove duplicates
        assertEquals("WiFi", result.get(0));
        assertEquals("Parking", result.get(1));
    }

    @Test
    public void should_PreserveOrder_When_MultipleGroupsProvided() {
        // Given
        List<AmenityGroup> highlightedAmenities = createMultipleOrderedAmenityGroups();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("First Group", result.get(0));
        assertEquals("Second Group", result.get(1));
        assertEquals("Third Group", result.get(2));
    }

    // ==================== Helper methods for creating test data ====================

    private RoomInfo createRoomInfoWithAmenities() {
        RoomInfo roomInfo = new RoomInfo();
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        
        // WiFi group
        AmenityGroup wifiGroup = new AmenityGroup();
        wifiGroup.setName(WIFI_GROUP);
        
        Amenity wifiAmenity = new Amenity();
        wifiAmenity.setName("Free WiFi");
        wifiAmenity.setAttributeName("WIFI");
        wifiAmenity.setCategoryName("Connectivity");
        wifiAmenity.setDisplayType("text");
        wifiAmenity.setSequence(1);
        wifiAmenity.setHighlightedName("Free WiFi Highlighted");
        wifiAmenity.setType("amenity");
        wifiAmenity.setTags(Arrays.asList("free", "wifi"));
        
        wifiGroup.setAmenities(Arrays.asList(wifiAmenity));
        amenityGroups.add(wifiGroup);
        
        // Parking group
        AmenityGroup parkingGroup = new AmenityGroup();
        parkingGroup.setName(PARKING_GROUP);
        
        Amenity parkingAmenity = new Amenity();
        parkingAmenity.setName("Free Parking");
        parkingAmenity.setAttributeName("PARKING");
        parkingAmenity.setCategoryName("Transportation");
        parkingAmenity.setDisplayType("text");
        parkingAmenity.setSequence(2);
        parkingAmenity.setHighlightedName("Free Parking Highlighted");
        parkingAmenity.setType("amenity");
        parkingAmenity.setTags(Arrays.asList("free", "parking"));
        
        parkingGroup.setAmenities(Arrays.asList(parkingAmenity));
        amenityGroups.add(parkingGroup);
        
        roomInfo.setAmenities(amenityGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithHighlightedAmenities() {
        RoomInfo roomInfo = createRoomInfoWithAmenities();
        
        // Create highlighted amenities that overlap with main amenities
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedGroup = new AmenityGroup();
        highlightedGroup.setName(PREMIUM_FEATURES);
        
        Amenity premiumWifi = new Amenity();
        premiumWifi.setName("Premium WiFi");
        premiumWifi.setAttributeName("PREMIUM_WIFI");
        premiumWifi.setCategoryName("Connectivity");
        premiumWifi.setSequence(1);
        premiumWifi.setHighlightedName("Premium WiFi Highlighted");
        premiumWifi.setType("premium");
        
        highlightedGroup.setAmenities(Arrays.asList(premiumWifi));
        highlightedGroups.add(highlightedGroup);
        
        roomInfo.setHighlightedAmenities(highlightedGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithMultipleHighlightedAmenities() {
        RoomInfo roomInfo = createRoomInfoWithAmenities();
        
        // Create multiple highlighted amenities with different sequences
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedGroup = new AmenityGroup();
        highlightedGroup.setName(PREMIUM_FEATURES);
        
        Amenity amenity1 = new Amenity();
        amenity1.setName("Second Amenity");
        amenity1.setAttributeName("SECOND");
        amenity1.setSequence(2);
        
        Amenity amenity2 = new Amenity();
        amenity2.setName("First Amenity");
        amenity2.setAttributeName("FIRST");
        amenity2.setSequence(1);
        
        highlightedGroup.setAmenities(Arrays.asList(amenity1, amenity2));
        highlightedGroups.add(highlightedGroup);
        
        roomInfo.setHighlightedAmenities(highlightedGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithNullSequenceAmenities() {
        RoomInfo roomInfo = createRoomInfoWithAmenities();
        
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedGroup = new AmenityGroup();
        highlightedGroup.setName(PREMIUM_FEATURES);
        
        Amenity amenityWithSequence = new Amenity();
        amenityWithSequence.setName("With Sequence");
        amenityWithSequence.setAttributeName("WITH_SEQ");
        amenityWithSequence.setSequence(1);
        
        Amenity amenityWithoutSequence = new Amenity();
        amenityWithoutSequence.setName("Without Sequence");
        amenityWithoutSequence.setAttributeName("WITHOUT_SEQ");
        amenityWithoutSequence.setSequence(null);
        
        highlightedGroup.setAmenities(Arrays.asList(amenityWithoutSequence, amenityWithSequence));
        highlightedGroups.add(highlightedGroup);
        
        roomInfo.setHighlightedAmenities(highlightedGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithChildAttributes() {
        RoomInfo roomInfo = new RoomInfo();
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Complex Amenities");
        
        Amenity amenity = new Amenity();
        amenity.setName("Complex Amenity");
        amenity.setAttributeName("COMPLEX");
        
        // Create child attributes
        List<AmenityAttribute> childAttributes = new ArrayList<>();
        
        AmenityAttribute child1 = new AmenityAttribute();
        child1.setName("Child Attribute 1");
        childAttributes.add(child1);
        
        AmenityAttribute child2 = new AmenityAttribute();
        child2.setName("Child Attribute 2");
        childAttributes.add(child2);
        
        amenity.setChildAttributes(childAttributes);
        group.setAmenities(Arrays.asList(amenity));
        amenityGroups.add(group);
        
        roomInfo.setAmenities(amenityGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithEmptyChildAttributes() {
        RoomInfo roomInfo = new RoomInfo();
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Simple Amenities");
        
        Amenity amenity = new Amenity();
        amenity.setName("Simple Amenity");
        amenity.setAttributeName("SIMPLE");
        amenity.setChildAttributes(new ArrayList<>()); // Empty list
        
        group.setAmenities(Arrays.asList(amenity));
        amenityGroups.add(group);
        
        roomInfo.setAmenities(amenityGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithOverlappingAmenities() {
        RoomInfo roomInfo = new RoomInfo();
        
        // Main amenities
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup mainGroup = new AmenityGroup();
        mainGroup.setName("Main Group");
        
        Amenity overlappingAmenity = new Amenity();
        overlappingAmenity.setName("Overlapping Amenity");
        overlappingAmenity.setAttributeName("OVERLAPPING");
        
        Amenity regularAmenity = new Amenity();
        regularAmenity.setName("Regular Amenity");
        regularAmenity.setAttributeName("REGULAR");
        
        mainGroup.setAmenities(Arrays.asList(overlappingAmenity, regularAmenity));
        amenityGroups.add(mainGroup);
        roomInfo.setAmenities(amenityGroups);
        
        // Highlighted amenities - overlapping with main
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedGroup = new AmenityGroup();
        highlightedGroup.setName("Highlighted Group");
        
        Amenity highlightedOverlapping = new Amenity();
        highlightedOverlapping.setName("Overlapping Amenity"); // Same name as in main
        highlightedOverlapping.setAttributeName("OVERLAPPING");
        
        highlightedGroup.setAmenities(Arrays.asList(highlightedOverlapping));
        highlightedGroups.add(highlightedGroup);
        roomInfo.setHighlightedAmenities(highlightedGroups);
        
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithEmptyAmenityGroup() {
        RoomInfo roomInfo = new RoomInfo();
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        
        // Empty group
        AmenityGroup emptyGroup = new AmenityGroup();
        emptyGroup.setName("Empty Group");
        emptyGroup.setAmenities(new ArrayList<>());
        amenityGroups.add(emptyGroup);
        
        // Valid group
        AmenityGroup validGroup = new AmenityGroup();
        validGroup.setName("Valid Group");
        
        Amenity amenity = new Amenity();
        amenity.setName("Valid Amenity");
        amenity.setAttributeName("VALID");
        
        validGroup.setAmenities(Arrays.asList(amenity));
        amenityGroups.add(validGroup);
        
        roomInfo.setAmenities(amenityGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithExtraHighlightedAmenities() {
        RoomInfo roomInfo = new RoomInfo();
        
        // Main amenities
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup mainGroup = new AmenityGroup();
        mainGroup.setName("Main Group");
        
        Amenity overlappingAmenity = new Amenity();
        overlappingAmenity.setName("Overlapping Amenity");
        overlappingAmenity.setAttributeName("OVERLAPPING");
        
        mainGroup.setAmenities(Arrays.asList(overlappingAmenity));
        amenityGroups.add(mainGroup);
        roomInfo.setAmenities(amenityGroups);
        
        // Highlighted amenities - some overlap, some don't
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedGroup = new AmenityGroup();
        highlightedGroup.setName("Highlighted Group");
        
        Amenity highlightedOverlapping = new Amenity();
        highlightedOverlapping.setName("Overlapping Amenity"); // Same name as in main
        highlightedOverlapping.setAttributeName("OVERLAPPING");
        highlightedOverlapping.setSequence(1);
        
        Amenity extraHighlighted = new Amenity();
        extraHighlighted.setName("Extra Highlighted Amenity"); // Not in main
        extraHighlighted.setAttributeName("EXTRA");
        extraHighlighted.setSequence(2);
        
        highlightedGroup.setAmenities(Arrays.asList(highlightedOverlapping, extraHighlighted));
        highlightedGroups.add(highlightedGroup);
        roomInfo.setHighlightedAmenities(highlightedGroups);
        
        return roomInfo;
    }

    private List<AmenityGroup> createHighlightedAmenityGroups() {
        List<AmenityGroup> groups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName(PREMIUM_FEATURES);
        
        List<Amenity> amenities = new ArrayList<>();
        
        Amenity amenity1 = new Amenity();
        amenity1.setName("Premium WiFi");
        amenity1.setAttributeName("PREMIUM_WIFI");
        amenity1.setCategoryName("Connectivity");
        amenity1.setDisplayType("text");
        amenity1.setSequence(1);
        amenities.add(amenity1);
        
        Amenity amenity2 = new Amenity();
        amenity2.setName("Concierge Service");
        amenity2.setAttributeName("CONCIERGE");
        amenity2.setCategoryName("Service");
        amenity2.setDisplayType("text");
        amenity2.setSequence(2);
        amenities.add(amenity2);
        
        group.setAmenities(amenities);
        groups.add(group);
        
        return groups;
    }

    private List<AmenityGroup> createHighlightedAmenityGroupsWithChildAttributes() {
        List<AmenityGroup> groups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Complex Features");
        
        Amenity amenity = new Amenity();
        amenity.setName("Complex Amenity");
        amenity.setAttributeName("COMPLEX");
        
        // Create child attributes
        List<AmenityAttribute> childAttributes = new ArrayList<>();
        AmenityAttribute child = new AmenityAttribute();
        child.setName("Child Attribute");
        childAttributes.add(child);
        
        amenity.setChildAttributes(childAttributes);
        group.setAmenities(Arrays.asList(amenity));
        groups.add(group);
        
        return groups;
    }

    private List<AmenityGroup> createMultipleHighlightedAmenityGroups() {
        List<AmenityGroup> groups = new ArrayList<>();
        
        AmenityGroup group1 = new AmenityGroup();
        group1.setName("First Group");
        Amenity amenity1 = new Amenity();
        amenity1.setName("First Amenity");
        amenity1.setAttributeName("FIRST");
        group1.setAmenities(Arrays.asList(amenity1));
        groups.add(group1);
        
        AmenityGroup group2 = new AmenityGroup();
        group2.setName("Second Group");
        Amenity amenity2 = new Amenity();
        amenity2.setName("Second Amenity");
        amenity2.setAttributeName("SECOND");
        group2.setAmenities(Arrays.asList(amenity2));
        groups.add(group2);
        
        return groups;
    }

    private List<AmenityGroup> createHighlightedGroupsWithEmptyGroup() {
        List<AmenityGroup> groups = new ArrayList<>();
        
        // Empty group
        AmenityGroup emptyGroup = new AmenityGroup();
        emptyGroup.setName("Empty Group");
        emptyGroup.setAmenities(new ArrayList<>());
        groups.add(emptyGroup);
        
        // Non-empty group
        AmenityGroup nonEmptyGroup = new AmenityGroup();
        nonEmptyGroup.setName("Non-Empty Group");
        Amenity amenity = new Amenity();
        amenity.setName("Valid Amenity");
        amenity.setAttributeName("VALID");
        nonEmptyGroup.setAmenities(Arrays.asList(amenity));
        groups.add(nonEmptyGroup);
        
        return groups;
    }

    private List<AmenityGroup> createDuplicateAmenityGroups() {
        List<AmenityGroup> groups = new ArrayList<>();
        
        AmenityGroup group1 = new AmenityGroup();
        group1.setName("WiFi");
        groups.add(group1);
        
        AmenityGroup group2 = new AmenityGroup();
        group2.setName("WiFi"); // Duplicate name
        groups.add(group2);
        
        AmenityGroup group3 = new AmenityGroup();
        group3.setName("Parking");
        groups.add(group3);
        
        return groups;
    }

    private List<AmenityGroup> createMultipleOrderedAmenityGroups() {
        List<AmenityGroup> groups = new ArrayList<>();
        
        AmenityGroup group1 = new AmenityGroup();
        group1.setName("First Group");
        groups.add(group1);
        
        AmenityGroup group2 = new AmenityGroup();
        group2.setName("Second Group");
        groups.add(group2);
        
        AmenityGroup group3 = new AmenityGroup();
        group3.setName("Third Group");
        groups.add(group3);
        
        return groups;
    }
} 