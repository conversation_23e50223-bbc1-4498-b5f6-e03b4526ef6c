package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class EMIRequestTransformerTest {

    @InjectMocks
    EMIRequestTransformer emiRequestTransformer;

    @Test
    public void convertEmiRequestTest() {
        UpdatedEmiRequest updatedEmiRequest = new UpdatedEmiRequest();
        updatedEmiRequest.setDeviceDetails(new DeviceDetails());
        updatedEmiRequest.setEmiDetail(new EMIDetail());

        updatedEmiRequest.setRequestDetails(new RequestDetails());

        updatedEmiRequest.setSearchCriteria(new UpdatedEMISearchCriteria());
        updatedEmiRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        updatedEmiRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        updatedEmiRequest.getSearchCriteria().getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        updatedEmiRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges().add(1);

        UpdateEmiDetailRequest updateEmiDetailRequest = emiRequestTransformer.convertEmiRequest(updatedEmiRequest, new CommonModifierResponse());
        org.springframework.util.Assert.notNull(updateEmiDetailRequest);
    }
}
