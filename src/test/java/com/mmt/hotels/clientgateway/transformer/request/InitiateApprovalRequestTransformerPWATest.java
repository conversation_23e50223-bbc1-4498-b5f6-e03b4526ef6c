package com.mmt.hotels.clientgateway.transformer.request;


import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.InitApprovalRequest;
import com.mmt.hotels.clientgateway.request.payment.*;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.transformer.request.pwa.InitiateApprovalRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class InitiateApprovalRequestTransformerPWATest {


    @Mock
    CommonHelper commonHelper;

    @InjectMocks
    InitiateApprovalRequestTransformerPWA initiateApprovalRequestTransformerPWA;

    @Test
    public void testConvertInitiateApprovalRequest() throws ClientGatewayException {

        InitApprovalRequest initApprovalRequest = new InitApprovalRequest();
        initApprovalRequest.setTxnKey("abcd1234");
        initApprovalRequest.setEmployeeComment("testing");
        initApprovalRequest.setSpecialRequest(new SpecialRequest());
        initApprovalRequest.getSpecialRequest().setDisclaimer("abcd");
        initApprovalRequest.getSpecialRequest().setCategories(new ArrayList<>());
        initApprovalRequest.getSpecialRequest().getCategories().add(new SpecialRequestCategory());
        initApprovalRequest.getSpecialRequest().getCategories().get(0).setSubCategories(new ArrayList<>());
        initApprovalRequest.getSpecialRequest().getCategories().get(0).getSubCategories().add(new SpecialRequestCategory());

        initApprovalRequest.setWorkflowStatus("PENDING");
        initApprovalRequest.setTravellerDetailsList(new ArrayList<>());
        initApprovalRequest.getTravellerDetailsList().add(new TravellerDetail());
        initApprovalRequest.getTravellerDetailsList().get(0).setGender(Gender.MALE);
        initApprovalRequest.getTravellerDetailsList().get(0).setPaxType(PaxType.ADULT);

        initApprovalRequest.setTripTag(new TripTag());
        initApprovalRequest.getTripTag().setTripTagDetails(new TripTagDetails());
        initApprovalRequest.getTripTag().getTripTagDetails().setAttributeList(new ArrayList<>());
        initApprovalRequest.getTripTag().getTripTagDetails().getAttributeList().add(new TripTagAttribute());
        initApprovalRequest.getTripTag().getTripTagDetails().getAttributeList().get(0).setAttributeSelectedValue(new ArrayList<>());
        initApprovalRequest.getTripTag().getTripTagDetails().getAttributeList().get(0).getAttributeSelectedValue().add("abcd");

        Mockito.when(commonHelper.getAuthToken(Mockito.any())).thenReturn("abcd");

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
        userServiceResponse.getResult().getExtendedUser().setUuid("ABCD");
        userServiceResponse.getResult().getExtendedUser().setProfileId("123");
        userServiceResponse.getResult().getExtendedUser().setProfileType("qwer");

        Mockito.when(commonHelper.getUserDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userServiceResponse);

        com.mmt.hotels.model.request.corporate.InitApprovalRequest resp = initiateApprovalRequestTransformerPWA.convertInitApprovalRequest(initApprovalRequest, new HashMap<>(), "abcd");
        Assert.assertNotNull(resp);
    }
}