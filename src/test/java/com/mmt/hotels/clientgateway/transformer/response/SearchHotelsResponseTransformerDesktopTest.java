package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.PersonalizedSection;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SearchHotelsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.request.flyfish.FlyfishReviewData;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.ReviewSummary;
import com.mmt.hotels.model.response.flyfish.TopicRatings;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.staticdata.Address;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria;
import com.mmt.hotels.pojo.response.detail.FeaturedAmenity;
import org.apache.commons.io.FileUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsResponseTransformerDesktopTest {

	@InjectMocks
	SearchHotelsResponseTransformerDesktop searchHotelsResponseTransformerDesktop;

	@Spy
	CommonResponseTransformer commonResponseTransformer;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	PolyglotService polyglotService;

	@Mock
	PolyglotHelper polyglotHelper;

	@Mock
	DateUtil dateUtil;

	@Mock
	private Map<String, Map<String, String>> amenitiesIconUrls;


	Hotel hotel;

	String hotelPersuasions;

	MyBizAssuredToolTip myBizAssuredToolTip;

	ValueStaysTooltip valueStaysToolTipDom;

	@Mock
	PersuasionUtil persuasionUtil;

	private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

	@Before
	public void init() throws IOException, JSONException {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		MySafetyTooltip mySafetyDataTooltips = new MySafetyTooltip();
		mySafetyDataTooltips.setTitle("SAFETY_TITLE");
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("testTranslation");
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"mySafetyDataTooltips",mySafetyDataTooltips);
		List<String> amenetiesWithUrl= new ArrayList<>();
		amenetiesWithUrl.add("WI_FI");
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"amenetiesWithUrl",amenetiesWithUrl);
		InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
		hotel = mapper.readValue(availPriceRequest, Hotel.class);
		hotelPersuasions = mapper.writeValueAsString(hotel.getHotelPersuasions());
		InputStream myBizAssuredToolTipStream = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/myBizAssuredTooltip.json");
		myBizAssuredToolTip = mapper.readValue(myBizAssuredToolTipStream, MyBizAssuredToolTip.class);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"myBizAssuredTooltipDom", myBizAssuredToolTip);
		InputStream valueStaysToolTipDomStream = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/valueStaysToolTipDom.json");
		valueStaysToolTipDom = mapper.readValue(valueStaysToolTipDomStream, ValueStaysTooltip.class);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"valueStaysTooltipDom", valueStaysToolTipDom);
		MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),"eng");
		MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
		myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
		myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
		myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"myBizStaticCard",myBizStaticCard);
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest(){
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		TransportPoi transportPoi = new TransportPoi();


		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), transportPoi,false);
		Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		Assert.assertEquals(2,((Map<String, Object>)hotel.getHotelPersuasions()).size());

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP)).getData().size() > 0);
//		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP)).getData().get(0).getText().contains(secondaryPersuasion));

	}

	@Test
	public void addSeoTextPersuasionTest() throws JsonProcessingException {
		Hotel hotel = new Hotel();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		boolean oddHotel = false;
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
		searchHotelsRequest.getRequestDetails().getTrafficSource().setSource("trivago");
		searchHotelsResponseTransformerDesktop.addSeoTextPersuasion(hotel, hotelEntity, oddHotel, searchHotelsRequest, null);
		Assert.assertNull(hotel.getHotelPersuasions());

		searchHotelsRequest.getRequestDetails().getTrafficSource().setSource(Constants.TRAFFIC_SOURCE_SEO);
		searchHotelsRequest.getRequestDetails().setSiteDomain("IN");
		searchHotelsResponseTransformerDesktop.addSeoTextPersuasion(hotel, hotelEntity, oddHotel, searchHotelsRequest, null);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID).getData());

		hotelEntity.setStarRating(3);
		hotelEntity.setCountryCode("IN");
		hotelEntity.setAddress(new Address());
		hotelEntity.getAddress().setLine1("Ist line addr");
		hotelEntity.getAddress().setLine2("2nd line addr");
		hotelEntity.setCityName("GOA");
		hotelEntity.setPropertyType("Hotel");
		hotelEntity.setFlyfishReviewSummary(new HashMap<>());
		JsonNode jsonNode = objectMapper.readTree(
				"{\"cumulativeRating\":4.5,\"totalReviewsCount\":226,\"totalRatingCount\":313,\"travellerRatingSummary\":{\"hotelSummary\":[{\"concept\":\"Safety and Hygiene\",\"value\":4.4,\"show\":true,\"heroTag\":true,\"reviewCount\":65},{\"concept\":\"Security\",\"value\":5.0,\"show\":true,\"reviewCount\":1},{\"concept\":\"Location\",\"value\":4.6,\"show\":true,\"reviewCount\":50,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Location\",\"relatedReviewCount\":53,\"tagType\":\"BASE\",\"priorityScore\":53},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Distance from Beach\",\"relatedReviewCount\":28,\"tagType\":\"BASE\",\"priorityScore\":28},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach\",\"relatedReviewCount\":23,\"tagType\":\"BASE\",\"priorityScore\":23},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Connectivity\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Central Location\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Location\",\"relatedReviewCount\":52,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":52},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Near Beach\",\"relatedReviewCount\":51,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":51}]},{\"concept\":\"Hospitality\",\"value\":4.5,\"show\":true,\"reviewCount\":167,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Courtesy\",\"relatedReviewCount\":76,\"tagType\":\"BASE\",\"priorityScore\":76},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Service Quality\",\"relatedReviewCount\":16,\"tagType\":\"BASE\",\"priorityScore\":16},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Courteous Staff\",\"relatedReviewCount\":75,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":75},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Service\",\"relatedReviewCount\":14,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":14}]},{\"concept\":\"Room\",\"value\":4.4,\"show\":true,\"reviewCount\":61,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Quality\",\"relatedReviewCount\":56,\"tagType\":\"BASE\",\"priorityScore\":56},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Space in Rooms\",\"relatedReviewCount\":7,\"tagType\":\"BASE\",\"priorityScore\":7},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Balcony\",\"relatedReviewCount\":6,\"tagType\":\"BASE\",\"priorityScore\":6},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Bathroom Hygiene\",\"relatedReviewCount\":4,\"tagType\":\"BASE\",\"priorityScore\":4},{\"sentiment\":\"NEGATIVE\",\"subConcept\":\"Bed Quality\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Amenities\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Room\",\"relatedReviewCount\":60,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":60},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Balcony\",\"relatedReviewCount\":6,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":6}]},{\"concept\":\"Cleanliness\",\"value\":4.2,\"show\":true,\"reviewCount\":222,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Cleanliness\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Resort Cleanliness\",\"relatedReviewCount\":10,\"tagType\":\"BASE\",\"priorityScore\":10},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Bathroom Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach Cleanliness\",\"relatedReviewCount\":3,\"tagType\":\"BASE\",\"priorityScore\":3},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Hotel Cleanliness\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Surroundings Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Garden Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Area Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Amenity Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Place Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Environment Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Room\",\"relatedReviewCount\":17,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":10000},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Property\",\"relatedReviewCount\":9,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":9900},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Bathroom\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Pool\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Facilities\",\"value\":4.2,\"show\":true,\"reviewCount\":96,\"subConcepts\":[{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Patio\",\"relatedReviewCount\":12,\"tagType\":\"BASE\",\"priorityScore\":12},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool\",\"relatedReviewCount\":8,\"tagType\":\"BASE\",\"priorityScore\":8},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"In-House Activities\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Pool\",\"relatedReviewCount\":8,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":8}]},{\"concept\":\"Value for Money\",\"value\":4.2,\"show\":true,\"reviewCount\":32,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Luxury\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Food\",\"value\":4.1,\"show\":true,\"reviewCount\":153,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Food\",\"relatedReviewCount\":38,\"tagType\":\"BASE\",\"priorityScore\":38},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Breakfast\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Local Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Continental Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Mediterranean Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Food\",\"relatedReviewCount\":34,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":34},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Breakfast\",\"relatedReviewCount\":15,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":15},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Child friendliness\",\"value\":4.0,\"show\":true,\"reviewCount\":21}],\"roomSummary\":{\"45000297254\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"View\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"566\":{\"cumulativeRating\":0.0},\"567\":{\"cumulativeRating\":0.0},\"24133\":{\"cumulativeRating\":0.0,\"best\":[{\"publishDate\":\"Feb 21, 2021\",\"travellerName\":\"Aditya Surana\",\"title\":\"Waooooo. Really worth staying\",\"rating\":5.0,\"reviewText\":\"Waooo, what a place what a location and above all the beach view rooms are the best\\nthe breakfast varity was good event the service but taste 5/10, rest a must visit resort if u visit goa\",\"id\":\"P9J5FYGIY2HN4A8EU55CO0ULNI9XHEDUV5S0DIQOINYFGGC5KTDNFR6IZYAVRU0XH5XTQ4IQJT0M\",\"travelType\":\"COUPLE\",\"crawledData\":false}]},\"45000308605\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Spacious Room\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068630\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068632\":{\"cumulativeRating\":0.0},\"560\":{\"cumulativeRating\":0.0}}},\"crawledData\":false,\"cityCode\":\"GOI\",\"sortingCriterion\":[\"Latest first\",\"Helpful first\",\"Positive first\",\"Negative first\"],\"ratingText\":\"Excellent\",\"postLockdownData\":{\"rating\":4.5,\"totalReviewCount\":64,\"ratingCount\":10,\"textCount\":47,\"imageCount\":2,\"imageTextCount\":5}}");
		hotelEntity.getFlyfishReviewSummary().put(OTA.MMT, jsonNode);
		LinkedHashSet<String> set = new LinkedHashSet<>();
		set.add("Cleanliness");
		set.add("Hygenic");
		hotelEntity.setFacilityHighlights(set);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop, "objectMapperUtil", objectMapperUtil);
		Mockito.when(objectMapperUtil.getObjectFromJsonNode(Mockito.any(), Mockito.any())).thenReturn(
				objectMapper.readValue(jsonNode.get("travellerRatingSummary").toString(), TravellerRatingSummaryDTO.class));
		searchHotelsResponseTransformerDesktop.addSeoTextPersuasion(hotel, hotelEntity, oddHotel, searchHotelsRequest, null);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID).getData());

		searchHotelsResponseTransformerDesktop.addSeoTextPersuasion(hotel, hotelEntity, !oddHotel, searchHotelsRequest, null);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID).getData());

		hotelEntity.setCountryCode("AE");
		hotelEntity.getFlyfishReviewSummary().put(OTA.TA, jsonNode);
		searchHotelsResponseTransformerDesktop.addSeoTextPersuasion(hotel, hotelEntity, !oddHotel, searchHotelsRequest, null);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		Assert.assertNotNull(((Map<String, PersuasionObject>)hotel.getHotelPersuasions()).get(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID).getData());
	}

	@Test
	public void fetchToolTipPersuasionDataTest() {
		Map<String, Object> toolTipConfigMap = new HashMap<>();
		toolTipConfigMap.put("VILLA", new HashMap<String, Object>());
		((Map<String, Object>) toolTipConfigMap.get("VILLA")).put("imageUrl", "www.mmtcdn.com/villa.png");
		((Map<String, Object>) toolTipConfigMap.get("VILLA"))
				.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
		ReflectionTestUtils
				.setField(searchHotelsResponseTransformerDesktop, "desktopToolTipPersuasionsMap", toolTipConfigMap);
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("testTranslation");
		Map<String, Object> toolTipDataMap = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,
																																					"fetchToolTipPersuasionData", "VILLA");
		Assert.assertNotNull(toolTipDataMap);
		Assert.assertNotNull(toolTipDataMap.get("imageUrl"));
		Assert.assertNotNull(toolTipDataMap.get("toolTipData"));
		Assert.assertEquals(((List<String>) toolTipDataMap.get("toolTipData")).size(), 4);
	}


	@Test
	public void overridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123456789");
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		matchMakerRequest.setHotels(Collections.singletonList(inputHotel));
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("MyBiz Recommended Properties Near This Property");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		Assert.assertEquals("MyBiz Recommended Properties Near This Property", personalizedSection.getHeading());
	}

	@Test
	public void doNotOverridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("CORPBUDGET");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		Assert.assertNull(personalizedSection.getHeading());
	}

	@Test
	public void testBuildQuickBookCard() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		QuickBookInfo quickBookInfo = new QuickBookInfo();
		quickBookInfo.setTitleWithPrice("");
		quickBookInfo.setRoomPersuasion("");
		Assert.assertNotNull(searchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo));
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		Assert.assertNotNull(searchHotelsResponseTransformerDesktop.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerDesktop.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}
	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerDesktop.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		Assert.assertNotNull(staticCard);
		Assert.assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}

	@Test
	public void testCreateFreeCancellationTooltip() throws IOException {
		CancellationTimeline cancellationTimelineHes = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/cancellationTimeline.json")),
				CancellationTimeline.class);
		CancellationTimeline cancellationTimeline = searchHotelsResponseTransformerDesktop.createFreeCancellationTooltip(cancellationTimelineHes);
		Assert.assertNotNull(cancellationTimeline);
	}

	@Test
	public void testCreateValueStayToolTipDom() {
		Mockito.doNothing().when(polyglotHelper).translateValueStaysTooltip(Mockito.any(ValueStaysTooltip.class));
		ValueStaysTooltip result = searchHotelsResponseTransformerDesktop.createValueStayToolTip("IN");
		Assert.assertNotNull(result);
	}

	@Test
	public void testCreateValueStayToolTipIntl() throws IOException {
		ValueStaysTooltip valueStaysToolTipIntl = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/mmtValueStaysTooltipIntl.json")),
				ValueStaysTooltip.class);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"valueStaysTooltipIntl", valueStaysToolTipIntl);
		Mockito.doNothing().when(polyglotHelper).translateValueStaysTooltip(Mockito.any(ValueStaysTooltip.class));
		ValueStaysTooltip result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "createValueStayToolTip", "AE");
		Assert.assertNotNull(result);
	}

	@Test
	public void testCreateMyBizAssuredToolTip() {
		Mockito.doNothing().when(polyglotHelper).translateMyBizAssuredTooltip(Mockito.any(MyBizAssuredToolTip.class));
		MyBizAssuredToolTip result = searchHotelsResponseTransformerDesktop.createMyBizAssuredToolTip();
		Assert.assertNotNull(result);
	}

	@Test
	public void buildCalendarCriteriaTest(){
		CalendarCriteria calendarCriteria = new CalendarCriteria();
		calendarCriteria.setAdvanceDays(5);
		calendarCriteria.setAvailable(true);
		calendarCriteria.setMaxDate("2022-10-21");
		calendarCriteria.setMlos(3);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildCalendarCriteria",calendarCriteria);
	}

	@Test
	public void buildSoldOutInfo(){
		SoldOutInfo soldOutInfo = new SoldOutInfo();
		soldOutInfo.setSoldOutReason("Out of Policy");
		soldOutInfo.setSoldOutSubText("OOP");
		soldOutInfo.setSoldOutText("Sold Out hai");
		soldOutInfo.setSoldOutType("Sold out");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildSoldOutInfo",soldOutInfo);
	}

	@Test
	public void getMybizSimilarHotelsFeaturesTest(){
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"getMybizSimilarHotelsFeatures");
	}

	@Test
	public void buildEmiDetailsTest(){
		Emi emi = new Emi();
		emi.setEmiAmount(32251);
		emi.setEmiType("Test1");
		emi.setBankName("HDFC");
		emi.setTenure(5);
		emi.setTotalCost(200.0);
		emi.setTotalInterest(500.0);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildEmiDetails",emi);
	}

	@Test
	public void buildHotelBottomCardTest(){
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildHotelBottomCard",new QuickBookInfo());
	}

	@Test
	public void getFacilityTest(){
		FeaturedAmenity featuredAmenity = new FeaturedAmenity();
		featuredAmenity.setIconUrl("Icon");
		featuredAmenity.setName("Test");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"getFacility",featuredAmenity);
	}

	@Test
	public void buildHotelTopCardTest(){
		MyBizSimilarToDirectObj myBizSimilarToDirectObj = new MyBizSimilarToDirectObj();
		myBizSimilarToDirectObj.setDistance("55");
		List<FeaturedAmenity> featuredAmenityList = new ArrayList<>();
		featuredAmenityList.add(new FeaturedAmenity());
		myBizSimilarToDirectObj.setAmenities(featuredAmenityList);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildHotelTopCard",myBizSimilarToDirectObj);
	}

	@Test
	public void updateTopLevelHoverTest() throws ClientGatewayException {

		Hotel hotel = new Hotel();

		try {
			org.json.JSONObject jo = new JSONObject("{\"data\":{\"titleText\":\"Holdthisbookingforfreetillmidnightofduedate\",\"subText\":\"LimitedBookingsubjecttoavailability\"},\"tooltipType\":\"MP_FARE_HOLD\"}");
			org.json.JSONArray ja = new JSONArray("[{\"hasAction\":false,\"icontype\":\"holdHotelIconSmall\",\"persuasionType\":\"BNPL\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"appendLeft3\"]},\"html\":false,\"id\":\"630cdde601a1251b91e0a11d\",\"multiPersuasionPriority\":0},{\"hasAction\":false,\"timer\":{\"expiry\":1665383340000},\"persuasionType\":\"BNPL\",\"persuasionKey\":\"BOOK_NOW_PERSUASION_TITLE\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"pc__holdBookingTooltip__bookNowText\",\"latoBold\"]},\"html\":false,\"id\":\"630c630a1c05b343d18680e7\",\"text\":\"BookNow@\\u20b90\",\"multiPersuasionPriority\":1},{\"hasAction\":false,\"icontype\":\"holdBookingInfoGrey\",\"persuasionType\":\"BNPL\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"appendRight3\"]},\"html\":false,\"id\":\"630cdd9d01a1251b91e0a11c\",\"multiPersuasionPriority\":2}]");
			Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("abc");
			Mockito.when(dateUtil.convertEpochToDateTime(Mockito.anyLong(), Mockito.anyString())).thenReturn("abc");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"updateTopLevelHover",jo,ja);
		}catch (Exception e){
			logger.error("error occured in getting file", e.getMessage());

		}
	}

//	@Test
//	public void addPersuasionHoverDataTest() throws ClientGatewayException {
//
//		try {
//			Hotel hotel = new Hotel();
//			SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
//			CancellationTimeline cancellationTimeline = new CancellationTimeline();
//			DisplayFare displayFare = new DisplayFare();
//			String persuasionStr = "{\"PC_MIDDLE_2\":{\"template\":\"MULTI_PERSUASION_H\",\"data\":[{\"hasAction\":true,\"actionType\":\"DETAIL_PAGE_MAP\",\"persuasionType\":\"LOCATION\",\"html\":true,\"style\":{\"styleClasses\":[\"pc__location\"]},\"id\":\"LOC_PERSUASION_1\",\"text\":\"<spanclass='blueText'>Anjuna<\\/span> |1.1kmfromAnjunaBeach\"}],\"placeholder\":\"MULTI\"},\"PC_MIDDLE_3\":{\"template\":\"MULTI_PERSUASION_V\",\"templateType\":\"DEFAULT\",\"data\":[{\"hasAction\":false,\"persuasionType\":\"STAY_TYPE\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"htlHighlt__title\"]},\"html\":true,\"id\":\"631ad079f047c9132b5dba40\",\"text\":\"ROOMSINACOTTAGE\",\"multiPersuasionPriority\":1},{\"hasAction\":false,\"persuasionType\":\"STAY_TYPE\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"htlHighlt__title\"]},\"html\":true,\"id\":\"631ace70f047c9132b5dba3f\",\"text\":\"ROOMSINACOTTAGE\",\"multiPersuasionPriority\":1}],\"style\":{\"styleClasses\":[\"htlHighlt\"]},\"placeholder\":\"PC_MIDDLE_3\"},\"PC_MIDDLE_8\":{\"template\":\"MULTI_PERSUASION_V\",\"templateType\":\"DEFAULT\",\"data\":[{\"hasAction\":false,\"icontype\":\"singleGreenTickIcon\",\"persuasionType\":\"CANCEL_TYPE\",\"multiPersuasionCount\":3,\"style\":{\"styleClasses\":[\"pc__inclusion\",\"greenText\",\"font12\"]},\"html\":true,\"id\":\"6106d0aa8f2f68259b696239\",\"text\":\"<b>FreeCancellationIncluded<\\/b>\",\"multiPersuasionPriority\":1}],\"placeholder\":\"PC_MIDDLE_8\"}}";
//
//			Object jo = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/transformer/response/TopLevelHover.json"));
//
//			hotel.setHotelPersuasions(jo);
//			Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.anyObject(),Mockito.any())).thenReturn(persuasionStr);
//
//			searchHotelsResponseTransformerDesktop.addPersuasionHoverData(hotel, hotelEntity, cancellationTimeline, displayFare);
//		} catch (Exception e){
//			logger.error("error occured in getting file", e.getMessage());
//		}
//	}

	@Test
	public void addToolTipTest() throws ClientGatewayException {

		try {
			Hotel hotel = new Hotel();
			SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
			CancellationTimeline cancellationTimeline = new CancellationTimeline();
			DisplayFare displayFare = new DisplayFare();

			org.json.JSONObject jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"BNPL_AVAIL\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"FCZPN\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"FREE_CANCELLATION\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

//			displayFare.setCorpMetaData(new CorpMetaInfo());
//			displayFare.getCorpMetaData().setValidationPayload(new ValidationResponse());
//			displayFare.getCorpMetaData().getValidationPayload().setFailureReasons(new ArrayList<>());
//			displayFare.getCorpMetaData().getValidationPayload().getFailureReasons().add("failure");
			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"OOP\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			Map<String, MySafetyTooltip> mySafetyToolTipTranslated = new HashMap<>();
			mySafetyToolTipTranslated.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),new MySafetyTooltip());
			ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"mySafetyToolTipTranslated", mySafetyToolTipTranslated);

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"SAFETY\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");


//			Map<String, Object> toolTipConfigMap = new HashMap<>();
//			toolTipConfigMap.put("VILLA", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("VILLA")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("VILLA"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("HOSTEL", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("HOSTEL")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("HOSTEL"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("APARTMENT", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("APARTMENT")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("APARTMENT"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("COTTAGE", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("COTTAGE")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("COTTAGE"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("HOMESTAY", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("HOMESTAY")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("HOMESTAY"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"desktopToolTipPersuasionsMap", toolTipConfigMap);

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"VILLA\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"HOSTEL\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"APARTMENT\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"COTTAGE\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"HOMESTAY\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			hotelEntity.setBudgetHotel(true);
			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"TOOL_TIP_MMT_VALUE_STAY\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"TOOL_TIP_MYBIZ_ASSURED\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"TOOL_TIP_LUXE\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

		} catch (Exception e){
			logger.error("error occured in getting file", e.getMessage());
		}
	}
	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerDesktop.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		junit.framework.Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		junit.framework.Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdDesktop()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdDesktop()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdDesktop()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdDesktop()));
	}

	@Test
	public void buildReviewSummaryTest() {
		ReviewSummary reviewSummary = new ReviewSummary();
		reviewSummary.setReviewCount(100);
		reviewSummary.setHotelRating(4.3);
		reviewSummary.setOta("MMT");
		TopicRatings topicRating = new TopicRatings();
		topicRating.setRating(4.3);
		topicRating.setTitle("Room");
		reviewSummary.setTopicRatings(Collections.singletonList(topicRating));
		FlyfishReviewData flyfishReviewData = searchHotelsResponseTransformerDesktop.buildReviewSummary(reviewSummary, new CommonModifierResponse());
		Assert.assertNotNull(flyfishReviewData);
	}

}