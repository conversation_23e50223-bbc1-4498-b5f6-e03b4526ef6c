package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.PoliciesResponseTransformer;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FilterFactoryTest {

    @InjectMocks
    private FilterFactory filterFactory;

    @Mock
    private FilterResponseTransformer filterResponseTransformer;

    @Mock
    private FilterRequestTransformer filterRequestTransformer;

    @Mock
    private FilterHelper filterHelper;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private PolyglotHelper polyglotHelper;

    @Mock
    private FilterConfig filterConfig;

    private String baseFilterSettings;
    private String suggestedFilterSettings;
    private Map<String, List<Filter>> compositeFilterMapping;

    @Before
    public void setup() {
        // Initialize test data
        baseFilterSettings = "{\"filters\":[],\"suggestedFiltersThreshold\":5}";
        suggestedFilterSettings = "{\"SUGGESTED_FILTER_THRESHOLD\":5}";
        compositeFilterMapping = new HashMap<>();
        List<Filter> filterList = new ArrayList<>();
        compositeFilterMapping.put("test", filterList);

        // Set test data using reflection
        ReflectionTestUtils.setField(filterFactory, "baseFilterSettings", baseFilterSettings);
        ReflectionTestUtils.setField(filterFactory, "suggestedFilterSettings", suggestedFilterSettings);
        ReflectionTestUtils.setField(filterFactory, "compositeFilterMapping", compositeFilterMapping);
        ReflectionTestUtils.setField(filterFactory, "mealPrefConfig", "{\"filters\":{}}");
        ReflectionTestUtils.setField(filterFactory, "toggleFilterConfig", "{\"filters\":{}}");
        ReflectionTestUtils.setField(filterFactory, "toggleFilterConfigHomestay", "{\"filters\":{}}");
    }

    @Test
    public void testGetRequestService_Default() {
        FilterRequestTransformer service = filterFactory.getRequestService(null);
        assertNotNull(service);
        assertEquals(filterRequestTransformer, service);
    }

    @Test
    public void testGetRequestService_PWA() {
        FilterRequestTransformer service = filterFactory.getRequestService("PWA");
        assertNotNull(service);
        assertEquals(filterRequestTransformer, service);
    }

    @Test
    public void testGetRequestService_Desktop() {
        FilterRequestTransformer service = filterFactory.getRequestService("DESKTOP");
        assertNotNull(service);
        assertEquals(filterRequestTransformer, service);
    }

    @Test
    public void testGetResponseService_Default() {
        FilterResponseTransformer service = filterFactory.getResponseService(null);
        assertNotNull(service);
        assertEquals(filterResponseTransformer, service);
    }

    @Test
    public void testGetResponseService_PWA() {
        FilterResponseTransformer service = filterFactory.getResponseService("PWA");
        assertNotNull(service);
        assertEquals(filterResponseTransformer, service);
    }

    @Test
    public void testGetResponseService_Desktop() {
        FilterResponseTransformer service = filterFactory.getResponseService("DESKTOP");
        assertNotNull(service);
        assertEquals(filterResponseTransformer, service);
    }


    @Test
    public void testGetCompositeFilterConfig() {
        Map<String, List<Filter>> config = filterFactory.getCompositeFilterConfig();
        assertNotNull(config);
        assertEquals(compositeFilterMapping, config);
    }

    @Test
    public void modifiedFilterWithPolyglotData(){
        String filter="{\"filtersToShow\":{\"AMENITIES\":{\"AMENITIES\":[]},\"BUSINESS\":{\"AMENITIES\":[\"Wi-Fi:5\"],\"FREE_BREAKFAST_AVAIL\":[\"BREAKFAST_AVAIL:0\"],\"IN_POLICY\":[\"IN_POLICY:3\"],\"MEAL_PLAN_AVAIL\":[\"TWO_MEAL_AVAIL:1\",\"ALL_MEAL_AVAIL:2\"],\"MMT_OFFERING\":[\"MyBiz Assured:4\",\"MySafety - Safe and Hygienic Stays:7\"],\"PAY_AT_HOTEL_AVAIL\":[\"PAH_AVAIL:6\"]},\"DRIVING_DISTANCE_KM\":{\"DRIVING_DISTANCE_KM\":[]},\"HOTEL_PRICE_MANUAL\":{\"HOTEL_PRICE_MANUAL\":[]},\"OTHER\":{\"FREE_CANCELLATION_AVAIL\":[\"CANCELLATION_AVAIL:1\"],\"PAY_LATER\":[\"PAY_LATER:0\"]},\"PRICE_BUCKET\":{\"HOTEL_PRICE_BUCKET\":[]},\"STAR_CATEGORY\":{\"STAR_RATING\":[\"3:0\",\"4:1\",\"5:2\",\"Unrated:3\"]},\"USER_RATING\":{\"USER_RATING\":[\"3:0\",\"4:1\",\"4_5:2\"]}},\"rankOrder\":{\"AMENITIES\":6,\"BUSINESS\":1,\"DRIVING_DISTANCE_KM\":9,\"HOTEL_PRICE_MANUAL\":4,\"OTHER\":7,\"PRICE_BUCKET\":3,\"STAR_CATEGORY\":2,\"USER_RATING\":5}}\n";
        String funnelSource="HOMESTAY";
        String modifiedFilter=filterFactory.modifiedFilterWithPolyglotData(filter,funnelSource);
        Assert.assertNotNull(modifiedFilter);
    }
}
