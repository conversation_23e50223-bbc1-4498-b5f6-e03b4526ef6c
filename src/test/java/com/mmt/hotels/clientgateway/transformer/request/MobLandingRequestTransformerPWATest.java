package com.mmt.hotels.clientgateway.transformer.request;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.transformer.request.pwa.MobLandingRequestTransformerPWA;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class MobLandingRequestTransformerPWATest {

	@InjectMocks
	MobLandingRequestTransformerPWA mobLandingRequestTransformerPWA;

	@Mock
	Utility utility;

	@Test
	public void testConvertMobLandingRequest() {
		MobLandingRequest mobLandingRequest = new MobLandingRequest();
		mobLandingRequest.setDeviceDetails(new DeviceDetails());
		mobLandingRequest.getDeviceDetails().setAppVersion("1.0");
		mobLandingRequest.getDeviceDetails().setBookingDevice("ANDROID");
		mobLandingRequest.getDeviceDetails().setDeviceId("abc123");
		mobLandingRequest.getDeviceDetails().setNetworkType("WIFI");
		mobLandingRequest.getDeviceDetails().setDeviceType("DESKTOP");

		mobLandingRequest.setSearchCriteria(new SearchHotelsCriteria());
		mobLandingRequest.getSearchCriteria().setHotelIds(new ArrayList<>());
		mobLandingRequest.getSearchCriteria().setLastHotelCategory("VILLA");
		mobLandingRequest.getSearchCriteria().setLastHotelId("1254368954357");
		mobLandingRequest.getSearchCriteria().setLat(12.02d);
		mobLandingRequest.getSearchCriteria().setLimit(20);
		mobLandingRequest.getSearchCriteria().setLng(10.00d);
		mobLandingRequest.getSearchCriteria().setPersonalizedSearch(true);
		mobLandingRequest.getSearchCriteria().setTotalHotelsShown(50);
		mobLandingRequest.getSearchCriteria().setCheckIn("2020-03-29");
		mobLandingRequest.getSearchCriteria().setCheckOut("2020-03-31");
		mobLandingRequest.getSearchCriteria().setCityCode("abcd");
		mobLandingRequest.getSearchCriteria().setCountryCode("IN");
		mobLandingRequest.getSearchCriteria().setCurrency("INR");
		mobLandingRequest.getSearchCriteria().setLocationId("RGNCR");
		mobLandingRequest.getSearchCriteria().setLocationType("region");
		mobLandingRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
		mobLandingRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
		mobLandingRequest.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(2);
		mobLandingRequest.getSearchCriteria().getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
		mobLandingRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges().add(2);

		mobLandingRequest.setFilterCriteria(new ArrayList<>());
		mobLandingRequest.getFilterCriteria().add(new Filter());
		mobLandingRequest.getFilterCriteria().get(0).setFilterGroup(FilterGroup.MMT_OFFERING);
		mobLandingRequest.getFilterCriteria().get(0).setFilterValue("MyBiz Assured");
		mobLandingRequest.getFilterCriteria().get(0).setRangeFilter(false);
		mobLandingRequest.getFilterCriteria().add(new Filter());
		mobLandingRequest.getFilterCriteria().get(1).setFilterGroup(FilterGroup.HOTEL_PRICE);
		mobLandingRequest.getFilterCriteria().get(1).setFilterRange(new FilterRange());
		mobLandingRequest.getFilterCriteria().get(1).getFilterRange().setMinValue(1);
		mobLandingRequest.getFilterCriteria().get(1).getFilterRange().setMaxValue(100);
		mobLandingRequest.getFilterCriteria().get(1).setRangeFilter(true);

		mobLandingRequest.setRequestDetails(new RequestDetails());
		mobLandingRequest.getRequestDetails().setFunnelSource("SEO");
		mobLandingRequest.getRequestDetails().setIdContext("abc");
		mobLandingRequest.getRequestDetails().setNotifCoupon("abc");
		mobLandingRequest.getRequestDetails().setSrCon("IN");
		mobLandingRequest.getRequestDetails().setSrCty("DEL");
		mobLandingRequest.getRequestDetails().setTrafficSource(new TrafficSource());
		mobLandingRequest.getRequestDetails().setVisitNumber(5);
		mobLandingRequest.getRequestDetails().setVisitorId("d6cba685-3aec-4373-a715-fcd4fcfd9f4a");
		mobLandingRequest.getRequestDetails().setSrLat(28d);
		mobLandingRequest.getRequestDetails().setSrLng(28d);

		mobLandingRequest.setFeatureFlags(new FeatureFlags());
		mobLandingRequest.getFeatureFlags().setNoOfAddons(4);
		mobLandingRequest.getFeatureFlags().setNoOfCoupons(4);
		mobLandingRequest.getFeatureFlags().setNoOfPersuasions(4);
		mobLandingRequest.getFeatureFlags().setNoOfSoldouts(4);
		mobLandingRequest.getFeatureFlags().setReviewSummaryRequired(true);
		mobLandingRequest.getFeatureFlags().setShortlistingRequired(true);
		mobLandingRequest.getFeatureFlags().setStaticData(true);
		mobLandingRequest.getFeatureFlags().setWalletRequired(true);

		mobLandingRequest.setImageDetails(new ImageDetails());
		mobLandingRequest.getImageDetails().setCategories(new ArrayList<ImageCategory>());
		mobLandingRequest.getImageDetails().getCategories().add(new ImageCategory());
		mobLandingRequest.getImageDetails().getCategories().get(0).setCount(2);
		mobLandingRequest.getImageDetails().getCategories().get(0).setHeight(2);
		mobLandingRequest.getImageDetails().getCategories().get(0).setWidth(2);
		mobLandingRequest.getImageDetails().setTypes(new ArrayList<String>());

		mobLandingRequest.setMatchMakerDetails(new MatchMakerRequest());

		mobLandingRequest.setRequiredApis(new RequiredApis());
		mobLandingRequest.getRequiredApis().setFilterSuggestionRequired(true);
		mobLandingRequest.getRequiredApis().setMetaRequired(true);
		mobLandingRequest.getRequiredApis().setPersonalizationRequired(true);
		mobLandingRequest.getRequiredApis().setMmrRequired(true);

		mobLandingRequest.setExpData("{APE:6,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T}");


		mobLandingRequest.setReviewDetails(new ReviewDetails());
		mobLandingRequest.getReviewDetails().setOtas(new ArrayList<>(Arrays.asList("MMT", "TA")));
		mobLandingRequest.getReviewDetails().setTagTypes(new ArrayList<>(Arrays.asList("BASE", "WHAT_GUESTS_SAY")));

		mobLandingRequest.setTravellerType("1");
		mobLandingRequest.setUuids(new ArrayList<>());

		Mockito.when(utility.checkIfFilterValueExistsInAppliedFilterMap(Mockito.any())).thenReturn(true);

		HotelLandingMobRequestBody hotelLandingMobRequestBody = mobLandingRequestTransformerPWA.
				convertMobLandingRequest(mobLandingRequest, new CommonModifierResponse());
		Assert.assertNotNull(hotelLandingMobRequestBody);
	}
}
