package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.StaticDetailRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.StaticDetailRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.StaticDetailRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.StaticDetailRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.StaticDetailRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.StaticDetailResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.StaticDetailResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.StaticDetailResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.StaticDetailResponseTransformerPWA;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class StaticDetailFactoryTest {


    @InjectMocks
    StaticDetailFactory staticDetailFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailResponseTransformerPWA" , new StaticDetailResponseTransformerPWA());
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailResponseTransformerDesktop" , new StaticDetailResponseTransformerDesktop());
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailResponseTransformerAndroid" , new StaticDetailResponseTransformerAndroid());
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailResponseTransformerIOS" , new StaticDetailResponseTransformerIOS());
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailRequestTransformerPWA" , new StaticDetailRequestTransformerPWA());
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailRequestTransformerDesktop" , new StaticDetailRequestTransformerDesktop());
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailRequestTransformerAndroid" , new StaticDetailRequestTransformerAndroid());
        ReflectionTestUtils.setField(staticDetailFactory,"staticDetailRequestTransformerIOS" , new StaticDetailRequestTransformerIOS());
        
    }
    
    @Test
    public void getRequestServiceTest(){
        StaticDetailRequestTransformer resp = staticDetailFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof StaticDetailRequestTransformerPWA  );
        resp = staticDetailFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof StaticDetailRequestTransformerDesktop  );
        resp = staticDetailFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof StaticDetailRequestTransformerAndroid  );
        resp = staticDetailFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof StaticDetailRequestTransformerIOS  );
        resp = staticDetailFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(staticDetailFactory.getRequestService("test") instanceof StaticDetailRequestTransformerDesktop);
    }

    @Test
    public void getResponseServiceTest(){
        StaticDetailResponseTransformer resp = staticDetailFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof StaticDetailResponseTransformerPWA  );
        resp = staticDetailFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof StaticDetailResponseTransformerDesktop  );
        resp = staticDetailFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof StaticDetailResponseTransformerAndroid  );
        resp = staticDetailFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof StaticDetailResponseTransformerIOS  );
        resp = staticDetailFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(staticDetailFactory.getResponseService("test") instanceof  StaticDetailResponseTransformerDesktop);
    }

}
