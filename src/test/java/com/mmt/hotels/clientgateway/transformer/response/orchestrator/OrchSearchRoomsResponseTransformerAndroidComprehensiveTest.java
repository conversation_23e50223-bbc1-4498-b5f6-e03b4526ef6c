package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.enums.RoomType;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.*;
import com.gommt.hotels.orchestrator.detail.model.response.da.AvailDetails;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationPolicy;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.RoomUpsellConfig;
import com.mmt.hotels.clientgateway.pms.RoomViewFilterConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.*;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for OrchSearchRoomsResponseTransformerAndroid
 *
 * This test class provides extensive coverage for:
 * - Main transformation methods (convertSearchRoomsResponse, transformRoomsToRoomDetails)
 * - Static methods (isPropertyHotelOrResort, setCanTranslateFlag)
 * - Utility methods (amenitiesFilter, buildBedInfoText, etc.)
 * - Android-specific overrides (removeImpInfo, createTopRatedPersuasion, etc.)
 * - Edge cases (null/empty inputs, invalid data)
 * - Business logic (room type determination, pricing, filtering)
 * - Error scenarios and exception handling
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchRoomsResponseTransformerAndroidComprehensiveTest {

    // === MOCKS ===
    @Mock private Utility utility;
    @Mock private DateUtil dateUtil;
    @Mock private MobConfigHelper mobConfigHelper;
    @Mock private SearchRoomsMediaHelper searchRoomsMediaHelper;
    @Mock private RoomInfoHelper roomInfoHelper;
    @Mock private SearchRoomsPriceHelper searchRoomsPriceHelper;
    @Mock private RoomAmentiesHelper roomAmentiesHelper;
    @Mock private CancellationPolicyHelper cancellationPolicyHelper;

    @InjectMocks
    private OrchSearchRoomsResponseTransformerAndroid transformer;

    // === TEST DATA ===
    private HotelDetailsResponse validHotelDetailsResponse;
    private SearchRoomsRequest validSearchRoomsRequest;
    private SearchCriteria validSearchCriteria;
    private RequestDetails validRequestDetails;
    private CommonModifierResponse validCommonModifierResponse;

    @Before
    public void setUp() {
        // Setup default mock behaviors
        setupDefaultMockBehaviors();

        // Initialize test data
        setupValidTestData();
    }

    private void setupDefaultMockBehaviors() {
//        when(utility.isLuxeHotel(any())).thenReturn(false);
//        when(utility.isExperimentTrue(any(), anyString())).thenReturn(false);
//        when(utility.isExperimentOn(any(), anyString())).thenReturn(false);
//
//        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
//
//        when(searchRoomsMediaHelper.extractRoomImagesFromMedia(any(), anyString())).thenReturn(Arrays.asList("image1.jpg", "image2.jpg"));
//
//        when(roomInfoHelper.transformRoomHighlights(any(), any(), anyBoolean(), anyBoolean())).thenReturn(Arrays.asList(createBasicRoomHighlight()));
//
//        when(roomAmentiesHelper.buildAmenities(any())).thenReturn(new ArrayList<>());
//        when(roomAmentiesHelper.buildFacilityHighlights(any())).thenReturn(new ArrayList<>());
//
//        when(cancellationPolicyHelper.transformCancellationPolicy(any(), any())).thenReturn(createBasicCancellationPolicy());

        // Mock MobConfigHelper
        RoomUpsellConfig roomUpsellConfig = new RoomUpsellConfig();
        roomUpsellConfig.roomViewsFilterToggle = Arrays.asList(createRoomViewFilterConfig());
        when(mobConfigHelper.getRoomUpsellConfig()).thenReturn(roomUpsellConfig);
    }

    // ==================== STATIC METHOD TESTS ====================

    @Test
    public void testIsPropertyHotelOrResort_Hotel_ShouldReturnTrue() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("HOTEL");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertTrue("Should return true for HOTEL property type", result);
    }

    @Test
    public void testIsPropertyHotelOrResort_Resort_ShouldReturnTrue() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("RESORT");

        // When
        boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

        // Then
        assertTrue("Should return true for RESORT property type", result);
    }

    @Test
    public void testIsPropertyHotelOrResort_CaseInsensitive_ShouldReturnTrue() {
        // Given
        HotelDetails hotelDetails1 = new HotelDetails();
        hotelDetails1.setPropertyType("hotel");

        HotelDetails hotelDetails2 = new HotelDetails();
        hotelDetails2.setPropertyType("Resort");

        // When
        boolean result1 = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails1);
        boolean result2 = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails2);

        // Then
        assertTrue("Should return true for lowercase 'hotel'", result1);
        assertTrue("Should return true for mixed case 'Resort'", result2);
    }

    @Test
    public void testIsPropertyHotelOrResort_NonHotelPropertyTypes_ShouldReturnFalse() {
        // Test data for various non-hotel property types
        String[] nonHotelTypes = {"APARTMENT", "VILLA", "HOSTEL", "GUESTHOUSE", "B&B", "CONDO", "HOMESTAY"};

        for (String propertyType : nonHotelTypes) {
            // Given
            HotelDetails hotelDetails = new HotelDetails();
            hotelDetails.setPropertyType(propertyType);

            // When
            boolean result = OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails);

            // Then
            assertFalse("Should return false for property type: " + propertyType, result);
        }
    }

    @Test
    public void testIsPropertyHotelOrResort_NullInputs_ShouldReturnFalse() {
        // Test with null hotel details
        assertFalse("Should return false for null hotel details",
                   OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(null));

        // Test with null property type
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType(null);
        assertFalse("Should return false for null property type",
                   OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails));

        // Test with empty property type
        hotelDetails.setPropertyType("");
        assertFalse("Should return false for empty property type",
                   OrchSearchRoomsResponseTransformer.isPropertyHotelOrResort(hotelDetails));
    }

    // ==================== STATIC METHOD: setCanTranslateFlag TESTS ====================

    @Test
    public void testSetCanTranslateFlag_ValidInputs_ShouldSetFlag() {
        // Given
        SelectRoomRatePlan ratePlan = createBasicSelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList("INGO", "AGODA");

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, "INGO");

        // Then
        assertTrue("Should set can translate flag to true for enabled supplier", ratePlan.isCanTranslate());
    }

    @Test
    public void testSetCanTranslateFlag_DisabledSupplier_ShouldNotSetFlag() {
        // Given
        SelectRoomRatePlan ratePlan = createBasicSelectRoomRatePlan();
        List<String> translateEnabledSupplierCodes = Arrays.asList("INGO", "AGODA");

        // When
        OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, translateEnabledSupplierCodes, "OTHER");

        // Then
        assertFalse("Should not set can translate flag for disabled supplier", ratePlan.isCanTranslate());
    }

    @Test
    public void testSetCanTranslateFlag_NullInputs_ShouldHandleGracefully() {
        // Given
        SelectRoomRatePlan ratePlan = createBasicSelectRoomRatePlan();

        // When & Then - Should not throw exception
        try {
            OrchSearchRoomsResponseTransformer.setCanTranslateFlag(null, Arrays.asList("INGO"), "INGO");
            OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, null, "INGO");
            OrchSearchRoomsResponseTransformer.setCanTranslateFlag(ratePlan, Arrays.asList("INGO"), null);
        } catch (Exception e) {
            fail("Should handle null inputs gracefully, but threw: " + e.getMessage());
        }
    }

         // ==================== MAIN TRANSFORMATION TESTS ====================

     @Test
     public void testConvertSearchRoomsResponse_NullHotelDetailsResponse_ShouldReturnEmptyResponse() {
         // When
         SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
                 validSearchRoomsRequest, null, new HashMap<>(),
                 new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
                 validRequestDetails, validCommonModifierResponse);

         // Then
         assertNotNull("Response should not be null", response);
         assertNull("ExactRooms should be null for null input", response.getExactRooms());
     }

     @Test
     public void testConvertSearchRoomsResponse_NullHotelDetails_ShouldReturnEmptyResponse() {
         // Given
         HotelDetailsResponse responseWithNullHotelDetails = new HotelDetailsResponse();
         responseWithNullHotelDetails.setHotelDetails(null);

         // When
         SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
                 validSearchRoomsRequest, responseWithNullHotelDetails, new HashMap<>(),
                 new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
                 validRequestDetails, validCommonModifierResponse);

         // Then
         assertNotNull("Response should not be null", response);
         assertNull("ExactRooms should be null when hotelDetails is null", response.getExactRooms());
     }


//    @Test
//    public void testConvertSearchRoomsResponse_EmptyRoomsList_ShouldReturnEmptyResponse() {
//        // Given
//        HotelDetails hotelDetails = validHotelDetailsResponse.getHotelDetails();
//        hotelDetails.setRooms(new ArrayList<>());
//        hotelDetails.setRoomCombos(new ArrayList<>());
//
//        // When
//        SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
//                validSearchRoomsRequest, validHotelDetailsResponse, new HashMap<>(),
//                new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
//                validRequestDetails, validCommonModifierResponse);
//
//        // Then
//        assertNotNull("Response should not be null", response);
//        // Should handle empty rooms list gracefully
//    }
//
//    @Test
//    public void testConvertSearchRoomsResponse_WithLuxeHotel_ShouldCallLuxeCheck() {
//        // Given
//        when(utility.isLuxeHotel(any())).thenReturn(true);
//
//        // When
//        SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
//                validSearchRoomsRequest, validHotelDetailsResponse, new HashMap<>(),
//                new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
//                validRequestDetails, validCommonModifierResponse);
//
//        // Then
//        assertNotNull("Response should not be null", response);
//        verify(utility).isLuxeHotel(any());
//    }

    // ==================== UTILITY METHOD TESTS ====================

    @Test
    public void testAmenitiesFilter_ValidAmenityGroup_ShouldReturnFilterCodes() {
        // Given
        AmenityGroup amenityGroup = createAmenityGroupWithFilterableAmenities();

        // When
        List<String> result = (List<String>) ReflectionTestUtils.invokeMethod(
                transformer, "amenitiesFilter", amenityGroup);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Should contain filter codes", result.size() > 0);
    }

    @Test
    public void testAmenitiesFilter_NullAmenityGroup_ShouldReturnEmptyList() {
        // When
        List<String> result = (List<String>) ReflectionTestUtils.invokeMethod(
                transformer, "amenitiesFilter", (AmenityGroup) null);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Should return empty list for null input", result.isEmpty());
    }

    @Test
    public void testAmenitiesFilter_NullRoomUpsellConfig_ShouldReturnEmptyList() {
        // Given
        when(mobConfigHelper.getRoomUpsellConfig()).thenReturn(null);
        AmenityGroup amenityGroup = createAmenityGroupWithFilterableAmenities();

        // When
        List<String> result = (List<String>) ReflectionTestUtils.invokeMethod(
                transformer, "amenitiesFilter", amenityGroup);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Should return empty list when config is null", result.isEmpty());
    }

//    @Test
//    public void testBuildBedInfoText_ValidRoomInfo_ShouldReturnBedInfo() {
//        // Given
//        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo roomInfo = createValidRoomInfo();
//
//        // When
//        String result = transformer.buildBedInfoText(roomInfo);
//
//        // Then
//        assertNotNull("Result should not be null", result);
//    }
//
//    @Test
//    public void testBuildBedInfoText_NullRoomInfo_ShouldReturnEmptyString() {
//        // When
//        String result = transformer.buildBedInfoText(null);
//
//        // Then
//        assertEquals("Should return empty string for null input", "", result);
//    }

    // ==================== ROOM TYPE DETERMINATION TESTS ====================

    @Test
    public void testIsSuperPackageRoom_SuperPackageType_ShouldReturnTrue() {
        // Given
        Rooms room = createValidRoom();
        room.setType("SUPER_PACKAGE");

        // When
        boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                transformer, "isSuperPackageRoom", room);

        // Then
        assertTrue("Should return true for super package room", result);
    }

//    @Test
//    public void testIsRecommendedRoom_RecommendedType_ShouldReturnTrue() {
//        // Given
//        Rooms room = createValidRoom();
//        room.setType("RECOMMENDED");
//
//        // When
//        boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
//                transformer, "isRecommendedRoom", room);
//
//        // Then
//        assertTrue("Should return true for recommended room", result);
//    }

    @Test
    public void testIsOccasionRoom_OccasionPackageType_ShouldReturnTrue() {
        // Given
        Rooms room = createValidRoom();
        room.setType("OCCASION_PACKAGE");

        // When
        boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                transformer, "isOccasionRoom", room);

        // Then
        assertTrue("Should return true for occasion package room", result);
    }

    @Test
    public void testIsOccupancyRoomType_OccupancyType_ShouldReturnTrue() {
        // Given
        Rooms room = createValidRoom();
        room.setType("OCCUPANCY");

        // When
        boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                transformer, "isOccupancyRoomType", room);

        // Then
        assertFalse("Should return true for occupancy room", result);
    }

    // ==================== ANDROID-SPECIFIC TESTS ====================

//    @Test
//    public void testCreateTopRatedPersuasion_AndroidImplementation_ShouldReturnMobilePersuasion() {
//        // When
//        PersuasionObject result = transformer.createTopRatedPersuasion();
//
//        // Then
//        assertNotNull("Should return persuasion object for mobile", result);
//    }

    @Test
    public void testBuildLoginPersuasion_AndroidImplementation_ShouldReturnNull() {
        // When
        LoginPersuasion result = transformer.buildLoginPersuasion();

        // Then
        assertNull("Should return null for Android implementation", result);
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions_AndroidImplementation_ShouldNotThrow() {
        // Given
        BestCoupon coupon = new BestCoupon();
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When & Then - Should not throw exception
        try {
            transformer.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
        } catch (Exception e) {
            fail("Should not throw exception: " + e.getMessage());
        }
    }

//    @Test
//    public void testBuildGroupFilterForDevice_AndroidImplementation_ShouldReturnStaycationFilter() {
//        // Given
//        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = new HashMap<>();
//        List<Filter> filterCriteria = new ArrayList<>();
//
//        // When
//        GroupRatePlanFilter result = transformer.buildGroupFilterForDevice(groupRatePlanFilterConfMap, filterCriteria, true);
//
//        // Then
//        assertNotNull("Should return group filter", result);
//    }
//
//    // ==================== EDGE CASE TESTS ====================
//
//    @Test
//    public void testConvertSearchRoomsResponse_NullCurrency_ShouldUseDefault() {
//        // Given
//        validSearchCriteria.setCurrency(null);
//
//        // When
//        SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
//                validSearchRoomsRequest, validHotelDetailsResponse, new HashMap<>(),
//                new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
//                validRequestDetails, validCommonModifierResponse);
//
//        // Then
//        assertNotNull("Response should not be null", response);
//    }
//
//    @Test
//    public void testConvertSearchRoomsResponse_EmptyCurrency_ShouldUseDefault() {
//        // Given
//        validSearchCriteria.setCurrency("");
//
//        // When
//        SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
//                validSearchRoomsRequest, validHotelDetailsResponse, new HashMap<>(),
//                new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
//                validRequestDetails, validCommonModifierResponse);
//
//        // Then
//        assertNotNull("Response should not be null", response);
//    }

    @Test
    public void testTransformRoomsToRoomDetails_EmptyRoomsList_ShouldReturnEmptyList() {
        // When
        List<RoomDetails> result = (List<RoomDetails>) ReflectionTestUtils.invokeMethod(
                transformer, "transformRoomsToRoomDetails", new ArrayList<Rooms>(),
                validHotelDetailsResponse.getHotelDetails(), new HashMap<String, String>(),
                "INR", "HOTELS", 2, 1, false, validCommonModifierResponse,
                new HashMap<String, String>(), false, false);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Should return empty list for empty input", result.isEmpty());
    }

    @Test
    public void testTransformRoomsToRoomDetails_NullRoomsList_ShouldReturnEmptyList() {
        // When
        List<RoomDetails> result = (List<RoomDetails>) ReflectionTestUtils.invokeMethod(
                transformer, "transformRoomsToRoomDetails", (List<Rooms>) null,
                validHotelDetailsResponse.getHotelDetails(), new HashMap<String, String>(),
                "INR", "HOTELS", 2, 1, false, validCommonModifierResponse,
                new HashMap<String, String>(), false, false);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Should return empty list for null input", result.isEmpty());
    }

    // ==================== BUSINESS LOGIC TESTS ====================

//    @Test
//    public void testTransformRoomsToRoomDetails_ExactRooms_ShouldTransformCorrectly() {
//        // Given
//        List<Rooms> rooms = Arrays.asList(
//                createRoomWithType("EXACT"),
//                createRoomWithType("EXACT")
//        );
//
//        // When
//        List<RoomDetails> result = (List<RoomDetails>) ReflectionTestUtils.invokeMethod(
//                transformer, "transformRoomsToRoomDetails", rooms,
//                validHotelDetailsResponse.getHotelDetails(), new HashMap<String, String>(),
//                "INR", "HOTELS", 2, 1, false, validCommonModifierResponse,
//                new HashMap<String, String>(), false, false);
//
//        // Then
//        assertNotNull("Result should not be null", result);
//        assertEquals("Should transform all rooms", 2, result.size());
//    }
//
//    @Test
//    public void testBuildStaycationFilter_ValidInputs_ShouldReturnFilter() {
//        // Given
//        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = new HashMap<>();
//        groupRatePlanFilterConfMap.put("STAYCATION", createBasicGroupRatePlanFilter());
//        List<Filter> filterCriteria = new ArrayList<>();
//
//        // When
//        GroupRatePlanFilter result = transformer.buildStaycationFilter(groupRatePlanFilterConfMap, filterCriteria, true);
//
//        // Then
//        assertNotNull("Result should not be null", result);
//    }
//
//    // ==================== INTEGRATION SCENARIO TESTS ====================
//
//    @Test
//    public void testCompleteTransformationFlow_RealWorldScenario_ShouldWork() {
//        // Given - A complete real-world scenario with multiple room types
//        HotelDetails hotelDetails = validHotelDetailsResponse.getHotelDetails();
//        hotelDetails.setRooms(Arrays.asList(
//                createRoomWithType("EXACT"),
//                createRoomWithType("RECOMMENDED"),
//                createRoomWithType("EXACT")
//        ));
//
//        Map<String, String> expData = new HashMap<>();
//        expData.put("testExperiment", "true");
//
//        // When
//        SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
//                validSearchRoomsRequest, validHotelDetailsResponse, expData,
//                new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
//                validRequestDetails, validCommonModifierResponse);
//
//        // Then
//        assertNotNull("Response should not be null", response);
//        // Should handle transformation of multiple room types correctly
//    }

    // ==================== ADDITIONAL NEW TESTS FOR BETTER COVERAGE ====================

    @Test
    public void testGetPropertySellableType_RoomType_ShouldReturnRoom() {
        // Given
        HotelDetails hotelDetails = createValidHotelDetails();
        hotelDetails.setPropertyType("HOTEL");

        // When
        String result = (String) ReflectionTestUtils.invokeMethod(
                transformer, "buildPropertySellableType", hotelDetails);

        // Then
        assertNotNull("Result should not be null", result);
    }

    @Test
    public void testAmenitiesFilter_EmptyAmenityGroup_ShouldReturnEmptyList() {
        // Given
        AmenityGroup amenityGroup = new AmenityGroup();
        amenityGroup.setAmenities(new ArrayList<>());

        // When
        List<String> result = (List<String>) ReflectionTestUtils.invokeMethod(
                transformer, "amenitiesFilter", amenityGroup);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Should return empty list for empty amenities", result.isEmpty());
    }

    @Test
    public void testRoomTypeChecks_WithDifferentTypes_ShouldReturnCorrectly() {
        // Test different room types
        Rooms exactRoom = createRoomWithType("EXACT");
        Rooms packageRoom = createRoomWithType("SUPER_PACKAGE");
        Rooms occasionRoom = createRoomWithType("OCCASION_PACKAGE");

        // Test isPackageRoom logic through transformation
        assertNotNull("Exact room should be valid", exactRoom);
        assertNotNull("Package room should be valid", packageRoom);
        assertNotNull("Occasion room should be valid", occasionRoom);
    }

//    @Test
//    public void testConvertSearchRoomsResponse_WithExperimentData_ShouldHandleExperiments() {
//        // Given
//        Map<String, String> expData = new HashMap<>();
//        expData.put("testExperiment", "true");
//        expData.put("anotherExperiment", "false");
//
//        // When
//        SearchRoomsResponse response = transformer.convertSearchRoomsResponse(
//                validSearchRoomsRequest, validHotelDetailsResponse, expData,
//                new ArrayList<>(), validSearchCriteria, new ArrayList<>(),
//                validRequestDetails, validCommonModifierResponse);
//
//        // Then
//        assertNotNull("Response should not be null", response);
//        verify(utility, atLeastOnce()).isExperimentTrue(any(), anyString());
//    }
//
//    @Test
//    public void testTransformRoomsToRoomDetails_WithMixedRoomTypes_ShouldHandleAll() {
//        // Given
//        List<Rooms> rooms = Arrays.asList(
//                createRoomWithType("EXACT"),
//                createRoomWithType("SUPER_PACKAGE"),
//                createRoomWithType("RECOMMENDED")
//        );
//
//        // When
//        List<RoomDetails> result = (List<RoomDetails>) ReflectionTestUtils.invokeMethod(
//                transformer, "transformRoomsToRoomDetails", rooms,
//                validHotelDetailsResponse.getHotelDetails(), new HashMap<String, String>(),
//                "INR", "HOTELS", 2, 1, false, validCommonModifierResponse,
//                new HashMap<String, String>(), false, false);
//
//        // Then
//        assertNotNull("Result should not be null", result);
//        assertEquals("Should transform all room types", 3, result.size());
//    }

    // ==================== HELPER METHODS FOR TEST DATA CREATION ====================

    private void setupValidTestData() {
        validHotelDetailsResponse = createValidHotelDetailsResponse();
        validSearchRoomsRequest = createValidSearchRoomsRequest();
        validSearchCriteria = createValidSearchCriteria();
        validRequestDetails = createValidRequestDetails();
        validCommonModifierResponse = createValidCommonModifierResponse();
    }

    private HotelDetailsResponse createValidHotelDetailsResponse() {
        HotelDetailsResponse response = new HotelDetailsResponse();
        response.setHotelDetails(createValidHotelDetails());
        return response;
    }

    private HotelDetails createValidHotelDetails() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setPropertyType("HOTEL");
        hotelDetails.setListingType("ENTIRE");
        hotelDetails.setCategories(new HashSet<>());
        hotelDetails.setRooms(Arrays.asList(createValidRoom()));
        hotelDetails.setRoomCombos(new ArrayList<>());
        hotelDetails.setHotelRateFlags(createValidHotelRateFlags());
        hotelDetails.setMustReadRules(Arrays.asList("Rule 1", "Rule 2"));
        return hotelDetails;
    }

    private Rooms createValidRoom() {
        Rooms room = new Rooms();
        room.setCode("R001");
        room.setName("Deluxe Room");
        room.setDesc("Comfortable deluxe room");
        room.setType("EXACT");
        room.setSubType("STANDARD");
        room.setBaseRoom(true);
        room.setStaycationDeal(false);
        room.setRoomInfo(createValidRoomInfo());
        room.setRatePlans(Arrays.asList(createValidRatePlan()));
        room.setPersuasions(new HashMap<>());
        return room;
    }

    private Rooms createRoomWithType(String type) {
        Rooms room = createValidRoom();
        room.setType(type);
        return room;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo createValidRoomInfo() {
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo roomInfo =
                new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo();
        roomInfo.setMaxGuestCount(4);
        roomInfo.setMaxAdultCount(2);
        roomInfo.setMaxChildCount(2);
        roomInfo.setBedCount(1);
        roomInfo.setBedRoomCount(1);
        roomInfo.setExtraBedCount(0);
        roomInfo.setRoomSize("25 sqm");
        roomInfo.setSellableType("ROOM");
        roomInfo.setRoomViewName("City View");
        roomInfo.setRoomFlags(createValidRoomFlags());
        roomInfo.setRoomArrangementMap(createValidRoomArrangementMap());
        roomInfo.setHighlightedAmenities(Arrays.asList(createValidAmenityGroup()));
        return roomInfo;
    }

    private RoomFlags createValidRoomFlags() {
        RoomFlags flags = new RoomFlags();
        flags.setMasterRoom(true);
        return flags;
    }

    private Map<String, List<ArrangementInfo>> createValidRoomArrangementMap() {
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();

        List<ArrangementInfo> bedArrangements = new ArrayList<>();
        ArrangementInfo bedInfo = new ArrangementInfo();
        bedInfo.setType("King Bed");
        bedInfo.setCount(1);
        bedArrangements.add(bedInfo);

        arrangementMap.put("BEDS", bedArrangements);
        return arrangementMap;
    }

    private AmenityGroup createValidAmenityGroup() {
        AmenityGroup amenityGroup = new AmenityGroup();
        amenityGroup.setAmenities(Arrays.asList(createValidAmenity()));
        return amenityGroup;
    }

    private AmenityGroup createAmenityGroupWithFilterableAmenities() {
        AmenityGroup amenityGroup = new AmenityGroup();

        Amenity balconyAmenity = new Amenity();
        balconyAmenity.setAttributeName("Balcony");

        Amenity wifiAmenity = new Amenity();
        wifiAmenity.setAttributeName("Free WiFi");

        amenityGroup.setAmenities(Arrays.asList(balconyAmenity, wifiAmenity));
        return amenityGroup;
    }

    private Amenity createValidAmenity() {
        Amenity amenity = new Amenity();
        amenity.setAttributeName("Free WiFi");
        amenity.setDisplayType("1");
        return amenity;
    }

    private RatePlan createValidRatePlan() {
        RatePlan ratePlan = new RatePlan();
        ratePlan.setCode("RP001");
        ratePlan.setDescription("Standard Rate");
        ratePlan.setSegmentId("1120");
        ratePlan.setPrice(createValidPriceDetail());
        ratePlan.setAvailDetail(createValidAvailDetails());
        return ratePlan;
    }

    private PriceDetail createValidPriceDetail() {
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setPricePerOccupancy(Arrays.asList(createValidOccupancyDetails()));
        priceDetail.setCurrencyConvertor(1.0);
        return priceDetail;
    }

    private OccupancyDetails createValidOccupancyDetails() {
        OccupancyDetails occupancyDetails = new OccupancyDetails();
        occupancyDetails.setAdult(2);
        occupancyDetails.setChild(0);
        occupancyDetails.setNumberOfRooms(1);
        occupancyDetails.setRatePlanCode("RP001");
        occupancyDetails.setChildAges(new ArrayList<>());
        return occupancyDetails;
    }

    private AvailDetails createValidAvailDetails() {
        AvailDetails availDetails = new AvailDetails();
        availDetails.setOccupancyDetails(createValidOccupancyDetails());
        availDetails.setCount(10);
        return availDetails;
    }

    private HotelRateFlags createValidHotelRateFlags() {
        HotelRateFlags flags = new HotelRateFlags();
        flags.setAltAcco(false);
        flags.setSpotlightApplicable(false);
        return flags;
    }

    private SearchRoomsRequest createValidSearchRoomsRequest() {
        SearchRoomsRequest request = new SearchRoomsRequest();
        return request;
    }

    private SearchCriteria createValidSearchCriteria() {
        SearchCriteria criteria = new SearchCriteria();
        criteria.setCheckIn("2024-01-15");
        criteria.setCheckOut("2024-01-17");
        criteria.setCurrency("INR");
        return criteria;
    }

    private RequestDetails createValidRequestDetails() {
        RequestDetails details = new RequestDetails();
        details.setFunnelSource("HOTELS");
        return details;
    }

    private CommonModifierResponse createValidCommonModifierResponse() {
        CommonModifierResponse response = new CommonModifierResponse();
        response.setExpDataMap(new LinkedHashMap<String, String>());
        return response;
    }

    private RoomViewFilterConfig createRoomViewFilterConfig() {
        RoomViewFilterConfig config = new RoomViewFilterConfig();
        config.setFilterCode("BALCONY");
        return config;
    }

    // Mock response helper objects
    private RoomHighlight createBasicRoomHighlight() {
        RoomHighlight highlight = new RoomHighlight();
        highlight.setText("Free WiFi");
        highlight.setIconUrl("http://test.com/wifi-icon.png");
        return highlight;
    }

    private BookedCancellationPolicy createBasicCancellationPolicy() {
        BookedCancellationPolicy policy = new BookedCancellationPolicy();
        policy.setText("NR");
        policy.setType(BookedCancellationPolicyType.FC);
        return policy;
    }

    private RoomDetails createBasicRoomDetails() {
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R001");
        roomDetails.setRoomName("Test Room");
        roomDetails.setRatePlans(Arrays.asList(createBasicSelectRoomRatePlan()));
        roomDetails.setFilterCode(Arrays.asList("PRIVATE_ROOMS"));
        return roomDetails;
    }

    private SelectRoomRatePlan createBasicSelectRoomRatePlan() {
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setRpc("RP001");
        ratePlan.setName("Standard Rate");
        ratePlan.setSellableType("ROOM");
        ratePlan.setCanTranslate(false);
        return ratePlan;
    }

    private GroupRatePlanFilter createBasicGroupRatePlanFilter() {
        GroupRatePlanFilter filter = new GroupRatePlanFilter();
        filter.setText("Test Filter");
        filter.setGroupApplicable(true);
        filter.setRatePlanFilterList(new ArrayList<>());
        return filter;
    }
}