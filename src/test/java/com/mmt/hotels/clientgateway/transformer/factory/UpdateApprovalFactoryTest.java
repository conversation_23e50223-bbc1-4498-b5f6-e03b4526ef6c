package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.UpdateApprovalRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.UpdateApprovalRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.UpdateApprovalRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.UpdateApprovalRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdateApprovalRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.UpdateApprovalResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.UpdateApprovalResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.UpdateApprovalResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.UpdateApprovalResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdateApprovalResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class UpdateApprovalFactoryTest {

    @InjectMocks
    UpdateApprovalFactory updateApprovalFactory;

    @Before
    public void init() {
        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalRequestTransformerPWA", new UpdateApprovalRequestTransformerPWA());
        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalRequestTransformerDesktop", new UpdateApprovalRequestTransformerDesktop());
        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalRequestTransformerAndroid", new UpdateApprovalRequestTransformerAndroid());
        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalRequestTransformerIOS", new UpdateApprovalRequestTransformerIOS());

        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalResponseTransformerPWA", new UpdateApprovalResponseTransformerPWA());
        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalResponseTransformerDesktop", new UpdateApprovalResponseTransformerDesktop());
        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalResponseTransformerAndroid", new UpdateApprovalResponseTransformerAndroid());
        ReflectionTestUtils.setField(updateApprovalFactory, "updateApprovalResponseTransformerIOS", new UpdateApprovalResponseTransformerIOS());
    }


    @Test
    public void getRequestServiceTest() {
        UpdateApprovalRequestTransformer resp = updateApprovalFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof UpdateApprovalRequestTransformerPWA);
        resp = updateApprovalFactory.getRequestService("MSITE");
        Assert.assertTrue(resp instanceof UpdateApprovalRequestTransformerPWA);
        resp = updateApprovalFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof UpdateApprovalRequestTransformerDesktop);
        resp = updateApprovalFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof UpdateApprovalRequestTransformerAndroid);
        resp = updateApprovalFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof UpdateApprovalRequestTransformerIOS);
        resp = updateApprovalFactory.getRequestService("");
        Assert.assertTrue(resp instanceof UpdateApprovalRequestTransformerDesktop);
    }

    @Test
    public void getResponseService() {
        UpdateApprovalResponseTransformer resp = updateApprovalFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof UpdateApprovalResponseTransformerPWA);
        resp = updateApprovalFactory.getResponseService("MSITE");
        Assert.assertTrue(resp instanceof UpdateApprovalResponseTransformerPWA);
        resp = updateApprovalFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof UpdateApprovalResponseTransformerDesktop);
        resp = updateApprovalFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof UpdateApprovalResponseTransformerAndroid);
        resp = updateApprovalFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof UpdateApprovalResponseTransformerIOS);
        resp = updateApprovalFactory.getResponseService("");
        Assert.assertTrue(resp instanceof UpdateApprovalResponseTransformerDesktop);
    }
}
