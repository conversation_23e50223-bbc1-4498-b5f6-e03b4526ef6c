package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.RoomInfoResponseTransformer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RoomInfoFactoryTest {
    @InjectMocks
    private RoomInfoFactory roomInfoFactory;

    @Mock
    private RoomInfoResponseTransformer roomInfoResponseTransformer;

    @Test
    public void testGetResponseService(){
        Assert.assertNotNull(roomInfoFactory.getResponseService("PWA"));
    }
}
