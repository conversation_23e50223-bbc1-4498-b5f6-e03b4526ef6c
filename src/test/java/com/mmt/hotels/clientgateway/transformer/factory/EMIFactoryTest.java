package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.EMIRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.ThankYouResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.ThankYouResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.ThankYouResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ThankYouResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class EMIFactoryTest {

    @InjectMocks
    EMIFactory emiFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(emiFactory,"emiRequestTransformer" , new EMIRequestTransformer());

    }

    @Test
    public void getResponseServiceTest(){
        EMIRequestTransformer resp = emiFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof EMIRequestTransformer  );
        resp = emiFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof EMIRequestTransformer  );
        resp = emiFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof EMIRequestTransformer  );
        resp = emiFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof EMIRequestTransformer  );
        resp = emiFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(emiFactory.getRequestService("test") instanceof  EMIRequestTransformer);
    }
}
