package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.SearchRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchRoomsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.SearchRoomsRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.SearchRoomsRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SearchRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchRoomsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchRoomsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformerPWA;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsFactoryTest {

    @InjectMocks
    SearchRoomsFactory searchRoomsFactory;

    @Before
    public void init(){
        // Setup regular request transformers
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerPWA" , new SearchRoomsRequestTransformerPWA());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerDesktop" , new SearchRoomsRequestTransformerDesktop());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerAndroid" , new SearchRoomsRequestTransformerAndroid());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerIOS" , new SearchRoomsRequestTransformerIOS());
        
        // Setup regular response transformers
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerPWA" , new SearchRoomsResponseTransformerPWA());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerDesktop" , new SearchRoomsResponseTransformerDesktop());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerAndroid" , new SearchRoomsResponseTransformerAndroid());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerIOS" , new SearchRoomsResponseTransformerIOS());
        
        // Setup orchestrator response transformers
        ReflectionTestUtils.setField(searchRoomsFactory,"orchSearchRoomsResponseTransformerPWA" , new OrchSearchRoomsResponseTransformerPWA());
        ReflectionTestUtils.setField(searchRoomsFactory,"orchSearchRoomsResponseTransformerDesktop" , new OrchSearchRoomsResponseTransformerDesktop());
        ReflectionTestUtils.setField(searchRoomsFactory,"orchSearchRoomsResponseTransformerAndroid" , new OrchSearchRoomsResponseTransformerAndroid());
        ReflectionTestUtils.setField(searchRoomsFactory,"orchSearchRoomsResponseTransformerIOS" , new OrchSearchRoomsResponseTransformerIOS());
    }
    
    @Test
    public void getRequestServiceTest(){
        SearchRoomsRequestTransformer resp = searchRoomsFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerPWA  );
        resp = searchRoomsFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerDesktop  );
        resp = searchRoomsFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerAndroid  );
        resp = searchRoomsFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerIOS  );
        resp = searchRoomsFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(searchRoomsFactory.getRequestService("test") instanceof  SearchRoomsRequestTransformerDesktop);
    }

    @Test
    public void getResponseServiceTest(){
        SearchRoomsResponseTransformer resp = searchRoomsFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerPWA  );
        resp = searchRoomsFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerDesktop  );
        resp = searchRoomsFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerAndroid  );
        resp = searchRoomsFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerIOS  );
        resp = searchRoomsFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(searchRoomsFactory.getResponseService("test") instanceof  SearchRoomsResponseTransformerDesktop);
    }

    @Test
    public void getSearchRoomsResponseServiceTest(){
        // Test PWA client
        OrchSearchRoomsResponseTransformer resp = searchRoomsFactory.getSearchRoomsResponseService("PWA");
        Assert.assertNotNull("PWA transformer should not be null", resp);
        Assert.assertTrue("PWA should return OrchSearchRoomsResponseTransformerPWA", resp instanceof OrchSearchRoomsResponseTransformerPWA);
        
        // Test MSITE client (should return PWA transformer)
        resp = searchRoomsFactory.getSearchRoomsResponseService("MSITE");
        Assert.assertNotNull("MSITE transformer should not be null", resp);
        Assert.assertTrue("MSITE should return OrchSearchRoomsResponseTransformerPWA", resp instanceof OrchSearchRoomsResponseTransformerPWA);
        
        // Test DESKTOP client
        resp = searchRoomsFactory.getSearchRoomsResponseService("DESKTOP");
        Assert.assertNotNull("DESKTOP transformer should not be null", resp);
        Assert.assertTrue("DESKTOP should return OrchSearchRoomsResponseTransformerDesktop", resp instanceof OrchSearchRoomsResponseTransformerDesktop);
        
        // Test ANDROID client
        resp = searchRoomsFactory.getSearchRoomsResponseService("ANDROID");
        Assert.assertNotNull("ANDROID transformer should not be null", resp);
        Assert.assertTrue("ANDROID should return OrchSearchRoomsResponseTransformerAndroid", resp instanceof OrchSearchRoomsResponseTransformerAndroid);
        
        // Test IOS client
        resp = searchRoomsFactory.getSearchRoomsResponseService("IOS");
        Assert.assertNotNull("IOS transformer should not be null", resp);
        Assert.assertTrue("IOS should return OrchSearchRoomsResponseTransformerIOS", resp instanceof OrchSearchRoomsResponseTransformerIOS);
        
        // Test empty string client (should default to DESKTOP)
        resp = searchRoomsFactory.getSearchRoomsResponseService("");
        Assert.assertNotNull("Empty client transformer should not be null", resp);
        Assert.assertTrue("Empty client should return OrchSearchRoomsResponseTransformerDesktop", resp instanceof OrchSearchRoomsResponseTransformerDesktop);
        
        // Test null client (should default to DESKTOP)
        resp = searchRoomsFactory.getSearchRoomsResponseService(null);
        Assert.assertNotNull("Null client transformer should not be null", resp);
        Assert.assertTrue("Null client should return OrchSearchRoomsResponseTransformerDesktop", resp instanceof OrchSearchRoomsResponseTransformerDesktop);
        
        // Test unknown client (should default to DESKTOP)
        resp = searchRoomsFactory.getSearchRoomsResponseService("UNKNOWN");
        Assert.assertNotNull("Unknown client transformer should not be null", resp);
        Assert.assertTrue("Unknown client should return OrchSearchRoomsResponseTransformerDesktop", resp instanceof OrchSearchRoomsResponseTransformerDesktop);
        
        // Test case sensitivity (lowercase should default to DESKTOP)
        resp = searchRoomsFactory.getSearchRoomsResponseService("pwa");
        Assert.assertNotNull("Lowercase client transformer should not be null", resp);
        Assert.assertTrue("Lowercase client should return OrchSearchRoomsResponseTransformerDesktop", resp instanceof OrchSearchRoomsResponseTransformerDesktop);
    }
}
