package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.RoomInfoRequest;
import com.mmt.hotels.clientgateway.response.BNPLDetails;
import com.mmt.hotels.clientgateway.response.CancellationTimeline;
import com.mmt.hotels.clientgateway.response.ExtraGuestInfo;
import com.mmt.hotels.clientgateway.response.RoomInfoResponse;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.rooms.Tariff;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.pricing.AvailDetails;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.RatePolicy;
import com.mmt.hotels.model.response.staticdata.VideoInfo;
import com.mmt.hotels.model.response.txn.*;
import com.mmt.model.BathroomArrangement;
import com.mmt.model.ReviewRoomInfo;
import com.mmt.model.SleepingArrangement;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;


import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RoomInfoResponseTransformerTest {

    @Mock
    private DateUtil dateUtil;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Utility utility;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private RoomInfoResponseTransformer roomInfoResponseTransformer;

    private Gson gson;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "dateUtil", dateUtil);
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "mealPlanMapPolyglot", new HashMap<>());
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "commonResponseTransformer", commonResponseTransformer);
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "utility", utility);
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "polyglotService", polyglotService);
        gson = new Gson();

        // Default mock configurations using lenient() to avoid UnnecessaryStubbingException
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated");
        lenient().when(utility.transformInclusions(any(), any(), any(), anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyInt())).thenReturn(Collections.emptyList());
        lenient().when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(5);
    }

    @Test
    public void testRoomHighlights() throws IOException, ParseException {
        // Create test data
        ReviewRoomInfo roomInfo = new ReviewRoomInfo();
        roomInfo.setRoomSize("300 sq.ft");
        roomInfo.setRoomViewName("City View");

        // Create room summary
        RoomSummary roomSummary = new RoomSummary();
        roomSummary.setTopRated(true);
        roomInfo.setRoomSummary(roomSummary);

        // Create bathroom arrangement
        BathroomArrangement bathroom = new BathroomArrangement();
        bathroom.setCount(2);
        roomInfo.setBathrooms(Collections.singletonList(bathroom));

        // Create bed
        SleepingArrangement bed = new SleepingArrangement();
        bed.setCount(1);
        bed.setType("King");
        roomInfo.setBeds(Collections.singletonList(bed));

        // Create tariff info
        PersistedTariffInfo tariff = new PersistedTariffInfo();
        tariff.setRoomDetails(roomInfo);
        tariff.setRoomCode("ROOM1");
        tariff.setRatePlanCode("RATE1");
        tariff.setRoomTypeName("Deluxe Room");

        // Create hotel
        PersistedHotel hotel = new PersistedHotel();
        hotel.setTariffInfoList(Collections.singletonList(tariff));

        // Create persisted data
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(Collections.singletonList(hotel));

        // Create txn data
        PersistanceMultiRoomResponseEntity txnData = new PersistanceMultiRoomResponseEntity();
        txnData.setPersistedData(persistedData);

        // Create request
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("ROOM1");
        request.setRatePlanCode("RATE1");

        // Test room highlights transformation
        RoomInfoResponse response = roomInfoResponseTransformer.transformRoomInfoResponses(request, txnData);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getRoomInfo());
    }

    @Test
    public void testRoomHighlightsGetter() throws IOException, ParseException {
        // Create test data
        ReviewRoomInfo roomInfo = new ReviewRoomInfo();
        roomInfo.setRoomSize("300 sq.ft");
        roomInfo.setRoomViewName("City View");

        List<RoomHighlight> list = roomInfoResponseTransformer.getRoomHighlights(roomInfo, true);
        Assert.assertNotNull(list);
    }

    @Test
    public void testRoomHighlightsWithNullValues() throws IOException, ParseException {
        // Create test data with null values
        ReviewRoomInfo roomInfo = new ReviewRoomInfo();
        roomInfo.setRoomSize(null);
        roomInfo.setRoomViewName(null);

        // Create room summary with null values
        RoomSummary roomSummary = new RoomSummary();
        roomSummary.setTopRated(false);
        roomInfo.setRoomSummary(roomSummary);

        // Create tariff info
        PersistedTariffInfo tariff = new PersistedTariffInfo();
        tariff.setRoomDetails(roomInfo);
        tariff.setRoomCode("ROOM1");
        tariff.setRatePlanCode("RATE1");
        tariff.setRoomTypeName(null);

        // Create hotel
        PersistedHotel hotel = new PersistedHotel();
        hotel.setTariffInfoList(Collections.singletonList(tariff));

        // Create persisted data
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(Collections.singletonList(hotel));

        // Create txn data
        PersistanceMultiRoomResponseEntity txnData = new PersistanceMultiRoomResponseEntity();
        txnData.setPersistedData(persistedData);

        // Create request
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("ROOM1");
        request.setRatePlanCode("RATE1");

        // Test room highlights transformation with null values
        RoomInfoResponse response = roomInfoResponseTransformer.transformRoomInfoResponses(request, txnData);
        Assert.assertNotNull(response);
        Assert.assertNull(response.getRoomSize());
        Assert.assertNull(response.getRoomViewName());
    }

    @Test
    public void testRoomHighlightsWithEmptyLists() throws IOException, ParseException {
        // Create test data with empty lists
        ReviewRoomInfo roomInfo = new ReviewRoomInfo();
        roomInfo.setBathrooms(new ArrayList<>());
        roomInfo.setBeds(new ArrayList<>());
        roomInfo.setRoomSize("300 sq.ft");
        roomInfo.setRoomViewName("City View");
        roomInfo.setRoomSummary(new RoomSummary());

        // Create tariff info
        PersistedTariffInfo tariff = new PersistedTariffInfo();
        tariff.setRoomDetails(roomInfo);
        tariff.setRoomCode("ROOM1");
        tariff.setRatePlanCode("RATE1");
        tariff.setRoomTypeName("Deluxe Room");

        // Create hotel
        PersistedHotel hotel = new PersistedHotel();
        hotel.setTariffInfoList(Collections.singletonList(tariff));

        // Create persisted data
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(Collections.singletonList(hotel));

        // Create txn data
        PersistanceMultiRoomResponseEntity txnData = new PersistanceMultiRoomResponseEntity();
        txnData.setPersistedData(persistedData);

        // Create request
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("ROOM1");
        request.setRatePlanCode("RATE1");

        // Test room highlights transformation with empty lists
        RoomInfoResponse response = roomInfoResponseTransformer.transformRoomInfoResponses(request, txnData);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getRoomInfo());

    }

    @Test
    public void testRoomHighlightsWithMultipleBedsAndBathrooms() throws IOException, ParseException {
        // Create test data with multiple beds and bathrooms
        ReviewRoomInfo roomInfo = new ReviewRoomInfo();
        roomInfo.setRoomSize("400 sq.ft");
        roomInfo.setRoomViewName("Sea View");

        // Create multiple bathrooms
        List<BathroomArrangement> bathrooms = new ArrayList<>();
        BathroomArrangement bathroom1 = new BathroomArrangement();
        bathroom1.setCount(1);
        bathrooms.add(bathroom1);
        BathroomArrangement bathroom2 = new BathroomArrangement();
        bathroom2.setCount(1);
        bathrooms.add(bathroom2);
        roomInfo.setBathrooms(bathrooms);

        // Create multiple beds
        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed1 = new SleepingArrangement();
        bed1.setCount(1);
        bed1.setType("King");
        beds.add(bed1);
        SleepingArrangement bed2 = new SleepingArrangement();
        bed2.setCount(2);
        bed2.setType("Queen");
        beds.add(bed2);
        roomInfo.setBeds(beds);

        // Create tariff info
        PersistedTariffInfo tariff = new PersistedTariffInfo();
        tariff.setRoomDetails(roomInfo);
        tariff.setRoomCode("ROOM1");
        tariff.setRatePlanCode("RATE1");
        tariff.setRoomTypeName("Suite Room");

        // Create hotel
        PersistedHotel hotel = new PersistedHotel();
        hotel.setTariffInfoList(Collections.singletonList(tariff));

        // Create persisted data
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(Collections.singletonList(hotel));

        // Create txn data
        PersistanceMultiRoomResponseEntity txnData = new PersistanceMultiRoomResponseEntity();
        txnData.setPersistedData(persistedData);

        // Create request
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("ROOM1");
        request.setRatePlanCode("RATE1");

        // Test room highlights transformation with multiple beds and bathrooms
        RoomInfoResponse response = roomInfoResponseTransformer.transformRoomInfoResponses(request, txnData);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getRoomInfo());
        Assert.assertNotNull(response.getRoomInfo().getHighlightedAmenities());
        Assert.assertNotNull(response.getRoomInfo().getBeds());

        Assert.assertNotNull(response.getRoomInfo().getRoomSize());
        Assert.assertNotNull(response.getRoomInfo().getRatePlans());
        Assert.assertNotNull(response.getRoomInfo().getRatePlans().get(0));
        Assert.assertNotNull(response.getRoomInfo().getRatePlans().get(0).getInclusionsList());
    }

    @Test
    public void testGetBnplDetails() {
        // Test with null DisplayFare
        BNPLDetails result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getBnplDetails", (DisplayFare)null);
        assertNull(result);

        // Test with valid DisplayFare
        DisplayFare displayFare = new DisplayFare();
        displayFare.setIsBNPLApplicable(true);
        displayFare.setBnplPersuasionMsg("BNPL message");
        displayFare.setBnplPolicyText("BNPL policy");
        displayFare.setOriginalBNPL(true);
        displayFare.setBnplVariant(BNPLVariant.BNPL_AT_0);

        // Create a BNPLDetails with the expected properties
        BNPLDetails mockBnplDetails = new BNPLDetails();
        mockBnplDetails.setBnplApplicable(true);
        mockBnplDetails.setBnplPersuasionMsg("BNPL message");
        mockBnplDetails.setBnplPolicyText("BNPL policy");

        when(commonResponseTransformer.buildBNPLDetails(
                eq(true), eq("BNPL message"), eq("BNPL policy"), 
                isNull(), isNull(), eq(true), eq(false), 
                eq(BNPLVariant.BNPL_AT_0), isNull(), isNull(), eq(0.0)))
                .thenReturn(mockBnplDetails);

        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getBnplDetails", displayFare);
        
        assertNotNull(result);
        assertTrue(result.isBnplApplicable());
        assertEquals("BNPL message", result.getBnplPersuasionMsg());
        assertEquals("BNPL policy", result.getBnplPolicyText());
    }

    @Test
    public void testConvertSpaceData() {
        // Test with null input
        SpaceData result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "convertSpaceData", 
                (com.mmt.hotels.model.response.staticdata.SpaceData)null);
        assertNull(result);

        // Test with valid input
        com.mmt.hotels.model.response.staticdata.SpaceData spaceData = new com.mmt.hotels.model.response.staticdata.SpaceData();
        ArrayList<String> list = new ArrayList<>();
        list.add("Space 1");
        spaceData.setDescriptive(list);

        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "convertSpaceData", spaceData);
        
        assertNotNull(result);
        assertEquals(list, result.getDescriptive());
    }

    @Test
    public void testBuildMedia() {
        // Test with null inputs
        List<MediaData> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "buildMedia", 
                null, null);
        assertTrue(CollectionUtils.isEmpty(result));

        // Test with only images
        List<String> images = Arrays.asList("image1.jpg", "image2.jpg");
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "buildMedia", images, null);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
        assertEquals("image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(1).getMediaType());
        assertEquals("image2.jpg", result.get(1).getUrl());

        // Test with only videos
        List<VideoInfo> videos = new ArrayList<>();
        VideoInfo video = new VideoInfo();
        video.setUrl("video1.mp4");
        video.setThumbnailUrl("thumbnail1.jpg");
        video.setText("Video 1");
        videos.add(video);
        
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "buildMedia", null, videos);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Constants.VIDEO_TYPE, result.get(0).getMediaType());
        assertEquals("video1.mp4", result.get(0).getUrl());
        assertEquals("thumbnail1.jpg", result.get(0).getThumbnailUrl());
        assertEquals("Video 1", result.get(0).getText());

        // Test with both images and videos
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "buildMedia", images, videos);
        
        assertNotNull(result);
        assertEquals(3, result.size());  // 1 video + 2 images
        assertEquals(Constants.VIDEO_TYPE, result.get(0).getMediaType());
        assertEquals(Constants.IMAGE_TYPE, result.get(1).getMediaType());
        assertEquals(Constants.IMAGE_TYPE, result.get(2).getMediaType());
    }

    @Test
    public void testGetExtraGuestInfo() {
        // Test with null input
        ExtraGuestInfo result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getExtraGuestInfo", 
                (String)null);
        assertNull(result);

        // Test with valid input
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Extra Guest Info");
        String extraGuestInfoText = "Extra beds available#Extra beds are available at an additional cost";
        
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getExtraGuestInfo", extraGuestInfoText);
        
        assertNotNull(result);
        assertEquals("Extra Guest Info", result.getExtraGuestInfoHeading());
        assertEquals("Extra beds are available at an additional cost", result.getExtraGuestInfoDscr());
    }

    @Test
    public void testGetRoomDescription() {
        // Create test with null hotel info
        PersistanceMultiRoomResponseEntity txnData = new PersistanceMultiRoomResponseEntity();
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(new ArrayList<>());
        txnData.setPersistedData(persistedData);
        
        String result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getRoomDescription", txnData);
        assertNull(result);

        // Create test with valid hotel info
        PersistedHotel hotel = new PersistedHotel();
        HotelInfo hotelInfo = new HotelInfo();
        hotelInfo.setRoomDescription("Spacious rooms with modern amenities");
        hotel.setHotelInfo(hotelInfo);
        persistedData.setHotelList(Collections.singletonList(hotel));
        
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getRoomDescription", txnData);
        assertEquals("Spacious rooms with modern amenities", result);
    }

    @Test
    public void testGetPropertyPolicy() {
        // Create test with null hotel info
        PersistanceMultiRoomResponseEntity txnData = new PersistanceMultiRoomResponseEntity();
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(new ArrayList<>());
        txnData.setPersistedData(persistedData);
        
        List<String> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getPropertyPolicy", txnData);
        assertNull(result);

        // Create test with valid hotel info but no property policy
        PersistedHotel hotel = new PersistedHotel();
        HotelInfo hotelInfo = new HotelInfo();
        Map<String, List<String>> policyMap = new HashMap<>();
        hotelInfo.setPolicyToMessagesMap(policyMap);
        hotel.setHotelInfo(hotelInfo);
        persistedData.setHotelList(Collections.singletonList(hotel));
        
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getPropertyPolicy", txnData);
        assertNull(result);

        // Create test with valid property policy
        List<String> policies = Arrays.asList("No pets allowed", "Check-in time: 2:00 PM");
        policyMap.put("Property Policy", policies);
        
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getPropertyPolicy", txnData);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("No pets allowed", result.get(0));
        assertEquals("Check-in time: 2:00 PM", result.get(1));
    }

    @Test
    public void testTransformRoomInfoResponse() {
        // Setup test data
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("ROOM1");
        request.setRatePlanCode("RATE1");

        // Create room info
        ReviewRoomInfo roomInfo = new ReviewRoomInfo();
        roomInfo.setRoomSize("300 sq.ft");
        roomInfo.setMaxGuestCount(2);
        roomInfo.setMaxAdultCount(2);
        roomInfo.setMaxChildCount(0);
        roomInfo.setRoomViewName("City View");
        roomInfo.setImages(new LinkedList<>(Arrays.asList("//image1.jpg", "//image2.jpg")));
        roomInfo.setRoomSummary(new RoomSummary());

        // Create tariff info
        PersistedTariffInfo tariff = new PersistedTariffInfo();
        tariff.setRoomDetails(roomInfo);
        tariff.setRoomCode("ROOM1");
        tariff.setRatePlanCode("RATE1");

        // Create hotel
        PersistedHotel hotel = new PersistedHotel();
        hotel.setTariffInfoList(Collections.singletonList(tariff));

        // Create persisted data
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(Collections.singletonList(hotel));

        // Create txn data
        PersistanceMultiRoomResponseEntity txnData = new PersistanceMultiRoomResponseEntity();
        txnData.setPersistedData(persistedData);

        // Use lenient for mocks that might not be used in all test runs
        lenient().when(utility.transformCancellationPolicy(any(), anyBoolean(), any(), anyString(), anyString(), anyInt(),any()))
            .thenReturn(null);

        // Test the method
        RoomInfoResponse response = roomInfoResponseTransformer.transformRoomInfoResponse(request, txnData);
        
        assertNotNull(response);
        assertEquals("300 sq.ft", response.getRoomSize());
        assertEquals("City View", response.getRoomViewName());
        assertEquals(2, response.getMaxGuestCount());
        assertEquals(2, response.getMaxAdultCount());
        assertEquals(0, response.getMaxChildCount());
        assertNotNull(response.getImages());
        assertEquals(2, response.getImages().size());
        assertTrue(response.getImages().get(0).startsWith("//image"));
    }

    @Test
    public void testGetConfirmationPolicy() {
        // Test with empty tariff list
        List<PersistedTariffInfo> tariffs = new ArrayList<>();
        RatePolicy result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getConfirmationPolicy", tariffs);
        assertNull(result);

        // Test with single tariff with null policy
        PersistedTariffInfo tariff1 = new PersistedTariffInfo();
        tariffs.add(tariff1);
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getConfirmationPolicy", tariffs);
        assertNull(result);

        // Test with single tariff with policy
        RatePolicy policy1 = new RatePolicy();
        policy1.setValue("POLICY1");
        policy1.setMostRestrictive("false");
        tariff1.setConfirmationPolicy(policy1);
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getConfirmationPolicy", tariffs);
        assertNotNull(result);
        assertEquals("POLICY1", result.getValue());

        // Clear the list and create a new test case with multiple tariffs
        tariffs.clear();
        
        // Add tariff with non-restrictive policy first
        PersistedTariffInfo tariff3 = new PersistedTariffInfo();
        RatePolicy policy3 = new RatePolicy();
        policy3.setValue("POLICY1");
        policy3.setMostRestrictive("false");
        tariff3.setConfirmationPolicy(policy3);
        tariffs.add(tariff3);
        
        // Add tariff with most restrictive policy
        PersistedTariffInfo tariff2 = new PersistedTariffInfo();
        RatePolicy policy2 = new RatePolicy();
        policy2.setValue("POLICY2");
        policy2.setMostRestrictive("Y");
        tariff2.setConfirmationPolicy(policy2);
        tariffs.add(tariff2);
        
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getConfirmationPolicy", tariffs);
        assertNotNull(result);
        assertEquals("POLICY2", result.getValue());
        
        // Test with restrictive policy first in the list
        tariffs.clear();
        tariffs.add(tariff2); // Add restrictive policy first
        tariffs.add(tariff3); // Add non-restrictive policy second
        
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getConfirmationPolicy", tariffs);
        assertNotNull(result);
        assertEquals("POLICY2", result.getValue());
    }

    @Test
    public void testGetTariffs_withValidData() {
        // Create test data for RoomTariff list
        List<com.mmt.hotels.model.response.pricing.RoomTariff> roomTariffList = new ArrayList<>();
        
        com.mmt.hotels.model.response.pricing.RoomTariff roomTariff1 = new com.mmt.hotels.model.response.pricing.RoomTariff();
        roomTariff1.setNumberOfAdults(2);
        roomTariff1.setNumberOfChildren(1);
        roomTariffList.add(roomTariff1);
        
        com.mmt.hotels.model.response.pricing.RoomTariff roomTariff2 = new com.mmt.hotels.model.response.pricing.RoomTariff();
        roomTariff2.setNumberOfAdults(1);
        roomTariff2.setNumberOfChildren(0);
        roomTariffList.add(roomTariff2);
        
        // Create AvailDetails with OccupancyDetails
        AvailDetails availDetails = new AvailDetails();
        com.mmt.hotels.model.response.pricing.OccupancyDetails occupancyDetails = 
            new com.mmt.hotels.model.response.pricing.OccupancyDetails();
        occupancyDetails.setAdult(3);
        occupancyDetails.setChild(1);
        occupancyDetails.setNumOfRooms(2);
        availDetails.setOccupancyDetails(occupancyDetails);
        
        // Call the private method using reflection
        List<Tariff> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getTariffs", roomTariffList, availDetails);
        
        // Assertions
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Tariff tariff = result.get(0);
        assertNotNull(tariff.getRoomTariffs());
        assertEquals(2, tariff.getRoomTariffs().size());
        
        // Check first room tariff
        RoomTariff rt1 = tariff.getRoomTariffs().get(0);
        assertEquals(Integer.valueOf(2), rt1.getNumberOfAdults());
        assertEquals(Integer.valueOf(1), rt1.getNumberOfChildren());
        
        // Check second room tariff
        RoomTariff rt2 = tariff.getRoomTariffs().get(1);
        assertEquals(Integer.valueOf(1), rt2.getNumberOfAdults());
        assertEquals(Integer.valueOf(0), rt2.getNumberOfChildren());
        
        // Check occupancy details
        assertNotNull(tariff.getOccupancydetails());
        assertEquals(Integer.valueOf(2), tariff.getOccupancydetails().getRoomCount());
        assertEquals(Integer.valueOf(3), tariff.getOccupancydetails().getNumberOfAdults());
        assertEquals(Integer.valueOf(1), tariff.getOccupancydetails().getNumberOfChildren());
    }
    
    @Test
    public void testGetTariffs_withNullAvailDetails() {
        // Create test data for RoomTariff list
        List<com.mmt.hotels.model.response.pricing.RoomTariff> roomTariffList = new ArrayList<>();
        
        com.mmt.hotels.model.response.pricing.RoomTariff roomTariff = new com.mmt.hotels.model.response.pricing.RoomTariff();
        roomTariff.setNumberOfAdults(2);
        roomTariff.setNumberOfChildren(0);
        roomTariffList.add(roomTariff);
        
        // Call the private method with null availDetails
        List<Tariff> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getTariffs", roomTariffList, null);
        
        // Assertions
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Tariff tariff = result.get(0);
        assertNotNull(tariff.getRoomTariffs());
        assertEquals(1, tariff.getRoomTariffs().size());
        
        RoomTariff rt = tariff.getRoomTariffs().get(0);
        assertEquals((Integer) 2, rt.getNumberOfAdults());
        assertEquals((Integer) 0, rt.getNumberOfChildren());
        
        // Occupancy details should be null when availDetails is null
        assertNull(tariff.getOccupancydetails());
    }
    
    @Test
    public void testGetTariffs_withNullOccupancyDetails() {
        // Create test data for RoomTariff list
        List<com.mmt.hotels.model.response.pricing.RoomTariff> roomTariffList = new ArrayList<>();
        
        com.mmt.hotels.model.response.pricing.RoomTariff roomTariff = new com.mmt.hotels.model.response.pricing.RoomTariff();
        roomTariff.setNumberOfAdults(1);
        roomTariff.setNumberOfChildren(2);
        roomTariffList.add(roomTariff);
        
        // Create AvailDetails without OccupancyDetails
        AvailDetails availDetails = new AvailDetails();
        availDetails.setOccupancyDetails(null);
        
        // Call the private method
        List<Tariff> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getTariffs", roomTariffList, availDetails);
        
        // Assertions
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Tariff tariff = result.get(0);
        assertNotNull(tariff.getRoomTariffs());
        assertEquals(1, tariff.getRoomTariffs().size());
        
        RoomTariff rt = tariff.getRoomTariffs().get(0);
        assertEquals((Integer) 1, rt.getNumberOfAdults());
        assertEquals((Integer) 2, rt.getNumberOfChildren());
        
        // Occupancy details should be null when occupancyDetails is null
        assertNull(tariff.getOccupancydetails());
    }
    
    @Test
    public void testGetTariffs_withEmptyList() {
        // Test with empty room tariff list
        List<com.mmt.hotels.model.response.pricing.RoomTariff> emptyList = new ArrayList<>();
        AvailDetails availDetails = new AvailDetails();
        
        List<Tariff> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getTariffs", emptyList, availDetails);
        
        // Should return null for empty list
        assertNull(result);
    }
    
    @Test
    public void testGetTariffs_withNullList() {
        // Test with null room tariff list
        AvailDetails availDetails = new AvailDetails();
        
        List<Tariff> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getTariffs", null, availDetails);
        
        // Should return null for null list
        assertNull(result);
    }
    
    @Test
    public void testGetCancellationTimeline_withValidData_bnplApplicable() {
        // Create test cancellation timeline
        com.mmt.hotels.model.response.pricing.CancellationTimeline sourceCancellationTimeline = 
            new com.mmt.hotels.model.response.pricing.CancellationTimeline();
        sourceCancellationTimeline.setBookingDate("2024-01-15");
        sourceCancellationTimeline.setCancellationDate("2024-01-20");
        sourceCancellationTimeline.setCancellationDateTime("2024-01-20 14:00");
        sourceCancellationTimeline.setCardChargeDate("2024-01-16");
        sourceCancellationTimeline.setCardChargeDateTime("2024-01-16 10:00");
        sourceCancellationTimeline.setCardChargeText("Card will be charged on this date");
        sourceCancellationTimeline.setBookingAmountText("Initial booking amount");
        sourceCancellationTimeline.setCheckInDate("2024-01-25");
        sourceCancellationTimeline.setCheckInDateTime("2024-01-25 15:00");
        sourceCancellationTimeline.setDateFormat("dd-MM-yyyy");
        sourceCancellationTimeline.setFreeCancellationText("Free cancellation till date");
        sourceCancellationTimeline.setSubTitle("Cancellation Policy");
        sourceCancellationTimeline.setTitle("Cancellation Information");
        
        // Call the private method with BNPL applicable
        CancellationTimeline result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, 
            "getCancellationTimeline", sourceCancellationTimeline, true);
        
        // Assertions
        assertNotNull(result);
        assertEquals("2024-01-15", result.getBookingDate());
        assertEquals("2024-01-20", result.getCancellationDate());
        assertEquals("2024-01-20 14:00", result.getCancellationDateTime());
        assertEquals("2024-01-16", result.getCardChargeDate());
        assertEquals("2024-01-16 10:00", result.getCardChargeDateTime());
        assertEquals("Card will be charged on this date", result.getCardChargeText());
        assertEquals("Initial booking amount", result.getBookingAmountText());
        assertEquals("2024-01-25", result.getCheckInDate());
        assertEquals("2024-01-25 15:00", result.getCheckInDateTime());
        assertEquals("dd-MM-yyyy", result.getDateFormat());
        assertEquals("Free cancellation till date", result.getFreeCancellationText());
        assertEquals("Cancellation Policy", result.getSubTitle());
        assertEquals("Cancellation Information", result.getTitle());
    }
    
    @Test
    public void testGetCancellationTimeline_withValidData_bnplNotApplicable() {
        // Create test cancellation timeline
        com.mmt.hotels.model.response.pricing.CancellationTimeline sourceCancellationTimeline = 
            new com.mmt.hotels.model.response.pricing.CancellationTimeline();
        sourceCancellationTimeline.setBookingDate("2024-01-15");
        sourceCancellationTimeline.setCancellationDate("2024-01-20");
        sourceCancellationTimeline.setCancellationDateTime("2024-01-20 14:00");
        sourceCancellationTimeline.setCardChargeDate("2024-01-16");
        sourceCancellationTimeline.setCardChargeDateTime("2024-01-16 10:00");
        sourceCancellationTimeline.setCardChargeText("Card will be charged on this date");
        sourceCancellationTimeline.setBookingAmountText("Initial booking amount");
        sourceCancellationTimeline.setCheckInDate("2024-01-25");
        sourceCancellationTimeline.setCheckInDateTime("2024-01-25 15:00");
        sourceCancellationTimeline.setDateFormat("dd-MM-yyyy");
        sourceCancellationTimeline.setFreeCancellationText("Free cancellation till date");
        sourceCancellationTimeline.setSubTitle("Cancellation Policy");
        sourceCancellationTimeline.setTitle("Cancellation Information");
        
        // Call the private method with BNPL not applicable
        CancellationTimeline result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, 
            "getCancellationTimeline", sourceCancellationTimeline, false);
        
        // Assertions - BNPL specific fields should not be set
        assertNotNull(result);
        assertEquals("2024-01-15", result.getBookingDate());
        assertEquals("2024-01-20", result.getCancellationDate());
        assertEquals("2024-01-20 14:00", result.getCancellationDateTime());
        
        // BNPL-specific fields should not be set when bnplApplicable is false
        assertNull(result.getCardChargeDate());
        assertNull(result.getCardChargeDateTime());
        assertNull(result.getCardChargeText());
        assertNull(result.getBookingAmountText());
        
        assertEquals("2024-01-25", result.getCheckInDate());
        assertEquals("2024-01-25 15:00", result.getCheckInDateTime());
        assertEquals("dd-MM-yyyy", result.getDateFormat());
        assertEquals("Free cancellation till date", result.getFreeCancellationText());
        assertEquals("Cancellation Policy", result.getSubTitle());
        assertEquals("Cancellation Information", result.getTitle());
    }
    
    @Test
    public void testGetCancellationTimeline_withNullInput() {
        // Test with null cancellation timeline
        CancellationTimeline result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, 
            "getCancellationTimeline", null, true);
        
        // Should return null for null input
        assertNull(result);
    }
    
    @Test
    public void testGetCancellationTimeline_withPartialData() {
        // Create test cancellation timeline with only some fields
        com.mmt.hotels.model.response.pricing.CancellationTimeline sourceCancellationTimeline = 
            new com.mmt.hotels.model.response.pricing.CancellationTimeline();
        sourceCancellationTimeline.setTitle("Basic Title");
        sourceCancellationTimeline.setCheckInDate("2024-01-25");
        // Other fields are null
        
        // Call the private method with BNPL applicable
        CancellationTimeline result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, 
            "getCancellationTimeline", sourceCancellationTimeline, true);
        
        // Assertions - should handle null fields gracefully
        assertNotNull(result);
        assertEquals("Basic Title", result.getTitle());
        assertEquals("2024-01-25", result.getCheckInDate());
        assertNull(result.getBookingDate());
        assertNull(result.getCancellationDate());
        assertNull(result.getCancellationDateTime());
        assertNull(result.getCardChargeDate());
        assertNull(result.getCardChargeDateTime());
        assertNull(result.getCardChargeText());
        assertNull(result.getBookingAmountText());
        assertNull(result.getCheckInDateTime());
        assertNull(result.getDateFormat());
        assertNull(result.getFreeCancellationText());
        assertNull(result.getSubTitle());
    }
}
