package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static junit.framework.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchStaticDetailFactoryTest {

    @InjectMocks
    private OrchStaticDetailFactory orchStaticDetailFactory;

    @Mock
    private OrchStaticDetailsResponseTransformerAndroid orchStaticDetailsResponseTransformerAndroid;

    @Mock
    private OrchStaticDetailsResponseTransformerPWA orchStaticDetailsResponseTransformerPwa;

    @Mock
    private OrchStaticDetailsResponseTransformerIOS orchStaticDetailsResponseTransformerIOS;

    @Mock
    private OrchStaticDetailsResponseTransformerDesktop orchStaticDetailsResponseTransformerDesktop;

    @Before
    public void setUp() {
        // Any setup required
    }

    @Test
    public void testGetResponseService_withNullClient_returnsDesktop() {
        // Test with null client
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService(null);
        assertEquals("Should return desktop transformer for null client", orchStaticDetailsResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseService_withEmptyClient_returnsDesktop() {
        // Test with empty string client
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("");
        assertEquals("Should return desktop transformer for empty client", orchStaticDetailsResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseService_withPWAClient_returnsPWA() {
        // Test with PWA client
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("PWA");
        assertEquals("Should return PWA transformer for PWA client", orchStaticDetailsResponseTransformerPwa, result);
    }

    @Test
    public void testGetResponseService_withMSITEClient_returnsPWA() {
        // Test with MSITE client (should map to PWA)
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("MSITE");
        assertEquals("Should return PWA transformer for MSITE client", orchStaticDetailsResponseTransformerPwa, result);
    }

    @Test
    public void testGetResponseService_withDESKTOPClient_returnsDesktop() {
        // Test with DESKTOP client
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("DESKTOP");
        assertEquals("Should return desktop transformer for DESKTOP client", orchStaticDetailsResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseService_withANDROIDClient_returnsAndroid() {
        // Test with ANDROID client
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("ANDROID");
        assertEquals("Should return android transformer for ANDROID client", orchStaticDetailsResponseTransformerAndroid, result);
    }

    @Test
    public void testGetResponseService_withIOSClient_returnsIOS() {
        // Test with IOS client
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("IOS");
        assertEquals("Should return iOS transformer for IOS client", orchStaticDetailsResponseTransformerIOS, result);
    }

    @Test
    public void testGetResponseService_withUnknownClient_returnsDesktop() {
        // Test with unknown client type
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("UNKNOWN");
        assertEquals("Should return desktop transformer for unknown client", orchStaticDetailsResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseService_withLowercaseClient_returnsDesktop() {
        // Test with lowercase client (should not match)
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("android");
        assertEquals("Should return desktop transformer for lowercase client", orchStaticDetailsResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseService_withMixedCaseClient_returnsDesktop() {
        // Test with mixed case client (should not match)
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("Android");
        assertEquals("Should return desktop transformer for mixed case client", orchStaticDetailsResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseService_withWhitespaceClient_returnsDesktop() {
        // Test with whitespace client
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("   ");
        assertEquals("Should return desktop transformer for whitespace client", orchStaticDetailsResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseService_allCasesSwitchBehavior() {
        // Comprehensive test to ensure all switch cases work
        assertSame("PWA case", orchStaticDetailsResponseTransformerPwa, orchStaticDetailFactory.getResponseService("PWA"));
        assertSame("MSITE case", orchStaticDetailsResponseTransformerPwa, orchStaticDetailFactory.getResponseService("MSITE"));
        assertSame("DESKTOP case", orchStaticDetailsResponseTransformerDesktop, orchStaticDetailFactory.getResponseService("DESKTOP"));
        assertSame("ANDROID case", orchStaticDetailsResponseTransformerAndroid, orchStaticDetailFactory.getResponseService("ANDROID"));
        assertSame("IOS case", orchStaticDetailsResponseTransformerIOS, orchStaticDetailFactory.getResponseService("IOS"));
    }

    @Test
    public void testGetResponseService_caseInsensitiveDefault() {
        // Test various invalid inputs that should all return default (desktop)
        String[] invalidInputs = {"desktop", "pwa", "ios", "android", "random", "123", "!@#", ""};
        
        for (String input : invalidInputs) {
            OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService(input);
            assertEquals("Should return desktop transformer for input: " + input, 
                        orchStaticDetailsResponseTransformerDesktop, result);
        }
    }
} 