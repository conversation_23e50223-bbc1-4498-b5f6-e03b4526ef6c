package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.corporate.UpdatePolicyResponse;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class UpdatePolicyResponseTransformerPWATest {

    @Mock
    CommonHelper commonHelper;

    @Mock
    CommonResponseTransformer commonResponseTransformer;


    @InjectMocks
    UpdatePolicyResponseTransformerPWA updatePolicyResponseTransformerPWA;

    @Test
    public void testConvertUpdatePolicyResponse() {

        CorpPolicyUpdateResponse corpPolicyUpdateResponse = new CorpPolicyUpdateResponse();
        corpPolicyUpdateResponse.setCorpMetaInfo(new CorpMetaInfo());
        Mockito.when(commonResponseTransformer.buildCorpApprovalInfo(Mockito.any())).thenReturn(new com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo());
        Mockito.when(commonResponseTransformer.buildManagers(Mockito.any())).thenReturn(new ArrayList<com.mmt.hotels.clientgateway.response.corporate.ApprovingManager>());

        UpdatePolicyResponse updatePolicyResponse = updatePolicyResponseTransformerPWA.convertUpdatePolicyResponse(corpPolicyUpdateResponse);
        Assert.assertNotNull(updatePolicyResponse);

    }

}
