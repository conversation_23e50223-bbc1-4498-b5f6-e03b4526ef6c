package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail;

import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerPWA;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;
import static junit.framework.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrchStaticDetailsResponseTransformerPWATest {

    @InjectMocks
    private OrchStaticDetailsResponseTransformerPWA transformer;

    @Mock
    private com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo orchestratorStaffInfo;

    @Mock
    private StaffInfo clientGatewayStaffInfo;

    private String starHostIconApp = "https://test.com/star-host-icon-pwa.png";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(transformer, "starHostIconApp", starHostIconApp);
    }

    @Test
    public void testBuildCardTitleMap_returnsNull() {
        // Test buildCardTitleMap method
        Map<String, String> result = transformer.buildCardTitleMap();
        assertNull("buildCardTitleMap should return null", result);
    }

    @Test
    public void testAddTitleData_doesNothing() {
        // Test addTitleData method (empty implementation)
        HotelResult hotelResult = new HotelResult();
        String countryCode = "IN";
        
        // This should not throw any exception
        transformer.addTitleData(hotelResult, countryCode);
        
        // Since the method is empty, we just verify it executes without error
        assertTrue("addTitleData should execute without error", true);
    }

    @Test
    public void testAddTitleData_withNullParams_doesNothing() {
        // Test addTitleData with null parameters
        transformer.addTitleData(null, null);
        
        // Since the method is empty, we just verify it executes without error
        assertTrue("addTitleData with null params should execute without error", true);
    }

    @Test
    public void testGetLuxeIcon_returnsCorrectIcon() {
        // Test getLuxeIcon method
        String result = transformer.getLuxeIcon();
        assertEquals("Should return LUXE_ICON_APPS constant", LUXE_ICON_APPS, result);
    }

    @Test
    public void testConvertStaffInfo_withNullStaffInfo_returnsNull() {
        // Test convertStaffInfo with null input
        StaffInfo result = transformer.convertStaffInfo(null);
        assertNull("Should return null for null staff info", result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostTrue_setsIconUrl() {
        // Test convertStaffInfo when isStarHost is true
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);
        
        // Mock the super.convertStaffInfo call by creating a spy
        OrchStaticDetailsResponseTransformerPWA transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was called
        verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostFalse_doesNotSetIconUrl() {
        // Test convertStaffInfo when isStarHost is false
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(false);
        
        // Mock the super.convertStaffInfo call
        OrchStaticDetailsResponseTransformerPWA transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was NOT called
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_withStarHostNull_doesNotSetIconUrl() {
        // Test convertStaffInfo when isStarHost is null
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(null);
        
        // Mock the super.convertStaffInfo call
        OrchStaticDetailsResponseTransformerPWA transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        
        StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify that setStarHostIconUrl was NOT called
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
        assertEquals("Should return client gateway staff info", clientGatewayStaffInfo, result);
    }

    @Test
    public void testConvertStaffInfo_callsSuperConvertStaffInfo() {
        // Test that convertStaffInfo calls super.convertStaffInfo and sets icon when star host is true
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);
        
        // Call the real method and verify it works
        StaffInfo result = transformer.convertStaffInfo(orchestratorStaffInfo);
        
        // Verify the method executed correctly by checking the return value
        assertNotNull("Should return valid staff info", result);
        verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
    }

    @Test
    public void testStarHostIconAppValue() {
        // Test that the @Value injection works correctly
        String iconUrl = (String) ReflectionTestUtils.getField(transformer, "starHostIconApp");
        assertEquals("starHostIconApp should be set correctly", starHostIconApp, iconUrl);
    }

    @Test
    public void testConvertStaffInfo_edgeCaseScenarios() {
        // Test various edge case scenarios
        
        // Test with different boolean values for isStarHost
        Boolean[] starHostValues = {true, false, null};
        
        for (Boolean starHostValue : starHostValues) {
            // Reset the mock
            reset(orchestratorStaffInfo);
            when(orchestratorStaffInfo.getIsStarHost()).thenReturn(starHostValue);
            
            OrchStaticDetailsResponseTransformerPWA transformerSpy = spy(transformer);
            lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
            lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
            
            StaffInfo result = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
            
            assertNotNull("Result should not be null for starHost value: " + starHostValue, result);
            
            if (Boolean.TRUE.equals(starHostValue)) {
                verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
            } else {
                verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
            }
        }
    }

    @Test
    public void testConvertStaffInfo_booleanUtilsLogic() {
        // Test specific BooleanUtils.isTrue scenarios
        
        // Test with Boolean.TRUE
        reset(orchestratorStaffInfo);
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(Boolean.TRUE);
        
        OrchStaticDetailsResponseTransformerPWA transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        
        transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        verify(orchestratorStaffInfo).setStarHostIconUrl(starHostIconApp);
        
        // Test with Boolean.FALSE
        reset(orchestratorStaffInfo);
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(Boolean.FALSE);
        
        transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        
        transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        verify(orchestratorStaffInfo, never()).setStarHostIconUrl(anyString());
    }

    @Test
    public void testConvertStaffInfo_multipleInvocations() {
        // Test multiple invocations to ensure consistency
        when(orchestratorStaffInfo.getIsStarHost()).thenReturn(true);
        
        OrchStaticDetailsResponseTransformerPWA transformerSpy = spy(transformer);
        lenient().when(transformerSpy.convertStaffInfo(orchestratorStaffInfo)).thenCallRealMethod();
        lenient().doReturn(clientGatewayStaffInfo).when((OrchStaticDetailResponseTransformer) transformerSpy).convertStaffInfo(orchestratorStaffInfo);
        
        // Call multiple times
        StaffInfo result1 = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        StaffInfo result2 = transformerSpy.convertStaffInfo(orchestratorStaffInfo);
        
        assertEquals("First call should return client gateway staff info", clientGatewayStaffInfo, result1);
        assertEquals("Second call should return client gateway staff info", clientGatewayStaffInfo, result2);
        
        // Verify setStarHostIconUrl was called (might be once if optimization exists)
        verify(orchestratorStaffInfo, atLeastOnce()).setStarHostIconUrl(starHostIconApp);
    }
} 