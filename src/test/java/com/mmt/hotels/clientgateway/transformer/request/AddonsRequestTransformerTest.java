package com.mmt.hotels.clientgateway.transformer.request;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.AvailRoomsSearchCriteria;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchAddonsCriteria;
import com.mmt.hotels.clientgateway.request.SearchAddonsRequest;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;

@RunWith(MockitoJUnitRunner.class)
public class AddonsRequestTransformerTest {

	@InjectMocks
	AddonsRequestTransformer addonsRequestTransformer;
	
	@Test
	public void testConvertSearchAddonsRequest(){
		SearchAddonsRequest searchAddonsRequest = new SearchAddonsRequest();
		
		searchAddonsRequest.setDeviceDetails(new DeviceDetails());
		
		SearchAddonsCriteria searchCriteria = new SearchAddonsCriteria();
		searchCriteria.setRoomCriteria(getRoomCriteria());
		searchAddonsRequest.setSearchCriteria(searchCriteria );
		
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		requestDetails.setTrafficSource(trafficSource );
		searchAddonsRequest.setRequestDetails(requestDetails );
		
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		ExtendedUser extendedUser = new ExtendedUser();
		commonModifierResponse.setExtendedUser(extendedUser );
		
		GetAddonsRequest req = addonsRequestTransformer.convertSearchAddonsRequest(searchAddonsRequest , commonModifierResponse );
		Assert.assertNotNull(req);
	}

	private List<AvailRoomsSearchCriteria> getRoomCriteria() {
		List<AvailRoomsSearchCriteria> roomCriteria = new ArrayList<>();
		AvailRoomsSearchCriteria crtria = new AvailRoomsSearchCriteria();
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate rmstyCandt = new RoomStayCandidate();
		rmstyCandt.setAdultCount(1);
		roomStayCandidates.add(rmstyCandt );
		crtria.setRoomStayCandidates(roomStayCandidates );
		roomCriteria.add(crtria );
		return roomCriteria;
	}
}
