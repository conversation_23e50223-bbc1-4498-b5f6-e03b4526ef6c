package com.mmt.hotels.clientgateway.transformer.response;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.time.LocalDate;
import java.util.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.UpdatedPriceCriteria;
import com.mmt.hotels.clientgateway.request.UpdatedPriceRoomCriteria;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPriceHelper;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;

/**
 * Unit tests for OrchUpdatedPriceResponseTransformer in GI project
 * Comprehensive test coverage following patterns from CG version and GI UpdatedPriceResponseTransformerTest
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchUpdatedPriceResponseTransformerTest {

    @InjectMocks
    private OrchUpdatedPriceResponseTransformer transformer;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private SearchRoomsPriceHelper searchRoomsPriceHelper;

    @Mock
    private Utility utility;

    @Mock
    private DateUtil dateUtil;

    private UpdatePriceRequest updatePriceRequest;
    private UpdatePriceResponse orchResponse;
    private CommonModifierResponse commonModifierResponse;
    private UpdatedPriceCriteria searchCriteria;
    private RequestDetails requestDetails;
    private ExtendedUser extendedUser;

    private static final String TEST_CURRENCY = "INR";
    private static final String TEST_CHECK_IN = "2024-01-15";
    private static final String TEST_CHECK_OUT = "2024-01-17";
    private static final String TEST_EXP_DATA = "expData";
    private static final String TEST_PRICING_KEY = "PRICE_KEY_123";
    private static final String TEST_SELLABLE_TYPE = "STANDARD";
    private static final String TEST_FUNNEL_SOURCE = "HOTELS";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        setupTestData();
    }

    private void setupTestData() {
        // Setup UpdatePriceRequest
        updatePriceRequest = new UpdatePriceRequest();
        searchCriteria = new UpdatedPriceCriteria();
        searchCriteria.setCurrency(TEST_CURRENCY);
        searchCriteria.setCheckIn(TEST_CHECK_IN);
        searchCriteria.setCheckOut(TEST_CHECK_OUT);
        
        requestDetails = new RequestDetails();
        requestDetails.setFunnelSource(TEST_FUNNEL_SOURCE);
        updatePriceRequest.setRequestDetails(requestDetails);
        updatePriceRequest.setSearchCriteria(searchCriteria);

        // Setup UpdatePriceResponse (orchestrator)
        orchResponse = new UpdatePriceResponse();
        orchResponse.setCurrency(TEST_CURRENCY);
        
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setPricingKey(TEST_PRICING_KEY);
        orchResponse.setPriceDetail(priceDetail);

        // Setup CommonModifierResponse
        commonModifierResponse = new CommonModifierResponse();
        extendedUser = new ExtendedUser();
        extendedUser.setProfileType("B2C");
        extendedUser.setAffiliateId("MMT");
        commonModifierResponse.setExtendedUser(extendedUser);
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.NEW_PROPERTY_OFFER.getKey(), "true");
        commonModifierResponse.setExpDataMap(expDataMap);
    }

    // ==================== NULL HANDLING TESTS ====================

    @Test
    public void should_ReturnNull_When_ResponseIsNull() {
        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, null, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNull(result);
    }

    // ==================== BASIC CONVERSION TESTS ====================

//    @Test
//    public void should_ConvertResponse_When_ValidInputProvided() {
//        // Given
//        setupRoomCriteria();
//        setupMockBehavior();
//
//        // When
//        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result =
//            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);
//
//        // Then
//        assertNotNull(result);
//        assertEquals(TEST_CURRENCY, result.getCurrency());
//        assertNotNull(result.getPriceMap());
//        assertEquals(TEST_PRICING_KEY, result.getDefaultPriceKey());
//
//        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class),
//            argThat(expDataMap -> expDataMap.containsKey(ExperimentKeys.NEW_PROPERTY_OFFER.getKey())),
//            any(OccupancyDetails.class), eq(TEST_CURRENCY), eq(TEST_SELLABLE_TYPE), eq(2),
//            eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), eq(""));
//    }

    @Test
    public void should_HandleMinimalData_When_RequiredFieldsOnly() {
        // Given
        setupMockBehavior();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals(TEST_CURRENCY, result.getCurrency());
        
        // Verify occupancy details has default room count (1) when no room criteria
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            argThat(occupancy -> occupancy.getNumberOfRooms() == 1), 
            anyString(), isNull(), anyInt(), anyBoolean(), anyString(), anyBoolean(), 
            anyBoolean(), anyBoolean(), anyBoolean(), anyString());
    }

    // ==================== MY PARTNER USER TESTS ====================

    @Test
    public void should_HandleMyPartnerUser_When_ExtendedUserIsMyPartner() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        extendedUser.setProfileType("CORP");
        extendedUser.setAffiliateId("MY_PARTNER");

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        // Verify myPartner is handled in price map call
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq(TEST_CURRENCY), eq(TEST_SELLABLE_TYPE), eq(2), 
            eq(false), eq(""), eq(true), eq(false), eq(false), anyBoolean(), eq(""));
    }

    @Test
    public void should_HandleNonMyPartnerUser_When_ExtendedUserIsRegular() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        extendedUser.setProfileType("B2C");
        extendedUser.setAffiliateId("REGULAR");

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq(TEST_CURRENCY), eq(TEST_SELLABLE_TYPE), eq(2), 
            eq(false), eq(""), eq(true), eq(false), eq(false), anyBoolean(), eq(""));
    }

    // ==================== COMMON MODIFIER RESPONSE TESTS ====================

    @Test
    public void should_HandleNullCommonModifierResponse_When_CommonModifierResponseIsNull() {
        // Given
        setupRoomCriteria();
        setupMockBehaviorForNullCommonModifier();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, null);

        // Then
        assertNotNull(result);
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq(TEST_CURRENCY), eq(TEST_SELLABLE_TYPE), eq(2), 
            eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), eq(""));
    }

    @Test
    public void should_HandleNullExtendedUser_When_ExtendedUserIsNull() {
        // Given
        setupRoomCriteria();
        setupMockBehaviorForNullCommonModifier();
        commonModifierResponse.setExtendedUser(null);

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq(TEST_CURRENCY), eq(TEST_SELLABLE_TYPE), eq(2), 
            eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), eq(""));
    }

    // ==================== ROOM CRITERIA TESTS ====================

    @Test
    public void should_HandleEmptyRoomCriteria_When_RoomCriteriaIsEmpty() {
        // Given
        searchCriteria.setRoomCriteria(new ArrayList<>());
        setupMockBehavior();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq(TEST_CURRENCY), isNull(), eq(2), 
            eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), eq(""));
    }

    @Test
    public void should_HandleNullRoomCriteria_When_RoomCriteriaIsNull() {
        // Given
        searchCriteria.setRoomCriteria(null);
        setupMockBehavior();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq(TEST_CURRENCY), isNull(), eq(2), 
            eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), eq(""));
    }

    @Test
    public void should_CalculateRoomCount_When_MultipleRoomCriteriaProvided() {
        // Given
        List<UpdatedPriceRoomCriteria> roomCriteriaList = new ArrayList<>();
        
        UpdatedPriceRoomCriteria roomCriteria1 = new UpdatedPriceRoomCriteria();
        roomCriteria1.setSellableType(TEST_SELLABLE_TYPE);
        List<RoomStayCandidate> candidates1 = Arrays.asList(
            new RoomStayCandidate(), new RoomStayCandidate()
        );
        roomCriteria1.setRoomStayCandidates(candidates1);
        
        UpdatedPriceRoomCriteria roomCriteria2 = new UpdatedPriceRoomCriteria();
        roomCriteria2.setSellableType("DELUXE");
        List<RoomStayCandidate> candidates2 = Arrays.asList(
            new RoomStayCandidate(), new RoomStayCandidate(), new RoomStayCandidate()
        );
        roomCriteria2.setRoomStayCandidates(candidates2);
        
        roomCriteriaList.add(roomCriteria1);
        roomCriteriaList.add(roomCriteria2);
        searchCriteria.setRoomCriteria(roomCriteriaList);
        
        setupMockBehavior();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        
        // Verify occupancy details has correct room count (2 + 3 = 5)
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            argThat(occupancy -> occupancy.getNumberOfRooms() == 5), 
            anyString(), anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), 
            anyBoolean(), anyBoolean(), anyBoolean(), anyString());
    }

    // ==================== EXPERIMENT TESTS ====================

    @Test
    public void should_HandleNewPropertyOfferExperiment_When_ExperimentIsEnabled() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        when(utility.isExperimentOn(any(Map.class), eq(ExperimentKeys.NEW_PROPERTY_OFFER.getKey()))).thenReturn(true);

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).isExperimentOn(any(Map.class), eq(ExperimentKeys.NEW_PROPERTY_OFFER.getKey()));
    }

    @Test
    public void should_HandleNewPropertyOfferExperiment_When_ExperimentIsDisabled() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        when(utility.isExperimentOn(any(Map.class), eq(ExperimentKeys.NEW_PROPERTY_OFFER.getKey()))).thenReturn(false);

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).isExperimentOn(any(Map.class), eq(ExperimentKeys.NEW_PROPERTY_OFFER.getKey()));
    }

    @Test
    public void should_HandleEmptyExpDataMap_When_ExpDataMapIsEmpty() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).getExpDataMap(TEST_EXP_DATA);
    }

    @Test
    public void should_HandleNullExpDataMap_When_ExpDataMapIsNull() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        commonModifierResponse.setExpDataMap(null);

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).getExpDataMap(TEST_EXP_DATA);
    }

    // ==================== DATE CALCULATION TESTS ====================

    @Test
    public void should_CalculateLengthOfStay_When_CheckInCheckOutProvided() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        when(dateUtil.getDaysDiff(LocalDate.parse(TEST_CHECK_IN), LocalDate.parse(TEST_CHECK_OUT)))
            .thenReturn(2);

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(dateUtil).getDaysDiff(LocalDate.parse(TEST_CHECK_IN), LocalDate.parse(TEST_CHECK_OUT));
    }

    @Test
    public void should_HandleDifferentLengthOfStay_When_DifferentDatesProvided() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        searchCriteria.setCheckIn("2024-01-15");
        searchCriteria.setCheckOut("2024-01-20");
        when(dateUtil.getDaysDiff(LocalDate.parse("2024-01-15"), LocalDate.parse("2024-01-20")))
            .thenReturn(5);

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq(TEST_CURRENCY), eq(TEST_SELLABLE_TYPE), eq(5), 
            eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), eq(""));
    }

    // ==================== PRICE MAP TESTS ====================

    @Test
    public void should_HandleEmptyPriceMap_When_PriceMapIsEmpty() {
        // Given
        setupRoomCriteria();
        setupMockBehaviorPartial();
        setupEmptyPriceMap();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getPriceMap());
        assertTrue(result.getPriceMap().isEmpty());
        assertNull(result.getDefaultPriceKey()); // Should be null when price map is empty
    }

    @Test
    public void should_SetDefaultPriceKey_When_PriceMapHasEntries() {
        // Given
        setupRoomCriteria();
        setupMockBehaviorPartial();
        setupMultiEntryPriceMap();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getPriceMap());
        assertEquals(2, result.getPriceMap().size());
        assertNotNull(result.getDefaultPriceKey());
        // Verify the default price key is one of the keys in the map
        assertTrue(result.getPriceMap().containsKey(result.getDefaultPriceKey()));
    }

    // ==================== EDGE CASES AND INTEGRATION TESTS ====================

    @Test
    public void should_HandleAllNullFields_When_MinimalDataProvided() {
        // Given
        UpdatePriceRequest minimalRequest = new UpdatePriceRequest();
        UpdatedPriceCriteria minimalCriteria = new UpdatedPriceCriteria();
        minimalCriteria.setCurrency("USD");
        minimalCriteria.setCheckIn("2024-01-15");
        minimalCriteria.setCheckOut("2024-01-16");
        minimalRequest.setSearchCriteria(minimalCriteria);
        minimalRequest.setRequestDetails(new RequestDetails());

        UpdatePriceResponse minimalResponse = new UpdatePriceResponse();
        minimalResponse.setCurrency("USD");
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setPricingKey("MINIMAL_KEY");
        minimalResponse.setPriceDetail(priceDetail);

        setupMockBehaviorForMinimal();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(minimalRequest, minimalResponse, null, null, null);

        // Then
        assertNotNull(result);
        assertEquals("USD", result.getCurrency());
    }

    @Test
    public void should_HandleGroupBookingFunnel_When_FunnelSourceIsGroupBooking() {
        // Given
        setupRoomCriteria();
        requestDetails.setFunnelSource("GROUP_BOOKING");
        setupMockBehavior();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).buildToolTip("GROUP_BOOKING");
    }

    @Test
    public void should_HandleToolTipBuilding_When_FunnelSourceProvided() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).buildToolTip(TEST_FUNNEL_SOURCE);
    }

    @Test
    public void should_HandleDifferentCurrencies_When_CurrencyIsUSD() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        searchCriteria.setCurrency("USD");
        orchResponse.setCurrency("USD");

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals("USD", result.getCurrency());
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), eq("USD"), eq(TEST_SELLABLE_TYPE), eq(2), 
            eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), eq(""));
    }

//    @Test
//    public void should_HandleNullRequestDetails_When_RequestDetailsIsNull() {
//        // Given
//        setupRoomCriteria();
//        setupMockBehavior();
//        updatePriceRequest.setRequestDetails(null);
//
//        // When
//        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result =
//            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, null, commonModifierResponse);
//
//        // Then
//        assertNotNull(result);
//        // Should handle null requestDetails gracefully in buildToolTip call
//        verify(utility).buildToolTip(null);
//    }

    @Test
    public void should_HandleNullFunnelSource_When_FunnelSourceIsNull() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        requestDetails.setFunnelSource(null);

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).buildToolTip(null);
    }

    @Test
    public void should_HandleOccupancyDetailsCorrectly_When_PricingKeyExists() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();
        PriceDetail priceDetail = orchResponse.getPriceDetail();
        priceDetail.setPricingKey("TEST_PRICING_KEY");

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, TEST_EXP_DATA, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        // Verify that occupancy details are set correctly
        verify(searchRoomsPriceHelper).getPriceMap(any(PriceDetail.class), any(Map.class), 
            argThat(occupancy -> "TEST_PRICING_KEY".equals(occupancy.getPricingKey()) && 
                               occupancy.getNumberOfRooms() == 2), 
            anyString(), anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), 
            anyBoolean(), anyBoolean(), anyBoolean(), anyString());
    }

    @Test
    public void should_HandleNullExpData_When_ExpDataIsNull() {
        // Given
        setupRoomCriteria();
        setupMockBehavior();

        // When
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result = 
            transformer.convertUpdatePriceResponse(updatePriceRequest, orchResponse, null, requestDetails, commonModifierResponse);

        // Then
        assertNotNull(result);
        verify(utility).getExpDataMap(null);
    }

    // ==================== HELPER METHODS ====================

    private void setupRoomCriteria() {
        List<UpdatedPriceRoomCriteria> roomCriteriaList = new ArrayList<>();
        UpdatedPriceRoomCriteria roomCriteria = new UpdatedPriceRoomCriteria();
        roomCriteria.setSellableType(TEST_SELLABLE_TYPE);
        
        List<RoomStayCandidate> candidates = Arrays.asList(
            new RoomStayCandidate(), new RoomStayCandidate()
        );
        roomCriteria.setRoomStayCandidates(candidates);
        
        roomCriteriaList.add(roomCriteria);
        searchCriteria.setRoomCriteria(roomCriteriaList);
    }

    private void setupMockBehavior() {
        // Reset all mocks first
        reset(dateUtil, utility, searchRoomsPriceHelper);
        
        // Setup all basic mocks
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(utility.isExperimentOn(any(Map.class), anyString())).thenReturn(true);
        when(utility.buildToolTip(anyString())).thenReturn(true);
        when(utility.getExpDataMap(anyString())).thenReturn(new LinkedHashMap<>());
        
        // Setup price map
        Map<String, TotalPricing> priceMap = new HashMap<>();
        priceMap.put(TEST_PRICING_KEY, mock(TotalPricing.class));
        when(searchRoomsPriceHelper.getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), anyString(), anyString(), anyInt(), anyBoolean(), 
            anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyString()))
            .thenReturn(priceMap);
    }

    private void setupMockBehaviorPartial() {
        // Reset all mocks first
        reset(dateUtil, utility, searchRoomsPriceHelper);
        
        // Setup only basic mocks without price map
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(utility.isExperimentOn(any(Map.class), anyString())).thenReturn(true);
        when(utility.buildToolTip(anyString())).thenReturn(true);
        when(utility.getExpDataMap(anyString())).thenReturn(new LinkedHashMap<>());
    }

    private void setupMockBehaviorForNullCommonModifier() {
        // Reset all mocks first
        reset(dateUtil, utility, searchRoomsPriceHelper);
        
        // Setup basic mocks
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(utility.buildToolTip(anyString())).thenReturn(true);
        when(utility.getExpDataMap(anyString())).thenReturn(new LinkedHashMap<>());
        
        // Setup price map
        Map<String, TotalPricing> priceMap = new HashMap<>();
        priceMap.put(TEST_PRICING_KEY, mock(TotalPricing.class));
        when(searchRoomsPriceHelper.getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), anyString(), anyString(), anyInt(), anyBoolean(), 
            anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyString()))
            .thenReturn(priceMap);
    }

    private void setupMockBehaviorForMinimal() {
        // Reset all mocks first
        reset(dateUtil, utility, searchRoomsPriceHelper);
        
        // Setup basic mocks
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(1);
//        when(utility.buildToolTip(anyString())).thenReturn(true);
//        when(utility.getExpDataMap(anyString())).thenReturn(new LinkedHashMap<>());
        
        // Setup price map
        Map<String, TotalPricing> priceMap = new HashMap<>();
        priceMap.put("MINIMAL_KEY", mock(TotalPricing.class));
//        when(searchRoomsPriceHelper.getPriceMap(any(PriceDetail.class), any(Map.class),
//            any(OccupancyDetails.class), anyString(), anyString(), anyInt(), anyBoolean(),
//            anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyString()))
//            .thenReturn(priceMap);
    }

    private void setupEmptyPriceMap() {
        Map<String, TotalPricing> emptyPriceMap = new HashMap<>();
        when(searchRoomsPriceHelper.getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), anyString(), anyString(), anyInt(), anyBoolean(), 
            anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyString()))
            .thenReturn(emptyPriceMap);
    }

    private void setupMultiEntryPriceMap() {
        Map<String, TotalPricing> priceMap = new HashMap<>();
        priceMap.put(TEST_PRICING_KEY, mock(TotalPricing.class));
        priceMap.put("PRICE_KEY_456", mock(TotalPricing.class));
        when(searchRoomsPriceHelper.getPriceMap(any(PriceDetail.class), any(Map.class), 
            any(OccupancyDetails.class), anyString(), anyString(), anyInt(), anyBoolean(), 
            anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyString()))
            .thenReturn(priceMap);
    }
} 