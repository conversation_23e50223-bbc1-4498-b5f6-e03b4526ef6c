package com.mmt.hotels.clientgateway.transformer.factory;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.transformer.response.PoliciesResponseTransformer;

@RunWith(MockitoJUnitRunner.class)
public class PolicyFactoryTest {

	@InjectMocks
	private PolicyFactory policyFactory;
	
	@Mock
	private PoliciesResponseTransformer policiesResponseTransformer;
	
	@Test
	public void testGetResponseService(){
		Assert.assertNotNull(policyFactory.getResponseService("PWA"));
	}
}
