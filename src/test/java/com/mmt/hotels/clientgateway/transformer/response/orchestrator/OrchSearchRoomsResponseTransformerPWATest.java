package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.GroupRatePlanFilter;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.RatePlanFilter;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchRoomsResponseTransformerPWATest {

    @InjectMocks
    private OrchSearchRoomsResponseTransformerPWA transformer;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void setUp() {
        // Inject the mocked polyglotService into the transformer
        ReflectionTestUtils.setField(transformer, "polyglotService", polyglotService);
        
        // Setup common mock responses
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Top Rated");
        when(polyglotService.getTranslatedData("LOYALTY_OFFER_TEXT")).thenReturn("Loyalty: %s");
        when(polyglotService.getTranslatedData("CASHBACK_OFFER_TEXT_PWA")).thenReturn("Cashback: ₹%d");
    }

    // ======================
    // createTopRatedPersuasion() Tests
    // ======================

//    @Test
//    public void should_CallCreateTopRatedPersuasionForMobile_When_CreatingTopRatedPersuasion() {
//        // When
//        PersuasionObject result = ReflectionTestUtils.invokeMethod(transformer, "createTopRatedPersuasion");
//
//        // Then
//        assertNotNull("Should return a persuasion object", result);
//        assertEquals("Should use mobile template", "IMAGE_TEXT_H", result.getTemplate());
//        assertEquals("Should use correct placeholder", "SELECT_TOP_R1", result.getPlaceholder());
//        assertNotNull("Should have data list", result.getData());
//        assertTrue("Data should be a list", result.getData() instanceof List);
//    }

    @Test
    public void should_CreateMobilePersuasionWithCorrectStyling_When_CreatingTopRatedPersuasion() {
        // When
        PersuasionObject result = ReflectionTestUtils.invokeMethod(transformer, "createTopRatedPersuasion");

        // Then
        assertNotNull("Should return a persuasion object", result);
        assertEquals("Should use mobile template", "IMAGE_TEXT_H", result.getTemplate());
        
        // Verify polyglot service was called
        verify(polyglotService).getTranslatedData("TOP_RATED");
    }

    // ======================
    // buildLoginPersuasion() Tests
    // ======================

    @Test
    public void should_ReturnNull_When_BuildingLoginPersuasion() {
        // When
        LoginPersuasion result = ReflectionTestUtils.invokeMethod(transformer, "buildLoginPersuasion");

        // Then
        assertNull("PWA should not show login persuasion", result);
    }

    // ======================
    // buildLoyaltyCashbackPersuasions() Tests
    // ======================

    @Test
    public void should_BuildLoyaltyPersuasion_When_LoyaltyOfferMessageIsPresent() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Save 10% with MMT Black");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        assertEquals("Should add one persuasion", 1, persuasionMap.size());
        assertTrue("Should contain cashback hero offer persuasion", 
                persuasionMap.containsKey(CASHBACK_HERO_OFFER_PERSUASION_NODE));
        
        PersuasionResponse persuasion = persuasionMap.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should have loyalty text", "Loyalty: Save 10% with MMT Black", persuasion.getPersuasionText());
        assertTrue("Should be HTML", persuasion.isHtml());
        assertEquals("Should use hero offer icon type", HERO_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
        
        verify(polyglotService).getTranslatedData("LOYALTY_OFFER_TEXT");
    }

    @Test
    public void should_BuildCashbackPersuasion_When_LoyaltyOfferMessageIsBlank() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage(""); // blank
        Map<String, Double> hybridDiscounts = new HashMap<>();
        hybridDiscounts.put("CTW", 150.6); // Will round to 151
        coupon.setHybridDiscounts(hybridDiscounts);
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        assertEquals("Should add one persuasion", 1, persuasionMap.size());
        
        PersuasionResponse persuasion = persuasionMap.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should have cashback text", "Cashback: ₹151", persuasion.getPersuasionText());
        assertTrue("Should be HTML", persuasion.isHtml());
        assertEquals("Should use cashback icon type", CASHBACK_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
        
        verify(polyglotService).getTranslatedData("CASHBACK_OFFER_TEXT_PWA");
    }

    @Test
    public void should_BuildCashbackPersuasion_When_LoyaltyOfferMessageIsNull() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage(null); // null
        Map<String, Double> hybridDiscounts = new HashMap<>();
        hybridDiscounts.put("CTW", 99.4); // Will round to 99
        coupon.setHybridDiscounts(hybridDiscounts);
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        assertEquals("Should add one persuasion", 1, persuasionMap.size());
        
        PersuasionResponse persuasion = persuasionMap.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should have cashback text", "Cashback: ₹99", persuasion.getPersuasionText());
        assertEquals("Should use cashback icon type", CASHBACK_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
    }

    @Test
    public void should_RoundCashbackAmountCorrectly_When_BuildingCashbackPersuasion() {
        // Test different rounding scenarios
        BestCoupon coupon = new BestCoupon();
        Map<String, Double> hybridDiscounts = new HashMap<>();
        
        // Test 1: 100.4 -> 100
        hybridDiscounts.put("CTW", 100.4);
        coupon.setHybridDiscounts(hybridDiscounts);
        Map<String, PersuasionResponse> persuasionMap1 = new HashMap<>();
        
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap1);
        
        PersuasionResponse persuasion1 = persuasionMap1.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should round down 100.4 to 100", "Cashback: ₹100", persuasion1.getPersuasionText());
        
        // Test 2: 100.6 -> 101
        hybridDiscounts.put("CTW", 100.6);
        Map<String, PersuasionResponse> persuasionMap2 = new HashMap<>();
        
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap2);
        
        PersuasionResponse persuasion2 = persuasionMap2.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should round up 100.6 to 101", "Cashback: ₹101", persuasion2.getPersuasionText());
        
        // Test 3: 100.5 -> 101 (rounds to nearest even)
        hybridDiscounts.put("CTW", 100.5);
        Map<String, PersuasionResponse> persuasionMap3 = new HashMap<>();
        
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap3);
        
        PersuasionResponse persuasion3 = persuasionMap3.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should round 100.5 to 101", "Cashback: ₹101", persuasion3.getPersuasionText());
    }

//    @Test
//    public void should_HandleMissingCTWKey_When_BuildingCashbackPersuasion() {
//        // Given
//        BestCoupon coupon = new BestCoupon();
//        Map<String, Double> hybridDiscounts = new HashMap<>();
//        hybridDiscounts.put("OTHER_KEY", 150.0); // No CTW key
//        coupon.setHybridDiscounts(hybridDiscounts);
//        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
//
//        // When/Then - Should not throw exception
//        try {
//            ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);
//        } catch (Exception e) {
//            // This might throw NullPointerException due to missing CTW key, which is expected behavior
//            assertTrue("Should handle missing CTW key gracefully",
//                    e.getCause() instanceof NullPointerException);
//        }
//    }
//
//    @Test
//    public void should_HandleNullHybridDiscounts_When_BuildingCashbackPersuasion() {
//        // Given
//        BestCoupon coupon = new BestCoupon();
//        coupon.setHybridDiscounts(null); // null hybrid discounts
//        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
//
//        // When/Then - Should not throw exception immediately
//        try {
//            ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);
//        } catch (Exception e) {
//            // This might throw NullPointerException, which is expected
//            assertTrue("Should handle null hybrid discounts",
//                    e.getCause() instanceof NullPointerException);
//        }
//    }

    @Test
    public void should_PreferLoyaltyOverCashback_When_BothAreAvailable() {
        // Given
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Special Loyalty Offer");
        Map<String, Double> hybridDiscounts = new HashMap<>();
        hybridDiscounts.put("CTW", 200.0);
        coupon.setHybridDiscounts(hybridDiscounts);
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        PersuasionResponse persuasion = persuasionMap.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertEquals("Should prefer loyalty over cashback", "Loyalty: Special Loyalty Offer", persuasion.getPersuasionText());
        assertEquals("Should use hero offer icon for loyalty", HERO_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
        
        // Should only call loyalty translation, not cashback
        verify(polyglotService).getTranslatedData("LOYALTY_OFFER_TEXT");
        verify(polyglotService, never()).getTranslatedData("CASHBACK_OFFER_TEXT_PWA");
    }

    // ======================
    // buildGroupFilterForDevice() Tests
    // ======================

    @Test
    public void should_ReturnStaycationFilter_When_StaycationIsTrue() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = Arrays.asList(createFilter("STAYCATION_DEALS"));
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNotNull("Should return staycation filter when staycation is true", result);
        assertEquals("Should have correct text", "GetawayDeals", result.getText());
        assertNotNull("Should have rate plan filter list", result.getRatePlanFilterList());
        assertFalse("Should have filters in list", result.getRatePlanFilterList().isEmpty());
        
        // Verify staycation filter was added and selected
        RatePlanFilter staycationRateFilter = result.getRatePlanFilterList().get(0);
        assertTrue("Should be selected", staycationRateFilter.isSelected());
    }

    @Test
    public void should_ReturnNull_When_StaycationIsFalse() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = new ArrayList<>();
        boolean staycation = false;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNull("Should return null when staycation is false", result);
    }

    @Test
    public void should_CreateUnselectedStaycationFilter_When_FilterCriteriaDoesNotMatch() {
        // Given
        Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap = createMockGroupFilterMap();
        List<Filter> filterCriteria = Arrays.asList(createFilter("OTHER_FILTER"));
        boolean staycation = true;

        // When
        GroupRatePlanFilter result = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupRatePlanFilterConfMap, filterCriteria, staycation);

        // Then
        assertNotNull("Should return staycation filter", result);
        
        RatePlanFilter staycationRateFilter = result.getRatePlanFilterList().get(0);
        assertFalse("Should not be selected when criteria doesn't match", staycationRateFilter.isSelected());
    }

    // ======================
    // PWA-Specific Behavior Tests
    // ======================

    @Test
    public void should_FollowPWAPattern_When_ComparingWithOtherPlatforms() {
        // Test PWA-specific behavior differences:
        
        // 1. Like iOS and Android, returns null for login persuasion
        LoginPersuasion loginPersuasion = ReflectionTestUtils.invokeMethod(transformer, "buildLoginPersuasion");
        assertNull("PWA should not show login persuasion like mobile platforms", loginPersuasion);
        
        // 2. Unlike iOS (empty), PWA has complex loyalty/cashback logic like Desktop
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("PWA Loyalty Offer");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);
        assertFalse("PWA should add loyalty/cashback persuasions unlike iOS", persuasionMap.isEmpty());
        
        // 3. Like mobile platforms, supports staycation filters
        Map<String, GroupRatePlanFilter> groupFilterMap = createMockGroupFilterMap();
        GroupRatePlanFilter groupFilter = ReflectionTestUtils.invokeMethod(transformer, "buildGroupFilterForDevice", 
                groupFilterMap, new ArrayList<>(), true);
        assertNotNull("PWA should support staycation filters like mobile", groupFilter);
        
        // 4. Uses mobile persuasion like iOS and Android
        PersuasionObject topRated = ReflectionTestUtils.invokeMethod(transformer, "createTopRatedPersuasion");
        assertEquals("PWA should use mobile template like other mobile platforms", "IMAGE_TEXT_H", topRated.getTemplate());
    }

    @Test
    public void should_UsePWASpecificTranslations_When_BuildingCashbackPersuasion() {
        // Given
        BestCoupon coupon = new BestCoupon();
        Map<String, Double> hybridDiscounts = new HashMap<>();
        hybridDiscounts.put("CTW", 250.0);
        coupon.setHybridDiscounts(hybridDiscounts);
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        verify(polyglotService).getTranslatedData("CASHBACK_OFFER_TEXT_PWA");
        verify(polyglotService, never()).getTranslatedData("CASHBACK_OFFER_TEXT"); // Should use PWA-specific key
    }

    @Test
    public void should_HandleComplexPersuasionBuilding_When_ProcessingCoupons() {
        // Test the complete flow with StringBuilder
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Complex Loyalty Message with Special Characters & Symbols");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();

        // When
        ReflectionTestUtils.invokeMethod(transformer, "buildLoyaltyCashbackPersuasions", coupon, persuasionMap);

        // Then
        PersuasionResponse persuasion = persuasionMap.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertTrue("Should contain the loyalty message", 
                persuasion.getPersuasionText().contains("Complex Loyalty Message with Special Characters & Symbols"));
        assertTrue("Should be marked as HTML", persuasion.isHtml());
        assertEquals("Should use hero offer icon", HERO_OFFER_PERSUASION_ICON_TYPE, persuasion.getIconType());
    }

    // ======================
    // Helper Methods
    // ======================

    private Map<String, GroupRatePlanFilter> createMockGroupFilterMap() {
        Map<String, GroupRatePlanFilter> map = new HashMap<>();
        
        GroupRatePlanFilter getawayDealsFilter = new GroupRatePlanFilter();
        getawayDealsFilter.setText("GetawayDeals");
        getawayDealsFilter.setRatePlanFilterList(new ArrayList<>());
        
        map.put("GetawayDeals", getawayDealsFilter);
        return map;
    }

    private Filter createFilter(String filterValue) {
        Filter filter = new Filter();
        filter.setFilterValue(filterValue);
        return filter;
    }
} 