package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.corporate.UpdateApprovalResponse;
import com.mmt.hotels.model.response.corporate.UpdateWorkflowResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UpdateApprovalResponseTransformerTest {

    @InjectMocks
    UpdateApprovalResponseTransformer updateApprovalResponseTransformer;

    @Test
    public void convertUpdateApprovalResponseTest() {
        UpdateWorkflowResponse responseHES = new UpdateWorkflowResponse();
        UpdateApprovalResponse updateApprovalResponse = new UpdateApprovalResponse();
        responseHES.setMessage("msg");
        responseHES.setResponseCode("300");
        responseHES.setStatus("ok");
        responseHES.setStatusCode(200);
        updateApprovalResponse = updateApprovalResponseTransformer.convertUpdateApprovalResponse(responseHES);
        Assert.assertNotNull(updateApprovalResponse);
        Assert.assertEquals("msg", updateApprovalResponse.getMessage());
        Assert.assertEquals("300", updateApprovalResponse.getResponseCode());
        Assert.assertEquals("ok", updateApprovalResponse.getStatus());
        Assert.assertEquals(new Integer(200), updateApprovalResponse.getStatusCode());
    }
}
