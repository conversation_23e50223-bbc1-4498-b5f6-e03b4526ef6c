package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import static org.junit.jupiter.api.Assertions.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.enums.OTA;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails;
import com.gommt.hotels.orchestrator.model.response.listing.*;
import com.gommt.hotels.orchestrator.model.response.listing.ImageDetails;
import com.gommt.hotels.orchestrator.model.response.ugc.ListingReviewDetails;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.flyfish.ReviewSummary;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import org.junit.Assert.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerSCIONTest {

    @InjectMocks
    private OrchSearchHotelsResponseTransformerSCION transformer;

    @Mock
    PolyglotService polyglotService;

    @Mock
    DateUtil dateUtil;

    @Mock
    private Utility utility;

    private static final String CORRELATION_ID = "test-correlation-id";
    private static final String CATEGORY_DETAILS = "{\"LUXURY\":{\"data\":[\"Luxury Hotel\"]}}";
    private static final String DEEP_LINK = "http://example.com/deeplink";
    private static final String ROOT_LEVEL_DEEPLINK = "http://example.com/root";
    private static final String ROOT_LEVEL_DEEPLINK_GLOBAL = "http://example.com/global";

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(transformer, "categoryDetailsText", CATEGORY_DETAILS);
        ReflectionTestUtils.setField(transformer, "deepLink", DEEP_LINK);
        ReflectionTestUtils.setField(transformer, "rootLevelDeeplink", ROOT_LEVEL_DEEPLINK);
        //ReflectionTestUtils.setField(transformer, "rootLevelDeeplinkGlobal", ROOT_LEVEL_DEEPLINK_GLOBAL);
        ReflectionTestUtils.setField(transformer, "hotelLevelAppDeepLink", ROOT_LEVEL_DEEPLINK_GLOBAL);
        ReflectionTestUtils.setField(transformer, "rootLevelSharingUrl", ROOT_LEVEL_DEEPLINK_GLOBAL);
        ReflectionTestUtils.setField(transformer, "gson", new Gson());
        MDC.put(MDCHelper.MDCKeys.CORRELATION.getStringValue(), CORRELATION_ID);
        when(polyglotService.getTranslatedData(any())).thenReturn("testTranslation");
        when(dateUtil.getDateFormatted(any(), any(), any())).thenReturn("2024-01-01");

        transformer.init();
    }

    @Test
    public void testConvertSearchHotelsResponse_NullListingResponse() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        String response = transformer.convertSearchHotelsResponse(request, null);
        Assert.assertNull(response);
    }

    @Test
    public void testConvertSearchHotelsResponse_NullPersonalizedSections() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        ListingResponse listingResponse = new ListingResponse();
        String response = transformer.convertSearchHotelsResponse(request, listingResponse);
        Assert.assertNull(response);
    }

    @Test
    public void testConvertSearchHotelsResponse_WithError() {
        SearchHotelsRequest request = createSearchRequest(false);
        ListingResponse listingResponse = createListingResponse();

        ErrorResponse error = new ErrorResponse();
        error.setCode("TEST_ERROR");
        error.setMessage("Test error message");
        listingResponse.setError(error);

        String response = transformer.convertSearchHotelsResponse(request, listingResponse);

        Assert.assertNotNull(response);
        Assert.assertTrue(response.contains("TEST_ERROR"));
    }

    @Test
    public void testConvertSearchHotelsResponse_WithGlobalUser() {
        SearchHotelsRequest request = createSearchRequest(true);
        ListingResponse listingResponse = createListingResponse();

        String response = transformer.convertSearchHotelsResponse(request, listingResponse);

        Assert.assertNotNull(response);
        Assert.assertTrue(response.contains(ROOT_LEVEL_DEEPLINK_GLOBAL));
    }

    @Test
    public void testConvertSearchHotelsResponse_WithExclusiveHotels() {
        SearchHotelsRequest request = createSearchRequest(false);
        ListingResponse listingResponse = createListingResponseWithExclusiveHotels();

        String response = transformer.convertSearchHotelsResponse(request, listingResponse);
        Assert.assertNotNull(response);
    }

    private SearchHotelsRequest createSearchRequest(boolean isGlobal) {
        SearchHotelsRequest request = new SearchHotelsRequest();
        SearchHotelsCriteria criteria = new SearchHotelsCriteria();

        // Basic location and currency details
        criteria.setCurrency("INR");
        criteria.setLocationId("CITY123");
        criteria.setLocationType("CITY");
        criteria.setCountryCode("IN");
        criteria.setCityName("Test City");

        // Coordinates
        criteria.setLat(28.0);
        criteria.setLng(77.0);

        // Dates
        criteria.setCheckIn("2024-01-01");
        criteria.setCheckOut("2024-01-02");

        // Room stay candidates
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> childAges = Arrays.asList(5, 8);
        roomStayCandidate.setChildAges(childAges);
        roomStayCandidates.add(roomStayCandidate);
        criteria.setRoomStayCandidates(roomStayCandidates);

        // Collection criteria
        CollectionCriteria collectionCriteria = new CollectionCriteria();
        collectionCriteria.setCollectionIds(Arrays.asList("COLL1", "COLL2"));
        collectionCriteria.setCollectionsCount("2");
        collectionCriteria.setCollectionRequired(true);
        collectionCriteria.setTrendingNow(true);
        collectionCriteria.setSuggestedForYouCards(true);
        collectionCriteria.setPropertyTypeCards(true);
        collectionCriteria.setStaticFilterCardsRequired(true);
        collectionCriteria.setDiscoverByDestinationCardsRequired(true);
        collectionCriteria.setInspiredCardsRequired(true);
        collectionCriteria.setOffbeatCitiesCardsRequired(true);
        collectionCriteria.setValueStayCardsRequired(true);
        collectionCriteria.setAthenaCategory("default");
        collectionCriteria.setLuxeCardRequired(true);
        collectionCriteria.setBannerListCardRequired(true);
        collectionCriteria.setFamilyCardRequired(true);
        collectionCriteria.setHostCardRequired(true);
        criteria.setCollectionCriteria(collectionCriteria);

        // Sort criteria
        SortCriteria sortCriteria = new SortCriteria();
        sortCriteria.setField("PRICE");
        sortCriteria.setOrder("ASC");

        // Global user info if needed
        if (isGlobal) {
            UserGlobalInfo globalInfo = new UserGlobalInfo();
            globalInfo.setEntityName("GLOBAL");
            criteria.setUserGlobalInfo(globalInfo);
        }

        request.setSearchCriteria(criteria);
        return request;
    }

    private ListingResponse createListingResponse() {
        ListingResponse response = new ListingResponse();

        // Set basic fields
        response.setLastPage(true);
        response.setLastHotelId("");
        response.setCurrency("INR");
        response.setTrackingMap(new HashMap<>());

        // Set location
        LocationDetails location = new LocationDetails();
        location.setId("CITY123");
        location.setType("CITY");
        location.setCityName("Test City");
        location.setCountryId("IN");
        location.setCountryName("India");
        response.setLocation(location);

        // Set personalized sections
        List<PersonalizedSectionDetails> sections = new ArrayList<>();
        PersonalizedSectionDetails section = new PersonalizedSectionDetails();
        section.setName("Test Section");
        section.setHotelCount(1);

        // Add hotel details
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        Set<String> categories = new HashSet<>();
        categories.add("LUXURY");
        hotel.setCategories(categories);
        hotels.add(hotel);

        section.setHotels(hotels);
        sections.add(section);
        response.setPersonalizedSections(sections);

        return response;
    }

    private ListingResponse createListingResponseWithExclusiveHotels() {
        ListingResponse response = createListingResponse();

        PersonalizedSectionDetails exclusiveSection = new PersonalizedSectionDetails();
        exclusiveSection.setName("EXCLUSIVE_HOTELS");
        exclusiveSection.setHotelCount(1);
        exclusiveSection.setFilterToHotelMap(new HashMap<>());

        response.getPersonalizedSections().add(exclusiveSection);

        return response;
    }

    @Test
    public void testBuildDisplayFarePrice_NullHotelEntity() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, null);
        Assert.assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_NullRooms() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        Assert.assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_EmptyRooms() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        hotelEntity.setRooms(new ArrayList<>());
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        Assert.assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_NullRatePlans() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        List<RoomEntity> rooms = new ArrayList<>();
        RoomEntity room = new RoomEntity();
        rooms.add(room);
        hotelEntity.setRooms(rooms);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        Assert.assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_EmptyRatePlans() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        List<RoomEntity> rooms = new ArrayList<>();
        RoomEntity room = new RoomEntity();
        room.setRatePlans(new ArrayList<>());
        rooms.add(room);
        hotelEntity.setRooms(rooms);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        Assert.assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_NullPrice() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = createHotelEntityWithRatePlan(null);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        Assert.assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_WithPrice() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PriceDetail price = new PriceDetail();
        price.setDisplayPrice(1000.0);
        price.setSavingPerc(10.0);
        price.setBlackDiscount(100.0);
        price.setBasePrice(1100.0);
        price.setTotalRoomCount(2);

        HotelDetails hotelEntity = createHotelEntityWithRatePlan(price);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);

        Assert.assertNotNull(searchWrapperHotelEntity.getDisplayFare());
        Assert.assertNotNull(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown());
        Assert.assertEquals(1000.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice(), 0.01);
        Assert.assertEquals(10.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getSavingPerc(), 0.01);
        Assert.assertEquals(100.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getBlackDiscount(), 0.01);
        Assert.assertEquals(1100.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getNonDiscountedPrice(), 0.01);
        Assert.assertEquals(new Integer(2), searchWrapperHotelEntity.getDisplayFare().getTotalRoomCount());
    }

    @Test
    public void testBuildDisplayFarePrice_WithCoupons() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PriceDetail price = new PriceDetail();
        price.setDisplayPrice(1000.0);

        List<PriceCouponInfo> coupons = new ArrayList<>();
        PriceCouponInfo coupon = new PriceCouponInfo();
        coupon.setCouponCode("TEST123");
        coupon.setDescription("Test Coupon");
        coupon.setDiscount(50.0);
        coupon.setSpecialPromoCoupon(true);
        coupon.setType("DISCOUNT");
        coupons.add(coupon);
        price.setApplicableCoupons(coupons);

        HotelDetails hotelEntity = createHotelEntityWithRatePlan(price);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);

        Assert.assertNotNull(searchWrapperHotelEntity.getDisplayFare());
        Assert.assertNotNull(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo());
        Assert.assertEquals("TEST123", searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode());
        Assert.assertEquals("Test Coupon", searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getDescription());
        Assert.assertEquals(50.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getDiscountAmount(), 0.01);
        Assert.assertTrue(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().isSpecialPromoCoupon());
        Assert.assertEquals("DISCOUNT", searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getType());
    }

    @Test
    public void testBuildDisplayFarePrice_WithMealPlans() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = createHotelEntityWithRatePlan(new PriceDetail());

        List<MealPlan> mealPlans = new ArrayList<>();
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("BB");
        mealPlan.setValue("Bed & Breakfast");
        mealPlans.add(mealPlan);
        hotelEntity.getRooms().get(0).getRatePlans().get(0).setMealPlans(mealPlans);

        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);

        Assert.assertNotNull(searchWrapperHotelEntity.getMealPlanIncluded());
        Assert.assertEquals("BB", searchWrapperHotelEntity.getMealPlanIncluded().getCode());
        Assert.assertEquals("Bed & Breakfast", searchWrapperHotelEntity.getMealPlanIncluded().getDesc());
    }

    private HotelDetails createHotelEntityWithRatePlan(PriceDetail price) {
        HotelDetails hotelEntity = new HotelDetails();
        List<RoomEntity> rooms = new ArrayList<>();
        RoomEntity room = new RoomEntity();
        List<ResponseRatePlan> ratePlans = new ArrayList<>();
        ResponseRatePlan ratePlan = new ResponseRatePlan();
        ratePlan.setPrice(price);
        ratePlans.add(ratePlan);
        room.setRatePlans(ratePlans);
        rooms.add(room);
        hotelEntity.setRooms(rooms);
        return hotelEntity;
    }

    @Test
    public void testGetMainImages_NullImageList() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        List<String> result = transformer.getMainImages(null, request);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetMainImages_EmptyImageList() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        List<ImageDetails> imageList = new ArrayList<>();
        List<String> result = transformer.getMainImages(imageList, request);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetMainImages_NullAdditionalProperties() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        List<ImageDetails> imageList = createSampleImageList();

        List<String> result = transformer.getMainImages(imageList, request);

        Assert.assertNotNull(result);
        assertEquals(3, result.size()); // Default imageCount is 1
        Assert.assertEquals("http://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void testGetMainImages_WithImageCountAndNoSecureUrl() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "2");
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList();

        List<String> result = transformer.getMainImages(imageList, request);

        Assert.assertNotNull(result);
        assertEquals(3, result.size());
        Assert.assertEquals("http://example.com/image1.jpg", result.get(0));
        Assert.assertEquals("http://example.com/image2.jpg", result.get(1));
    }

    @Test
    public void testGetMainImages_WithSecureUrl() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "2");
        additionalProperties.put("secureUrl", "https://secure");
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList();
        // Set SCION request flag
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setRequestor("SCION");

        List<String> result = transformer.getMainImages(imageList, request);

        Assert.assertNotNull(result);
        assertEquals(2, result.size());
        Assert.assertEquals("https://secure:http://example.com/image1.jpg", result.get(0));
        Assert.assertEquals("https://secure:http://example.com/image2.jpg", result.get(1));
    }

    @Test
    public void testGetMainImages_RequestedMoreThanAvailable() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "5"); // Request more images than available
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList(); // Creates 3 images
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setRequestor("SCION");

        List<String> result = transformer.getMainImages(imageList, request);

        Assert.assertNotNull(result);
        assertEquals(3, result.size()); // Should return all available images
    }

    @Test
    public void testGetMainImages_NonScionRequest() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "2");
        additionalProperties.put("secureUrl", "https://secure");
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList();
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setRequestor("TEST_REQUESTOR");

        List<String> result = transformer.getMainImages(imageList, request);

        Assert.assertNotNull(result);
        Assert.assertEquals(3, result.size()); // Should return all images without modification
        Assert.assertEquals("http://example.com/image1.jpg", result.get(0));
        Assert.assertEquals("http://example.com/image2.jpg", result.get(1));
        Assert.assertEquals("http://example.com/image3.jpg", result.get(2));
    }

    // Helper method to create sample image list
    private List<ImageDetails> createSampleImageList() {
        List<ImageDetails> imageList = new ArrayList<>();

        ImageDetails image1 = new ImageDetails();
        image1.setUrl("http://example.com/image1.jpg");
        imageList.add(image1);

        ImageDetails image2 = new ImageDetails();
        image2.setUrl("http://example.com/image2.jpg");
        imageList.add(image2);

        ImageDetails image3 = new ImageDetails();
        image3.setUrl("http://example.com/image3.jpg");
        imageList.add(image3);

        return imageList;
    }

    @Test
    public void testBuildReviewSummary_NullReviewDetails() {
        HotelDetails hotelEntity = new HotelDetails();
        ReviewSummary result = transformer.buildReviewSummary(hotelEntity);

        Assert.assertNotNull(result);
        Assert.assertNull(result.getOta());
        Assert.assertNotNull(result.getHotelRating());
    }

    @Test
    public void testBuildReviewSummary_WithReviewDetails() {
        HotelDetails hotelEntity = new HotelDetails();
        ListingReviewDetails reviewDetails = new ListingReviewDetails();
        reviewDetails.setOta("MMT");
        reviewDetails.setRating(4.5f);
        reviewDetails.setTotalReviewCount(100);
        reviewDetails.setTotalRatingCount(150);
        hotelEntity.setReviewDetails(reviewDetails);

        ReviewSummary result = transformer.buildReviewSummary(hotelEntity);

        Assert.assertNotNull(result);
        Assert.assertEquals("MMT", result.getOta());
        Assert.assertEquals(4.5f, result.getHotelRating(), 0.001);
        Assert.assertEquals(100, result.getReviewCount());
        Assert.assertEquals(150, result.getRatingCount());
    }

    @Test
    public void testBuildReviewSummary_NullHotelEntity() {
        ReviewSummary result = transformer.buildReviewSummary(null);

        Assert.assertNotNull(result);
        Assert.assertNull(result.getOta());
    }

    @Test
    public void testBuildFilterToHotelMap_NullInput() {
        Map<String, com. gommt. hotels. orchestrator. model. response. listing.ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = null;
        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
                transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFilterToHotelMap_EmptyInput() {
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();
        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
                transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFilterToHotelMap_WithEmptyHotels() {
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();
        ExclusiveFilterHotels<HotelDetails> exclusiveFilterHotels = new ExclusiveFilterHotels<>();
        exclusiveFilterHotels.setHotels(new ArrayList<>());
        TabDetails tabDetails = new TabDetails();
        exclusiveFilterHotels.setTabDetails(tabDetails);
        filterToHotelMap.put("testFilter", exclusiveFilterHotels);

        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
                transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        Assert.assertNotNull(result);
        assertEquals(1, result.size());
        Assert.assertTrue(result.containsKey("testFilter"));
        Assert.assertNotNull(result.get("testFilter").getTabDetails());
        Assert.assertTrue(CollectionUtils.isEmpty(result.get("testFilter").getHotels()));
    }

    @Test
    public void testBuildFilterToHotelMap_WithHotels() {
        // Setup input data
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();
        ExclusiveFilterHotels<HotelDetails> exclusiveFilterHotels = new ExclusiveFilterHotels<>();

        // Create sample hotel details
        List<HotelDetails> hotelList = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setId("TEST123");
        hotelList.add(hotel);
        exclusiveFilterHotels.setHotels(hotelList);

        // Create sample tab details
        TabDetails tabDetails = new TabDetails();
        tabDetails.setTitle("Test Tab");
        exclusiveFilterHotels.setTabDetails(tabDetails);

        filterToHotelMap.put("testFilter", exclusiveFilterHotels);

        // Setup listing response and request
        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        // Execute method
        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
                transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        // Verify results
        Assert.assertNotNull(result);
        assertEquals(1, result.size());
        Assert.assertTrue(result.containsKey("testFilter"));
        Assert.assertNotNull(result.get("testFilter").getTabDetails());
        Assert.assertFalse(CollectionUtils.isEmpty(result.get("testFilter").getHotels()));
        assertEquals(1, result.get("testFilter").getHotels().size());
    }

    @Test
    public void testBuildFilterToHotelMap_MultipleFilters() {
        // Setup input data
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();

        // Create first filter
        ExclusiveFilterHotels<HotelDetails> filter1 = new ExclusiveFilterHotels<>();
        List<HotelDetails> hotelList1 = new ArrayList<>();
        HotelDetails hotel1 = new HotelDetails();
        hotel1.setId("TEST123");
        hotelList1.add(hotel1);
        filter1.setHotels(hotelList1);
        TabDetails tabDetails1 = new TabDetails();
        tabDetails1.setTitle("Tab 1");
        filter1.setTabDetails(tabDetails1);
        filterToHotelMap.put("filter1", filter1);

        // Create second filter
        ExclusiveFilterHotels<HotelDetails> filter2 = new ExclusiveFilterHotels<>();
        List<HotelDetails> hotelList2 = new ArrayList<>();
        HotelDetails hotel2 = new HotelDetails();
        hotel2.setId("TEST456");
        hotelList2.add(hotel2);
        filter2.setHotels(hotelList2);
        TabDetails tabDetails2 = new TabDetails();
        tabDetails2.setTitle("Tab 2");
        filter2.setTabDetails(tabDetails2);
        filterToHotelMap.put("filter2", filter2);

        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        // Execute method
        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
                transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        // Verify results
        Assert.assertNotNull(result);
        assertEquals(2, result.size());
        Assert.assertTrue(result.containsKey("filter1"));
        Assert.assertTrue(result.containsKey("filter2"));
        assertEquals(1, result.get("filter1").getHotels().size());
        assertEquals(1, result.get("filter2").getHotels().size());
        Assert.assertEquals("Tab 1", result.get("filter1").getTabDetails().getTitle());
        Assert.assertEquals("Tab 2", result.get("filter2").getTabDetails().getTitle());
    }

    @Test
    public void testConvertTabDetails_NullInput() {
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(null, searchRequest);
        Assert.assertNull(result);
    }

    @Test
    public void testConvertTabDetails_FullDetails() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
                new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();

        sourceTabDetails.setId("tab123");
        sourceTabDetails.setTitle("Luxury Hotels");
        sourceTabDetails.setSelected(true);
        sourceTabDetails.setIconUrl("https://example.com/icon.png");
        sourceTabDetails.setFilterGroup("AMENITIES");
        sourceTabDetails.setFilterValue("POOL");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals("tab123", result.getId());
        Assert.assertEquals("Luxury Hotels", result.getTitle());
        Assert.assertTrue(result.isSelected());
        Assert.assertEquals("https://example.com/icon.png", result.getIconUrl());
        Assert.assertEquals("AMENITIES", result.getFilterGroup());
        Assert.assertEquals("POOL", result.getFilterValue());
        Assert.assertNotNull(result.getDeeplink()); // Assuming deeplink is generated
    }

    @Test
    public void testConvertTabDetails_MinimalDetails() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
                new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();

        sourceTabDetails.setId("tab123");
        sourceTabDetails.setTitle("Budget Hotels");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals("tab123", result.getId());
        Assert.assertEquals("Budget Hotels", result.getTitle());
        Assert.assertFalse(result.isSelected());
        Assert.assertNull(result.getIconUrl());
        Assert.assertNull(result.getFilterGroup());
        Assert.assertNull(result.getFilterValue());
        Assert.assertNotNull(result.getDeeplink());
    }

    @Test
    public void testConvertTabDetails_WithFilterGroupAndValue() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
                new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();

        sourceTabDetails.setId("tab123");
        sourceTabDetails.setFilterGroup("STAR_RATING");
        sourceTabDetails.setFilterValue("5_STAR");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals("tab123", result.getId());
        Assert.assertEquals("STAR_RATING", result.getFilterGroup());
        Assert.assertEquals("5_STAR", result.getFilterValue());
        Assert.assertNotNull(result.getDeeplink());
    }

    @Test
    public void testConvertTabDetails_WithSearchRequest() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        SearchHotelsCriteria criteria = new SearchHotelsCriteria();
        criteria.setCityName("Mumbai");
        criteria.setLocationId("1234");
        criteria.setCountryCode("IN");
        searchRequest.setSearchCriteria(criteria);

        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
                new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();
        sourceTabDetails.setId("tab123");
        sourceTabDetails.setTitle("City Hotels");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals("tab123", result.getId());
        Assert.assertEquals("City Hotels", result.getTitle());
        Assert.assertNotNull(result.getDeeplink());
        // Verify that deeplink contains search criteria information
        Assert.assertTrue(result.getDeeplink().contains("Mumbai"));
        Assert.assertTrue(result.getDeeplink().contains("1234"));
    }

    @Test
    public void testConvertTabDetails_EmptySearchRequest() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
                new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();
        sourceTabDetails.setId("tab123");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals("tab123", result.getId());
        Assert.assertNotNull(result.getDeeplink());
    }

    @Test
    public void testConvertAddress_WithValidAddress() {
        // Arrange
        com.gommt.hotels.orchestrator.model.response.content.Address sourceAddress = new com.gommt.hotels.orchestrator.model.response.content.Address();
        sourceAddress.setArea(Collections.singletonList("Bandra"));
        sourceAddress.setLine1("24, Turner Road");
        sourceAddress.setLine2("Near Linking Road");

        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(sourceAddress);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(Collections.singletonList("Bandra"), result.getArea());
        Assert.assertEquals("24, Turner Road", result.getLine1());
        Assert.assertEquals("Near Linking Road", result.getLine2());
    }

    @Test
    public void testConvertAddress_WithNullAddress() {
        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(null);

        // Assert
        Assert.assertNull(result);
    }

    @Test
    public void testConvertAddress_WithEmptyAddress() {
        // Arrange
        com.gommt.hotels.orchestrator.model.response.content.Address sourceAddress = new com.gommt.hotels.orchestrator.model.response.content.Address();

        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(sourceAddress);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertNull(result.getArea());
        Assert.assertNull(result.getLine1());
        Assert.assertNull(result.getLine2());
    }

    @Test
    public void testConvertAddress_WithPartialAddress() {
        // Arrange
        com.gommt.hotels.orchestrator.model.response.content.Address sourceAddress = new com.gommt.hotels.orchestrator.model.response.content.Address();
        sourceAddress.setArea(Collections.singletonList("Andheri East"));
        sourceAddress.setLine1("Near Metro Station");

        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(sourceAddress);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(Collections.singletonList("Andheri East"), result.getArea());
        Assert.assertEquals("Near Metro Station", result.getLine1());
        Assert.assertNull(result.getLine2());
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithValidReviewDetails() {
        // Arrange
        HotelDetails hotelEntity = new HotelDetails();
        ListingReviewDetails reviewDetails = new ListingReviewDetails();
        reviewDetails.setOta("MMT");
        reviewDetails.setRating(4.5f);
        reviewDetails.setTotalReviewCount(100);
        reviewDetails.setTotalRatingCount(150);
        hotelEntity.setReviewDetails(reviewDetails);

        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(hotelEntity);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertFalse(result.containsKey(OTA.MMT));
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithNullHotelEntity() {
        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(null);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithNullReviewDetails() {
        // Arrange
        HotelDetails hotelEntity = new HotelDetails();
        hotelEntity.setReviewDetails(null);

        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(hotelEntity);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithNullOta() {
        // Arrange
        HotelDetails hotelEntity = new HotelDetails();
        ListingReviewDetails reviewDetails = new ListingReviewDetails();
        reviewDetails.setOta(null);
        reviewDetails.setRating(4.5f);
        reviewDetails.setTotalReviewCount(100);
        reviewDetails.setTotalRatingCount(150);
        hotelEntity.setReviewDetails(reviewDetails);

        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(hotelEntity);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithLowerCaseOta() {
        // Arrange
        HotelDetails hotelEntity = new HotelDetails();
        ListingReviewDetails reviewDetails = new ListingReviewDetails();
        reviewDetails.setOta("ta");  // lowercase OTA code
        reviewDetails.setRating(4.5f);
        reviewDetails.setTotalReviewCount(100);
        reviewDetails.setTotalRatingCount(150);
        hotelEntity.setReviewDetails(reviewDetails);

        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(hotelEntity);

        // Assert
        Assert.assertNotNull(result);
        assertEquals(1, result.size());
    }

}