package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.AffiliateResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.AffiliateResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.AffiliateResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.AffiliateResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.AffiliateResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class AffiliateFactoryTest {
    @InjectMocks
    AffiliateFactory affiliateFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(affiliateFactory, "affiliateResponseTransformerPWA", new AffiliateResponseTransformerPWA());
        ReflectionTestUtils.setField(affiliateFactory, "affiliateResponseTransformerAndroid", new AffiliateResponseTransformerAndroid());
        ReflectionTestUtils.setField(affiliateFactory, "affiliateResponseTransformerDesktop", new AffiliateResponseTransformerDesktop());
        ReflectionTestUtils.setField(affiliateFactory, "affiliateResponseTransformerIOS", new AffiliateResponseTransformerIOS());
    }

    @Test
    public void getResponseServiceTest() {
        AffiliateResponseTransformer resp = affiliateFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof AffiliateResponseTransformerPWA);
        resp = affiliateFactory.getResponseService("MSITE");
        Assert.assertTrue(resp instanceof AffiliateResponseTransformerPWA);
        resp = affiliateFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof AffiliateResponseTransformerDesktop);
        resp = affiliateFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof AffiliateResponseTransformerAndroid);
        resp = affiliateFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof AffiliateResponseTransformerIOS);
        resp = affiliateFactory.getResponseService("");
        Assert.assertTrue(resp instanceof AffiliateResponseTransformerDesktop);
        resp = affiliateFactory.getResponseService("test");
        Assert.assertTrue(resp instanceof AffiliateResponseTransformerDesktop);
    }
}
