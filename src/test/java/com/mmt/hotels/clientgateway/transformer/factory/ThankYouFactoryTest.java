package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.StaticDetailRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.StaticDetailRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.StaticDetailRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.StaticDetailRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.StaticDetailRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.StaticDetailResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.android.ThankYouResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.StaticDetailResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.desktop.ThankYouResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.StaticDetailResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.ios.ThankYouResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.StaticDetailResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ThankYouResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ThankYouFactoryTest {

    @InjectMocks
    ThankYouFactory thankYouFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(thankYouFactory,"thankYouResponseTransformerPWA" , new ThankYouResponseTransformerPWA());
        ReflectionTestUtils.setField(thankYouFactory,"thankYouResponseTransformerAndroid" , new ThankYouResponseTransformerAndroid());
        ReflectionTestUtils.setField(thankYouFactory,"thankYouResponseTransformerDesktop" , new ThankYouResponseTransformerDesktop());
        ReflectionTestUtils.setField(thankYouFactory,"thankYouResponseTransformerIOS" , new ThankYouResponseTransformerIOS());

    }

    @Test
    public void getResponseServiceTest(){
        ThankYouResponseTransformer resp = thankYouFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof ThankYouResponseTransformerPWA  );
        resp = thankYouFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof ThankYouResponseTransformerDesktop  );
        resp = thankYouFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof ThankYouResponseTransformerAndroid  );
        resp = thankYouFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof ThankYouResponseTransformerIOS  );
        resp = thankYouFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(thankYouFactory.getResponseService("test") instanceof ThankYouResponseTransformerDesktop);
    }
}
