package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.AddonsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AddonsReponseTransformer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AddonsServiceFactoryTest {

    @InjectMocks
    private AddonsServiceFactory addonsServiceFactory;
    @Mock
    private AddonsReponseTransformer addonsReponseTransformer;
    @Mock
    private AddonsRequestTransformer addonsRequestTransformer;

    @Test
    public void getRequestServiceTest() {
        Assert.assertNotNull(addonsServiceFactory.getRequestService("PWA"));
    }

    @Test
    public void getResponseServiceTest() {
        Assert.assertNotNull(addonsServiceFactory.getResponseService("PWA"));
    }
}
