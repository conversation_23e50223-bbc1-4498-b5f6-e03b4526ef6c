package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.model.RoomInfo;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT_GCC;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_TEXT_GCC;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @since 12-Jul-2022, Tuesday 6:48 PM
 */
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsResponseTransformerDesktopTest {

    @InjectMocks
    SearchRoomsResponseTransformerDesktop searchRoomsResponseTransformerDesktop;

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private Utility utility;

    @Test
    public void testCreateTopRatedPersuasion() {
        Mockito.when(polyglotService.getTranslatedData(anyString())).thenReturn("Top Rated");
        PersuasionObject topRatedPersuasion = searchRoomsResponseTransformerDesktop.createTopRatedPersuasion();
        assertNotNull(topRatedPersuasion);
    }

    @Test
    public void testBuildLoginPersuasionGcc() {
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        Mockito.when(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT_GCC)).thenReturn("Login Persuasion Text");
        Mockito.when(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT_GCC)).thenReturn("Login Persuasion SubText");
        LoginPersuasion loginPersuasion = searchRoomsResponseTransformerDesktop.buildLoginPersuasion();
        assertNotNull(loginPersuasion);
    }

    @Test
    public void testBuildLoginPersuasionIn() {
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
        Mockito.when(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT)).thenReturn("Login Persuasion Text");
        Mockito.when(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT)).thenReturn("Login Persuasion SubText");
        LoginPersuasion loginPersuasion = searchRoomsResponseTransformerDesktop.buildLoginPersuasion();
        assertNotNull(loginPersuasion);
    }

    @Test
    public void reorderInclusionsTest() {
        List<BookedInclusion> inclusions = new ArrayList<>();
        inclusions.add(new BookedInclusion());
        List<BookedInclusion> result = ReflectionTestUtils
                .invokeMethod(utility, "reorderInclusions", inclusions);
        Assert.assertNotNull(result);
        inclusions.get(0).setCategory("ZPN");
        result = ReflectionTestUtils
                .invokeMethod(utility, "reorderInclusions", inclusions);
        Assert.assertNotNull(result);
    }
    @Test
    public void testBuildRoomSizeText() {
        // Arrange
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("100");
        roomInfo.setRoomSizeUnit("sq.ft");

        // Act
        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerDesktop,"buildRoomSizeText",roomInfo);

        // Assert
        assertEquals("100 sq.ft (9 sq.mt)", result);

        //For ambiguous roomSize
        roomInfo.setRoomSize("xyz");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerDesktop,"buildRoomSizeText",roomInfo);
        assertEquals("xyz sq.ft", result);

        //For roomSizeUnit is not sq.ft
        roomInfo.setRoomSize("100");
        roomInfo.setRoomSizeUnit("sq.inch");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerDesktop,"buildRoomSizeText",roomInfo);
        assertEquals("100 sq.inch", result);

        //For roomInfo is null
        roomInfo= null;
        assertNull(ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerDesktop,"buildRoomSizeText",roomInfo));

    }

}