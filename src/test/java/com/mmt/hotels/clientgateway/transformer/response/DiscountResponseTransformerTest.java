package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.model.response.discount.ValidCouponResult;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;

@RunWith(MockitoJUnitRunner.class)
public class DiscountResponseTransformerTest {

	@InjectMocks
	DiscountResponseTransformer discountResponseTransformer;
	
	@Mock
	CommonResponseTransformer commonResponseTransformer;

	@Mock
	private Utility utility;

	@Mock
	private PolyglotService polyglotService;

	@Before
	public void setup() {
		ReflectionTestUtils.setField(commonResponseTransformer, "intlNrSupplierExclusionList", new ArrayList<>());
		ReflectionTestUtils.setField(commonResponseTransformer, "propertyRulesMaxCount", 4);
		ReflectionTestUtils.setField(commonResponseTransformer, "corpSegments", new HashSet<>(Arrays.asList("1135", "1152")));
	}

	@Test
	public void testConvertValidateCouponResponse(){
		ValidCouponResult validCouponresult = new ValidCouponResult();
		ValidateCouponResponse validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		validateCouponResponse.setDisplayPriceBreakDown(displayPriceBreakDown );
		String bnplUnavailableMsg = "bnpl unavailable";
		validateCouponResponse.setBnplUnavailableMsg(bnplUnavailableMsg);
		ValidateCouponResponseBody resp = discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN", false,"");
		Assert.assertNotNull(resp);
		Assert.assertEquals(bnplUnavailableMsg, resp.getTotalPricing().getBnplUnavailableMsg());
	}
	
}
