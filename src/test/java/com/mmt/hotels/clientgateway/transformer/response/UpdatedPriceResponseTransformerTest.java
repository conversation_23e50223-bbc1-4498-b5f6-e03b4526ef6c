package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.UpdatedPriceRoomCriteria;
import com.mmt.hotels.clientgateway.request.UpdatedPriceCriteria;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.HermesRatePlanData;
import com.mmt.hotels.clientgateway.response.hermes.cgresponse.PaxWiseInfoResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class UpdatedPriceResponseTransformerTest {

    @InjectMocks
    private UpdatedPriceResponseTransformer updatedPriceResponseTransformer;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private Utility utility;

    @Mock
    private DateUtil dateUtil;

    private static final String TEST_CURRENCY = "INR";
    private static final String TEST_CHECK_IN = "2024-02-15";
    private static final String TEST_CHECK_OUT = "2024-02-17";
    private static final String TEST_RATE_PLAN_CODE = "TEST_RATE_PLAN";
    private static final String TEST_ROOM_CODE = "TEST_ROOM";
    private static final String TEST_SELLABLE_TYPE = "HOTEL";
    private static final String TEST_FUNNEL_SOURCE = "WEB";

    @Before
    public void setUp() {
        Mockito.reset(commonResponseTransformer, utility, dateUtil);
    }

    @Test
    public void testConvertUpdatedPriceResponse_Success() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequest();
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponse();
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(false);
        
        Map<String, TotalPricing> mockPriceMap = new HashMap<>();
        mockPriceMap.put("DEFAULT", new TotalPricing());
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(mockPriceMap);
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result);
        Assert.notNull(result.getPriceMap());
        Assert.isTrue(result.getPriceMap().containsKey("DEFAULT"));
        Assert.notNull(result.getDefaultPriceKey());
        Assert.notNull(result.getTotalPax());
        if (result.getHermesData() != null) {
            Assert.isTrue(!result.getHermesData().isEmpty());
        }
        
        // Verify method calls
        verify(dateUtil).getDaysDiff(any(LocalDate.class), any(LocalDate.class));
        verify(commonResponseTransformer).buildCorpApprovalInfo(any());
        verify(commonResponseTransformer).getPriceMap(any(), any(), any(), eq(1), eq(TEST_CURRENCY), 
                eq(TEST_SELLABLE_TYPE), eq(2), eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), isNull());
    }

    @Test
    public void testConvertUpdatedPriceResponse_NullPriceBreakDownResponse() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequest();
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(false);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                null, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.isNull(result);
    }

    @Test
    public void testConvertUpdatedPriceResponse_MyPartnerRequest() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequest();
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponse();
        // Create a commonModifierResponse that will actually result in myPartner=true
        // Note: Since we can't mock static methods, we test with the actual logic
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(true);
        
        Map<String, TotalPricing> mockPriceMap = new HashMap<>();
        mockPriceMap.put("DEFAULT", new TotalPricing());
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(mockPriceMap);
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result);
        // Note: The actual myPartner value depends on the real Utility.isMyPartnerRequest implementation
        // We verify the method was called with some myPartner value (true or false)
        verify(commonResponseTransformer).getPriceMap(any(), any(), any(), eq(1), eq(TEST_CURRENCY), 
                eq(TEST_SELLABLE_TYPE), eq(2), eq(false), eq(""), eq(true), eq(false), eq(false), anyBoolean(), isNull());
    }

    @Test
    public void testConvertUpdatedPriceResponse_EmptyPriceMap() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequest();
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponse();
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(false);
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(new HashMap<>());
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result);
        Assert.isNull(result.getDefaultPriceKey());
    }

    @Test
    public void testConvertUpdatedPriceResponse_MultipleRoomsAndChildren() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequestWithMultipleRooms();
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponse();
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(false);
        
        Map<String, TotalPricing> mockPriceMap = new HashMap<>();
        mockPriceMap.put("DEFAULT", new TotalPricing());
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(mockPriceMap);
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result);
        Assert.notNull(result.getTotalPax());
        Assert.isTrue(result.getTotalPax().contains("2")); // 2 rooms
        // HermesData should be created if room criteria exists, but be defensive
        if (result.getHermesData() != null) {
            Assert.isTrue(!result.getHermesData().isEmpty());
        }
        
        // Verify room count is calculated correctly (2 room stay candidates)
        verify(commonResponseTransformer).getPriceMap(any(), any(), any(), eq(2), eq(TEST_CURRENCY), 
                eq(TEST_SELLABLE_TYPE), eq(2), eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), isNull());
    }

    @Test
    public void testConvertUpdatedPriceResponse_NullCommonModifierResponse() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequest();
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponse();
        
        Map<String, TotalPricing> mockPriceMap = new HashMap<>();
        mockPriceMap.put("DEFAULT", new TotalPricing());
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(mockPriceMap);
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, null);

        // Assert
        Assert.notNull(result);
        verify(commonResponseTransformer).getPriceMap(any(), any(), any(), eq(1), eq(TEST_CURRENCY), 
                eq(TEST_SELLABLE_TYPE), eq(2), eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), isNull());
    }

    @Test
    public void testConvertUpdatedPriceResponse_EmptyRoomCriteria() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequestWithEmptyRooms();
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponse();
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(false);
        
        Map<String, TotalPricing> mockPriceMap = new HashMap<>();
        mockPriceMap.put("DEFAULT", new TotalPricing());
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                isNull(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(mockPriceMap);
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result);
        Assert.isNull(result.getTotalPax());
        
        // Verify sellableType is null and room count is 1 (default value when room criteria is empty)
        verify(commonResponseTransformer).getPriceMap(any(), any(), any(), eq(1), eq(TEST_CURRENCY), 
                isNull(), eq(2), eq(false), eq(""), eq(true), eq(false), eq(false), eq(false), isNull());
    }

    @Test
    public void testConvertUpdatedPriceResponse_GroupBookingFunnel() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequest();
        updatePriceRequest.getRequestDetails().setFunnelSource("GROUP");
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponse();
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(false);
        
        Map<String, TotalPricing> mockPriceMap = new HashMap<>();
        mockPriceMap.put("DEFAULT", new TotalPricing());
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(mockPriceMap);
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result);
        // The actual groupBooking value depends on Utility.isGroupBookingFunnel("GROUP")
        // We verify that getPriceMap was called but allow for either true or false for groupBooking parameter
        verify(commonResponseTransformer).getPriceMap(any(), any(), any(), eq(1), eq(TEST_CURRENCY), 
                eq(TEST_SELLABLE_TYPE), eq(2), eq(false), eq(""), eq(true), anyBoolean(), eq(false), eq(false), isNull());
    }

    @Test
    public void testConvertUpdatedPriceResponse_WithCouponCode() throws Exception {
        // Arrange
        UpdatePriceRequest updatePriceRequest = createTestUpdatePriceRequest();
        PriceBreakDownResponse priceBreakDownResponse = createTestPriceBreakDownResponseWithCoupon();
        CommonModifierResponse commonModifierResponse = createTestCommonModifierResponse(false);
        
        Map<String, TotalPricing> mockPriceMap = new HashMap<>();
        mockPriceMap.put("SAVE20", new TotalPricing());
        
        when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        when(commonResponseTransformer.buildCorpApprovalInfo(any())).thenReturn(null);
        when(commonResponseTransformer.getPriceMap(any(), any(), any(), anyInt(), anyString(), 
                anyString(), anyInt(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), isNull()))
                .thenReturn(mockPriceMap);
        when(utility.buildToolTip(anyString())).thenReturn(true);

        // Act
        UpdatePriceResponse result = updatedPriceResponseTransformer.convertUpdatedPriceResponse(
                priceBreakDownResponse, updatePriceRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result);
    }

    // Helper methods to create test data
    private UpdatePriceRequest createTestUpdatePriceRequest() {
        UpdatePriceRequest request = new UpdatePriceRequest();
        request.setSearchCriteria(createTestSearchCriteria());
        request.setRequestDetails(createTestRequestDetails());
        request.setExpDataMap(new HashMap<>());
        return request;
    }

    private UpdatedPriceCriteria createTestSearchCriteria() {
        UpdatedPriceCriteria searchCriteria = new UpdatedPriceCriteria();
        searchCriteria.setCurrency(TEST_CURRENCY);
        searchCriteria.setCheckIn(TEST_CHECK_IN);
        searchCriteria.setCheckOut(TEST_CHECK_OUT);
        
        List<UpdatedPriceRoomCriteria> roomCriteria = new ArrayList<>();
        UpdatedPriceRoomCriteria roomCriterion = new UpdatedPriceRoomCriteria();
        roomCriterion.setRatePlanCode(TEST_RATE_PLAN_CODE);
        roomCriterion.setRoomCode(TEST_ROOM_CODE);
        roomCriterion.setSellableType(TEST_SELLABLE_TYPE);
        
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setChildAges(Arrays.asList(5, 8));
        roomStayCandidates.add(roomStayCandidate);
        
        roomCriterion.setRoomStayCandidates(roomStayCandidates);
        roomCriteria.add(roomCriterion);
        
        searchCriteria.setRoomCriteria(roomCriteria);
        return searchCriteria;
    }

    private UpdatePriceRequest createTestUpdatePriceRequestWithMultipleRooms() {
        UpdatePriceRequest request = createTestUpdatePriceRequest();
        
        // Add a second room stay candidate to the existing room criterion
        RoomStayCandidate secondRoom = new RoomStayCandidate();
        secondRoom.setAdultCount(1);
        secondRoom.setChildAges(Arrays.asList(3));
        
        request.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().add(secondRoom);
        return request;
    }

    private UpdatePriceRequest createTestUpdatePriceRequestWithEmptyRooms() {
        UpdatePriceRequest request = new UpdatePriceRequest();
        request.setSearchCriteria(createTestSearchCriteriaWithEmptyRooms());
        request.setRequestDetails(createTestRequestDetails());
        request.setExpDataMap(new HashMap<>());
        return request;
    }

    private UpdatedPriceCriteria createTestSearchCriteriaWithEmptyRooms() {
        UpdatedPriceCriteria searchCriteria = new UpdatedPriceCriteria();
        searchCriteria.setCurrency(TEST_CURRENCY);
        searchCriteria.setCheckIn(TEST_CHECK_IN);
        searchCriteria.setCheckOut(TEST_CHECK_OUT);
        searchCriteria.setRoomCriteria(new ArrayList<>());
        return searchCriteria;
    }

    private RequestDetails createTestRequestDetails() {
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource(TEST_FUNNEL_SOURCE);
        return requestDetails;
    }

    private PriceBreakDownResponse createTestPriceBreakDownResponse() {
        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        return new PriceBreakDownResponse.Builder()
                .buildDisplayPriceBreakDown(displayPriceBreakDown)
                .build();
    }

    private PriceBreakDownResponse createTestPriceBreakDownResponseWithCoupon() {
        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        BestCoupon couponInfo = new BestCoupon();
        couponInfo.setCouponCode("SAVE20");
        displayPriceBreakDown.setCouponInfo(couponInfo);
        return new PriceBreakDownResponse.Builder()
                .buildDisplayPriceBreakDown(displayPriceBreakDown)
                .build();
    }

    private CommonModifierResponse createTestCommonModifierResponse(boolean isMyPartner) {
        CommonModifierResponse response = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        if (isMyPartner) {
            extendedUser.setProfileType("CORP");
            extendedUser.setAffiliateId("MY_PARTNER");
        } else {
            extendedUser.setProfileType("INDIVIDUAL");
            extendedUser.setAffiliateId("REGULAR");
        }
        response.setExtendedUser(extendedUser);
        return response;
    }
} 