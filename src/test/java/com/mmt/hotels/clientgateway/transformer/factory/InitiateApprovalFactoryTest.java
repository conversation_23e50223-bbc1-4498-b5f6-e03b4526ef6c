package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.InitiateApprovalRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.UpdatePolicyRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.InitiateApprovalRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.android.UpdatePolicyRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.InitiateApprovalRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.desktop.UpdatePolicyRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.InitiateApprovalRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.ios.UpdatePolicyRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.InitiateApprovalRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.InitiateApprovalResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatePolicyResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.InitiateApprovalResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.android.UpdatePolicyResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.InitiateApprovalResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.desktop.UpdatePolicyResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.InitiateApprovalResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.ios.UpdatePolicyResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.InitiateApprovalResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class InitiateApprovalFactoryTest {


    @InjectMocks
    InitiateApprovalFactory initiateApprovalFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalResponseTransformerPWA" , new InitiateApprovalResponseTransformerPWA());
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalResponseTransformerAndroid" , new InitiateApprovalResponseTransformerAndroid());
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalResponseTransformerIOS" , new InitiateApprovalResponseTransformerIOS());
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalResponseTransformerDesktop" , new InitiateApprovalResponseTransformerDesktop());
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalRequestTransformerPWA" , new InitiateApprovalRequestTransformerPWA());
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalRequestTransformerAndroid" , new InitiateApprovalRequestTransformerAndroid());
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalRequestTransformerIOS" , new InitiateApprovalRequestTransformerIOS());
        ReflectionTestUtils.setField(initiateApprovalFactory,"initiateApprovalRequestTransformerDesktop" , new InitiateApprovalRequestTransformerDesktop());

    }

    @Test
    public void getRequestServiceTest(){
        InitiateApprovalRequestTransformer resp = initiateApprovalFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof InitiateApprovalRequestTransformerPWA  );
        resp = initiateApprovalFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof InitiateApprovalRequestTransformerDesktop  );
        resp = initiateApprovalFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof InitiateApprovalRequestTransformerAndroid  );
        resp = initiateApprovalFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof InitiateApprovalRequestTransformerIOS  );
        resp = initiateApprovalFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(initiateApprovalFactory.getRequestService("test") instanceof InitiateApprovalRequestTransformerDesktop);
    }

    @Test
    public void getResponseServiceTest(){
        InitiateApprovalResponseTransformer resp = initiateApprovalFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof InitiateApprovalResponseTransformerPWA  );
        resp = initiateApprovalFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof InitiateApprovalResponseTransformerDesktop  );
        resp = initiateApprovalFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof InitiateApprovalResponseTransformerAndroid  );
        resp = initiateApprovalFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof InitiateApprovalResponseTransformerIOS  );
        resp = initiateApprovalFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(initiateApprovalFactory.getResponseService("test") instanceof  InitiateApprovalResponseTransformerDesktop);
    }


}
