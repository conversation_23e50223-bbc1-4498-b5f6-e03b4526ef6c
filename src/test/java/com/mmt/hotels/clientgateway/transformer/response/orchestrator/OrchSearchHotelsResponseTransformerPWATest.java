package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.orchestrator.enums.Funnel;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerPWATest {
     @InjectMocks
    OrchSearchHotelsResponseTransformerPWA orchSearchHotelsResponseTransformerPWA;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    ObjectMapper mapper;

    @Spy
    PersuasionUtil persuasionUtil;

    @Before
    public void setUp() throws JsonParseException {
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
    }

    @Test
    public void buildBottomSheet() {
        orchSearchHotelsResponseTransformerPWA.buildBottomSheet(null);

    }

    @Test
    public void addPersuasionHoverData() {
        Hotel hotel = new Hotel();
        hotel.setId("testHotel");
        orchSearchHotelsResponseTransformerPWA.addPersuasionHoverData(hotel, null);
    }

    @Test
    public void addSeoTextPersuasion() {
        orchSearchHotelsResponseTransformerPWA.addSeoTextPersuasion(null, null, false, null, null);
    }

    @Test
    public void addLocationPersuasionToHotelPersuasions() throws JsonProcessingException {
        Hotel hotelEntity = new Hotel();
        List<String> locationPersuasions = Collections.singletonList("Chanakyapuri");
        LinkedHashSet<String> facilities = new LinkedHashSet<>(Arrays.asList("Spa", "Restaurant", "Bar"));
        TransportPoi nearestGroundTransportPoi = new TransportPoi();
        LocationDetails locusData = new LocationDetails();
        locusData.setCityName("Delhi");
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setPageContext("LISTING");
        String dayUsePersuasionsText = "Day Use";
        String drivingTimeText = "10.0 km drive to T1 - Delhi Airport (IGI Airport)";
        orchSearchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        searchHotelsRequest.getRequestDetails().setFunnelSource(Funnel.DAYUSE.name());

        orchSearchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);

    }

    private SearchHotelsRequest createSearchHotelsRequest() throws JsonProcessingException {
        String requestString = "{\"correlationKey\":null,\"brand\":null,\"client\":null,\"blackInfo\":null,\"deviceDetails\":{\"appVersion\":\"128.0.0.0\",\"deviceId\":\"d7bb97cf-762b-4484-91fc-a224c03cdc96\",\"deviceType\":\"DESKTOP\",\"bookingDevice\":\"DESKTOP\",\"networkType\":\"WiFi\",\"deviceName\":null,\"appVersionIntGi\":null,\"simSerialNo\":null},\"lastProductId\":null,\"limit\":null,\"requestDetails\":{\"visitorId\":\"d23c479b373ee283\",\"visitNumber\":1,\"trafficSource\":null,\"srCon\":null,\"srCty\":null,\"srcState\":null,\"srLat\":null,\"srLng\":null,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"notifCoupon\":null,\"callBackType\":null,\"pushDataToCallToBookQ\":null,\"pushDataToListAllPropQ\":null,\"payMode\":null,\"loggedIn\":true,\"couponCount\":10,\"siteDomain\":\"in\",\"channel\":\"B2Cweb\",\"pageContext\":\"LISTING\",\"firstTimeUserState\":0,\"uuid\":null,\"corpAuthCode\":null,\"corpUserId\":null,\"seoCorp\":false,\"requestor\":null,\"wishCode\":null,\"preApprovedValidity\":null,\"metaInfo\":false,\"zcp\":null,\"requisitionID\":null,\"myBizFlowIdentifier\":null,\"brand\":\"MMT\",\"previousTxnKey\":null,\"oldWorkflowId\":null,\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"sessionId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"promoConsent\":false,\"flyerInfo\":null,\"premium\":false,\"semanticSearchDetails\":null,\"forwardBookingFlow\":false,\"extendedPackageCall\":false,\"isIgnoreSEO\":false,\"isRequestCallBack\":false,\"isListAllPropCall\":false},\"detailDeepLinkUrl\":null,\"sortCriteria\":null,\"filterCriteria\":[],\"appliedBatchKeys\":[],\"filterGroupsToRemove\":null,\"filtersToRemove\":null,\"featureFlags\":{\"staticData\":true,\"reviewSummaryRequired\":true,\"walletRequired\":true,\"shortlistingRequired\":false,\"noOfCoupons\":0,\"noOfAddons\":0,\"noOfPersuasions\":0,\"noOfSoldouts\":0,\"coupon\":true,\"mmtPrime\":false,\"persuationSeg\":null,\"persuasionsEngineHit\":true,\"checkAvailability\":true,\"liteResponse\":false,\"applyAbsorption\":false,\"bestOffersLimit\":0,\"dealOfTheDayRequired\":false,\"addOnRequired\":false,\"roomInfoRequired\":false,\"allInclusions\":false,\"hotelCatAndPropNotRequiredInMeta\":false,\"extraAltAccoRequired\":false,\"limitedFilterCall\":false,\"corpMMRRequired\":false,\"unmodifiedAmenities\":false,\"poisRequiredOnMap\":true,\"persuasionsRequired\":true,\"similarHotel\":false,\"locus\":false,\"comparator\":false,\"originListingMap\":false,\"mostBooked\":false,\"detailMap\":false,\"showUpsell\":false,\"filterRanking\":false,\"quickReview\":false,\"dayUsePersuasion\":false,\"selectiveHotels\":false,\"persuasionSuppression\":false,\"hidePrice\":false,\"showBnplCard\":false,\"modifyBooking\":false,\"cardRequired\":false,\"topCard\":false,\"filters\":false,\"seoDS\":false,\"seoCohort\":null,\"roomPreferenceEnabled\":false,\"flashDealClaimed\":false,\"upsellRateplanRequired\":false,\"orientation\":null,\"elitePackageEnabled\":false,\"premiumThemesCardRequired\":false,\"isGoTribe3_0\":null},\"matchMakerDetails\":{},\"imageDetails\":{\"types\":[\"professional\"],\"categories\":[{\"type\":\"H\",\"count\":1,\"height\":162,\"width\":243,\"imageFormat\":\"webp\"}]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]},\"expData\":\"{\\\"EMIDT\\\":2,\\\"UGCV2\\\":\\\"T\\\",\\\"HFC\\\":\\\"F\\\",\\\"VIDEO\\\":0,\\\"APT\\\":\\\"T\\\",\\\"CHPC\\\":\\\"T\\\",\\\"LSTNRBY\\\":\\\"T\\\",\\\"AARI\\\":\\\"T\\\",\\\"RCPN\\\":\\\"T\\\",\\\"MRS\\\":\\\"T\\\",\\\"ADDON\\\":\\\"T\\\",\\\"NLP\\\":\\\"Y\\\",\\\"PERNEW\\\":\\\"T\\\",\\\"GRPN\\\":\\\"T\\\",\\\"BNPL\\\":\\\"T\\\",\\\"MCUR\\\":\\\"T\\\",\\\"HAFC\\\":\\\"T\\\",\\\"PLRS\\\":\\\"T\\\",\\\"MMRVER\\\":\\\"V3\\\",\\\"PDO\\\":\\\"PN\\\",\\\"BLACK\\\":\\\"T\\\",\\\"CV2\\\":\\\"T\\\",\\\"FLTRPRCBKT\\\":\\\"T\\\",\\\"RTBC\\\":\\\"T\\\",\\\"MLOS\\\":\\\"T\\\",\\\"WPAH\\\":\\\"F\\\",\\\"AIP\\\":\\\"T\\\",\\\"BNPL0\\\":\\\"T\\\",\\\"HIS\\\":\\\"DEFAULT\\\",\\\"APE\\\":10,\\\"PAH\\\":5,\\\"IAO\\\":\\\"F\\\",\\\"CRF\\\":\\\"B\\\",\\\"ALC\\\":\\\"T\\\",\\\"SOU\\\":\\\"T\\\",\\\"PAH5\\\":\\\"T\\\",\\\"rearch\\\":\\\"True\\\"}\",\"expVariantKeys\":null,\"cohertVar\":null,\"multiCityFilter\":null,\"additionalProperties\":null,\"cardId\":null,\"manthanExpDataMap\":null,\"expDataMap\":null,\"contentExpDataMap\":null,\"userLocation\":null,\"clusterId\":null,\"orgId\":null,\"validExpList\":null,\"variantKeys\":null,\"businessIdentificationEnableFromUserService\":false,\"selectedTabId\":null,\"searchCriteria\":{\"checkIn\":\"2024-10-26\",\"checkOut\":\"2024-10-27\",\"countryCode\":\"IN\",\"cityCode\":\"ZNSHIM\",\"cityName\":null,\"locationId\":\"CTDEL\",\"locationType\":\"znshim\",\"lat\":null,\"lng\":null,\"currency\":\"INR\",\"personalCorpBooking\":false,\"rmDHS\":false,\"boostProperty\":null,\"baseRateplanCode\":null,\"selectedRatePlan\":null,\"multiCurrencyInfo\":null,\"preAppliedFilter\":false,\"roomStayCandidates\":[{\"rooms\":1,\"adultCount\":2,\"childAges\":[]}],\"parentLocationId\":null,\"parentLocationType\":null,\"tripType\":null,\"slot\":null,\"giHotelId\":null,\"hotelIds\":null,\"limit\":1,\"lastHotelId\":null,\"lastFetchedWindowInfo\":null,\"lastHotelCategory\":null,\"personalizedSearch\":true,\"nearBySearch\":false,\"wishListedSearch\":false,\"totalHotelsShown\":null,\"sectionsType\":null,\"collectionCriteria\":null,\"bookingForGuest\":false,\"travellerEmailID\":null,\"vcId\":null},\"lastPeekedOnMapHotelIds\":null,\"mapDetails\":null,\"nearbyFilter\":null,\"filterRemovedCriteria\":null}";
        return mapper.readValue(requestString, SearchHotelsRequest.class);
    }

    @Test
    public void buildBGColor() {
        orchSearchHotelsResponseTransformerPWA.buildBGColor(null, null, null);
    }

    @Test
    public void addBookingConfirmationPersuasion() {
        orchSearchHotelsResponseTransformerPWA.addBookingConfirmationPersuasion(null);
    }

    @Test
    public void buildStaticCard() {
        orchSearchHotelsResponseTransformerPWA.buildStaticCard(null, null);
    }
}