package com.mmt.hotels.clientgateway.transformer.factory;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.transformer.request.DiscountRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.DiscountResponseTransformer;

@RunWith(MockitoJUnitRunner.class)
public class DiscountServiceFactoryTest {

	@InjectMocks
	private DiscountServiceFactory discountServiceFactory;
	@Mock
	private DiscountRequestTransformer discountRequestTransformer;
	@Mock
	private DiscountResponseTransformer discountResponseTransformer;

	@Test
	public void testGetRequestService(){
		Assert.assertNotNull(discountServiceFactory.getRequestService("PWA"));
	}
	
	@Test
	public void testGetResponseService(){
		Assert.assertNotNull(discountServiceFactory.getResponseService("PWA"));
	}
}
