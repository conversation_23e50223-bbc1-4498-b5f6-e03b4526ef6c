package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchHotelsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsResponseTransformerIOSTest {
	
	@InjectMocks
	SearchHotelsResponseTransformerIOS searchHotelsResponseTransformerIOS;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	PolyglotService polyglotService;

	Hotel hotel;

	@Mock
	PersuasionUtil persuasionUtil;

	@Before
	public void init() throws IOException {
			ObjectMapper mapper = new ObjectMapper();
			mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
			hotel = mapper.readValue(availPriceRequest, Hotel.class);
			MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
			myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
			myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
			myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
			ReflectionTestUtils.setField(searchHotelsResponseTransformerIOS,"myBizStaticCard",myBizStaticCard);
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest(){
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
		searchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().size() > 0);
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().get(0).getText().contains(secondaryPersuasion));
	}

	@Test
	public void testBuildQuickBookCard() {
		Assert.assertNotNull(searchHotelsResponseTransformerIOS.buildQuickBookCard(new QuickBookInfo()));
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		Assert.assertNotNull(searchHotelsResponseTransformerIOS.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerIOS.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}

	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		hotelEntity.setDetailDeeplinkUrl("test");
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerIOS.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		Assert.assertNotNull(staticCard);
		Assert.assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}

	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerIOS.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		junit.framework.Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		junit.framework.Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps()));
	}
}