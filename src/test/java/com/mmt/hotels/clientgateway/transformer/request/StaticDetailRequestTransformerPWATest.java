package com.mmt.hotels.clientgateway.transformer.request;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.request.pwa.StaticDetailRequestTransformerPWA;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;


@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class StaticDetailRequestTransformerPWATest {
    @InjectMocks
    StaticDetailRequestTransformerPWA staticDetailRequestTransformerPWA;

    ObjectMapperUtil objectMapperUtil = new ObjectMapperUtil();

    @Mock
    Utility utility;

    @Before
    public void setup() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
    }

    @Test
    public void testConvertStaticDetailRequest(){

        StaticDetailRequest staticDetailRequest = new StaticDetailRequest();
        staticDetailRequest.setDeviceDetails(new DeviceDetails());

        staticDetailRequest.setSearchCriteria(new StaticDetailCriteria());
        staticDetailRequest.getSearchCriteria().setLat(10d);
        staticDetailRequest.getSearchCriteria().setLng(10d);
        staticDetailRequest.getSearchCriteria().setHotelId("test");
        staticDetailRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        staticDetailRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        staticDetailRequest.getSearchCriteria().getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        staticDetailRequest.getSearchCriteria().setTripType("Family");

        staticDetailRequest.setFeatureFlags(new FeatureFlags());
        staticDetailRequest.setRequestDetails(new RequestDetails());
        staticDetailRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        staticDetailRequest.getRequestDetails().setLoggedIn(true);

        staticDetailRequest.setFilterCriteria(new ArrayList<Filter>());
        staticDetailRequest.getFilterCriteria().add(new Filter());
        staticDetailRequest.getFilterCriteria().get(0).setFilterGroup(FilterGroup.FREE_BREAKFAST);
        staticDetailRequest.getFilterCriteria().get(0).setFilterRange(new FilterRange());
        staticDetailRequest.getFilterCriteria().get(0).getFilterRange().setMaxValue(10);
        staticDetailRequest.getFilterCriteria().get(0).getFilterRange().setMinValue(10);

        staticDetailRequest.setImageDetails(new ImageDetails());
        staticDetailRequest.getImageDetails().setTypes(new ArrayList<>());
        staticDetailRequest.getImageDetails().setCategories(new ArrayList<ImageCategory>());
        ImageCategory imageCat = new ImageCategory();
        imageCat.setHeight(200);
        imageCat.setWidth(200);
        imageCat.setCount(1);

        staticDetailRequest.getImageDetails().getCategories().add(imageCat);
        
        staticDetailRequest.setFeatureFlags(new FeatureFlags());
        staticDetailRequest.getFeatureFlags().setReviewSummaryRequired(true);
        staticDetailRequest.getFeatureFlags().setShortlistingRequired(true);
        staticDetailRequest.getFeatureFlags().setStaticData(true);

        staticDetailRequest.setReviewDetails(new ReviewDetails());
        staticDetailRequest.getReviewDetails().setOtas(new ArrayList<>());
        staticDetailRequest.getReviewDetails().setTagTypes(new ArrayList<>());

        staticDetailRequest.getSearchCriteria().setSlot(new Slot());
        staticDetailRequest.getSearchCriteria().getSlot().setTimeSlot(10);

        RequiredApis requiredApis = new RequiredApis();
		staticDetailRequest.setRequiredApis(requiredApis );
		
        
        CommonModifierResponse commonModifierResponse =   new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("uuid");
        extendedUser.setProfileType("PERSONAL");
		commonModifierResponse.setExtendedUser(extendedUser );
		
		HydraResponse hydraResponse = new HydraResponse();
		Set<String> hydraMatchedSegment = new HashSet<>();
		hydraMatchedSegment.add("seg");
		hydraResponse.setHydraMatchedSegment(hydraMatchedSegment );
		commonModifierResponse.setHydraResponse(hydraResponse );
        
        HotelDetailsMobRequestBody hotelDetailsMobRequestBody =  staticDetailRequestTransformerPWA.convertStaticDetailRequest(staticDetailRequest, new CommonModifierResponse());

        Assert.assertNotNull(hotelDetailsMobRequestBody);

    }

    @Test
    public void testConvertStaticDetailsRequest(){
    	SearchRoomsRequest searchRequest = new SearchRoomsRequest();
    	SearchRoomsCriteria searchCriteria = new SearchRoomsCriteria();
		searchRequest.setSearchCriteria(searchCriteria );
    	
		RequestDetails requestDetails = new RequestDetails();
		searchRequest.setRequestDetails(requestDetails );
		
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("uuid");
        extendedUser.setProfileType("PERSONAL");
		commonModifierResponse.setExtendedUser(extendedUser );
		
		HydraResponse hydraResponse = new HydraResponse();
		Set<String> hydraMatchedSegment = new HashSet<>();
		hydraMatchedSegment.add("seg");
		hydraResponse.setHydraMatchedSegment(hydraMatchedSegment );
		commonModifierResponse.setHydraResponse(hydraResponse );
		
		HotelDetailsMobRequestBody hotelDetailsMobRequestBody = new HotelDetailsMobRequestBody();
		HotelDetailsMobRequestBody req = staticDetailRequestTransformerPWA.convertStaticDetailRequest(searchRequest ,
    			commonModifierResponse , hotelDetailsMobRequestBody );
    	 Assert.assertNotNull(req);
    }

    @Test
    public void testConvertCityGuideRequest(){
        com.mmt.hotels.clientgateway.request.CityGuideRequest inputRequest  = new com.mmt.hotels.clientgateway.request.CityGuideRequest();
        inputRequest.setSearchCriteria(new CityGuideSearchCriteria());
        inputRequest.getSearchCriteria().setCheckOut("2021-08-09");
        inputRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        inputRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        inputRequest.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(3);
        CityGuideRequest request = staticDetailRequestTransformerPWA.convertCityGuideRequest(inputRequest, "test");
        Assert.assertNotNull(request);
        Assert.assertEquals("test", request.getCorrelationKey());
        Assert.assertEquals(inputRequest.getSearchCriteria().getCheckIn(), request.getCheckin());
    }

    @Test
    public void testGetHotStoreHotelsRequest() throws Exception {
        WishListedHotelsDetailRequest wishListedHotelsDetailRequest = objectMapperUtil.getObjectFromJson("{\"deviceDetails\":{\"appVersion\":\"8.6.4.RC1\",\"bookingDevice\":\"ANDROID\",\"deviceId\":\"311c8e73900a8d98\",\"deviceType\":\"Mobile\",\"networkType\":\"WiFi\",\"resolution\":\"xhdpi\"},\"requestDetails\":{\"channel\":\"Native\",\"couponCount\":3,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"loggedIn\":true,\"pageContext\":\"DETAIL\",\"siteDomain\":\"in\",\"visitNumber\":1,\"visitorId\":\"74856825370536007467679936889328709665\"},\"searchCriteria\":{\"hotelIds\":[\"201412311314381986\"],\"visitNumber\":1,\"visitorId\":\"74856825370536007467679936889328709665\",\"cityCode\":\"CTDEL\",\"locationId\":\"CTDEL\",\"locationType\":\"city\",\"countryCode\":\"IN\",\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[],\"positionsForAgeLessThan1\":[]}]},\"imageDetails\":{\"categories\":[{\"count\":4,\"height\":252,\"imageFormat\":\"webp\",\"type\":\"H\",\"width\":459}],\"types\":[\"professional\",\"traveller\"]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\",\"MANUAL\",\"OTHER\",\"EXT\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]}}", WishListedHotelsDetailRequest.class, DependencyLayer.ORCHESTRATOR);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("uuid");
        extendedUser.setProfileType("PERSONAL");
        commonModifierResponse.setExtendedUser(extendedUser);
        HotStoreHotelsRequestBody hotStoreHotelsRequest = staticDetailRequestTransformerPWA.getHotStoreHotelsRequest(wishListedHotelsDetailRequest, commonModifierResponse);
        Assert.assertNotNull(hotStoreHotelsRequest);
    }

    @Test
    public void testGetFlyfishReviewRequest() throws Exception {
        WishListedHotelsDetailRequest wishListedHotelsDetailRequest = objectMapperUtil.getObjectFromJson("{\"deviceDetails\":{\"appVersion\":\"8.6.4.RC1\",\"bookingDevice\":\"ANDROID\",\"deviceId\":\"311c8e73900a8d98\",\"deviceType\":\"Mobile\",\"networkType\":\"WiFi\",\"resolution\":\"xhdpi\"},\"requestDetails\":{\"channel\":\"Native\",\"couponCount\":3,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"loggedIn\":true,\"pageContext\":\"DETAIL\",\"siteDomain\":\"in\",\"visitNumber\":1,\"visitorId\":\"74856825370536007467679936889328709665\"},\"searchCriteria\":{\"hotelIds\":[\"201412311314381986\"],\"visitNumber\":1,\"visitorId\":\"74856825370536007467679936889328709665\",\"cityCode\":\"CTDEL\",\"locationId\":\"CTDEL\",\"locationType\":\"city\",\"countryCode\":\"IN\",\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[],\"positionsForAgeLessThan1\":[]}]},\"imageDetails\":{\"categories\":[{\"count\":4,\"height\":252,\"imageFormat\":\"webp\",\"type\":\"H\",\"width\":459}],\"types\":[\"professional\",\"traveller\"]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\",\"MANUAL\",\"OTHER\",\"EXT\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]}}", WishListedHotelsDetailRequest.class, DependencyLayer.ORCHESTRATOR);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("uuid");
        extendedUser.setProfileType("PERSONAL");
        commonModifierResponse.setExtendedUser(extendedUser);
        FlyfishReviewRequestBody flyfishReviewRequest = staticDetailRequestTransformerPWA.getFlyfishReviewRequest(wishListedHotelsDetailRequest, commonModifierResponse);
        Assert.assertNotNull(flyfishReviewRequest);
    }

}
