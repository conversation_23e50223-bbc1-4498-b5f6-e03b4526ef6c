package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.enums.BNPLDisabledReason;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.MobLandingRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.payment.InsuranceAddOnData;
import com.mmt.hotels.clientgateway.response.CancellationTimeline;
import com.mmt.hotels.clientgateway.response.FCBenefit;
import com.mmt.hotels.clientgateway.response.Policy;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.EMIAbridgeResponse;
import com.mmt.hotels.clientgateway.response.availrooms.HotelTag;
import com.mmt.hotels.clientgateway.response.availrooms.MyBizQuickPayConfigBO;
import com.mmt.hotels.clientgateway.response.availrooms.Rules;
import com.mmt.hotels.clientgateway.response.corporate.CorpAutobookRequestorConfigBO;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.clientgateway.response.listingmap.Poi;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData;
import com.mmt.hotels.clientgateway.response.moblanding.SpokeCityCG;
import com.mmt.hotels.clientgateway.response.moblanding.ValueStaysDataCG;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.TagInfo;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.thankyou.AmountDetail;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.filter.FilterRange;
import com.mmt.hotels.model.clm.ClmPersuasion;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.addon.InsuranceData;
import com.mmt.hotels.model.response.addon.TmInsuranceAddOns;
import com.mmt.hotels.model.response.addon.WidgetData;
import com.mmt.hotels.model.response.adtech.*;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.corporate.*;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.ConceptSummaryDTO;
import com.mmt.hotels.model.response.flyfish.SubConceptDTO;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.listpersonalization.*;
import com.mmt.hotels.model.response.mmtprime.BlackInfo;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.jsonviews.LongStayBenefits;
import com.mmt.hotels.model.response.prime.DoubleBlackValidateResponse;
import com.mmt.hotels.model.response.searchwrapper.CollectionsResponseBo;
import com.mmt.hotels.model.response.searchwrapper.GeoLocation;
import com.mmt.hotels.model.response.searchwrapper.RecommendedSearchContext;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged;
import com.mmt.hotels.model.response.staticdata.BbLatLong;
import com.mmt.hotels.model.response.staticdata.HotelResult;
import com.mmt.hotels.model.response.staticdata.HouseRules;
import com.mmt.hotels.model.response.staticdata.LatLong;
import com.mmt.hotels.model.response.staticdata.poiinfo.Category;
import com.mmt.hotels.model.response.staticdata.poiinfo.Centre;
import com.mmt.hotels.model.response.staticdata.poiinfo.Meta;
import com.mmt.hotels.model.response.staticdata.poiinfo.POIInfo;
import com.mmt.hotels.model.response.txn.BookingMessageCard;
import com.mmt.hotels.model.response.txn.BookingMessageCardData;
import com.mmt.hotels.model.response.txn.BookingMessageCardDataStyle;
import com.mmt.hotels.model.response.txn.LoyaltyMessageResponse;
import com.mmt.hotels.clientgateway.response.PersuasionData;
import com.mmt.hotels.model.response.HotelBenefitInfo;
import com.mmt.hotels.pojo.listing.personalization.CardAction;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.model.AttributesFacility;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.TAXES_KEY;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.filter.FilterGroup.LOCATION;
import static com.mmt.hotels.filter.FilterGroup.STAR_RATING;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommonResponseTransformerTest {

	@InjectMocks
	CommonResponseTransformer commonResponseTransformer;

	@Mock
	PoliciesResponseTransformer policiesResponseTransformer;

	@Spy
	ObjectMapperUtil objectMapperUtil;

	@Spy
	private Utility utility;

	@Mock
	private PolyglotService polyglotService;

	@Mock
	SearchHotelsFactory searchHotelsFactory;

	@Mock
	private FilterFactory filterFactory;

	@Before
	public void init(){
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
			@Override
			public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
				return invocationOnMock.getArgument(0);
			}
		});
	}
	@Mock
	com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse listPersonalizationResponse;

	@Mock
	List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCBList;

	@Mock
	com.mmt.hotels.pojo.listing.personalization.CardData cardDataCB;

	@Mock
	com.mmt.hotels.pojo.listing.personalization.CardCondition cardCondition;

	@Mock
	Filter filter;

	@Mock
	CardPayloadResponse cardPayload;

	@Mock
	GenericCardPayloadData genericCardPayloadData;

	@Mock
	CollectionsResponseBo<SearchWrapperHotelEntityAbridged> searchWrapperHotelEntity;

	@Mock
	com.mmt.hotels.pojo.listing.personalization.CardAction cardAction;

	@Mock
	com.mmt.hotels.model.response.listpersonalization.ContextualFilterData contextualFilterData;

	@Mock
	RoomTypeDetails roomTypeDetails;

	@Mock
	PropertyPersuasions propertyPersuasions;

	@Mock
	RecommendedSearchContext searchContext;

	@Mock
	com.mmt.hotels.filter.FilterRange filterRange;

	@Mock
	private CommonModifierResponse commonModifierResponse;

	@Mock
	private FilterConfiguration filterConfiguration;

	@Before
	public void setUp() throws Exception {
		MockitoAnnotations.initMocks(this);
		ReflectionTestUtils.setField(commonResponseTransformer, "corpSegments", new HashSet<>(Arrays.asList("1135", "1152")));
		ReflectionTestUtils.setField(utility, "polyglotService", polyglotService);
		LinkedHashMap<String, Double> discountParameters = new LinkedHashMap<>();
		discountParameters.put("discountPercentThreshold", 0.05);
		discountParameters.put("discountThreshold", 100.00);
		ReflectionTestUtils.setField(commonResponseTransformer, "discountParameters", discountParameters);
		ReflectionTestUtils.setField(utility, "mmtRatingsCountThreshold", 50);
		filterConfiguration = mock(FilterConfiguration.class);
	}

	@Test
	public void testGetCouponDetails() {
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		BestCoupon couponInfo = new BestCoupon();
		displayPriceBrkDwn.setCouponInfo(couponInfo);
		List<Coupon> cpnList = commonResponseTransformer.getCouponDetails(displayPriceBrkDwn);
		Assert.assertNotNull(cpnList);
	}

	@Test
	public void testGetEmiDetails(){
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		Emi emiDetails = new Emi();
		displayPriceBrkDwn.setEmiDetails(emiDetails );
		EMIDetail emiDetail = commonResponseTransformer.getEmiDetails(displayPriceBrkDwn);
		Assert.assertNotNull(emiDetail);
	}
	
	@Test
	 public void testBuildCancellationTimeline(){
		com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTL = new com.mmt.hotels.model.response.pricing.CancellationTimeline();
		CancellationTimeline resp = commonResponseTransformer.buildCancellationTimeline(cancellationTL );
		Assert.assertNotNull(resp);
	 }

	 @Test
	 public void testBuildCancellationPolicyTimeline(){
		 com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTL = new com.mmt.hotels.model.response.pricing.CancellationTimeline();
		 List<CancellationPolicyTimeline> cancellationPolicyTimelines = new ArrayList<>();
		 cancellationPolicyTimelines.add(new CancellationPolicyTimeline());
		 cancellationTL.setCancellationPolicyTimelineList(cancellationPolicyTimelines);
		 com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline resp = commonResponseTransformer.buildCancellationPolicyTimeline(cancellationTL);
		Assert.assertNotNull(resp);
	 }

	@Test
	public void testGetEmiAbridgeDetails(){
		com.mmt.model.EMIAbridgeResponse emiRsp = new com.mmt.model.EMIAbridgeResponse();
		EMIAbridgeResponse resp =  commonResponseTransformer.getEmiAbridgeDetails(emiRsp );
		Assert.assertNotNull(resp);
	}
	
	@Test
	public void testBuildBNPLDetails(){
		BNPLDetails resp = commonResponseTransformer.buildBNPLDetails(true,"", "", "", "", true, false, null, null, "", 0.0);
		Assert.assertNotNull(resp);
	}

	@Test
	public void testGetAddons(){
		List<AddOnNode> addOns = new ArrayList<>();
		AddOnNode addonNode = new AddOnNode();
		Map<String, List<String>> tnc = new HashMap<>();
		List<String> tnclst = new ArrayList<>();
		tnclst.add("tnc text");
		tnc.put("tnc", tnclst );
		addonNode.setTnc(tnc );
		List<GenericCardPayloadData> descriptions = new ArrayList<>();
		GenericCardPayloadData gnrcCardPyld = new GenericCardPayloadData();
		descriptions.add(gnrcCardPyld );
		addonNode.setDescriptions(descriptions );
		addOns.add(addonNode);

		AddOnNode insurance = new AddOnNode();
		insurance.setId("INSURANCE_ID");
		insurance.setInsuranceData(new InsuranceData());
		insurance.getInsuranceData().setTmInsuranceAddOns(new ArrayList<>());
		insurance.getInsuranceData().getTmInsuranceAddOns().add(new TmInsuranceAddOns());
		addOns.add(insurance);

		List<com.mmt.hotels.clientgateway.request.payment.AddOnNode> resp = commonResponseTransformer
				.getAddons(addOns );
		Assert.assertNotNull(resp);
	}

	@Test
	public void buildCorpApprovalInfoTest() {
		CorpMetaInfo corpMetaInfo = new CorpMetaInfo();
		corpMetaInfo.setApprovalRequired(true);
		corpMetaInfo.setValidationPayload(new ValidationResponse());
		corpMetaInfo.getValidationPayload().setBlockOopBooking(1);
		corpMetaInfo.getValidationPayload().setBlockSkipApproval(1);
		corpMetaInfo.setTags(new ArrayList<>());
		corpMetaInfo.getTags().add(new CorpTags());

		com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo resp = commonResponseTransformer.buildCorpApprovalInfo(corpMetaInfo);
		Assert.assertNotNull(resp);
	}

	@Test
	public void buildManagersTest() {
		List<ApprovingManager> managerList = new ArrayList<>();
		managerList.add(new ApprovingManager());
		managerList.get(0).setId(1);
		managerList.get(0).setName("Rishabh");
		managerList.get(0).setBusinessEmailId("<EMAIL>");

		List<com.mmt.hotels.clientgateway.response.corporate.ApprovingManager> resp = commonResponseTransformer.buildManagers(managerList);
		Assert.assertNotNull(resp);
	}
	
	@Test
	public void testBuildRatePlanList(){
		List<RoomType> roomTypes = new ArrayList<>();
		RoomType rmTyp = new RoomType();
		rmTyp.setRoomTypeCode("roomCode");
		Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanList = new HashMap<>();
		com.mmt.hotels.model.response.pricing.RatePlan ratePln = new com.mmt.hotels.model.response.pricing.RatePlan();
		ratePln.setRatePlanCode("rtPln1");
		com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = new com.mmt.hotels.model.response.pricing.CancellationTimeline();
		List<CancellationPolicyTimeline> cancellationPolicyTimelines = new ArrayList<>();
		cancellationPolicyTimelines.add(new CancellationPolicyTimeline());
		cancellationTimeline.setCancellationPolicyTimelineList(cancellationPolicyTimelines);
		ratePln.setCancellationTimeline(cancellationTimeline);
		ratePlanList.put("rtPln1", ratePln);
		rmTyp.setRatePlanList(ratePlanList);
		roomTypes.add(rmTyp);
		BNPLDetails bnplDetails = new BNPLDetails();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setCouponInfo(new BestCoupon());
		displayPriceBreakDown.getCouponInfo().setDiscountAmount(0d);
		Emi emiDetails = new Emi();
		emiDetails.setEmiAmount(234.2f);
		emiDetails.setInterestRate(1.2f);
		displayPriceBreakDown.setEmiDetails(emiDetails);
		displayPriceBreakDown.setTotalTax(9.0d);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);
		List<RatePlan> resp = commonResponseTransformer.buildRateplanList(roomTypes, bnplDetails, null, displayPriceBreakDown, null,null, null, "IN");
		Assert.assertNotNull(resp);
	}

	@Test
	public void getDoubleBlackInfoTest() {
		Assert.assertNotNull(commonResponseTransformer.getDoubleBlackInfo(new DoubleBlackValidateResponse()));
	}

	@Test
	public void getPriceMapTest() {
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setCouponInfo(new BestCoupon());
		displayPriceBreakDown.getCouponInfo().setDiscountAmount(0d);
		Emi emiDetails = new Emi();
		emiDetails.setEmiAmount(234.2f);
		emiDetails.setInterestRate(1.2f);
		displayPriceBreakDown.setEmiDetails(emiDetails);
		List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
		displayPriceBreakDownList.add(displayPriceBreakDown);
		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.DETAIL_SEARCH_ROOMS);
		MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), Constants.B2C);
		displayPriceBreakDown.setTotalTax(9.0d);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);
		Map<String, String> expDataMap1 = new HashMap<>();
		expDataMap1.put("ST","T");
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, expDataMap1, 2, "INR",null, 1,  true, "1152",true, false, true,true, null));
		expDataMap1.put("PDO","PNT");
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, expDataMap1, 2, "INR",null, 2,  true, "1152",true, true, true,true, null));
		expDataMap1.put("PDO","PRNT");
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, expDataMap1, 1, "INR","bed", 1,  true, "1152",true, true, true,false, null));
		expDataMap1.put("PDO","PN");
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, expDataMap1, 1, "INR",null, 2,  true, "1152",true, true, false,false, "entire"));
		expDataMap1.put("PDO","TP");
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, expDataMap1, 2, "INR",null, 1,  true, "1152",true, true, true,false, null));
		expDataMap1.put("PDO","TPT");
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, expDataMap1, 2, "INR",null, 3,  true, "1152",true, true, true,false, null));
		expDataMap1.remove("PDO");
		displayPriceBreakDown.setCouponInfo(null);
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, expDataMap1, 2, "INR",null, 1,  true, "1152",true, true, true,false, null));
	}

	@Test
	public void buildSelectedSpecialRequestTest() {
		SpecialRequest specialRequest = new SpecialRequest();
		specialRequest.setCategories(new ArrayList<>());
		specialRequest.getCategories().add(new SpecialRequestCategory());
		specialRequest.getCategories().get(0).setCode("test");
		Assert.assertNotNull(commonResponseTransformer.buildSelctedSpecialRequests(specialRequest, specialRequest));
	}

	@Test
	public void buildPropertyRulesTest() {
		RequestInputBO inputBo = new RequestInputBO.Builder().buildNotices(new ArrayList<>()).
				buildPahWithCC(true).buildCancellationPolicyType("NR").
				buildHouseRules(new HouseRules()).buildMustReadRules(new ArrayList<>())
				.buildCheckinPolicy(new RatePolicy()).buildConfirmationPolicy(new RatePolicy()).build();
		inputBo.getConfirmationPolicy().setDescription("test");
		inputBo.getCheckinPolicy().setDescription("test");
		inputBo.getNotices().add(new Notices());
		inputBo.getNotices().get(0).setDescription("test");
		List<Policy> policyList = new ArrayList<>();
		policyList.add(new Policy());
		policyList.get(0).setRules(new ArrayList<>());
		policyList.get(0).getRules().add("test1");
		inputBo.getMustReadRules().add("test");
		Mockito.when(policiesResponseTransformer.buildHouseRules(Mockito.any())).thenReturn(policyList);
		Assert.assertNotNull(commonResponseTransformer.buildPropertyRules(inputBo));
	}

	@Test
	public void buildAdditionalChargesTest() {
		//cityCode is set as CTMALDI to test for Transfers Message
		AdditionalChargesBO inputBo = new AdditionalChargesBO.Builder().buildAdditionalFees(new ArrayList<>()).buildCityCode(Constants.CITY_CODE_MALDIVES).buildPropertyType("test").build();
		inputBo.getAdditionalFees().add(new AdditionalFees());
		inputBo.getAdditionalFees().get(0).setAmount(0d);
		inputBo.getAdditionalFees().get(0).setPrice(new AdditionalFeesPrice());
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayRoom(1d);
		inputBo.getAdditionalFees().get(0).setTotalRooms(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayAdult(1d);
		inputBo.getAdditionalFees().get(0).setTotalAdults(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayChild(1d);
		inputBo.getAdditionalFees().get(0).setTotalChild(1);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false));
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayRoom(null);
		inputBo.getAdditionalFees().get(0).setTotalRooms(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayAdult(1d);
		inputBo.getAdditionalFees().get(0).setTotalAdults(1);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false));
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayAdult(null);
		inputBo.getAdditionalFees().get(0).setTotalAdults(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightRoom(1d);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false));
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightRoom(null);
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightAdult(1d);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false));
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightAdult(1d);
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightChild(1d);
		inputBo.getAdditionalFees().get(0).setTotalChild(1);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo,false));
		inputBo.getAdditionalFees().get(0).setLeafCategory(Constants.TRANSFERS);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, true).getBreakup().get(0).getChargesMsg());
	}

	@Test
	public void buildBlackInfoTest() {
		Assert.assertNotNull(commonResponseTransformer.buildBlackInfo(new BlackInfo()));
	}

	@Test
	public void buildTagsTest() {
		List<CorpTags> tagList = new ArrayList<>();
		tagList.add(new CorpTags());
		Assert.assertNotNull(commonResponseTransformer.buildTags(tagList));
	}

	@Test
	public void getImportantInfoSectionTest() {
		RequestInputBO inputBo = new RequestInputBO.Builder().build();
		Assert.assertNotNull(commonResponseTransformer.getImportantInfoSection(inputBo));
	}

	@Test
	public void testGetAmenities() {
		List<FacilityGroup> amenities = new ArrayList<>();
		FacilityGroup fcltyGrp = new FacilityGroup();
		List<Facility> facilities = new ArrayList<>();
		Facility fclty = new Facility();
		fclty.setDisplayType("2");
		List<AttributesFacility> childAttributes = new ArrayList<>();
		AttributesFacility attr = new AttributesFacility();
		childAttributes.add(attr );
		fclty.setChildAttributes(childAttributes );
		facilities.add(fclty );
		fcltyGrp.setFacilities(facilities );
		amenities.add(fcltyGrp );
		List<SelectRoomAmenities> amnts = commonResponseTransformer.getAmenities(amenities);
		Assert.assertNotNull(amnts);
	}

	@Test
	public void testGetHotelCategories_hotelCategoryIsEmpty_returnsNull(){
		Assert.assertNull(commonResponseTransformer.getHotelCategories(null,false));
	}

	@Test
	public void testGetHotelCategories_hotelCategoryIsNotEmpty(){
		Set<String> hotelCategories = new HashSet<>();
		hotelCategories.add("");
		hotelCategories.add("category");
		List<String> hotelCategoryTypesPriority = new ArrayList<>();
		hotelCategoryTypesPriority.add("");
		ReflectionTestUtils.setField(commonResponseTransformer,"categoryTextToCategoryTypeMap",new HashMap<>());
		ReflectionTestUtils.setField(commonResponseTransformer,"hotelCategoryTypesPriority",hotelCategoryTypesPriority);
		Assert.assertNotEquals(hotelCategoryTypesPriority.subList(0,1),commonResponseTransformer.getHotelCategories(hotelCategories,false));
	}

	@Test
	public void testBuildAmountYouPayingNow(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAmountYouPayingNow", new ArrayList<PricingDetails>(),null, "");
	}

	@Test
	public void buildBaseFare_buildTaxesAndServiceFee_buildWallet_buildPriceAfterDiscount_buildTotalDiscounts(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(1);
		displayPriceBreakDown.setHotelTax(1);
		displayPriceBreakDown.setHotelServiceCharge(1);
		displayPriceBreakDown.setMmtServiceCharge(1);
		displayPriceBreakDown.setAffiliateFee(1);
		displayPriceBreakDown.setWallet(1);
		displayPriceBreakDown.setMmtDiscount(4);
		displayPriceBreakDown.setSavingPerc(2.0);
		displayPriceBreakDown.setBlackDiscount(1);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);
		displayPriceBreakDown.setCdfDiscount(2);
		String expData= "{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,IAO:T,EMIDT:1,NEWTY:T,AIP:T,TFT:T,GEC:F}";
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildBaseFare", new ArrayList<PricingDetails>(),displayPriceBreakDown);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTaxesAndServiceFee", new ArrayList<PricingDetails>(),displayPriceBreakDown,"IN",new HashMap<>(),false, 0.0);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTaxesAndServiceFee", new ArrayList<PricingDetails>(),displayPriceBreakDown,"IN",new HashMap<>(),true, 0.0);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildWallet", new ArrayList<PricingDetails>(),displayPriceBreakDown);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildPriceAfterDiscount", new ArrayList<PricingDetails>(),displayPriceBreakDown);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTotalDiscounts", new ArrayList<PricingDetails>(),displayPriceBreakDown, true, "1152",new HashMap<>());
	}

	@Test
	public void test_buildDiscount(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(20);
		displayPriceBreakDown.setHotelTax(1);
		displayPriceBreakDown.setHotelServiceCharge(1);
		displayPriceBreakDown.setMmtServiceCharge(1);
		displayPriceBreakDown.setAffiliateFee(1);
		displayPriceBreakDown.setWallet(1);
		displayPriceBreakDown.setSavingPerc(2.0);
		displayPriceBreakDown.setMmtDiscount(4);
		displayPriceBreakDown.setCdfDiscount(5);
		displayPriceBreakDown.setBlackDiscount(1);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);

		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.REVIEW_AVAIL_ROOMS);
		List<PricingDetails> pricingDetails = new ArrayList<PricingDetails>();
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTotalDiscounts", pricingDetails,displayPriceBreakDown, true, "1152",new HashMap<>());
		Assert.assertNotNull(pricingDetails);

	}

	@Test
	public void testtBuildCancellationTimeline(){
		Assert.assertNotEquals(new CancellationTimeline(),commonResponseTransformer.buildCancellationTimeline(new com.mmt.hotels.model.response.pricing.CancellationTimeline()));
	}

	@Test
	public void testtBuildCancellationPolicyTimeline(){
		Assert.assertNotEquals(new CancellationPolicyTimeline(),commonResponseTransformer.buildCancellationPolicyTimeline(new com.mmt.hotels.model.response.pricing.CancellationTimeline()));
	}

	@Test
	public void buildAffiliateFeeDetails_affiliateFeeDetailsIsEmpty_returnsNull(){
		Assert.assertNull(commonResponseTransformer.buildAffiliateFeeDetails(null));
	}

	@Test
	public void buildAffiliateFeeDetails_affiliateFeeDetailsIsNotEmpty_returnsAffiliateFeeDetailsList(){
		List<AffiliateFeeDetails> affiliateFeeDetailsList = new ArrayList<>();
		affiliateFeeDetailsList.add(new  AffiliateFeeDetails());
		Assert.assertNotNull(commonResponseTransformer.buildAffiliateFeeDetails(affiliateFeeDetailsList));
	}

	@Test
	public void testGetHighlightedAmenities(){
		List<FacilityGroup> highlightedAmenities = new ArrayList<>();
		FacilityGroup facilityGroup = new FacilityGroup();
		facilityGroup.setName("");
		highlightedAmenities.add(facilityGroup);
		Assert.assertNotNull(commonResponseTransformer.getHighlightedAmenities(highlightedAmenities));
	}

	@Test
	public void buildListPersonalizationResponse_listPersonalizationResponseIsNull_returnsNull() {
		Assert.assertNull(commonResponseTransformer.buildListPersonalizationResponse(null,"",new LinkedHashMap<>(), "", "", commonModifierResponse));
	}
	@Test
	public void buildListPersonalizationResponse_listPersonalizationResponseIsNotNull_returnsNotNull() {
		ListPersonalizationResponse listPersonalizationResponse = new ListPersonalizationResponse();
		listPersonalizationResponse.setCardData(new HashMap<>());
		listPersonalizationResponse.getCardData().put(1, new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).add(new CardData());
		listPersonalizationResponse.getCardData().get(1).get(0).setCardPayload(new CardPayloadResponse());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardPayload().setAltAccoDiscovery(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardPayload().setAltAccoData(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).setCardAction(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().add(new CardAction());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).setPriceBucket(new PriceBucket());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).setMatchmakerTags(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).getMatchmakerTags().add(new MatchmakerTag());

		Mockito.when(filterFactory.getFilterConfiguration(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(new FilterConfiguration());
		Assert.assertNull(commonResponseTransformer.buildListPersonalizationResponse(listPersonalizationResponse,"ANDROID",new LinkedHashMap<>(), "test", "", commonModifierResponse));
	}

	@Test
	public void buildCardInfo() {
		List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCBList = new ArrayList<>();
		cardDataCBList.add(cardDataCB);
		FilterConfiguration filterConfiguration = mock(FilterConfiguration.class);
		CardInfo result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer,
				"buildCardInfo",
				cardDataCBList,
				"ANDROID",
				new LinkedHashMap<String, String>(),
				filterConfiguration
		);
		assertNotNull(result);
	}

	@Test
	public void buildCardCondition(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardCondition", cardCondition);
	}

	@Test
	public void buildFilters(){
		Set<Filter> filters = new HashSet<>();
		filters.add(filter);
		Mockito.when(filter.getFilterGroup()).thenReturn(STAR_RATING);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFilters", filters);
	}

	@Test
	public void buildCardPayload_and_buildGenericCardData(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardPayload", cardPayload,"",null);
		List<GenericCardPayloadData> genericCardData = new ArrayList<>();
		genericCardData.add(genericCardPayloadData);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildGenericCardData", genericCardData);
	}

	@Test
	public void buildSpokeCityDataEmptyTest(){
		List<SpokeCity> spokeCityList = new ArrayList<>();
		List<SpokeCityCG> spokeCityCGList =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildSpokeCityData", spokeCityList);
		Assert.assertEquals(0,spokeCityCGList.size());
	}

	@Test
	public void buildSpokeCityDataTest(){
		List<SpokeCity> spokeCityList = new ArrayList<>();
		SpokeCity spokeCity = new SpokeCity();
		spokeCity.setLocId("CITY");
		spokeCityList.add(spokeCity);
		List<SpokeCityCG> spokeCityCGList =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildSpokeCityData", spokeCityList);
		Assert.assertEquals(1,spokeCityCGList.size());
	}

	@Test
	public void buildHotelListNewEmptyTest(){
	CardPayloadData cardPayloadData =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardPayload", cardPayload,"",null);
	List<Hotel> hotelList =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "convertToHotelListNew", cardPayloadData);
	Assert.assertNull(hotelList);
	}


	@Test
	public void buildAltAccoDiscovery(){
		List<CollectionsResponseBo<SearchWrapperHotelEntityAbridged>> list = new ArrayList<>();
		list.add(searchWrapperHotelEntity);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAltAccoDiscovery", null,"");
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAltAccoDiscovery", list,"");
	}

	@Test
	public void buildCollectionsResponse(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCollectionsResponse", null,"");
	}

	@Test
	public void buildCardAction(){
		List<com.mmt.hotels.pojo.listing.personalization.CardAction> list = new ArrayList<>();
		list.add(cardAction);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardAction", list, null);
	}

	@Test
	public void buildContextualFilterData(){
		List<com.mmt.hotels.model.response.listpersonalization.ContextualFilterData> list = new ArrayList<>();
		list.add(contextualFilterData);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildContextualFilterData", list);
	}

	@Test
	public void buildPropertyPersuasions(){
		List<PropertyPersuasions> list = new ArrayList<>();
		list.add(propertyPersuasions);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildPropertyPersuations", list);
	}

	@Test
	public void buildSearchContext(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildSearchContext", searchContext);
	}

	@Test
	public void getConfirmationPolicy(){
		Assert.assertNull(commonResponseTransformer.getConfirmationPolicy(roomTypeDetails));
	}

	@Test
	public void buildFilterRange(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFilterRange", filterRange);
	}

	@Test
	public void buildAppliedFilterMap(){
		Set<Filter> filters= new HashSet<>();
		filters.add(filter);
		Mockito.when(filter.getFilterGroup()).thenReturn(STAR_RATING);
		Map<FilterGroup, Set<Filter>> appliedFilterMap = new HashMap<>();
		appliedFilterMap.put(STAR_RATING,filters);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAppliedFilterMap", appliedFilterMap);
	}

	@Test
	public void buildPolarisDataTest() {
		PolarisData polarisDataCB = new PolarisData();
		polarisDataCB.setShowImage(true);
		List<PolarisTag> tagListCB = new ArrayList<>();
		PolarisTag polarisTagCB = new PolarisTag();
		polarisTagCB.setId("1");
		polarisTagCB.setDesc("testDescription");
		polarisTagCB.setBbox(new BbLatLong());
		polarisTagCB.getBbox().setNe(new LatLong());
		polarisTagCB.getBbox().setSw(new LatLong());
		tagListCB.add(polarisTagCB);
		polarisDataCB.setTags(tagListCB);
		com.mmt.hotels.clientgateway.response.moblanding.PolarisData polarisDataCG = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "buildPolarisData", polarisDataCB);
		Assert.assertNotNull(polarisDataCG);
		Assert.assertEquals(polarisDataCG.getTags().size(), 1);
	}

	public void testGetPois(){
		POIInfo poiInfo = new POIInfo();
		poiInfo.setId("1234");
		poiInfo.setCentre(new Centre());
		poiInfo.setMeta(new Meta());
		poiInfo.getMeta().setCategory(new Category());
		poiInfo.getMeta().getCategory().setName("Airport");

		List<Poi> pois = commonResponseTransformer.getPois(Arrays.asList(poiInfo));

		Assert.assertEquals(1, pois.size());
	}

//	@Test
//	public void testBuildMedia(){
//
//		List<String> mainImages = Arrays.asList("img1","img2","img3","img4","img5","img6","img7");
//		VideoInfo v1 = new VideoInfo();
//		v1.setUrl("videourl1");
//		VideoInfo v2 = new VideoInfo();
//		v1.setUrl("videourl2");
//		List<VideoInfo> videoInfoList = Arrays.asList(v1,v2);
//
//		List<MediaInfo> mediaInfoList = commonResponseTransformer.buildMedia(Arrays.asList("imgurl1", "imgurl2"), Arrays.asList(new VideoInfo()),null);
//		Assert.assertEquals(3, mediaInfoList.size());
//
//		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,"{VIDEO:0}");
//		Assert.assertEquals(5,mediaInfoList.size());
//		Assert.assertNull(mediaInfoList.stream().filter(m->m.getMediaType().equalsIgnoreCase("VIDEO")).findAny().orElse(null));
//
//		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,"{VIDEO:1}");
//		Assert.assertEquals(5,mediaInfoList.size());
//		Assert.assertNotNull(mediaInfoList.stream().filter(m->m.getMediaType().equalsIgnoreCase("VIDEO")).findAny());
//		Assert.assertNotNull(mediaInfoList.get(0));
//		Assert.assertTrue(mediaInfoList.get(0).getMediaType().equalsIgnoreCase("VIDEO"));
//
//		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,"{VIDEO:2}");
//		Assert.assertEquals(5,mediaInfoList.size());
//		Assert.assertNotNull(mediaInfoList.stream().filter(m->m.getMediaType().equalsIgnoreCase("IMAGE")).findAny());
//		Assert.assertNotNull(mediaInfoList.get(1));
//		Assert.assertTrue(mediaInfoList.get(1).getMediaType().equalsIgnoreCase("VIDEO"));
//
//		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,"{VIDEO:3}");
//		Assert.assertEquals(5,mediaInfoList.size());
//	}

	@Test
	public void test_buildGeoLocation() {
		com.mmt.hotels.model.response.searchwrapper.GeoLocation webApiGeoLocation = new GeoLocation();
		com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation response;

		GeoLocation geoLocation = null;
		response = commonResponseTransformer.buildGeoLocation(geoLocation);
		Assert.assertNull(response);

		webApiGeoLocation.setLatitude("12");
		response = commonResponseTransformer.buildGeoLocation(webApiGeoLocation);
		Assert.assertEquals(new Double(12.0d),response.getLatitude());
		Assert.assertNull(response.getLongitude());
		Assert.assertNull(response.getDistance());

		webApiGeoLocation.setLongitude("34");
		response = commonResponseTransformer.buildGeoLocation(webApiGeoLocation);
		Assert.assertEquals(new Double(12.0d),response.getLatitude());
		Assert.assertEquals(new Double(34.0d),response.getLongitude());
		Assert.assertNull(response.getDistance());

		webApiGeoLocation.setDistanceMeter(56.0d);
		response = commonResponseTransformer.buildGeoLocation(webApiGeoLocation);
		Assert.assertEquals(new Double(12.0d),response.getLatitude());
		Assert.assertEquals(new Double(34.0d),response.getLongitude());
		Assert.assertEquals(new Double(56.0d),response.getDistance());
	}

	@Test
	public void test_buildReviewSummary() throws IOException, JsonParseException {
		Assert.assertNull(commonResponseTransformer.buildReviewSummary(null,null,null, null));

		HashMap<OTA, JsonNode> map = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:flyfishSummary.json")),
				HashMap.class);

		HashMap<OTA,JsonNode> webApiMap = new HashMap<>();
		ObjectMapper objectMapper = new ObjectMapper();
		webApiMap.put(OTA.MMT,objectMapper.valueToTree(map.get("MMT")));
		webApiMap.put(OTA.TA,objectMapper.valueToTree(map.get("TA")));

		ReviewSummary response = commonResponseTransformer.buildReviewSummary(null,webApiMap,null, null);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.TA.getValue(),response.getSource());

		response = commonResponseTransformer.buildReviewSummary("IN",webApiMap,null, null);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.MMT.getValue(),response.getSource());


		webApiMap.put(OTA.EXT,objectMapper.valueToTree(map.get("MMT")));
		webApiMap.remove(OTA.MMT);
		webApiMap.remove(OTA.TA);
		response = commonResponseTransformer.buildReviewSummary("IN",webApiMap,null, null);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.EXT.getValue(),response.getSource());

		DeviceDetails device=new DeviceDetails();
		device.setBookingDevice("ANDROID");
		device.setAppVersion("8.5.9");
		response = commonResponseTransformer.buildReviewSummary(null,webApiMap,device, null);
		Assert.assertNotNull(response);

		device.setAppVersion("8.4.6");
		response = commonResponseTransformer.buildReviewSummary(null,webApiMap,device, null);
		Assert.assertNull(response);

		JsonNode jsonNode = objectMapper.readTree(
				"{\"cumulativeRating\":4.5,\"totalReviewsCount\":226,\"totalRatingCount\":50,\"travellerRatingSummary\":{\"hotelSummary\":[{\"concept\":\"Safety and Hygiene\",\"value\":4.4,\"show\":true,\"heroTag\":true,\"reviewCount\":65},{\"concept\":\"Security\",\"value\":5.0,\"show\":true,\"reviewCount\":1},{\"concept\":\"Location\",\"value\":4.6,\"show\":true,\"reviewCount\":50,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Location\",\"relatedReviewCount\":53,\"tagType\":\"BASE\",\"priorityScore\":53},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Distance from Beach\",\"relatedReviewCount\":28,\"tagType\":\"BASE\",\"priorityScore\":28},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach\",\"relatedReviewCount\":23,\"tagType\":\"BASE\",\"priorityScore\":23},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Connectivity\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Central Location\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Location\",\"relatedReviewCount\":52,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":52},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Near Beach\",\"relatedReviewCount\":51,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":51}]},{\"concept\":\"Hospitality\",\"value\":4.5,\"show\":true,\"reviewCount\":167,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Courtesy\",\"relatedReviewCount\":76,\"tagType\":\"BASE\",\"priorityScore\":76},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Service Quality\",\"relatedReviewCount\":16,\"tagType\":\"BASE\",\"priorityScore\":16},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Courteous Staff\",\"relatedReviewCount\":75,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":75},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Service\",\"relatedReviewCount\":14,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":14}]},{\"concept\":\"Room\",\"value\":4.4,\"show\":true,\"reviewCount\":61,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Quality\",\"relatedReviewCount\":56,\"tagType\":\"BASE\",\"priorityScore\":56},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Space in Rooms\",\"relatedReviewCount\":7,\"tagType\":\"BASE\",\"priorityScore\":7},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Balcony\",\"relatedReviewCount\":6,\"tagType\":\"BASE\",\"priorityScore\":6},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Bathroom Hygiene\",\"relatedReviewCount\":4,\"tagType\":\"BASE\",\"priorityScore\":4},{\"sentiment\":\"NEGATIVE\",\"subConcept\":\"Bed Quality\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Amenities\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Room\",\"relatedReviewCount\":60,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":60},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Balcony\",\"relatedReviewCount\":6,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":6}]},{\"concept\":\"Cleanliness\",\"value\":4.2,\"show\":true,\"reviewCount\":222,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Cleanliness\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Resort Cleanliness\",\"relatedReviewCount\":10,\"tagType\":\"BASE\",\"priorityScore\":10},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Bathroom Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach Cleanliness\",\"relatedReviewCount\":3,\"tagType\":\"BASE\",\"priorityScore\":3},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Hotel Cleanliness\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Surroundings Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Garden Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Area Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Amenity Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Place Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Environment Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Room\",\"relatedReviewCount\":17,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":10000},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Property\",\"relatedReviewCount\":9,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":9900},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Bathroom\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Pool\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Facilities\",\"value\":4.2,\"show\":true,\"reviewCount\":96,\"subConcepts\":[{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Patio\",\"relatedReviewCount\":12,\"tagType\":\"BASE\",\"priorityScore\":12},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool\",\"relatedReviewCount\":8,\"tagType\":\"BASE\",\"priorityScore\":8},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"In-House Activities\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Pool\",\"relatedReviewCount\":8,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":8}]},{\"concept\":\"Value for Money\",\"value\":4.2,\"show\":true,\"reviewCount\":32,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Luxury\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Food\",\"value\":4.1,\"show\":true,\"reviewCount\":153,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Food\",\"relatedReviewCount\":38,\"tagType\":\"BASE\",\"priorityScore\":38},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Breakfast\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Local Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Continental Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Mediterranean Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Food\",\"relatedReviewCount\":34,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":34},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Breakfast\",\"relatedReviewCount\":15,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":15},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Child friendliness\",\"value\":4.0,\"show\":true,\"reviewCount\":21}],\"roomSummary\":{\"45000297254\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"View\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"566\":{\"cumulativeRating\":0.0},\"567\":{\"cumulativeRating\":0.0},\"24133\":{\"cumulativeRating\":0.0,\"best\":[{\"publishDate\":\"Feb 21, 2021\",\"travellerName\":\"Aditya Surana\",\"title\":\"Waooooo. Really worth staying\",\"rating\":5.0,\"reviewText\":\"Waooo, what a place what a location and above all the beach view rooms are the best\\nthe breakfast varity was good event the service but taste 5/10, rest a must visit resort if u visit goa\",\"id\":\"P9J5FYGIY2HN4A8EU55CO0ULNI9XHEDUV5S0DIQOINYFGGC5KTDNFR6IZYAVRU0XH5XTQ4IQJT0M\",\"travelType\":\"COUPLE\",\"crawledData\":false}]},\"45000308605\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Spacious Room\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068630\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068632\":{\"cumulativeRating\":0.0},\"560\":{\"cumulativeRating\":0.0}}},\"crawledData\":false,\"cityCode\":\"GOI\",\"sortingCriterion\":[\"Latest first\",\"Helpful first\",\"Positive first\",\"Negative first\"],\"ratingText\":\"Excellent\",\"postLockdownData\":{\"rating\":4.5,\"totalReviewCount\":64,\"ratingCount\":10,\"textCount\":47,\"imageCount\":2,\"imageTextCount\":5}}");
		webApiMap.put(OTA.MMT, jsonNode);
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(Constants.SHOW_MMT_RATING_EXP, Constants.TRUE);
		webApiMap.put(OTA.TA, jsonNode);
		response = commonResponseTransformer.buildReviewSummary("UAE", webApiMap, device, expDataMap);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.TA.getValue(),response.getSource());

		expDataMap.put(Constants.SHOW_MMT_RATING_EXP, "False");
		response = commonResponseTransformer.buildReviewSummary("UAE", webApiMap, device, expDataMap);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.TA.getValue(),response.getSource());

		jsonNode = objectMapper.readTree(
				"{\"cumulativeRating\":4.5,\"totalReviewsCount\":226,\"totalRatingCount\":51,\"travellerRatingSummary\":{\"hotelSummary\":[{\"concept\":\"Safety and Hygiene\",\"value\":4.4,\"show\":true,\"heroTag\":true,\"reviewCount\":65},{\"concept\":\"Security\",\"value\":5.0,\"show\":true,\"reviewCount\":1},{\"concept\":\"Location\",\"value\":4.6,\"show\":true,\"reviewCount\":50,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Location\",\"relatedReviewCount\":53,\"tagType\":\"BASE\",\"priorityScore\":53},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Distance from Beach\",\"relatedReviewCount\":28,\"tagType\":\"BASE\",\"priorityScore\":28},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach\",\"relatedReviewCount\":23,\"tagType\":\"BASE\",\"priorityScore\":23},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Connectivity\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Central Location\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Location\",\"relatedReviewCount\":52,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":52},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Near Beach\",\"relatedReviewCount\":51,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":51}]},{\"concept\":\"Hospitality\",\"value\":4.5,\"show\":true,\"reviewCount\":167,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Courtesy\",\"relatedReviewCount\":76,\"tagType\":\"BASE\",\"priorityScore\":76},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Service Quality\",\"relatedReviewCount\":16,\"tagType\":\"BASE\",\"priorityScore\":16},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Courteous Staff\",\"relatedReviewCount\":75,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":75},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Service\",\"relatedReviewCount\":14,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":14}]},{\"concept\":\"Room\",\"value\":4.4,\"show\":true,\"reviewCount\":61,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Quality\",\"relatedReviewCount\":56,\"tagType\":\"BASE\",\"priorityScore\":56},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Space in Rooms\",\"relatedReviewCount\":7,\"tagType\":\"BASE\",\"priorityScore\":7},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Balcony\",\"relatedReviewCount\":6,\"tagType\":\"BASE\",\"priorityScore\":6},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Bathroom Hygiene\",\"relatedReviewCount\":4,\"tagType\":\"BASE\",\"priorityScore\":4},{\"sentiment\":\"NEGATIVE\",\"subConcept\":\"Bed Quality\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Amenities\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Room\",\"relatedReviewCount\":60,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":60},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Balcony\",\"relatedReviewCount\":6,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":6}]},{\"concept\":\"Cleanliness\",\"value\":4.2,\"show\":true,\"reviewCount\":222,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Room Cleanliness\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Resort Cleanliness\",\"relatedReviewCount\":10,\"tagType\":\"BASE\",\"priorityScore\":10},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Bathroom Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool Cleanliness\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Beach Cleanliness\",\"relatedReviewCount\":3,\"tagType\":\"BASE\",\"priorityScore\":3},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Hotel Cleanliness\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Surroundings Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Garden Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Area Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Amenity Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Place Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Staff Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Environment Cleanliness\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Room\",\"relatedReviewCount\":17,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":10000},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Property\",\"relatedReviewCount\":9,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":9900},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Bathroom\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Clean Pool\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Facilities\",\"value\":4.2,\"show\":true,\"reviewCount\":96,\"subConcepts\":[{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Patio\",\"relatedReviewCount\":12,\"tagType\":\"BASE\",\"priorityScore\":12},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Pool\",\"relatedReviewCount\":8,\"tagType\":\"BASE\",\"priorityScore\":8},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"In-House Activities\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Pool\",\"relatedReviewCount\":8,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":8}]},{\"concept\":\"Value for Money\",\"value\":4.2,\"show\":true,\"reviewCount\":32,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Luxury\",\"relatedReviewCount\":2,\"tagType\":\"BASE\",\"priorityScore\":2},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Value for Money\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Food\",\"value\":4.1,\"show\":true,\"reviewCount\":153,\"subConcepts\":[{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Food\",\"relatedReviewCount\":38,\"tagType\":\"BASE\",\"priorityScore\":38},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Breakfast\",\"relatedReviewCount\":17,\"tagType\":\"BASE\",\"priorityScore\":17},{\"sentiment\":\"NEUTRAL\",\"subConcept\":\"Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"BASE\",\"priorityScore\":5},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Local Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Continental Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Mediterranean Food\",\"relatedReviewCount\":1,\"tagType\":\"BASE\",\"priorityScore\":1},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Food\",\"relatedReviewCount\":34,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":34},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Breakfast\",\"relatedReviewCount\":15,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":15},{\"sentiment\":\"POSITIVE\",\"subConcept\":\"Good Restaurant\",\"relatedReviewCount\":5,\"tagType\":\"WHAT_GUESTS_SAY\",\"priorityScore\":5}]},{\"concept\":\"Child friendliness\",\"value\":4.0,\"show\":true,\"reviewCount\":21}],\"roomSummary\":{\"45000297254\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"View\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"566\":{\"cumulativeRating\":0.0},\"567\":{\"cumulativeRating\":0.0},\"24133\":{\"cumulativeRating\":0.0,\"best\":[{\"publishDate\":\"Feb 21, 2021\",\"travellerName\":\"Aditya Surana\",\"title\":\"Waooooo. Really worth staying\",\"rating\":5.0,\"reviewText\":\"Waooo, what a place what a location and above all the beach view rooms are the best\\nthe breakfast varity was good event the service but taste 5/10, rest a must visit resort if u visit goa\",\"id\":\"P9J5FYGIY2HN4A8EU55CO0ULNI9XHEDUV5S0DIQOINYFGGC5KTDNFR6IZYAVRU0XH5XTQ4IQJT0M\",\"travelType\":\"COUPLE\",\"crawledData\":false}]},\"45000308605\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Spacious Room\",\"sentiment\":\"POSITIVE\",\"category\":\"LIKE_DISLIKE\"},{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068630\":{\"cumulativeRating\":0.0,\"insights\":[{\"subConcept\":\"Clean bathrooms get a thumbs up from our guests\",\"sentiment\":\"POSITIVE\",\"tip\":\"Clean bathrooms get a thumbs up from our guests\",\"category\":\"ROOM_TIP\"}]},\"45000068632\":{\"cumulativeRating\":0.0},\"560\":{\"cumulativeRating\":0.0}}},\"crawledData\":false,\"cityCode\":\"GOI\",\"sortingCriterion\":[\"Latest first\",\"Helpful first\",\"Positive first\",\"Negative first\"],\"ratingText\":\"Excellent\",\"postLockdownData\":{\"rating\":4.5,\"totalReviewCount\":64,\"ratingCount\":10,\"textCount\":47,\"imageCount\":2,\"imageTextCount\":5}}");
		webApiMap.put(OTA.MMT, jsonNode);
		expDataMap.put(Constants.SHOW_MMT_RATING_EXP, Constants.TRUE);
		response = commonResponseTransformer.buildReviewSummary("UAE", webApiMap, device, expDataMap);
		Assert.assertNotNull(response);
//		Assert.assertEquals(OTA.MMT.getValue(),response.getSource());

		expDataMap = null;
		response = commonResponseTransformer.buildReviewSummary("UAE", webApiMap, device, expDataMap);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.TA.getValue(),response.getSource());
	}

	@Test
	public void buildValueStayTagTest(){
		Map<String, HotelTag> map = commonResponseTransformer.buildValueStaysHotelTag("Sample Title", new HashMap<>());
		Assert.assertTrue(MapUtils.isNotEmpty(map));
	}

	@Test
	public void buildFreeCancellationBenefitsTest() {
		List<FCBenefit> benefitsCG = ReflectionTestUtils
				.invokeMethod(commonResponseTransformer, "buildFreeCancellationBenefits",
											Collections.emptyList());
		Assert.assertNull(benefitsCG);

		List<com.mmt.hotels.model.response.pricing.FCBenefit> benefitsHes = new ArrayList<>();
		com.mmt.hotels.model.response.pricing.FCBenefit fcBenefitHes = new com.mmt.hotels.model.response.pricing.FCBenefit();
		fcBenefitHes.setType("FC");
		fcBenefitHes.setText("FC till check in");
		benefitsHes.add(fcBenefitHes);

		fcBenefitHes = new com.mmt.hotels.model.response.pricing.FCBenefit();
		fcBenefitHes.setType("FCZPN");
		fcBenefitHes.setText("Zero Payment Now");
		benefitsHes.add(fcBenefitHes);
		benefitsCG = ReflectionTestUtils
																	.invokeMethod(commonResponseTransformer, "buildFreeCancellationBenefits",
																								benefitsHes);
		Assert.assertEquals(benefitsCG.size(), 2);
		Assert.assertEquals(benefitsCG.get(0).getIconType(), IconType.SINGLETICK);
		Assert.assertEquals(benefitsCG.get(1).getIconType(), IconType.DOUBLETICK);
	}

	@Test
	public void testEnableAmenitiesPersuasion() {
//		Assert.assertFalse(commonResponseTransformer.enableAmenitiesPersuasion("{disableAmenities:1}", "HOTELS",false));
		Assert.assertTrue(commonResponseTransformer.enableAmenitiesPersuasion("{disableAmenities:0}", "HOTELS",true));
//		Assert.assertFalse(commonResponseTransformer.enableAmenitiesPersuasion(null, "HOTELS",false));
	}

	@Test
	public void testEnableSaveValuePersuasion() {
		Assert.assertFalse(commonResponseTransformer.enableSaveValue("{disableSaveValue:1}"));
//		Assert.assertTrue(commonResponseTransformer.enableSaveValue("{disableSaveValue:0}"));
		Assert.assertFalse(commonResponseTransformer.enableSaveValue(null));
	}

	@Test
	public void testBuildFilterCG() {
		Filter filterHes = new Filter();
		filterHes.setFilterGroup(STAR_RATING);
		filterHes.setFilterRange(new FilterRange());
		filterHes.setSequence(0);
		filterHes.setSuggestedFilters(new ArrayList<>());
		Filter suggestedFilter = new Filter();
		suggestedFilter.setFilterGroup(LOCATION);
		filterHes.getSuggestedFilters().add(suggestedFilter);
		Assert.assertNotNull(commonResponseTransformer.buildFilterCG(filterHes));
	}

	@Test
	public void testBuildSearchContext() {
		String lob = "HOTELS";
		String cityCode = "CTDEL";
		String countryCode = "IN";
		String tripType = "ONEWAY";
		CardPayloadResponse cardPayload = new CardPayloadResponse();
		AdTechSearchContext searchContext = new AdTechSearchContext();
		ArrayList<AdTechSearchContextDetails> currSearchContext = new ArrayList<>();
		AdTechSearchContextDetails searchContextDetails = new AdTechSearchContextDetails();
		searchContextDetails.setLob(lob);
		AdTechDestination destination = new AdTechDestination();
		destination.setCityCode(cityCode);
		destination.setCountryCode(countryCode);
		searchContextDetails.setDestination(destination);
		Date startDate = Date.from(Instant.now());
		searchContextDetails.setStartDate(startDate.getTime());
		Date endDate = Date.from(Instant.now().plus(1, ChronoUnit.DAYS));
		searchContextDetails.setEndDate(endDate.getTime());
		searchContextDetails.setTripType(tripType);
		AdTechHotel hotels = new AdTechHotel();
		ArrayList<AdTechRoomStayParam> roomStayParams = new ArrayList<>();
		AdTechRoomStayParam roomStayParam = new AdTechRoomStayParam();
		ArrayList<AdTechGuestCount> guestCounts = new ArrayList<>();
		AdTechGuestCount guestCount = new AdTechGuestCount();
		guestCount.setCount(2);
		guestCounts.add(guestCount);
		roomStayParam.setGuestCounts(guestCounts);
		roomStayParams.add(roomStayParam);
		hotels.setRoomStayParams(roomStayParams);
		searchContextDetails.setHotels(hotels);
		currSearchContext.add(searchContextDetails);
		searchContext.setCurrSearchContext(currSearchContext);
		cardPayload.setSearchContext(searchContext);
		com.mmt.hotels.clientgateway.response.moblanding.AdTechSearchContext searchContextCG = commonResponseTransformer.buildSearchContext(cardPayload);
		Assert.assertNotNull(searchContextCG);
		Assert.assertNotNull(searchContextCG.getCurrSearchContext());
		Assert.assertEquals(lob, searchContextCG.getCurrSearchContext().get(0).getLob());
		Assert.assertNotNull(searchContextCG.getCurrSearchContext().get(0).getDestination());
		Assert.assertNotNull(searchContextCG.getCurrSearchContext().get(0).getHotels());
		Assert.assertEquals(tripType, searchContextCG.getCurrSearchContext().get(0).getTripType());

	}

	@Test
	public void testBuildSkipApprovalReasons() {
		List<com.mmt.hotels.model.response.corporate.ReasonForSkipApproval> skipApprovalReasonsHES = new ArrayList<>();
		com.mmt.hotels.model.response.corporate.ReasonForSkipApproval reasonForSkipApproval= new ReasonForSkipApproval();
		reasonForSkipApproval.setEnablePersonalCorpBooking(true);
		reasonForSkipApproval.setText("abc");
		reasonForSkipApproval.setInputType("inputtype");
		skipApprovalReasonsHES.add(reasonForSkipApproval);
		List<com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval> response = commonResponseTransformer.buildSkipApprovalReasons(skipApprovalReasonsHES);
		Assert.assertNotNull(response);
	}

	@Test
	public void testBuildGeoLocation() {
		double latitude = 28.64163;
		double longitude = 77.21613;
		HotelResult hotelResult = new HotelResult();
		hotelResult.setLatitude(latitude);
		hotelResult.setLongitude(longitude);
		com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation geoLocation = commonResponseTransformer.buildGeoLocation(hotelResult);
		assertNotNull(geoLocation);
		assertEquals(latitude, geoLocation.getLatitude(), 0.0);
		assertEquals(longitude, geoLocation.getLongitude(), 0.0);
	}

	@Test
	public void buildCorpAutobookRequestorConfigTest() {
		AutobookRequestorConfigBO autobookRequestorConfigHES = new AutobookRequestorConfigBO();
		autobookRequestorConfigHES.setTitle("title");
		autobookRequestorConfigHES.setSubTitle("subtitle");
		CorpAutobookRequestorConfigBO corpAutobookRequestorConfigCG = commonResponseTransformer.buildCorpAutobookRequestorConfig(autobookRequestorConfigHES);
		Assert.assertNotNull(corpAutobookRequestorConfigCG);
		Assert.assertEquals("title", corpAutobookRequestorConfigCG.getTitle());
		Assert.assertEquals("subtitle", corpAutobookRequestorConfigCG.getSubTitle());
	}

	@Test
	public void testGetPriceDetailForHeaderDetails(){
		TotalPricing totalPricing = new TotalPricing();
		List<PricingDetails> details = new ArrayList<PricingDetails>();
		PricingDetails priceDetails1 = new PricingDetails();
		priceDetails1.setAmount(2088.0);
		priceDetails1.setKey("TAXES");
		priceDetails1.setLabel("taxes & fees");

		PricingDetails priceDetails2 = new PricingDetails();
		priceDetails2.setAmount(12000.0);
		priceDetails2.setKey("TOTAL_AMOUNT");
		priceDetails2.setLabel("Total Amount to be paid");

		details.add(priceDetails1);
		details.add(priceDetails2);

		totalPricing.setDetails(details);
		totalPricing.setCouponAmount(0);
		DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
		commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, null, true);
	}

	@Test
	public void testGetPriceDetailForSlotDetails(){
		TotalPricing totalPricing = new TotalPricing();
		List<PricingDetails> details = new ArrayList<PricingDetails>();
		PricingDetails priceDetails1 = new PricingDetails();
		priceDetails1.setAmount(2088.0);
		priceDetails1.setKey("TAXES");
		priceDetails1.setLabel("taxes & fees");

		PricingDetails priceDetails2 = new PricingDetails();
		priceDetails2.setAmount(12000.0);
		priceDetails2.setKey("TOTAL_AMOUNT");
		priceDetails2.setLabel("Total Amount to be paid");

		details.add(priceDetails1);
		details.add(priceDetails2);

		totalPricing.setDetails(details);
		totalPricing.setCouponAmount(0);
		DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
		DayUseSlotPlan slotPlan = new DayUseSlotPlan();
		commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, slotPlan, false);
	}

	@Test
	public void testBuildPaymentPlan(){
		com.mmt.hotels.model.response.pricing.PaymentPlan paymentPlan = new PaymentPlan();
		paymentPlan.setAmount(100.0);
		List<PaymentPlan> paymentPlans = new ArrayList<>();
		paymentPlans.add(new PaymentPlan());
		paymentPlan.setPaymentPolicy(paymentPlans);

		Mockito.when(polyglotService.getTranslatedData(PAYMENT_PLAN_PENALTY_TEXT)).thenReturn("Payment Panelty");
		com.mmt.hotels.clientgateway.response.rooms.PaymentPlan paymentPlanCG = commonResponseTransformer.buildPaymentPlan(paymentPlan);
		Assert.assertEquals("Payment Panelty", paymentPlanCG.getPenaltyText());
	}

	@Test
	public void testBuildMyBizQuickPayConfig() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("abc");
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		MyBizQuickPayConfigBO myBizQuickPayConfigBO = commonResponseTransformer.buildMyBizQuickPayConfig(displayPriceBreakDown);
		Assert.assertNotNull(myBizQuickPayConfigBO);

	}

	@Test
	public void testBuildFooterMessageForNewAppFromNewLoyaltyResponse() {
		// Arrange
		CommonResponseTransformer commonResponseTransformer = new CommonResponseTransformer();
		final LoyaltyMessageResponse loyaltyMessageResponse = new LoyaltyMessageResponse();
		loyaltyMessageResponse.setWalletEarn(0.0);
		loyaltyMessageResponse.setLoyaltyEligible(false);
		loyaltyMessageResponse.setTierName("tierName");
		loyaltyMessageResponse.setMessageHeader("messageHeader");
		loyaltyMessageResponse.setMessageTitle("messageTitle");
		loyaltyMessageResponse.setMessageText("messageText");
		loyaltyMessageResponse.setIconUrl("iconUrl");
		loyaltyMessageResponse.setCta("cta");
		loyaltyMessageResponse.setCtaLink("ctaLink");
		loyaltyMessageResponse.setCtaActionType("ctaActionType");
		loyaltyMessageResponse.setTextList("textList");
		loyaltyMessageResponse.setTnc("tnc");
		//when(goCashBusinessProcessorUnderTest.getLoyaltyMessage(null, 0.0, 0.0)).thenReturn(loyaltyMessageResponse);
		// Assuming Card is a separate class and has a setter method for cardId and templateId
		BookingMessageCard card = new BookingMessageCard();
		card.setCardId("ALL_DISPLAY_META_CARD");
		card.setTemplateID("gi_all_display_meta_card");

		// Assuming Style is a separate class and has setter methods for all its fields
		BookingMessageCardDataStyle style = new BookingMessageCardDataStyle();


		// Assuming Data is a separate class and has a setter method for style
		BookingMessageCardData data = new BookingMessageCardData();
		data.setStyle(style);

		// Set data to card
		card.setData(data);

		style.setBgColorGradient(Arrays.asList("#FFF4F0"));
		style.setLineBgColour(Arrays.asList("#FFFFFF", "#FF9F7C"));

		// Create a new BookingMessageCardData object and set its properties
		data.setHeader("goTribe2 Member benefits");
		data.setMessage("Book & get <b>2.5% cashback</b> on this booking");
		data.setIconUrl("https://gos3.ibcdn.com/gotribe2_orange-1713785360.png");
		data.setGoCashIconUrl("https://gos3.ibcdn.com/gocash-1672205100.png");
		data.setStyle(style);

		// Create a new BookingMessageCard object and set its properties
		card.setCardId("REVIEW_GO_CASH_GOTRIBE1");
		card.setTemplateID("review_gocash_card");
		card.setData(data);

		// Add card to loyaltyMessageResponse
		loyaltyMessageResponse.setCards(Arrays.asList(card));

		LoyaltyMessageResponse footerMessage = new LoyaltyMessageResponse();
		Map<String, String> expData = new HashMap<>();
		BlackInfo binfo = new BlackInfo();


		expData.put("goTribe3", "true");
		// Set up the necessary behavior for the mock objects
		// This will depend on what behavior you want to test for the footerMessage object

		// Act
		LoyaltyMessageResponse footerResp = commonResponseTransformer.buildFooterMessageFromNewLoyaltyResponse(loyaltyMessageResponse, expData, binfo, footerMessage);

		// Assert
		// Add your assertions here based on what you expect the method to do with the footerMessage object
		// For example, if you expect the method to modify the footerMessage object, you can check its state here.
		Assert.assertEquals("goTribe2 Member benefits", footerResp.getMessageTitle());
		Assert.assertEquals("Book & get <b>2.5% cashback</b> on this booking", footerResp.getMessageText());
		Assert.assertEquals("https://gos3.ibcdn.com/gotribe2_orange-1713785360.png", footerResp.getIconUrlV2());
	}

	@Test
	public void testBuildFooterMessageForOldAppFromNewLoyaltyResponse() {
		// Arrange
		CommonResponseTransformer commonResponseTransformer = new CommonResponseTransformer();
		final LoyaltyMessageResponse loyaltyMessageResponse = new LoyaltyMessageResponse();
		loyaltyMessageResponse.setWalletEarn(0.0);
		loyaltyMessageResponse.setLoyaltyEligible(false);
		loyaltyMessageResponse.setTierName("tierName");
		loyaltyMessageResponse.setMessageHeader("messageHeader");
		loyaltyMessageResponse.setMessageTitle("messageTitle");
		loyaltyMessageResponse.setMessageText("messageText");
		loyaltyMessageResponse.setIconUrl("iconUrl");
		loyaltyMessageResponse.setCta("cta");
		loyaltyMessageResponse.setCtaLink("ctaLink");
		loyaltyMessageResponse.setCtaActionType("ctaActionType");
		loyaltyMessageResponse.setTextList("textList");
		loyaltyMessageResponse.setTnc("tnc");
		//when(goCashBusinessProcessorUnderTest.getLoyaltyMessage(null, 0.0, 0.0)).thenReturn(loyaltyMessageResponse);
		// Assuming Card is a separate class and has a setter method for cardId and templateId
		BookingMessageCard card = new BookingMessageCard();
		card.setCardId("ALL_DISPLAY_META_CARD");
		card.setTemplateID("gi_all_display_meta_card");

		// Assuming Style is a separate class and has setter methods for all its fields
		BookingMessageCardDataStyle style = new BookingMessageCardDataStyle();


		// Assuming Data is a separate class and has a setter method for style
		BookingMessageCardData data = new BookingMessageCardData();
		data.setStyle(style);

		// Set data to card
		card.setData(data);

		style.setBgColorGradient(Arrays.asList("#FFF4F0"));
		style.setLineBgColour(Arrays.asList("#FFFFFF", "#FF9F7C"));

		// Create a new BookingMessageCardData object and set its properties
		data.setHeader("goTribe2 Member benefits");
		data.setMessage("Book & get <b>2.5% cashback</b> on this booking");
		data.setIconUrl("https://gos3.ibcdn.com/gotribe2_orange-1713785360.png");
		data.setGoCashIconUrl("https://gos3.ibcdn.com/gocash-1672205100.png");
		data.setStyle(style);

		// Create a new BookingMessageCard object and set its properties
		card.setCardId("REVIEW_GO_CASH_GOTRIBE1");
		card.setTemplateID("review_gocash_card");
		card.setData(data);

		// Add card to loyaltyMessageResponse
		loyaltyMessageResponse.setCards(Arrays.asList(card));

		LoyaltyMessageResponse footerMessage = new LoyaltyMessageResponse();
		Map<String, String> expData = new HashMap<>();
		BlackInfo binfo = new BlackInfo();
		binfo.setOldFooterStripIconUrl("old footer icon url");

		expData.put("goTribe3", "false");
		// Set up the necessary behavior for the mock objects
		// This will depend on what behavior you want to test for the footerMessage object

		// Act
		LoyaltyMessageResponse footerResp = commonResponseTransformer.buildFooterMessageFromNewLoyaltyResponse(loyaltyMessageResponse, expData, binfo, footerMessage);

		// Assert
		// Add your assertions here based on what you expect the method to do with the footerMessage object
		// For example, if you expect the method to modify the footerMessage object, you can check its state here.
		Assert.assertEquals("goTribe2 Member benefits", footerResp.getMessageTitle());
		Assert.assertEquals("Book & get 2.5% cashback on this booking", footerResp.getMessageText());
		Assert.assertEquals("#fdddd3", footerResp.getIconColor());
		Assert.assertEquals("old footer icon url", footerResp.getIconUrl());
	}
	@Test
	public void testBuildCardInfo() {
		com.mmt.hotels.pojo.listing.personalization.CardData cardData = new CardData();
		List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataList = new ArrayList<>();
		cardDataList.add(cardData);
		com.mmt.hotels.pojo.listing.personalization.CardAction cardAction = new CardAction();
		List<com.mmt.hotels.pojo.listing.personalization.CardAction> cardActionList = new ArrayList<>();
		cardActionList.add(cardAction);
		cardData.setCardAction(cardActionList);
		CardInfo cardInfo = commonResponseTransformer.buildCardInfo(cardDataList, "DESKTOP",new LinkedHashMap<>(), null);
		Assert.assertNotNull(cardInfo);
	}

	@Test
	public void testGetHotelRatingSummary() {
		TravellerRatingSummaryDTO travellerRatingSummaryDTO = new TravellerRatingSummaryDTO();
		List<ConceptSummaryDTO> hotelSummaryList = new ArrayList<>();
		ConceptSummaryDTO conceptSummaryDTO = new ConceptSummaryDTO();
		SubConceptDTO subConcept = new SubConceptDTO();
		List<SubConceptDTO> subConcepts = new ArrayList<>();
		subConcepts.add(subConcept);
		conceptSummaryDTO.setSubConcepts(subConcepts);
		hotelSummaryList.add(conceptSummaryDTO);
		travellerRatingSummaryDTO.setHotelSummary(hotelSummaryList);
		List<ConceptSummary> resp = commonResponseTransformer.getHotelRatingSummary(travellerRatingSummaryDTO);
		Assert.assertNotNull(resp);
	}

	@Test
	public void testBuildResponseForBooking() {
		com.mmt.hotels.model.response.corporate.ReasonForBooking reasonForBooking = new ReasonForBooking();
		List<com.mmt.hotels.model.response.corporate.ReasonForBooking> reasons = new ArrayList<>();
		reasons.add(reasonForBooking);
		List<com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking> response = commonResponseTransformer.buildReasonForBooking(reasons);
		Assert.assertNotNull(response);

	}

	@Test
	public void testSetBnplNewVariantDetails() {
		String bnplNewVariantSubText = "Pay the remaining amount using any paymode any time before 19 Jun";
		BNPLDetails bnplDetails = new BNPLDetails();
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "setBnplNewVariantDetails", bnplNewVariantSubText, bnplDetails);
		Assert.assertNotNull(bnplDetails.getBnplNewVariantSubText());
		Assert.assertEquals(bnplNewVariantSubText, bnplDetails.getBnplNewVariantSubText());
	}

	@Test
	public void testBuildValueStaysData() {
		ValueStaysData valueStaysData = new ValueStaysData();
		valueStaysData.setStarText("star text");
		ValueStaysPersuasion valueStaysPersuasion = new ValueStaysPersuasion();
		valueStaysPersuasion.setText("text");
		valueStaysPersuasion.setIconUrl("iconUrl");
		valueStaysPersuasion.setIconType("iconType");
		valueStaysData.setValueStaysPersuasion(valueStaysPersuasion);
		ValueStaysDataCG valueStaysDataCG = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildValueStaysData", valueStaysData);
		Assert.assertNotNull(valueStaysDataCG);
	}

	@Test
	public void buildChildAttributesCgFromHesTest(){
		List<com.mmt.model.AttributesFacility> attributesFacilityList = new ArrayList<>();
		AttributesFacility attributesFacility = new AttributesFacility();
		attributesFacility.setName("test");
		attributesFacilityList.add(attributesFacility);
		commonResponseTransformer.buildChildAttributesCgFromHes(attributesFacilityList);
	}

	@Test
	public void enableDiscountTest(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setTotalSaving(201);
		displayPriceBreakDown.setDisplayPrice(200);
		commonResponseTransformer.enableDiscount(displayPriceBreakDown);
	}

	@Test
	public void buildHighlightedAmenitiesTest(){
		Set<String> highlights = new HashSet<>();
		highlights.add("Hello");
		highlights.add("hi");
		commonResponseTransformer.buildHighlightedAmenities(highlights);
	}

	@Test
	public void buildAmenitiesTest(){
		List<com.mmt.model.FacilityGroup> facilityWithGrp = new ArrayList<>();
		FacilityGroup facilityGroup = new FacilityGroup();
		facilityGroup.setName("Hello");
		List<Facility> facilities = new ArrayList<>();
		Facility facility = new Facility();
		facility.setName("Helloq");
		facilities.add(facility);
		facilityGroup.setFacilities(facilities);
		facilityWithGrp.add(facilityGroup);
		commonResponseTransformer.buildAmenities(facilityWithGrp,facilityWithGrp);
	}

	@Test
	public void buildPaymentDateTest_paymentDateHesNull() throws Exception {
		java.lang.reflect.Method method = commonResponseTransformer.getClass().getDeclaredMethod("buildPaymentDate", String.class);
		method.setAccessible(true);
		String paymentDate = (String) method.invoke(commonResponseTransformer, (Object) null);
		Assert.assertNull(paymentDate);
	}

	@Test
	public void buildPaymentDateTest_paymentDateHes_splitLengthZero() throws Exception {
		java.lang.reflect.Method method = commonResponseTransformer.getClass().getDeclaredMethod("buildPaymentDate", String.class);
		method.setAccessible(true);
		String paymentDate = (String) method.invoke(commonResponseTransformer, "");
		Assert.assertNull(paymentDate);
	}

	@Test
	public void buildPaymentDateTest_paymentDateHes_splitLengthNonZero() throws Exception {
		java.lang.reflect.Method method = commonResponseTransformer.getClass().getDeclaredMethod("buildPaymentDate", String.class);
		method.setAccessible(true);
		String paymentDate = (String) method.invoke(commonResponseTransformer, "26 June, 8:30 AM");
		Assert.assertEquals(paymentDate, "26 June");
	}

	@Test
	public void getBNPLDisabledReasonTest() {

		// Priority to be given to Active Booking Threshodl
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, true, true,true));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, true, true,false));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, true, false,true));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, true, false,false));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, false, true, true));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, false, false, true));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, false, true, false));


		assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, true, false,false));
		assertEquals(BNPLDisabledReason.INSURANCE_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, false, true,false));
		assertEquals(BNPLDisabledReason.GOCASH_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, false, false, true));

		//Positive
		assertEquals(null, commonResponseTransformer.getBNPLDisabledReason(false, false, false, false));

		//Combo
		assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_GOCASH_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, true, false, true));
		assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_INSURANCE_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, true, true, false));
		assertEquals(BNPLDisabledReason.GOCASH_INSURANCE_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, false, true, true));


	}

	@Test
	public void getPricingDetails_test_TCSAdded() {
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(1000);
		displayPriceBreakDown.setHotelTax(100);
		displayPriceBreakDown.setDisplayPrice(1100);
		displayPriceBreakDown.setHotelServiceCharge(10);
		displayPriceBreakDown.setMmtServiceCharge(10);
		displayPriceBreakDown.setAffiliateFee(1);
		displayPriceBreakDown.setWallet(10);
		displayPriceBreakDown.setMmtDiscount(40);
		displayPriceBreakDown.setSavingPerc(2.0);
		displayPriceBreakDown.setBlackDiscount(1);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);
		displayPriceBreakDown.setCdfDiscount(2);
		String countryCode = "AE";
		String payMode = "PAS";
		boolean isCorp = false;
		String segmentId = "1150";
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(ExperimentKeys.EXP_WALLET.getKey(), "3");
		boolean groupBookingFunnel = false;
		boolean cbrAvailable = false;
		LoyaltyMessageResponse loyaltyData = null;
		ClmPersuasion clmData = null;
		AmountDetail paidAmount = new AmountDetail();
		paidAmount.setAmount(1200.0);
		List<PricingDetails> list = commonResponseTransformer.getPricingDetails(displayPriceBreakDown,countryCode,payMode,isCorp,segmentId,expDataMap,groupBookingFunnel,cbrAvailable,loyaltyData,clmData,paidAmount,null, 0.0,0.0);
		Assert.assertNotNull(list);
		Assert.assertTrue(list.get(5).getLabel().equalsIgnoreCase("TCS Amount"));
	}

	@Test
	public void getPricingDetails_test_TCSNotAdded() {
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(1000);
		displayPriceBreakDown.setHotelTax(100);
		displayPriceBreakDown.setDisplayPrice(1100);
		displayPriceBreakDown.setHotelServiceCharge(10);
		displayPriceBreakDown.setMmtServiceCharge(10);
		displayPriceBreakDown.setAffiliateFee(1);
		displayPriceBreakDown.setWallet(10);
		displayPriceBreakDown.setMmtDiscount(40);
		displayPriceBreakDown.setSavingPerc(2.0);
		displayPriceBreakDown.setBlackDiscount(1);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);
		displayPriceBreakDown.setCdfDiscount(2);
		String countryCode = "AE";
		String payMode = "PAS";
		boolean isCorp = false;
		String segmentId = "1150";
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(ExperimentKeys.EXP_WALLET.getKey(), "3");
		boolean groupBookingFunnel = false;
		boolean cbrAvailable = false;
		LoyaltyMessageResponse loyaltyData = null;
		ClmPersuasion clmData = null;
		AmountDetail paidAmount = new AmountDetail();
		paidAmount.setAmount(1100.0);
		List<PricingDetails> list = commonResponseTransformer.getPricingDetails(displayPriceBreakDown,countryCode,payMode,isCorp,segmentId,expDataMap,groupBookingFunnel,cbrAvailable,loyaltyData,clmData,paidAmount,null, 0.0,0.0);
		Assert.assertNotNull(list);
		Assert.assertFalse(list.get(5).getLabel().equalsIgnoreCase("TCS Amount"));
	}

	@Test
	public void prepareUpgradeInfoTest() {
		HotelRates hotelRates = new HotelRates();
		UpgradeInfo upgradeInfo = new UpgradeInfo();
		RoomInfo selectedRoomInfo = new RoomInfo();
		selectedRoomInfo.setRoomSize("100");
		selectedRoomInfo.setRoomSizeUnit("sq.ft");
		selectedRoomInfo.setBedType("Double Bed");
		selectedRoomInfo.setBeds(new ArrayList<>());
		selectedRoomInfo.getBeds().add(new SleepingArrangement());
		selectedRoomInfo.getBeds().get(0).setType("Double Bed");
		selectedRoomInfo.setRoomViewName("City View");
		selectedRoomInfo.setRoomName("ECONOMIC ROOM");
		upgradeInfo.setRoomInfo(selectedRoomInfo);
		hotelRates.setUpgradeInfo(upgradeInfo);
		//Null checks
		RateplansUpgrade rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(hotelRates, null, 0, new BlackBenefits(), "");
		Assert.assertNotNull(rateplansUpgrade);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().size(), 1);
		Assert.assertNotNull(rateplansUpgrade.getSelectedRateplans());
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().size(), 1);

		TotalPricing totalPricing = new TotalPricing();
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(hotelRates, totalPricing, 0, new BlackBenefits(), "");
		Assert.assertNotNull(rateplansUpgrade);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().size(), 1);
		Assert.assertNotNull(rateplansUpgrade.getSelectedRateplans());
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().size(), 1);

		polyglotService.getTranslatedData(PER_NIGHT_TEXT);

		//Pricing Tests
		totalPricing.setNoCouponText("NO_COUPON_TEXT");
		totalPricing.setCouponSubtext("COUPON_SUB_TEXT");
		totalPricing.setCurrency("INR");
		totalPricing.setCouponAmount(100.0);
		totalPricing.setGroupPriceText("GROUP_PRICE_TEXT");
		totalPricing.setSavingsText("SAVING_TEXT");
		totalPricing.setDetails(new ArrayList<>());
		PricingDetails pricingDetails = new PricingDetails();
		pricingDetails.setKey(TAXES_KEY);
		pricingDetails.setAmount(90.0);
		totalPricing.getDetails().add(pricingDetails);
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(hotelRates, totalPricing, 0, new BlackBenefits(), "");
		Assert.assertNotNull(rateplansUpgrade);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertNotNull(rateplansUpgrade.getSelectedRateplans());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().size(), 1);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0));
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getNoCouponText(), "NO_COUPON_TEXT");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getCouponSubtext(), "COUPON_SUB_TEXT");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getCurrency(), "INR");
		Assert.assertTrue(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getCouponAmount() == 100.0);
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getGroupPriceText(), "GROUP_PRICE_TEXT");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getSavingsText(), "SAVING_TEXT");

		//Room Info Tests
		hotelRates = new HotelRates();
		upgradeInfo = new UpgradeInfo();
		selectedRoomInfo = new RoomInfo();
		selectedRoomInfo.setRoomSize("100");
		selectedRoomInfo.setRoomSizeUnit("sq.ft");
		selectedRoomInfo.setBedType("Double Bed");
		selectedRoomInfo.setBeds(new ArrayList<>());
		selectedRoomInfo.getBeds().add(new SleepingArrangement());
		selectedRoomInfo.getBeds().get(0).setType("Double Bed");
		selectedRoomInfo.setRoomViewName("City View");
		selectedRoomInfo.setRoomName("ECONOMIC ROOM");
		RoomInfo upgradedRoomInfo = new RoomInfo();
		upgradedRoomInfo.setRoomSize("120");
		upgradedRoomInfo.setRoomSizeUnit("sq.ft");
		upgradedRoomInfo.setBedType("King Bed");
		upgradedRoomInfo.setBeds(new ArrayList<>());
		upgradedRoomInfo.getBeds().add(new SleepingArrangement());
		upgradedRoomInfo.getBeds().get(0).setType("King Bed");
		upgradedRoomInfo.setRoomViewName("Lake View");
		upgradedRoomInfo.setRoomName("DELUXE ROOM");
		upgradeInfo.setRoomInfo(upgradedRoomInfo);
		hotelRates.setUpgradeInfo(upgradeInfo);
		hotelRates.setRoomInfo(new HashMap<>());
		hotelRates.getRoomInfo().put("test_room", selectedRoomInfo);
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(hotelRates, totalPricing, 0, new BlackBenefits(), "");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getRoomName(), "DELUXE ROOM");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getRoomDesc(), "120 sq.ft | King Bed | Lake View");
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().get(0).getRoomName(), "ECONOMIC ROOM");
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().get(0).getRoomDesc(), "100 sq.ft | Double Bed | City View");

		//Inclusion Tests
		Inclusion inclusion = new Inclusion();
		inclusion.setCode("300");
		inclusion.setValue("Room Upgraded");
		inclusion.setIconUrl("Test Icon");
		upgradeInfo.setInclusions(new ArrayList<>());
		upgradeInfo.getInclusions().add(inclusion);
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(hotelRates, totalPricing, 0, new BlackBenefits(), "");
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertTrue(rateplansUpgrade.getUpgradedRateplans().size() > 0);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList());
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().get(0));
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().get(0).getText(), "Room Upgraded");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().get(0).getIconUrl(), "Test Icon");
	}

	@Test
	public void prepareTagInfoTest() {
		Mockito.when(polyglotService.getTranslatedData(ROOM_UPGRADED_TAG_TITLE)).thenReturn("Room Upgraded");
		TagInfo tagInfo = commonResponseTransformer.prepareTagInfo(null);
		Assert.assertNull(tagInfo);
		tagInfo = commonResponseTransformer.prepareTagInfo(new BlackBenefits());
		Assert.assertNull(tagInfo);
		BlackBenefits blackBenefits = new BlackBenefits();
		blackBenefits.setRoomUpgrade(true);
		tagInfo = commonResponseTransformer.prepareTagInfo(blackBenefits);
		Assert.assertNotNull(tagInfo);
		Assert.assertNotNull(tagInfo.getTitle());
		Assert.assertEquals(tagInfo.getTitle(), "Room Upgraded");
	}

	@Test
	public void testAddBusinessIdentificationCard() {
		com.mmt.hotels.clientgateway.response.moblanding.CardData result = commonResponseTransformer.addBusinessIdentificationCard(0, false);
		Assert.assertNotNull(result);
	}

	@Test
	public void testBuildBusinessIdentificationCards_emptyAffiliateId() {
		com.mmt.hotels.clientgateway.response.moblanding.CardData result = commonResponseTransformer.buildBusinessIdentificationCards(new HotelRates(), "");
		Assert.assertNull(result);
	}

	@Test
	public void testBuildBusinessIdentificationCards_nullHotelRates() {
		com.mmt.hotels.clientgateway.response.moblanding.CardData result = commonResponseTransformer.buildBusinessIdentificationCards(null, "affiliateId");
		Assert.assertNull(result);
	}

	@Test
	public void testBuildBusinessIdentificationCards_validInput() {
		HotelRates hotelRates = new HotelRates();
		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
		RoomType roomType = new RoomType();
		com.mmt.hotels.model.response.pricing.RatePlan ratePlan = new com.mmt.hotels.model.response.pricing.RatePlan();
		DisplayFare displayFare = new DisplayFare();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setSavingPerc(10.0);
		displayFare.setDisplayPriceBreakDown(displayPriceBreakDown);
		ratePlan.setDisplayFare(displayFare);
		if(MapUtils.isEmpty(roomType.getRatePlanList())){
			roomType.setRatePlanList(new HashMap<>());
		}
		roomType.getRatePlanList().put("ratePlan", ratePlan);
		if(MapUtils.isEmpty(roomTypeDetails.getRoomType())){
			roomTypeDetails.setRoomType(new HashMap<>());
		}
		roomTypeDetails.getRoomType().put("roomType", roomType);
		hotelRates.setRoomTypeDetails(roomTypeDetails);
		Mockito.when(utility.isDetailPageAPI(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()))).thenReturn(true);
		Mockito.when(utility.isBusinessIdentificationApplicable(Mockito.anyString(), Mockito.any())).thenReturn(true);
		com.mmt.hotels.clientgateway.response.moblanding.CardData result = commonResponseTransformer.buildBusinessIdentificationCards(hotelRates, "affiliateId");
		assertNotNull(result);
	}

	@Test
	public void buildInsuranceAddOnData_test() throws JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		InsuranceData dataFromHES = new InsuranceData();
		List<TmInsuranceAddOns> addonsList = new ArrayList<>();
		TmInsuranceAddOns tminsaddon = new TmInsuranceAddOns();
		addonsList.add(tminsaddon);
		dataFromHES.setTmInsuranceAddOns(addonsList);
		com.mmt.hotels.model.response.addon.WidgetData insuarnceWidgetDataHES = new WidgetData();
		String jsonString = "{\"name\":\"John\", \"age\":30, \"city\":\"New York\"}";
		JsonNode ui = mapper.readTree(jsonString);
		insuarnceWidgetDataHES.setUi(ui);
		JsonNode data = mapper.readTree(jsonString);
		insuarnceWidgetDataHES.setData(data);
		InsuranceAddOnData insuranceAddOnData = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildInsuranceAddOnData", dataFromHES, insuarnceWidgetDataHES);
		Assert.assertNotNull(insuranceAddOnData.getWidgetData().getData());
		Assert.assertNotNull(insuranceAddOnData.getWidgetData().getUi());
	}

	@Test
	public void buildInsuranceAddOnData_testFailedCase() {
		InsuranceData dataFromHES = new InsuranceData();
		List<TmInsuranceAddOns> addonsList = new ArrayList<>();
		TmInsuranceAddOns tminsaddon = new TmInsuranceAddOns();
		addonsList.add(tminsaddon);
		dataFromHES.setTmInsuranceAddOns(addonsList);
		com.mmt.hotels.model.response.addon.WidgetData insuarnceWidgetDataHES = new WidgetData();
		insuarnceWidgetDataHES.setUi(null);
		insuarnceWidgetDataHES.setData(null);
		InsuranceAddOnData insuranceAddOnData = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildInsuranceAddOnData", dataFromHES, insuarnceWidgetDataHES);
		Assert.assertNotNull(insuranceAddOnData);
		Assert.assertNull(insuranceAddOnData.getWidgetData());
	}

	@Test
	public void testUpdateOriginalPriceTextInUpgradedRateplan() {
		List<RatePlanDetails> upgradedRateplans = new ArrayList<>();
		RatePlanDetails upgradedRateplan = new RatePlanDetails();
		TotalPricing totalPriceMap = new TotalPricing();
		upgradedRateplan.setPriceMap(totalPriceMap);
		upgradedRateplans.add(upgradedRateplan);
		UpgradedRateplanOriginalPriceDetails upgradeRateplanOriginalPriceDetails = new UpgradedRateplanOriginalPriceDetails();
		upgradeRateplanOriginalPriceDetails.setCurrency("INR");
		upgradeRateplanOriginalPriceDetails.setDisplayPrice(43233);
		upgradeRateplanOriginalPriceDetails.setTaxes(123);
		Mockito.when(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_ORIGINAL_PRICE_GI)).thenReturn("Original Price: <value>");
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "updateOriginalPriceTextInUpgradedRateplan",
				upgradedRateplans, upgradeRateplanOriginalPriceDetails, 2);
		Assert.assertNotNull(upgradedRateplans.get(0).getPriceMap().getOriginalPriceMsg());
	}

	@Test
	public void testBuildMobLandingFilters_NullFiltersHES() {
		com.mmt.hotels.model.response.listpersonalization.Filters result = commonResponseTransformer.buildMobLandingFilters(null, filterConfiguration);
		assertNull(result);
	}

	@Test
	public void testBuildMobLandingFilters_EmptyFilterList() {
		com.mmt.hotels.model.response.listpersonalization.Filters filtersHES = new com.mmt.hotels.model.response.listpersonalization.Filters();
		filtersHES.setFilterList(new LinkedHashMap<>());
		com.mmt.hotels.model.response.listpersonalization.Filters result = commonResponseTransformer.buildMobLandingFilters(filtersHES, filterConfiguration);
		assertTrue(result.getFilterList().isEmpty());
	}

	@Test
	public void testUpdateFilters_NullFilters() {
		assertDoesNotThrow(() -> commonResponseTransformer.updateFilters(null, new LinkedHashMap<>()));
	}

	@Test
	public void testUpdateFilters_NullConfig() {
		LinkedHashSet<Filter> filters = new LinkedHashSet<>();
		filters.add(new Filter());
		commonResponseTransformer.updateFilters(filters, null);
		assertEquals(1, filters.size());
		assertNotNull(filters.iterator().next());
	}

	@Test
	public void testBuildCardInfoArray() {
		com.mmt.hotels.pojo.listing.personalization.CardData cardData = new com.mmt.hotels.pojo.listing.personalization.CardData();
		com.mmt.hotels.pojo.listing.personalization.CardData cardData1 = new com.mmt.hotels.pojo.listing.personalization.CardData();
		List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataList = new ArrayList<>();
		cardDataList.add(cardData);
		cardData.setTemplateId("multifilter_corousal");
		cardDataList.add(cardData1);
		cardData1.setTemplateId("multifilter_corousal");
		com.mmt.hotels.pojo.listing.personalization.CardAction cardAction = new com.mmt.hotels.pojo.listing.personalization.CardAction();
		List<com.mmt.hotels.pojo.listing.personalization.CardAction> cardActionList = new ArrayList<>();
		cardActionList.add(cardAction);
		cardData.setCardAction(cardActionList);
		cardData1.setCardAction(cardActionList);
		LinkedHashMap<String, String> multifilterPokus = new LinkedHashMap<>();
		multifilterPokus.put(Constants.MULTIFILTER_COROUSAL, "true");
		CardInfo cardInfo = commonResponseTransformer.buildCardInfo(cardDataList, "DESKTOP",multifilterPokus, filterConfiguration);

		Assert.assertNotNull(cardInfo);
		Assert.assertNotNull(cardInfo.getCardInfoArray());
		Assert.assertEquals(2, cardInfo.getCardInfoArray().size());
	}

	@Test
	public void testCheckIfRuleDuplicate() {
		List<Rules> rulesList = new ArrayList<>();
		Rules rule1 = new Rules();
		rule1.setTitle("Test Rule 1");
		Rules rule2 = new Rules();
		rule2.setTitle("Test Rule 2");
		rulesList.add(rule1);
		rulesList.add(rule2);

		boolean result1 = commonResponseTransformer.checkIfRuleDuplicate(rulesList, "Test Rule 1");
		assertTrue(result1, "Should return true for duplicate rule");

		boolean result2 = commonResponseTransformer.checkIfRuleDuplicate(rulesList, "Test Rule 3");
		assertFalse(result2, "Should return false for non-duplicate rule");
	}

	@Test
	public void testIsSupplierExcluded() {
		ReflectionTestUtils.setField(commonResponseTransformer, "intlNrSupplierExclusionList", Arrays.asList("SUPP1", "SUPP2"));

		boolean result1 = commonResponseTransformer.isSupplierExcluded("SUPP1");
		assertTrue(result1, "Should return true for excluded supplier");

		boolean result2 = commonResponseTransformer.isSupplierExcluded("SUPP3");
		assertFalse(result2, "Should return false for non-excluded supplier");

		boolean result3 = commonResponseTransformer.isSupplierExcluded(null);
		assertFalse(result3, "Should return false for null supplier code");
	}

	@Test
	public void testGetManthanGocashValue() {
		RoomTypeDetails roomDetails = new RoomTypeDetails();
		DisplayFare totalDisplayFare = new DisplayFare();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		BestCoupon couponInfo = new BestCoupon();
		Map<String, Double> hybridDiscounts = new HashMap<>();
		hybridDiscounts.put(Constants.GOCASH_KEY, 100.0);
		couponInfo.setHybridDiscounts(hybridDiscounts);
		displayPriceBreakDown.setCouponInfo(couponInfo);
		totalDisplayFare.setDisplayPriceBreakDown(displayPriceBreakDown);
		roomDetails.setTotalDisplayFare(totalDisplayFare);

		int result1 = commonResponseTransformer.getManthanGocashValue(roomDetails);
		assertEquals(100, result1);

		roomDetails.setTotalDisplayFare(null);
		int result2 = commonResponseTransformer.getManthanGocashValue(roomDetails);
		assertEquals(0, result2);

		int result3 = commonResponseTransformer.getManthanGocashValue(null);
		assertEquals(0, result3);
	}

	@Test
	public void testGetLoyaltyInstantDiscount() {
		RoomTypeDetails roomDetails = new RoomTypeDetails();
		DisplayFare totalDisplayFare = new DisplayFare();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBlackDiscount(200.0);
		totalDisplayFare.setDisplayPriceBreakDown(displayPriceBreakDown);
		roomDetails.setTotalDisplayFare(totalDisplayFare);

		int result1 = commonResponseTransformer.getLoyaltyInstantDiscount(roomDetails);
		assertEquals(200, result1);

		roomDetails.setTotalDisplayFare(null);
		int result2 = commonResponseTransformer.getLoyaltyInstantDiscount(roomDetails);
		assertEquals(0, result2);

		int result3 = commonResponseTransformer.getLoyaltyInstantDiscount(null);
		assertEquals(0, result3);
	}

	@Test
	public void testGetBookingMessageCardById() {
		LoyaltyMessageResponse loyaltyMessageResponse = new LoyaltyMessageResponse();
		List<BookingMessageCard> cards = new ArrayList<>();

		BookingMessageCard card1 = new BookingMessageCard();
		card1.setCardId("CARD1");
		cards.add(card1);

		BookingMessageCard card2 = new BookingMessageCard();
		card2.setCardId("CARD2");
		cards.add(card2);

		loyaltyMessageResponse.setCards(cards);

		BookingMessageCard result1 = commonResponseTransformer.getBookingMessageCardById(loyaltyMessageResponse, "CARD1");
		assertNotNull(result1);
		assertEquals("Should return correct card","CARD1", result1.getCardId());

		BookingMessageCard result2 = commonResponseTransformer.getBookingMessageCardById(loyaltyMessageResponse, "CARD3");
		assertNull(result2, "Should return null for non-existing card ID");

		BookingMessageCard result3 = commonResponseTransformer.getBookingMessageCardById(null, "CARD1");
		assertNull(result3, "Should return null for null loyalty message response");
	}

	@Test
	public void testCheckBnplApplicability() {
		boolean result1 = commonResponseTransformer.checkBnplApplicability(true, true, true);
		assertTrue(result1, "Should return true when all conditions are true");
	}

	@Test
	public void testShowInDisabledState() {
		boolean result1 = commonResponseTransformer.showInDisabledState(false, true, BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD);
		assertTrue(result1, "Should return true when conditions match for disabled state");

		boolean result2 = commonResponseTransformer.showInDisabledState(true, true, null);
		assertFalse(result2, "Should return false when bnplApplicable is true");
	}

	@Test
	public void testCheckIfMandatoryChargesTooHigh() {
		boolean result1 = commonResponseTransformer.checkIfMandatoryChargesTooHigh(1000.0, 5000.0);
		assertFalse(result1, "Should return false when charges are within limit");

		boolean result3 = commonResponseTransformer.checkIfMandatoryChargesTooHigh(0.0, 5000.0);
		assertFalse(result3, "Should return false when charges are zero");

		boolean result4 = commonResponseTransformer.checkIfMandatoryChargesTooHigh(100.0, 0.0);
		assertTrue(result4, "Should return true when booking amount is zero");
	}

	@Test
	public void testCheckIfInvalidCoupon() {
		TotalPricing totalPricing = new TotalPricing();
		List<PricingDetails> pricingDetails = new ArrayList<>();

		PricingDetails detail1 = new PricingDetails();
		detail1.setKey("1234");
		detail1.setAmount(50.0);
		pricingDetails.add(detail1);

		totalPricing.setDetails(pricingDetails);

		boolean result1 = commonResponseTransformer.checkIfInvalidCoupon(totalPricing);
		assertFalse(result1, "Should return true when invalid coupon exists with non-zero amount");

		detail1.setAmount(0.0);
		boolean result2 = commonResponseTransformer.checkIfInvalidCoupon(totalPricing);
		assertFalse(result2, "Should return false when invalid coupon exists with zero amount");

		totalPricing.setDetails(new ArrayList<>());
		boolean result3 = commonResponseTransformer.checkIfInvalidCoupon(totalPricing);
		assertFalse(result3, "Should return false when no pricing details exist");

		boolean result4 = commonResponseTransformer.checkIfInvalidCoupon(null);
		assertFalse(result4, "Should return false when total pricing is null");
	}

	// ======================
	// Tests for New Methods Added
	// ======================

	@Test
	public void testGetLongStayBenefitPersuasion_Null() {
		PersuasionResponse result = commonResponseTransformer.getLongStayBenefitPersuasion(null);
		assertNull(result);
	}

	@Test
	public void testGetLongStayBenefitPersuasion_Valid() {
		LongStayBenefits longStayBenefits = new LongStayBenefits();
		longStayBenefits.setTitle("Long Stay Offer");
		longStayBenefits.setTitleColor("#007E7D");
		
		List<Inclusion> inclusionsList = new ArrayList<>();
		Inclusion losInclusion = new Inclusion();
		losInclusion.setInclusionType("LOS");
		losInclusion.setCode("Save 25% on extended stays");
		inclusionsList.add(losInclusion);
		
		longStayBenefits.setInclusionsList(inclusionsList);

		PersuasionResponse result = commonResponseTransformer.getLongStayBenefitPersuasion(longStayBenefits);

		assertNotNull(result);
		assertEquals("Long Stay Offer", result.getTitle());
		assertEquals("#007E7D", result.getTitleColor());
		assertNotNull(result.getPersuasions());
		assertEquals(1, result.getPersuasions().size());
	}

	@Test
	public void testBuildTajGiftCardOrHotelCreditPersuasion_Valid() {
		HotelBenefitInfo hotelBenefitInfo = new HotelBenefitInfo();
		hotelBenefitInfo.setTitle("Taj Gift Card");
		hotelBenefitInfo.setBenefitType("GIFT_CARD");
		hotelBenefitInfo.setText("Get ₹500 gift card on booking");
		hotelBenefitInfo.setIconUrl("https://example.com/gift-card-icon.png");

		ReflectionTestUtils.setField(commonResponseTransformer, "utility", utility);
		when(utility.isReviewPageAPI(Mockito.anyString())).thenReturn(false);

		PersuasionResponse result = commonResponseTransformer.buildTajGiftCardOrHotelCreditPersuasion(hotelBenefitInfo);

		assertNotNull(result);
		assertEquals("Taj Gift Card", result.getTitle());
		assertEquals("#000000", result.getTitleColor());
		assertEquals("GIFT_CARD", result.getPersuasionType());
		assertEquals("Get ₹500 gift card on booking", result.getPersuasionText());
		assertEquals("https://example.com/gift-card-icon.png", result.getIconUrl());
		Assert.assertTrue(result.isHtml());
		
		assertNotNull(result.getStyle());
		assertEquals("#4a4a4a", result.getStyle().getTextColor());
//		assertEquals(37, result.getStyle().getIconHeight());
//		assertEquals(41, result.getStyle().getIconWidth());
	}

	@Test
	public void testBuildTajGiftCardOrHotelCreditPersuasion_ReviewPage() {
		HotelBenefitInfo hotelBenefitInfo = new HotelBenefitInfo();
		hotelBenefitInfo.setTitle("Hotel Credit");
		hotelBenefitInfo.setBenefitType("HOTEL_CREDIT");
		hotelBenefitInfo.setText("Get ₹1000 hotel credit");
		hotelBenefitInfo.setIconUrl("https://example.com/hotel-credit-icon.png");

		ReflectionTestUtils.setField(commonResponseTransformer, "utility", utility);
		when(utility.isReviewPageAPI(Mockito.anyString())).thenReturn(true);

		PersuasionResponse result = commonResponseTransformer.buildTajGiftCardOrHotelCreditPersuasion(hotelBenefitInfo);

		assertNotNull(result);
		assertNull(result.getTitle());
		assertEquals("#000000", result.getTitleColor());
		assertEquals("HOTEL_CREDIT", result.getPersuasionType());
		assertEquals("Get ₹1000 hotel credit", result.getPersuasionText());
		assertEquals("https://example.com/hotel-credit-icon.png", result.getIconUrl());
		Assert.assertTrue(result.isHtml());
	}

}
