package com.mmt.hotels.clientgateway.transformer.response;


import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class UpdatedPriceResponseTransformerPWATest {

    @InjectMocks
    UpdatedPriceResponseTransformer updatedPriceResponseTransformerPWA;

    @Spy
    DateUtil dateUtil;

    @Test(expected = Exception.class)
    public void testConvertUpdatedPriceResponse(){

        UpdatePriceRequest request = new UpdatePriceRequest();
        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setFunnelSource("Hotels");
        request.setSearchCriteria(new UpdatedPriceCriteria());
        request.getSearchCriteria().setCheckIn("2021-08-08");
        request.getSearchCriteria().setCheckOut("2021-08-18");
        request.getSearchCriteria().setRoomCriteria(new ArrayList<>());
        request.getSearchCriteria().getRoomCriteria().add(new UpdatedPriceRoomCriteria());
        request.getSearchCriteria().getRoomCriteria().get(0).setRoomStayCandidates(new ArrayList<>());
        request.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().add(new RoomStayCandidate());
        request.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setAdultCount(2);
        request.getSearchCriteria().getRoomCriteria().get(0).setSellableType("Room");

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(1d);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(1d);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");


        PriceBreakDownResponse priceBreakDownResponseCB = new PriceBreakDownResponse.Builder().buildDisplayPriceBreakDown(displayPriceBreakDown).buildDisplayPriceBreakDownList(displayPriceBreakDownList).build();
        updatedPriceResponseTransformerPWA.convertUpdatedPriceResponse(priceBreakDownResponseCB, request,commonModifierResponse);


    }
}
