package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfoExtension;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Tag;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.RoomHighlightType;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SleepingArrangementRoomInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.StayDetail;
import com.mmt.hotels.clientgateway.response.rooms.StayTypeInfo;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.flyfish.TagData;
import com.mmt.hotels.model.response.pricing.ExtraGuestDetail;
import com.mmt.model.SleepingArrangement;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for RoomInfoHelper in GI project
 * Comprehensive test coverage following patterns from CG version but adapted for GI implementation
 */
@RunWith(MockitoJUnitRunner.class)
public class RoomInfoHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Utility utility;

    @InjectMocks
    private RoomInfoHelper roomInfoHelper;

    // Test constants
    private static final String BATHROOM_TEXT = "Bathroom";
    private static final String BATHROOMS_TEXT = "Bathrooms";
    private static final String ALTERNATE_BED_TYPE_OR_TEXT = "or";

    @Before
    public void setUp() {
        // Setup common polyglot service responses
        when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOM_TEXT))
                .thenReturn(BATHROOM_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOMS_TEXT))
                .thenReturn(BATHROOMS_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT))
                .thenReturn(ALTERNATE_BED_TYPE_OR_TEXT);
    }

    // ==================== getSleepingArrangements Tests (Static Method) ====================

    @Test
    public void should_ReturnSleepingArrangements_When_ValidRoomInfoWithBeds() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithBeds();

        // When
        List<SleepingArrangement> result = RoomInfoHelper.getSleepingArrangements(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        SleepingArrangement firstBed = result.get(0);
        assertEquals("Queen Bed", firstBed.getType());
        assertEquals(1, firstBed.getCount());
        
        SleepingArrangement secondBed = result.get(1);
        assertEquals("Sofa Bed", secondBed.getType());
        assertEquals(1, secondBed.getCount());
    }

    @Test
    public void should_ReturnNull_When_RoomInfoIsNull() {
        // When
        List<SleepingArrangement> result = RoomInfoHelper.getSleepingArrangements(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_RoomArrangementMapIsNull() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomArrangementMap(null);

        // When
        List<SleepingArrangement> result = RoomInfoHelper.getSleepingArrangements(roomInfo);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_RoomArrangementMapIsEmpty() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomArrangementMap(new HashMap<>());

        // When
        List<SleepingArrangement> result = RoomInfoHelper.getSleepingArrangements(roomInfo);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BedsListIsEmpty() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        arrangementMap.put("BEDS", new ArrayList<>());
        roomInfo.setRoomArrangementMap(arrangementMap);

        // When
        List<SleepingArrangement> result = RoomInfoHelper.getSleepingArrangements(roomInfo);

        // Then
        assertNull(result);
    }

    // ==================== transformRoomHighlights Tests ====================

    @Test
    public void should_ReturnEmptyList_When_RoomInfoIsNull() {
        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(null, null, false, false);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_BuildBathroomHighlights_When_RoomInfoHasBathrooms() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithBathrooms();

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight bathroomHighlight = result.get(0);
        assertEquals("2 " + BATHROOMS_TEXT, bathroomHighlight.getText());
        assertEquals(RoomHighlightType.BATHROOM_TYPE.name(), bathroomHighlight.getIdentifier());
        assertEquals(Integer.valueOf(6), bathroomHighlight.getSelectRoomRevampOrder());
    }

    @Test
    public void should_BuildSingleBathroomHighlight_When_OneBathroom() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithSingleBathroom();

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight bathroomHighlight = result.get(0);
        assertEquals("1 " + BATHROOM_TEXT, bathroomHighlight.getText());
        assertEquals(RoomHighlightType.BATHROOM_TYPE.name(), bathroomHighlight.getIdentifier());
    }

    @Test
    public void should_BuildBedHighlights_When_StandardBedInfo() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithStandardBeds();

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight bedHighlight = result.get(0);
        assertEquals("King Bed, 2 x Single Bed", bedHighlight.getText());
        assertEquals(RoomHighlightType.BED_TYPE.name(), bedHighlight.getIdentifier());
        assertEquals(Integer.valueOf(5), bedHighlight.getSelectRoomRevampOrder());
    }

    @Test
    public void should_BuildPilgrimageBedHighlights_When_PilgrimageEnabled() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithAlternateBeds();

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, true);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight bedHighlight = result.get(0);
        assertEquals("1 Queen Bed " + ALTERNATE_BED_TYPE_OR_TEXT + " 2 Single Bed", bedHighlight.getText());
        assertEquals(RoomHighlightType.BED_TYPE.name(), bedHighlight.getIdentifier());
    }

    @Test
    public void should_AddExtraBedSubText_When_ExtraGuestDetailProvided() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithStandardBeds();
        ExtraGuestDetail extraGuestDetail = new ExtraGuestDetail();
        extraGuestDetail.setRoomSelectionExtraBedText("Extra bed available for additional charge");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, extraGuestDetail, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight bedHighlight = result.get(0);
        assertEquals("Extra bed available for additional charge", bedHighlight.getSubText());
    }

    @Test
    public void should_HandleSingleGuestCount_When_MaxGuestCountIsOne() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithSingleGuest();

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight guestHighlight = result.get(0);
        assertEquals("Max 1 Guest", guestHighlight.getText());
        assertEquals(RoomHighlightType.GUEST_COUNT.name(), guestHighlight.getIdentifier());
    }

    @Test
    public void should_HandleMultipleGuestCount_When_MaxGuestCountIsGreaterThanOne() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaxGuestCount(4);

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight guestHighlight = result.get(0);
        assertEquals("Max 4 Guests", guestHighlight.getText());
        assertEquals("Max 4 Guests", guestHighlight.getDescription());
        assertEquals(RoomHighlightType.GUEST_COUNT.name(), guestHighlight.getIdentifier());
        assertEquals("https://gos3.ibcdn.com/paxBlackIcon-1678093500.png", guestHighlight.getIconUrl());
    }

    @Test
    public void should_HandleMultipleGuestCount_When_MaxGuestCountIsTwo() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaxGuestCount(2);

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight guestHighlight = result.get(0);
        assertEquals("Max 2 Guests", guestHighlight.getText());
        assertEquals("Max 2 Guests", guestHighlight.getDescription());
        assertEquals(RoomHighlightType.GUEST_COUNT.name(), guestHighlight.getIdentifier());
    }

    @Test
    public void should_BuildRoomSizeHighlight_When_RoomSizeInSquareMeters() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("30");
        roomInfo.setRoomSizeUnit("sq m");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight roomSizeHighlight = result.get(0);
        assertEquals("30 sq m", roomSizeHighlight.getText());
        assertEquals(RoomHighlightType.ROOM_SIZE.name(), roomSizeHighlight.getIdentifier());
        assertEquals(Integer.valueOf(3), roomSizeHighlight.getSelectRoomRevampOrder());
    }

    @Test
    public void should_NotBuildRoomSizeHighlight_When_RoomSizeIsNull() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize(null);
        roomInfo.setRoomSizeUnit("sq ft");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void should_NotBuildRoomSizeHighlight_When_RoomSizeIsEmpty() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("");
        roomInfo.setRoomSizeUnit("sq ft");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void should_HandleInvalidRoomSize_When_RoomSizeIsNotNumeric() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("invalid");
        roomInfo.setRoomSizeUnit("sq ft");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight roomSizeHighlight = result.get(0);
        assertEquals("invalid sq ft", roomSizeHighlight.getText()); // Should not have conversion due to parsing error
        assertEquals(RoomHighlightType.ROOM_SIZE.name(), roomSizeHighlight.getIdentifier());
    }

    @Test
    public void should_HandleInvalidRoomSize_When_RoomSizeIsNotNumericForSquareFeet() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("not-a-number");
        roomInfo.setRoomSizeUnit("sq ft");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight roomSizeHighlight = result.get(0);
        // Should only show the original text without conversion due to parsing exception
        assertEquals("not-a-number sq ft", roomSizeHighlight.getText());
        assertEquals(RoomHighlightType.ROOM_SIZE.name(), roomSizeHighlight.getIdentifier());
    }

    @Test
    public void should_HandleInvalidRoomSize_When_RoomSizeIsSpecialCharactersForSquareFeet() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("12.34.56"); // Invalid number format
        roomInfo.setRoomSizeUnit("sq ft");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight roomSizeHighlight = result.get(0);
        // Should only show the original text without conversion due to parsing exception
        assertEquals("12.34.56 sq ft", roomSizeHighlight.getText());
        assertEquals(RoomHighlightType.ROOM_SIZE.name(), roomSizeHighlight.getIdentifier());
    }

    @Test
    public void should_BuildRoomViewHighlight_When_RoomInfoHasRoomViewName() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomViewName("City View");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RoomHighlight roomViewHighlight = result.get(0);
        assertEquals("City View", roomViewHighlight.getText());
        assertEquals("City View", roomViewHighlight.getDescription());
        assertEquals(RoomHighlightType.ROOM_VIEW.name(), roomViewHighlight.getIdentifier());
        assertEquals(Integer.valueOf(4), roomViewHighlight.getSelectRoomRevampOrder());
        assertEquals("https://gos3.ibcdn.com/roomViewIcon-1678093525.png", roomViewHighlight.getIconUrl());
    }



    @Test
    public void should_NotBuildRoomViewHighlight_When_RoomViewNameIsNull() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomViewName(null);

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void should_NotBuildRoomViewHighlight_When_RoomViewNameIsEmpty() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomViewName("");

        // When
        List<RoomHighlight> result = roomInfoHelper.transformRoomHighlights(roomInfo, null, false, false);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    // ==================== buildStayDetails Tests ====================

    @Test
    public void should_BuildStayDetails_When_ValidHotelDetailsWithRooms() {
        // Given
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();
        HotelDetails hotelDetails = createHotelDetailsWithRooms();
        
        when(utility.createBedInfoTextFromBedInfoMap(any())).thenReturn("1 King Bed, 1 Sofa Bed");

        // When
        roomInfoHelper.buildStayDetails(searchRoomsResponse, roomInfo, 1, "Hotel", hotelDetails, false);

        // Then
        assertNotNull(roomInfo.getStayDetail());
        StayDetail stayDetail = roomInfo.getStayDetail();
        assertEquals(Integer.valueOf(2), stayDetail.getBed());
        assertEquals(Integer.valueOf(1), stayDetail.getBedRoom());
        assertEquals(Integer.valueOf(2), stayDetail.getMaxGuests());
        assertEquals(Integer.valueOf(2), stayDetail.getBaseGuests());
        assertEquals(Integer.valueOf(4), stayDetail.getMaxCapacity());
        assertEquals(Integer.valueOf(1), stayDetail.getBathroom());
        assertEquals("1 King Bed, 1 Sofa Bed", roomInfo.getBedInfoText());
    }

    @Test
    public void should_BuildStayDetails_When_ValidHotelDetailsWithRoomCombos() {
        // Given
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();
        HotelDetails hotelDetails = createHotelDetailsWithRoomCombos();
        
        when(utility.createBedInfoTextFromBedInfoMap(any())).thenReturn("2 King Bed");

        // When
        roomInfoHelper.buildStayDetails(searchRoomsResponse, roomInfo, 1, "Hotel", hotelDetails, false);

        // Then
        assertNotNull(roomInfo.getStayDetail());
        StayDetail stayDetail = roomInfo.getStayDetail();
        assertEquals(Integer.valueOf(2), stayDetail.getBed());
        assertEquals(Integer.valueOf(2), stayDetail.getBedRoom());
        assertEquals(Integer.valueOf(4), stayDetail.getMaxGuests());
        assertEquals(Integer.valueOf(4), stayDetail.getBaseGuests());
        assertEquals(Integer.valueOf(4), stayDetail.getMaxCapacity());
        assertEquals(Integer.valueOf(2), stayDetail.getBathroom());
    }

    @Test
    public void should_SetZeroBedRoom_When_HostelPropertyType() {
        // Given
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();
        HotelDetails hotelDetails = createHotelDetailsWithRooms();
        
        // When
        roomInfoHelper.buildStayDetails(searchRoomsResponse, roomInfo, 1, Constants.PROPERTY_TYPE_HOSTEL, hotelDetails, false);

        // Then
        assertNotNull(roomInfo.getStayDetail());
        assertEquals(Integer.valueOf(0), roomInfo.getStayDetail().getBedRoom());
    }


    // ==================== getSpaceData Tests ====================

    @Test
    public void should_ReturnNull_When_SpaceDataIsNull() {
        // When
        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            roomInfoHelper.getSpaceData(null, new CommonModifierResponse());

        // Then
        assertNull(result);
    }

    @Test
    public void should_TransformMedia_When_SpaceHasMedia() {
        // Given
        SpaceData orchSpaceData = createOrchSpaceDataWithMedia();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();

        // When
        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            roomInfoHelper.getSpaceData(orchSpaceData, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getSpaces());
        assertEquals(1, result.getSpaces().size());
        
        Space space = result.getSpaces().get(0);
        assertNotNull(space.getMedia());
        assertEquals(2, space.getMedia().size());
        
        MediaData mediaData1 = space.getMedia().get(0);
        assertEquals("IMAGE", mediaData1.getMediaType());
        assertEquals("https://example.com/image1.jpg", mediaData1.getUrl());
        
        MediaData mediaData2 = space.getMedia().get(1);
        assertEquals("VIDEO", mediaData2.getMediaType());
        assertEquals("https://example.com/video1.mp4", mediaData2.getUrl());
    }

    // ==================== buildRoomInfo Tests ====================

    @Test
    public void should_ReturnNull_When_HotelDetailsIsNull() {
        // When
        SleepingArrangementRoomInfo result = roomInfoHelper.buildRoomInfo(
            null, new SearchRoomsResponse(), new ArrayList<>(), "IN", false, 
            Pair.of(false, false), false, new HashMap<>());

        // Then
        assertNull(result);
    }

    @Test
    public void should_NotUpdateGuestRoomDetails_When_EmptyRooms() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(new ArrayList<>());
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        // When
        roomInfoHelper.updateGuestRoomDetails(hotelDetails, "IN", new HashMap<>(), roomInfo);

        // Then
        assertNull(roomInfo.getGuestRoomKey());
        assertNull(roomInfo.getGuestRoomValue());
        verify(utility, never()).getGuestRoomKeyValue(any(), any(), any());
    }

    // ==================== getFreeChildTextFromHotelDetails Tests ====================

    @Test
    public void should_ReturnNull_When_NoFreeChildTextFound() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithRooms();

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnFreeChildText_When_RoomComboHasFreeChildText() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithRoomCombosAndFreeChildText();

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertEquals("Children under 12 stay free", result);
    }

    @Test
    public void should_ReturnFreeChildText_When_RoomRatePlanHasFreeChildText() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithRoomsAndFreeChildText();

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertEquals("Up to 2 children stay free", result);
    }

    @Test
    public void should_ReturnFirstFreeChildText_When_MultipleRoomCombosHaveFreeChildText() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithMultipleRoomCombosWithFreeChildText();

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertEquals("First combo free child text", result);
    }

    @Test
    public void should_ReturnRoomFreeChildText_When_RoomComboHasNoFreeChildText() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithEmptyRoomComboAndRoomsWithFreeChildText();

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertEquals("Room rate plan free child text", result);
    }

    @Test
    public void should_ReturnNull_When_HotelDetailsHasNullRoomCombosAndRooms() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(null);
        hotelDetails.setRooms(null);

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HotelDetailsHasEmptyRoomCombosAndRooms() {
        // Given
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(new ArrayList<>());
        hotelDetails.setRooms(new ArrayList<>());

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_RoomsHaveNoRatePlans() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithRoomsButNoRatePlans();

        // When
        String result = roomInfoHelper.getFreeChildTextFromHotelDetails(hotelDetails);

        // Then
        assertNull(result);
    }

    // ==================== buildRoomSummary Tests ====================

    @Test
    public void should_BuildRoomSummary_When_ValidRoomInfoExtension() {
        // Given
        RoomInfoExtension roomInfoExtension = createRoomInfoExtensionWithSummary();

        // When
        RoomSummary result = roomInfoHelper.buildRoomSummary(roomInfoExtension);

        // Then
        assertNotNull(result);
        assertTrue(result.isTopRated());
        assertEquals(4, result.getRatingCount());
        assertEquals(150, result.getReviewCount());
        assertFalse(result.isDisableLowRating());
        assertNotNull(result.getTagData());
        assertEquals(2, result.getTagData().size());
        
        TagData tag1 = result.getTagData().get(0);
        assertEquals("Clean", tag1.getName());
        assertEquals("cleanliness", tag1.getId());
        assertEquals("POSITIVE", tag1.getTagType());
        assertEquals("POSITIVE", tag1.getSentiment());
    }

    @Test
    public void should_ReturnNull_When_RoomInfoExtensionIsNull() {
        // When
        RoomSummary result = roomInfoHelper.buildRoomSummary(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_RoomSummaryInExtensionIsNull() {
        // Given
        RoomInfoExtension roomInfoExtension = new RoomInfoExtension();
        roomInfoExtension.setRoomSummary(null);

        // When
        RoomSummary result = roomInfoHelper.buildRoomSummary(roomInfoExtension);

        // Then
        assertNull(result);
    }

    @Test
    public void should_HandleEmptyTagData_When_NoTags() {
        // Given
        RoomInfoExtension roomInfoExtension = new RoomInfoExtension();
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomSummary orchSummary = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomSummary();
        orchSummary.setTopRated(true);
        orchSummary.setRatingCount(5);
        orchSummary.setReviewCount(100);
        orchSummary.setDisableLowRating(false);
        orchSummary.setTagData(new ArrayList<>());
        roomInfoExtension.setRoomSummary(orchSummary);

        // When
        RoomSummary result = roomInfoHelper.buildRoomSummary(roomInfoExtension);

        // Then
        assertNotNull(result);
        assertNotNull(result.getTagData());
        assertTrue(result.getTagData().isEmpty());
    }

    // ==================== initializeActionInfoMap Tests ====================

    @Test
    public void should_InitializeActionInfoMap_When_ValidMap() {
        // Given
        Map<String, StayTypeInfo> configMap = new HashMap<>();
        StayTypeInfo stayTypeInfo = new StayTypeInfo();
        stayTypeInfo.setTitle("Test Title");
        configMap.put("TEST_KEY", stayTypeInfo);

        // When
        roomInfoHelper.initializeActionInfoMap(configMap);

        // Then
        // We can't directly verify the private field, but we can test behavior
        // This is tested indirectly through buildStayDetails test
        assertNotNull(configMap);
    }

    @Test
    public void should_HandleNullMap_When_InitializingActionInfoMap() {
        // When - This should not throw an exception
        roomInfoHelper.initializeActionInfoMap(null);

        // Then - No exception thrown, test passes
        assertTrue(true);
    }

    // ==================== Helper methods for creating test data ====================

    private RoomInfo createRoomInfoWithBeds() {
        RoomInfo roomInfo = new RoomInfo();
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        
        List<ArrangementInfo> beds = new ArrayList<>();
        
        ArrangementInfo queenBed = new ArrangementInfo();
        queenBed.setType("Queen Bed");
        queenBed.setCount(1);
        beds.add(queenBed);
        
        ArrangementInfo sofaBed = new ArrangementInfo();
        sofaBed.setType("Sofa Bed");
        sofaBed.setCount(1);
        beds.add(sofaBed);
        
        arrangementMap.put("BEDS", beds);
        roomInfo.setRoomArrangementMap(arrangementMap);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithSizeAndView() {
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("25");
        roomInfo.setRoomSizeUnit("sq ft");
        roomInfo.setRoomViewName("City View");
        roomInfo.setMaxGuestCount(2);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithSizeViewAndBeds() {
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("25");
        roomInfo.setRoomSizeUnit("sq ft");
        roomInfo.setRoomViewName("City View");
        roomInfo.setMaxGuestCount(2);
        
        // Add bed arrangement
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        List<ArrangementInfo> beds = new ArrayList<>();
        
        ArrangementInfo kingBed = new ArrangementInfo();
        kingBed.setType("King Bed");
        kingBed.setCount(1);
        beds.add(kingBed);
        
        arrangementMap.put("BEDS", beds);
        roomInfo.setRoomArrangementMap(arrangementMap);
        
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithBathrooms() {
        RoomInfo roomInfo = new RoomInfo();
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        
        List<ArrangementInfo> bathrooms = new ArrayList<>();
        ArrangementInfo bathroom = new ArrangementInfo();
        bathroom.setType("Full Bathroom");
        bathroom.setCount(2);
        bathrooms.add(bathroom);
        
        arrangementMap.put("BATHROOM", bathrooms);
        roomInfo.setRoomArrangementMap(arrangementMap);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithSingleBathroom() {
        RoomInfo roomInfo = new RoomInfo();
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        
        List<ArrangementInfo> bathrooms = new ArrayList<>();
        ArrangementInfo bathroom = new ArrangementInfo();
        bathroom.setType("Full Bathroom");
        bathroom.setCount(1);
        bathrooms.add(bathroom);
        
        arrangementMap.put("BATHROOM", bathrooms);
        roomInfo.setRoomArrangementMap(arrangementMap);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithStandardBeds() {
        RoomInfo roomInfo = new RoomInfo();
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        
        List<ArrangementInfo> beds = new ArrayList<>();
        
        ArrangementInfo kingBed = new ArrangementInfo();
        kingBed.setType("King Bed");
        kingBed.setCount(1);
        beds.add(kingBed);
        
        ArrangementInfo singleBed = new ArrangementInfo();
        singleBed.setType("Single Bed");
        singleBed.setCount(2);
        beds.add(singleBed);
        
        arrangementMap.put("BEDS", beds);
        roomInfo.setRoomArrangementMap(arrangementMap);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithAlternateBeds() {
        RoomInfo roomInfo = new RoomInfo();
        Map<String, List<ArrangementInfo>> arrangementMap = new HashMap<>();
        
        List<ArrangementInfo> beds = new ArrayList<>();
        ArrangementInfo queenBed = new ArrangementInfo();
        queenBed.setType("Queen Bed");
        queenBed.setCount(1);
        beds.add(queenBed);
        
        List<ArrangementInfo> alternateBeds = new ArrayList<>();
        ArrangementInfo singleBed = new ArrangementInfo();
        singleBed.setType("Single Bed");
        singleBed.setCount(2);
        alternateBeds.add(singleBed);
        
        arrangementMap.put("BEDS", beds);
        arrangementMap.put("ALTERNATE_BEDS", alternateBeds);
        roomInfo.setRoomArrangementMap(arrangementMap);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithSingleGuest() {
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaxGuestCount(1);
        return roomInfo;
    }

    private HotelDetails createHotelDetailsWithRooms() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setStayTypeText("Deluxe Room");
        hotelDetails.setPropertyType("Hotel");
        hotelDetails.setListingType("Apartment");
        hotelDetails.setSellableUnit("entire");
        
        List<Rooms> rooms = new ArrayList<>();
        Rooms room = new Rooms();
        
        // Create room info with spaces
        RoomInfo roomInfo = new RoomInfo();
        List<SpaceData> spaces = new ArrayList<>();
        SpaceData spaceData = new SpaceData();
        spaceData.setType(SpaceData.Type.PRIVATE);
        
        List<com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space> spaceList = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space();
        space.setSpaceType("BEDROOM");
        
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails sleepingDetails = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails();
        sleepingDetails.setBedCount(2);
        sleepingDetails.setBedRoomCount(1);
        sleepingDetails.setMinOccupancy(2);
        sleepingDetails.setMaxOccupancy(4);
        space.setSleepingDetails(sleepingDetails);
        
        spaceList.add(space);
        spaceData.setSpaces(spaceList);
        spaces.add(spaceData);
        roomInfo.setSpaces(spaces);
        
        // Set room info extension for bathroom count
        RoomInfoExtension extension = new RoomInfoExtension();
        extension.setBathroomCount(1);
        roomInfo.setRoomInfoExtension(extension);
        
        room.setRoomInfo(roomInfo);
        
        // Create rate plans with occupancy details
        List<RatePlan> ratePlans = new ArrayList<>();
        RatePlan ratePlan = new RatePlan();
        
        ratePlans.add(ratePlan);
        room.setRatePlans(ratePlans);
        
        rooms.add(room);
        hotelDetails.setRooms(rooms);
        
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithRoomCombos() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setStayTypeText("Apartment Suite");
        hotelDetails.setPropertyType("Apartment");
        hotelDetails.setSellableUnit("entire");
        
        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = 
            new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        
        List<Rooms> rooms = new ArrayList<>();
        
        // First room
        Rooms room1 = createRoomWithSpaceData(1, 1, 2, 2, 1);
        rooms.add(room1);
        
        // Second room  
        Rooms room2 = createRoomWithSpaceData(1, 1, 2, 2, 1);
        rooms.add(room2);
        
        roomCombo.setRooms(rooms);
        roomCombos.add(roomCombo);
        hotelDetails.setRoomCombos(roomCombos);
        
        return hotelDetails;
    }

    private Rooms createRoomWithSpaceData(int bedCount, int bedRoomCount, int minOccupancy, int maxOccupancy, int bathroomCount) {
        Rooms room = new Rooms();
        
        RoomInfo roomInfo = new RoomInfo();
        List<SpaceData> spaces = new ArrayList<>();
        SpaceData spaceData = new SpaceData();
        spaceData.setType(SpaceData.Type.PRIVATE);
        
        List<com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space> spaceList = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space();
        space.setSpaceType("BEDROOM");
        
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails sleepingDetails = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails();
        sleepingDetails.setBedCount(bedCount);
        sleepingDetails.setBedRoomCount(bedRoomCount);
        sleepingDetails.setMinOccupancy(minOccupancy);
        sleepingDetails.setMaxOccupancy(maxOccupancy);
        space.setSleepingDetails(sleepingDetails);
        
        spaceList.add(space);
        spaceData.setSpaces(spaceList);
        spaces.add(spaceData);
        roomInfo.setSpaces(spaces);
        
        RoomInfoExtension extension = new RoomInfoExtension();
        extension.setBathroomCount(bathroomCount);
        roomInfo.setRoomInfoExtension(extension);
        
        room.setRoomInfo(roomInfo);
        return room;
    }

    private SpaceData createOrchSpaceData() {
        SpaceData spaceData = new SpaceData();
//        spaceData.setDescriptive("Private Room");
        spaceData.setType(SpaceData.Type.PRIVATE);
        
        // Create display item for shared info
        com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem displayItem = 
            new com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem();
        displayItem.setText("Shared amenities");
        displayItem.setIconUrl("https://example.com/icon.png");
        spaceData.setDisplayItem(displayItem);
        
        // Create spaces
        List<com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space> spaces = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space();
        space.setName("Bedroom");
        space.setSpaceType("BEDROOM");
        space.setAreaText("15 sq m");
        space.setDescriptionText("Comfortable bedroom");
        space.setSpaceId("space_1");
        
        spaces.add(space);
        spaceData.setSpaces(spaces);
        
        return spaceData;
    }

    private SpaceData createOrchSpaceDataWithOccupancy() {
        SpaceData spaceData = createOrchSpaceData();
        
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space = spaceData.getSpaces().get(0);
        space.setBaseOccupancy(2);
        space.setMaxOccupancy(4);
        space.setFinalOccupancy(3);
        
        return spaceData;
    }

    private SpaceData createOrchSpaceDataWithMedia() {
        SpaceData spaceData = createOrchSpaceData();
        
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space space = spaceData.getSpaces().get(0);
        
        List<com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity> media = new ArrayList<>();
        
        com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity media1 = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity();
        media1.setMediaType("IMAGE");
        media1.setUrl("https://example.com/image1.jpg");
        media.add(media1);
        
        com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity media2 = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity();
        media2.setMediaType("VIDEO");
        media2.setUrl("https://example.com/video1.mp4");
        media.add(media2);
        
        space.setMedia(media);
        
        return spaceData;
    }

    private RoomInfoExtension createRoomInfoExtensionWithSummary() {
        RoomInfoExtension extension = new RoomInfoExtension();
        
        com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomSummary roomSummary = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomSummary();
        roomSummary.setTopRated(true);
        roomSummary.setRatingCount(4);
        roomSummary.setReviewCount(150);
        roomSummary.setDisableLowRating(false);
        
        List<Tag> tagData = new ArrayList<>();
        
        Tag tag1 = new Tag();
        tag1.setName("Clean");
        tag1.setId("cleanliness");
        tag1.setTagType("POSITIVE");
        tag1.setSentiment("POSITIVE");
        tagData.add(tag1);
        
        Tag tag2 = new Tag();
        tag2.setName("Comfortable");
        tag2.setId("comfort");
        tag2.setTagType("POSITIVE");
        tag2.setSentiment("POSITIVE");
        tagData.add(tag2);
        
        roomSummary.setTagData(tagData);
        extension.setRoomSummary(roomSummary);
        
        return extension;
    }

    private HotelDetails createHotelDetailsWithRoomCombosAndFreeChildText() {
        HotelDetails hotelDetails = new HotelDetails();
        
        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = 
            new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo.setFreeChildText("Children under 12 stay free");
        roomCombos.add(roomCombo);
        
        hotelDetails.setRoomCombos(roomCombos);
        hotelDetails.setRooms(new ArrayList<>());
        
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithRoomsAndFreeChildText() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(new ArrayList<>());
        
        List<Rooms> rooms = new ArrayList<>();
        Rooms room = new Rooms();
        
        List<RatePlan> ratePlans = new ArrayList<>();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setFreeChildText("Up to 2 children stay free");
        ratePlans.add(ratePlan);
        
        room.setRatePlans(ratePlans);
        rooms.add(room);
        hotelDetails.setRooms(rooms);
        
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithMultipleRoomCombosWithFreeChildText() {
        HotelDetails hotelDetails = new HotelDetails();
        
        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        
        // First room combo with free child text
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo1 = 
            new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo1.setFreeChildText("First combo free child text");
        roomCombos.add(roomCombo1);
        
        // Second room combo with different free child text
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo2 = 
            new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo2.setFreeChildText("Second combo free child text");
        roomCombos.add(roomCombo2);
        
        hotelDetails.setRoomCombos(roomCombos);
        hotelDetails.setRooms(new ArrayList<>());
        
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithEmptyRoomComboAndRoomsWithFreeChildText() {
        HotelDetails hotelDetails = new HotelDetails();
        
        // Empty room combos (no free child text)
        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = 
            new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        // No free child text set
        roomCombos.add(roomCombo);
        hotelDetails.setRoomCombos(roomCombos);
        
        // Rooms with free child text
        List<Rooms> rooms = new ArrayList<>();
        Rooms room = new Rooms();
        
        List<RatePlan> ratePlans = new ArrayList<>();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setFreeChildText("Room rate plan free child text");
        ratePlans.add(ratePlan);
        
        room.setRatePlans(ratePlans);
        rooms.add(room);
        hotelDetails.setRooms(rooms);
        
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithRoomsButNoRatePlans() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(new ArrayList<>());
        
        List<Rooms> rooms = new ArrayList<>();
        Rooms room = new Rooms();
        room.setRatePlans(new ArrayList<>()); // Empty rate plans
        rooms.add(room);
        hotelDetails.setRooms(rooms);
        
        return hotelDetails;
    }
} 