package com.mmt.hotels.clientgateway.transformer.response;

import java.util.*;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.model.response.pricing.LinkedRate;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import org.apache.commons.collections.CollectionUtils;
import com.mmt.hotels.clientgateway.response.UpSellDetails;
import com.mmt.hotels.clientgateway.response.rooms.StayDetail;
import com.mmt.hotels.clientgateway.response.rooms.Tariff;
import com.mmt.hotels.clientgateway.util.*;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jboss.logging.MDC;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.response.BlackInfo;
import com.mmt.hotels.clientgateway.response.availrooms.DoubleBlackInfo;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchRoomsResponseTransformerAndroid;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.pricing.AvailDetails;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.LowestRateAPResp;
import com.mmt.hotels.model.response.pricing.MealPlan;
import com.mmt.hotels.model.response.pricing.OccupancyDetails;
import com.mmt.hotels.model.response.pricing.PackageRoomRatePlan;
import com.mmt.hotels.model.response.pricing.PackageRoomType;
import com.mmt.hotels.model.response.pricing.PaymentDetails;
import com.mmt.hotels.model.response.pricing.PaymentMode;
import com.mmt.hotels.model.response.pricing.Penalty;
import com.mmt.hotels.model.response.pricing.RangePrice;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.RoomType;
import com.mmt.hotels.model.response.pricing.RoomTypeDetails;
import com.mmt.hotels.model.response.pricing.SupplierDetails;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.pricing.jsonviews.RTBPreApprovedCard;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.model.response.staticdata.ImageType;
import com.mmt.hotels.model.response.staticdata.MediaData;
import com.mmt.hotels.model.response.staticdata.ProfessionalImageEntity;
import com.mmt.hotels.model.response.staticdata.Space;
import com.mmt.hotels.model.response.staticdata.SpaceData;
import com.mmt.hotels.model.response.staticdata.VideoInfo;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import com.mmt.model.HtlRmInfo;
import com.mmt.model.RoomInfo;
import com.mmt.model.SleepingArrangement;

import junit.framework.Assert;

import static com.mmt.hotels.clientgateway.constants.Constants.SPACE_OCCUPANCY_EXTRA_GUESTS;
import static com.mmt.hotels.clientgateway.constants.Constants.SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsResponseTransformerAndroidTest {
	
	@InjectMocks
    SearchRoomsResponseTransformerAndroid searchRoomsResponseTransformerAndroid;

    @InjectMocks
    DateUtil dateUtil;

    @Mock
    MobConfigHelper mobConfigHelper;

    @Mock
    CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;

    @Mock
    OfferCardUtil offerCardUtil;

    ObjectMapperUtil objectMapperUtil = new ObjectMapperUtil();

    @Before
    public void setup() {
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
        utility = Mockito.spy(new Utility());
        MockitoAnnotations.initMocks(this);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        Map<String,Map<String, Map<String,String>>> ratePlanNameMap = new HashMap<>();
        ratePlanNameMap.put(Constants.DEFAULT,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_FC,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_BED_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_NR,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_BED_TYPE,"{NR}");

        ReflectionTestUtils.setField(utility, "ratePlanNameMap", ratePlanNameMap);
        ReflectionTestUtils.setField(utility, "ratePlanNameMapRedesign", ratePlanNameMap);
        ReflectionTestUtils.setField(utility,"apLimitForInclusionIcons",1);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "rtbCardConfigs", new HashMap<>());

        List<String> mealPlanCodeList = Arrays.asList("AP", "CP");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"mealPlanCodeList",mealPlanCodeList);

        ObjectMapper mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
    }

    @Test
    public void testConvertSearchRoomsResponse() {
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"mealplanFilterEnable",true);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);

        HotelRates hotelRates = new HotelRates();
        hotelRates.setBnplBaseAmount(10d);
        hotelRates.setBlackEligible(false);
        hotelRates.setIsBNPLAvailable(false);
        hotelRates.setPahWalletApplicable(false);
        hotelRates.setPnAvlbl(true);
        hotelRates.setPanCardRequired(true);
        hotelRates.setShowFcBanner(true);
        hotelRates.setSoldOut(false);
        hotelRates.setBreakFast(true);
        hotelRates.setFreeCancellation(true);
        hotelRates.setBreakFastAvailable(true);
        hotelRates.setFreeCancellationAvailable(true);
        hotelRates.setPAHTariffAvailable(true);

        hotelRates.setRecommendedRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRecommendedRoomTypeDetails().setTotalDisplayFare(new DisplayFare());

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(1d);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");
        displayPriceBreakDown.getCouponInfo().setDiscountAmount(100.0);

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(1d);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
        displayPriceBreakDownList.get(0).getCouponInfo().setDiscountAmount(100.0);

        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        hotelRates.getRecommendedRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());
        hotelRates.getRecommendedRoomTypeDetails().setOccupancyDetails(new OccupancyDetails());
        hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().setAdult(2);

        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();

        ratePlanCB.setAvailDetails(new AvailDetails());
        ratePlanCB.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanCB.setCancelPenaltyList(new ArrayList<CancelPenalty>());
        ratePlanCB.getCancelPenaltyList().add(new CancelPenalty());
        ratePlanCB.getCancelPenaltyList().get(0).setPenaltyDescription(new Penalty());
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");

        // LOS inclusions
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(1).setCode("LOS1_Code");
        ratePlanCB.getInclusions().get(1).setValue("LOS1_Value");
        ratePlanCB.getInclusions().get(1).setType(Constants.INCLUSION_TYPE_LOS);
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(2).setCode("LOS2_Code");
        ratePlanCB.getInclusions().get(2).setValue("LOS2_Value");
        ratePlanCB.getInclusions().get(2).setType(Constants.INCLUSION_TYPE_LOS);

        ratePlanCB.setMealPlans(new ArrayList<MealPlan>());
        ratePlanCB.getMealPlans().add(new MealPlan());
        ratePlanCB.getMealPlans().get(0).setCode("SMAP");
        ratePlanCB.getMealPlans().get(0).setValue("abcd");
        ratePlanCB.setPaymentDetails(new PaymentDetails());
        ratePlanCB.getPaymentDetails().setPaymentMode(PaymentMode.PAS);
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        ratePlanCB.setSupplierDetails(new SupplierDetails());
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        ratePlanCB.getSupplierDetails().setCostPrice(19d);
        ratePlanCB.setCampaingText("Free Cancellation till 24 hrs");

        roomType.getRatePlanList().put("abc", ratePlanCB);


        hotelRates.getRecommendedRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOffers(new ArrayList<>());
        hotelRates.getOffers().add(new RangePrice());
        hotelRates.setRtbPreApproved(false);
        hotelRates.setRequestToBook(true);

        List<HotelRates> hotelRatesList = new ArrayList<HotelRates>();
        hotelRatesList.add(hotelRates);

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildHotelRates(hotelRatesList).build();


        List<HtlRmInfo> htlRmInfoList = new ArrayList<HtlRmInfo>();
        htlRmInfoList.add(new HtlRmInfo());
        htlRmInfoList.get(0).setHotelRoomInfo(new HashMap<String, RoomInfo>());
        SpaceData spaceData = new SpaceData();
        Space space = new Space();
        space.setDescriptionText("");
        space.setOpenCardText("test");
        space.setMedia(Arrays.asList(new MediaData()));
        spaceData.setSpaces(Arrays.asList(space));
        htlRmInfoList.get(0).setSharedSpaces(spaceData);

        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaster(true);
        roomInfo.setMaxAdultCount(5);
        roomInfo.setRoomSize("1");
        roomInfo.setRoomViewName("view");
        roomInfo.setBedType("king");
        roomInfo.setRoomSummary(new RoomSummary());
        roomInfo.getRoomSummary().setTopRated(true);

        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed1 = new SleepingArrangement();
        bed1.setCount(3);
        bed1.setType("");
        beds.add(bed1);
        SleepingArrangement bed2 = new SleepingArrangement();
        bed2.setCount(1);
        bed2.setType("");
        beds.add(bed2);
        roomInfo.setBeds(beds);

        htlRmInfoList.get(0).getHotelRoomInfo().put("abc", roomInfo);
        roomInfo.setFacilityWithGrp(new ArrayList<>());
        roomInfo.getFacilityWithGrp().add(new FacilityGroup());
        roomInfo.getFacilityWithGrp().get(0).setName("test");
        roomInfo.getFacilityWithGrp().get(0).setFacilities(new ArrayList<>());
        roomInfo.getFacilityWithGrp().get(0).getFacilities().add(new Facility());
        roomInfo.setRoomLevelVideos(new ArrayList<>());
        roomInfo.getRoomLevelVideos().add(new VideoInfo());
        roomInfo.getRoomLevelVideos().get(0).setTags(new ArrayList<>());
        roomInfo.getRoomLevelVideos().get(0).setUrl("url");

        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity.Builder().buildHtlRmInfo(htlRmInfoList).build();

        when(commonResponseTransformer.buildBlackInfo(Mockito.any())).thenReturn(new BlackInfo());
        when(commonResponseTransformer.getDoubleBlackInfo(Mockito.any())).thenReturn(new DoubleBlackInfo());

        HotelImage hotelImage = new HotelImage();
        hotelImage.setHotelId("test");
        hotelImage.setImageDetails(new ImageType());
        hotelImage.getImageDetails().setProfessional(new HashMap<String, List<ProfessionalImageEntity>>());
        hotelImage.getImageDetails().getProfessional().put("R", new ArrayList<>());
        hotelImage.getImageDetails().getProfessional().get("R").add(new ProfessionalImageEntity());
        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setCheckIn("2022-11-03");
        criteria.setCheckOut("2022-11-04");
        criteria.setLocationType("region");
        ReflectionTestUtils
                .setField(searchRoomsResponseTransformerAndroid, "dateUtil" , dateUtil);
       List<Filter> filterlist = new ArrayList<>();
       filterlist.add(new Filter());
       filterlist.get(0).setFilterGroup(FilterGroup.MEAL_PLAN_AVAIL);
       filterlist.get(0).setFilterValue("TWO_MEAL_AVAIL");
        Map<String,String> expDataMap = new HashMap<>();
        expDataMap.put("plcnew","true");
        expDataMap.put("ratePlanRedesign","true");
        CommonModifierResponse commonModifierResponsep = new CommonModifierResponse();
        LinkedHashMap<String, String> expData = new LinkedHashMap<>();
        expData.put("testKey", "testval");
        commonModifierResponsep.setExpDataMap(expData);
        commonModifierResponsep.setVariantKey("test");

        SearchRoomsResponse searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,filterlist,new RequestDetails(), commonModifierResponsep);

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getExpData());
        Assert.assertNotNull(searchRoomsResponse.getVariantKey());
        ratePlanCB.getMealPlans().get(0).setCode("AP");
        filterlist.get(0).setFilterValue("ALL_MEAL_AVAIL");
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,filterlist,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);

        hotelRates.setOtherRecommendedRooms(new ArrayList<>());
        hotelRates.getOtherRecommendedRooms().add(new RoomTypeDetails());
        hotelRates.getOtherRecommendedRooms().get(0).setRoomType(new HashMap<>());
        hotelRates.getOtherRecommendedRooms().get(0).getRoomType().put("def",roomType);
        hotelRates.getOtherRecommendedRooms().get(0).setTotalDisplayFare(new DisplayFare());
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        LowestRateAPResp lowestRateAPResp = new LowestRateAPResp();
        AvailDetails availDetails = new AvailDetails();
        availDetails.setOccupancyDetails(new OccupancyDetails());
        lowestRateAPResp.setAvailDetails(availDetails);
        hotelRates.setLowestRate(lowestRateAPResp);

        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").setSegmentId(Constants.MYPARTNER_SEGMENT_ID);
        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").getSupplierDetails().setSupplierCode("EPXX0034");

        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getPersuasions());


        hotelRates.setRoomTypeDetails(null); hotelRates.setPropertyType("Hostel");
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        hotelRates.setRecommendedRoomTypeDetails(null);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOccupencyLessRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getOccupencyLessRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getOccupencyLessRoomTypeDetails().getRoomType().put("Def",roomType);
        hotelRates.getOccupencyLessRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.setCategories(new HashSet<String>(){{add("luxury_hotels");}});
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        hotelRates.setRtbPreApprovedCard(new RTBPreApprovedCard());
        hotelRates.setRTBRatePlanPreApproved(true);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        hotelRates.setRTBRatePlanPreApproved(false);
        hotelRates.setRequestToBook(false);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap, null, criteria,null,new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);

        //for package rate plans
        setupForPackgeRatePlans(hotelRates);
        hotelRates.setOccupencyLessRoomTypeDetails(null);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid
                                  .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                                                              hotelImage, expDataMap, null, criteria, null,
                                                              new RequestDetails(), new CommonModifierResponse());
        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getPackageRooms());
        Assert.assertTrue(
            searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getFilterCode().contains("PACKAGE_RATE"));
        //if package in locked then LUXE PACKAGE filter should not be present
//        Assert.assertFalse(searchRoomsResponse.getFilters().contains("PACKAGE_RATE"));

        //setting first room and rate plan as package
        hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next().getValue().getRatePlanList()
                  .entrySet().iterator().next().getValue().setPackageRoomRatePlan(true);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid
                                  .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                                                              hotelImage, expDataMap, null, criteria, null,
                                                              new RequestDetails(), new CommonModifierResponse());
        //first filter has to be package
//        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        hotelRates.setCategories(new HashSet<String>(){{add("package_hotels");}});

        searchRoomsResponse = searchRoomsResponseTransformerAndroid
                .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                        hotelImage, expDataMap, null, criteria, null,
                        new RequestDetails(), new CommonModifierResponse());

//        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        hotelRates.setDetailDeeplinkUrl("detaildeeplinkurl?city=City&checkAvailability=true");
        hotelRates.setName("hotelName");
        hotelRates.setHotelIcon("hotelIcon");

        searchRoomsResponse = searchRoomsResponseTransformerAndroid
                .convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
                        hotelImage, expDataMap, null, criteria, null,
                        new RequestDetails(), new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse.getDetailDeeplinkUrl());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails().getUrl());


    }

    @Test
    public void testUpSellDetails() {
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE)).thenReturn("Add Free Cancellation for ₹{amount} for all guests");

        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_DETAILS_PAGE_TITLE)).thenReturn("Add Breakfast for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE)).thenReturn("Add Breakfast + Lunch/Dinner for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE)).thenReturn("Add Lunch/Dinner for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE)).thenReturn("Add All meals for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE)).thenReturn("Add Free Cancellation for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE)).thenReturn("Add Breakfast & Free Cancellation for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE)).thenReturn("Add Breakfast + Lunch/Dinner & Free Cancellation for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE)).thenReturn("Add Lunch/Dinner & Free Cancellation for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE)).thenReturn("Add All meals & Free Cancellation for ₹{amount} for all guests");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE)).thenReturn("Includes Taxes & Fees");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! Breakfast added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! Breakfast + Lunch/Dinner added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! All meals added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! Lunch/Dinner added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! Free Cancellation added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! Breakfast & Free Cancellation added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! Breakfast + Lunch/Dinner & Free Cancellation added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! Lunch/Dinner & Free Cancellation added to your booking");
        when(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE)).thenReturn("Great! All meals & Free Cancellation added to your booking");

        UpSellDetails Obj = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildUpSellDetails", "EP", false, "EP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSubTitle(), "Includes Taxes & Fees");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Free Cancellation added to your booking");

        // ========= EP Base rateplan without free cancellation ========
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", false, "CP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", false, "CP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast & Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast & Free Cancellation added to your booking");


        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", false, "MAP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast + Lunch/Dinner for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast + Lunch/Dinner added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", false, "MAP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast + Lunch/Dinner & Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast + Lunch/Dinner & Free Cancellation added to your booking");

        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", false, "AP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", false, "AP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals & Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals & Free Cancellation added to your booking");

        // ======== EP Base rateplan with free cancellation =======

        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", true, "CP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", true, "CP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast added to your booking");


        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", true, "MAP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast + Lunch/Dinner for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast + Lunch/Dinner added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", true, "MAP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Breakfast + Lunch/Dinner for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Breakfast + Lunch/Dinner added to your booking");

        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", true, "AP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("EP", true, "AP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");

        // ======= CP Base rateplan without free cancellation =====
        Obj = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildUpSellDetails", "CP", false, "CP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSubTitle(), "Includes Taxes & Fees");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Free Cancellation added to your booking");

        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", false, "MAP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Lunch/Dinner for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Lunch/Dinner added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", false, "MAP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Lunch/Dinner & Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Lunch/Dinner & Free Cancellation added to your booking");

        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", false, "AP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", false, "AP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals & Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals & Free Cancellation added to your booking");

        // ======== CP Base rateplan with free cancellation =====
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", true, "MAP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Lunch/Dinner for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Lunch/Dinner added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", true, "MAP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Lunch/Dinner for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Lunch/Dinner added to your booking");

        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", true, "AP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("CP", true, "AP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");

        // ======== MAP Base rateplan without free cancellation =====
        Obj = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildUpSellDetails", "MAP", false, "MAP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSubTitle(), "Includes Taxes & Fees");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Free Cancellation added to your booking");

        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("MAP", false, "AP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("MAP", false, "AP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals & Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals & Free Cancellation added to your booking");

        // ======== MAP Base rateplan with free cancellation =====
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("MAP", true, "AP", false, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");
        Obj = searchRoomsResponseTransformerAndroid.buildUpSellDetails("MAP", true, "AP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add All meals for ₹10 for all guests");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! All meals added to your booking");

        // ======== AP Base rateplan without free cancellation =====
        Obj = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildUpSellDetails", "AP", false, "AP", true, 10.0);
        Assert.assertEquals(Obj.getTitle(), "Add Free Cancellation for ₹10 for all guests");
        Assert.assertEquals(Obj.getSubTitle(), "Includes Taxes & Fees");
        Assert.assertEquals(Obj.getSelectedTitle(), "Great! Free Cancellation added to your booking");

        // ======== AP Base rateplan with free cancellation =====
        // There is nothing to upsell in this case.
    }

    private void setupForPackgeRatePlans(HotelRates hotelRates) {
        hotelRates.setPackageRoomDetails(new RoomTypeDetails());
        hotelRates.getPackageRoomDetails().setRoomType(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().put("packageRoom1", new PackageRoomType());
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setAnimationType("UNLOCKED");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setRecommendText("#MMT Recommends");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setCtaText("Select Package");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").setRatePlanList(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .put("packageRatePlan1", new PackageRoomRatePlan());
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckOutDate("2021-07-24");
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckInDate("2021-07-21");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setDisplayFare(new DisplayFare());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setPackageRoomRatePlan(true);
    }

    // Constants
    public static final String CLIENT_DESKTOP = "DESKTOP";
    public static final String ANDROID = "ANDROID";

    @Test
    public void testBuildSleepInfoText() {


        /*MOCKS*/
        when(polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS)).thenReturn("Can accommodate {extra} more guest at extra cost");
        when(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT)).thenReturn("Private rooms also available");
        when(polyglotService.getTranslatedData(HOSTEL_BEDS_AVAILABLE_TEXT)).thenReturn("Shared dorm also available");
        when(polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT)).thenReturn("Sleeps {occupancy} guests");


        StayDetail stayDetail;
        Pair<Boolean, Boolean> bedAndRoomPresent;

        // Case 1: stayDetail is null
        System.out.println("Running Test Case 1: stayDetail is null");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", null, false, 0, new ImmutablePair<>(false, false), "Free Child Belongs here", false);

        // Case 2: maxGuests is 0
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(0);
        System.out.println("Running Test Case 2: maxGuests is 0");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "Free Child Belongs here", false);
        assertNull(stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 2");

        // Case 3: maxGuests > 0 with isOHSExpEnable false

        // DESKTOP client
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
        System.out.println("Running Test Case 3: maxGuests > 0 with isOHSExpEnable false");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("Sleeps 2 guests", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 3 - Scenario 1");

        // ANDROID client with isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 3: maxGuests > 0 with isOHSExpEnable false and isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", true);
        assertEquals("Sleeps 2 guests", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 3 - Scenario 2");

        // ANDROID client without isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 3: maxGuests > 0 with isOHSExpEnable false and not isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("<b>Sleeps 2 guests</b>", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 3 - Scenario 3");

        // Case 4: maxGuests < maxCapacity with isOHSExpEnable false

        // DESKTOP client
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
        System.out.println("Running Test Case 4: maxGuests < maxCapacity with isOHSExpEnable false");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("Sleeps 2 guests", stayDetail.getSleepInfoText());
        assertEquals("Can accommodate 1 more guest at extra cost", stayDetail.getAdditionalSleepInfoText());
        System.out.println("Assertion Passed: Test Case 4 - Scenario 1");

        // ANDROID client with isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 4: maxGuests < maxCapacity with isOHSExpEnable false and isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", true);
        assertEquals("Sleeps 2 guests, Can accommodate 1 more guest at extra cost", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 4 - Scenario 2");

        // ANDROID client without isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 4: maxGuests < maxCapacity with isOHSExpEnable false and not isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("<b>Sleeps 2 guests</b> • Can accommodate 1 more guest at extra cost", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 4 - Scenario 3");

        // Case 5: maxGuests > maxCapacity with isOHSExpEnable false

        // DESKTOP client
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(3);
        stayDetail.setMaxCapacity(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
        System.out.println("Running Test Case 5: maxGuests > maxCapacity with isOHSExpEnable false");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("Sleeps 3 guests", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 5 - Scenario 1");

        // ANDROID client with isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(3);
        stayDetail.setMaxCapacity(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 5: maxGuests > maxCapacity with isOHSExpEnable false and isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", true);
        assertEquals("Sleeps 3 guests", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 5 - Scenario 2");

        // ANDROID client without isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(3);
        stayDetail.setMaxCapacity(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 5: maxGuests > maxCapacity with isOHSExpEnable false and not isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("<b>Sleeps 3 guests</b>", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 5 - Scenario 3");

        // Case 6: maxGuests == maxCapacity with isOHSExpEnable false

        // DESKTOP client
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
        System.out.println("Running Test Case 6: maxGuests == maxCapacity with isOHSExpEnable false");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("Sleeps 2 guests", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 6 - Scenario 1");

        // ANDROID client with isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 6: maxGuests == maxCapacity with isOHSExpEnable false and isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", true);
        assertEquals("Sleeps 2 guests", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 6 - Scenario 2");

        // ANDROID client without isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(2);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 6: maxGuests == maxCapacity with isOHSExpEnable false and not isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, false, 0, new ImmutablePair<>(false, false), "", false);
        assertEquals("<b>Sleeps 2 guests</b>", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 6 - Scenario 3");

        // Case 7: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 1

        // DESKTOP client
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
        System.out.println("Running Test Case 7: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 1");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 1, bedAndRoomPresent, "", false);
        assertNull(stayDetail.getSleepInfoText());
        assertNull(stayDetail.getAdditionalSleepInfoText());
        assertEquals("Private rooms also available", stayDetail.getBedAndRoomAvailabilityText());
        System.out.println("Assertion Passed: Test Case 7 - Scenario 1");

        // ANDROID client with isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 7: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 1 and isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 1, bedAndRoomPresent, "", true);
        assertEquals("Private rooms also available", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 7 - Scenario 2");

        // ANDROID client without isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 7: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 1 and not isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 1, bedAndRoomPresent, "", false);
        assertEquals("Private rooms also available", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 7 - Scenario 3");


        // ANDROID client without isNewDetailPageTrue && BedAndRoomPresent=false,false
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(false ,false);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 7: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 1 and not isNewDetailPageTrue and BedAndRoomPresent=false,false");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 1, bedAndRoomPresent, "", false);
        assertNull(stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 7 - Scenario 4");


        // Case 8: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 2

        // DESKTOP client
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
        System.out.println("Running Test Case 8: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 2");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 2, bedAndRoomPresent, "", false);
        assertEquals("Sleeps 2 guests", stayDetail.getSleepInfoText());
        assertEquals("Shared dorm also available", stayDetail.getBedAndRoomAvailabilityText());
        System.out.println("Assertion Passed: Test Case 8 - Scenario 1");

        // ANDROID client with isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 8: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 2 and isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 2, bedAndRoomPresent, "", true);
        assertEquals("Shared dorm also available", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 8 - Scenario 2");

        // ANDROID client without isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 8: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 2 and not isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 2, bedAndRoomPresent, "", false);
        assertEquals("Shared dorm also available", stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 8 - Scenario 3");

        // ANDROID client without isNewDetailPageTrue && bedAndRoomPresent=false,false
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(false, false);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 8: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 2 and not isNewDetailPageTrue && bedAndRoomPresent=false,false");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 2, bedAndRoomPresent, "", false);
        assertNull( stayDetail.getSleepInfoText());
        System.out.println("Assertion Passed: Test Case 8 - Scenario 4");

        // Case 9: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 0

        // DESKTOP client
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
        System.out.println("Running Test Case 9: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 0");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 0, bedAndRoomPresent, "", false);
        assertNull(stayDetail.getSleepInfoText());
        assertNull(stayDetail.getAdditionalSleepInfoText());
        assertNull(stayDetail.getBedInfoText());
        System.out.println("Assertion Passed: Test Case 9 - Scenario 1");

        // ANDROID client with isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 9: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 0 and isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 0, bedAndRoomPresent, "", true);
        assertNull(stayDetail.getSleepInfoText());
        assertNull(stayDetail.getAdditionalSleepInfoText());
        assertNull(stayDetail.getBedInfoText());
        System.out.println("Assertion Passed: Test Case 9 - Scenario 2");

        // ANDROID client without isNewDetailPageTrue
        stayDetail = new StayDetail();
        stayDetail.setMaxGuests(2);
        stayDetail.setMaxCapacity(3);
        bedAndRoomPresent = new ImmutablePair<>(true, true);
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), ANDROID);
        System.out.println("Running Test Case 9: maxGuests > 0 with isOHSExpEnable true and sellableCombo == 0 and not isNewDetailPageTrue");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildSleepInfoText", stayDetail, true, 0, bedAndRoomPresent, "", false);
        assertNull(stayDetail.getSleepInfoText());
        assertNull(stayDetail.getAdditionalSleepInfoText());
        assertNull(stayDetail.getBedInfoText());
        System.out.println("Assertion Passed: Test Case 9 - Scenario 3");
    }

    @Test
    public void getRoomTariffInThisRatePlanCountTest(){
        List<Tariff> tariffList = new ArrayList<>();
        Tariff tariff = new Tariff();
        com.mmt.hotels.clientgateway.response.rooms.RoomTariff roomTariff = new com.mmt.hotels.clientgateway.response.rooms.RoomTariff();
        tariff.setRoomTariffs(new ArrayList<>());
        tariff.getRoomTariffs().add(roomTariff);
        tariff.getRoomTariffs().add(roomTariff);
        tariffList.add(tariff);
        Assert.assertNotNull(ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "getRoomTariffInThisRatePlanCount", tariffList));
    }


    @Test
    public void testBuildParentLinkedRates() throws Exception {
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<LinkedRate> linkedRates = new ArrayList<>();
        LinkedRate linkedRate = new LinkedRate();
        linkedRate.setType("type");
        linkedRate.setPricingKey("pricingKey");
        linkedRates.add(linkedRate);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildParentLinkedRates", ratePlan, linkedRates);

        org.junit.Assert.assertTrue(CollectionUtils.isNotEmpty(ratePlan.getParentLinkedRates()));
        org.junit.Assert.assertEquals("type", ratePlan.getParentLinkedRates().get(0).getType());
        org.junit.Assert.assertEquals("pricingKey", ratePlan.getParentLinkedRates().get(0).getPricingKey());
    }

    @Test
    public void testBuildChildLinkedRates() throws Exception {
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<LinkedRate> linkedRates = new ArrayList<>();
        LinkedRate linkedRate = new LinkedRate();
        linkedRate.setType("type");
        linkedRate.setPricingKey("pricingKey");
        linkedRates.add(linkedRate);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildChildLinkedRates", ratePlan, linkedRates);

        org.junit.Assert.assertTrue(CollectionUtils.isNotEmpty(ratePlan.getChildLinkedRates()));
        org.junit.Assert.assertEquals("type", ratePlan.getChildLinkedRates().get(0).getType());
        org.junit.Assert.assertEquals("pricingKey", ratePlan.getChildLinkedRates().get(0).getPricingKey());
    }

    @Test
    public void testLinkRatePlans() throws Exception {
        SelectRoomRatePlan ratePlan1 = new SelectRoomRatePlan();
        ratePlan1.setRpc("rpc1");
        ratePlan1.setLinkedRatePlanName("Linked Rate Plan 1");

        SelectRoomRatePlan ratePlan2 = new SelectRoomRatePlan();
        ratePlan2.setRpc("rpc2");
        ratePlan2.setLinkedRatePlanName("Linked Rate Plan 2");

        com.mmt.hotels.clientgateway.response.rooms.LinkedRate linkedRate = new com.mmt.hotels.clientgateway.response.rooms.LinkedRate();
        linkedRate.setPricingKey("rpc2");
        linkedRate.setType("CANCELLATION_POLICY_NR");
        ratePlan1.setChildLinkedRates(Collections.singletonList(linkedRate));

        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        ratePlans.add(ratePlan1);
        ratePlans.add(ratePlan2);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "linkRatePlans", ratePlans);

        org.junit.Assert.assertTrue(CollectionUtils.isNotEmpty(ratePlan1.getLinkedRatePlans()));
        org.junit.Assert.assertEquals("Linked Rate Plan 2", ratePlan1.getLinkedRatePlans().get(0).getData().getRatePlanName());
    }

}	
