package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlanFlags;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.service.PolyglotService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SearchRoomsPersuasionHelper in GI project
 * Tests only the functionality that actually exists in GI implementation:
 * 1. Staycation deal persuasion logic
 * 2. Empty buildRatePlanPersuasionsMap method
 */
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsPersuasionHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private SearchRoomsPersuasionHelper searchRoomsPersuasionHelper;

    @Before
    public void setUp() {
        // Setup common mock behaviors
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Mocked Translation Text");
    }

    // ==================== getRatePlanPersuasion Tests - Staycation Logic ====================

    @Test
    public void should_ReturnNull_When_RatePlanOrchV2IsNull() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = null;
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_ReturnNull_When_RatePlanFlagsIsNull() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = new RatePlan();
        ratePlanOrchV2.setRatePlanFlags(null);
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_CreateStaycationPersuasion_When_StaycationDealAndGetawayFunnel() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        when(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"))
            .thenReturn("Special Getaway Deal");

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PersuasionResponse persuasion = result.get(0);
        assertEquals("Special Getaway Deal", persuasion.getPersuasionText());
        assertEquals("rightBottom", persuasion.getPlaceholderId());
        assertEquals("STAYCATION", persuasion.getId());
        assertEquals("TEXT_WITH_BG_IMAGE", persuasion.getTemplate());
        
        assertNotNull(persuasion.getStyle());
        assertEquals("#4a4a4a", persuasion.getStyle().getTextColor());
        assertEquals("SMALL", persuasion.getStyle().getFontSize());
        assertEquals("https://promos.makemytrip.com/Hotels_product/package/tag-bkg.png", 
            persuasion.getStyle().getBgUrl());
        
        verify(polyglotService).getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT");
    }

    @Test
    public void should_NotCreateStaycationPersuasion_When_StaycationDealButNotGetawayFunnel() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "DESKTOP";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_NotCreateStaycationPersuasion_When_GetawayFunnelButNoStaycationDeal() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithoutStaycationDeal();
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_NotCreateStaycationPersuasion_When_PackageRatePlanIsFalse() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDealButNotPackage();
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_HandleCaseInsensitiveFunnelSource_When_GetawayInDifferentCase() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "getaway"; // lowercase
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        when(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"))
            .thenReturn("Special Getaway Deal");

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("STAYCATION", result.get(0).getId());
        verify(polyglotService).getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT");
    }

    @Test
    public void should_HandleNullFunnelSource_When_GettingRatePlanPersuasion() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = null;
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_HandleEmptyFunnelSource_When_GettingRatePlanPersuasion() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_HandleNullSelectRoomRatePlan_When_GettingRatePlanPersuasion() {
        // Given
        SelectRoomRatePlan ratePlan = null;
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        when(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"))
            .thenReturn("Special Getaway Deal");

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("STAYCATION", result.get(0).getId());
        verify(polyglotService).getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT");
    }

    @Test
    public void should_HandleNullCommonModifierResponse_When_GettingRatePlanPersuasion() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = null;
        boolean isLuxeHotel = false;

        when(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"))
            .thenReturn("Special Getaway Deal");

        // When
        List<PersuasionResponse> result = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("STAYCATION", result.get(0).getId());
        verify(polyglotService).getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT");
    }

    @Test
    public void should_HandleBothLuxeAndNonLuxeHotel_When_GettingRatePlanPersuasion() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();

        when(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"))
            .thenReturn("Special Getaway Deal");

        // When - Test with luxe hotel
        List<PersuasionResponse> luxeResult = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, true);

        // When - Test with non-luxe hotel
        List<PersuasionResponse> nonLuxeResult = searchRoomsPersuasionHelper.getRatePlanPersuasion(
            ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, false);

        // Then - Both should work the same way (isLuxeHotel parameter doesn't affect GI logic)
        assertNotNull(luxeResult);
        assertNotNull(nonLuxeResult);
        assertEquals(1, luxeResult.size());
        assertEquals(1, nonLuxeResult.size());
        assertEquals("STAYCATION", luxeResult.get(0).getId());
        assertEquals("STAYCATION", nonLuxeResult.get(0).getId());
    }

    @Test
    public void should_HandlePolyglotServiceException_When_TranslationFails() {
        // Given
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        RatePlan ratePlanOrchV2 = createRatePlanWithStaycationDeal();
        String funnelSource = "GETAWAY";
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        boolean isLuxeHotel = false;

        when(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"))
            .thenThrow(new RuntimeException("Translation service unavailable"));

        // When & Then - Should propagate exception (no try-catch in original code)
        try {
            searchRoomsPersuasionHelper.getRatePlanPersuasion(
                ratePlan, ratePlanOrchV2, funnelSource, commonModifierResponse, isLuxeHotel);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException e) {
            assertEquals("Translation service unavailable", e.getMessage());
        }
    }

    // ==================== buildRatePlanPersuasionsMap Tests ====================

    @Test
    public void should_ReturnEmptyMap_When_BuildRatePlanPersuasionsMapWithValidInputs() {
        // Given
        RatePlan ratePlan = new RatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();

        // When
        Map<String, PersuasionResponse> result = searchRoomsPersuasionHelper
            .buildRatePlanPersuasionsMap(ratePlan, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty()); // Method returns empty map as per TODO comment in GI code
    }

    @Test
    public void should_ReturnEmptyMap_When_BuildRatePlanPersuasionsMapWithNullRatePlan() {
        // Given
        RatePlan ratePlan = null;
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();

        // When
        Map<String, PersuasionResponse> result = searchRoomsPersuasionHelper
            .buildRatePlanPersuasionsMap(ratePlan, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyMap_When_BuildRatePlanPersuasionsMapWithNullModifierResponse() {
        // Given
        RatePlan ratePlan = new RatePlan();
        CommonModifierResponse commonModifierResponse = null;

        // When
        Map<String, PersuasionResponse> result = searchRoomsPersuasionHelper
            .buildRatePlanPersuasionsMap(ratePlan, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyMap_When_BuildRatePlanPersuasionsMapWithBothNull() {
        // Given
        RatePlan ratePlan = null;
        CommonModifierResponse commonModifierResponse = null;

        // When
        Map<String, PersuasionResponse> result = searchRoomsPersuasionHelper
            .buildRatePlanPersuasionsMap(ratePlan, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // ==================== Helper Methods for Test Data Creation ====================

    private RatePlan createRatePlanWithStaycationDeal() {
        RatePlan ratePlan = new RatePlan();
        RatePlanFlags flags = new RatePlanFlags();
        flags.setPackageRatePlan(true);
        flags.setStaycationDeal(true);
        ratePlan.setRatePlanFlags(flags);
        return ratePlan;
    }

    private RatePlan createRatePlanWithoutStaycationDeal() {
        RatePlan ratePlan = new RatePlan();
        RatePlanFlags flags = new RatePlanFlags();
        flags.setPackageRatePlan(true);
        flags.setStaycationDeal(false);
        ratePlan.setRatePlanFlags(flags);
        return ratePlan;
    }

    private RatePlan createRatePlanWithStaycationDealButNotPackage() {
        RatePlan ratePlan = new RatePlan();
        RatePlanFlags flags = new RatePlanFlags();
        flags.setPackageRatePlan(false);
        flags.setStaycationDeal(true);
        ratePlan.setRatePlanFlags(flags);
        return ratePlan;
    }
} 