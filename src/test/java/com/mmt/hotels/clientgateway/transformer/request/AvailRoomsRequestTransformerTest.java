package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsRequestTransformerTest {

    @InjectMocks
    AvailRoomsRequestTransformer availRoomsRequestTransformer;
    
    @Mock
    CommonHelper commonHelper;

    @Spy
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;

    @Test
    public void testConvertAvailRoomsRequest(){
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());

        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        availRoomsRequest.setExpData("abc");


        availRoomsRequest.setRequestDetails(new RequestDetails());
        availRoomsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        availRoomsRequest.getRequestDetails().setLoggedIn(false);
        availRoomsRequest.getRequestDetails().setSrLng(10d);
        availRoomsRequest.getRequestDetails().setSrLat(10d);
        availRoomsRequest.getRequestDetails().setCouponCount(2);

        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setRoomCriteria(new ArrayList<AvailRoomsSearchCriteria>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().add(new AvailRoomsSearchCriteria());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().add(new RoomStayCandidate());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).getChildAges().add(1);

        availRoomsRequest.setEmiDetail(new EMIDetail());
        PriceByHotelsRequestBody priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, new CommonModifierResponse());
        Assert.notNull(priceByHotelsRequestBody);
        Map<Object,Object> map = new LinkedHashMap<>();
        map.put("trv_reference","sessionKey");
        map.put("tag_one", 5);
        map.put("tag_two", false);
        map.put("tag_three", new LinkedHashMap<String, String>());
        ((LinkedHashMap) map.get("tag_three")).put("trivago", "booking");
        availRoomsRequest.getSearchCriteria().setMetaChannelInfo(map);
        priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, new CommonModifierResponse());
        Assert.notNull(priceByHotelsRequestBody);
        Assert.notNull(priceByHotelsRequestBody.getMetaChannelInfo());
        Assert.notNull(((LinkedHashMap)priceByHotelsRequestBody.getMetaChannelInfo()).get("trv_reference"));
    }

    @Test
    public void testBuildAppliedFilterMapForAvail_EmptyFilters() {
        // Test with empty filter list
        Map<com.mmt.hotels.filter.FilterGroup, Set<Filter>> result = availRoomsRequestTransformer.buildAppliedFilterMapForAvail(null);
        Assert.isNull(result);
        
        result = availRoomsRequestTransformer.buildAppliedFilterMapForAvail(new ArrayList<>());
        Assert.isNull(result);
    }
    
    @Test
    public void testBuildAppliedFilterMapForAvail_WithFilters() {
        // Create test filters
        List<com.mmt.hotels.clientgateway.request.Filter> filters = new ArrayList<>();
        
        // Filter 1: AMENITIES filter
        com.mmt.hotels.clientgateway.request.Filter filter1 = new com.mmt.hotels.clientgateway.request.Filter();
        filter1.setFilterGroup(FilterGroup.AMENITIES);
        filter1.setFilterValue("SWIMMING_POOL");
        filter1.setRangeFilter(false);
        filters.add(filter1);
        
        // Filter 2: HOTEL_PRICE range filter
        com.mmt.hotels.clientgateway.request.Filter filter2 = new com.mmt.hotels.clientgateway.request.Filter();
        filter2.setFilterGroup(FilterGroup.HOTEL_PRICE);
        filter2.setFilterValue("1000-2000");
        filter2.setRangeFilter(true);
        
        com.mmt.hotels.clientgateway.request.FilterRange filterRange = new com.mmt.hotels.clientgateway.request.FilterRange();
        filterRange.setMinValue(1000);
        filterRange.setMaxValue(2000);
        filter2.setFilterRange(filterRange);
        filters.add(filter2);
        
        // Test the method
        Map<com.mmt.hotels.filter.FilterGroup, Set<Filter>> result = availRoomsRequestTransformer.buildAppliedFilterMapForAvail(filters);
        
        // Verify results
        Assert.notNull(result);
        Assert.isTrue(result.size() == 2, "Expected 2 filter groups");
        
        // Verify AMENITIES filter
        Assert.isTrue(result.containsKey(com.mmt.hotels.filter.FilterGroup.AMENITIES), "AMENITIES filter group not found");
        Set<Filter> amenitiesFilters = result.get(com.mmt.hotels.filter.FilterGroup.AMENITIES);
        Assert.isTrue(amenitiesFilters.size() == 1, "Expected 1 AMENITIES filter");
        Filter amenityFilter = amenitiesFilters.iterator().next();
        Assert.isTrue(amenityFilter.getFilterGroup() == com.mmt.hotels.filter.FilterGroup.AMENITIES, "Filter group mismatch");
        Assert.isTrue("SWIMMING_POOL".equals(amenityFilter.getFilterValue()), "Filter value mismatch");
        Assert.isTrue(!amenityFilter.isRangeFilter(), "Range filter flag mismatch");
        
        // Verify HOTEL_PRICE filter
        Assert.isTrue(result.containsKey(com.mmt.hotels.filter.FilterGroup.HOTEL_PRICE), "HOTEL_PRICE filter group not found");
        Set<Filter> priceFilters = result.get(com.mmt.hotels.filter.FilterGroup.HOTEL_PRICE);
        Assert.isTrue(priceFilters.size() == 1, "Expected 1 HOTEL_PRICE filter");
        Filter priceFilter = priceFilters.iterator().next();
        Assert.isTrue(priceFilter.getFilterGroup() == com.mmt.hotels.filter.FilterGroup.HOTEL_PRICE, "Filter group mismatch");
        Assert.isTrue("1000-2000".equals(priceFilter.getFilterValue()), "Filter value mismatch");
        Assert.isTrue(priceFilter.isRangeFilter(), "Range filter flag mismatch");
        Assert.isTrue(priceFilter.getFilterRange() != null, "Filter range should not be null");
        Assert.isTrue(priceFilter.getFilterRange().getMinValue() == 1000, "Min value mismatch");
        Assert.isTrue(priceFilter.getFilterRange().getMaxValue() == 2000, "Max value mismatch");
    }
    
    @Test
    public void testBuildAppliedFilterMapForAvail_MultipleFiltersInSameGroup() {
        // Create test filters in the same group
        List<com.mmt.hotels.clientgateway.request.Filter> filters = new ArrayList<>();
        
        // Filter 1: AMENITIES filter - SWIMMING_POOL
        com.mmt.hotels.clientgateway.request.Filter filter1 = new com.mmt.hotels.clientgateway.request.Filter();
        filter1.setFilterGroup(FilterGroup.AMENITIES);
        filter1.setFilterValue("SWIMMING_POOL");
        filter1.setRangeFilter(false);
        filters.add(filter1);
        
        // Filter 2: AMENITIES filter - WIFI
        com.mmt.hotels.clientgateway.request.Filter filter2 = new com.mmt.hotels.clientgateway.request.Filter();
        filter2.setFilterGroup(FilterGroup.AMENITIES);
        filter2.setFilterValue("WIFI");
        filter2.setRangeFilter(false);
        filters.add(filter2);
        
        // Test the method
        Map<com.mmt.hotels.filter.FilterGroup, Set<Filter>> result = availRoomsRequestTransformer.buildAppliedFilterMapForAvail(filters);
        
        // Verify results
        Assert.notNull(result);
        Assert.isTrue(result.size() == 1, "Expected 1 filter group");
        
        // Verify AMENITIES filter has multiple entries
        Assert.isTrue(result.containsKey(com.mmt.hotels.filter.FilterGroup.AMENITIES), "AMENITIES filter group not found");
        Set<Filter> amenitiesFilters = result.get(com.mmt.hotels.filter.FilterGroup.AMENITIES);
        Assert.isTrue(amenitiesFilters.size() == 2, "Expected 2 AMENITIES filters");
        
        // Verify the filter values are present
        boolean foundWifi = false;
        boolean foundSwimmingPool = false;
        
        for (Filter filter : amenitiesFilters) {
            if ("WIFI".equals(filter.getFilterValue())) {
                foundWifi = true;
            } else if ("SWIMMING_POOL".equals(filter.getFilterValue())) {
                foundSwimmingPool = true;
            }
        }
        
        Assert.isTrue(foundWifi, "WIFI filter not found");
        Assert.isTrue(foundSwimmingPool, "SWIMMING_POOL filter not found");
    }
    
    @Test
    public void testGetOldFilterFromNewFilter() {
        // Create a client gateway filter
        com.mmt.hotels.clientgateway.request.Filter newFilter = new com.mmt.hotels.clientgateway.request.Filter();
        newFilter.setFilterGroup(FilterGroup.HOTEL_PRICE);
        newFilter.setFilterValue("1000-2000");
        newFilter.setRangeFilter(true);
        
        com.mmt.hotels.clientgateway.request.FilterRange filterRange = new com.mmt.hotels.clientgateway.request.FilterRange();
        filterRange.setMinValue(1000);
        filterRange.setMaxValue(2000);
        newFilter.setFilterRange(filterRange);
        
        // Convert to core filter
        Filter oldFilter = availRoomsRequestTransformer.getOldFilterFromNewFilter(newFilter, com.mmt.hotels.filter.FilterGroup.HOTEL_PRICE);
        
        // Verify conversion
        Assert.notNull(oldFilter);
        Assert.isTrue(oldFilter.getFilterGroup() == com.mmt.hotels.filter.FilterGroup.HOTEL_PRICE, "Filter group mismatch");
        Assert.isTrue("1000-2000".equals(oldFilter.getFilterValue()), "Filter value mismatch");
        Assert.isTrue(oldFilter.isRangeFilter(), "Range filter flag mismatch");
        
        // Verify filter range was copied
        Assert.notNull(oldFilter.getFilterRange());
        Assert.isTrue(oldFilter.getFilterRange().getMinValue() == 1000, "Min value mismatch");
        Assert.isTrue(oldFilter.getFilterRange().getMaxValue() == 2000, "Max value mismatch");
    }
    
    @Test
    public void testGetOldFilterFromNewFilter_WithoutRange() {
        // Create a client gateway filter without range
        com.mmt.hotels.clientgateway.request.Filter newFilter = new com.mmt.hotels.clientgateway.request.Filter();
        newFilter.setFilterGroup(FilterGroup.AMENITIES);
        newFilter.setFilterValue("SWIMMING_POOL");
        newFilter.setRangeFilter(false);
        
        // Convert to core filter
        Filter oldFilter = availRoomsRequestTransformer.getOldFilterFromNewFilter(newFilter, com.mmt.hotels.filter.FilterGroup.AMENITIES);
        
        // Verify conversion
        Assert.notNull(oldFilter);
        Assert.isTrue(oldFilter.getFilterGroup() == com.mmt.hotels.filter.FilterGroup.AMENITIES, "Filter group mismatch");
        Assert.isTrue("SWIMMING_POOL".equals(oldFilter.getFilterValue()), "Filter value mismatch");
        Assert.isTrue(!oldFilter.isRangeFilter(), "Range filter flag mismatch");
        Assert.isNull(oldFilter.getFilterRange(), "Filter range should be null");
    }
}
