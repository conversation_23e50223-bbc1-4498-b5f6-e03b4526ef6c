package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.BookingModRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.BookingModResponseTransformer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

@RunWith(MockitoJUnitRunner.class)
public class BookingModificationFactoryTest {

    @InjectMocks
    BookingModificationFactory bkgModFactory;

    @Mock
    BookingModRequestTransformer bookingModRequestTransformer;

    @Mock
    BookingModResponseTransformer bookingModResponseTransformer;

    @Test
    public void  getRequestServiceTest(){
        Assert.assertTrue(bkgModFactory.getRequestService("") instanceof  BookingModRequestTransformer);
        Assert.assertTrue(bkgModFactory.getRequestService("ANDROID") instanceof  BookingModRequestTransformer);
        Assert.assertTrue(bkgModFactory.getRequestService("IOS") instanceof  BookingModRequestTransformer);
        Assert.assertTrue(bkgModFactory.getRequestService("PWA") instanceof  BookingModRequestTransformer);
        Assert.assertTrue(bkgModFactory.getRequestService("DESKTOP") instanceof  BookingModRequestTransformer);
        Assert.assertTrue(bkgModFactory.getRequestService("MSITE") instanceof  BookingModRequestTransformer);
    }

    @Test
    public void  getResponseServiceTest(){
        Assert.assertTrue(bkgModFactory.getResponseService("") instanceof  BookingModResponseTransformer);
        Assert.assertTrue(bkgModFactory.getResponseService("ANDROID") instanceof  BookingModResponseTransformer);
        Assert.assertTrue(bkgModFactory.getResponseService("DESKTOP") instanceof  BookingModResponseTransformer);
        Assert.assertTrue(bkgModFactory.getResponseService("IOS") instanceof  BookingModResponseTransformer);
        Assert.assertTrue(bkgModFactory.getResponseService("PWA") instanceof  BookingModResponseTransformer);
        Assert.assertTrue(bkgModFactory.getResponseService("MSITE") instanceof  BookingModResponseTransformer);
        Assert.assertTrue(bkgModFactory.getResponseService(null) instanceof  BookingModResponseTransformer);
    }


}
