package com.mmt.hotels.clientgateway.transformer.request;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.payment.AdditionalCheckoutInfo;
import com.mmt.hotels.clientgateway.request.payment.PaymentRequestClient;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.payment.AddressDetails;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class PaymentRequestTransformerTest {

    @InjectMocks
    PaymentRequestTransformer prTransformer;

    @Mock
    PaymentHelper payHelper;

    @Spy
    CommonHelper commonHelper;

    @Spy
    Utility utility;

    @Before
    public void init(){
        ReflectionTestUtils.setField(payHelper,"commonHelper",commonHelper);
    }

    @Test
    public void modifyPaymentRequestTest() throws  Exception{
        PaymentRequestClient beginCheckoutReqBody = new Gson().fromJson("{\"alternateCurrencySelected\":false,\"continueWithoutAddOn\":false,\"correlationKey\":\"9ea4da78-4b84-4c06-86c8-6fea21703266\",\"currency\":\"INR\",\"doubleBlackValidated\":false,\"emiAvailable\":false,\"authenticationDetail\":{\"otpDetail\":{\"key\":\"\",\"otp\":\"\"}},\"errorConfig\":{\"sessionTimeoutMins\":30,\"sessionTimeoutURL\":\"http://hotelz.makemytrip.com/makemytrip/site/hotels/errors/sessionExpiredError\"},\"idContext\":\"MOB\",\"mmtPrime\":false,\"paymentDetail\":{\"channel\":\"Native\",\"isBNPL\":false,\"mode\":\"PAS\",\"emiDetails\":{\"payoption\":\"EMI_HDFC_3\"}},\"recheckRequired\":false,\"registerForBlack\":false,\"requiredOtpValidation\":false,\"transactionKey\":\"61a1f1c6-5b88-4c2f-8918-04d9debe3dd1\",\"travellerDetails\":[{\"emailID\":\"<EMAIL>\",\"firstName\":\"anuh\",\"lastName\":\"kumar\",\"masterPax\":true,\"isdCode\":\"91\",\"mobileNo\":\"8929089026\",\"paxType\":\"ADULT\",\"title\":\"Mr\"}],\"specialRequest\":{\"categories\":[{\"code\":\"101\"},{\"code\":\"102\",\"subCategories\":[{\"code\":\"1021\",\"values\":[\"05:00 PM\"]}]},{\"code\":\"103\",\"subCategories\":[{\"code\":\"1031\",\"values\":[\"07:00 AM\"]}]},{\"code\":\"104\"},{\"code\":\"109\",\"values\":[\"dummy dummy\"]}]},\"visitNumber\":\"1\"}"
                ,PaymentRequestClient.class);
        beginCheckoutReqBody.setAdditionalCheckoutInfo(new AdditionalCheckoutInfo());
        beginCheckoutReqBody.getAdditionalCheckoutInfo().setBookingUrl("url");
        beginCheckoutReqBody.getAdditionalCheckoutInfo().setMetaInfo(new HashMap<>());
        beginCheckoutReqBody.getAdditionalCheckoutInfo().getMetaInfo().put("ABC","123");

        Mockito.when(payHelper.checkUserOTPValidated(Mockito.any())).thenReturn(true);
        // Mockito.doCallRealMethod().when(payHelper).getGenderFromTitle(Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyTravelerDetails(Mockito.any(BeginCheckoutReqBody.class));
        Mockito.doCallRealMethod().when(payHelper).getPhoneCommId(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(MockHttpServletRequest.class));
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(Map.class));
        Mockito.doCallRealMethod().when(payHelper).sanitizeTimeOutURL(Mockito.any());
        BeginCheckoutReqBody modifiedBody =  prTransformer.modifyPaymentRequest(beginCheckoutReqBody,new MockHttpServletRequest(), "PWA");
        Assert.assertNotNull(modifiedBody);
        Assert.assertNotNull(modifiedBody.getPaymentDetail());
        Assert.assertNotNull(modifiedBody.getPaymentDetail().getEmiDetails());
        Assert.assertNotNull(modifiedBody.getErrorConfig());
        Assert.assertNotNull(modifiedBody.getSpecialRequest());
        Assert.assertNotNull(modifiedBody.getSpecialRequest().getCategories());
        Assert.assertNotNull(modifiedBody.getAuthenticationDetail());
        Assert.assertNotNull(modifiedBody.getAuthenticationDetail().getOtpDetail());
        Assert.assertNotNull(modifiedBody.getAdditionalCheckoutInfo());
        Assert.assertNotNull(modifiedBody.getAdditionalCheckoutInfo().getMetaInfo().get("ABC"));
        Assert.assertNotNull(modifiedBody.getTravelerDetailsList());
    }

    @Test
    public void modifyPaymentRequestGstnDetailTest() throws  Exception{
        PaymentRequestClient beginCheckoutReqBody = new Gson().fromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:gstaddresschange/paymentCheckoutWithAddress.json"), "UTF-8")
                ,PaymentRequestClient.class);
        Mockito.when(payHelper.checkUserOTPValidated(Mockito.any())).thenReturn(true);
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(MockHttpServletRequest.class));
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(Map.class));
        // Request has gstn details
        BeginCheckoutReqBody modifiedBody =  prTransformer.modifyPaymentRequest(beginCheckoutReqBody,new MockHttpServletRequest(), "DESKTOP");
        Assert.assertNotNull(modifiedBody.getAddressDetails());
        // Response from user service has gstn details
        Mockito.doCallRealMethod().when(payHelper).populateAndCheckUserData(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).populateUUID(Mockito.any(),Mockito.any());
        Mockito.when(payHelper.getUserServiceResponse(Mockito.any(), Mockito.any())).thenReturn(getUserResponseServiceMockResponse());
        modifiedBody =  prTransformer.modifyPaymentRequest(beginCheckoutReqBody,new MockHttpServletRequest(), "DESKTOP");
        Assert.assertNotNull(modifiedBody);
        Assert.assertNotNull(modifiedBody.getAddressDetails().getState());
    }


    @Test(expected = ClientGatewayException.class)
    public void modifyPaymentRequestExceptionTest() throws  Exception {
        PaymentRequestClient beginCheckoutReqBody = new Gson().fromJson("{\"alternateCurrencySelected\":false,\"continueWithoutAddOn\":false,\"correlationKey\":\"9ea4da78-4b84-4c06-86c8-6fea21703266\",\"currency\":\"INR\",\"doubleBlackValidated\":false,\"emiAvailable\":false,\"authenticationDetail\":{\"otpDetail\":{\"key\":\"\",\"otp\":\"\"}},\"errorConfig\":{\"sessionTimeoutMins\":30,\"sessionTimeoutURL\":\"http://hotelz.makemytrip.com/makemytrip/site/hotels/errors/sessionExpiredError\"},\"idContext\":\"MOB\",\"mmtPrime\":false,\"paymentDetail\":{\"channel\":\"Native\",\"isBNPL\":false,\"mode\":\"PAS\",\"emiDetails\":{\"payoption\":\"EMI_HDFC_3\"}},\"recheckRequired\":false,\"registerForBlack\":false,\"requiredOtpValidation\":false,\"transactionKey\":\"61a1f1c6-5b88-4c2f-8918-04d9debe3dd1\",\"travellerDetails\":[{\"emailID\":\"<EMAIL>\",\"firstName\":\"anuh\",\"lastName\":\"kumar\",\"masterPax\":true,\"isdCode\":\"91\",\"mobileNo\":\"8929089026\",\"paxType\":\"ADULT\",\"title\":\"Mr\"}],\"specialRequest\":{\"categories\":[{\"code\":\"101\"},{\"code\":\"102\",\"subCategories\":[{\"code\":\"1021\",\"values\":[\"05:00 PM\"]}]},{\"code\":\"103\",\"subCategories\":[{\"code\":\"1031\",\"values\":[\"07:00 AM\"]}]},{\"code\":\"104\"},{\"code\":\"109\",\"values\":[\"dummy dummy\"]}]},\"visitNumber\":\"1\"}"
                , PaymentRequestClient.class);
        beginCheckoutReqBody.setAdditionalCheckoutInfo(new AdditionalCheckoutInfo());
        beginCheckoutReqBody.getAdditionalCheckoutInfo().setBookingUrl("url");
        beginCheckoutReqBody.getAdditionalCheckoutInfo().setMetaInfo(new HashMap<>());
        beginCheckoutReqBody.getAdditionalCheckoutInfo().getMetaInfo().put("ABC", "123");

        Mockito.when(payHelper.checkUserOTPValidated(Mockito.any())).thenReturn(false);
        Mockito.doCallRealMethod().when(payHelper).modifyTravelerDetails(Mockito.any(BeginCheckoutReqBody.class));
        // Mockito.doCallRealMethod().when(payHelper).getGenderFromTitle(Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).getPhoneCommId(Mockito.any(), Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(MockHttpServletRequest.class));
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(Map.class));

        BeginCheckoutReqBody modifiedBody = prTransformer.modifyPaymentRequest(beginCheckoutReqBody, new MockHttpServletRequest(), "PWA");
    }

    @Test(expected = ClientGatewayException.class)
    public void modifyPaymentRequestGstnDetailExceptionTest() throws Exception{
        PaymentRequestClient beginCheckoutReqBody = new Gson().fromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:gstaddresschange/paymentCheckoutWithoutAddress.json"), "UTF-8")
                ,PaymentRequestClient.class);
        Mockito.doCallRealMethod().when(payHelper).populateAndCheckUserData(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(MockHttpServletRequest.class));
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(Map.class));
        BeginCheckoutReqBody modifiedBody =  prTransformer.modifyPaymentRequest(beginCheckoutReqBody,new MockHttpServletRequest(), "PWA");
    }

    @Test
    public void modifyPaymentRequestOldTest() throws  Exception{
        BeginCheckoutReqBody beginCheckoutReqBody = new Gson().fromJson("{\"alternateCurrencySelected\":false,\"continueWithoutAddOn\":false,\"correlationKey\":\"9ea4da78-4b84-4c06-86c8-6fea21703266\",\"currency\":\"INR\",\"doubleBlackValidated\":false,\"emiAvailable\":false,\"authenticationDetail\":{\"otpDetail\":{\"key\":\"\",\"otp\":\"\"}},\"errorConfig\":{\"sessionTimeoutMins\":30,\"sessionTimeoutURL\":\"http://hotelz.makemytrip.com/makemytrip/site/hotels/errors/sessionExpiredError\"},\"idContext\":\"MOB\",\"mmtPrime\":false,\"paymentDetail\":{\"channel\":\"Native\",\"isBNPL\":false,\"mode\":\"PAS\",\"emiDetails\":{\"payoption\":\"EMI_HDFC_3\"}},\"recheckRequired\":false,\"registerForBlack\":false,\"requiredOtpValidation\":false,\"transactionKey\":\"61a1f1c6-5b88-4c2f-8918-04d9debe3dd1\",\"travelerDetail\":{\"emailID\":\"<EMAIL>\",\"firstName\":\"anuh\",\"lastName\":\"kumar\",\"masterPax\":true,\"isdCode\":\"91\",\"mobileNo\":\"8929089026\",\"paxType\":\"ADULT\",\"title\":\"Mr\"},\"specialRequest\":{\"categories\":[{\"code\":\"101\"},{\"code\":\"102\",\"subCategories\":[{\"code\":\"1021\",\"values\":[\"05:00 PM\"]}]},{\"code\":\"103\",\"subCategories\":[{\"code\":\"1031\",\"values\":[\"07:00 AM\"]}]},{\"code\":\"104\"},{\"code\":\"109\",\"values\":[\"dummy dummy\"]}]},\"visitNumber\":\"1\"}"
                ,BeginCheckoutReqBody.class);

        Mockito.when(payHelper.checkUserOTPValidated(Mockito.any())).thenReturn(true);
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(MockHttpServletRequest.class));
         Mockito.doCallRealMethod().when(payHelper).getGenderFromTitle(Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).getPhoneCommId(Mockito.any(),Mockito.any());
        Mockito.doCallRealMethod().when(payHelper).modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class),Mockito.any(Map.class));
        Mockito.doCallRealMethod().when(payHelper).modifyTravelerDetails(Mockito.any(BeginCheckoutReqBody.class));

        BeginCheckoutReqBody modifiedBody =  prTransformer.modifyPaymentRequest(beginCheckoutReqBody,new MockHttpServletRequest());
        Assert.assertNotNull(modifiedBody);
        Assert.assertNotNull(modifiedBody.getPaymentDetail());
        Assert.assertNotNull(modifiedBody.getPaymentDetail().getEmiDetails());
        Assert.assertNotNull(modifiedBody.getErrorConfig());
        Assert.assertNotNull(modifiedBody.getSpecialRequest());
        Assert.assertNotNull(modifiedBody.getSpecialRequest().getCategories());
        Assert.assertNotNull(modifiedBody.getAuthenticationDetail());
        Assert.assertNotNull(modifiedBody.getAuthenticationDetail().getOtpDetail());
        Assert.assertNotNull(modifiedBody.getTravelerDetailsList());
    }

    private UserServiceResponse getUserResponseServiceMockResponse(){
        UserServiceResponse response = new UserServiceResponse();
        UserServiceResult result = new UserServiceResult();
        ExtendedUser extendedUser = new ExtendedUser();
        AddressDetails addressDetails = new AddressDetails();
        addressDetails.setState("kerala");
        addressDetails.setAddress1("test address");
        addressDetails.setPostalCode("972934");
        addressDetails.setCity("Idukki");
        extendedUser.setAddressDetails(Collections.singletonList(addressDetails));
        extendedUser.setUuid("992734AD");
        result.setExtendedUser(extendedUser);
        response.setResult(result);
        return response;
    }
}
