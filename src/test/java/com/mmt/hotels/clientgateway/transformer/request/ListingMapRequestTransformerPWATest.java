package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.transformer.request.pwa.ListingMapRequestTransformerPWA;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.ArrayList;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class ListingMapRequestTransformerPWATest {

    @InjectMocks
    ListingMapRequestTransformerPWA listingMapRequestTransformerPWA;

    @Mock
    Utility utility;

    @Test
    public void testConvertListingMapRequest() {
        ListingMapRequest listingMapRequest = new ListingMapRequest();
        listingMapRequest.setDeviceDetails(new DeviceDetails());

        listingMapRequest.setRequestDetails(new RequestDetails());
        listingMapRequest.getRequestDetails().setSrLat(28d);
        listingMapRequest.getRequestDetails().setSrLng(28d);

        listingMapRequest.setSearchCriteria(new SearchHotelsCriteria());
        listingMapRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        listingMapRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        listingMapRequest.getSearchCriteria().setLat(28d);
        listingMapRequest.getSearchCriteria().setLng(28d);

        listingMapRequest.setImageDetails(new ImageDetails());
        listingMapRequest.getImageDetails().setCategories(new ArrayList<ImageCategory>());
        listingMapRequest.getImageDetails().getCategories().add(new ImageCategory());
        listingMapRequest.getImageDetails().getCategories().get(0).setCount(2);
        listingMapRequest.getImageDetails().getCategories().get(0).setHeight(2);
        listingMapRequest.getImageDetails().getCategories().get(0).setWidth(2);

        listingMapRequest.setFeatureFlags(new FeatureFlags());

        listingMapRequest.setSortCriteria(new SortCriteria());

        listingMapRequest.getRequestDetails().setTrafficSource(new TrafficSource());

        listingMapRequest.setMapDetails(new MapDetails());
        listingMapRequest.getMapDetails().setLatLngBounds(new LatLngBounds());

        listingMapRequest.setMatchMakerDetails(new MatchMakerRequest());

        listingMapRequest.setExpData("{APE:36,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,EMIDT:2}");

        SearchWrapperInputRequest searchWrapperInputRequest = listingMapRequestTransformerPWA.convertListingMapRequest(listingMapRequest, new CommonModifierResponse());
        Assert.notNull(searchWrapperInputRequest);
    }

}
