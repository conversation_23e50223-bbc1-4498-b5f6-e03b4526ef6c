package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.SearchHotelsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchHotelsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.SearchHotelsRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.SearchHotelsRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchHotelsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SearchHotelsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchHotelsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsFactoryTest {


    @InjectMocks
    SearchHotelsFactory searchHotelsFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsResponseTransformerPWA" , new SearchHotelsResponseTransformerPWA());
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsResponseTransformerDesktop" , new SearchHotelsResponseTransformerDesktop());
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsResponseTransformerAndroid" , new SearchHotelsResponseTransformerAndroid());
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsResponseTransformerIOS" , new SearchHotelsResponseTransformerIOS());
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsRequestTransformerPWA" , new SearchHotelsRequestTransformerPWA());
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsRequestTransformerDesktop" , new SearchHotelsRequestTransformerDesktop());
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsRequestTransformerAndroid" , new SearchHotelsRequestTransformerAndroid());
        ReflectionTestUtils.setField(searchHotelsFactory,"searchHotelsRequestTransformerIOS" , new SearchHotelsRequestTransformerIOS());
        
    }
    
    @Test
    public void getRequestServiceTest(){
        SearchHotelsRequestTransformer resp = searchHotelsFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof SearchHotelsRequestTransformerPWA  );
        resp = searchHotelsFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof SearchHotelsRequestTransformerDesktop  );
        resp = searchHotelsFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof SearchHotelsRequestTransformerAndroid  );
        resp = searchHotelsFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof SearchHotelsRequestTransformerIOS  );
        resp = searchHotelsFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(searchHotelsFactory.getRequestService("test") instanceof SearchHotelsRequestTransformerDesktop);
    }

    @Test
    public void getResponseServiceTest(){
        SearchHotelsResponseTransformer resp = searchHotelsFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof SearchHotelsResponseTransformerPWA  );
        resp = searchHotelsFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof SearchHotelsResponseTransformerDesktop  );
        resp = searchHotelsFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof SearchHotelsResponseTransformerAndroid  );
        resp = searchHotelsFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof SearchHotelsResponseTransformerIOS  );
        resp = searchHotelsFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(searchHotelsFactory.getResponseService("test") instanceof  SearchHotelsResponseTransformerDesktop);
    }

}
