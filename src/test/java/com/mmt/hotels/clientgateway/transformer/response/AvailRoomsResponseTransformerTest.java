package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.transformer.response.desktop.AvailRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.addon.LOB;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.addon.InsuranceDetails;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.persuasion.HotelTag;
import com.mmt.hotels.model.response.persuasion.HotelTagType;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.TotalPricing;
import com.mmt.hotels.model.response.prime.DoubleBlackValidateResponse;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.model.EMIAbridgeResponse;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import com.mmt.model.HtlRmInfo;
import com.mmt.model.RoomInfo;
import com.mmt.model.SleepingArrangement;
import com.mmt.propertymanager.config.PropertyManager;
import junit.framework.Assert;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsResponseTransformerTest {


    @InjectMocks
    AvailRoomsResponseTransformerDesktop availRoomsResponseTransformerDesktop;

    @InjectMocks
    CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;

    @Mock
    PropertyManager propManager;;

    @Mock
    DateUtil dateUtil;

    String expString = "{\"APE\":\"10\",\"PAH\":\"5\",\"PAH5\":\"T\",\"WPAH\":\"F\",\"BNPL\":\"t\",\"MRS\":\"T\",\"PDO\":\"PN\",\"MCUR\":\"T\",\"ADDON\":\"T\",\"CHPC\":\"T\",\"AARI\":\"T\",\"NLP\":\"Y\",\"RCPN\":\"T\",\"PLRS\":\"T\",\"MMRVER\":\"V3\",\"BLACK\":\"T\",\"IAO\":\"T\",\"BNPL0\":\"t\",\"EMIDT\":\"1\",\"NEWTY\":\"T\",\"AIP\":\"T\",\"TFT\":\"T\",\"GEC\":\"A\",\"SRRP\":\"T\",\"new_user_pf_ih\":\"0\",\"streaks_video\":\"false\",\"appUpgrade\":\"false\",\"charity_preselect\":\"2\",\"dweb_transit_dh\":\"true\",\"details_budget\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"overViewQuickBook\\\",\\\"goTribeoffer\\\",\\\"quickBook\\\",\\\"featuredReviews\\\",\\\"hostCell\\\",\\\"exploreInfoCard\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"preferredBy\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"villaRule\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\",\\\"similarHotels\\\"]\",\"new_user_review\":\"2\",\"new_user_comm\":\"2\",\"mweb_truecaller_login_hotels\":\"0\",\"details_soldout\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"goTribeoffer\\\",\\\"changeDates\\\",\\\"alternateDates\\\",\\\"similarHotels\\\",\\\"featuredReviews\\\",\\\"hostCell\\\",\\\"exploreInfoCard\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"preferredBy\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"villaRule\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\"]\",\"upsell_dweb\":\"false\",\"h_unif_autos\":\"0\",\"hermes_hero_image\":\"1\",\"aaqb\":\"1\",\"bank_deal_dweb\":\"1\",\"Hotels_charity_show\":\"0\",\"promocode_pwa\":\"false\",\"augur_test_defaultonly\":\"{api=default, config=default}\",\"details_altacco\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"featuredReviews\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"goTribeoffer\\\",\\\"quickBook\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"overViewQuickBook\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"hostCell\\\",\\\"villaRule\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\",\\\"similarHotels\\\"]\",\"Mw2a_hotels_bottomsheet_typage\":\"0\",\"regionAutosuggestResults\":\"false\",\"dh_newtheme_BE\":\"false\",\"centralized_cache\":\"1\",\"streas_video\":\"{streaksvideo=false}\",\"bnplZeroVariant\":\"true\",\"autosuggest_mweb\":\"false\",\"thanku_revamp\":\"true\",\"aaproperty_config\":\"1\",\"valueOverview\":\"true\",\"Autosuggest_exp\":\"0\",\"pwa_login\":\"false\",\"hermes_bnpl_checkin\":\"1\",\"srp_revamp_be\":\"true\",\"external_rating\":\"1\",\"cpPreApply\":\"true\",\"gsdet\":\"false\",\"price_filter_config\":\"false\",\"dh_newtheme\":\"true\",\"upsell_mweb\":\"false\",\"hotels_charity_amount\":\"0\",\"dweb_new_payment\":\"false\",\"srp_cleanup\":\"false\",\"augur_shradha_testdefault\":\"{api=interspersion, config=default}\",\"qb_1room\":\"1\",\"autosuggestrating_dweb\":\"true\",\"autosuggest_exp1\":\"expscore1\",\"aastar\":\"1\",\"food_dt\":\"false\",\"dweb_login\":\"false\",\"enableMergedPropertyType\":\"true\",\"multiroom_dweb\":\"1\",\"dweb_videos\":\"false\",\"hotel_performance_matrix\":\"false\",\"new_bookAt1\":\"false\",\"details_v2_budget\":\"[\\\"headerCard\\\",\\\"paxInfo\\\",\\\"quickBook\\\",\\\"singleRatePlan\\\",\\\"govtPolicy\\\",\\\"indianness\\\",\\\"goStays\\\",\\\"offers\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"aboutHotel\\\",\\\"villaRule\\\",\\\"location\\\",\\\"inclusiveRate\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"similarHotels\\\"]\",\"near_me_exp\":\"3\",\"uni_flow\":\"1\",\"mWebHotelsPrefillDates\":\"1\",\"food_pwa\":\"false\",\"streaksvideo\":\"false\",\"bnpl_login_persuasion\":\"0\",\"Meta_skipRS\":\"1\",\"dweb_details_unif\":\"false\",\"pwa_recent_search\":\"0\",\"ShowFoodMenu\":\"false\",\"mwebNewLoginWidget\":\"false\",\"augur_test_shradha_l2r\":\"{api=l2r_rt, config=default, dsConfig={algoId=gi_l2r_v2, version=v2}}\",\"upsell_review\":\"1\",\"dateless_dweb\":\"0\",\"hotjar_mweb\":\"0\",\"test_SIBI_AA\":\"false\",\"qna_exp_mweb\":\"0\",\"pwa_video\":\"false\",\"taxExperiment\":\"taxamt\",\"details_v2_altacco\":\"[\\\"headerCard\\\",\\\"paxInfo\\\",\\\"quickBook\\\",\\\"singleRatePlan\\\",\\\"govtPolicy\\\",\\\"goStays\\\",\\\"offers\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"inclusiveRate\\\",\\\"aboutHotel\\\",\\\"location\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"villaRule\\\",\\\"similarHotels\\\"]\",\"alternate_dweb\":\"false\",\"autosuggest_dweb\":\"false\",\"hourD0\":\"14\",\"maldives_city_guide\":\"1\",\"new_to_dh\":\"false\",\"hourly_mweb2app_pwa\":\"0\",\"dweb_transit_ih\":\"true\",\"dspersv1_newprice\":\"false\",\"dateless_pwa\":\"0\",\"hourly_funnel_pwa\":\"0\",\"explicit_intent_new_user\":\"2\",\"DH_Price_Filter\":\"1\",\"transfer_exp_Intl\":\"2\",\"single_poi\":\"false\",\"h_unif_autos_mweb\":\"0\",\"be_upsell_review_hotel\":\"1\",\"regionAutosuggestResultsDweb\":\"false\",\"details_v2_soldout\":\"[\\\"headerCard\\\",\\\"changeDates\\\",\\\"similarHotels\\\",\\\"goStays”,”offers\\\",\\\"indianness\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"aboutHotel\\\",\\\"villaRule\\\",\\\"location\\\",\\\"inclusiveRate\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"govtPolicy\\\"]\",\"similarprop\":\"1\",\"budget_gostay\":\"2\",\"APEINTL\":\"36\",\"unificationDetailV2Dummy\":\"false\",\"BPG\":\"1\",\"IH_Price_Filter\":\"2\",\"dhqb\":\"1\",\"Prefillfilter\":\"0\",\"Explicit_Intent_IH\":\"false\",\"showChildBed\":\"2\",\"pflow\":\"b\",\"checkInD0\":\"true\",\"new_user_coachmarks\":\"2\",\"book_at_1_user_restriction\":\"true\",\"test_augur\":\"0\",\"details_v2_prem\":\"[\\\"headerCard\\\",\\\"paxInfo\\\",\\\"quickBook\\\",\\\"singleRatePlan\\\",\\\"govtPolicy\\\",\\\"goStays\\\",\\\"offers\\\",\\\"indianness\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"aboutHotel\\\",\\\"villaRule\\\",\\\"location\\\",\\\"inclusiveRate\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"similarHotels\\\"]\",\"showIndiannness\":\"true\",\"bank_deal_pwa\":\"1\",\"Hourly_intro_banner\":\"true\",\"dweb_transit\":\"false\",\"forceUpgrade\":\"false\",\"unificationDetailV2\":\"true\",\"bnplNewVariant\":\"false\",\"mwebNewPaymentsPage\":\"0\",\"bnpl_v2\":\"2\",\"streaks_hermes\":\"1\",\"hourly_hotels_funnel\":\"true\",\"web_autosuggest\":\"false\",\"Hourly_timesheet\":\"1\",\"multiroom_mweb\":\"1\",\"mweb_details_unif\":\"false\",\"Couple_Friendly_Intent\":\"true\",\"quickBook\":\"false\",\"details_prem\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"overViewQuickBook\\\",\\\"goTribeoffer\\\",\\\"quickBook\\\",\\\"hostCell\\\",\\\"exploreInfoCard\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"preferredBy\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"villaRule\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"featuredReviews\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\",\\\"similarHotels\\\"]\",\"autosuggestrating_mweb\":\"true\",\"dwebPrefilldate\":\"false\",\"featured_reviews\":\"false\",\"thanku_revamp2\":\"false\",\"thanku_revamp1\":\"false\",\"ALC\":\"F\",\"gocashPreApply\":\"true\"}";
    private Map<String, String> expData = null;
    @Before
    public void setup() {
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
            MockitoAnnotations.initMocks(this);
            Mockito.when(propManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(Mockito.mock(CommonConfig.class));
            this.availRoomsResponseTransformerDesktop.init();


        utility = Mockito.spy(new Utility());
        expData = buildExpData();
        MockitoAnnotations.initMocks(this);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        Map<String,Map<String,Map<String,String>>> ratePlanNameMap = new HashMap<>();
        ratePlanNameMap.put(Constants.DEFAULT,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_FC,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_BED_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_NR,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_BED_TYPE,"{NR}");

        ReflectionTestUtils.setField(commonResponseTransformer, "intlNrSupplierExclusionList", new ArrayList<>());
        ReflectionTestUtils.setField(commonResponseTransformer, "propertyRulesMaxCount", 4);
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "mealPlanMapPolyglot", new HashMap<>());
        ReflectionTestUtils.setField(commonResponseTransformer, "corpSegments", new HashSet<>(Arrays.asList("1135", "1152")));
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "groupBookingCardKeys", Arrays.asList("BEST_PRICE_GUARANTEE_CARD","POST_BOOKING_CARD"));
        ReflectionTestUtils.setField(utility, "ratePlanNameMap", ratePlanNameMap);
        ReflectionTestUtils.setField(utility, "ratePlanNameMapRedesign", ratePlanNameMap);
        ReflectionTestUtils.setField(utility,"apLimitForInclusionIcons",1);
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "rtbCardConfigs", new HashMap<>());
        Map<String, CardData> reviewPageCards = new HashMap<>();
        reviewPageCards.put("BEST_PRICE_GUARANTEE_CARD", new CardData());
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "reviewPageCards", reviewPageCards);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);
    }
    private Map<String, String> buildExpData() {
        Map<String, String> expDataMap = null;
        if (StringUtils.isNotBlank(expString)) {
            try {
                Type type = new TypeToken<Map<String, String>>() {
                }.getType();
                expString = expString.replaceAll("^\"|\"$", "");
                expDataMap = new Gson().fromJson(expString, type);
            } catch (Exception e) {
            }
        }
        return expDataMap;
    }
    @Test
    public void testAvailRoomsResponse(){

        HotelRates hotelRates = new HotelRates();
        hotelRates.setBnplBaseAmount(10d);
        hotelRates.setBlackEligible(false);
        hotelRates.setIsBNPLAvailable(false);
        hotelRates.setPahWalletApplicable(false);
        hotelRates.setPnAvlbl(true);
        hotelRates.setPanCardRequired(true);
        hotelRates.setShowFcBanner(true);
        hotelRates.setGroupBookingHotel(true);
        hotelRates.setSoldOut(false);
        hotelRates.setBreakFast(true);
        hotelRates.setFreeCancellation(true);
        hotelRates.setBreakFastAvailable(true);
        hotelRates.setFreeCancellationAvailable(true);
        hotelRates.setPAHTariffAvailable(true);
        hotelRates.setUserEligiblePayMode(PaymentMode.PAH_WITHOUT_CC);
        hotelRates.setAddressLines(new ArrayList<>());
        hotelRates.getAddressLines().add("Delhi");
        Map<String, RoomInfo> roomInfoMap = new HashMap<>();
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("300");
        roomInfo.setRoomSizeUnit(" sq.ft.");
        roomInfo.setBedInfoText("1 King Size Bed");
        roomInfoMap.put("1323434",roomInfo);
        hotelRates.setRoomInfo(roomInfoMap);
        hotelRates.setCheckInTime("2 PM");
        hotelRates.setCheckInTime("12 PM");
        hotelRates.setCheckInTimeRange("2 PM");
        hotelRates.setCheckOutTimeRange("12 PM");

        Map<String, HotelTag> hotelTagMap = new HashMap<>();
        HotelTag hotelTag = new HotelTag();
        hotelTag.background = "test";
        hotelTag.type = HotelTagType.DRIVING_DISTANCE;
        hotelTag.icon = "test";
        hotelTag.title = "test";
        hotelTagMap.put("test", hotelTag);
        hotelRates.setHotelTags(hotelTagMap);

        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getRoomTypeDetails().getTotalDisplayFare().setOtherCouponByPaymode(new HashMap<>());
        List<BestCoupon> otherCouponsList = new ArrayList<>();
        otherCouponsList.add(new BestCoupon());
        hotelRates.getRoomTypeDetails().getTotalDisplayFare().getOtherCouponByPaymode().put(PaymentMode.PAS.toString(), otherCouponsList);

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(0.5);
        displayPriceBreakDown.setBlackDiscount(0.5);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");
        displayPriceBreakDown.getCouponInfo().setBnplAllowed(false);

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(0.5);
        displayPriceBreakDownList.get(0).setBlackDiscount(0.5);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
        displayPriceBreakDownList.get(0).getCouponInfo().setBnplAllowed(false);

        hotelRates.getRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());

        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();

        ratePlanCB.setAvailDetails(new AvailDetails());
        ratePlanCB.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanCB.setCancelPenaltyList(new ArrayList<CancelPenalty>());
        ratePlanCB.getCancelPenaltyList().add(new CancelPenalty());
        ratePlanCB.getCancelPenaltyList().get(0).setPenaltyDescription(new Penalty());
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.getInclusions().get(0).setSegmentIdentifier("BLACK");
        ratePlanCB.setMealPlans(new ArrayList<MealPlan>());
        ratePlanCB.getMealPlans().add(new MealPlan());
        ratePlanCB.getMealPlans().get(0).setCode("abcd");
        ratePlanCB.getMealPlans().get(0).setValue("abcd");
        ratePlanCB.setPaymentDetails(new PaymentDetails());
        ratePlanCB.getPaymentDetails().setPaymentMode(PaymentMode.PAS);
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlanCB.getDisplayFare().getDisplayPriceBreakDown().setEmiDetails(new Emi());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        ratePlanCB.setSupplierDetails(new SupplierDetails());
        ratePlanCB.getSupplierDetails().setCostPrice(19d);
        ratePlanCB.setAddOns(new ArrayList<>());
        ratePlanCB.getAddOns().add(new AddOnNode());
        ratePlanCB.setCampaingText("Free cancellation till 24 hrs");
        hotelRates.setAddOns(new ArrayList<>());
        hotelRates.getAddOns().add(new AddOnNode());
        List<GenericCardPayloadData> addonDescList = new ArrayList<>();
        GenericCardPayloadData addonDesc = new GenericCardPayloadData();
        addonDesc.setTitleText("charity");
        addonDescList.add(addonDesc);
        hotelRates.getAddOns().get(0).setDescriptions(addonDescList);
        Map<String,List<String>> addonTncMap = new HashMap<>();
        List<String> tncList = new ArrayList<>();
        tncList.add("addonTnc");
        addonTncMap.put("tncList", tncList);
        hotelRates.getAddOns().get(0).setTnc(addonTncMap);
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        List<RoomTariff> roomTarrif = new ArrayList<>();
        RoomTariff tarrif = new RoomTariff();
        tarrif.setNumberOfAdults(2);
        tarrif.setNumberOfChildren(2);
        tarrif.setChildAges(new ArrayList<>());
        tarrif.getChildAges().add(1);
        roomTarrif.add(tarrif);
        RoomTariff tarrif2 = new RoomTariff();
        tarrif2.setNumberOfAdults(2);
        tarrif2.setNumberOfChildren(2);
        tarrif2.setChildAges(new ArrayList<>());
        tarrif2.getChildAges().add(1);
        roomTarrif.add(tarrif2);
        ratePlanCB.setRoomTariff(roomTarrif);
        ratePlanCB.setSegmentId(Constants.MYPARTNER_SEGMENT_ID);
        ratePlanCB.getSupplierDetails().setSupplierCode("EPXX0034");

        roomType.getRatePlanList().put("abc", ratePlanCB);


        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setEmiDetails(new EMIAbridgeResponse());
        hotelRates.setDoubleBlackInfo(new DoubleBlackValidateResponse());
        SpecialRequest spclReq = new SpecialRequest();
        List<SpecialRequestCategory> categories = new ArrayList<>();
        SpecialRequestCategory spclReqCat = new SpecialRequestCategory();
        spclReqCat.setCode("123");
        spclReqCat.setName("Early Checkin");
        List<SpecialRequestCategory> subCategories = new ArrayList<>();
        SpecialRequestCategory spclReqSubCat = new SpecialRequestCategory();
        spclReqSubCat.setName("6 AM");
        subCategories.add(spclReqSubCat);
        spclReqCat.setSubCategories(subCategories);
        categories.add(spclReqCat);
        spclReq.setCategories(categories);
        hotelRates.setSpecialRequestAvailable(spclReq);
        hotelRates.setMustReadRules(new ArrayList<>());
        hotelRates.getMustReadRules().add("Unmarried couples not allowed");
        List<AlertInfo> hotelLevelAlert = new ArrayList<>();
        List<AlertInfo.AlertType> reasons = new ArrayList<>();
        reasons.add(AlertInfo.AlertType.PRICE);
        AlertInfo alert = new AlertInfo();
        alert.setRpcc("abcd");
        alert.setExpectedValue("1000");
        alert.setActualValue("1500");
        alert.setMismatchType(AlertInfo.AlertType.PRICE);
        hotelLevelAlert.add(alert);
        hotelRates.setAlerts(hotelLevelAlert);

        List<HotelRates> hotelRatesList = new ArrayList<HotelRates>();
        hotelRatesList.add(hotelRates);

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildHotelRates(hotelRatesList).build();


        List<HtlRmInfo> htlRmInfoList = new ArrayList<HtlRmInfo>();
        htlRmInfoList.add(new HtlRmInfo());
        htlRmInfoList.get(0).setHotelRoomInfo(new HashMap<String, RoomInfo>());

        roomInfo.setMaster(true);
        roomInfo.setBedInfoText("1 Double Bed,1 King Bed,1 Queen Bed,6 Sofa Cum Beds");
        roomInfo.setMaxAdultCount(5);
        roomInfo.setBeds(new ArrayList<>());
        roomInfo.getBeds().add(new SleepingArrangement());

        htlRmInfoList.get(0).getHotelRoomInfo().put("abc", roomInfo);

        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity.Builder().buildHtlRmInfo(htlRmInfoList).build();

        AvailRoomsResponse availRoomsResponse;

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        commonModifierResponse.setAppVersionIntGi("3000"); // Soldout handling is enabled from certain versions, FE yet to provice. You can increase this value and configure in future.
        commonModifierResponse.setFlavour("Android");


        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,"", "", "", expData, false, "HOTELS",commonModifierResponse,false,null,false, "");

        Assert.assertNotNull(availRoomsResponse);
        Assert.assertNotNull(availRoomsResponse.getRateplanlist().get(0).getRatePlanPersuasions());
        Assert.assertNotNull(availRoomsResponse.getTcClauseDetails());

        // Soldout card case
        Assert.assertEquals(availRoomsResponse.getSoldOutCallOut().getPriceChange(), "INCREASE");
        Assert.assertEquals(availRoomsResponse.getSoldOutCallOut().getReasons(), reasons);


        for (Map.Entry<String, RoomType> entry : roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().getRoomType().entrySet()) {
            List<com.mmt.hotels.model.response.pricing.RatePlan> ratePlanlist = entry.getValue().getRatePlanList().entrySet().stream().map(ratePlan -> ratePlan.getValue()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ratePlanlist) && ratePlanlist.get(0).getPaymentDetails() !=null &&
                    ratePlanlist.get(0).getPaymentDetails().getPaymentMode() !=null) {
                        ratePlanlist.get(0).getPaymentDetails().setPaymentMode(PaymentMode.PAH_WITH_CC);
            }
        }

        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

        roomDetailsResponse.getHotelRates().get(0).setCurrencyCode("AED");
        ratePlanCB.getSupplierDetails().setHotelierCurrencyCode("INR");

        List<AdditionalFees> additionalFeesList = new ArrayList<AdditionalFees>();
        AdditionalFees mandatoryCharge = new AdditionalFees();
        mandatoryCharge.setCurrency("AED");
        mandatoryCharge.setAmount(100.00);
        additionalFeesList.add(mandatoryCharge);
        roomDetailsResponse.getHotelRates().get(0).setMandatoryCharges(additionalFeesList);
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,"", "", "", expData, false, "GROUP",commonModifierResponse, false,new HotelImage(),false, "");

        for (com.mmt.hotels.clientgateway.response.PricingDetails priceDetail : availRoomsResponse.getTotalpricing().getDetails()) {
            if (Constants.TOTAL_AMOUNT_KEY.equalsIgnoreCase(priceDetail.getKey())) {

                Assert.assertNull(priceDetail.getHotelierCurrencyCode());
                Assert.assertNull(priceDetail.getHotelierCurrencyAmount());
                break;
            }
        }


        for (AdditionalMandatoryChargesBreakup breakup : availRoomsResponse.getAdditionalFees().getBreakup()) {

            Assert.assertNull(breakup.getHotelierCurrency());
            Assert.assertNull(breakup.getHotelierCurrencyAmount());
        }

        Assert.assertNotNull(availRoomsResponse.getTotalpricing().getPayAtHotelText());

        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setBookingAmount(0f);
        mpFareHoldStatus.setHoldEligible(true);
        mpFareHoldStatus.setExpiry(new Long(123456));
        mpFareHoldStatus.setEligibleForHoldBooking(true);
        roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").setMpFareHoldStatus(mpFareHoldStatus);
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,"", "", "", expData, false, "HOTELS",commonModifierResponse,false,new HotelImage(),false, "");
        Assert.assertNotNull(availRoomsResponse.getBookNowDetails());

    }
    
    @Test
    public void testTotalPricingResponse(){
    	TotalPricingResponse totalPricingResponseOld = new TotalPricingResponse();
    	TotalPricing totalPricing = new TotalPricing();
        String bnplUnavailableMsg = "bnpl unavailable";
        totalPricing.setBnplUnavailableMsg(bnplUnavailableMsg);
    	totalPricingResponseOld.setPriceBreakdown(totalPricing);
    	Map<LOB,DisplayPriceBreakDown> lobWisePricing = new HashMap<>();
    	totalPricing.setDisplayPrice(4505.0);
    	totalPricing.setDisplayPriceAlternateCurrency(76);
    	DisplayPriceBreakDown displayPriceBrkDwnHotel = new DisplayPriceBreakDown();
    	displayPriceBrkDwnHotel.setDisplayPrice(3504.0);
    	displayPriceBrkDwnHotel.setBasePrice(5500.0);
    	displayPriceBrkDwnHotel.setHotelTax(594.0);
    	displayPriceBrkDwnHotel.setMmtServiceCharge(297.0);
    	displayPriceBrkDwnHotel.setCdfDiscount(1346.0);
    	displayPriceBrkDwnHotel.setMmtDiscount(550.0);
    	displayPriceBrkDwnHotel.setWallet(100.0);
    	displayPriceBrkDwnHotel.setAffiliateFee(100.0);
    	lobWisePricing.put(LOB.HOTEL, displayPriceBrkDwnHotel);
    	DisplayPriceBreakDown displayPriceBrkDwnAddon = new DisplayPriceBreakDown();
    	displayPriceBrkDwnAddon.setDisplayPrice(5.0);
    	lobWisePricing.put(LOB.ADDON, displayPriceBrkDwnAddon);
    	totalPricing.setLobWisePricing(lobWisePricing);
        DisplayPriceBreakDown displayPriceBrkDwnAddonInsurance = new DisplayPriceBreakDown();
        displayPriceBrkDwnAddonInsurance.setDisplayPrice(5.0);
        displayPriceBrkDwnAddonInsurance.setInsuranceBreakupMap(new HashMap<>());
        InsuranceDetails id1 = new InsuranceDetails();
        id1.setAmount(2.0d);
        id1.setDisplayLabel("and");
        InsuranceDetails id2 = new InsuranceDetails();
        id2.setAmount(3.0d);
        id2.setDisplayLabel("and");
        displayPriceBrkDwnAddonInsurance.getInsuranceBreakupMap().put("12",id1);
        displayPriceBrkDwnAddonInsurance.getInsuranceBreakupMap().put("13",id2);
        lobWisePricing.put(LOB.INSURANCE, displayPriceBrkDwnAddonInsurance);

    	TotalPriceResponse totalPriceRsp = availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN");
    	Assert.assertNotNull(totalPriceRsp);
    	Assert.assertNotNull(totalPriceRsp.getTotalPricing());
    	Assert.assertEquals(bnplUnavailableMsg, totalPriceRsp.getTotalPricing().getBnplUnavailableMsg());
    	Assert.assertNotNull(totalPriceRsp.getAddon());
    	Assert.assertNotNull(totalPriceRsp.getTotalPricing().getDetails());
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(0).getAmount(), 5500.0); // base price
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(1).getAmount(), 1996.0); // total discount
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(2).getAmount(), 3504.0); // price after discount
        Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(3).getAmount(), 100.0); // wallet
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(4).getAmount(), 991.0); // taxes
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(5).getAmount(), 4505.0); // total amount
    	Assert.assertEquals(totalPriceRsp.getAddon().get(0).getAmount(), 5.0); // addon amount
        Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(6).getAmount(),5.0d);//insurance
    }

    @Test
    public void testAlertsNoticesAndMandatoryCharges() throws IOException {
        RoomDetailsResponse roomDetailsResponse = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:review/reviewdata.json")),
                RoomDetailsResponse.class);

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        AvailRoomsResponse cgAvailRoomResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(roomDetailsResponse, null, "", "", "",expData, false, "",commonModifierResponse,false,new HotelImage(),false, "");

        Assert.assertEquals(3, cgAvailRoomResponse.getAlerts().size());
        Assert.assertEquals(2, cgAvailRoomResponse.getAdditionalFees().getBreakup().size());

        roomDetailsResponse.getHotelRates().get(0).setPreApprovalExpired(true);
        cgAvailRoomResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(roomDetailsResponse, null, "", "","", expData, false, "",commonModifierResponse,false,new HotelImage(),false, "");
        Assert.assertEquals(3, cgAvailRoomResponse.getAlerts().size());
        Assert.assertEquals(2, cgAvailRoomResponse.getAdditionalFees().getBreakup().size());

    }

    private com.mmt.hotels.model.response.PayLaterEligibilityResponse buildPayLaterResponse(boolean eligible)
    {
        com.mmt.hotels.model.response.PayLaterEligibilityResponse hes = new com.mmt.hotels.model.response.PayLaterEligibilityResponse();
        hes.setEligible(eligible);
        hes.setCorrelationKey(Constants.CORRELATIONKEY);
        hes.setAmount(1051.20);
        return hes;
    }

    @Test
    public void testConvertPayLaterEligibilityResponse()
    {

        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(true),true));
        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(true),false));
        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(false),true));
        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(false),false));


    }

    @Test
    public void testGetBnplDetails() {
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        String bnplNewVariantText = "Book now @ just Rs. 1";
        String bnplNewVariantSubText = "Pay the remaining amount using any paymode any time before 19 Jun";
        roomTypeDetails.setBnplNewVariantText(bnplNewVariantText);
        roomTypeDetails.setBnplNewVariantSubText(bnplNewVariantSubText);
        roomTypeDetails.setTotalDisplayFare(new DisplayFare());
        roomTypeDetails.getTotalDisplayFare().setIsBNPLApplicable(true);
        BNPLDetails bnplDetails = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getBnplDetails", roomTypeDetails, true,null,"IN",null);
        Assert.assertNotNull(bnplDetails);
        Assert.assertEquals(bnplNewVariantText, bnplDetails.getBnplNewVariantText());
        Assert.assertNotNull(bnplNewVariantSubText, bnplDetails.getBnplNewVariantSubText());
    }

    @Test
    public void buildHotelPersuasionsTest() {

        HotelRates hotelrates = new HotelRates();
        hotelrates.setRoomTypeDetails(new RoomTypeDetails());
        hotelrates.getRoomTypeDetails().setRoomType(new HashMap<>());

        RatePlan ratePlan2 = new RatePlan();
        ratePlan2.setMpFareHoldStatus(new MpFareHoldStatus());
        ratePlan2.getMpFareHoldStatus().setExpiry(new Long(123456));
        ratePlan2.getMpFareHoldStatus().setHoldEligible(true);
        ratePlan2.getMpFareHoldStatus().setHoldEligible(true);
        ratePlan2.getMpFareHoldStatus().setBookingAmount(0.0f);
        RoomType roomType = new RoomType();
        roomType.setRatePlanList(new HashMap<>());
        roomType.getRatePlanList().put("xyz",ratePlan2);
        hotelrates.getRoomTypeDetails().getRoomType().put("abc",roomType);

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);

        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        Map<String, PersuasionResponse> hotelPersuasions = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "buildHotelPersuasions", hotelrates , true, commonModifierResponse);
        Assert.assertNotNull(hotelPersuasions.get(Constants.BOOK_NOW_PERSUASION_KEY));
    }

    @Test
    public void getBookNowDetailsTest() {

        BookNowDetails details = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getBookNowDetails" ,0, new Long(123456));
        Assert.assertNotNull(details);
    }

    @Test
    public void getMpFareHoldMinExpiryTest() {

        HotelRates hotelrates = new HotelRates();
        hotelrates.setRoomTypeDetails(new RoomTypeDetails());
        hotelrates.getRoomTypeDetails().setRoomType(new HashMap<>());

        RatePlan ratePlan = new RatePlan();
        ratePlan.setMpFareHoldStatus(new MpFareHoldStatus());
        ratePlan.getMpFareHoldStatus().setExpiry(new Long(123456));
        ratePlan.getMpFareHoldStatus().setHoldEligible(true);
        ratePlan.getMpFareHoldStatus().setHoldEligible(true);

        RoomType roomType = new RoomType();
        roomType.setRatePlanList(new HashMap<>());
        roomType.getRatePlanList().put("abc",ratePlan);
        hotelrates.getRoomTypeDetails().getRoomType().put("abc",roomType);

        Long mpFareHoldExpiry = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getMpFareHoldMinExpiry" , hotelrates);
        Assert.assertNotNull(mpFareHoldExpiry);

        RatePlan ratePlan2 = new RatePlan();
        ratePlan2.setMpFareHoldStatus(new MpFareHoldStatus());
        ratePlan2.getMpFareHoldStatus().setExpiry(null);
        ratePlan2.getMpFareHoldStatus().setHoldEligible(true);
        ratePlan2.getMpFareHoldStatus().setHoldEligible(true);

        roomType.getRatePlanList().put("xyz",ratePlan2);
        hotelrates.getRoomTypeDetails().getRoomType().put("abc",roomType);

        mpFareHoldExpiry = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getMpFareHoldMinExpiry" , hotelrates);
        Assert.assertNull(mpFareHoldExpiry);
    }

    @Test
    public void getTcsInfoCard_testForGCC() {
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "INTL");
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getTcsInfoCard", "IN", "IN", true, PaymentMode.PAS);
        MDC.clear();
    }

    @Test
    public void getTcsInfoCard_testForNonGCC() {
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "INTL");
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getTcsInfoCard",  "INTL", "CAN", true, PaymentMode.PAS);
        MDC.clear();
    }

    @Test
    public void testBasicInitialization() {
        // Test that the transformer is properly initialized
        Assert.assertNotNull(availRoomsResponseTransformerDesktop);
        Assert.assertNotNull(commonResponseTransformer);
        Assert.assertNotNull(utility);
        Assert.assertNotNull(polyglotService);
        Assert.assertNotNull(metricAspect);
        Assert.assertNotNull(propManager);
        Assert.assertNotNull(dateUtil);
    }

}
