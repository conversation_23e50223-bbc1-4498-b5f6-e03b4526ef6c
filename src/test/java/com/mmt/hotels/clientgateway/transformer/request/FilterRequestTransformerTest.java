package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class FilterRequestTransformerTest {
    
    @InjectMocks
    FilterRequestTransformer filterRequestTransformer;

    @Mock
    Utility utility;
    
    @Test
    public void convertFilterRequestTest() {
        FilterCountRequest request = new FilterCountRequest();
        request.setRequestDetails(new RequestDetails());
        request.setDeviceDetails(new DeviceDetails());
        request.setExpData("abc");


        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setTrafficSource(new TrafficSource());
        request.getRequestDetails().setLoggedIn(false);
        request.getRequestDetails().setSrLng(10d);
        request.getRequestDetails().setSrLat(10d);
        request.getRequestDetails().setCouponCount(2);

        request.setSearchCriteria(new SearchHotelsCriteria());
        request.getSearchCriteria().setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        request.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        request.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(2);

        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();

        Assert.notNull(filterRequestTransformer.convertSearchRequest(request,commonModifierResponse));
    }
    
}
