package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.request.UpdateApprovalRequest;
import com.mmt.hotels.model.request.payment.ApprovalAction;
import com.mmt.hotels.model.request.payment.DeviceDetails;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class UpdateApprovalRequestTransformerTest {

    @InjectMocks
    UpdateApprovalRequestTransformer updateApprovalRequestTransformer;

    @Mock
    CorporateHelper corporateHelper;


    @Test
    public void getDeviceDetailsTest() throws Exception {
        UpdateApprovalRequest updateApprovalRequest = new UpdateApprovalRequest();
        java.lang.reflect.Method method = updateApprovalRequestTransformer.getClass().getDeclaredMethod("getDeviceDetails",String.class, UpdateApprovalRequest.class);
        method.setAccessible(true);
        DeviceDetails deviceDetails = (DeviceDetails) method.invoke(updateApprovalRequestTransformer,"ANDROID", updateApprovalRequest);
        Assert.assertNotNull(deviceDetails);
        Assert.assertEquals("ANDROID",deviceDetails.getOsType().toString());
    }

    @Test
    public void getApprovalActionTest() throws Exception {
        UpdateApprovalRequest updateApprovalRequest = new UpdateApprovalRequest();
        ApprovalAction approvalAction;
        updateApprovalRequest.setAction("Action_123");
        ApprovalAction.map.put("Action_123",null);

        java.lang.reflect.Method method = updateApprovalRequestTransformer.getClass().getDeclaredMethod("getApprovalAction", UpdateApprovalRequest.class);
        method.setAccessible(true);
        Assert.assertEquals("FAILED", method.invoke(updateApprovalRequestTransformer, updateApprovalRequest).toString());
    }

    @Test
    public void getApproverUuidTest() throws Exception {
        com.mmt.hotels.model.request.corporate.UpdateApprovalRequest updateApprovalRequestHES = new com.mmt.hotels.model.request.corporate.UpdateApprovalRequest();
        UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.any(),Mockito.any())).thenReturn(userDetailsDTO);
        userDetailsDTO.setUuid("Uuid_123");
        java.lang.reflect.Method method = updateApprovalRequestTransformer.getClass().getDeclaredMethod("getApproverUuid", com.mmt.hotels.model.request.corporate.UpdateApprovalRequest.class, String.class, Map.class);
        method.setAccessible(true);
        String resp = (String) method.invoke(updateApprovalRequestTransformer, updateApprovalRequestHES, "", new HashMap<>());
        Assert.assertEquals("Uuid_123", resp);
    }

    @Test
    public void convertUpdateApprovalRequestTest() throws Exception {
        UpdateApprovalRequest updateApprovalRequest = new UpdateApprovalRequest();

        java.lang.reflect.Method method = updateApprovalRequestTransformer.getClass().getDeclaredMethod("convertUpdateApprovalRequest", UpdateApprovalRequest.class, String.class, String.class, Map.class);
        method.setAccessible(true);
        com.mmt.hotels.model.request.corporate.UpdateApprovalRequest updateApprovalRequestHES = (com.mmt.hotels.model.request.corporate.UpdateApprovalRequest) method.invoke(updateApprovalRequestTransformer, updateApprovalRequest, "", "INR", new HashMap<>());
        Assert.assertNotNull(updateApprovalRequestHES);
        Assert.assertEquals("INR", updateApprovalRequestHES.getCorrelationKey());
    }

}
