package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * Unit tests for SearchRoomsMediaHelper in GI project
 * Comprehensive test coverage following patterns from CG version but adapted for GI implementation
 * GI version focuses on room image extraction and basic media population without 360 image support
 */
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsMediaHelperTest {

    @InjectMocks
    private SearchRoomsMediaHelper searchRoomsMediaHelper;

    @Before
    public void setUp() {
        // No additional setup needed for GI version (no PolyglotService or @Value injection)
    }

    // ==================== extractRoomImagesFromMedia Tests ====================

    @Test
    public void should_ExtractRoomImages_When_MediaHasValidProfessionalImages() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM001", Arrays.asList("https://example.com/image1.jpg"));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void should_ExtractMultipleRoomImages_When_MediaHasMultipleValidImages() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM001", 
            Arrays.asList("https://example.com/image1.jpg", "https://example.com/image2.jpg", "https://example.com/image3.jpg"));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0));
        assertEquals("https://example.com/image2.jpg", result.get(1));
        assertEquals("https://example.com/image3.jpg", result.get(2));
    }

    @Test
    public void should_ReturnEmptyList_When_MediaIsNull() {
        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(null, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalMediaEntitiesIsNull() {
        // Given
        Media media = new Media();
        media.setProfessionalMediaEntities(null);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalMediaEntitiesIsEmpty() {
        // Given
        Media media = new Media();
        media.setProfessionalMediaEntities(new HashMap<>());

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalMediaEntitiesDoesNotContainRKey() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        professionalMediaEntities.put("H", createProfessionalMediaEntityList("ROOM001", Arrays.asList("https://example.com/image1.jpg")));
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalImagesListIsNull() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        professionalMediaEntities.put("R", null);
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalImagesListIsEmpty() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        professionalMediaEntities.put("R", new ArrayList<>());
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_FilterImagesByRoomCode_When_MultipleRoomCodesExist() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        List<ProfessionalMediaEntity> imageList = new ArrayList<>();
        imageList.addAll(createProfessionalMediaEntityList("ROOM001", Arrays.asList("https://example.com/room001_1.jpg", "https://example.com/room001_2.jpg")));
        imageList.addAll(createProfessionalMediaEntityList("ROOM002", Arrays.asList("https://example.com/room002_1.jpg", "https://example.com/room002_2.jpg")));
        imageList.addAll(createProfessionalMediaEntityList("ROOM003", Arrays.asList("https://example.com/room003_1.jpg")));
        
        professionalMediaEntities.put("R", imageList);
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("https://example.com/room001_1.jpg", result.get(0));
        assertEquals("https://example.com/room001_2.jpg", result.get(1));
    }

    @Test
    public void should_ReturnEmptyList_When_NoImagesMatchRoomCode() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM002", Arrays.asList("https://example.com/image1.jpg"));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_SkipImagesWithNullUrl_When_ExtractingRoomImages() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        List<ProfessionalMediaEntity> imageList = new ArrayList<>();
        
        ProfessionalMediaEntity validImage = new ProfessionalMediaEntity();
        validImage.setRoomCode("ROOM001");
        validImage.setUrl("https://example.com/valid.jpg");
        imageList.add(validImage);
        
        ProfessionalMediaEntity invalidImage = new ProfessionalMediaEntity();
        invalidImage.setRoomCode("ROOM001");
        invalidImage.setUrl(null);
        imageList.add(invalidImage);
        
        professionalMediaEntities.put("R", imageList);
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/valid.jpg", result.get(0));
    }

    @Test
    public void should_SkipImagesWithBlankUrl_When_ExtractingRoomImages() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        List<ProfessionalMediaEntity> imageList = new ArrayList<>();
        
        ProfessionalMediaEntity validImage = new ProfessionalMediaEntity();
        validImage.setRoomCode("ROOM001");
        validImage.setUrl("https://example.com/valid.jpg");
        imageList.add(validImage);
        
        ProfessionalMediaEntity blankUrlImage = new ProfessionalMediaEntity();
        blankUrlImage.setRoomCode("ROOM001");
        blankUrlImage.setUrl("");
        imageList.add(blankUrlImage);
        
        ProfessionalMediaEntity whitespaceUrlImage = new ProfessionalMediaEntity();
        whitespaceUrlImage.setRoomCode("ROOM001");
        whitespaceUrlImage.setUrl("   ");
        imageList.add(whitespaceUrlImage);
        
        professionalMediaEntities.put("R", imageList);
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/valid.jpg", result.get(0));
    }

    @Test
    public void should_AddHttpsPrefix_When_UrlDoesNotStartWithHttp() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM001", Arrays.asList("//example.com/image1.jpg"));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void should_PreserveHttpsUrl_When_UrlAlreadyStartsWithHttps() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM001", Arrays.asList("https://example.com/image1.jpg"));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void should_PreserveHttpUrl_When_UrlAlreadyStartsWithHttp() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM001", Arrays.asList("http://example.com/image1.jpg"));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("http://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void should_HandleMixedUrlFormats_When_ExtractingRoomImages() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM001", Arrays.asList(
            "https://example.com/image1.jpg",
            "http://example.com/image2.jpg", 
            "//example.com/image3.jpg",
            "/path/to/image4.jpg"
        ));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0));
        assertEquals("http://example.com/image2.jpg", result.get(1));
        assertEquals("https://example.com/image3.jpg", result.get(2));
        assertEquals("https:/path/to/image4.jpg", result.get(3));
    }

//    @Test
//    public void should_HandleNullRoomCode_When_ExtractingRoomImages() {
//        // Given
//        Media media = createMediaWithProfessionalImages("ROOM001", Arrays.asList("https://example.com/image1.jpg"));
//
//        // When
//        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, null);
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.isEmpty()); // No images should match null room code
//    }

    @Test
    public void should_HandleEmptyRoomCode_When_ExtractingRoomImages() {
        // Given
        Media media = createMediaWithProfessionalImages("ROOM001", Arrays.asList("https://example.com/image1.jpg"));

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty()); // No images should match empty room code
    }

    // ==================== populateMedia Tests ====================

    @Test
    public void should_PopulateMediaData_When_ValidRoomImagesProvided() {
        // Given
        List<String> roomImages = Arrays.asList("https://example.com/image1.jpg", "https://example.com/image2.jpg");
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        MediaData mediaData1 = result.get(0);
        assertEquals("https://example.com/image1.jpg", mediaData1.getUrl());
        assertEquals(Constants.IMAGE_TYPE, mediaData1.getMediaType());
        
        MediaData mediaData2 = result.get(1);
        assertEquals("https://example.com/image2.jpg", mediaData2.getUrl());
        assertEquals(Constants.IMAGE_TYPE, mediaData2.getMediaType());
    }

    @Test
    public void should_ReturnEmptyList_When_RoomImagesIsNull() {
        // Given
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, null, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_RoomImagesIsEmpty() {
        // Given
        List<String> roomImages = new ArrayList<>();
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_AddHttpsPrefix_When_UrlDoesNotStartWithHttpInPopulateMedia() {
        // Given
        List<String> roomImages = Arrays.asList("//example.com/image1.jpg");
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
    }

    @Test
    public void should_PreserveHttpsUrl_When_UrlAlreadyStartsWithHttpsInPopulateMedia() {
        // Given
        List<String> roomImages = Arrays.asList("https://example.com/image1.jpg");
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
    }

    @Test
    public void should_PreserveHttpUrl_When_UrlAlreadyStartsWithHttpInPopulateMedia() {
        // Given
        List<String> roomImages = Arrays.asList("http://example.com/image1.jpg");
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("http://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
    }

    @Test
    public void should_HandleMixedUrlFormats_When_PopulatingMedia() {
        // Given
        List<String> roomImages = Arrays.asList(
            "https://example.com/image1.jpg",
            "http://example.com/image2.jpg",
            "//example.com/image3.jpg",
            "/path/to/image4.jpg"
        );
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals("http://example.com/image2.jpg", result.get(1).getUrl());
        assertEquals("https://example.com/image3.jpg", result.get(2).getUrl());
        assertEquals("https:/path/to/image4.jpg", result.get(3).getUrl());
        
        // All should have IMAGE_TYPE
        for (MediaData mediaData : result) {
            assertEquals(Constants.IMAGE_TYPE, mediaData.getMediaType());
        }
    }

    @Test
    public void should_HandleNullStaticRoomInfoMap_When_PopulatingMedia() {
        // Given
        List<String> roomImages = Arrays.asList("https://example.com/image1.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
    }

    @Test
    public void should_HandleEmptyStaticRoomInfoMap_When_PopulatingMedia() {
        // Given
        List<String> roomImages = Arrays.asList("https://example.com/image1.jpg");
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
    }

    @Test
    public void should_HandleNullRoomCode_When_PopulatingMedia() {
        // Given
        List<String> roomImages = Arrays.asList("https://example.com/image1.jpg");
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
    }

    @Test
    public void should_HandleEmptyRoomCode_When_PopulatingMedia() {
        // Given
        List<String> roomImages = Arrays.asList("https://example.com/image1.jpg");
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals(Constants.IMAGE_TYPE, result.get(0).getMediaType());
    }

    @Test
    public void should_HandleLargeNumberOfImages_When_PopulatingMedia() {
        // Given
        List<String> roomImages = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            roomImages.add("https://example.com/image" + i + ".jpg");
        }
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(100, result.size());
        
        for (int i = 0; i < 100; i++) {
            MediaData mediaData = result.get(i);
            assertEquals("https://example.com/image" + (i + 1) + ".jpg", mediaData.getUrl());
            assertEquals(Constants.IMAGE_TYPE, mediaData.getMediaType());
        }
    }

    @Test
    public void should_HandleSpecialCharactersInUrl_When_PopulatingMedia() {
        // Given
        List<String> roomImages = Arrays.asList(
            "https://example.com/image with spaces.jpg",
            "https://example.com/image%20encoded.jpg",
            "https://example.com/image&param=value.jpg",
            "https://example.com/image?query=test.jpg"
        );
        Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(staticRoomInfoMap, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("https://example.com/image with spaces.jpg", result.get(0).getUrl());
        assertEquals("https://example.com/image%20encoded.jpg", result.get(1).getUrl());
        assertEquals("https://example.com/image&param=value.jpg", result.get(2).getUrl());
        assertEquals("https://example.com/image?query=test.jpg", result.get(3).getUrl());
        
        for (MediaData mediaData : result) {
            assertEquals(Constants.IMAGE_TYPE, mediaData.getMediaType());
        }
    }

    // ==================== Helper Methods for Test Data Creation ====================

    private Media createMediaWithProfessionalImages(String roomCode, List<String> imageUrls) {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        professionalMediaEntities.put("R", createProfessionalMediaEntityList(roomCode, imageUrls));
        media.setProfessionalMediaEntities(professionalMediaEntities);
        return media;
    }

    private List<ProfessionalMediaEntity> createProfessionalMediaEntityList(String roomCode, List<String> imageUrls) {
        List<ProfessionalMediaEntity> entityList = new ArrayList<>();
        
        for (String imageUrl : imageUrls) {
            ProfessionalMediaEntity entity = new ProfessionalMediaEntity();
            entity.setRoomCode(roomCode);
            entity.setUrl(imageUrl);
            entityList.add(entity);
        }
        
        return entityList;
    }
} 