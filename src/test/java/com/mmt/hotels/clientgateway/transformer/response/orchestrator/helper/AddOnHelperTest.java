package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOnDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOns;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationPolicy;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PolicyDetails;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.addon.AddOnNode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for AddOnHelper class.
 * Tests all methods with comprehensive coverage including edge cases.
 */
@RunWith(MockitoJUnitRunner.class)
public class AddOnHelperTest {

    @InjectMocks
    private AddOnHelper addOnHelper;

    @Mock
    private PolyglotService polyglotService;

    private static final String FLEXI_CANCEL_TYPE = "FLEXI_CANCEL";
    private static final String FLEXI_CANCEL_TRANSLATED_TEXT = "FlexiCancel for ₹{0} per night";
    private static final String PRICE_DESCRIPTION = "₹500";

    @Before
    public void setUp() {
        // Setup common mock responses
        when(polyglotService.getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT))
                .thenReturn(FLEXI_CANCEL_TRANSLATED_TEXT);
    }

    // ========== transformAddOns Tests ==========

    @Test
    public void should_ReturnEmptyMap_When_AddOnsIsNull() {
        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyMap_When_AddOnsHasNoDetailsOrPolicies() {
        // Given
        AddOns addOns = new AddOns();
        addOns.setAddOnDetails(null);
        addOns.setAddOnPolicies(null);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_TransformAddOnDetails_When_OnlyAddOnDetailsPresent() {
        // Given
        AddOns addOns = createAddOnsWithDetails();

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
        
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertNotNull(addOnDetails);
        assertNotNull(addOnDetails.getApplied());
        assertEquals("Applied Title", addOnDetails.getApplied().getTitle());
    }

    @Test
    public void should_TransformAddOnPolicies_When_OnlyAddOnPoliciesPresent() {
        // Given
        AddOns addOns = createAddOnsWithPolicies();

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
        
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertNotNull(addOnDetails);
        assertEquals("Test Icon URL", addOnDetails.getBackgroundImage());
        assertEquals("Test Value", addOnDetails.getCta());
    }

    @Test
    public void should_CombineBothMaps_When_BothAddOnDetailsAndPoliciesPresent() {
        // Given
        AddOns addOns = createAddOnsWithBothDetailsAndPolicies();

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        // Should contain entries from both sources
        assertTrue(result.size() >= 1);
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
    }

    // ========== transformAddOnDetails Tests ==========

    @Test
    public void should_ReturnEmptyMap_When_AddOnDetailsListIsNull() {
        // Given
        AddOns addOns = new AddOns();
        addOns.setAddOnDetails(null);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyMap_When_AddOnDetailsListIsEmpty() {
        // Given
        AddOns addOns = new AddOns();
        addOns.setAddOnDetails(new ArrayList<>());

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_SkipNonFlexiCancelDetails_When_TypeIsNotFlexiCancel() {
        // Given
        AddOns addOns = new AddOns();
        List<AddOnDetails> addOnDetailsList = new ArrayList<>();
        
        AddOnDetails nonFlexiDetail = new AddOnDetails();
        nonFlexiDetail.setType("SOME_OTHER_TYPE");
        addOnDetailsList.add(nonFlexiDetail);
        
        addOns.setAddOnDetails(addOnDetailsList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ProcessFirstFlexiCancelOnly_When_MultipleFlexiCancelDetailsPresent() {
        // Given
        AddOns addOns = new AddOns();
        List<AddOnDetails> addOnDetailsList = new ArrayList<>();
        
        AddOnDetails firstFlexiDetail = createFlexiCancelAddOnDetail("First");
        AddOnDetails secondFlexiDetail = createFlexiCancelAddOnDetail("Second");
        
        addOnDetailsList.add(firstFlexiDetail);
        addOnDetailsList.add(secondFlexiDetail);
        
        addOns.setAddOnDetails(addOnDetailsList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
        
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertEquals("First", addOnDetails.getApplied().getTitle());
    }

    @Test
    public void should_TransformAppliedPolicyDetails_When_AppliedPolicyPresent() {
        // Given
        AddOns addOns = createAddOnsWithDetails();

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertNotNull(addOnDetails.getApplied());
        assertEquals("Applied Title", addOnDetails.getApplied().getTitle());
        
        // Verify polyglot translation was applied
        String expectedFormattedText = MessageFormat.format(FLEXI_CANCEL_TRANSLATED_TEXT, PRICE_DESCRIPTION);
        assertEquals(expectedFormattedText, addOnDetails.getApplied().getPriceDescription());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT);
    }

    @Test
    public void should_NotSetPriceDescription_When_TranslatedTextIsEmpty() {
        // Given
        when(polyglotService.getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT))
                .thenReturn("");
        
        AddOns addOns = createAddOnsWithDetails();

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertEquals("₹500", addOnDetails.getApplied().getPriceDescription());
    }

    @Test
    public void should_NotSetPriceDescription_When_OriginalPriceDescriptionIsEmpty() {
        // Given
        AddOns addOns = new AddOns();
        List<AddOnDetails> addOnDetailsList = new ArrayList<>();
        
        AddOnDetails flexiDetail = new AddOnDetails();
        flexiDetail.setType(FLEXI_CANCEL_TYPE);
        
        PolicyDetails appliedPolicy = new PolicyDetails();
        appliedPolicy.setTitle("Applied Title");
        appliedPolicy.setPriceDescription(""); // Empty price description
        flexiDetail.setApplied(appliedPolicy);
        
        addOnDetailsList.add(flexiDetail);
        addOns.setAddOnDetails(addOnDetailsList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertEquals("", addOnDetails.getApplied().getPriceDescription());
    }

    @Test
    public void should_TransformRemovedPolicyDetails_When_RemovedPolicyPresent() {
        // Given
        AddOns addOns = new AddOns();
        List<AddOnDetails> addOnDetailsList = new ArrayList<>();
        
        AddOnDetails flexiDetail = new AddOnDetails();
        flexiDetail.setType(FLEXI_CANCEL_TYPE);
        
        PolicyDetails removedPolicy = new PolicyDetails();
        removedPolicy.setTitle("Removed Title");
        removedPolicy.setPriceDescription("Removed Price");
        flexiDetail.setRemoved(removedPolicy);
        
        addOnDetailsList.add(flexiDetail);
        addOns.setAddOnDetails(addOnDetailsList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertNotNull(addOnDetails.getRemoved());
        assertEquals("Removed Title", addOnDetails.getRemoved().getTitle());
        assertEquals("Removed Price", addOnDetails.getRemoved().getPriceDescription());
    }

    @Test
    public void should_HandleNullAddOnDetail_When_NullElementInList() {
        // Given
        AddOns addOns = new AddOns();
        List<AddOnDetails> addOnDetailsList = new ArrayList<>();
        addOnDetailsList.add(null);
        addOnDetailsList.add(createFlexiCancelAddOnDetail("Valid"));
        
        addOns.setAddOnDetails(addOnDetailsList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
    }

    // ========== transformAddOnPolicies Tests ==========

    @Test
    public void should_SkipPolicyWithoutInclusions_When_InclusionsListIsEmpty() {
        // Given
        AddOns addOns = new AddOns();
        List<CancellationPolicy> policiesList = new ArrayList<>();
        
        CancellationPolicy policy = new CancellationPolicy();
        policy.setInclusions(new ArrayList<>());
        policiesList.add(policy);
        
        addOns.setAddOnPolicies(policiesList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ProcessFlexiCancelInclusions_When_FlexiCancelInclusionPresent() {
        // Given
        AddOns addOns = createAddOnsWithPolicies();

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
        
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertEquals("Test Icon URL", addOnDetails.getBackgroundImage());
        assertEquals("Test Value", addOnDetails.getCta());
        assertNotNull(addOnDetails.getApplied());
        assertEquals("Test Value", addOnDetails.getApplied().getTitle());
    }

    @Test
    public void should_ApplyPolyglotTranslation_When_ProcessingPolicyInclusions() {
        // Given
        AddOns addOns = createAddOnsWithPolicies();

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = result.get(FLEXI_CANCEL_TYPE);
        assertEquals(FLEXI_CANCEL_TRANSLATED_TEXT, addOnDetails.getApplied().getPriceDescription());
        
        //verify(polyglotService, times(2)).getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT);
    }

    @Test
    public void should_SkipNonFlexiCancelInclusions_When_InclusionTypeIsNotFlexiCancel() {
        // Given
        AddOns addOns = new AddOns();
        List<CancellationPolicy> policiesList = new ArrayList<>();
        
        CancellationPolicy policy = new CancellationPolicy();
        List<Inclusion> inclusions = new ArrayList<>();
        
        Inclusion nonFlexiInclusion = new Inclusion();
        nonFlexiInclusion.setInclusionType("SOME_OTHER_TYPE");
        nonFlexiInclusion.setValue("Other Value");
        inclusions.add(nonFlexiInclusion);
        
        policy.setInclusions(inclusions);
        policiesList.add(policy);
        addOns.setAddOnPolicies(policiesList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_HandleNullInclusion_When_NullElementInInclusionsList() {
        // Given
        AddOns addOns = new AddOns();
        List<CancellationPolicy> policiesList = new ArrayList<>();
        
        CancellationPolicy policy = new CancellationPolicy();
        List<Inclusion> inclusions = new ArrayList<>();
        inclusions.add(null);
        inclusions.add(createFlexiCancelInclusion());
        
        policy.setInclusions(inclusions);
        policiesList.add(policy);
        addOns.setAddOnPolicies(policiesList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
    }

    @Test
    public void should_HandleNullPolicy_When_NullElementInPoliciesList() {
        // Given
        AddOns addOns = new AddOns();
        List<CancellationPolicy> policiesList = new ArrayList<>();
        policiesList.add(null);
        policiesList.add(createCancellationPolicyWithFlexiInclusion());
        
        addOns.setAddOnPolicies(policiesList);

        // When
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> result = 
                addOnHelper.transformAddOns(addOns);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FLEXI_CANCEL_TYPE));
    }

    // ========== convertToAddOnNodeList Tests ==========

    @Test
    public void should_ReturnEmptyList_When_AddOnDetailsMapIsNull() {
        // When
        List<AddOnNode> result = addOnHelper.convertToAddOnNodeList(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_AddOnDetailsMapIsEmpty() {
        // When
        List<AddOnNode> result = addOnHelper.convertToAddOnNodeList(new HashMap<>());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ConvertToAddOnNodeList_When_ValidMapProvided() {
        // Given
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> addOnDetailsMap = new HashMap<>();
        
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails addOnDetails = 
                new com.mmt.hotels.clientgateway.response.rooms.AddOnDetails();
        addOnDetails.setCta("Test CTA");
        addOnDetails.setBackgroundImage("Test Image");
        
        addOnDetailsMap.put(FLEXI_CANCEL_TYPE, addOnDetails);

        // When
        List<AddOnNode> result = addOnHelper.convertToAddOnNodeList(addOnDetailsMap);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        AddOnNode addOnNode = result.get(0);
        assertEquals(FLEXI_CANCEL_TYPE, addOnNode.getAddOnType());
        //assertEquals("Test CTA", addOnNode.getCtaText());
        //assertEquals("Test Image", addOnNode.getImageUrl());
    }

    @Test
    public void should_ConvertMultipleEntries_When_MultipleAddOnDetailsProvided() {
        // Given
        Map<String, com.mmt.hotels.clientgateway.response.rooms.AddOnDetails> addOnDetailsMap = new HashMap<>();
        
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails flexiDetails = 
                new com.mmt.hotels.clientgateway.response.rooms.AddOnDetails();
        flexiDetails.setCta("Flexi CTA");
        
        com.mmt.hotels.clientgateway.response.rooms.AddOnDetails otherDetails = 
                new com.mmt.hotels.clientgateway.response.rooms.AddOnDetails();
        otherDetails.setCta("Other CTA");
        
        addOnDetailsMap.put(FLEXI_CANCEL_TYPE, flexiDetails);
        addOnDetailsMap.put("OTHER_TYPE", otherDetails);

        // When
        List<AddOnNode> result = addOnHelper.convertToAddOnNodeList(addOnDetailsMap);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verify both entries are converted
        boolean flexiFound = false, otherFound = false;
        for (AddOnNode node : result) {
            if (FLEXI_CANCEL_TYPE.equals(node.getAddOnType())) {
                flexiFound = true;
                //assertEquals("Flexi CTA", node.getCtaText());
            } else if ("OTHER_TYPE".equals(node.getAddOnType())) {
                otherFound = true;
                //assertEquals("Other CTA", node.getCtaText());
            }
        }
        assertTrue("FlexiCancel AddOnNode should be present", flexiFound);
        assertTrue("Other AddOnNode should be present", otherFound);
    }

    // ========== Helper Methods ==========

    private AddOns createAddOnsWithDetails() {
        AddOns addOns = new AddOns();
        List<AddOnDetails> addOnDetailsList = new ArrayList<>();
        addOnDetailsList.add(createFlexiCancelAddOnDetail("Applied Title"));
        addOns.setAddOnDetails(addOnDetailsList);
        return addOns;
    }

    private AddOns createAddOnsWithPolicies() {
        AddOns addOns = new AddOns();
        List<CancellationPolicy> policiesList = new ArrayList<>();
        policiesList.add(createCancellationPolicyWithFlexiInclusion());
        addOns.setAddOnPolicies(policiesList);
        return addOns;
    }

    private AddOns createAddOnsWithBothDetailsAndPolicies() {
        AddOns addOns = new AddOns();
        
        // Add details
        List<AddOnDetails> addOnDetailsList = new ArrayList<>();
        addOnDetailsList.add(createFlexiCancelAddOnDetail("Details Title"));
        addOns.setAddOnDetails(addOnDetailsList);
        
        // Add policies
        List<CancellationPolicy> policiesList = new ArrayList<>();
        policiesList.add(createCancellationPolicyWithFlexiInclusion());
        addOns.setAddOnPolicies(policiesList);
        
        return addOns;
    }

    private AddOnDetails createFlexiCancelAddOnDetail(String title) {
        AddOnDetails flexiDetail = new AddOnDetails();
        flexiDetail.setType(FLEXI_CANCEL_TYPE);
        
        PolicyDetails appliedPolicy = new PolicyDetails();
        appliedPolicy.setTitle(title);
        appliedPolicy.setPriceDescription(PRICE_DESCRIPTION);
        flexiDetail.setApplied(appliedPolicy);
        
        return flexiDetail;
    }

    private CancellationPolicy createCancellationPolicyWithFlexiInclusion() {
        CancellationPolicy policy = new CancellationPolicy();
        List<Inclusion> inclusions = new ArrayList<>();
        inclusions.add(createFlexiCancelInclusion());
        policy.setInclusions(inclusions);
        return policy;
    }

    private Inclusion createFlexiCancelInclusion() {
        Inclusion inclusion = new Inclusion();
        inclusion.setInclusionType(FLEXI_CANCEL_TYPE);
        inclusion.setValue("Test Value");
        inclusion.setIconUrl("Test Icon URL");
        return inclusion;
    }
} 