package com.mmt.hotels.clientgateway.transformer.response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.Policy;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.Notices;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.mmt.hotels.clientgateway.request.PoliciesRequest;
import com.mmt.hotels.clientgateway.response.PoliciesResponse;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancelPenalty.CancellationType;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.RatePolicy;
import com.mmt.hotels.model.response.pricing.SupplierDetails;
import com.mmt.hotels.model.response.staticdata.ChildExtraBedPolicy;
import com.mmt.hotels.model.response.staticdata.CommonRules;
import com.mmt.hotels.model.response.staticdata.ExtraBedRules;
import com.mmt.hotels.model.response.staticdata.HouseRules;
import com.mmt.hotels.model.response.staticdata.PolicyRules;
import com.mmt.hotels.model.response.staticdata.Rule;
import com.mmt.hotels.model.response.txn.HotelInfo;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedHotel;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.model.response.txn.PersistedTariffInfo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PoliciesResponseTransformerTest {

	@InjectMocks
	private PoliciesResponseTransformer policiesResponseTransformer;

	@Mock
	private Utility utility;

	@Mock
	private PolyglotService polyglotService;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
		// Set up the intlNrSupplierExclusionList field for testing
		List<String> exclusionList = new ArrayList<>();
		exclusionList.add("INGO");
		exclusionList.add("EXPEDIA");
		ReflectionTestUtils.setField(policiesResponseTransformer, "intlNrSupplierExclusionList", exclusionList);
		
		// Default mock responses
		when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");
	}
	
	@Test
	public void testTransformPolicyResponse(){
		PoliciesRequest policiesRequest = new PoliciesRequest();
		PersistanceMultiRoomResponseEntity txnDataEntity = new PersistanceMultiRoomResponseEntity();
		txnDataEntity.setPersistedData(buildPersistedData());
		PoliciesResponse resp = policiesResponseTransformer.transformPolicyResponse(policiesRequest , txnDataEntity  );	
		Assert.assertNotNull(resp);
	}

	@Test
	public void testGetCancellationPolicyType_withFreeCancellation() {
		// Create test data with Free Cancellation
		List<PersistedTariffInfo> tariffList = new ArrayList<>();
		PersistedTariffInfo tariffInfo = new PersistedTariffInfo();
		
		List<CancelPenalty> cancelPenalties = new ArrayList<>();
		CancelPenalty cancelPenalty = new CancelPenalty();
		cancelPenalty.setCancellationType(CancellationType.FREE_CANCELLATON);
		cancelPenalties.add(cancelPenalty);
		tariffInfo.setCancelPenaltyList(cancelPenalties);
		tariffList.add(tariffInfo);
		
		String result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getCancellationPolicyType", tariffList);
		
		assertEquals(BookedCancellationPolicyType.FC.name(), result);
	}

	@Test
	public void testGetCancellationPolicyType_withNullCancellationType() {
		// Create test data with null cancellation type
		List<PersistedTariffInfo> tariffList = new ArrayList<>();
		PersistedTariffInfo tariffInfo = new PersistedTariffInfo();
		
		List<CancelPenalty> cancelPenalties = new ArrayList<>();
		CancelPenalty cancelPenalty = new CancelPenalty();
		cancelPenalty.setCancellationType(null);
		cancelPenalties.add(cancelPenalty);
		tariffInfo.setCancelPenaltyList(cancelPenalties);
		tariffList.add(tariffInfo);
		
		String result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getCancellationPolicyType", tariffList);
		
		assertNull(result);
	}

	@Test
	public void testGetCancellationDate_withValidData() {
		// Create test data with cancellation date
		List<PersistedTariffInfo> tariffList = new ArrayList<>();
		PersistedTariffInfo tariffInfo = new PersistedTariffInfo();
		
		List<CancelPenalty> cancelPenalties = new ArrayList<>();
		CancelPenalty cancelPenalty = new CancelPenalty();
		cancelPenalty.setTillDate("2024-01-20");
		cancelPenalties.add(cancelPenalty);
		tariffInfo.setCancelPenaltyList(cancelPenalties);
		tariffList.add(tariffInfo);
		
		String result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getCancellationDate", tariffList);
		
		assertEquals("2024-01-20", result);
	}

	@Test
	public void testGetCancelPenalty_withMostRestrictivePolicy() {
		// Create test data with multiple tariffs, one with most restrictive policy
		List<PersistedTariffInfo> tariffList = new ArrayList<>();
		
		// First tariff - non-restrictive
		PersistedTariffInfo tariffInfo1 = new PersistedTariffInfo();
		List<CancelPenalty> cancelPenalties1 = new ArrayList<>();
		CancelPenalty cancelPenalty1 = new CancelPenalty();
		cancelPenalty1.setMostRestrictive("false");
		cancelPenalty1.setTillDate("2024-01-20");
		cancelPenalties1.add(cancelPenalty1);
		tariffInfo1.setCancelPenaltyList(cancelPenalties1);
		tariffList.add(tariffInfo1);
		
		// Second tariff - most restrictive
		PersistedTariffInfo tariffInfo2 = new PersistedTariffInfo();
		List<CancelPenalty> cancelPenalties2 = new ArrayList<>();
		CancelPenalty cancelPenalty2 = new CancelPenalty();
		cancelPenalty2.setMostRestrictive("Y");
		cancelPenalty2.setTillDate("2024-01-15");
		cancelPenalties2.add(cancelPenalty2);
		tariffInfo2.setCancelPenaltyList(cancelPenalties2);
		tariffList.add(tariffInfo2);
		
		CancelPenalty result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getCancelPenalty", tariffList);
		
		assertNotNull(result);
		assertEquals("2024-01-15", result.getTillDate());
		assertEquals("Y", result.getMostRestrictive());
	}

	@Test
	public void testIsSupplierExcluded_withExcludedSupplier() {
		boolean result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "isSupplierExcluded", "INGO123");
		assertTrue(result);
	}

	@Test
	public void testIsSupplierExcluded_withNonExcludedSupplier() {
		boolean result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "isSupplierExcluded", "OTHERSUPP");
		assertFalse(result);
	}

	@Test
	public void testGetCheckinPolicy_withMostRestrictivePolicy() {
		// Create test data with multiple tariffs
		List<PersistedTariffInfo> tariffList = new ArrayList<>();
		
		// First tariff - non-restrictive
		PersistedTariffInfo tariffInfo1 = new PersistedTariffInfo();
		RatePolicy checkinPolicy1 = new RatePolicy();
		checkinPolicy1.setMostRestrictive("false");
		checkinPolicy1.setValue("POLICY1");
		tariffInfo1.setCheckinPolicy(checkinPolicy1);
		tariffList.add(tariffInfo1);
		
		// Second tariff - most restrictive
		PersistedTariffInfo tariffInfo2 = new PersistedTariffInfo();
		RatePolicy checkinPolicy2 = new RatePolicy();
		checkinPolicy2.setMostRestrictive("Y");
		checkinPolicy2.setValue("POLICY2");
		tariffInfo2.setCheckinPolicy(checkinPolicy2);
		tariffList.add(tariffInfo2);
		
		RatePolicy result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getCheckinPolicy", tariffList);
		
		assertNotNull(result);
		assertEquals("POLICY2", result.getValue());
		assertEquals("Y", result.getMostRestrictive());
	}

	@Test
	public void testGetConfirmationPolicy_withMostRestrictivePolicy() {
		// Create test data with multiple tariffs
		List<PersistedTariffInfo> tariffList = new ArrayList<>();
		
		// First tariff - non-restrictive
		PersistedTariffInfo tariffInfo1 = new PersistedTariffInfo();
		RatePolicy confirmationPolicy1 = new RatePolicy();
		confirmationPolicy1.setMostRestrictive("false");
		confirmationPolicy1.setValue("CONFIRM_POLICY1");
		tariffInfo1.setConfirmationPolicy(confirmationPolicy1);
		tariffList.add(tariffInfo1);
		
		// Second tariff - most restrictive
		PersistedTariffInfo tariffInfo2 = new PersistedTariffInfo();
		RatePolicy confirmationPolicy2 = new RatePolicy();
		confirmationPolicy2.setMostRestrictive("Y");
		confirmationPolicy2.setValue("CONFIRM_POLICY2");
		tariffInfo2.setConfirmationPolicy(confirmationPolicy2);
		tariffList.add(tariffInfo2);
		
		RatePolicy result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getConfirmationPolicy", tariffList);
		
		assertNotNull(result);
		assertEquals("CONFIRM_POLICY2", result.getValue());
		assertEquals("Y", result.getMostRestrictive());
	}

	@Test
	public void testBuildHouseRules_withCompleteData() {
		HouseRules houseRules = new HouseRules();
		
		// Set up common rules
		List<CommonRules> commonRulesList = new ArrayList<>();
		CommonRules commonRule = new CommonRules();
		commonRule.setCategory("Check-in/Check-out");
		
		List<Rule> rules = new ArrayList<>();
		Rule rule1 = new Rule();
		rule1.setText("Check-in time: 2:00 PM");
		rules.add(rule1);
		
		Rule rule2 = new Rule();
		rule2.setText("Check-out time: 12:00 PM");
		rules.add(rule2);
		
		commonRule.setRules(rules);
		commonRulesList.add(commonRule);
		houseRules.setCommonRules(commonRulesList);
		
		// Set up extra bed policy
		ChildExtraBedPolicy extraBedPolicy = new ChildExtraBedPolicy();
		extraBedPolicy.setLabel("Extra Bed Policy");
		extraBedPolicy.setPolicyInfo("Extra beds available upon request");
		houseRules.setChildExtraBedPolicy(extraBedPolicy);
		
		List<Policy> result = policiesResponseTransformer.buildHouseRules(houseRules);
		
		assertNotNull(result);
		assertFalse(result.isEmpty());
	}

	@Test
	public void testRemoveEmptyString_withMixedData() {
		List<String> inputList = new ArrayList<>();
		inputList.add("Valid rule 1");
		inputList.add("");
		inputList.add("Valid rule 2");
		inputList.add(null);
		inputList.add("Valid rule 3");
		
		List<String> result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "removeEmptyString", inputList);
		
		assertNotNull(result);
		assertEquals(3, result.size());
		assertTrue(result.contains("Valid rule 1"));
		assertTrue(result.contains("Valid rule 2"));
		assertTrue(result.contains("Valid rule 3"));
		assertFalse(result.contains(""));
		assertFalse(result.contains(null));
	}

	@Test
	public void testRemoveEmptyString_withEmptyList() {
		List<String> inputList = new ArrayList<>();
		
		List<String> result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "removeEmptyString", inputList);
		
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	public void testGetPolicyForExtraBed_withCompleteData() {
		ChildExtraBedPolicy extraBedPolicy = new ChildExtraBedPolicy();
		extraBedPolicy.setLabel("Child Extra Bed Policy");
		extraBedPolicy.setPolicyInfo("Extra beds available for children");
		
		// Set up policy rules
		List<PolicyRules> policyRulesList = new ArrayList<>();
		PolicyRules policyRules = new PolicyRules();
		policyRules.setAgeGroup("0-12 years");
		
		Set<ExtraBedRules> extraBedTerms = new HashSet<>();
		ExtraBedRules extraBedRule = new ExtraBedRules();
		extraBedRule.setLabel("Free");
		extraBedRule.setValue("No charge");
		extraBedTerms.add(extraBedRule);
		
		policyRules.setExtraBedTerms(extraBedTerms);
		policyRulesList.add(policyRules);
		extraBedPolicy.setPolicyRules(policyRulesList);
		
		Policy result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getPolicyForExtraBed", extraBedPolicy);
		
		assertNotNull(result);
		assertEquals("Child Extra Bed Policy", result.getTitle());
		assertNotNull(result.getRules());
		assertFalse(result.getRules().isEmpty());
		assertTrue(result.getRules().contains("Extra beds available for children"));
	}

	@Test
	public void testGetPolicyForExtraBed_withMissingLabel() {
		ChildExtraBedPolicy extraBedPolicy = new ChildExtraBedPolicy();
		extraBedPolicy.setLabel(null); // Missing label
		extraBedPolicy.setPolicyInfo("Extra beds available for children");
		
		Policy result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getPolicyForExtraBed", extraBedPolicy);
		
		assertNull(result);
	}

	@Test
	public void testGetPolicyForExtraBed_withMissingPolicyInfo() {
		ChildExtraBedPolicy extraBedPolicy = new ChildExtraBedPolicy();
		extraBedPolicy.setLabel("Child Extra Bed Policy");
		extraBedPolicy.setPolicyInfo(null); // Missing policy info
		
		Policy result = ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "getPolicyForExtraBed", extraBedPolicy);
		
		assertNull(result);
	}

	@Test
	public void testPopulateStaticPolicies_withValidData() {
		List<Policy> policies = new ArrayList<>();
		Map<String, List<String>> policyMap = new HashMap<>();
		
		List<String> rules1 = new ArrayList<>();
		rules1.add("Pool open 6AM-10PM");
		rules1.add("No diving allowed");
		policyMap.put("Pool Policy", rules1);
		
		List<String> rules2 = new ArrayList<>();
		rules2.add("Pets allowed with fee");
		policyMap.put("Pet Policy", rules2);
		
		ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "populateStaticPolicies", policies, policyMap);
		
		assertNotNull(policies);
		assertEquals(2, policies.size());
	}

	@Test
	public void testPopulateStaticPolicies_withEmptyMap() {
		List<Policy> policies = new ArrayList<>();
		Map<String, List<String>> policyMap = new HashMap<>();
		
		ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "populateStaticPolicies", policies, policyMap);
		
		assertNotNull(policies);
		assertTrue(policies.isEmpty());
	}

	@Test
	public void testPopulateUpfrontMustReadPolicies_withNotices() {
		List<Policy> policies = new ArrayList<>();
		
		// Create notices
		List<Notices> notices = new ArrayList<>();
		Notices notice1 = new Notices();
		notice1.setDescription("Important notice 1");
		notices.add(notice1);
		
		Notices notice2 = new Notices();
		notice2.setDescription("Important notice 2");
		notices.add(notice2);
		
		RequestInputBO inputBo = new RequestInputBO.Builder()
				.buildCountryCode("US")
				.buildCancellationPolicyType(BookedCancellationPolicyType.FC.name())
				.buildNotices(notices)
				.build();
		
		ReflectionTestUtils.invokeMethod(policiesResponseTransformer, "populateUpfrontMustReadPolicies", policies, inputBo);
		
		assertNotNull(policies);
		assertFalse(policies.isEmpty());
		
		// Check that notices were added to the policy rules
		Policy policy = policies.get(0);
		assertTrue(policy.getRules().contains("Important notice 1"));
		assertTrue(policy.getRules().contains("Important notice 2"));
	}

	private PersistedMultiRoomData buildPersistedData() {
		PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
		
		List<PersistedHotel> hotelList = new ArrayList<>();
		PersistedHotel persistedHotel = new PersistedHotel();
		
		HotelInfo hotelInfo = new HotelInfo();
		hotelInfo.setHouseRules(getHouseRules());
		hotelInfo.setPolicyToMessagesMap(getPolicyToMessagesMap());
		hotelInfo.setCountryCode("IN");
		persistedHotel.setHotelInfo(hotelInfo );
		
		List<PersistedTariffInfo> tariffInfoList = new ArrayList<>();
		PersistedTariffInfo tarrifInfo = new PersistedTariffInfo();
		
		List<CancelPenalty> cancelPenaltyList = new ArrayList<>();
		CancelPenalty canPenlty = new CancelPenalty();
		canPenlty.setCancellationType(CancellationType.FREE_CANCELLATON);
		cancelPenaltyList.add(canPenlty );
		tarrifInfo.setCancelPenaltyList(cancelPenaltyList );
		
		SupplierDetails supplierDetails = new SupplierDetails();
		supplierDetails.setSupplierCode("INGO");
		tarrifInfo.setSupplierDetails(supplierDetails );
		
		RatePolicy checkinPolicy = new RatePolicy();
		checkinPolicy.setMostRestrictive("Y");
		tarrifInfo.setCheckinPolicy(checkinPolicy );
		
		RatePolicy confirmationPolicy = new RatePolicy();
		confirmationPolicy.setMostRestrictive("Y");
		tarrifInfo.setConfirmationPolicy(confirmationPolicy );
		
		tariffInfoList.add(tarrifInfo );
		persistedHotel.setTariffInfoList(tariffInfoList );
		
		hotelList.add(persistedHotel );
		persistedMultiRoomData.setHotelList(hotelList );
		
		CancellationTimeline cancellationTimeline = new CancellationTimeline();
		persistedMultiRoomData.setCancellationTimeline(cancellationTimeline );
		
		PriceByHotelsRequestBody availReqBody = new PriceByHotelsRequestBody();
		availReqBody.setPayMode("PAH_WITH_CC");
		persistedMultiRoomData.setAvailReqBody(availReqBody );
		return persistedMultiRoomData;
	}

	private HouseRules getHouseRules() {
		HouseRules houseRules = new HouseRules();
		
		List<CommonRules> commonRules = new ArrayList<CommonRules>();
		CommonRules cmnRule = new CommonRules();
		cmnRule.setCategory("");
		List<Rule> rules = new ArrayList<>();
		Rule rule = new Rule();
		rule.setText("rule txst");
		rules.add(rule );
		cmnRule.setRules(rules );
		commonRules.add(cmnRule );
		houseRules.setCommonRules(commonRules );

		
		ChildExtraBedPolicy extraBedPolicy = new ChildExtraBedPolicy();
		extraBedPolicy.setLabel("extra bed");
		extraBedPolicy.setPolicyInfo("extra bed available");
		List<PolicyRules> policyRules = new ArrayList<>();
		PolicyRules policyRule = new PolicyRules();
		policyRule.setAgeGroup("10 to 11");
		Set<ExtraBedRules> extraBedTerms = new HashSet<ExtraBedRules>();
		ExtraBedRules extraBedTerm = new ExtraBedRules();
		extraBedTerm.setLabel("chargable");
		extraBedTerm.setValue("INR 1000");
		extraBedTerms.add(extraBedTerm );
		policyRule.setExtraBedTerms(extraBedTerms );
		policyRules.add(policyRule );
		extraBedPolicy.setPolicyRules(policyRules );
		houseRules.setChildExtraBedPolicy(extraBedPolicy );
		
		
		List<ChildExtraBedPolicy> extraBedPolicyList = new ArrayList<>();
		extraBedPolicyList.add(extraBedPolicy);
		houseRules.setExtraBedPolicyList(extraBedPolicyList );
		
		return houseRules;
	}

	private Map<String, List<String>> getPolicyToMessagesMap() {
		Map<String, List<String>> map = new HashMap<>();
		List<String> rules = new ArrayList<>();
		rules.add("rule1");
		map.put("Category", rules );
		return map;
	}
}
