package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.UpdatePolicyRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.UpdatePolicyRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.UpdatePolicyRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.UpdatePolicyRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.UpdatePolicyResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.UpdatePolicyResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.UpdatePolicyResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.UpdatePolicyResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class UpdatePolicyFactoryTest {


    @InjectMocks
    UpdatePolicyFactory updatePolicyFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyResponseTransformerPWA" , new UpdatePolicyResponseTransformerPWA());
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyResponseTransformerAndroid" , new UpdatePolicyResponseTransformerAndroid());
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyResponseTransformerIOS" , new UpdatePolicyResponseTransformerIOS());
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyResponseTransformerDesktop" , new UpdatePolicyResponseTransformerDesktop());
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyRequestTransformerPWA" , new UpdatePolicyRequestTransformerPWA());
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyRequestTransformerAndroid" , new UpdatePolicyRequestTransformerAndroid());
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyRequestTransformerIOS" , new UpdatePolicyRequestTransformerIOS());
        ReflectionTestUtils.setField(updatePolicyFactory,"updatePolicyRequestTransformerDesktop" , new UpdatePolicyRequestTransformerDesktop());

    }

    @Test
    public void getRequestServiceTest(){
        UpdatePolicyRequestTransformer resp = updatePolicyFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof UpdatePolicyRequestTransformerPWA  );
        resp = updatePolicyFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof UpdatePolicyRequestTransformerDesktop  );
        resp = updatePolicyFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof UpdatePolicyRequestTransformerAndroid  );
        resp = updatePolicyFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof UpdatePolicyRequestTransformerIOS  );
        resp = updatePolicyFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(updatePolicyFactory.getRequestService("test") instanceof UpdatePolicyRequestTransformerDesktop);
    }

    @Test
    public void getResponseServiceTest(){
        UpdatePolicyResponseTransformer resp = updatePolicyFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof UpdatePolicyResponseTransformerPWA  );
        resp = updatePolicyFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof UpdatePolicyResponseTransformerDesktop  );
        resp = updatePolicyFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof UpdatePolicyResponseTransformerAndroid  );
        resp = updatePolicyFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof UpdatePolicyResponseTransformerIOS  );
        resp = updatePolicyFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(updatePolicyFactory.getResponseService("test") instanceof UpdatePolicyResponseTransformerDesktop);
    }


}
