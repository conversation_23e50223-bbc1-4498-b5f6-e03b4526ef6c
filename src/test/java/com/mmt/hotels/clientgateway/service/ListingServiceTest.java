package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.FetchCollectionHelper;
import com.mmt.hotels.clientgateway.helpers.ListingHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.GroupBookingResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.restexecutors.FilterExecutor;
import com.mmt.hotels.clientgateway.restexecutors.ListingMapExecutor;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchHotelsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.transformer.factory.ListingMapFactory;
import com.mmt.hotels.clientgateway.transformer.factory.MobLandingFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.ListingMapRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.SearchHotelsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchHotelsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.pwa.ListingMapRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.MobLandingRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchHotelsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.ListingMapResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ListingMapResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.MobLandingResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.kafka.JsonKafkaProducer;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.pojo.listing.personalization.CardAction;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import com.mmt.hotels.pojo.response.landing.HotelLandingWrapperResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

@RunWith(MockitoJUnitRunner.class)
public class ListingServiceTest {

    @InjectMocks
    ListingService listingService;

    @Mock
    FilterFactory filterFactory;

    @Mock
    FilterExecutor filterExecutor;

    @Mock
    FetchCollectionHelper fetchCollectionHelper;

    @Mock
    private FilterResponseTransformer filterResponseTransformer;

    @Mock
    PolyglotService polyglotService;

    @Mock
    ListingMapFactory listingMapFactory;

    @Mock
    ListingMapExecutor listingMapExecutor;
    
    @Mock
    CommonHelper commonHelper;

    @Mock
    ListingHelper listingHelper;

    @Mock
    private SearchHotelsFactory searchHotelsFactory;

    @Mock
    MetricErrorLogger metricErrorLogger;

    @Mock
    private MobLandingFactory mobLandingFactory;

    @Mock
    private SearchHotelsExecutor searchHotelsExecutor;

    @Mock
    private MobLandingExecutor mobLandingExecutor;

    @Mock
    private OldToNewerRequestTransformer oldToNewerRequestTransformer;
    
    @Mock
    private MobConfigHelper mobConfigHelper;
    
    @Mock
    private HotelMetaDataService hotelMetaDataService;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    JsonKafkaProducer<GroupBookingRequest> jsonKafkaProducer;

    @Mock
    private Utility utility;

    @Mock
    MetricAspect metricAspect;

    @Before
    public void init() {
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
    }

    @Test (expected = Exception.class)
    public void filterCountTest() throws ClientGatewayException {

        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        FilterSearchMetaDataResponse filterResponse = new FilterSearchMetaDataResponse();
        filterResponse.setFilterDataMap(new HashMap<>());
        filterResponse.getFilterDataMap().put(FilterGroup.HOTEL_PRICE, new ArrayList<>());
        FilterResponseTransformer responseTransformer = Mockito.mock(FilterResponseTransformer.class);
        Mockito.when(filterFactory.getFilterConfiguration(Mockito.any(), Mockito.any(),null,Mockito.any())).thenReturn(new FilterConfiguration());

        Mockito.when(responseTransformer.convertFilterResponse(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FilterResponse());

        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(),Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        FilterResponse resp = listingService.filterCount(filterCountRequest, null, null, true);
        Assert.assertNotNull(resp);
        listingService.filterCount(null, null,null, true);

    }

    @Test (expected = Exception.class)
    public void filterCountOldTest() throws ClientGatewayException {

        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.lenient().when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());

        Mockito.when(filterExecutor.filterCount(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn("abcd");
        SearchWrapperInputRequest filterCountRequest = new SearchWrapperInputRequest();
        String resp = listingService.filterCountOld(filterCountRequest,null,null);
        Assert.assertNotNull(resp);

        listingService.filterCountOld(null,null,null);

    }

    @Test (expected = Exception.class)
    public void mobLandingTest() throws ClientGatewayException {
        MobLandingRequest mobLandingRequest = new MobLandingRequest();
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("MOBILE");
        deviceDetails.setDeviceType("android");
        mobLandingRequest.setDeviceDetails(deviceDetails);
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("DAYUSE");
        requestDetails.setSiteDomain("IN");
        mobLandingRequest.setRequestDetails(requestDetails);
        Mockito.doNothing().when(commonHelper).updateCurrencyAndSource(Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(commonHelper.getFlavour(Mockito.any())).thenReturn("android");
        MobLandingRequestTransformerPWA mobLandingTransformer = Mockito.mock(MobLandingRequestTransformerPWA.class);
        HotelLandingMobRequestBody moblandingBody = new HotelLandingMobRequestBody();
        Mockito.when(mobLandingFactory.getRequestService(Mockito.any())).thenReturn(mobLandingTransformer);
        Mockito.when(mobLandingTransformer.convertMobLandingRequest(Mockito.any(), Mockito.any())).thenReturn(moblandingBody);
        Future<String> response = CompletableFuture.completedFuture("{\"listPersonalizationResponse\":{\"cardData\":{\"2\":[{\"cardSubType\":\"CARD\",\"minItemsToShow\":0,\"index\":1,\"cardId\":\"RECENTSEARCH\",\"hasAction\":false,\"hasFilter\":false,\"hasToggle\":false,\"hasLocation\":false,\"templateId\":\"RECENT_SEARCH_CAROUSEL\",\"hasToolTip\":false,\"imageAvailable\":false,\"claimed\":false}],\"6\":[{\"cardSubType\":\"CARD\",\"minItemsToShow\":0,\"index\":1,\"cardId\":\"FAQ\",\"hasAction\":false,\"hasFilter\":false,\"hasToggle\":false,\"hasLocation\":false,\"templateId\":\"FAQ_CARD\",\"hasToolTip\":false,\"imageAvailable\":false,\"claimed\":false}],\"7\":[{\"cardSubType\":\"CAROUSEL\",\"minItemsToShow\":0,\"index\":1,\"cardId\":\"OFFERS\",\"hasAction\":false,\"hasFilter\":false,\"hasToggle\":false,\"hasLocation\":false,\"templateId\":\"OFFERS_CAROUSEL\",\"hasToolTip\":false,\"imageAvailable\":false,\"claimed\":false}]},\"experimentId\":0,\"trackText\":\"RECENTSEARCH_CARD_S|FAQ_CARD_S|OFFERS_CAROUSEL_S|\"},\"uuids\":[],\"completedRequests\":[\"PERSONALIZATION\"],\"currentTimeStamp\":1714630883701}");
        Future<Object> objectResponse = CompletableFuture.completedFuture(new Object());
        Mockito.when(mobLandingExecutor.moblanding(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(response);
        MobLandingResponseTransformerPWA mobLandingRespTransformer = Mockito.mock(MobLandingResponseTransformerPWA.class);
        HotelLandingWrapperResponse hotelLandingWrapperResponse = new HotelLandingWrapperResponse();
        ListPersonalizationResponse listPersonalizationResponse = new ListPersonalizationResponse();
        hotelLandingWrapperResponse.setCompletedRequests(new HashSet<>());
        hotelLandingWrapperResponse.setCurrentTimeStamp(2151234);
        hotelLandingWrapperResponse.setUuids(new HashSet<>());
        hotelLandingWrapperResponse.setListPersonalizationResponse(listPersonalizationResponse);
        RequiredApis requiredApis = new RequiredApis();
        requiredApis.setPagemakerRequired(true);
        requiredApis.setPersonalizationRequired(true);
        mobLandingRequest.setRequiredApis(requiredApis);
        Mockito.when(mobLandingExecutor.getPageMakerChunkData(Mockito.anyString(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(objectResponse);
        Mockito.when(mobLandingExecutor.getPageMakerRenderedData(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(objectResponse);
        Mockito.when(objectMapperUtil.getObjectFromJson(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(hotelLandingWrapperResponse);
        MobLandingResponse resp = listingService.mobLanding(mobLandingRequest,null,null);
        Assert.assertNotNull(resp);
        listingService.mobLanding(null,null,null);

    }

    @Test
    public void getCardInfoTest(){
        CardCollections cardCollections = new CardCollections();
        cardCollections.setCardInfo(new CardData());
        List<CardAction> cardActionList = new ArrayList<>();
        cardActionList.add(new CardAction());
        cardCollections.getCardInfo().setCardAction(cardActionList);
        cardCollections.getCardInfo().setCardPayload(new CardPayloadResponse());
        List<GenericCardPayloadData> genericCardPayloadDataList = new ArrayList<>();
        GenericCardPayloadData genericCardPayloadData = new GenericCardPayloadData();
        // Ensure no recursive reference is set here
        genericCardPayloadDataList.add(genericCardPayloadData);

        cardCollections.getCardInfo().getCardPayload().setGenericCardData(genericCardPayloadDataList);
        Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingService, "getCardInfo", cardCollections));
    }




    @Test (expected = Exception.class)
    public void listingMapTest() throws ClientGatewayException {

        ListingMapRequestTransformer searchTransformer = Mockito.mock(ListingMapRequestTransformerPWA.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(listingMapFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertListingMapRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());

        Mockito.when(listingMapExecutor.listingMap(Mockito.any(), Mockito.any())).thenReturn(new HotelListingMapResponse());
        ListingMapResponseTransformer responseTransformer = Mockito.mock(ListingMapResponseTransformerPWA.class);
        Mockito.when(listingMapFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
        Mockito.when(responseTransformer.convertListingMapResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ListingMapResponse());

        ListingMapResponse resp = listingService.listingMap(new ListingMapRequest(),null,null);
        Assert.assertNotNull(resp);
        listingService.listingMap(null,null,null);

    }

    @Test (expected = Exception.class)
    public void testFetchCollectionsOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        String responseHes="{\"cardCollections\":[{\"cardType\":\"SUGGESTEDFORYOU\",\"heading\":\"Suggested for you\",\"subHeading\":\"\",\"priority\":\"7\",\"priorities\":\"9\",\"cardList\":[{\"description\":\"Explore Romantic Beach & Mountain Stays!\",\"imageUrl\":\"\",\"priority\":\"7\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"1e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"1\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"GOA\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&_uCurrency=INR&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CVilla%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Villa%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}},{\"description\":\"Stay More,Pay Less!\",\"imageUrl\":\"\",\"priority\":\"7\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"1e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"1\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"GOA\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&_uCurrency=INR&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CVilla%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Villa%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}}]},{\"cardType\":\"PROPERTY\",\"heading\":\"For all your travel needs\",\"subHeading\":\"\",\"priority\":\"19\",\"cardList\":[{\"description\":\"Homestays\",\"imageUrl\":\"\",\"priority\":\"19\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"2e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"2\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=2e0e&checkAvailability=true&_uCurrency=INR&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CHomestay%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=2e0e&checkAvailability=true&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Homestay%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Homestay\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}},{\"description\":\"Villas\",\"imageUrl\":\"\",\"priority\":\"7\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"1e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"1\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&_uCurrency=INR&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CVilla%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Villa%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}}]}],\"correlationKey\":\"8474e6ec-8e43-4a39-b2d8-f4a4eac77200\",\"responseErrors\":{\"errorList\":[{\"errorCode\":\"400000\",\"errorMessage\":\"Unexpected Error happened during processing %s API, Exception Message: %s\",\"errorAdditionalInfo\":null}]}}";
        Mockito.when(listingService.convertHesCollectionResponseToOldFormat(Mockito.anyString())).thenReturn("test");
        Assert.assertNotNull(listingService.fetchCollectionsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.fetchCollectionsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>());
        
    }

    @Test (expected = Exception.class)
    public void testSearchHotelsOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Assert.assertNotNull(listingService.searchHotelsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchHotelsExecutor.searchHotelsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.searchHotelsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>());
    }

    @Test (expected = Exception.class)
    public void testListingMapOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Assert.assertNotNull(listingService.searchHotelsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchHotelsExecutor.listingMapOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.listingMapOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>());
    }

    @Test (expected = Exception.class)
    public void searchHotelsTest() throws ClientGatewayException {

    	SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());

        Mockito.when(searchHotelsExecutor.searchHotels(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new SearchWrapperResponseBO.Builder().build());
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerPWA.class);
        Mockito.when(searchHotelsFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
        Mockito.when(responseTransformer.convertSearchHotelsResponse(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new SearchHotelsResponse());
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(false);
        //TODO : FOR REARCH
        HashMap<String, String> expMap = new HashMap<>();
        expMap.put("rearch","F");
        searchHotelsRequest.setExpDataMap(expMap);
        SearchHotelsResponse resp = listingService.searchHotels(searchHotelsRequest,null,null);
        Assert.assertNotNull(resp);
        searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(true);
        Mockito.when(searchHotelsExecutor.searchPersonalizedHotels(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ListingPagePersonalizationResponsBO());
        resp = listingService.searchHotels(searchHotelsRequest,null,null);
        Assert.assertNotNull(resp);
        listingService.searchHotels(null,null,null);

    }

    @Test (expected = Exception.class)
    public void testGetMobConfig() throws ClientGatewayException {

        Mockito.when(mobConfigHelper.getHotelMobConfigAsString()).thenReturn("Response");
        Assert.assertNotNull(listingService.getMobConfig());
        Mockito.reset(mobConfigHelper.getHotelMobConfigAsString());
        listingService.getMobConfig();
    }
    
//    @Test
//    public void testGetMetaDataByCityResponse() throws ClientGatewayException{
//    	Mockito.when(commonHelper.getInboundCurrencyCode(Mockito.any(), Mockito.any(),
//    			Mockito.any())).thenReturn("INR");
//    	Mockito.when(searchHotelsExecutor.getMetaDataResponse(Mockito.any(),
//    			Mockito.anyMap())).thenReturn("");
//
//    	Mockito.when(hotelMetaDataService.filterLocationAndFaclity(Mockito.any(),
//    			Mockito.any())).thenReturn("test");
//
//    	String resp = listingService.getMetaDataByCityResponse("cityId", null, null,
//    			null, "correlationKey", new HashMap<>());
//    	Assert.assertNotNull(resp);
//    }

    @Test (expected = Exception.class)
    public void testListingPersonalizationOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(mobLandingExecutor.listPersonalizedCards(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(listingService.listingPersonalizationOld(new HotelLandingMobRequestBody(),Mockito.any(),new HashMap<>()));
        Mockito.when(mobLandingExecutor.listPersonalizedCards(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.listingPersonalizationOld(new HotelLandingMobRequestBody(),Mockito.any(),new HashMap<>());
    }

    @Test (expected = Exception.class)
    public void testNearByOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateNearByHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.nearByHotelsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(listingService.nearByOld(new SearchWrapperInputRequest(),Mockito.any(),new HashMap<>()));
        Mockito.when(searchHotelsExecutor.nearByHotelsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.nearByOld(new SearchWrapperInputRequest(),Mockito.any(),new HashMap<>());
    }

    @Test
    public void testConvertHesCollectionResponseToOldFormat() throws ClientGatewayException, JsonProcessingException {
        String resp="{\"cardCollections\":[{\"cardType\":\"TRENDINGNOW\",\"heading\":\"Trending Now\",\"subHeading\":\"\",\"priority\":\"4\",\"cardList\":[{\"description\":\"Spacious apartments for a fun-filled vacay\",\"imageUrl\":\"https://promos.makemytrip.com/altaccoimages/group_2.png\",\"priority\":\"4\",\"searchContext\":{\"checkIn\":\"2021-05-28\",\"checkOut\":\"2021-05-30\",\"roomStayParams\":\"2e0e\"},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"GOA\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"cityPriority\":0,\"appliedFilterMap\":{\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}],\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}]}}}}]}]}";
        CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = new ObjectMapper().readValue(resp,new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {});
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(collectionsResponseBO);
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(listingService.convertHesCollectionResponseToOldFormat(resp));

    }

    @Test (expected = Exception.class)
    public void testFetchCollections() throws ClientGatewayException {
        SearchHotelsRequestTransformer  searchHotelsRequestTransformer=Mockito.mock(SearchHotelsRequestTransformerAndroid.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerAndroid.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");

        CollectionsResponseBo<SearchWrapperHotelEntity> response=createSuccessDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Mockito.when(searchHotelsFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
        Assert.assertNotNull(listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()));

        response=createFailedDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Assert.assertNotNull(listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()).getResponseErrors());

        response=createDummyHesResponseWithNoCollections();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setPageContext(Constants.PAGE_CONTEXT_REVIEW);
        fetchCollectionRequest.setRequestDetails(requestDetails);
        Assert.assertNotNull(listingService.fetchCollections(fetchCollectionRequest,new HashMap<>(),new HashMap<>()).getResponseErrors());

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>());

    }

    @Test (expected = Exception.class)
    public void testFetchCollectionsV2() throws ClientGatewayException {
        SearchHotelsRequestTransformer  searchHotelsRequestTransformer=Mockito.mock(SearchHotelsRequestTransformerAndroid.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerAndroid.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");

        Mockito.when(fetchCollectionHelper.shouldAddFilters(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);

        CollectionsResponseBo<SearchWrapperHotelEntity> response=createSuccessDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Mockito.when(searchHotelsFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
        Assert.assertNotNull(listingService.fetchCollectionsV2(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()));

        response=createFailedDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Assert.assertNotNull(listingService.fetchCollectionsV2(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()).getResponseErrors());

        response=createDummyHesResponseWithNoCollections();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setPageContext(Constants.PAGE_CONTEXT_REVIEW);
        fetchCollectionRequest.setRequestDetails(requestDetails);
        Assert.assertNotNull(listingService.fetchCollectionsV2(fetchCollectionRequest,new HashMap<>(),new HashMap<>()).getResponseErrors());

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.fetchCollectionsV2(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>());

    }

    @Test
    public void testFetchCollections2() throws ClientGatewayException {
        SearchHotelsRequestTransformer  searchHotelsRequestTransformer=Mockito.mock(SearchHotelsRequestTransformerAndroid.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerAndroid.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");

        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(JsonParseException.class);
        Assert.assertNotNull(listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()));

    }

    private CollectionsResponseBo<SearchWrapperHotelEntity> createSuccessDummyHesResponse() {
        CollectionsResponseBo<SearchWrapperHotelEntity> resp=new CollectionsResponseBo<SearchWrapperHotelEntity>();
        resp.setCardCollections(createCardCollection());
        resp.setCollectionsResponse(createCollections());
        resp.setCorrelationKey("csdfsd");
        resp.setSuggestedFilters(new CardCollections());
        resp.setMoreFiltersMap(createDummyMoreFiltersMap());
        return resp;
    }

    private CollectionsResponseBo<SearchWrapperHotelEntity> createFailedDummyHesResponse() {
        CollectionsResponseBo<SearchWrapperHotelEntity> resp=new CollectionsResponseBo<SearchWrapperHotelEntity>();
        resp.setResponseErrors(new ResponseErrors());
        resp.setCorrelationKey("csdfsd");
        return resp;
    }

    private CollectionsResponseBo<SearchWrapperHotelEntity> createDummyHesResponseWithNoCollections() {
        CollectionsResponseBo<SearchWrapperHotelEntity> resp=new CollectionsResponseBo<SearchWrapperHotelEntity>();
        resp.setCorrelationKey("csdfsd");
        return resp;
    }

    private List<FeaturedCollections<SearchWrapperHotelEntity>> createCollections() {
        List<FeaturedCollections<SearchWrapperHotelEntity>> list=new ArrayList<>();
        FeaturedCollections<SearchWrapperHotelEntity> fc=new FeaturedCollections<SearchWrapperHotelEntity>();
        list.add(fc);
        return list;
    }

    private Map<String, List<Filter>> createDummyMoreFiltersMap() {
        Map<String, List<Filter>> moreFiltersMap = new HashMap<>();
        List<Filter> filterList = new ArrayList<>();
        Filter filter = new Filter();
        FilterGroup filterGroup = FilterGroup.UGC_GROUP_RATING;
        filter.setFilterGroup(filterGroup);
        filter.setTitle("4 to 5");
        filter.setFilterValue("4_5");
        filter.setRangeFilter(false);
        filter.setSequence(5);
        filter.setThreshold(30);
        filter.setQuantityFilter(false);
        filterList.add(filter);
        moreFiltersMap.put("Popular Filters", filterList);
        return moreFiltersMap;
    }

    private List<CardCollections> createCardCollection() {
        List<CardCollections> cardCollectionList=new ArrayList<>();
        CardCollections cardCollection=new CardCollections();
        cardCollectionList.add(cardCollection);
        return cardCollectionList;
    }

//    @Test  (expected = Exception.class)
//    public void submitGroupBookingExceptionTest() throws ClientGatewayException {
//        listingService.submitGroupBooking(Mockito.any());
//    }

    @Test
    public void submitGroupBookingTest() throws ClientGatewayException {
        Assert.assertNotNull(listingService.submitGroupBooking(new GroupBookingRequest()));
    }

    @Test (expected = Exception.class)
    public void batchFilterResponseTest() throws ClientGatewayException {

        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(),Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.lenient().when(filterExecutor.filterCount(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(filterSearchMetaDataResponse);
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setClient("DESKTOP");
        filterCountRequest.setRequestDetails(new RequestDetails());
        FilterResponse resp = listingService.batchFilterResponse(filterCountRequest,null,true);
        Assert.assertNotNull(resp);

        listingService.batchFilterResponse(null,null,null);

    }

    @Test (expected = Exception.class)
    public void batchFilterResponseThrowsExceptionTest() throws ClientGatewayException {

        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        List<Error> errors = new ArrayList<>();
        errors.add(new Error.Builder().buildErrorCode("1234","error ").build());
        filterSearchMetaDataResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.lenient().when(filterExecutor.filterCount(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(filterSearchMetaDataResponse);
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        FilterResponse resp = listingService.batchFilterResponse(filterCountRequest,null,true);
        Assert.assertNotNull(resp);

        listingService.batchFilterResponse(null,null,null);

    }

    @Test (expected = Exception.class)
    public void testLandingDiscoveryOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.landingDiscoveryOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(listingService.landingDiscoveryOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchHotelsExecutor.landingDiscoveryOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.landingDiscoveryOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>());
    }

    @Test
    public void createParamsMapTest() throws ClientGatewayException {
        ListingSearchRequest listingSearchRequest = new ListingSearchRequest();
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        searchHotelsCriteria.setRoomStayCandidates(new ArrayList<>());
        searchHotelsCriteria.getRoomStayCandidates().add(new RoomStayCandidate());
        searchHotelsCriteria.getRoomStayCandidates().get(0).setAdultCount(2);
        searchHotelsCriteria.getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        searchHotelsCriteria.setCheckIn("2024-05-13");
        searchHotelsCriteria.setCheckOut("2024-05-14");
        listingSearchRequest.setSearchCriteria(searchHotelsCriteria);
        Map<String, Object> result = listingService.createParamsMap(listingSearchRequest,"LANDING");
       Assert.assertNotNull(result);
    }

    @Test
    public void testCreateParamsMap() throws ClientGatewayException {
        ListingSearchRequest request = new ListingSearchRequest();
        SearchHotelsCriteria criteria = new SearchHotelsCriteria();
        criteria.setCheckIn("2023-12-01");
        criteria.setCheckOut("2023-12-05");
        criteria.setRoomStayCandidates(Collections.singletonList(new RoomStayCandidate(2, Arrays.asList(5, 7))));
        request.setSearchCriteria(criteria);

        Map<String, Object> paramsMap = listingService.createParamsMap(request, "LISTING");
        Assert.assertEquals(2, paramsMap.get("pax_adult"));
        Assert.assertEquals(2, paramsMap.get("pax_child"));
        Assert.assertEquals("20231201", paramsMap.get("checkin_date"));
        Assert.assertEquals("20231205", paramsMap.get("checkout_date"));
    }

    @Test
    public void testSubmitGroupBooking() throws ClientGatewayException {
        GroupBookingRequest request = new GroupBookingRequest();
        GroupBookingResponse response = listingService.submitGroupBooking(request);
        Assert.assertNotNull(response);
    }


}