package com.mmt.hotels.clientgateway.service;

import com.gommt.hotels.orchestrator.detail.enums.Brand;
import com.gommt.hotels.orchestrator.detail.enums.Country;
import com.gommt.hotels.orchestrator.detail.enums.Currency;
import com.gommt.hotels.orchestrator.detail.enums.DeviceType;
import com.gommt.hotels.orchestrator.detail.enums.Funnel;
import com.gommt.hotels.orchestrator.detail.enums.IdContext;
import com.gommt.hotels.orchestrator.detail.enums.PageContext;
import com.gommt.hotels.orchestrator.detail.enums.SiteDomain;
import com.gommt.hotels.orchestrator.detail.enums.SubProfileType;
import com.gommt.hotels.orchestrator.detail.enums.TrafficType;
import com.gommt.hotels.orchestrator.detail.model.request.CalendarAvailabilityRequest;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.FilterDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.RangeDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse;
import com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.helpers.OrchAlternatePriceHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.FilterRange;
import com.mmt.hotels.clientgateway.request.ImageCategory;
import com.mmt.hotels.clientgateway.request.ImageDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.SemanticSearchDetails;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.UpdatedPriceCriteria;
import com.mmt.hotels.clientgateway.request.UpdatedPriceRoomCriteria;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.restexecutors.OrchDetailExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.factory.OrchStaticDetailFactory;
import com.mmt.hotels.clientgateway.transformer.factory.OrchUpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchSlotsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.request.OrchUpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.OrchUpdatedPriceResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchSlotsResponseTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.UserLocation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_VALUE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrchDetailServiceTest {

    @InjectMocks
    private OrchDetailService orchDetailService;

    @Mock
    private Utility utility;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private OrchDetailExecutor orchDetailExecutor;

    @Mock
    private SearchRoomsFactory searchRoomsFactory;

    @Mock
    private SearchSlotsFactory searchSlotsFactory;

    @Mock
    private OrchStaticDetailFactory orchStaticDetailFactory;

    @Mock
    private OrchUpdatedPriceFactory orchUpdatedPriceFactory;

    @Mock
    private OrchUpdatedPriceRequestTransformer orchUpdatedPriceRequestTransformer;

    @Mock
    OrchAlternatePriceHelper orchAlternatePriceHelper;

    @Mock
    ObjectMapperUtil objectMapperUtil;

    @Mock
    RestConnectorUtil restConnectorUtil;

    private SearchRoomsRequest searchRoomsRequest;
    private StaticDetailRequest staticDetailRequest;
    private UpdatePriceRequest updatePriceRequest;
    private DayUseRoomsRequest dayUseRoomsRequest;
    private CommonModifierResponse commonModifierResponse;

    @BeforeEach
    void setUp() {
        searchRoomsRequest = createSearchRoomsRequest();
        staticDetailRequest = createStaticDetailRequest();
        updatePriceRequest = createUpdatePriceRequest();
        dayUseRoomsRequest = createDayUseRoomsRequest();
        commonModifierResponse = createCommonModifierResponse();
        ReflectionTestUtils.setField(orchDetailService, "orchAlternatePriceHelper", orchAlternatePriceHelper);
        ReflectionTestUtils.setField(orchDetailService, "objectMapperUtil", objectMapperUtil);
        ReflectionTestUtils.setField(orchDetailService, "restConnectorUtil", restConnectorUtil);
        MDC.put("language", "english");
        MDC.put("region", "in");
    }

    // =================================================================
    // SEARCH SLOTS TESTS
    // =================================================================

    @Test
    @DisplayName("Test searchSlots - Success")
    void testSearchSlots_Success() throws ClientGatewayException {
        // Arrange
        HotelDetailsResponse mockResponse = mock(HotelDetailsResponse.class);
        DayUseRoomsResponse mockDayUseRoomsResponse = mock(DayUseRoomsResponse.class);
        OrchSearchSlotsResponseTransformer mockTransformer = mock(OrchSearchSlotsResponseTransformer.class);

        when(orchDetailExecutor.searchRooms(any(DetailRequest.class), any(), any())).thenReturn(mockResponse);
        when(searchSlotsFactory.getOrchResponseService(anyString())).thenReturn(mockTransformer);
        when(mockTransformer.convertSearchSlotsResponse(any(), any(), any()))
                .thenReturn(mockDayUseRoomsResponse);

        // Act
        DayUseRoomsResponse result = orchDetailService.searchSlots(dayUseRoomsRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse);

        // Assert
        assertNotNull(result);
        verify(orchDetailExecutor, times(1)).searchRooms(any(DetailRequest.class), any(), any());
        verify(searchSlotsFactory, times(1)).getOrchResponseService(anyString());
        verify(metricAspect, times(2)).addToTimeInternalProcess(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("Test searchSlots - Downstream Error")
    void testSearchSlots_DownstreamError() throws ClientGatewayException {
        // Arrange
        when(orchDetailExecutor.searchRooms(any(DetailRequest.class), any(), any()))
                .thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, "Downstream error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.searchSlots(dayUseRoomsRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    @Test
    @DisplayName("Test searchSlots - General Exception")
    void testSearchSlots_GeneralException() throws ClientGatewayException {
        // Arrange
        when(orchDetailExecutor.searchRooms(any(DetailRequest.class), any(), any())).thenThrow(new RuntimeException("General error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.searchSlots(dayUseRoomsRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    // =================================================================
    // BUILD SEARCH SLOTS REQUEST TESTS
    // =================================================================

    @Test
    @DisplayName("Test buildSearchSlotsRequest - Null Request")
    void testBuildSearchSlotsRequest_NullRequest() {
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                orchDetailService.buildSearchSlotsRequest(null, commonModifierResponse));
        assertEquals("DayUseRoomsRequest cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - Null CommonModifierResponse")
    void testBuildSearchSlotsRequest_NullCommonModifierResponse() {
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, null));
        assertEquals("CommonModifierResponse cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Existing Image Categories")
    void testBuildSearchSlotsRequest_WithExistingImageCategories() {
        // Arrange - Set up image details with existing "R" category
        ImageDetails imageDetails = new ImageDetails();
        ImageCategory category = new ImageCategory();
        category.setType("R");
        category.setCount(50);
        category.setHeight(400);
        category.setWidth(600);
        category.setImageFormat("JPG");
        imageDetails.setCategories(Arrays.asList(category));
        dayUseRoomsRequest.setImageDetails(imageDetails);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getImageDetails().getCategories());
        assertEquals(1, result.getImageDetails().getCategories().size());
        assertEquals("R", result.getImageDetails().getCategories().get(0).getType());
        assertEquals(50, result.getImageDetails().getCategories().get(0).getCount()); // Should use existing count, not default 100
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Empty Image Categories")
    void testBuildSearchSlotsRequest_WithEmptyImageCategories() {
        // Arrange - Set up image details with empty categories
        ImageDetails imageDetails = new ImageDetails();
        imageDetails.setCategories(new ArrayList<>());
        dayUseRoomsRequest.setImageDetails(imageDetails);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getImageDetails().getCategories());
        assertEquals(1, result.getImageDetails().getCategories().size()); // Should add default "R" category
        assertEquals("R", result.getImageDetails().getCategories().get(0).getType());
        assertEquals(100, result.getImageDetails().getCategories().get(0).getCount());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null Image Details")
    void testBuildSearchSlotsRequest_WithNullImageDetails() {
        // Arrange - Set null image details
        dayUseRoomsRequest.setImageDetails(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getImageDetails().getCategories());
        assertEquals(1, result.getImageDetails().getCategories().size()); // Should add default "R" category
        assertEquals("R", result.getImageDetails().getCategories().get(0).getType());
        assertEquals(100, result.getImageDetails().getCategories().get(0).getCount());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null Slot")
    void testBuildSearchSlotsRequest_WithNullSlot() {
        // Arrange - Set null slot
        dayUseRoomsRequest.getSearchCriteria().setSlot(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSlotDetails());
        assertEquals(0, result.getSlotDetails().getTimeSlot());
        assertEquals(0, result.getSlotDetails().getDuration());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null RoomStayCandidates")
    void testBuildSearchSlotsRequest_WithNullRoomStayCandidates() {
        // Arrange - Set null room stay candidates
        dayUseRoomsRequest.getSearchCriteria().setRoomStayCandidates(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRooms());
        assertTrue(result.getRooms().isEmpty());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Empty RoomStayCandidates")
    void testBuildSearchSlotsRequest_WithEmptyRoomStayCandidates() {
        // Arrange - Set empty room stay candidates
        dayUseRoomsRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRooms());
        assertTrue(result.getRooms().isEmpty());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null Filter Criteria")
    void testBuildSearchSlotsRequest_WithNullFilterCriteria() {
        // Arrange - Set null filter criteria
        dayUseRoomsRequest.setFilterCriteria(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertTrue(result.getFilters().isEmpty());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null MatchMaker Details")
    void testBuildSearchSlotsRequest_WithNullMatchMakerDetails() {
        // Arrange - Set null matchmaker details
        dayUseRoomsRequest.setMatchMakerDetails(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFilters());
        // Should still build filters successfully without matchmaker details
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null Feature Flags")
    void testBuildSearchSlotsRequest_WithNullFeatureFlags() {
        // Arrange - Set null feature flags
        dayUseRoomsRequest.setFeatureFlags(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getClientDetails());
        assertNotNull(result.getClientDetails().getFeatureFlags());
        // Should build with default feature flag values
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null Exp Data Map")
    void testBuildSearchSlotsRequest_WithNullExpDataMap() {
        // Arrange - Set null exp data map
        dayUseRoomsRequest.setExpDataMap(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRooms());
        // Should build successfully with null exp data map
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null Exp Data")
    void testBuildSearchSlotsRequest_WithNullExpData() {
        // Arrange - Set null exp data
        dayUseRoomsRequest.setExpData(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNull(result.getExperimentData());
    }

    @Test
    @DisplayName("Test buildSearchSlotsRequest - With Null Exp Variant Keys")
    void testBuildSearchSlotsRequest_WithNullExpVariantKeys() {
        // Arrange - Set null exp variant keys
        dayUseRoomsRequest.setExpVariantKeys(null);

        // Act
        DetailRequest result = orchDetailService.buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);

        // Assert
        assertNotNull(result);
        assertNull(result.getExpVariantKeys());
    }

    // =================================================================
    // SEARCH ROOMS TESTS
    // =================================================================

    @Test
    @DisplayName("Test searchRooms - Success")
    void testSearchRooms_Success() throws ClientGatewayException {
        // Arrange
        HotelDetailsResponse mockResponse = mock(HotelDetailsResponse.class);
        SearchRoomsResponse mockSearchRoomsResponse = mock(SearchRoomsResponse.class);
        OrchSearchRoomsResponseTransformer mockTransformer = mock(OrchSearchRoomsResponseTransformer.class);

        when(orchDetailExecutor.searchRooms(any(DetailRequest.class), any(), any())).thenReturn(mockResponse);
        when(searchRoomsFactory.getSearchRoomsResponseService(anyString())).thenReturn(mockTransformer);
        when(mockTransformer.convertSearchRoomsResponse(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(mockSearchRoomsResponse);

        // Act
        SearchRoomsResponse result = orchDetailService.searchRooms(searchRoomsRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse);

        // Assert
        assertNotNull(result);
        verify(orchDetailExecutor, times(1)).searchRooms(any(DetailRequest.class), any(), any());
        verify(searchRoomsFactory, times(1)).getSearchRoomsResponseService(anyString());
        verify(metricAspect, times(2)).addToTimeInternalProcess(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("Test searchRooms - Downstream Error")
    void testSearchRooms_DownstreamError() throws ClientGatewayException {
        // Arrange
        when(orchDetailExecutor.searchRooms(any(DetailRequest.class), any(), any()))
                .thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, "Downstream error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.searchRooms(searchRoomsRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    @Test
    @DisplayName("Test searchRooms - General Exception")
    void testSearchRooms_GeneralException() throws ClientGatewayException {
        // Arrange
        when(orchDetailExecutor.searchRooms(any(DetailRequest.class), any(), any())).thenThrow(new RuntimeException("General error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.searchRooms(searchRoomsRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    // =================================================================
    // UPDATE PRICE TESTS
    // =================================================================

    @Test
    @DisplayName("Test updatePrice - Success")
    void testUpdatePrice_Success() throws ClientGatewayException {
        // Arrange
        DetailRequest mockDetailRequest = mock(DetailRequest.class);
        UpdatePriceResponse mockUpdatePriceResponse = mock(UpdatePriceResponse.class);
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse mockClientResponse =
                mock(com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse.class);
        OrchUpdatedPriceResponseTransformer mockTransformer = mock(OrchUpdatedPriceResponseTransformer.class);

        when(orchUpdatedPriceRequestTransformer.buildUpdatePriceRequest(any(), any())).thenReturn(mockDetailRequest);
        when(orchDetailExecutor.updatePrice(any(), any(), any())).thenReturn(mockUpdatePriceResponse);
        when(orchUpdatedPriceFactory.getOrchResponseService(anyString())).thenReturn(mockTransformer);
        when(mockTransformer.convertUpdatePriceResponse(any(), any(), any(), any(), any())).thenReturn(mockClientResponse);

        // Act
        com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse result =
                orchDetailService.updatePrice(updatePriceRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse);

        // Assert
        assertNotNull(result);
        verify(orchUpdatedPriceRequestTransformer, times(1)).buildUpdatePriceRequest(any(), any());
        verify(orchDetailExecutor, times(1)).updatePrice(any(), any(), any());
        verify(metricAspect, times(1)).addToTimeInternalProcess(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("Test updatePrice - Downstream Error")
    void testUpdatePrice_DownstreamError() throws ClientGatewayException {
        // Arrange
        when(orchUpdatedPriceRequestTransformer.buildUpdatePriceRequest(any(), any())).thenReturn(mock(DetailRequest.class));
        when(orchDetailExecutor.updatePrice(any(), any(), any()))
                .thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, "Downstream error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.updatePrice(updatePriceRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    @Test
    @DisplayName("Test updatePrice - General Exception")
    void testUpdatePrice_GeneralException() throws ClientGatewayException {
        // Arrange
        when(orchUpdatedPriceRequestTransformer.buildUpdatePriceRequest(any(), any())).thenReturn(mock(DetailRequest.class));
        when(orchDetailExecutor.updatePrice(any(), any(), any())).thenThrow(new RuntimeException("General error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.updatePrice(updatePriceRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    // =================================================================
    // STATIC DETAILS TESTS
    // =================================================================

    @Test
    @DisplayName("Test staticDetails - Success")
    void testStaticDetails_Success() throws ClientGatewayException {
        // Arrange
        HotelStaticContentResponse mockResponse = mock(HotelStaticContentResponse.class);
        StaticDetailResponse mockStaticDetailResponse = mock(StaticDetailResponse.class);
        OrchStaticDetailResponseTransformer mockTransformer = mock(OrchStaticDetailResponseTransformer.class);

        when(orchDetailExecutor.staticDetails(any(DetailRequest.class), any(), any())).thenReturn(mockResponse);
        when(orchStaticDetailFactory.getResponseService(anyString())).thenReturn(mockTransformer);
        when(mockTransformer.convertStaticDetailResponse(any(), any(), any())).thenReturn(mockStaticDetailResponse);

        // Act
        StaticDetailResponse result = orchDetailService.staticDetails(staticDetailRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse);

        // Assert
        assertNotNull(result);
        verify(orchDetailExecutor, times(1)).staticDetails(any(DetailRequest.class), any(), any());
        verify(orchStaticDetailFactory, times(1)).getResponseService(anyString());
        verify(metricAspect, times(2)).addToTimeInternalProcess(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("Test staticDetails - Downstream Error")
    void testStaticDetails_DownstreamError() throws ClientGatewayException {
        // Arrange
        when(orchDetailExecutor.staticDetails(any(DetailRequest.class), any(), any()))
                .thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, "Downstream error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.staticDetails(staticDetailRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    @Test
    @DisplayName("Test staticDetails - General Exception")
    void testStaticDetails_GeneralException() throws ClientGatewayException {
        // Arrange
        when(orchDetailExecutor.staticDetails(any(DetailRequest.class), any(), any())).thenThrow(new RuntimeException("General error"));

        // Act & Assert
        assertThrows(ClientGatewayException.class, () ->
                orchDetailService.staticDetails(staticDetailRequest, new HashMap<>(), new HashMap<>(), commonModifierResponse));
    }

    // =================================================================
    // BUILDER METHOD TESTS
    // =================================================================

    @Test
    @DisplayName("Test buildSearchRoomsRequest - Null Request")
    void testBuildSearchRoomsRequest_NullRequest() {
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                orchDetailService.buildSearchRoomsRequest(null, commonModifierResponse));
        assertEquals("SearchRoomsRequest cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildSearchRoomsRequest - Null CommonModifierResponse")
    void testBuildSearchRoomsRequest_NullCommonModifierResponse() {
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                orchDetailService.buildSearchRoomsRequest(searchRoomsRequest, null));
        assertEquals("CommonModifierResponse cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildSearchRoomsRequest - Null SearchCriteria")
    void testBuildSearchRoomsRequest_NullSearchCriteria() {
        searchRoomsRequest.setSearchCriteria(null);
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                orchDetailService.buildSearchRoomsRequest(searchRoomsRequest, commonModifierResponse));
        assertEquals("SearchRoomsCriteria cannot be null", e.getMessage());
    }

    @Test
    @DisplayName("Test buildStaticDetailRequest - Null Request")
    void testBuildStaticDetailRequest_NullRequest() {
        IllegalArgumentException e = assertThrows(IllegalArgumentException.class, () ->
                orchDetailService.buildStaticDetailRequest(null, commonModifierResponse));
        assertEquals("StaticDetailRequest cannot be null", e.getMessage());
    }

    // =================================================================
    // DPT COLLECTIONS FILTER TESTS
    // =================================================================

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - Empty FilterMap")
    void testUpdateAppliedFilterMapDptCollections_EmptyFilterMap() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        assertDoesNotThrow(() -> orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest));

        // Assert
        assertTrue(filterMap.isEmpty());
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - With Amenities Filter")
    void testUpdateAppliedFilterMapDptCollections_WithAmenitiesFilter() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptCollectionFilter = new FilterDetails();
        dptCollectionFilter.setValues(new HashSet<>(Arrays.asList("coll1#AMENITIES=wifi^pool")));
        filterMap.put("DPT_COLLECTIONS", dptCollectionFilter);

        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest);

        // Assert
        assertFalse(filterMap.containsKey("DPT_COLLECTIONS"));
        assertTrue(filterMap.containsKey("AMENITIES"));
        assertEquals(2, filterMap.get("AMENITIES").getValues().size());
        assertTrue(filterMap.get("AMENITIES").getValues().contains("wifi"));
        assertTrue(filterMap.get("AMENITIES").getValues().contains("pool"));
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - With Price Bucket Filter")
    void testUpdateAppliedFilterMapDptCollections_WithPriceBucketFilter() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptCollectionFilter = new FilterDetails();
        dptCollectionFilter.setValues(new HashSet<>(Arrays.asList("coll1#HOTEL_PRICE_BUCKET=1000-2000")));
        filterMap.put("DPT_COLLECTIONS", dptCollectionFilter);

        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest);

        // Assert
        assertFalse(filterMap.containsKey("DPT_COLLECTIONS"));
        assertTrue(filterMap.containsKey("HOTEL_PRICE_BUCKET"));
        assertEquals(1, filterMap.get("HOTEL_PRICE_BUCKET").getRange().size());
        assertEquals(2000, (int) filterMap.get("HOTEL_PRICE_BUCKET").getRange().get(0).getMax());
        assertEquals(1000, (int) filterMap.get("HOTEL_PRICE_BUCKET").getRange().get(0).getMin());
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - With Property Collections")
    void testUpdateAppliedFilterMapDptCollections_WithPropertyCollections() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptPropCollectionFilter = new FilterDetails();
        dptPropCollectionFilter.setValues(new HashSet<>(Arrays.asList("prop1#value1", "prop2#value2")));
        filterMap.put("DPT_PROP_COLLECTIONS", dptPropCollectionFilter);

        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest);

        // Assert
        assertTrue(filterMap.containsKey("DPT_PROP_COLLECTIONS"));
        assertEquals(2, filterMap.get("DPT_PROP_COLLECTIONS").getValues().size());
        assertTrue(filterMap.get("DPT_PROP_COLLECTIONS").getValues().contains("value1"));
        assertTrue(filterMap.get("DPT_PROP_COLLECTIONS").getValues().contains("value2"));
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - With Multiple Filters Combined")
    void testUpdateAppliedFilterMapDptCollections_WithMultipleFiltersCombined() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptCollectionFilter = new FilterDetails();
        dptCollectionFilter.setValues(new HashSet<>(Arrays.asList("coll1#AMENITIES=wifi^pool#HOTEL_PRICE_BUCKET=1000-2000")));
        filterMap.put("DPT_COLLECTIONS", dptCollectionFilter);

        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest);

        // Assert
        assertFalse(filterMap.containsKey("DPT_COLLECTIONS"));
        assertTrue(filterMap.containsKey("AMENITIES"));
        assertTrue(filterMap.containsKey("HOTEL_PRICE_BUCKET"));

        // Verify amenities
        assertEquals(2, filterMap.get("AMENITIES").getValues().size());
        assertTrue(filterMap.get("AMENITIES").getValues().contains("wifi"));
        assertTrue(filterMap.get("AMENITIES").getValues().contains("pool"));

        // Verify price bucket
        assertEquals(1, filterMap.get("HOTEL_PRICE_BUCKET").getRange().size());
        assertEquals(2000, (int) filterMap.get("HOTEL_PRICE_BUCKET").getRange().get(0).getMax());
        assertEquals(1000, (int) filterMap.get("HOTEL_PRICE_BUCKET").getRange().get(0).getMin());
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - With Location Area Filter")
    void testUpdateAppliedFilterMapDptCollections_WithLocationAreaFilter() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptCollectionFilter = new FilterDetails();
        dptCollectionFilter.setValues(new HashSet<>(Arrays.asList("coll1#area=Central_Delhi")));
        filterMap.put("DPT_COLLECTIONS", dptCollectionFilter);

        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest);

        // Assert - Location filters should be handled by FilterHelper.updateAppliedFilterMapForLocationDptFilters
        assertFalse(filterMap.containsKey("DPT_COLLECTIONS"));
        // The matchMakerRequest should be updated by the static method call
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - With Location POI Filter")
    void testUpdateAppliedFilterMapDptCollections_WithLocationPoiFilter() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptCollectionFilter = new FilterDetails();
        dptCollectionFilter.setValues(new HashSet<>(Arrays.asList("coll1#poi=Airport")));
        filterMap.put("DPT_COLLECTIONS", dptCollectionFilter);

        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest);

        // Assert - Location filters should be handled by FilterHelper.updateAppliedFilterMapForLocationDptFilters
        assertFalse(filterMap.containsKey("DPT_COLLECTIONS"));
        // The matchMakerRequest should be updated by the static method call
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - No DPT Filter Processing")
    void testUpdateAppliedFilterMapDptCollections_NoDptFilterProcessing() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptCollectionFilter = new FilterDetails();
        dptCollectionFilter.setValues(new HashSet<>(Arrays.asList("simple_filter_without_splitter")));
        filterMap.put("DPT_COLLECTIONS", dptCollectionFilter);

        com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerRequest =
            new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest();

        // Act
        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, matchMakerRequest);

        // Assert - Filter should remain unchanged as it doesn't contain DPT splitter
        assertTrue(filterMap.containsKey("DPT_COLLECTIONS"));
        assertEquals(1, filterMap.get("DPT_COLLECTIONS").getValues().size());
    }

    // =================================================================
    // MULTI CURRENCY INFO TESTS
    // =================================================================

    @Test
    @DisplayName("Test buildMultiCurrencyInfo - With Valid Data")
    void testBuildMultiCurrencyInfo_WithValidData() {
        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo inputMultiCurrencyInfo =
            new com.mmt.hotels.clientgateway.request.MultiCurrencyInfo();
        inputMultiCurrencyInfo.setUserCurrency("USD");
        inputMultiCurrencyInfo.setRegionCurrency("INR");

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod(
                "buildMultiCurrencyInfo",
                com.mmt.hotels.clientgateway.request.MultiCurrencyInfo.class
            );
            method.setAccessible(true);

            MultiCurrencyInfo result = (MultiCurrencyInfo) method.invoke(orchDetailService, inputMultiCurrencyInfo);

            assertNotNull(result);
            assertEquals("USD", result.getUserCurrency());
            assertEquals("INR", result.getRegionCurrency());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildMultiCurrencyInfo", e);
        }
    }

    @Test
    @DisplayName("Test buildMultiCurrencyInfo - With Null Input")
    void testBuildMultiCurrencyInfo_WithNullInput() {
        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod(
                "buildMultiCurrencyInfo",
                com.mmt.hotels.clientgateway.request.MultiCurrencyInfo.class
            );
            method.setAccessible(true);

            MultiCurrencyInfo result = (MultiCurrencyInfo) method.invoke(orchDetailService, (Object) null);

            assertNull(result);
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildMultiCurrencyInfo with null input", e);
        }
    }

    // =================================================================
    // SLOT DETAILS TESTS
    // =================================================================

    @Test
    @DisplayName("Test buildSlotDetails - With Valid Slot")
    void testBuildSlotDetails_WithValidSlot() {
        Slot slot = new Slot();
        slot.setTimeSlot(14);
        slot.setDuration(4);

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod("buildSlotDetails", Slot.class);
            method.setAccessible(true);

            SlotDetails result = (SlotDetails) method.invoke(orchDetailService, slot);

            assertNotNull(result);
            assertEquals(14, result.getTimeSlot());
            assertEquals(4, result.getDuration());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildSlotDetails", e);
        }
    }

    @Test
    @DisplayName("Test buildSlotDetails - With Null Slot")
    void testBuildSlotDetails_WithNullSlot() {
        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod("buildSlotDetails", Slot.class);
            method.setAccessible(true);

            SlotDetails result = (SlotDetails) method.invoke(orchDetailService, (Object) null);

            assertNotNull(result);
            assertEquals(0, result.getTimeSlot());
            assertEquals(0, result.getDuration());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildSlotDetails with null slot", e);
        }
    }

    @Test
    @DisplayName("Test buildSlotDetails - With Null TimeSlot and Duration")
    void testBuildSlotDetails_WithNullTimeSlotAndDuration() {
        Slot slot = new Slot();
        slot.setTimeSlot(null);
        slot.setDuration(null);

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod("buildSlotDetails", Slot.class);
            method.setAccessible(true);

            SlotDetails result = (SlotDetails) method.invoke(orchDetailService, slot);

            assertNotNull(result);
            assertEquals(0, result.getTimeSlot());
            assertEquals(0, result.getDuration());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildSlotDetails with null values", e);
        }
    }

    // =================================================================
    // ROOM DETAILS TESTS (First 2 lines coverage)
    // =================================================================

    @Test
    @DisplayName("Test buildRoomDetails - Null RoomStayCandidates")
    void testBuildRoomDetails_NullRoomStayCandidates() {
        Map<String, String> expDataMap = new HashMap<>();

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod(
                "buildRoomDetails",
                List.class,
                Map.class
            );
            method.setAccessible(true);

            @SuppressWarnings("unchecked")
            List<RoomDetails> result = (List<RoomDetails>) method.invoke(orchDetailService, null, expDataMap);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildRoomDetails with null input", e);
        }
    }

    @Test
    @DisplayName("Test buildRoomDetails - Empty RoomStayCandidates")
    void testBuildRoomDetails_EmptyRoomStayCandidates() {
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        Map<String, String> expDataMap = new HashMap<>();

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod(
                "buildRoomDetails",
                List.class,
                Map.class
            );
            method.setAccessible(true);

            @SuppressWarnings("unchecked")
            List<RoomDetails> result = (List<RoomDetails>) method.invoke(orchDetailService, roomStayCandidates, expDataMap);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildRoomDetails with empty input", e);
        }
    }

    @Test
    @DisplayName("Test buildRoomDetails - Valid RoomStayCandidates")
    void testBuildRoomDetails_ValidRoomStayCandidates() {
        List<RoomStayCandidate> roomStayCandidates = createRoomStayCandidates();
        Map<String, String> expDataMap = new HashMap<>();

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod(
                "buildRoomDetails",
                List.class,
                Map.class
            );
            method.setAccessible(true);

            @SuppressWarnings("unchecked")
            List<RoomDetails> result = (List<RoomDetails>) method.invoke(orchDetailService, roomStayCandidates, expDataMap);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(2, result.get(0).getAdults());
            assertEquals(Arrays.asList(5, 8), result.get(0).getChildrenAges());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildRoomDetails with valid input", e);
        }
    }

    @Test
    @DisplayName("Test buildRoomDetails - With Null RoomStayCandidate in List")
    void testBuildRoomDetails_WithNullRoomStayCandidateInList() {
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        roomStayCandidates.add(null); // Add null candidate
        roomStayCandidates.add(createRoomStayCandidates().get(0)); // Add valid candidate
        Map<String, String> expDataMap = new HashMap<>();

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = OrchDetailService.class.getDeclaredMethod(
                "buildRoomDetails",
                List.class,
                Map.class
            );
            method.setAccessible(true);

            @SuppressWarnings("unchecked")
            List<RoomDetails> result = (List<RoomDetails>) method.invoke(orchDetailService, roomStayCandidates, expDataMap);

            assertNotNull(result);
            assertEquals(1, result.size()); // Only valid candidate should be processed
            assertEquals(2, result.get(0).getAdults());
        } catch (Exception e) {
            throw new RuntimeException("Failed to test buildRoomDetails with null candidate in list", e);
        }
    }

    // =================================================================
    // HELPER METHOD TESTS (tested via public methods)
    // =================================================================

    @Test
    @DisplayName("Test buildUserGlobalInfo")
    void testBuildUserGlobalInfo() {
        UserGlobalInfo ugi = new UserGlobalInfo();
        ugi.setUserCountry("IN");
        ugi.setEntityName("MMT");
        com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo result = orchDetailService.buildUserGlobalInfo(ugi);
        assertEquals("IN", result.getUserCountry());
        assertEquals("MMT", result.getEntityName());
    }

    @Test
    @DisplayName("Test updateAppliedFilterMapDptCollections - Property Collections")
    void testUpdateAppliedFilterMapDptCollections_PropCollections() {
        Map<String, FilterDetails> filterMap = new HashMap<>();
        FilterDetails dptPropCollectionFilter = new FilterDetails();
        dptPropCollectionFilter.setValues(new HashSet<>(Arrays.asList("prop1#value1", "prop2#value2")));
        filterMap.put("DPT_PROP_COLLECTIONS", dptPropCollectionFilter);

        orchDetailService.updateAppliedFilterMapDptCollections(filterMap, new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest());

        assertTrue(filterMap.containsKey("DPT_PROP_COLLECTIONS"));
        assertEquals(2, filterMap.get("DPT_PROP_COLLECTIONS").getValues().size());
    }

    @Test
    @DisplayName("Test buildClientDetails - MyPartner Traffic Type")
    void testBuildClientDetails_MyPartnerTrafficType() {
        DetailRequest result = orchDetailService.buildSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);
        assertEquals(TrafficType.B2C, result.getClientDetails().getRequestDetails().getTrafficType());
    }

    @Test
    @DisplayName("Test buildUserDetails - Null UserLocation")
    void testBuildUserDetails_NullUserLocation() {
        commonModifierResponse.setUserLocation(null);
        DetailRequest result = orchDetailService.buildSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);
        assertNotNull(result.getClientDetails().getUserDetails().getLocation());
        assertEquals("", result.getClientDetails().getUserDetails().getLocation().getCityName());
    }

    @Test
    @DisplayName("Test buildFeatureFlags - Null HydraResponse")
    void testBuildFeatureFlags_NullHydraResponse() {
        commonModifierResponse.setHydraResponse(null);
        DetailRequest result = orchDetailService.buildSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);
        assertFalse(result.getClientDetails().getFeatureFlags().isFlightBooker());
    }


    // =================================================================
    // TEST DATA HELPERS
    // =================================================================

    private SearchRoomsRequest createSearchRoomsRequest() {
        SearchRoomsRequest request = new SearchRoomsRequest();
        request.setClient("android");
        request.setDeviceDetails(createDeviceDetails());
        request.setRequestDetails(createRequestDetails());
        request.setFeatureFlags(createFeatureFlags());
        request.setFilterCriteria(new ArrayList<>());
        request.setImageDetails(createImageDetails());
        request.setExpDataMap(new HashMap<>());
        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setHotelId("hotel123");
        criteria.setCheckIn("2023-12-01");
        criteria.setCheckOut("2023-12-05");
        criteria.setCurrency("INR");
        criteria.setLocationId("loc123");
        criteria.setLocationType("city");
        criteria.setCityCode("BLR");
        criteria.setCityName("Bangalore");
        criteria.setCountryCode("IN");
        criteria.setLat(12.9716);
        criteria.setLng(77.5946);
        criteria.setRoomStayCandidates(createRoomStayCandidates());
        request.setSearchCriteria(criteria);
        return request;
    }

    private StaticDetailRequest createStaticDetailRequest() {
        StaticDetailRequest request = new StaticDetailRequest();
        request.setClient("android");
        request.setDeviceDetails(createDeviceDetails());
        request.setRequestDetails(createRequestDetails());
        request.setFeatureFlags(createFeatureFlags());
        request.setFilterCriteria(new ArrayList<>());
        request.setImageDetails(createImageDetails());
        request.setExpDataMap(new HashMap<>());
        StaticDetailCriteria criteria = new StaticDetailCriteria();
        criteria.setHotelId("hotel123");
        criteria.setCheckIn("2023-12-01");
        criteria.setCheckOut("2023-12-05");
        criteria.setCurrency("INR");
        criteria.setLocationId("loc123");
        criteria.setLocationType("city");
        criteria.setCityCode("BLR");
        criteria.setCityName("Bangalore");
        criteria.setCountryCode("IN");
        criteria.setLat(12.9716);
        criteria.setLng(77.5946);
        criteria.setRoomStayCandidates(createRoomStayCandidates());
        request.setSearchCriteria(criteria);
        return request;
    }

    private UpdatePriceRequest createUpdatePriceRequest() {
        UpdatePriceRequest request = new UpdatePriceRequest();
        request.setClient("android");
        request.setRequestDetails(createRequestDetails());
        request.setExpDataMap(new HashMap<>());
        UpdatedPriceCriteria criteria = new UpdatedPriceCriteria();
        criteria.setHotelId("hotel123");
        criteria.setCheckIn("2023-12-01");
        criteria.setCheckOut("2023-12-05");
        UpdatedPriceRoomCriteria roomCriteria = new UpdatedPriceRoomCriteria();
        roomCriteria.setMtKey("mt123");
        roomCriteria.setPricingKey("pricing123");
        roomCriteria.setRoomStayCandidates(createRoomStayCandidates());
        criteria.setRoomCriteria(Arrays.asList(roomCriteria));
        request.setSearchCriteria(criteria);
        return request;
    }

    private DayUseRoomsRequest createDayUseRoomsRequest() {
        DayUseRoomsRequest request = new DayUseRoomsRequest();
        request.setClient("android");
        request.setDeviceDetails(createDeviceDetails());
        request.setRequestDetails(createRequestDetails());
        request.setFeatureFlags(createFeatureFlags());
        request.setFilterCriteria(new ArrayList<>());
        request.setImageDetails(createImageDetails());
        request.setExpDataMap(new HashMap<>());
        request.setExpData("test_exp_data");
        request.setExpVariantKeys("test_variant_keys");
        request.setMatchMakerDetails(new com.mmt.hotels.model.request.matchmaker.MatchMakerRequest());

        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setHotelId("hotel123");
        criteria.setGiHotelId("gi_hotel_123");
        criteria.setCheckIn("2023-12-01");
        criteria.setCheckOut("2023-12-05");
        criteria.setCurrency("INR");
        criteria.setLocationId("loc123");
        criteria.setLocationType("city");
        criteria.setCityCode("BLR");
        criteria.setCityName("Bangalore");
        criteria.setCountryCode("IN");
        criteria.setLat(12.9716);
        criteria.setLng(77.5946);
        criteria.setRoomStayCandidates(createRoomStayCandidates());

        // Add slot details for day use
        Slot slot = new Slot();
        slot.setTimeSlot(9);
        slot.setDuration(3);
        criteria.setSlot(slot);

        request.setSearchCriteria(criteria);
        return request;
    }

    private CommonModifierResponse createCommonModifierResponse() {
        CommonModifierResponse response = new CommonModifierResponse();
        response.setMcId("mc123");
        response.setMmtAuth("auth123");
        response.setApplicationId(1);
        response.setAffiliateId("affiliate123");
        response.setCdfContextId("cdf123");
        response.setCityTaxExclusive(false);
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("user123");
        extendedUser.setProfileType("BUSINESS");
        extendedUser.setAffiliateId("affiliate123");
        response.setExtendedUser(extendedUser);
        UserLocation userLocation = new UserLocation();
        userLocation.setCity("Bangalore");
        userLocation.setCountry("IN");
        userLocation.setState("KA");
        response.setUserLocation(userLocation);
        HydraResponse hydraResponse = new HydraResponse();
        hydraResponse.setFlightBooker(false);
        hydraResponse.setHydraMatchedSegment(new HashSet<>(Arrays.asList("segment1", "segment2")));
        response.setHydraResponse(hydraResponse);
        response.setManthanExpDataMap(new HashMap<>());
        return response;
    }

    private List<RoomStayCandidate> createRoomStayCandidates() {
        List<RoomStayCandidate> candidates = new ArrayList<>();
        RoomStayCandidate candidate = new RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(Arrays.asList(5, 8));
        candidates.add(candidate);
        return candidates;
    }

    private DeviceDetails createDeviceDetails() {
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceId("device123");
        deviceDetails.setDeviceName("Android Device");
        deviceDetails.setBookingDevice("MOBILE");
        deviceDetails.setAppVersion("1.0.0");
        deviceDetails.setNetworkType("WIFI");
        return deviceDetails;
    }

    private RequestDetails createRequestDetails() {
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setRequestId("req123");
        requestDetails.setJourneyId("journey123");
        requestDetails.setVisitorId("visitor123");
        requestDetails.setChannel("MOBILE");
        requestDetails.setSessionId("session123");
        requestDetails.setFunnelSource("HOTELS");
        requestDetails.setPageContext("LISTING");
        requestDetails.setBrand("MMT");
        requestDetails.setIdContext("B2C");
        requestDetails.setSiteDomain("IN");
        requestDetails.setRequestor("user123");
        requestDetails.setLoggedIn(true);
        requestDetails.setPremium(false);
        requestDetails.setMyraMsgId("myra123");
        TrafficSource trafficSource = new TrafficSource();
        trafficSource.setSource("DIRECT");
        trafficSource.setType("B2C");
        trafficSource.setFlowType("DIRECT");
        requestDetails.setTrafficSource(trafficSource);
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("query");
        semanticSearchDetails.setSemanticData("data");
        requestDetails.setSemanticSearchDetails(semanticSearchDetails);
        return requestDetails;
    }

    private FeatureFlags createFeatureFlags() {
        FeatureFlags featureFlags = new FeatureFlags();
        featureFlags.setWalletRequired(true);
        featureFlags.setCoupon(true);
        featureFlags.setComparator(true);
        return featureFlags;
    }

    private ImageDetails createImageDetails() {
        ImageDetails imageDetails = new ImageDetails();
        imageDetails.setTypes(Arrays.asList("EXTERIOR", "ROOM"));
        ImageCategory category = new ImageCategory();
        category.setType("ROOM");
        category.setCount(5);
        category.setHeight(400);
        category.setWidth(600);
        category.setImageFormat("JPG");
        imageDetails.setCategories(Arrays.asList(category));
        return imageDetails;
    }
}

// Mock enum to avoid dependency issues in the test environment
enum FilterGroup {
    AMENITIES("AMENITIES"),
    HOTEL_PRICE_BUCKET("HOTEL_PRICE_BUCKET"),
    DPT_COLLECTIONS("DPT_COLLECTIONS"),
    DPT_PROP_COLLECTIONS("DPT_PROP_COLLECTIONS");

    private final String filterName;

    FilterGroup(String filterName) {
        this.filterName = filterName;
    }

    public String getFilterName() {
        return filterName;
    }

    public static FilterGroup getFilterGroupFromFilterName(String filterName) {
        for (FilterGroup group : values()) {
            if (group.getFilterName().equalsIgnoreCase(filterName)) {
                return group;
            }
        }
        return null;
    }
} 