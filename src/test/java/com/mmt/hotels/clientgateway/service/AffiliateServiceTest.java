package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.CreateQuoteRequest;
import com.mmt.hotels.clientgateway.request.GetQuoteRequest;
import com.mmt.hotels.clientgateway.request.UpdateAffiliateFeeRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.restexecutors.AffiliateExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.AffiliateFactory;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.DiscountServiceFactory;
import com.mmt.hotels.clientgateway.transformer.response.AffiliateResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.DiscountResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.affiliate.CreateQuoteResponse;
import com.mmt.hotels.model.affiliate.GetQuoteResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import com.mmt.hotels.model.request.CreateQuoteRequestBody;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.SpecialRequest;
import com.mmt.hotels.model.response.pricing.SpecialRequestCategory;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class AffiliateServiceTest {

    @InjectMocks
    AffiliateService affiliateService;

    @Mock
    private AffiliateExecutor affiliateExecutor;

    @Mock
    private AffiliateFactory affiliateFactory;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private PaymentHelper payHelper;

    @Mock
    private AvailRoomsFactory availRoomsFactory;

    @Mock
    private DiscountServiceFactory discountServiceFactory;

    @InjectMocks
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private AffiliateResponseTransformer affiliateResponseTransformer;

    @Mock
    private AvailRoomsResponseTransformer availRoomsResponseTransformer;

    @Mock
    private DiscountResponseTransformer discountResponseTransformer;
    
    @Mock
    private UserServiceExecutor userServiceExecutor;

    @InjectMocks
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void init(){
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void testGetUpdateAffiliateFeeResponse() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getUpdatedAffiliateFeeResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UpdateAffiliateFeeResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(Mockito.any())).thenReturn(updatedAffiliateFeeResponse);

        UpdatedAffiliateFeeResponse actualResponse = affiliateService.getUpdateAffiliateFeeResponse(new UpdateAffiliateFeeRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertEquals(updatedAffiliateFeeResponse, actualResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetUpdateAffiliateFeeResponse_Exception() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getUpdatedAffiliateFeeResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UpdateAffiliateFeeResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(Mockito.any())).thenThrow(new NullPointerException());

        affiliateService.getUpdateAffiliateFeeResponse(new UpdateAffiliateFeeRequest(), new HashMap<>(), new HashMap<>());
    }

    @Test
    public void testCreateQuote() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getCreateQuoteResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CreateQuoteResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateCreateQuoteResponse(Mockito.any())).thenReturn(new com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse());

        com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse createQuoteResponse = affiliateService.createQuote(new CreateQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(createQuoteResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testCreateQuote_Exception() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getCreateQuoteResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CreateQuoteResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateCreateQuoteResponse(Mockito.any())).thenThrow(new NullPointerException());

        com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse createQuoteResponse = affiliateService.createQuote(new CreateQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(createQuoteResponse);
    }

    @Test
    public void testGetQuote() throws ClientGatewayException, UnsupportedEncodingException {
        GetQuoteResponse getQuoteResponseHES = new GetQuoteResponse();
        getQuoteResponseHES.setValidateCouponResponse(new ValidateCouponResponse.Builder().build());
        getQuoteResponseHES.setAvailResponse(new RoomDetailsResponse.Builder().buildHotelRates(new ArrayList<>(Arrays.asList(new HotelRates()))).build());
        getQuoteResponseHES.getAvailResponse().getHotelRates().get(0).setSpecialRequestAvailable(new SpecialRequest());
        SpecialRequestCategory specialRequestCategory = new SpecialRequestCategory();
        specialRequestCategory.setName("Early checkin");
        specialRequestCategory.setCode("101");
        specialRequestCategory.setSubCategories(Arrays.asList(new SpecialRequestCategory()));
        specialRequestCategory.getSubCategories().get(0).setValues(new String[]{"09:00 AM"});
        getQuoteResponseHES.getAvailResponse().getHotelRates().get(0).getSpecialRequestAvailable().setCategories(Arrays.asList(specialRequestCategory));
        getQuoteResponseHES.setSpecialRequest(new SpecialRequest());
        getQuoteResponseHES.getSpecialRequest().setCategories(Arrays.asList(specialRequestCategory));

        AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
        availRoomsResponse.setHotelInfo(new HotelResult());

        Mockito.when(affiliateExecutor.getPersistedQuoteDataMerged(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(getQuoteResponseHES);
        Mockito.when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        Mockito.when(availRoomsResponseTransformer.convertAvailRoomsResponse(Mockito.any(), Mockito.any(),   Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(),Mockito.any(), Mockito.anyBoolean(),Mockito.any(), Mockito.anyBoolean(), Mockito.anyString())).thenReturn(availRoomsResponse);
        Mockito.when(discountServiceFactory.getResponseService(Mockito.any())).thenReturn(discountResponseTransformer);
        Mockito.when(discountResponseTransformer.convertValidateCouponResponse(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyBoolean(),Mockito.any())).thenReturn(new ValidateCouponResponseBody());
        AvailRoomsResponse getQuoteResponseCG = affiliateService.getQuote(new GetQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(getQuoteResponseCG);
    }
    
    @Test
    public void createQuoteOld() throws UnsupportedEncodingException, ClientGatewayException {
    	ReflectionTestUtils.invokeMethod(affiliateService, "populateUserDetailsOld", new CreateQuoteRequestBody(), new UserDetailsDTO());
    	Assert.assertNull(affiliateService.createQuoteOld(new CreateQuoteRequestBody(), new HashMap<>(), new HashMap<>(), "test"));
    	
    }
    
    @Test
    public void getQuoteOld() throws UnsupportedEncodingException, ClientGatewayException {
    	GetQuoteResponse response = new GetQuoteResponse();
    	response.setAvailResponse(new RoomDetailsResponse());
    	response.setTravelerDetailsList(new ArrayList<>());
    	response.getTravelerDetailsList().add(new TravelerDetail());
    	Mockito.when(affiliateExecutor.getPersistedQuoteDataMerged(Mockito.any(), Mockito.anyMap(), Mockito.anyMap())).thenReturn(response);
    	Assert.assertNotNull(affiliateService.getQuoteOld(new com.mmt.hotels.model.affiliate.GetQuoteRequest(), new HashMap<>(), new HashMap<>(), "test"));
    }
}
