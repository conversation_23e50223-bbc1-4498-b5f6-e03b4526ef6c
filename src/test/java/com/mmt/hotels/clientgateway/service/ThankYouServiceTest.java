package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.ThankYouRequest;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.restexecutors.ThankYouExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.ThankYouFactory;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.map.HashedMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.UnsupportedEncodingException;
import java.util.Date;

@RunWith(MockitoJUnitRunner.class)
public class ThankYouServiceTest {
    @InjectMocks
    ThankYouService thankYouService;

    @Mock
    private ThankYouExecutor thankYouExecutor;

    @Mock
    private ThankYouFactory thankYouFactory;

    @Mock
    private ThankYouResponseTransformer mockedThankYouResponseTransformer;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private PropertyManager propertyManager;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(thankYouService, "thankYouTime", 21600000);
    }

    @Test
    public void initTest(){
        ReflectionTestUtils.invokeMethod(thankYouService, "init");
        CommonConfig config = Mockito.mock(CommonConfig.class);
        Mockito.when(propertyManager.getProperty(Mockito.any(),Mockito.any())).thenReturn(config);
        ReflectionTestUtils.invokeMethod(thankYouService, "init");
    }

    @Test
    public void testGetThankYouResponse() throws UnsupportedEncodingException, ClientGatewayException {
        ThankYouRequest thankYouRequest = new ThankYouRequest();

        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new PersistanceMultiRoomResponseEntity();
        persistanceMultiRoomResponseEntity.setPersistedData(new PersistedMultiRoomData());
        persistanceMultiRoomResponseEntity.getPersistedData().setTimeOfBooking(new Date().getTime());

        Mockito.when(thankYouExecutor.getThankYouReponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(persistanceMultiRoomResponseEntity);
        ThankYouResponse thankYouResponse = new ThankYouResponse();
        Mockito.doReturn(thankYouResponse).when(mockedThankYouResponseTransformer).convertThankYouResponse(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.doReturn(mockedThankYouResponseTransformer).when(thankYouFactory).getResponseService(Mockito.any());

        ThankYouResponse actualResponse = thankYouService.getThankYouResponse(thankYouRequest, new HashedMap(), new HashedMap());

        Assert.assertEquals(thankYouResponse, actualResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetThankYouResponse_DataExpired() throws UnsupportedEncodingException, ClientGatewayException {
        ThankYouRequest thankYouRequest = new ThankYouRequest();

        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new PersistanceMultiRoomResponseEntity();
        persistanceMultiRoomResponseEntity.setPersistedData(new PersistedMultiRoomData());
        persistanceMultiRoomResponseEntity.getPersistedData().setTimeOfBooking(new Date().getTime());

        Mockito.when(thankYouExecutor.getThankYouReponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(persistanceMultiRoomResponseEntity);

        ReflectionTestUtils.setField(thankYouService, "thankYouTime", 0);

        thankYouService.getThankYouResponse(thankYouRequest, new HashedMap(), new HashedMap());
    }

    @Test
    public void testGetThankYouResponse_Old() throws UnsupportedEncodingException, ClientGatewayException {
        ThankYouRequest thankYouRequest = new ThankYouRequest();

        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new PersistanceMultiRoomResponseEntity();
        persistanceMultiRoomResponseEntity.setPersistedData(new PersistedMultiRoomData());
        persistanceMultiRoomResponseEntity.getPersistedData().setTimeOfBooking(new Date().getTime());

        Mockito.when(thankYouExecutor.getThankYouReponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(persistanceMultiRoomResponseEntity);
        Mockito.doReturn("").when(objectMapperUtil).getJsonFromObject(Mockito.any(), Mockito.any());

        String actualResponse = thankYouService.getThankYouResponseOld(thankYouRequest, new HashedMap(), new HashedMap());

        Assert.assertEquals("", actualResponse);
        ReflectionTestUtils.setField(thankYouService, "thankYouTime", 0);
        thankYouService.getThankYouResponseOld(thankYouRequest, new HashedMap(), new HashedMap());
    }
}
