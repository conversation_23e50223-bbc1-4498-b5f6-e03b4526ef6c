package com.mmt.hotels.clientgateway.service;


import com.google.common.util.concurrent.Futures;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.modification.ModifiedPaymentDetail;
import com.mmt.hotels.clientgateway.request.modification.ProBookingRequest;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.modification.ProBookingResponse;
import com.mmt.hotels.clientgateway.response.modification.RatePreviewResponse;
import com.mmt.hotels.clientgateway.restexecutors.AvailRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.CorporateExecutor;
import com.mmt.hotels.clientgateway.restexecutors.PaymentExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.BookingModificationFactory;
import com.mmt.hotels.clientgateway.transformer.request.BookingModRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.BookingModResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class BookingModificationServiceTest {

    @InjectMocks
    BookingModificationService bkgModService;

    @Mock
    CommonHelper commonHelper;

    @Mock
    BookingModificationFactory bkgModFactory;

    @Mock
    SearchRoomsExecutor searchRoomsExecutor;

    @Mock
    AvailRoomsExecutor availRoomsExecutor;

    @Mock
    CorporateExecutor corporateExecutor;

    @Mock
    PaymentExecutor paymentExecutor;

    @Mock
    MetricErrorLogger metricErrorLogger;

    @Mock
    BookingModRequestTransformer bkgModReqTransfrmr;

    @Mock
    BookingModResponseTransformer bkgModRespTransfrmr;

    @Test
    public void fetchDetailPriceTest()throws  Exception{
        RatePreviewRequest rateReviewRequest = new RatePreviewRequest();
        Map<String,String> httpHeaderMap = new HashMap<>();
        Mockito.when(commonHelper.processRequestForBkgMod( Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(bkgModFactory.
                getRequestService(Mockito.any())).thenReturn(bkgModReqTransfrmr);
        Mockito.when(bkgModReqTransfrmr.convertPriceRequest(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new PriceByHotelsRequestBody());

        Mockito.when( searchRoomsExecutor.getRoomPrices(Mockito.any(), Mockito.any(), Mockito.any(),  Mockito.any())).thenReturn(Futures.immediateFuture(new RoomDetailsResponse()));
        Mockito.when(bkgModFactory.getResponseService(Mockito.any())).thenReturn(bkgModRespTransfrmr);
        Mockito.when(bkgModRespTransfrmr.convertPriceResponse(Mockito.any(),Mockito.any(),Mockito.any() )).thenReturn(new RatePreviewResponse());
        RatePreviewResponse response = bkgModService.fetchDetailPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNull(response.getErrorCode());
        Assert.assertNull(response.getErrorMessage());

        rateReviewRequest.setIdContext("CORP");
        response = bkgModService.fetchDetailPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());
    }

    @Test
    public void fetchDetailPriceFailureTest()throws  Exception{
        RatePreviewRequest rateReviewRequest = new RatePreviewRequest();
        Map<String,String> httpHeaderMap = new HashMap<>();
        Mockito.when(commonHelper.processRequestForBkgMod( Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(bkgModFactory.
                getRequestService(Mockito.any())).thenReturn(bkgModReqTransfrmr);
        Mockito.when(bkgModReqTransfrmr.convertPriceRequest(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new PriceByHotelsRequestBody());

        Mockito.when( searchRoomsExecutor.getRoomPrices(Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any())).thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM ));
        RatePreviewResponse response = bkgModService.fetchDetailPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());

        Mockito.reset(searchRoomsExecutor);
        Mockito.when( searchRoomsExecutor.getRoomPrices(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ArrayIndexOutOfBoundsException("message" ));
         response = bkgModService.fetchDetailPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());
    }

    @Test
    public void fetchReviewPriceTest()throws  Exception{
        RatePreviewRequest rateReviewRequest = new RatePreviewRequest();
        Map<String,String> httpHeaderMap = new HashMap<>();
        Mockito.when(commonHelper.processRequestForBkgMod( Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(bkgModFactory.
                getRequestService(Mockito.any())).thenReturn(bkgModReqTransfrmr);
        Mockito.when(bkgModReqTransfrmr.convertPriceRequest(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new PriceByHotelsRequestBody());

        Mockito.when( availRoomsExecutor.availRooms(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new RoomDetailsResponse());
        Mockito.when(bkgModFactory.getResponseService(Mockito.any())).thenReturn(bkgModRespTransfrmr);
        Mockito.when(bkgModRespTransfrmr.convertPriceResponse(Mockito.any(),Mockito.any(),Mockito.any() )).thenReturn(new RatePreviewResponse());
        RatePreviewResponse response = bkgModService.fetchReviewPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNull(response.getErrorCode());
        Assert.assertNull(response.getErrorMessage());

        rateReviewRequest.setIdContext("CORP");
        response = bkgModService.fetchReviewPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());
    }

    @Test
    public void fetchReviewPriceFailureTest()throws  Exception{
        RatePreviewRequest rateReviewRequest = new RatePreviewRequest();
        Map<String,String> httpHeaderMap = new HashMap<>();
        Mockito.when(commonHelper.processRequestForBkgMod( Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(bkgModFactory.
                getRequestService(Mockito.any())).thenReturn(bkgModReqTransfrmr);
        Mockito.when(bkgModReqTransfrmr.convertPriceRequest(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new PriceByHotelsRequestBody());

        Mockito.when( availRoomsExecutor.availRooms(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM ));
        RatePreviewResponse response = bkgModService.fetchReviewPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());

        Mockito.reset(availRoomsExecutor);
        Mockito.when( availRoomsExecutor.availRooms(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ArrayIndexOutOfBoundsException("message" ));
        response = bkgModService.fetchReviewPrice(rateReviewRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());
    }

    @Test
    public void createProBookingFailureTest()throws  Exception{
        ProBookingRequest proBookingRequest = new ProBookingRequest();
        Map<String,String> httpHeaderMap = new HashMap<>();
         ProBookingResponse response = bkgModService.createProBooking(proBookingRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());
    }

    @Test
    public void createProBookingSuccessTest()throws  Exception{
        ProBookingRequest proBookingRequest = new ProBookingRequest();
        proBookingRequest.setPaymentDetail(new ModifiedPaymentDetail());
        Map<String,String> httpHeaderMap = new HashMap<>();
        Mockito.when(bkgModFactory.getRequestService(Mockito.any())).thenReturn(bkgModReqTransfrmr);
        Mockito.when(paymentExecutor.beginPaymentCheckoutForModifyBooking(Mockito.any(), Mockito.any())).thenReturn(new PaymentCheckoutResponse());

        Mockito.when(bkgModFactory.getResponseService(Mockito.any())).thenReturn(bkgModRespTransfrmr);
        Mockito.when(bkgModRespTransfrmr.convertPaymentRsponse(Mockito.any(),Mockito.any())).thenReturn(new ProBookingResponse());
        ProBookingResponse response = bkgModService.createProBooking(proBookingRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNull(response.getErrorCode());
        Assert.assertNull(response.getErrorMessage());

        proBookingRequest.setWorkflowStatus("SKIP");
        response = bkgModService.createProBooking(proBookingRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNull(response.getErrorCode());
        Assert.assertNull(response.getErrorMessage());

        Mockito.reset(paymentExecutor);
        Mockito.when(paymentExecutor.beginPaymentCheckoutForModifyBooking(Mockito.any(), Mockito.any())).thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR,ErrorType.DOWNSTREAM));
         response = bkgModService.createProBooking(proBookingRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());
    }

    @Test
    public void createInitApprovalSuccessTest()throws  Exception{
        ProBookingRequest proBookingRequest = new ProBookingRequest();
        proBookingRequest.setPaymentDetail(new ModifiedPaymentDetail());
        proBookingRequest.setWorkflowStatus("PENDING");
        Map<String,String> httpHeaderMap = new HashMap<>();
        Mockito.when(bkgModFactory.getRequestService(Mockito.any())).thenReturn(bkgModReqTransfrmr);
        Mockito.when(bkgModReqTransfrmr.convertRequestApproval(Mockito.any(),Mockito.any())).thenReturn(new InitApprovalRequest());
        Mockito.when(bkgModFactory.getResponseService(Mockito.any())).thenReturn(bkgModRespTransfrmr);
        Mockito.when(corporateExecutor.requestApproval(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CGServerResponse());
        Mockito.when(bkgModRespTransfrmr.convertRequestApprovalResponse(Mockito.any(),Mockito.any())).thenReturn(new ProBookingResponse());
        ProBookingResponse response = bkgModService.createProBooking(proBookingRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNull(response.getErrorCode());
        Assert.assertNull(response.getErrorMessage());

        Mockito.reset(corporateExecutor);
        Mockito.when(corporateExecutor.requestApproval(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR,ErrorType.DOWNSTREAM));
        response = bkgModService.createProBooking(proBookingRequest, httpHeaderMap);
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getErrorCode());
        Assert.assertNotNull(response.getErrorMessage());
    }
}
