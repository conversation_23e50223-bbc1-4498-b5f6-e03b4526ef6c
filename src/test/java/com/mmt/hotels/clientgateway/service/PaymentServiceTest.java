package com.mmt.hotels.clientgateway.service;


import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.OTPAuthenticationException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.payment.PaymentRequestClient;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.restexecutors.HydraExecutor;
import com.mmt.hotels.clientgateway.restexecutors.PaymentExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.PaymentFactory;
import com.mmt.hotels.clientgateway.transformer.request.PaymentRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.PaymentResponseTransformer;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.request.payment.PaymentDetail;
import com.mmt.hotels.model.request.payment.UserDetail;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class PaymentServiceTest {

    @InjectMocks
    PaymentService paymentService;

    @Spy
    CommonHelper commonHelper;

    @Mock
    HeadersUtil headersUtil;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private ObjectMapperUtil mapper;

    @Mock
    UserServiceExecutor userService;

    @Mock
    PaymentExecutor paymentExecutor;

    @Mock
    PaymentFactory paymentFactory;

    @Mock
    HydraExecutor hydraExecutor;

    @Mock
    PaymentHelper paymentHelper;

    @Mock
    MetricErrorLogger  metricErrorLogger;

    @Mock
    PaymentRequestTransformer paymentRequestTransformer;

    @Mock
    PaymentResponseTransformer paymentResponseTransformer;

    @Mock
    ObjectMapperUtil objectMapperUtil;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(paymentRequestTransformer,"commonHelper" , commonHelper);
        ReflectionTestUtils.setField(paymentRequestTransformer,"payHelper" , paymentHelper);
    }

    @Test
    public void PaymentCheckoutTest() throws Exception{

        BeginCheckoutReqBody paymentRequest = getPaymentRequest();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentExecutor.beginPaymentCheckout(Mockito.any(),Mockito.any())).thenReturn(new PaymentCheckoutResponse());
        PaymentCheckoutResponse response = paymentService.paymentCheckoutOld(paymentRequest,request);
        Assert.assertNotNull(response);

    }

    @Test
    public void PaymentCheckoutNew2Test() throws Exception{

        PaymentRequestClient paymentRequest = getPaymentRequestClient();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentRequestTransformer.modifyPaymentRequest(Mockito.any(PaymentRequestClient.class),Mockito.any(HttpServletRequest.class), Mockito.anyString())).thenReturn(new BeginCheckoutReqBody());
        Mockito.when(paymentExecutor.beginPaymentCheckout(Mockito.any(),Mockito.any())).thenReturn(new PaymentCheckoutResponse());
        Mockito.when(paymentFactory.getResponseService(Mockito.any())).thenReturn(paymentResponseTransformer);
        Mockito.when(paymentResponseTransformer.processResponse(Mockito.any(),Mockito.any())).thenReturn(null);
        PaymentResponse response = paymentService.paymentCheckout(paymentRequest,request,"","");
        Assert.assertNull(response);

    }

    @Test
    public void PaymentCheckoutNewTest() throws Exception{

        PaymentRequestClient paymentRequest = getPaymentRequestClient();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentRequestTransformer.modifyPaymentRequest(Mockito.any(PaymentRequestClient.class),Mockito.any(HttpServletRequest.class), Mockito.anyString())).thenReturn(new BeginCheckoutReqBody());
        Mockito.when(paymentExecutor.beginPaymentCheckout(Mockito.any(),Mockito.any())).thenReturn(new PaymentCheckoutResponse());
        Mockito.when(paymentFactory.getResponseService(Mockito.any())).thenReturn(paymentResponseTransformer);
        Mockito.when(paymentResponseTransformer.processResponse(Mockito.any(),Mockito.any())).thenReturn(new PaymentResponse());
        PaymentResponse response = paymentService.paymentCheckout(paymentRequest,request,"","");
        Assert.assertNotNull(response);

    }

    @Test
    public void PaymentCheckout2Test() throws Exception{

        BeginCheckoutReqBody paymentRequest = getPaymentRequest();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentExecutor.beginPaymentCheckout(Mockito.any(),Mockito.any())).thenReturn(null);
        PaymentCheckoutResponse response = paymentService.paymentCheckoutOld(paymentRequest,request);
        Assert.assertNull(response);

    }

    @Test
    public void PaymentCheckoutOTPErrorTest() throws Exception{

        BeginCheckoutReqBody paymentRequest = getPaymentRequest();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentRequestTransformer.modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class) , Mockito.any(HttpServletRequest.class))).thenThrow(new OTPAuthenticationException(DependencyLayer.CLIENTGATEWAY, ErrorType.AUTHENTICATION,"",""));
        PaymentCheckoutResponse response = paymentService.paymentCheckoutOld(paymentRequest,request);
        Assert.assertNotNull(response.getResponseErrors());
        Assert.assertEquals("3999", response.getResponseErrors().getErrorList().get(0).getErrorCode());

    }

    @Test(expected = ClientGatewayException.class)
    public void PaymentCheckoutGenErrorTest() throws Exception{

        BeginCheckoutReqBody paymentRequest = getPaymentRequest();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentRequestTransformer.modifyPaymentRequest(Mockito.any(BeginCheckoutReqBody.class) , Mockito.any(HttpServletRequest.class))).thenThrow(new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.AUTHENTICATION,"",""));
        paymentService.paymentCheckoutOld(paymentRequest,request);
    }

    @Test(expected = ClientGatewayException.class)
    public void PaymentCheckoutNewOTPErrorTest() throws Exception{

        PaymentRequestClient paymentRequest = getPaymentRequestClient();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentRequestTransformer.modifyPaymentRequest(Mockito.any(PaymentRequestClient.class) , Mockito.any(HttpServletRequest.class), Mockito.anyString())).thenThrow(new OTPAuthenticationException(DependencyLayer.CLIENTGATEWAY, ErrorType.AUTHENTICATION,"",""));
        paymentService.paymentCheckout(paymentRequest,request,"","");
    }

    @Test(expected = ClientGatewayException.class)
    public void PaymentCheckoutNewGenErrorTest() throws Exception{

        PaymentRequestClient paymentRequest = getPaymentRequestClient();
        HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(paymentFactory.getRequestService(Mockito.any())).thenReturn(paymentRequestTransformer);
        Mockito.when(paymentRequestTransformer.modifyPaymentRequest(Mockito.any(PaymentRequestClient.class) , Mockito.any(HttpServletRequest.class), Mockito.anyString())).thenThrow(new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.AUTHENTICATION,"",""));
        paymentService.paymentCheckout(paymentRequest,request,"","");
    }

    private BeginCheckoutReqBody getPaymentRequest() {
        BeginCheckoutReqBody paymentRequest = new BeginCheckoutReqBody();
        paymentRequest.setCorrelationKey("abcd");
        paymentRequest.setUserDetail(new UserDetail());
        paymentRequest.setPaymentDetail(new PaymentDetail());
        paymentRequest.getPaymentDetail().setChannel("b2c");
        paymentRequest.setTravelerDetailsList(new ArrayList<>());
        return paymentRequest;

    }

    private PaymentRequestClient getPaymentRequestClient() {
        PaymentRequestClient paymentRequest = new PaymentRequestClient();
        paymentRequest.setCorrelationKey("abcd");
        paymentRequest.setPaymentDetail(new com.mmt.hotels.clientgateway.request.payment.PaymentDetail());
        paymentRequest.getPaymentDetail().setChannel("b2c");
        paymentRequest.setTravellerDetails(new ArrayList<>());
        return paymentRequest;

    }


}