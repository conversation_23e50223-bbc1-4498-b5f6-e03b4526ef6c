package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.Ugc;
import com.mmt.hotels.clientgateway.request.ugc.UgcQr;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.TotalPriceResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.ugc.*;
import com.mmt.hotels.clientgateway.restexecutors.AvailRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.ReviewExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserLoginInfo;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.request.AvailRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.PayLaterEligibilityResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ReviewServiceTest {

    @InjectMocks
    ReviewService reviewService;

    @Mock
    CommonHelper commonHelper;

    @Mock
    OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Mock
    AvailRoomsFactory availRoomsFactory;

    @Mock
    AvailRoomsExecutor availRoomsExecutor;
    
    @Mock
    ObjectMapperUtil objectMapperUtil;

    @Mock
    UserServiceExecutor userServiceExecutor;

    @Mock
    MetricAspect metricAspect;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private Utility utility;

    @InjectMocks
    PolyglotService polyglotServiceObj;


    @Before
    public void setUp() {
        polyglotServiceObj = Mockito.mock(PolyglotService.class);
        Field polyglotServiceField = ReflectionUtils.findField(ReviewService.class, "polyglotService");
        ReflectionUtils.makeAccessible(polyglotServiceField);
        ReflectionUtils.setField(polyglotServiceField, reviewService, polyglotServiceObj);

    }
    @Test
    public void testGetAvailPriceOld() throws ClientGatewayException {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        Mockito.when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        Mockito.when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.when(availRoomsExecutor.availRoomsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(reviewService.getAvailPriceOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>()));
    }
    
    @Test
    public void testGetUpdatedPriceOccuLessOld() throws ClientGatewayException {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        Mockito.when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        Mockito.when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.when(availRoomsExecutor.updatedPriceOccuLessOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(reviewService.getUpdatedPriceOccuLessOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>()));
    }
    
    @Test
    public void testgetTotalPrice() throws ClientGatewayException {
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        Mockito.when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        Mockito.when(availRoomsResponseTransformer.convertTotalPricingResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new TotalPriceResponse());
        Mockito.when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new TotalPricingResponse());
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetTotalPriceException() throws Exception{
        Mockito.when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
        reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(), "123", new HashMap<>(), "ANDROID");
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetUpdatedPriceOccuLessOldException() throws Exception {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        Mockito.when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        Mockito.when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.when(availRoomsExecutor.updatedPriceOccuLessOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
        reviewService.getUpdatedPriceOccuLessOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>());
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetAvailPriceOldException() throws Exception {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        Mockito.when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        Mockito.when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.when(availRoomsExecutor.availRoomsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
        reviewService.getAvailPriceOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>());
    }

    @Test(expected = ClientGatewayException.class)
    public void testAvailRooms() throws ClientGatewayException {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(),Mockito.any())).thenReturn("test");
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.lenient().when(availRoomsResponseTransformer.convertAvailRoomsResponse(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(),Mockito.any(),Mockito.anyBoolean(),Mockito.any(),Mockito.anyBoolean(), Mockito.anyString())).thenReturn(new AvailRoomsResponse());
        Assert.assertNotNull(reviewService.availRooms(new AvailRoomsRequest(),new HashMap<>(),new HashMap<>()));

        Mockito.when(availRoomsExecutor.availRooms(Mockito.any(),Mockito.any(),Mockito.anyMap())).thenThrow(new ClientGatewayException());
        reviewService.availRooms(new AvailRoomsRequest(),new HashMap<>(),new HashMap<>());
    }

    @Test(expected = ClientGatewayException.class)
    public void testFetchPayLaterEligibility() throws ClientGatewayException {
        Mockito.when(commonHelper.getMMTAuth(Mockito.any(), Mockito.any())).thenReturn("mmt-auth");
        Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UserServiceResponse());
        Assert.assertNotNull(reviewService.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), "123", new HashMap<>(),new HashMap<>(), "PWA"));
    }

    @Test
    public void testFetchPayLaterEligibilityException() throws Exception {
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        Mockito.when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        Mockito.when(availRoomsResponseTransformer.convertPayLaterEligibilityResponse(Mockito.any(), Mockito.any(),Mockito.anyBoolean())).thenReturn(new com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse());
        UserServiceResponse userServiceResponse = new UserServiceResponse();
        UserServiceResult userServiceResult = new UserServiceResult();
        ExtendedUser extendedUser = new ExtendedUser();
        List<UserLoginInfo> userLoginInfoList = new ArrayList<>();
        UserLoginInfo userLoginInfo = new UserLoginInfo();
        userLoginInfo.setLoginType("MOBILE");
        userLoginInfo.setLoginId("9101257825");
        userLoginInfoList.add(userLoginInfo);
        extendedUser.setLoginInfoList(userLoginInfoList);
        userServiceResult.setExtendedUser(extendedUser);
        userServiceResponse.setResult(userServiceResult);

        PayLaterEligibilityRequest payLaterEligibilityRequest = new PayLaterEligibilityRequest();
        payLaterEligibilityRequest.setTxnKey("txnKey");
        Mockito.when(commonHelper.getMMTAuth(Mockito.any(), Mockito.any())).thenReturn("mmt-auth");
        Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userServiceResponse);

        Mockito.when(availRoomsExecutor.fetchPayLaterEligibility(Mockito.any(),Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new PayLaterEligibilityResponse());
        Assert.assertNotNull(reviewService.fetchPayLaterEligibility(payLaterEligibilityRequest, "123", new HashMap<>(),new HashMap<>(), "PWA"));
    }

    @Test
    public void UgcResponseClientUgcResponseMapperTest() {
        // Arrange
        UgcResponse ugcResponse = new UgcResponse();
        ugcResponse.setUgcId("1234");
        ugcResponse.setStatus("Success");
        QuestionData questionData = new QuestionData();
        questionData.setUgcId("1234");
        LobData lobData = new LobData();
        lobData.setContentId("1234");
        lobData.setHotelName("h1");
        lobData.setHotelId("1234");
        lobData.setCityCode("city1");
        questionData.setLobData(lobData);
        Map<String, LevelConfig> levelConfigMap = new HashMap<>();
        LevelConfig levelConfig = new LevelConfig();
        levelConfig.setLevelName("1234");
        levelConfig.setLevelText("text");
        levelConfig.setLevelTotalQuestions(1);
        levelConfig.setLevelTotalPageNumbers(2);
        levelConfigMap.put("1", levelConfig);
        levelConfigMap.put("2", levelConfig);
        levelConfigMap.put("3", levelConfig);
        questionData.setLevelConfig(levelConfigMap);
        questionData.setNumberOfLevels(3);
        ugcResponse.setQuestionData(questionData);
        ClientUgcResponse clientUgcResponse = new ClientUgcResponse();

        // Act
        reviewService.UgcResponseClientUgcResponseMapper(ugcResponse,clientUgcResponse, null);

        // Assert
        assertNotNull(clientUgcResponse);
    }

    @Test
    public void createQuestionDetailsTest(){
        // Arrange
        Question question = new Question();
        question.setQuestionId("1234");
        question.setQuestionTitle("text");
        question.setLevel(1);
        question.setAnswerTitle("1");
        QuestionData questionData = new QuestionData();
        questionData.setPageId(1);
        QuestionDetails questionDetails1 = reviewService.createQuestionDetails(question, questionData);
        assertNotNull(questionDetails1);
    }

    @Test
    public void createQuestionConfigTest(){
        QuestionConfig questionConfig = reviewService.createQuestionConfig(1);
        assertNotNull(questionConfig);
    }

    @Test
    public void UgcResponseClientLevelSpecificMaxAmountTest() {
        List<Level> levelsList = new ArrayList<>();
        Level level1 = new Level();
        level1.setMaxAmount(500);
        Level level2 = new Level();
        level2.setMaxAmount(1000);
        Level level3 = new Level();
        level3.setMaxAmount(1500);
        levelsList.add(level1);
        levelsList.add(level2);
        levelsList.add(level3);

        Integer max1 = reviewService.getLevelSpecificMaxAmount(1, levelsList, 5000);
        // Assert
        assertTrue(500 == max1);

        Integer max2 = reviewService.getLevelSpecificMaxAmount(2, levelsList, 5000);
        // Assert
        assertTrue(1000 == max2);

        Integer max3 = reviewService.getLevelSpecificMaxAmount(3, levelsList, 5000);
        // Assert
        assertTrue(1500 == max3);

        Integer max4 = reviewService.getLevelSpecificMaxAmount(3, null, 5000);
        // Assert
        assertTrue(5000 == max4);
    }
}
