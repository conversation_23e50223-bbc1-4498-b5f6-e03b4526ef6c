package com.mmt.hotels.clientgateway.service;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.model.response.addon.AddOnEntity;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.SearchAddonsRequest;
import com.mmt.hotels.clientgateway.response.SearchAddonsResponse;
import com.mmt.hotels.clientgateway.restexecutors.AddonsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.AddonsServiceFactory;
import com.mmt.hotels.clientgateway.transformer.request.AddonsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AddonsReponseTransformer;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;

@RunWith(MockitoJUnitRunner.class)
public class AddonsServiceTest {

	@InjectMocks
	AddonsService addonsService;
	
	@Mock
	private CommonHelper commonHelper;

	@Mock
	private AddonsExecutor addonsExecutor;
	
	@Mock
	private AddonsServiceFactory addonsServiceFactory;

	@Mock
	private OldToNewerRequestTransformer oldRequestTransformer;

	@Test
	public void testGetAddons() throws ClientGatewayException {
		Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(),Mockito.anyMap()))
			.thenReturn(new CommonModifierResponse());
		
		AddonsRequestTransformer reqTrnsfrmr = Mockito.mock(AddonsRequestTransformer.class);
		Mockito.when(addonsServiceFactory.getRequestService(Mockito.any())).thenReturn(reqTrnsfrmr );
		Mockito.when(reqTrnsfrmr.convertSearchAddonsRequest(Mockito.any(), Mockito.any())).thenReturn(new GetAddonsRequest());

		AddonsReponseTransformer respTrnsfrmr = Mockito.mock(AddonsReponseTransformer.class);
		Mockito.when(addonsServiceFactory.getResponseService(Mockito.any())).thenReturn(respTrnsfrmr );
		Mockito.when(respTrnsfrmr.convertSearchAddonsResponse(Mockito.any())).thenReturn(new SearchAddonsResponse());
		
		SearchAddonsResponse resp = addonsService.getAddons(new SearchAddonsRequest(), new HashMap<>(), new HashMap<>());
		Assert.assertNotNull(resp);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetAddonsException() throws ClientGatewayException {
		Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(),Mockito.anyMap()))
				.thenReturn(new CommonModifierResponse());

		AddonsRequestTransformer reqTrnsfrmr = Mockito.mock(AddonsRequestTransformer.class);
		Mockito.when(addonsServiceFactory.getRequestService(Mockito.any())).thenReturn(reqTrnsfrmr );
		Mockito.when(reqTrnsfrmr.convertSearchAddonsRequest(Mockito.any(), Mockito.any())).thenReturn(new GetAddonsRequest());

		AddOnEntity addOnEntity = new AddOnEntity();
		List<Error> errors = new ArrayList<>();
		errors.add(new Error.Builder().buildErrorCode("1234","blah ").build());
		addOnEntity.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
		Mockito.when(addonsExecutor.getAddonsResponse(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(addOnEntity);

		 addonsService.getAddons(new SearchAddonsRequest(), new HashMap<>(), new HashMap<>());
	}

	@Test
	public void testGetAddonsOld() throws ClientGatewayException {
		Mockito.when(oldRequestTransformer.updateAddonsRequest(Mockito.any())).thenReturn(new SearchAddonsRequest());
		Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(),Mockito.anyMap()))
				.thenReturn(new CommonModifierResponse());

		AddonsRequestTransformer reqTrnsfrmr = Mockito.mock(AddonsRequestTransformer.class);
		Mockito.when(addonsServiceFactory.getRequestService(Mockito.any())).thenReturn(reqTrnsfrmr );
		Mockito.when(reqTrnsfrmr.convertSearchAddonsRequest(Mockito.any(), Mockito.any())).thenReturn(new GetAddonsRequest());

		Mockito.when(addonsExecutor.getAddonsResponse(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("{}");
		String resp = addonsService.getAddons(new GetAddonsRequest(), new HashMap<>(), new HashMap<>());
		Assert.assertNotNull(resp);
	}
	
}
