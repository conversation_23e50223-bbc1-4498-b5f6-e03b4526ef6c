package com.mmt.hotels.clientgateway.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.pojo.response.HotelComparatorResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.StaticDetailExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UpdatedPriceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.StaticDetailFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.SearchRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.StaticDetailRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.UpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatedPriceResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;

@RunWith(MockitoJUnitRunner.class)
public class ComparatorServiceTest {
    @InjectMocks
    ComparatorService comparatorService;

    @Mock
    CommonHelper commonHelper;

    @Mock
    OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Mock
    SearchRoomsFactory searchRoomsFactory;

    @Mock
    SearchRoomsExecutor searchRoomsExecutor;

    @Test (expected = NullPointerException.class)
    public void testComparatorOld() throws ClientGatewayException, IOException {
        HotelDetailsMobRequestBody requestBody = getHotelDetailsMobRequestBody();
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        InputStream comapratorResponse = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/comparatorResponse.json");
        UpsellHotelDetailResponse comparatorResponse = mapper.readValue(comapratorResponse, UpsellHotelDetailResponse.class);
        Mockito.when(searchRoomsExecutor.getComparatorOld(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(comparatorResponse);
        Assert.assertNotNull(comparatorService.comparatorOld(requestBody,new HashMap<>(), new HashMap<>(), ""));
        Mockito.when(searchRoomsExecutor.getComparatorOld(Mockito.any(),Mockito.any(), Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        comparatorService.comparatorOld(requestBody,new HashMap<>(),new HashMap<>(), "");
    }


    private HotelDetailsMobRequestBody getHotelDetailsMobRequestBody() {
        HotelDetailsMobRequestBody hotelDetailsMobRequestBody=new HotelDetailsMobRequestBody();
        hotelDetailsMobRequestBody.setBookingDevice("android");
        ResponseFilterFlags responseFilterFlags =new ResponseFilterFlags() ;
        responseFilterFlags.setWalletRequired(true);
        hotelDetailsMobRequestBody.setResponseFilterFlags(responseFilterFlags);
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate =new RoomStayCandidate() ;
        List<GuestCount> guestCounts =new ArrayList<>() ;
        GuestCount guestCount =new GuestCount() ;
        guestCount.setCount("2");
        List<Integer> ages = new ArrayList<>();
        ages.add(19);
        ages.add(20);
        guestCount.setAges(ages);
        guestCounts.add(guestCount);
        candidate.setGuestCounts(guestCounts);
        roomStayCandidates.add(candidate);
        hotelDetailsMobRequestBody.setRoomStayCandidates(roomStayCandidates);
        GuestRecommendationEnabledReqBody guestRecommendation = new GuestRecommendationEnabledReqBody();
        guestRecommendation.setMaxRecommendations("1");
        guestRecommendation.setText("true");
        hotelDetailsMobRequestBody.setGuestRecommendEnabled(guestRecommendation);
        List<String> hotelList = new ArrayList<>();
        hotelList.add("123");
        hotelDetailsMobRequestBody.setComparatorHotelsList(hotelList);
        hotelDetailsMobRequestBody.setHotelId("226");
        return hotelDetailsMobRequestBody;
    }
}
