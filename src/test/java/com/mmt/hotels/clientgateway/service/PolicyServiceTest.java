package com.mmt.hotels.clientgateway.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.PoliciesRequest;
import com.mmt.hotels.clientgateway.response.PoliciesResponse;
import com.mmt.hotels.clientgateway.restexecutors.HESRestExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.PolicyFactory;
import com.mmt.hotels.clientgateway.transformer.response.PoliciesResponseTransformer;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class PolicyServiceTest {

	@InjectMocks
	private PolicyService policyService;
	
	@Mock
	private HESRestExecutor restExecutor;
	
	@Mock
	private PolicyFactory policyFactory;

	@Test
	public void testGetPolicies() throws ClientGatewayException {
		Mockito.when(restExecutor.getTxnData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PersistanceMultiRoomResponseEntity());
		PoliciesResponseTransformer transformer = Mockito.mock(PoliciesResponseTransformer.class);
		Mockito.when(policyFactory.getResponseService(Mockito.any())).thenReturn(transformer);
		Mockito.when(transformer.transformPolicyResponse(Mockito.any(), Mockito.any())).thenReturn(new PoliciesResponse());
		PoliciesResponse policiesResponse = policyService.getPolicies(new PoliciesRequest(),new HashMap<>());
		Assert.assertNotNull(policiesResponse);
	}
}
