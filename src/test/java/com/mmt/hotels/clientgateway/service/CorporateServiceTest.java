package com.mmt.hotels.clientgateway.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.ValidationException;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.corporate.UpdateApprovalResponse;
import com.mmt.hotels.clientgateway.response.corporate.UpdatePolicyResponse;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.InitiateApprovalFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdateApprovalFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatePolicyFactory;
import com.mmt.hotels.clientgateway.transformer.request.InitiateApprovalRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.UpdateApprovalRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.UpdatePolicyRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.pwa.InitiateApprovalRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdateApprovalRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.UpdatePolicyRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.InitiateApprovalResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdateApprovalResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatePolicyResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.pwa.InitiateApprovalResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdateApprovalResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.payment.ApprovalDetails;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import com.mmt.hotels.model.response.corporate.GetApprovalsResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.exception.GenericException;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.corporate.ApproverInfo;
import com.mmt.hotels.clientgateway.response.corporate.WorkflowInfoResponse;
import com.mmt.hotels.clientgateway.restexecutors.CorporateExecutor;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.corporate.UpdateApprovalRequest;
import com.mmt.hotels.model.request.payment.DeviceDetails;
import com.mmt.hotels.model.request.payment.OsType;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateRequest;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class CorporateServiceTest {

    @InjectMocks
    CorporateService corporateService;

    @Mock
    CorporateExecutor corporateExecutor;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private AvailRoomsFactory availRoomsFactory;

    @Mock
    CorporateHelper corporateHelper;

    @Mock
    UpdatePolicyFactory updatePolicyFactory;

    @Mock
    InitiateApprovalFactory initiateApprovalFactory;

    @Mock
    UpdateApprovalFactory updateApprovalFactory;

    @Mock
    Utility utility;

    @Test
    public void updateApprovalByAuthcodeTest() throws Exception {

        WorkflowInfoResponse workflowInfo = new WorkflowInfoResponse();
        workflowInfo.setApproverInfo(new ApproverInfo());
        workflowInfo.getApproverInfo().setEmailId("<EMAIL>");
        workflowInfo.setWorkflowId("1234");

        Mockito.when(corporateExecutor.getWorkflowInfoByAuthCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(workflowInfo);

        UpdateApprovalRequest updateApprovalRequest = new UpdateApprovalRequest();
        updateApprovalRequest.setDeviceDetails(new DeviceDetails());
        updateApprovalRequest.getDeviceDetails().setOsType(OsType.DESKTOP);

        UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
        userDetailsDTO.setUuid("ABCD");

        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userDetailsDTO);
        Mockito.when(corporateExecutor.getUpdateApprovalResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new CGServerResponse());


        CGServerResponse response = corporateService.updateApprovalByAuthcode(updateApprovalRequest,new HashMap<>(),"auth",new HashMap<>(), "");
        Assert.assertNotNull(response);

    }

    @Test
    public void workflowInfoByAuthcodeTest() throws Exception {
        WorkflowInfoResponse workflowInfo = new WorkflowInfoResponse();
        workflowInfo.setApproverInfo(new ApproverInfo());
        workflowInfo.getApproverInfo().setEmailId("<EMAIL>");
        workflowInfo.setWorkflowId("1234");

        Mockito.when(corporateExecutor.getWorkflowInfoByAuthCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(workflowInfo);

        UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
        userDetailsDTO.setUuid("ABCD");
        userDetailsDTO.setProfileType("BUSINESS");
        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userDetailsDTO);
        Mockito.when(corporateExecutor.workflowInfoByAuthcode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new CGServerResponse());

        CGServerResponse response = corporateService.workflowInfoByAuthcode("authcode",new HashMap<>(), new HashMap<>(),"abs123","IN", "desktop");
        Assert.assertNotNull(response);

    }

    @Test
    public void approvalsInfoTest() throws Exception {

        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);

        GetApprovalsResponse getApprovalsResponse = new GetApprovalsResponse();
        getApprovalsResponse.setRoomDetailsResponse(new RoomDetailsResponse());
        getApprovalsResponse.setPriceByHotelsRequestBody(new PriceByHotelsRequestBody());
        getApprovalsResponse.getPriceByHotelsRequestBody().setCheckin("");
        getApprovalsResponse.getPriceByHotelsRequestBody().setCheckout("");
        getApprovalsResponse.setApprovalDetails(new ApprovalDetails());

        UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
        userDetailsDTO.setUuid("ABCD");
        userDetailsDTO.setProfileType("BUSINESS");

        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userDetailsDTO);
        Mockito.when(corporateExecutor.getApprovalsInfo(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(getApprovalsResponse);
        Mockito.doNothing().when(metricErrorLogger).logErrorInMetric(Mockito.any(), Mockito.any());
        Mockito.doNothing().when(metricAspect).addToTime(Mockito.any(), Mockito.any(), Mockito.anyLong());
        Mockito.when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        Mockito.when(availRoomsResponseTransformer.convertAvailRoomsResponse(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(),Mockito.any(),Mockito.anyBoolean(),Mockito.any(),Mockito.anyBoolean(), Mockito.anyString())).thenReturn(new AvailRoomsResponse());


        try{
            corporateService.approvalsInfo("", "",  new HashMap<>(), new HashMap<>(), "correlationKey", "siteDomain", "srcClient");
        } catch (ValidationException e){
            Assert.assertEquals(e.getMessage(), ValidationErrors.WORKFLOW_ID_AND_AUTHCODE_NULL.getErrorMsg());
            Assert.assertEquals(e.getCode(), ValidationErrors.WORKFLOW_ID_AND_AUTHCODE_NULL.getErrorCode());
        }

        try{
            Mockito.when(corporateExecutor.getWorkflowInfoByAuthCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new WorkflowInfoResponse());
            corporateService.approvalsInfo("", "authCode",  new HashMap<>(), new HashMap<>(), "correlationKey", "siteDomain", "srcClient");
            Assert.assertEquals(true, true);
        } catch (ErrorResponseFromDownstreamException e){
            Assert.assertEquals(e.getMessage(), ValidationErrors.WORKFLOW_ID_NULL.getErrorMsg());
            Assert.assertEquals(e.getCode(), ValidationErrors.WORKFLOW_ID_NULL.getErrorCode());
        }

        Assert.assertNotNull(corporateService.approvalsInfo("workflowId", "",  new HashMap<>(), new HashMap<>(), "", "siteDomain", "DESKTOP"));

    }

    @Test
    public void testRequestApproval() throws Exception{
    	InitApprovalRequest approvalRequest = new InitApprovalRequest();
    	
        UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
        userDetailsDTO.setUuid("ABCD");
        userDetailsDTO.setProfileType("BUSINESS");
        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.any(),Mockito.any(),
        		Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userDetailsDTO);
    	
        Mockito.when(corporateExecutor.requestApproval(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyMap())).thenReturn(new CGServerResponse());
    	CGServerResponse resp = corporateService.requestApproval(approvalRequest,new HashMap<>(),new HashMap<>(),"");
    	Assert.assertNotNull(resp);
    }
    
    @Test(expected=GenericException.class)
    public void testRequestApprovalException() throws Exception{
    	InitApprovalRequest approvalRequest = new InitApprovalRequest();
    	
        UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
        userDetailsDTO.setUuid("ABCD");
        userDetailsDTO.setProfileType("BUSINESS");
        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.any(),Mockito.any(),
        		Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userDetailsDTO);
    	
        Mockito.when(corporateExecutor.requestApproval(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
    	corporateService.requestApproval(approvalRequest,new HashMap<>(),new HashMap<>(), "");
    }
    
    @Test
    public void testUpdateCorpPolicy() throws Exception{
    	CorpPolicyUpdateRequest request = new CorpPolicyUpdateRequest();
    	List<String> travellerEmailId = new ArrayList<>();
    	travellerEmailId.add("<EMAIL>");
		request.setTravellerEmailId(travellerEmailId );
    	
        UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
        userDetailsDTO.setUuid("ABCD");
        userDetailsDTO.setProfileType("BUSINESS");

        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.any(),Mockito.any(),
        		Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userDetailsDTO);        
        
        Mockito.when(corporateExecutor.updateCorpPolicy(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any())).thenReturn("{}");
        
		String resp = corporateService.updateCorpPolicy(request, new HashMap<>(), new HashMap<>(), "");
		Assert.assertNotNull(resp);
    }

    @Test
    public void updatePolicyTest() throws Exception {
        UpdatePolicyRequestTransformer updatePolicyRequestTransformer = Mockito.mock(UpdatePolicyRequestTransformerPWA.class);
        UpdatePolicyResponseTransformer updatePolicyResponseTransformer = Mockito.mock(UpdatePolicyResponseTransformerPWA.class);

        UpdatePolicyRequest request = new UpdatePolicyRequest();
        Mockito.when(corporateExecutor.updatePolicy(Mockito.any(),Mockito.any(),Mockito.anyMap(),Mockito.any(),Mockito.any())).thenReturn(new CorpPolicyUpdateResponse());
        Mockito.when(updatePolicyFactory.getRequestService(Mockito.any())).thenReturn(updatePolicyRequestTransformer);
        Mockito.when(updatePolicyFactory.getResponseService(Mockito.any())).thenReturn(updatePolicyResponseTransformer);
        Mockito.when(updatePolicyRequestTransformer.convertUpdatePolicyRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UpdatePolicyRequest());
        Mockito.when(updatePolicyResponseTransformer.convertUpdatePolicyResponse(Mockito.any())).thenReturn(new UpdatePolicyResponse());

        UpdatePolicyResponse resp = corporateService.updatePolicy(request, new HashMap<>(), new HashMap<>(), "","");
        Assert.assertNotNull(resp);
    }


    @Test
    public void initApprovalsTest() throws Exception {
        InitiateApprovalRequestTransformer initiateApprovalRequestTransformer = Mockito.mock(InitiateApprovalRequestTransformerPWA.class);
        InitiateApprovalResponseTransformer initiateApprovalResponseTransformer = Mockito.mock(InitiateApprovalResponseTransformerPWA.class);

        com.mmt.hotels.clientgateway.request.InitApprovalRequest request = new com.mmt.hotels.clientgateway.request.InitApprovalRequest();
        Mockito.when(corporateExecutor.requestApproval(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new CGServerResponse());
        Mockito.when(initiateApprovalFactory.getRequestService(Mockito.any())).thenReturn(initiateApprovalRequestTransformer);
        Mockito.when(initiateApprovalFactory.getResponseService(Mockito.any())).thenReturn(initiateApprovalResponseTransformer);
        Mockito.when(initiateApprovalRequestTransformer.convertInitApprovalRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new InitApprovalRequest());
        Mockito.when(initiateApprovalResponseTransformer.processResponse(Mockito.any())).thenReturn(new InitApprovalResponse());

        InitApprovalResponse resp = corporateService.initiateApproval(request,new HashMap<>(),new HashMap<>(), "","");
        Assert.assertNotNull(resp);
    }

    @Test
    public void getWorkflowInfoTest() throws Exception{
        ReflectionTestUtils.setField(corporateService, "corporateHelper", corporateHelper);
        ReflectionTestUtils.setField(corporateService, "corporateExecutor", corporateExecutor);
        Mockito.when(corporateHelper.getCorpUserIdForMyBizUser(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.any(), Mockito.anyString())).thenReturn(new UserDetailsDTO());
        Mockito.when(corporateExecutor.workflowInfoByAuthcode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyString(),Mockito.anyMap())).thenReturn(new CGServerResponse());
        CGServerResponse cgServerResponse = corporateService.getWorkflowInfo("testWorkflow", new HashMap<>(),new HashMap<>(), "", "IN","CG");
        Assert.assertNotNull(cgServerResponse);
    }

    @Test
    public void updateApprovalUsingWorkflowIdTest() throws Exception {
        UpdateApprovalRequestTransformer updateApprovalRequestTransformer = Mockito.mock(UpdateApprovalRequestTransformerPWA.class);
        UpdateApprovalResponseTransformer updateApprovalResponseTransformer = Mockito.mock(UpdateApprovalResponseTransformerPWA.class);

        Mockito.when(updateApprovalFactory.getRequestService(Mockito.any())).thenReturn(updateApprovalRequestTransformer);
        Mockito.when(updateApprovalFactory.getResponseService(Mockito.any())).thenReturn(updateApprovalResponseTransformer);
        Mockito.when(updateApprovalRequestTransformer.convertUpdateApprovalRequest(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new UpdateApprovalRequest());
        Mockito.when(updateApprovalResponseTransformer.convertUpdateApprovalResponse(Mockito.any())).thenReturn(new UpdateApprovalResponse());

        Assert.assertNotNull(corporateService.updateApprovalUsingWorkflowId(new com.mmt.hotels.clientgateway.request.UpdateApprovalRequest(), new HashMap<>(),"testWorkflow",
                new HashMap<>(),"", ""));
    }

    @Test
    public void updateApprovalUsingAuthCodeTest() throws Exception {
        Mockito.when(corporateExecutor.getWorkflowInfoByAuthCode(Mockito.any(), Mockito.anyMap(), Mockito.any())).thenReturn(new WorkflowInfoResponse());

        UpdateApprovalRequestTransformer updateApprovalRequestTransformer = Mockito.mock(UpdateApprovalRequestTransformerPWA.class);
        UpdateApprovalResponseTransformer updateApprovalResponseTransformer = Mockito.mock(UpdateApprovalResponseTransformerPWA.class);

        Mockito.when(updateApprovalFactory.getRequestService(Mockito.any())).thenReturn(updateApprovalRequestTransformer);
        Mockito.when(updateApprovalFactory.getResponseService(Mockito.any())).thenReturn(updateApprovalResponseTransformer);
        Mockito.when(updateApprovalRequestTransformer.convertUpdateApprovalRequest(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new UpdateApprovalRequest());
        Mockito.when(updateApprovalResponseTransformer.convertUpdateApprovalResponse(Mockito.any())).thenReturn(new UpdateApprovalResponse());

        Assert.assertNotNull(corporateService.updateApprovalUsingAuthCode(new com.mmt.hotels.clientgateway.request.UpdateApprovalRequest(),new HashMap<>(), "", new HashMap<>(), "", ""));
    }

    @Test
    public void updateApprovalsTest() throws Exception {
        UpdateApprovalRequestTransformer updateApprovalRequestTransformer = Mockito.mock(UpdateApprovalRequestTransformerPWA.class);
        UpdateApprovalResponseTransformer updateApprovalResponseTransformer = Mockito.mock(UpdateApprovalResponseTransformerPWA.class);

        Mockito.when(updateApprovalFactory.getRequestService(Mockito.any())).thenReturn(updateApprovalRequestTransformer);
        Mockito.when(updateApprovalFactory.getResponseService(Mockito.any())).thenReturn(updateApprovalResponseTransformer);
        Mockito.when(updateApprovalRequestTransformer.convertUpdateApprovalRequest(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new UpdateApprovalRequest());
        Mockito.when(updateApprovalResponseTransformer.convertUpdateApprovalResponse(Mockito.any())).thenReturn(new UpdateApprovalResponse());

        Assert.assertNotNull(corporateService.updateApprovals(new com.mmt.hotels.clientgateway.request.UpdateApprovalRequest(), new HashMap<>(),
                new HashMap<>(),"", "","",""));
    }

}
