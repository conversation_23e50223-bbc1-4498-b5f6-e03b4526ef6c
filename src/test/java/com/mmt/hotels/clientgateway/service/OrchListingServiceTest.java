package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.FilterRange;
import com.mmt.hotels.clientgateway.request.ImageCategory;
import com.mmt.hotels.clientgateway.request.ImageDetails;
import com.mmt.hotels.clientgateway.request.LatLngBounds;
import com.mmt.hotels.clientgateway.request.MapDetails;
import com.mmt.hotels.clientgateway.request.MultiCityFilter;
import com.mmt.hotels.clientgateway.request.MultiCurrencyInfo;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.gommt.hotels.orchestrator.model.request.listing.ListingRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.ReviewDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.SemanticSearchDetails;
import com.mmt.hotels.clientgateway.request.SortCriteria;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.restexecutors.OrchSearchHotelsExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformerSCION;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.matchmaker.LatLngObject;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.*;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_VALUE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrchListingServiceTest {

    @InjectMocks
    OrchListingService orchListingService;

    @Mock
    MetricErrorLogger metricErrorLogger;

    @Spy
    Utility utility;

    @Mock
    MetricAspect metricAspect;

    @Mock
    OrchSearchHotelsExecutor orchSearchHotelsExecutor;

    @Mock
    SearchHotelsFactory searchHotelsFactory;

    @Mock
    private OrchSearchHotelsResponseTransformerSCION scionTransformer;

    @Mock
    OrchSearchHotelsResponseTransformerDesktop orchSearchHotelsResponseTransformerDesktop;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(orchListingService, "metricErrorLogger", metricErrorLogger);
        ReflectionTestUtils.setField(orchListingService, "utility", utility);
        ReflectionTestUtils.setField(orchListingService, "metricAspect", metricAspect);
        ReflectionTestUtils.setField(orchListingService, "orchSearchHotelsExecutor", orchSearchHotelsExecutor);
        ReflectionTestUtils.setField(orchListingService, "searchHotelsFactory", searchHotelsFactory);
        doNothing().when(utility).setPaginatedToMDC(any());
        doNothing().when(utility).setLoggingParametersToMDC(any(), any(), any());
        doNothing().when(metricAspect).addToTimeInternalProcess(any(), any(), anyLong());
        when(searchHotelsFactory.getSearchHotelsScionTransformer(any())).thenReturn(scionTransformer);
        MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(), "EN");
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
    }

    @Test
    public void searchHotels() throws ClientGatewayException {

        ReflectionTestUtils.setField(orchListingService, "metricErrorLogger", metricErrorLogger);
        ReflectionTestUtils.setField(orchListingService, "utility", utility);
        ReflectionTestUtils.setField(orchListingService, "metricAspect", metricAspect);
        ReflectionTestUtils.setField(orchListingService, "orchSearchHotelsExecutor", orchSearchHotelsExecutor);
        ReflectionTestUtils.setField(orchListingService, "searchHotelsFactory", searchHotelsFactory);

        when(searchHotelsFactory.getSearchHotelsResponseService(Mockito.any())).thenReturn(orchSearchHotelsResponseTransformerDesktop);

        //Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setDeviceDetails(new DeviceDetails());
        searchHotelsRequest.getDeviceDetails().setBookingDevice(Constants.DEVICE_OS_ANDROID);
        searchHotelsRequest.getDeviceDetails().setDeviceType("MOBILE");
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        searchHotelsRequest.getRequestDetails().setSrLat(28d);
        searchHotelsRequest.getRequestDetails().setSrLng(28d);
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        searchHotelsRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        searchHotelsRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        searchHotelsRequest.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(2);
        searchHotelsRequest.getSearchCriteria().setLat(28d);
        searchHotelsRequest.getSearchCriteria().setLng(28d);
        searchHotelsRequest.setFilterCriteria(new ArrayList<>());
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.HOTEL_CATEGORY, "swimming"));
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.HOTEL_CATEGORY, "MMT_LUXE"));
        Filter priceFilter = new Filter(FilterGroup.HOTEL_PRICE, "1000-2000");
        FilterRange filterRange = new FilterRange();
        filterRange.setMinValue(10000);
        filterRange.setMaxValue(20000);
        priceFilter.setFilterRange(filterRange);
        searchHotelsRequest.getFilterCriteria().add(priceFilter);


        Filter priceFilter2 = new Filter(FilterGroup.HOTEL_PRICE, "1000-2000");
        FilterRange filterRange2 = new FilterRange();
        filterRange2.setMinValue(5000);
        filterRange2.setMaxValue(8000);
        priceFilter2.setFilterRange(filterRange2);
        searchHotelsRequest.getFilterCriteria().add(priceFilter2);

        searchHotelsRequest.setImageDetails(new ImageDetails());
        searchHotelsRequest.getImageDetails().setCategories(new ArrayList<>());
        searchHotelsRequest.getImageDetails().getCategories().add(new ImageCategory());
        searchHotelsRequest.getImageDetails().getCategories().get(0).setCount(2);
        searchHotelsRequest.getImageDetails().getCategories().get(0).setHeight(2);
        searchHotelsRequest.getImageDetails().getCategories().get(0).setWidth(2);
        searchHotelsRequest.setFeatureFlags(new FeatureFlags());
        searchHotelsRequest.setSortCriteria(new SortCriteria());
        searchHotelsRequest.getSortCriteria().setOrder("asc");
        searchHotelsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        searchHotelsRequest.setFilterGroupsToRemove(new ArrayList<>());
        searchHotelsRequest.getFilterGroupsToRemove().add(FilterGroup.AMENITIES);
        searchHotelsRequest.setFiltersToRemove(new ArrayList<>());
        searchHotelsRequest.getFiltersToRemove().add(new Filter(FilterGroup.AMENITIES, "test"));
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.AMENITIES, "test"));
        searchHotelsRequest.setReviewDetails(new ReviewDetails());
        searchHotelsRequest.getReviewDetails().setOtas(new ArrayList<>());
        searchHotelsRequest.getReviewDetails().setTagTypes(new ArrayList<>());
        searchHotelsRequest.setMapDetails(new MapDetails());
        searchHotelsRequest.getMapDetails().setLngSegments(5);
        searchHotelsRequest.getMapDetails().setLatSegments(5);
        searchHotelsRequest.getMapDetails().setLatLngBounds(new LatLngBounds());
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.VILLA_AND_APPT, "VILLA_AND_APPT"));
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.DRIVE_WALK_PROXIMITY_KEY_POI, "DISTANCE_POIBURJ#dd_0-3000"));
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("r123");
        searchHotelsRequest.setMultiCityFilter(new MultiCityFilter());
        searchHotelsRequest.getSearchCriteria().setNearBySearch(true);
        searchHotelsRequest.getSearchCriteria().setLimit(10);
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("some query text");
        semanticSearchDetails.setSemanticData("some semantic data");
        searchHotelsRequest.getRequestDetails().setSemanticSearchDetails(semanticSearchDetails);
        searchHotelsRequest.setExpDataMap(new HashMap<>());
        searchHotelsRequest.getExpDataMap().put("roomCountDefault", EXACT_ROOM_VALUE);

        searchHotelsRequest.setMatchMakerDetails(new MatchMakerRequest());
        LatLngObject latObject = new LatLngObject();
        latObject.setPoiId("poiId");
        latObject.setName("name");
        latObject.setLatitude(20.0);
        LatLngObject lngObject = new LatLngObject();
        lngObject.setPoiId("poiId");
        lngObject.setName("name");
        lngObject.setLatitude(40.0);
        searchHotelsRequest.getMatchMakerDetails().setLatLng(Arrays.asList(latObject, lngObject));
        Tags tags = new Tags();
        tags.setTagAreaId("tagAreaId");
        tags.setTagDescription("tagDescription");
        searchHotelsRequest.getMatchMakerDetails().setSelectedTags(Collections.singletonList(tags));
        searchHotelsRequest.getSearchCriteria().getRoomStayCandidates().get(0).setRooms(1);
        searchHotelsRequest.getSearchCriteria().setMultiCurrencyInfo(new MultiCurrencyInfo());
        searchHotelsRequest.getSearchCriteria().getMultiCurrencyInfo().setRegionCurrency("INR");
        searchHotelsRequest.getSearchCriteria().getMultiCurrencyInfo().setRegionCurrency("AED");
        SearchHotelsResponse searchHotelsResponse = orchListingService.searchHotels(searchHotelsRequest, new CommonModifierResponse(), new HashMap<>(), new HashMap<>());
        Assert.assertNull(searchHotelsResponse);
        Slot slot = new Slot();
        slot.setTimeSlot(7);
        searchHotelsRequest.getSearchCriteria().setSlot(slot);
        searchHotelsResponse = orchListingService.searchHotels(searchHotelsRequest, new CommonModifierResponse(), new HashMap<>(), new HashMap<>());
        Assert.assertNull(searchHotelsResponse);
    }

    @Test
    public void testSearchHotelsScion_SuccessfulSearch() throws ClientGatewayException {
        // Arrange
        SearchHotelsRequest request = createSampleSearchRequest();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        CommonModifierResponse modifierResponse = new CommonModifierResponse();

        ListingResponse mockListingResponse = new ListingResponse();
        String expectedResponse = "{'hotels': []}";

        when(orchSearchHotelsExecutor.searchHotels(any(ListingRequest.class), any(), any()))
                .thenReturn(mockListingResponse);
        when(scionTransformer.convertSearchHotelsResponse(any(), any()))
                .thenReturn(expectedResponse);

        // Act
        String actualResponse = orchListingService.searchHotelsScion(request, parameterMap, headerMap, modifierResponse);

        // Assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(orchSearchHotelsExecutor).searchHotels(any(ListingRequest.class), eq(parameterMap), eq(headerMap));
        verify(scionTransformer).convertSearchHotelsResponse(eq(request), eq(mockListingResponse));
    }

    @Test()
    public void testSearchHotelsScion() throws ClientGatewayException {
        // Arrange
        SearchHotelsRequest request = createSampleSearchRequest();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        CommonModifierResponse modifierResponse = new CommonModifierResponse();

        // Act
        orchListingService.searchHotelsScion(request, parameterMap, headerMap, modifierResponse);
    }

    @Test
    public void testSearchHotelsScion_NullSearchCriteria() throws ClientGatewayException {
        // Arrange
        SearchHotelsRequest request = createSampleSearchRequest();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        CommonModifierResponse modifierResponse = new CommonModifierResponse();

        ListingResponse mockListingResponse = new ListingResponse();
        String expectedResponse = "{'hotels': []}";

        when(orchSearchHotelsExecutor.searchHotels(any(ListingRequest.class), any(), any()))
                .thenReturn(mockListingResponse);
        when(scionTransformer.convertSearchHotelsResponse(any(), any()))
                .thenReturn(expectedResponse);

        // Act
        String actualResponse = orchListingService.searchHotelsScion(request, parameterMap, headerMap, modifierResponse);

        // Assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
    }

    private SearchHotelsRequest createSampleSearchRequest() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        request.setExpData("{\"SOU\":\"t\",\"BLACK\":\"T\",\"HSCFS\":\"4\",\"GRPN\":\"T\",\"OCCFCNR\":\"f\",\"CGC\":\"T\",\"ST\":\"T\",\"VIDEO\":\"0\",\"HRNB\":\"3\",\"MRS\":\"T\",\"RCPN\":\"T\",\"GBE\":\"f\",\"RRR\":\"3\",\"FBS\":\"C\",\"PDO\":\"PN\",\"PAH\":\"5\",\"AALV2\":\"T\",\"BNPL\":\"t\",\"GEC\":\"f\",\"NLP\":\"Y\",\"detailV3\":\"t\",\"WPAH\":\"t\",\"IAO\":\"t\",\"CHPC\":\"t\",\"SPKG\":\"T\",\"AIP\":\"t\",\"ADC\":\"t\",\"PLV2\":\"T\",\"SMC\":\"f\",\"PLRS\":\"T\",\"APT\":\"f\",\"APEINTL\":\"6\",\"MBDTC\":\"T\",\"SOC\":\"T\",\"RTBC\":\"T\",\"PAV\":\"1\",\"AARI\":\"t\",\"CRF\":\"B\",\"ADDON\":\"T\",\"NHL\":\"t\",\"WSP\":\"t\",\"DPCR\":\"0\",\"MLOS\":\"t\",\"MMRVER\":\"V3\",\"TFT\":\"t\",\"HSTV2\":\"T\",\"GBRP\":\"t\",\"MCUR\":\"t\",\"LSTNRBY\":\"t\",\"CV2\":\"t\",\"FLTRPRCBKT\":\"t\",\"HAFC\":\"T\",\"B2BPAH\":\"t\",\"HFC\":\"T\",\"LSOF\":\"t\",\"SRRP\":\"T\",\"GALLERYV2\":\"T\",\"EMI\":\"F\",\"ALC\":\"f\",\"SPCR\":\"2\",\"BNPL0\":\"T\"}");
        request.setExpDataMap(new HashMap<>());
        SearchHotelsCriteria criteria = new SearchHotelsCriteria();
        request.setDeviceDetails(new DeviceDetails());
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setRequestId("12345");
        request.getDeviceDetails().setBookingDevice(Constants.DEVICE_OS_ANDROID);
        criteria.setLimit(10);

        criteria.setCheckIn("2024-01-01");
        criteria.setCheckOut("2024-01-02");
        criteria.setLocationId("CITY123");

        ArrayList<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStay = new RoomStayCandidate();
        roomStay.setAdultCount(2);
        roomStayCandidates.add(roomStay);

        criteria.setRoomStayCandidates(roomStayCandidates);
        request.setSearchCriteria(criteria);
        return request;
    }
}