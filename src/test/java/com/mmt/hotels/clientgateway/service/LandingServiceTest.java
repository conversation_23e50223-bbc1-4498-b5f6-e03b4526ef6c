package com.mmt.hotels.clientgateway.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;

@RunWith(MockitoJUnitRunner.class)
public class LandingServiceTest {

	@InjectMocks
	private LandingService landingService;
	
	@Mock
	private MobLandingExecutor mobLandingExecutor;
	
	@Test
	public void testGetLatLngFromGooglePlaceId() throws ClientGatewayException{
		Mockito.when(mobLandingExecutor.getLatLngFromGooglePlaceId(Mockito.anyString(), Mockito.any(),Mockito.any())).thenReturn("");
		Assert.assertNotNull(landingService.getLatLngFromGooglePlaceId("", null, null));
	}
}
