package com.mmt.hotels.clientgateway.service;

import static org.junit.Assert.assertNotNull;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.mmt.hotels.clientgateway.helpers.CommonHelper;

public class HotelMetaDataServiceTest {
	@InjectMocks
	HotelMetaDataService hotelMetaDataService;

	@Mock
	private CommonHelper commonHelper;

	@Before
	public void setUp() throws Exception {
		MockitoAnnotations.initMocks(this);
		ReflectionTestUtils.setField(hotelMetaDataService, "locCount", 1);
		ReflectionTestUtils.setField(hotelMetaDataService, "facCount", 1);
	}

	@Test
	public void filterLocationAndFaclity() {
		String metaJson = "{\"metadata\":{\"tyratings\":[{\"rating\":\"1+\",\"count\":514},{\"rating\":\"2+\",\"count\":513},{\"rating\":\"3+\",\"count\":482},{\"rating\":\"4+\",\"count\":168},{\"rating\":\"4.5+\",\"count\":13}],\"mmtratings\":[{\"rating\":\"1+\",\"count\":1250},{\"rating\":\"2+\",\"count\":1216},{\"rating\":\"3+\",\"count\":1102},{\"rating\":\"4+\",\"count\":544},{\"rating\":\"4.5+\",\"count\":153}],\"propertytypes\":[{\"count\":1857,\"propertytype\":\"Hotel\"},{\"count\":448,\"propertytype\":\"Guest House\"},{\"count\":239,\"propertytype\":\"Apartment\"},{\"count\":56,\"propertytype\":\"Bed N Breakfast\"},{\"count\":47,\"propertytype\":\"Homestay\"},{\"count\":30,\"propertytype\":\"Resort\"},{\"count\":21,\"propertytype\":\"Hostel\"},{\"count\":19,\"propertytype\":\"Holiday Home\"},{\"count\":15,\"propertytype\":\"Villa\"},{\"count\":6,\"propertytype\":\"Farm House\"},{\"count\":5,\"propertytype\":\"Cottage\"},{\"count\":3,\"propertytype\":\"Apart-hotel\"},{\"count\":3,\"propertytype\":\"Bungalow\"},{\"count\":2,\"propertytype\":\"Heritage\"},{\"count\":1,\"propertytype\":\"Camp\"}],\"hotelcategories\":[{\"code\":\"MMA\",\"name\":\"MMT Assured\",\"count\":214,\"sortingOption\":\"PO\"},{\"code\":\"VH\",\"name\":\"VALUE+ BUDGET\",\"count\":74,\"description\":\"Great Rooms PLUS Great Location PLUS Free Wi-Fi PLUS Breakfast. Starting at Rs. 999\",\"sortingOption\":\"PO\"},{\"code\":\"RES\",\"name\":\"Resorts\",\"count\":17,\"description\":\"Resort properties in the city\",\"sortingOption\":\"PO\"},{\"code\":\"PRM\",\"name\":\"Premium\",\"count\":38,\"description\":\"Enjoy a world-class experience in these handpicked hotels\",\"sortingOption\":\"PO\"}],\"hotelcount\":[{\"field\":\"totalhotels\",\"count\":2912},{\"field\":\"valueplus\",\"count\":93},{\"field\":\"non-valueplus\",\"count\":2819}],\"hotellocations\":[{\"count\":643,\"location\":\"Gurgaon\"},{\"count\":336,\"location\":\"Paharganj, New Delhi\"},{\"count\":247,\"location\":\"Karol Bagh\"},{\"count\":163,\"location\":\"South Delhi\"},{\"count\":152,\"location\":\"New Delhi Railway Station\"},{\"count\":132,\"location\":\"Noida\"},{\"count\":121,\"location\":\"Mahipalpur\"},{\"count\":98,\"location\":\"Delhi Airport\"},{\"count\":93,\"location\":\"Railway Station\"},{\"count\":81,\"location\":\"Airport\"},{\"count\":71,\"location\":\"Ghaziabad\"},{\"count\":66,\"location\":\"Nehru Place\"},{\"count\":64,\"location\":\"Central Delhi\"},{\"count\":59,\"location\":\"Patel Nagar\"},{\"count\":53,\"location\":\"Greater Kailash\"}],\"hiqratings\":[{\"rating\":\"1+\",\"count\":1199},{\"rating\":\"2+\",\"count\":1196},{\"rating\":\"3+\",\"count\":1172},{\"rating\":\"4+\",\"count\":925},{\"rating\":\"4.5+\",\"count\":494}],\"pricebuckets\":[{\"highest\":1000,\"least\":0,\"count\":728},{\"highest\":3100,\"least\":1001,\"count\":1062},{\"highest\":4000,\"least\":3101,\"count\":922},{\"highest\":11700,\"least\":4001,\"count\":200}],\"starratings\":[{\"rating\":\"1\",\"count\":517},{\"rating\":\"2\",\"count\":485},{\"rating\":\"3\",\"count\":923},{\"rating\":\"4\",\"count\":126},{\"rating\":\"5\",\"count\":73}],\"taratings\":[{\"rating\":\"1+\",\"count\":1305},{\"rating\":\"2+\",\"count\":1266},{\"rating\":\"3+\",\"count\":1143},{\"rating\":\"4+\",\"count\":641},{\"rating\":\"4.5+\",\"count\":284}],\"hotelfacilities\":[{\"count\":1532,\"facility\":\"Parking\"},{\"count\":1390,\"facility\":\"Internet/Wi-Fi\"},{\"count\":1324,\"facility\":\"Restaurant/Bar\"},{\"count\":500,\"facility\":\"Business Facilities\"},{\"count\":194,\"facility\":\"Swimming Pool\"}]},\"failureReason\":{},\"responseErrors\":null,\"correlationKey\":\"b0e2b1ee-f1ee-4c41-9c63-c3c45c694688,2f560929-6068-477c-992c-ba0cf46056a8\"}";

		Mockito.when(commonHelper.isJsonString(Mockito.anyString())).thenReturn(false);
		assertNotNull(hotelMetaDataService.filterLocationAndFaclity(metaJson, "1"));
		Mockito.when(commonHelper.isJsonString(Mockito.anyString())).thenReturn(true);
		assertNotNull(hotelMetaDataService.filterLocationAndFaclity(metaJson, "1"));

		// Check if condition

		metaJson = "{\"meta\":{\"tyratings\":[{\"rating\":\"1+\",\"count\":514},{\"rating\":\"2+\",\"count\":513},{\"rating\":\"3+\",\"count\":482},{\"rating\":\"4+\",\"count\":168},{\"rating\":\"4.5+\",\"count\":13}],\"mmtratings\":[{\"rating\":\"1+\",\"count\":1250},{\"rating\":\"2+\",\"count\":1216},{\"rating\":\"3+\",\"count\":1102},{\"rating\":\"4+\",\"count\":544},{\"rating\":\"4.5+\",\"count\":153}],\"propertytypes\":[{\"count\":1857,\"propertytype\":\"Hotel\"},{\"count\":448,\"propertytype\":\"Guest House\"},{\"count\":239,\"propertytype\":\"Apartment\"},{\"count\":56,\"propertytype\":\"Bed N Breakfast\"},{\"count\":47,\"propertytype\":\"Homestay\"},{\"count\":30,\"propertytype\":\"Resort\"},{\"count\":21,\"propertytype\":\"Hostel\"},{\"count\":19,\"propertytype\":\"Holiday Home\"},{\"count\":15,\"propertytype\":\"Villa\"},{\"count\":6,\"propertytype\":\"Farm House\"},{\"count\":5,\"propertytype\":\"Cottage\"},{\"count\":3,\"propertytype\":\"Apart-hotel\"},{\"count\":3,\"propertytype\":\"Bungalow\"},{\"count\":2,\"propertytype\":\"Heritage\"},{\"count\":1,\"propertytype\":\"Camp\"}],\"hotelcategories\":[{\"code\":\"MMA\",\"name\":\"MMT Assured\",\"count\":214,\"sortingOption\":\"PO\"},{\"code\":\"VH\",\"name\":\"VALUE+ BUDGET\",\"count\":74,\"description\":\"Great Rooms PLUS Great Location PLUS Free Wi-Fi PLUS Breakfast. Starting at Rs. 999\",\"sortingOption\":\"PO\"},{\"code\":\"RES\",\"name\":\"Resorts\",\"count\":17,\"description\":\"Resort properties in the city\",\"sortingOption\":\"PO\"},{\"code\":\"PRM\",\"name\":\"Premium\",\"count\":38,\"description\":\"Enjoy a world-class experience in these handpicked hotels\",\"sortingOption\":\"PO\"}],\"hotelcount\":[{\"field\":\"totalhotels\",\"count\":2912},{\"field\":\"valueplus\",\"count\":93},{\"field\":\"non-valueplus\",\"count\":2819}],\"hotellocations\":[{\"count\":643,\"location\":\"Gurgaon\"},{\"count\":336,\"location\":\"Paharganj, New Delhi\"},{\"count\":247,\"location\":\"Karol Bagh\"},{\"count\":163,\"location\":\"South Delhi\"},{\"count\":152,\"location\":\"New Delhi Railway Station\"},{\"count\":132,\"location\":\"Noida\"},{\"count\":121,\"location\":\"Mahipalpur\"},{\"count\":98,\"location\":\"Delhi Airport\"},{\"count\":93,\"location\":\"Railway Station\"},{\"count\":81,\"location\":\"Airport\"},{\"count\":71,\"location\":\"Ghaziabad\"},{\"count\":66,\"location\":\"Nehru Place\"},{\"count\":64,\"location\":\"Central Delhi\"},{\"count\":59,\"location\":\"Patel Nagar\"},{\"count\":53,\"location\":\"Greater Kailash\"}],\"hiqratings\":[{\"rating\":\"1+\",\"count\":1199},{\"rating\":\"2+\",\"count\":1196},{\"rating\":\"3+\",\"count\":1172},{\"rating\":\"4+\",\"count\":925},{\"rating\":\"4.5+\",\"count\":494}],\"pricebuckets\":[{\"highest\":1000,\"least\":0,\"count\":728},{\"highest\":3100,\"least\":1001,\"count\":1062},{\"highest\":4000,\"least\":3101,\"count\":922},{\"highest\":11700,\"least\":4001,\"count\":200}],\"starratings\":[{\"rating\":\"1\",\"count\":517},{\"rating\":\"2\",\"count\":485},{\"rating\":\"3\",\"count\":923},{\"rating\":\"4\",\"count\":126},{\"rating\":\"5\",\"count\":73}],\"taratings\":[{\"rating\":\"1+\",\"count\":1305},{\"rating\":\"2+\",\"count\":1266},{\"rating\":\"3+\",\"count\":1143},{\"rating\":\"4+\",\"count\":641},{\"rating\":\"4.5+\",\"count\":284}],\"hotelfacilities\":[{\"count\":1532,\"facility\":\"Parking\"},{\"count\":1390,\"facility\":\"Internet/Wi-Fi\"},{\"count\":1324,\"facility\":\"Restaurant/Bar\"},{\"count\":500,\"facility\":\"Business Facilities\"},{\"count\":194,\"facility\":\"Swimming Pool\"}]},\"failureReason\":{},\"responseErrors\":null,\"correlationKey\":\"b0e2b1ee-f1ee-4c41-9c63-c3c45c694688,2f560929-6068-477c-992c-ba0cf46056a8\"}";
		assertNotNull(hotelMetaDataService.filterLocationAndFaclity(metaJson, "1"));

		metaJson = "{\"metadata\":null}";
		assertNotNull(hotelMetaDataService.filterLocationAndFaclity(metaJson, "1"));

		// Hotel Facility Missing
		metaJson = "{\"metadata\":{\"tyratings\":[{\"rating\":\"1+\",\"count\":514},{\"rating\":\"2+\",\"count\":513},{\"rating\":\"3+\",\"count\":482},{\"rating\":\"4+\",\"count\":168},{\"rating\":\"4.5+\",\"count\":13}],\"mmtratings\":[{\"rating\":\"1+\",\"count\":1250},{\"rating\":\"2+\",\"count\":1216},{\"rating\":\"3+\",\"count\":1102},{\"rating\":\"4+\",\"count\":544},{\"rating\":\"4.5+\",\"count\":153}],\"propertytypes\":[{\"count\":1857,\"propertytype\":\"Hotel\"},{\"count\":448,\"propertytype\":\"Guest House\"},{\"count\":239,\"propertytype\":\"Apartment\"},{\"count\":56,\"propertytype\":\"Bed N Breakfast\"},{\"count\":47,\"propertytype\":\"Homestay\"},{\"count\":30,\"propertytype\":\"Resort\"},{\"count\":21,\"propertytype\":\"Hostel\"},{\"count\":19,\"propertytype\":\"Holiday Home\"},{\"count\":15,\"propertytype\":\"Villa\"},{\"count\":6,\"propertytype\":\"Farm House\"},{\"count\":5,\"propertytype\":\"Cottage\"},{\"count\":3,\"propertytype\":\"Apart-hotel\"},{\"count\":3,\"propertytype\":\"Bungalow\"},{\"count\":2,\"propertytype\":\"Heritage\"},{\"count\":1,\"propertytype\":\"Camp\"}],\"hotelcategories\":[{\"code\":\"MMA\",\"name\":\"MMT Assured\",\"count\":214,\"sortingOption\":\"PO\"},{\"code\":\"VH\",\"name\":\"VALUE+ BUDGET\",\"count\":74,\"description\":\"Great Rooms PLUS Great Location PLUS Free Wi-Fi PLUS Breakfast. Starting at Rs. 999\",\"sortingOption\":\"PO\"},{\"code\":\"RES\",\"name\":\"Resorts\",\"count\":17,\"description\":\"Resort properties in the city\",\"sortingOption\":\"PO\"},{\"code\":\"PRM\",\"name\":\"Premium\",\"count\":38,\"description\":\"Enjoy a world-class experience in these handpicked hotels\",\"sortingOption\":\"PO\"}],\"hotelcount\":[{\"field\":\"totalhotels\",\"count\":2912},{\"field\":\"valueplus\",\"count\":93},{\"field\":\"non-valueplus\",\"count\":2819}],\"hotellocations\":[{\"count\":643,\"location\":\"Gurgaon\"},{\"count\":336,\"location\":\"Paharganj, New Delhi\"},{\"count\":247,\"location\":\"Karol Bagh\"},{\"count\":163,\"location\":\"South Delhi\"},{\"count\":152,\"location\":\"New Delhi Railway Station\"},{\"count\":132,\"location\":\"Noida\"},{\"count\":121,\"location\":\"Mahipalpur\"},{\"count\":98,\"location\":\"Delhi Airport\"},{\"count\":93,\"location\":\"Railway Station\"},{\"count\":81,\"location\":\"Airport\"},{\"count\":71,\"location\":\"Ghaziabad\"},{\"count\":66,\"location\":\"Nehru Place\"},{\"count\":64,\"location\":\"Central Delhi\"},{\"count\":59,\"location\":\"Patel Nagar\"},{\"count\":53,\"location\":\"Greater Kailash\"}],\"hiqratings\":[{\"rating\":\"1+\",\"count\":1199},{\"rating\":\"2+\",\"count\":1196},{\"rating\":\"3+\",\"count\":1172},{\"rating\":\"4+\",\"count\":925},{\"rating\":\"4.5+\",\"count\":494}],\"pricebuckets\":[{\"highest\":1000,\"least\":0,\"count\":728},{\"highest\":3100,\"least\":1001,\"count\":1062},{\"highest\":4000,\"least\":3101,\"count\":922},{\"highest\":11700,\"least\":4001,\"count\":200}],\"starratings\":[{\"rating\":\"1\",\"count\":517},{\"rating\":\"2\",\"count\":485},{\"rating\":\"3\",\"count\":923},{\"rating\":\"4\",\"count\":126},{\"rating\":\"5\",\"count\":73}],\"taratings\":[{\"rating\":\"1+\",\"count\":1305},{\"rating\":\"2+\",\"count\":1266},{\"rating\":\"3+\",\"count\":1143},{\"rating\":\"4+\",\"count\":641},{\"rating\":\"4.5+\",\"count\":284}],\"htm\":[{\"count\":1532,\"facility\":\"Parking\"},{\"count\":1390,\"facility\":\"Internet/Wi-Fi\"},{\"count\":1324,\"facility\":\"Restaurant/Bar\"},{\"count\":500,\"facility\":\"Business Facilities\"},{\"count\":194,\"facility\":\"Swimming Pool\"}]},\"failureReason\":{},\"responseErrors\":null,\"correlationKey\":\"b0e2b1ee-f1ee-4c41-9c63-c3c45c694688,2f560929-6068-477c-992c-ba0cf46056a8\"}";
		assertNotNull(hotelMetaDataService.filterLocationAndFaclity(metaJson, "1"));

		metaJson = "{\"metadata\":{\"tyratings\":[{\"rating\":\"1+\",\"count\":514},{\"rating\":\"2+\",\"count\":513},{\"rating\":\"3+\",\"count\":482},{\"rating\":\"4+\",\"count\":168},{\"rating\":\"4.5+\",\"count\":13}],\"mmtratings\":[{\"rating\":\"1+\",\"count\":1250},{\"rating\":\"2+\",\"count\":1216},{\"rating\":\"3+\",\"count\":1102},{\"rating\":\"4+\",\"count\":544},{\"rating\":\"4.5+\",\"count\":153}],\"propertytypes\":[{\"count\":1857,\"propertytype\":\"Hotel\"},{\"count\":448,\"propertytype\":\"Guest House\"},{\"count\":239,\"propertytype\":\"Apartment\"},{\"count\":56,\"propertytype\":\"Bed N Breakfast\"},{\"count\":47,\"propertytype\":\"Homestay\"},{\"count\":30,\"propertytype\":\"Resort\"},{\"count\":21,\"propertytype\":\"Hostel\"},{\"count\":19,\"propertytype\":\"Holiday Home\"},{\"count\":15,\"propertytype\":\"Villa\"},{\"count\":6,\"propertytype\":\"Farm House\"},{\"count\":5,\"propertytype\":\"Cottage\"},{\"count\":3,\"propertytype\":\"Apart-hotel\"},{\"count\":3,\"propertytype\":\"Bungalow\"},{\"count\":2,\"propertytype\":\"Heritage\"},{\"count\":1,\"propertytype\":\"Camp\"}],\"hotelcategories\":[{\"code\":\"MMA\",\"name\":\"MMT Assured\",\"count\":214,\"sortingOption\":\"PO\"},{\"code\":\"VH\",\"name\":\"VALUE+ BUDGET\",\"count\":74,\"description\":\"Great Rooms PLUS Great Location PLUS Free Wi-Fi PLUS Breakfast. Starting at Rs. 999\",\"sortingOption\":\"PO\"},{\"code\":\"RES\",\"name\":\"Resorts\",\"count\":17,\"description\":\"Resort properties in the city\",\"sortingOption\":\"PO\"},{\"code\":\"PRM\",\"name\":\"Premium\",\"count\":38,\"description\":\"Enjoy a world-class experience in these handpicked hotels\",\"sortingOption\":\"PO\"}],\"hotelcount\":[{\"field\":\"totalhotels\",\"count\":2912},{\"field\":\"valueplus\",\"count\":93},{\"field\":\"non-valueplus\",\"count\":2819}],\"loca\":[{\"count\":643,\"location\":\"Gurgaon\"},{\"count\":336,\"location\":\"Paharganj, New Delhi\"},{\"count\":247,\"location\":\"Karol Bagh\"},{\"count\":163,\"location\":\"South Delhi\"},{\"count\":152,\"location\":\"New Delhi Railway Station\"},{\"count\":132,\"location\":\"Noida\"},{\"count\":121,\"location\":\"Mahipalpur\"},{\"count\":98,\"location\":\"Delhi Airport\"},{\"count\":93,\"location\":\"Railway Station\"},{\"count\":81,\"location\":\"Airport\"},{\"count\":71,\"location\":\"Ghaziabad\"},{\"count\":66,\"location\":\"Nehru Place\"},{\"count\":64,\"location\":\"Central Delhi\"},{\"count\":59,\"location\":\"Patel Nagar\"},{\"count\":53,\"location\":\"Greater Kailash\"}],\"hiqratings\":[{\"rating\":\"1+\",\"count\":1199},{\"rating\":\"2+\",\"count\":1196},{\"rating\":\"3+\",\"count\":1172},{\"rating\":\"4+\",\"count\":925},{\"rating\":\"4.5+\",\"count\":494}],\"pricebuckets\":[{\"highest\":1000,\"least\":0,\"count\":728},{\"highest\":3100,\"least\":1001,\"count\":1062},{\"highest\":4000,\"least\":3101,\"count\":922},{\"highest\":11700,\"least\":4001,\"count\":200}],\"starratings\":[{\"rating\":\"1\",\"count\":517},{\"rating\":\"2\",\"count\":485},{\"rating\":\"3\",\"count\":923},{\"rating\":\"4\",\"count\":126},{\"rating\":\"5\",\"count\":73}],\"taratings\":[{\"rating\":\"1+\",\"count\":1305},{\"rating\":\"2+\",\"count\":1266},{\"rating\":\"3+\",\"count\":1143},{\"rating\":\"4+\",\"count\":641},{\"rating\":\"4.5+\",\"count\":284}],\"hotelfacilities\":[{\"count\":1532,\"facility\":\"Parking\"},{\"count\":1390,\"facility\":\"Internet/Wi-Fi\"},{\"count\":1324,\"facility\":\"Restaurant/Bar\"},{\"count\":500,\"facility\":\"Business Facilities\"},{\"count\":194,\"facility\":\"Swimming Pool\"}]},\"failureReason\":{},\"responseErrors\":null,\"correlationKey\":\"b0e2b1ee-f1ee-4c41-9c63-c3c45c694688,2f560929-6068-477c-992c-ba0cf46056a8\"}";
		assertNotNull(hotelMetaDataService.filterLocationAndFaclity(metaJson, "1"));

	}

	@Test
	public void testfilterLocationAndFaclity_Exception() {
		String metaJson = "{\"metadata\":{\"tyratings\":[{\"rating\":\"1+\",\"count\":514},{\"rating\":\"2+\",\"count\":513},{\"rating\":\"3+\",\"count\":482},{\"rating\":\"4+\",\"count\":168},{\"rating\":\"4.5+\",\"count\":13}],\"mmtratings\":[{\"rating\":\"1+\",\"count\":1250},{\"rating\":\"2+\",\"count\":1216},{\"rating\":\"3+\",\"count\":1102},{\"rating\":\"4+\",\"count\":544},{\"rating\":\"4.5+\",\"count\":153}],\"propertytypes\":[{\"count\":1857,\"propertytype\":\"Hotel\"},{\"count\":448,\"propertytype\":\"Guest House\"},{\"count\":239,\"propertytype\":\"Apartment\"},{\"count\":56,\"propertytype\":\"Bed N Breakfast\"},{\"count\":47,\"propertytype\":\"Homestay\"},{\"count\":30,\"propertytype\":\"Resort\"},{\"count\":21,\"propertytype\":\"Hostel\"},{\"count\":19,\"propertytype\":\"Holiday Home\"},{\"count\":15,\"propertytype\":\"Villa\"},{\"count\":6,\"propertytype\":\"Farm House\"},{\"count\":5,\"propertytype\":\"Cottage\"},{\"count\":3,\"propertytype\":\"Apart-hotel\"},{\"count\":3,\"propertytype\":\"Bungalow\"},{\"count\":2,\"propertytype\":\"Heritage\"},{\"count\":1,\"propertytype\":\"Camp\"}],\"hotelcategories\":[{\"code\":\"MMA\",\"name\":\"MMT Assured\",\"count\":214,\"sortingOption\":\"PO\"},{\"code\":\"VH\",\"name\":\"VALUE+ BUDGET\",\"count\":74,\"description\":\"Great Rooms PLUS Great Location PLUS Free Wi-Fi PLUS Breakfast. Starting at Rs. 999\",\"sortingOption\":\"PO\"},{\"code\":\"RES\",\"name\":\"Resorts\",\"count\":17,\"description\":\"Resort properties in the city\",\"sortingOption\":\"PO\"},{\"code\":\"PRM\",\"name\":\"Premium\",\"count\":38,\"description\":\"Enjoy a world-class experience in these handpicked hotels\",\"sortingOption\":\"PO\"}],\"hotelcount\":[{\"field\":\"totalhotels\",\"count\":2912},{\"field\":\"valueplus\",\"count\":93},{\"field\":\"non-valueplus\",\"count\":2819}],\"hotellocations\":[{\"count\":643,\"location\":\"Gurgaon\"},{\"count\":336,\"location\":\"Paharganj, New Delhi\"},{\"count\":247,\"location\":\"Karol Bagh\"},{\"count\":163,\"location\":\"South Delhi\"},{\"count\":152,\"location\":\"New Delhi Railway Station\"},{\"count\":132,\"location\":\"Noida\"},{\"count\":121,\"location\":\"Mahipalpur\"},{\"count\":98,\"location\":\"Delhi Airport\"},{\"count\":93,\"location\":\"Railway Station\"},{\"count\":81,\"location\":\"Airport\"},{\"count\":71,\"location\":\"Ghaziabad\"},{\"count\":66,\"location\":\"Nehru Place\"},{\"count\":64,\"location\":\"Central Delhi\"},{\"count\":59,\"location\":\"Patel Nagar\"},{\"count\":53,\"location\":\"Greater Kailash\"}],\"hiqratings\":[{\"rating\":\"1+\",\"count\":1199},{\"rating\":\"2+\",\"count\":1196},{\"rating\":\"3+\",\"count\":1172},{\"rating\":\"4+\",\"count\":925},{\"rating\":\"4.5+\",\"count\":494}],\"pricebuckets\":[{\"highest\":1000,\"least\":0,\"count\":728},{\"highest\":3100,\"least\":1001,\"count\":1062},{\"highest\":4000,\"least\":3101,\"count\":922},{\"highest\":11700,\"least\":4001,\"count\":200}],\"starratings\":[{\"rating\":\"1\",\"count\":517},{\"rating\":\"2\",\"count\":485},{\"rating\":\"3\",\"count\":923},{\"rating\":\"4\",\"count\":126},{\"rating\":\"5\",\"count\":73}],\"taratings\":[{\"rating\":\"1+\",\"count\":1305},{\"rating\":\"2+\",\"count\":1266},{\"rating\":\"3+\",\"count\":1143},{\"rating\":\"4+\",\"count\":641},{\"rating\":\"4.5+\",\"count\":284}],\"hotelfacilities\":[{\"count\":1532,\"facility\":\"Parking\"},{\"count\":1390,\"facility\":\"Internet/Wi-Fi\"},{\"count\":1324,\"facility\":\"Restaurant/Bar\"},{\"count\":500,\"facility\":\"Business Facilities\"},{\"count\":194,\"facility\":\"Swimming Pool\"}]},\"failureReason\":{},\"responseErrors\":null,\"correlationKey\":\"b0e2b1ee-f1ee-4c41-9c63-c3c45c694688,2f560929-6068-477c-992c-ba0cf46056a8\"}";
		Mockito.when(commonHelper.isJsonString(Mockito.anyString())).thenReturn(true);
		ReflectionTestUtils.setField(hotelMetaDataService, "facCount", -1);
		assertNotNull(hotelMetaDataService.filterLocationAndFaclity(metaJson, "1"));
		
	}

}
