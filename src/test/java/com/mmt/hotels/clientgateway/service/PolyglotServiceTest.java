package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.restexecutors.PolyglotRestExecutor;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.polyglot.LanguageData;
import com.mmt.hotels.model.polyglot.PolyglotTranslation;
import org.apache.commons.collections4.MapUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.ReflectionUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PolyglotServiceTest {

    @InjectMocks
    private PolyglotService polyglotService;

    @Mock
    PolyglotRestExecutor polyglotRestExecutor;

    private CaffeineCacheManager caffeineCacheManager;

    @Spy
    private MetricAspect metricAspect;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Before
    public void setup() throws ClientGatewayException {

        caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCacheNames(Arrays.asList(Constants.TRANSLATION_CACHE));
        caffeineCacheManager.setCaffeine(Caffeine.newBuilder());
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(polyglotService,"polyglotCacheBackup","classpath:polyglotCacheCGBackup.json");
        ReflectionTestUtils.setField(polyglotService,"polyglotMobGenCacheBackup","classpath:polyglotMobGenCacheCGBackup.json");

        MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),"eng");
        ReflectionTestUtils.setField(polyglotService, "cacheManager", caffeineCacheManager);
        Path path = Paths.get("src/test/resources/mock-polyglot.json");
        String response = null;
        try {
            Stream<String> lines = Files.lines(path);
            response = lines.collect(Collectors.joining("\n"));
            lines.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        ObjectMapperUtil objectMapperUtil = new ObjectMapperUtil();
        objectMapperUtil.init();
        PolyglotTranslation polyglotTranslation = objectMapperUtil.getObjectFromJson(response, PolyglotTranslation.class, DependencyLayer.POLYGLOT);
        Mockito.when(polyglotRestExecutor.getPolyglotTranslation()).thenReturn(polyglotTranslation);
    }

    @Test
    public void getTranslatedData() throws ClientGatewayException {
        Map<String, Object> map = polyglotService.getTranslatedData();
        Mockito.verify(polyglotRestExecutor, Mockito.times(1)).getPolyglotTranslation();
        Assert.assertTrue(MapUtils.isNotEmpty(map));
        Map<String, Object> languageDataEng = caffeineCacheManager.getCache(Constants.TRANSLATION_CACHE).get("eng", Map.class);
        Assert.assertNotNull(languageDataEng);
    }

    @Test
    public void getTranslatedDataWithFunnelSource() throws ClientGatewayException {
        String key = "price_pn_title";
        String funnelSource = "HOMESTAY";
        Map<String, Object> languageDataEng = caffeineCacheManager.getCache(Constants.TRANSLATION_CACHE).get("eng", Map.class);
        String translatedData = polyglotService.getTranslatedData(key, funnelSource);
        Assert.assertNotNull(translatedData);
        translatedData = polyglotService.getTranslatedData(null, funnelSource);
        Assert.assertNull(translatedData);
        translatedData = polyglotService.getTranslatedData("DESKTOP_POPULAR_GROUPS_PAH_AVAIL_TITLE", funnelSource);
        Assert.assertNotNull(translatedData);
    }

    @Test
    public void getCacheStatsTest() {
        Assert.assertNotNull(polyglotService.getCacheStats());
    }
}