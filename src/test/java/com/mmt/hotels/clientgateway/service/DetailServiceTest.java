package com.mmt.hotels.clientgateway.service;

import com.google.common.util.concurrent.Futures;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailCriteria;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.CalendarBO;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.restexecutors.CalendarExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.StaticDetailExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UpdatedPriceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.StaticDetailFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.SearchRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.StaticDetailRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.UpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.StaticDetailRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.UpdatedPriceResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.CalendarAvailabilityResponse;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DetailServiceTest {
    @InjectMocks
    DetailService detailService;

    @Mock
    CommonHelper commonHelper;

    @Mock
    OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Mock
    SearchRoomsFactory searchRoomsFactory;
    
    @Mock
    StaticDetailFactory staticDetailFactory;
    
    @Mock
    UpdatedPriceFactory UpdatedPriceFactory;

    @Mock
    SearchRoomsExecutor searchRoomsExecutor;

    @Spy
    private MetricAspect metricAspect;
    
    @Mock
    StaticDetailExecutor staticDetailExecutor;
    
    @Mock
    UpdatedPriceExecutor updatedPriceExecutor;
    
    @Mock
    MetricErrorLogger metricErrorLogger;

    @Mock
    Utility utility;

    @Mock
    CalendarExecutor calendarExecutor;
    
    @Test (expected = Exception.class)
    public void testSearchRooms() throws ClientGatewayException, InterruptedException {
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsResponseTransformer searchRoomsResponseTransformer = Mockito.mock(SearchRoomsResponseTransformer.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.lenient().when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.lenient().when(searchRoomsFactory.getResponseService(Mockito.any())).thenReturn(searchRoomsResponseTransformer);
        Mockito.lenient().when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.lenient().when(searchRoomsExecutor.getRoomPrices(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Futures.immediateFuture(new RoomDetailsResponse()));
        Mockito.lenient().when(utility.getCountDownLatch(3)).thenReturn(new CountDownLatch(0));
        Mockito.lenient().when(searchRoomsResponseTransformer.convertSearchRoomsResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new SearchRoomsResponse());
        Assert.assertNotNull(detailService.searchRooms(searchRoomsRequest, new HashMap<>(), new HashMap<>()));
        detailService.searchRooms(new SearchRoomsRequest(), new HashMap<>(),new HashMap<>());
    }
    
    @Test (expected = Exception.class)
    public void testStaticDetail() throws ClientGatewayException {
        StaticDetailRequestTransformer staticDetailRequestTransformer = Mockito.mock(StaticDetailRequestTransformer.class);
        StaticDetailResponseTransformer staticDetailResponseTransformer = Mockito.mock(StaticDetailResponseTransformer.class);
        StaticDetailRequest staticDetailRequest = new StaticDetailRequest();
        staticDetailRequest.setDeviceDetails(new DeviceDetails());
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(staticDetailFactory.getRequestService(Mockito.any())).thenReturn(staticDetailRequestTransformer);
        Mockito.when(staticDetailFactory.getResponseService(Mockito.any())).thenReturn(staticDetailResponseTransformer);
        Mockito.when(staticDetailExecutor.getStaticDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HotelDetailWrapperResponse());
        Mockito.when(staticDetailRequestTransformer.convertStaticDetailRequest(Mockito.any(), Mockito.any())).thenReturn(new HotelDetailsMobRequestBody());
        Mockito.when(staticDetailResponseTransformer.convertStaticDetailResponse(Mockito.any(),Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new StaticDetailResponse());
        Assert.assertNotNull(detailService.staticDetail(staticDetailRequest, new HashMap<>(), new HashMap<>()));
        detailService.staticDetail(null, new HashMap<>(), new HashMap<>());
    }
    
    @Test (expected = Exception.class)
    public void testUpdatePrice() throws ClientGatewayException {
    	UpdatedPriceRequestTransformer UpdatePriceRequestTransformer = Mockito.mock(UpdatedPriceRequestTransformer.class);
    	UpdatedPriceResponseTransformer UpdatedPriceResponseTransformer = Mockito.mock(UpdatedPriceResponseTransformer.class);
    	UpdatePriceRequest UpdatedPriceRequest = new UpdatePriceRequest();
    	UpdatedPriceRequest.setDeviceDetails(new DeviceDetails());
    	UpdatedPriceRequest.setSearchCriteria(new UpdatedPriceCriteria());
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.lenient().when(UpdatedPriceFactory.getRequestService(Mockito.any())).thenReturn(UpdatePriceRequestTransformer);
        Mockito.lenient().when(UpdatedPriceFactory.getResponseService(Mockito.any())).thenReturn(UpdatedPriceResponseTransformer);
        Mockito.lenient().when(updatedPriceExecutor.updatedPrice(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PriceBreakDownResponse());
        Mockito.lenient().when(UpdatePriceRequestTransformer.convertUpdatedPriceRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.lenient().when(UpdatedPriceResponseTransformer.convertUpdatedPriceResponse(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(new UpdatePriceResponse());
        Assert.assertNotNull(detailService.updatePrice(UpdatedPriceRequest, new HashMap<>(),new HashMap<>()));
        detailService.updatePrice(null, new HashMap<>(), new HashMap<>());
    }

    @Test (expected = Exception.class)
    public void testSearchPriceOld() throws ClientGatewayException {
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomsRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.when(searchRoomsExecutor.getRoomPricesOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(detailService.searchPriceOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchRoomsExecutor.getRoomPricesOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        detailService.searchPriceOld(null,new HashMap<>(),new HashMap<>());
    }
    
    @Test (expected = Exception.class)
    public void testAlternateDatesPriceOld() throws ClientGatewayException {
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomsRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.when(searchRoomsExecutor.alternateDatesPriceOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(detailService.alternateDatesPriceOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchRoomsExecutor.alternateDatesPriceOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        detailService.alternateDatesPriceOld(null,new HashMap<>(),new HashMap<>());
    }
    
    @Test
    public void testGetStaticDetailsResponse() throws ClientGatewayException {
		HotelDetailsMobRequestBody request = new HotelDetailsMobRequestBody();
    	SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
    	Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any(HotelDetailsMobRequestBody.class)))
    	.thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        StaticDetailRequestTransformer staticDetailTrfnsfrmr = Mockito.mock(StaticDetailRequestTransformer.class);
		Mockito.when(staticDetailFactory.getRequestService(Mockito.any())).thenReturn(staticDetailTrfnsfrmr );
    	Mockito.when(staticDetailTrfnsfrmr.convertStaticDetailRequest(Mockito.any(SearchRoomsRequest.class),
    			Mockito.any(), Mockito.any(HotelDetailsMobRequestBody.class))).thenReturn(request);
    	Mockito.when(staticDetailExecutor.getStaticDetailsResponse(Mockito.any(), Mockito.any(), Mockito.anyMap())).thenReturn("");
    	String resp = detailService.getStaticDetailsResponse(request, new HashMap<>(), new HashMap<>());
    	Assert.assertNotNull(resp);
    }
    
    @Test(expected = Exception.class)
    public void testGetStaticDetailsResponseException() throws ClientGatewayException{
		HotelDetailsMobRequestBody request = new HotelDetailsMobRequestBody();
    	Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any(HotelDetailsMobRequestBody.class)))
    	.thenThrow(new RuntimeException());
       	detailService.getStaticDetailsResponse(request, new HashMap<>(), new HashMap<>());
    }

    @Test (expected = Exception.class)
    public void testCityGuideData() throws ClientGatewayException {
        StaticDetailRequestTransformer staticDetailRequestTransformer = Mockito.mock(StaticDetailRequestTransformer.class);
        com.mmt.hotels.clientgateway.request.CityGuideRequest staticDetailRequest = new com.mmt.hotels.clientgateway.request.CityGuideRequest();
        Mockito.when(staticDetailFactory.getRequestService(Mockito.any())).thenReturn(staticDetailRequestTransformer);
        Mockito.when(staticDetailExecutor.getCityGuildeResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CityGuideResponse());
        Mockito.when(staticDetailRequestTransformer.convertCityGuideRequest(Mockito.any(), Mockito.any())).thenReturn(new CityGuideRequest());
        Assert.assertNotNull(detailService.cityGuideData(staticDetailRequest, new HashMap<>(), new HashMap<>(), null));
        detailService.cityGuideData(null, new HashMap<>(), new HashMap<>(),null);
    }

    @Test
    public void testWishListedStaticDetail() throws ClientGatewayException {
        WishListedHotelsDetailRequest wishListedHotelsDetailRequest = new WishListedHotelsDetailRequest();
        wishListedHotelsDetailRequest.setSearchCriteria(new WishListedHotelsDetailCriteria());
        StaticDetailRequestTransformer staticDetailRequestTransformer = Mockito.mock(StaticDetailRequestTransformerAndroid.class);
        StaticDetailResponseTransformer staticDetailResponseTransformer = Mockito.mock(StaticDetailResponseTransformer.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(staticDetailFactory.getRequestService(Mockito.any())).thenReturn(staticDetailRequestTransformer);
        Mockito.when(staticDetailFactory.getResponseService(Mockito.any())).thenReturn(staticDetailResponseTransformer);
        Mockito.when(staticDetailRequestTransformer.getHotStoreHotelsRequest(Mockito.any(), Mockito.any())).thenReturn(new HotStoreHotelsRequestBody());
        Mockito.when(staticDetailRequestTransformer.getFlyfishReviewRequest(Mockito.any(), Mockito.any())).thenReturn(new FlyfishReviewRequestBody());
        Mockito.when(staticDetailExecutor.getWishListedHotelsFromHotStore(Mockito.any(), Mockito.any())).thenReturn(new HotStoreHotelsWrapperResponse());
        Mockito.when(staticDetailExecutor.getFlyFishReviewSummary(Mockito.any(), Mockito.any())).thenReturn(new FlyfishReviewWrapperResponse());
        WishListedHotelsDetailResponse wishListedHotelsDetailResponse = detailService.wishListedStaticDetail(wishListedHotelsDetailRequest, new HashMap<>(), new HashMap<>());
        assertNull(wishListedHotelsDetailResponse);
    }

    @Test(expected = Exception.class)
    public void testWishListedStaticDetailException() throws ClientGatewayException {
        WishListedHotelsDetailRequest wishListedHotelsDetailRequest = new WishListedHotelsDetailRequest();
        wishListedHotelsDetailRequest.setSearchCriteria(new WishListedHotelsDetailCriteria());
        StaticDetailRequestTransformer staticDetailRequestTransformer = Mockito.mock(StaticDetailRequestTransformerAndroid.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(staticDetailFactory.getRequestService(Mockito.any())).thenReturn(staticDetailRequestTransformer);
        Mockito.when(staticDetailRequestTransformer.getHotStoreHotelsRequest(Mockito.any(), Mockito.any())).thenReturn(new HotStoreHotelsRequestBody());
        Mockito.when(staticDetailRequestTransformer.getFlyfishReviewRequest(Mockito.any(), Mockito.any())).thenReturn(new FlyfishReviewRequestBody());
        Mockito.when(staticDetailExecutor.getWishListedHotelsFromHotStore(Mockito.any(), Mockito.any())).thenReturn(new HotStoreHotelsWrapperResponse());
        Mockito.when(staticDetailExecutor.getFlyFishReviewSummary(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException());
        detailService.wishListedStaticDetail(wishListedHotelsDetailRequest, new HashMap<>(), new HashMap<>());
    }

    @Test
    public void testBuildSearchCriteria() {
        WishListedHotelsDetailRequest wishListedHotelsDetailRequest = new WishListedHotelsDetailRequest();
        WishListedHotelsDetailCriteria wishListedHotelsDetailCriteria = new WishListedHotelsDetailCriteria();
        wishListedHotelsDetailCriteria.setCityCode("CTDEL");
        wishListedHotelsDetailCriteria.setLocationId("CTDEL");
        wishListedHotelsDetailCriteria.setLocationType("city");
        wishListedHotelsDetailRequest.setSearchCriteria(wishListedHotelsDetailCriteria);
        SearchCriteria searchCriteria = detailService.buildSearchCriteria(wishListedHotelsDetailRequest);
        assertNotNull(searchCriteria);
        assertEquals(wishListedHotelsDetailRequest.getSearchCriteria().getCityCode(), searchCriteria.getCityCode());
        assertEquals(wishListedHotelsDetailRequest.getSearchCriteria().getLocationId(), searchCriteria.getLocationId());
        assertEquals(wishListedHotelsDetailRequest.getSearchCriteria().getLocationType(), searchCriteria.getLocationType());
    }

    @Test
    public void fetchCalendarAvailabilityTest() throws ClientGatewayException {
        CalendarAvailabilityRequest calendarAvailabilityRequest = new CalendarAvailabilityRequest();
        com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse calendarAvailabilityResponse = new com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse();
        Map<String, CalendarBO> dates = new HashMap<>();
        CalendarBO calendarBO = new CalendarBO();
        calendarBO.setStatus("AVAILABLE");
        dates.put("2022-05-10", calendarBO);
        calendarAvailabilityResponse.setDates(dates);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformer.class);
        SearchRoomsResponseTransformer searchRoomsResponseTransformer = Mockito.mock(SearchRoomsResponseTransformer.class);
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsFactory.getResponseService(Mockito.any())).thenReturn(searchRoomsResponseTransformer);
        Mockito.doReturn(new PriceByHotelsRequestBody()).when(searchRoomsRequestTransformer).convertSearchRoomsRequest(Mockito.any(), Mockito.any());
        Mockito.doReturn(calendarAvailabilityResponse).when(searchRoomsResponseTransformer).convertCalendarAvailabilityResponse(Mockito.any());
        Mockito.doReturn(new CalendarAvailabilityResponse()).when(calendarExecutor).getCalendarAvailability(Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyMap());
        com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse response = detailService.fetchCalendarAvailability(calendarAvailabilityRequest, "", new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void fetchCalendarAvailabilityTest_Success() throws ClientGatewayException {
        CalendarAvailabilityRequest calendarAvailabilityRequest = new CalendarAvailabilityRequest();
        com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse calendarAvailabilityResponse = new com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse();
        Map<String, CalendarBO> dates = new HashMap<>();
        CalendarBO calendarBO = new CalendarBO();
        calendarBO.setStatus("AVAILABLE");
        dates.put("2022-05-10", calendarBO);
        calendarAvailabilityResponse.setDates(dates);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformer.class);
        SearchRoomsResponseTransformer searchRoomsResponseTransformer = Mockito.mock(SearchRoomsResponseTransformer.class);
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsFactory.getResponseService(Mockito.any())).thenReturn(searchRoomsResponseTransformer);
        Mockito.doReturn(new PriceByHotelsRequestBody()).when(searchRoomsRequestTransformer).convertSearchRoomsRequest(Mockito.any(), Mockito.any());
        Mockito.doReturn(calendarAvailabilityResponse).when(searchRoomsResponseTransformer).convertCalendarAvailabilityResponse(Mockito.any());
        Mockito.doReturn(new CalendarAvailabilityResponse()).when(calendarExecutor).getCalendarAvailability(Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyMap());
        com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse response = detailService.fetchCalendarAvailability(calendarAvailabilityRequest, "", new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test(expected = ClientGatewayException.class)
    public void fetchCalendarAvailabilityTest_Failure() throws ClientGatewayException {
        CalendarAvailabilityRequest calendarAvailabilityRequest = new CalendarAvailabilityRequest();
        com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse calendarAvailabilityResponse = new com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse();
        Map<String, CalendarBO> dates = new HashMap<>();
        CalendarBO calendarBO = new CalendarBO();
        calendarBO.setStatus("AVAILABLE");
        dates.put("2022-05-10", calendarBO);
        calendarAvailabilityResponse.setDates(dates);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformer.class);
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.doReturn(new PriceByHotelsRequestBody()).when(searchRoomsRequestTransformer).convertSearchRoomsRequest(Mockito.any(), Mockito.any());
        Mockito.when(calendarExecutor.getCalendarAvailability(Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyMap())).thenThrow(ClientGatewayException.class);
        detailService.fetchCalendarAvailability(calendarAvailabilityRequest, "", new HashMap<>(), new HashMap<>());
    }

    @Test
    public void buildCalendarCriteriaTest() {
        CalendarAvailabilityRequest calendarAvailabilityRequest = new CalendarAvailabilityRequest();
        CalendarCriteria response = detailService.buildCalendarCriteria(calendarAvailabilityRequest);
        Assert.assertNull(response);
        com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria calendarCriteria = new com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria();
        calendarCriteria.setMaxDate("2022-05-10");
        calendarCriteria.setAvailable(true);
        calendarCriteria.setAdvanceDays(60);
        calendarCriteria.setMlos(2);
        calendarAvailabilityRequest.setCalendarCriteria(calendarCriteria);
        response = detailService.buildCalendarCriteria(calendarAvailabilityRequest);
        Assert.assertNotNull(response);
    }

}
