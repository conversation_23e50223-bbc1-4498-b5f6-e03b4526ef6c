package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.model.request.dpt.FeatureStoreRequest;
import com.mmt.hotels.model.request.dpt.FeatureStoreRequestEntity;
import com.mmt.hotels.model.response.dpt.FeatureStoreResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class FeaturestoreHelperTest {

    @InjectMocks
    private FeaturestoreHelper featurestoreHelper;

    private ObjectMapper objectMapper;

    @Before
    public void setup() {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testPrepareFeatureStoreRequest_WithAllFields() {
        // Given
        String userProfile = "testUser";
        String cityCheckInDate = "city123_2024-03-20";
        List<String> features = Arrays.asList("feature1", "feature2");

        // When
        FeatureStoreRequest request = featurestoreHelper.prepareFeatureStoreRequest(userProfile, cityCheckInDate, features);

        // Then
        assertNotNull(request);
        assertNotNull(request.getEntities());
        assertEquals(1, request.getEntities().size());
        
        FeatureStoreRequestEntity entity = request.getEntities().get(0);
        assertNotNull(entity);
        assertNotNull(entity.getFeatures());
        assertEquals(features, entity.getFeatures());
        
        assertNotNull(entity.getEntity());
        assertNotNull(entity.getEntity().getUserDetails());
        assertEquals(1, entity.getEntity().getUserDetails().size());
        assertEquals(userProfile, entity.getEntity().getUserDetails().get(0));
        
        assertNotNull(entity.getEntity().getCityIdCheckInDate());
        assertEquals(1, entity.getEntity().getCityIdCheckInDate().size());
        assertEquals(cityCheckInDate, entity.getEntity().getCityIdCheckInDate().get(0));
    }

    @Test
    public void testPrepareFeatureStoreRequest_WithEmptyFields() {
        // Given
        String userProfile = "";
        String cityCheckInDate = "";
        List<String> features = new ArrayList<>();

        // When
        FeatureStoreRequest request = featurestoreHelper.prepareFeatureStoreRequest(userProfile, cityCheckInDate, features);

        // Then
        assertNotNull(request);
        assertNotNull(request.getEntities());
        assertEquals(1, request.getEntities().size());
        
        FeatureStoreRequestEntity entity = request.getEntities().get(0);
        assertNotNull(entity);
        assertNotNull(entity.getFeatures());
        assertTrue(entity.getFeatures().isEmpty());
        
        assertNotNull(entity.getEntity());
        assertNull(entity.getEntity().getUserDetails());
        assertNull(entity.getEntity().getCityIdCheckInDate());
    }

    @Test
    public void testPrepareUserProfile() {
        // Given
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("testUuid");
        extendedUser.setProfileType("BUSINESS");

        // When
        String userProfile = featurestoreHelper.prepareUserProfile(extendedUser);

        // Then
        assertEquals("testUuid/BUSINESS", userProfile);
    }


    @Test
    public void testProcessFeatureStoreResponseForBusinessUser_WithInvalidResponse() {
        // Given
        FeatureStoreResponse response = new FeatureStoreResponse();
        FeatureStoreRequest request = new FeatureStoreRequest();

        // When
        boolean result = featurestoreHelper.processFeatureStoreResponseForBusinessUser(response, request);

        // Then
        assertFalse(result);
    }

    @Test
    public void testProcessFeatureStoreResponseForBusinessUser_WithNullInputs() {
        // When
        boolean result = featurestoreHelper.processFeatureStoreResponseForBusinessUser(null, null);

        // Then
        assertFalse(result);
    }
} 