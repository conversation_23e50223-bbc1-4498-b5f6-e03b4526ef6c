package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PricingEngineHelperTest {

    @InjectMocks
    PricingEngineHelper pricingEngineHelper;

    @Spy
    Utility utility;

    @Test
    public void testAppendPEEInUrl() {
        String expData = "{\"PEE\":\"t\"}";
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?pee=T");

        expData = "{\"PEE\":\"f\"}";
        url = "http://test";
        finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?pee=F");

        expData = "{\"PEE\":\"T\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&pee=T");

        expData = "{\"PEE\":\"F\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&pee=F");
    }

    @Test
    public void testAppendPEEDInUrl() {
        String expData = "{\"PEED\":\"t\"}";
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?peed=T");

        expData = "{\"PEED\":\"f\"}";
        url = "http://test";
        finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?peed=F");

        expData = "{\"PEED\":\"T\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&peed=T");

        expData = "{\"PEED\":\"F\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&peed=F");
    }
}
