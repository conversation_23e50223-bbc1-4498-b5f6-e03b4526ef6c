package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigProps;
import com.mmt.hotels.clientgateway.restexecutors.PolyglotRestExecutor;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.polyglot.PolyglotTranslation;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class MobConfigHelperTest {

	@InjectMocks
	MobConfigHelper mobConfigHelper;
	
	@Mock
	ObjectMapperUtil objectMapperUtil;
	
	@Mock
	PropertyManager propManager;
	
	@Mock
	MobConfigProps mobConfigProps;
	
	@Mock
	CommonHelper commonHelper;

	@Mock
	PolyglotRestExecutor polyglotRestExecutor;

	@Mock
	CommonResponseTransformer commonResponseTransformer;

	@Mock
	PolyglotHelper polyglotHelper;

	private void mockPmsVersion(String currentVersion){
		Mockito.when(propManager.getProperty(Mockito.anyString(), Mockito.anyObject())).thenReturn(mobConfigProps);
	}
	private void mockConfigJson(String configJson){
		Mockito.when(propManager.getProperty(Mockito.anyString(), Mockito.anyObject())).thenReturn(mobConfigProps);
		Mockito.when(mobConfigProps.configJson()).thenReturn(configJson);
	}
	
	@Test
	public void getHotelMobConfigAsStringTest() throws Exception {
		mockPmsVersion("");
		Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("json");
		assertThat(mobConfigHelper.getHotelMobConfigAsString(), containsString("json"));


	}
}
