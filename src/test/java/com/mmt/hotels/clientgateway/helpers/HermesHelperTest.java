package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HermesHelperTest {

    @InjectMocks
    private HermesHelper hermesHelper;

    @Mock
    private Utility utility;

    @Mock
    private PricingEngineHelper pricingEngineHelper;

    @Before
    public void setup() {
        ReflectionTestUtils.setField(hermesHelper, "domain", "https://test.domain.com");
        ReflectionTestUtils.setField(hermesHelper, "detailPriceUrl", "api/detail/price");
    }

    @Test
    public void testGetPaxStringFromRoomStay_WithChildren() {
        // Given
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate1 = new RoomStayCandidate();
        candidate1.setAdultCount(2);
        candidate1.setChildAges(Arrays.asList(5, 7));
        roomStayCandidates.add(candidate1);

        RoomStayCandidate candidate2 = new RoomStayCandidate();
        candidate2.setAdultCount(1);
        candidate2.setChildAges(Arrays.asList(3));
        roomStayCandidates.add(candidate2);

        when(utility.getTotalAdultsFromRequest(any())).thenReturn(3);
        when(utility.getTotalChildrenFromRequest(any())).thenReturn(3);

        // When
        String paxString = hermesHelper.getPaxStringFromRoomStay(roomStayCandidates);

        // Then
        assertEquals("2-3-3-5_7_3", paxString);
    }

    @Test
    public void testGetPaxStringFromRoomStay_WithoutChildren() {
        // Given
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate = new RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(new ArrayList<>());
        roomStayCandidates.add(candidate);

        when(utility.getTotalAdultsFromRequest(any())).thenReturn(2);
        when(utility.getTotalChildrenFromRequest(any())).thenReturn(0);

        // When
        String paxString = hermesHelper.getPaxStringFromRoomStay(roomStayCandidates);

        // Then
        assertEquals("1-2-0", paxString);
    }

    @Test
    public void testGetHermesFlavour() {
        assertEquals("mobile", hermesHelper.getHermesFlavour("PWA"));
        assertEquals("mobile", hermesHelper.getHermesFlavour("MSITE"));
        assertEquals("ios", hermesHelper.getHermesFlavour("IOS"));
        assertEquals("android", hermesHelper.getHermesFlavour("ANDROID"));
        assertEquals("v3", hermesHelper.getHermesFlavour("OTHER"));
    }

    @Test
    public void testGetHermesComboId_ValidCombo() {
        // Given
        RecommendedCombo combo = new RecommendedCombo();
        RoomDetailsResponse room = new RoomDetailsResponse();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setPayMode("PAY_AT_HOTEL");
        ratePlan.setSegmentId("SEG123");
        ratePlan.setSupplierCode("SUP456");
        combo.setComboMealPlan("CP");

        // When
        String comboId = hermesHelper.getHermesComboId(combo);

        // Then
        assertNotNull(combo);
    }

    @Test
    public void testGetHermesComboId_InvalidCombo() {
        // Given
        RecommendedCombo combo = new RecommendedCombo();
        combo.setRooms(new ArrayList<>());

        // When
        String comboId = hermesHelper.getHermesComboId(combo);

        // Then
        assertNull(comboId);
    }
} 