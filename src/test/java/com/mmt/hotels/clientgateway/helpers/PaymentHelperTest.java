package com.mmt.hotels.clientgateway.helpers;

import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.OfferDetailsRequest;
import com.mmt.hotels.clientgateway.request.payment.GstnDetail;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.payment.*;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.HeaderConstants.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class PaymentHelperTest {


    @Mock
    CommonHelper commonHelper;

    @InjectMocks
    PaymentHelper paymentHelper;

    @Mock
    UserServiceExecutor userService;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    ScramblerClient scramblerClient;

    @Mock
    PhoneNumberUtil phoneNumberUtil;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;



    @Test
    public void initTest() throws  Exception{
        CommonConfig config = Mockito.mock(CommonConfig.class);
        Mockito.when(propertyManager.getProperty(Mockito.any(),Mockito.any())).thenReturn(config);
        paymentHelper.init();
        Field userRetries = PaymentHelper.class.getDeclaredField("createGuestUserRetries");
        userRetries.setAccessible(true);
        Assert.assertEquals(0, (int)userRetries.get(paymentHelper));

        Mockito.when(propertyManager.getProperty(Mockito.any(),Mockito.any())).thenReturn(null);
        paymentHelper.init();

    }

    @Test
    public void GenderTest() {
        Assert.assertEquals(Gender.MALE , paymentHelper.getGenderFromTitle("Mr."));
        Assert.assertEquals(Gender.FEMALE , paymentHelper.getGenderFromTitle("Mrs."));
        Assert.assertEquals(Gender.FEMALE , paymentHelper.getGenderFromTitle("Ms."));
        Assert.assertEquals(Gender.MALE , paymentHelper.getGenderFromTitle("trina."));
    }

   /* @Test
    public void modifyTravellerDetailsTest() {

        BeginCheckoutReqBody paymentRequestClient = new BeginCheckoutReqBody();
        paymentRequestClient.setTravelerDetail(new TravelerDetail());
        paymentRequestClient.getTravelerDetail().setTitle("abcd");
        Mockito.when(paymentHelper.getGenderFromTitle(Mockito.anyString())).thenReturn(Gender.MALE);

        paymentHelper.modifyTravelerDetails(paymentRequestClient);
        Assert.assertTrue(paymentRequestClient.getTravelerDetail().isMasterPax());


    }

    @Test
    public void sanitizeTimeOutURLTest() {

        PaymentRequestClient paymentRequestClient = new PaymentRequestClient();
        paymentRequestClient.setTravelerDetail(new TravelerDetail());
        paymentRequestClient.getTravelerDetail().setTitle("abcd");
        paymentRequestClient.setErrorConfig(new ErrorConfig());
        paymentRequestClient.getErrorConfig().setSessionTimeoutURL("abcd//http");


        paymentHelper.sanitizeTimeOutURL(paymentRequestClient);
        Assert.assertNotNull(paymentRequestClient.getErrorConfig().getSessionTimeoutURL());


    }*/

    @Test
    public void populateAndCheckUserDataTest() throws ClientGatewayException, ScramblerClientException {

        BeginCheckoutReqBody paymentRequestClient = buildPaymentRequest();

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
        userServiceResponse.getResult().getExtendedUser().setLoginInfoList(new ArrayList<>());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginType("MOBILE");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginId("9811111111");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(1).setLoginType("EMAIL");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(1).setLoginId("<EMAIL>");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(2).setLoginType("FK_IDNTY");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(2).setLoginId("FK123");
        userServiceResponse.getResult().getExtendedUser().setPersonalDetails(new UserPersonalDetail());
        userServiceResponse.getResult().getExtendedUser().getPersonalDetails().setName(new UserName());
        userServiceResponse.getResult().getExtendedUser().getPersonalDetails().getName().setFirstName("First");
        userServiceResponse.getResult().getExtendedUser().getPersonalDetails().getName().setLastName("Last");
        userServiceResponse.getResult().getExtendedUser().setProfileType("BUSINESS");
        userServiceResponse.getResult().getExtendedUser().setUuid("ABCDEFG");
        userServiceResponse.getResult().getExtendedUser().setProfileId("pid");
        AddressDetails addressDetails = new AddressDetails();
        addressDetails.setState("kerala");
        addressDetails.setPostalCode("234235");
        addressDetails.setAddress1("test address");
        userServiceResponse.getResult().getExtendedUser().setAddressDetails(Collections.singletonList(addressDetails));

        paymentRequestClient.getPaymentDetail().setChannel("FKPWA");

        Mockito.when(userService.getUserServiceResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userServiceResponse);

        UserServiceResponse response =  paymentHelper.populateAndCheckUserData(paymentRequestClient,new HashMap<>());
        Assert.assertNotNull(response);
        Assert.assertEquals( "ABCDEFG" ,paymentRequestClient.getUserDetail().getUuid());
        Assert.assertEquals("BUSINESS",paymentRequestClient.getUserDetail().getProfileType());
        Assert.assertEquals("<EMAIL>",paymentRequestClient.getUserDetail().getEmailID());
        Assert.assertEquals("9811111111",paymentRequestClient.getUserDetail().getMobileNo());
        Assert.assertNotNull(paymentRequestClient.getUserDetail().getEmailCommId());
        Assert.assertNotNull(paymentRequestClient.getUserDetail().getPhoneCommId());
        Assert.assertEquals("FK123",paymentRequestClient.getFk_token());
        Assert.assertEquals("kerala", paymentRequestClient.getAddressDetails().getState());
        Assert.assertEquals("234235", paymentRequestClient.getAddressDetails().getPostalCode());
        Assert.assertEquals("test address", paymentRequestClient.getAddressDetails().getAddress1());
    }

    @Test(expected = ClientGatewayException.class)
    public void populateAndCheckUserExceptionTest() throws ClientGatewayException, ScramblerClientException {
        BeginCheckoutReqBody paymentRequestClient = buildPaymentRequest();
        Mockito.when(userService.getUserServiceResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any() )).thenThrow(new NullPointerException("abc"));
        paymentHelper.populateAndCheckUserData(paymentRequestClient,new HashMap<>());
    }

    @Test
    public void populateGuestUserDataTest() throws ClientGatewayException, ScramblerClientException {

        BeginCheckoutReqBody paymentRequestClient = buildPaymentRequest();
        paymentRequestClient.setUserDetail(null);

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setData(new UserData());
        userServiceResponse.getResult().getData().setUuid("ABCDEFG");
        Mockito.when(userService.getUserServiceResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any() )).thenReturn(null);
        Mockito.when(userService.createGuestUser(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyInt(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userServiceResponse);
        paymentHelper.populateAndCheckUserData(paymentRequestClient,new HashMap<>());
        Assert.assertEquals( "ABCDEFG" ,paymentRequestClient.getUserDetail().getUuid());
        Assert.assertEquals("PERSONAL",paymentRequestClient.getUserDetail().getProfileType());
        Assert.assertEquals("<EMAIL>",paymentRequestClient.getUserDetail().getEmailID());
        Assert.assertEquals("9811111121",paymentRequestClient.getUserDetail().getMobileNo());
        Assert.assertNotNull(paymentRequestClient.getUserDetail().getEmailCommId());
        Assert.assertNotNull(paymentRequestClient.getUserDetail().getPhoneCommId());
    }


    @Test(expected = ClientGatewayException.class)
    public void populateGuestUserExceptionTest() throws ClientGatewayException, ScramblerClientException {
        BeginCheckoutReqBody paymentRequestClient = buildPaymentRequest();
        paymentRequestClient.setUserDetail(null);
        Mockito.when(userService.getUserServiceResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any() )).thenReturn(null);
        Mockito.when(userService.createGuestUser(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyInt(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenThrow(new ClientGatewayException());
        paymentHelper.populateAndCheckUserData(paymentRequestClient,new HashMap<>());
    }

    private BeginCheckoutReqBody buildPaymentRequest() {

       BeginCheckoutReqBody paymentRequestClient = new BeginCheckoutReqBody();
        paymentRequestClient.setUserDetail(new UserDetail());
        paymentRequestClient.getUserDetail().setEmailID("<EMAIL>");
        paymentRequestClient.getUserDetail().setMobileNo("9898989898");

        paymentRequestClient.setTravelerDetailsList(new ArrayList<>());
        paymentRequestClient.getTravelerDetailsList().add(new TravelerDetail());
        paymentRequestClient.getTravelerDetailsList().get(0).setEmailID("<EMAIL>");
        paymentRequestClient.getTravelerDetailsList().get(0).setMobileNo("9811111121");
        paymentRequestClient.getTravelerDetailsList().get(0).setIsdCode("isd");
        paymentRequestClient.getTravelerDetailsList().get(0).setFirstName("blah");
        paymentRequestClient.getTravelerDetailsList().get(0).setLastName("halb");

        paymentRequestClient.setAuthToken("abc");

        paymentRequestClient.setPaymentDetail(new PaymentDetail());
        paymentRequestClient.getPaymentDetail().setChannel("FKPWA");
        paymentRequestClient.setCorrelationKey("ckey");
        paymentRequestClient.setIdContext("corp");
        paymentRequestClient.setSiteDomain("gcc");

        paymentRequestClient.setRequiredOtpValidation(true);
        return paymentRequestClient;
    }

    @Test
    public void checkUserOTPValidatedTest() throws Exception {

        BeginCheckoutReqBody paymentRequestClient = buildPaymentRequest();
        paymentRequestClient.setRequiredOtpValidation(true);
        paymentRequestClient.setUserDetail(new UserDetail());
        paymentRequestClient.getUserDetail().setMobileNo("*********");
        Assert.assertTrue(paymentHelper.checkUserOTPValidated(paymentRequestClient));


        CommonConfig config = Mockito.mock(CommonConfig.class);
        Mockito.when(propertyManager.getProperty(Mockito.any(),Mockito.any())).thenReturn(config);
        Assert.assertFalse(paymentHelper.checkUserOTPValidated(paymentRequestClient));

        paymentRequestClient.getUserDetail().setMobileNo("9811111121");
        Assert.assertTrue(paymentHelper.checkUserOTPValidated(paymentRequestClient));



    }

    @Test
    public void populateUUIDTest() throws Exception{

        UserServiceResponse userServiceResponse = getUserServiceResponse();
        BeginCheckoutReqBody paymentRequestClient = getPaymentRequest();

        paymentRequestClient.setUserDetail(new UserDetail());
        paymentHelper.populateUUID(paymentRequestClient, userServiceResponse);
        Assert.assertNotNull(paymentRequestClient);

    }


    private UserServiceResponse getUserServiceResponse() {

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
        userServiceResponse.getResult().getExtendedUser().setLoginInfoList(new ArrayList<>());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginType("MOBILE");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginId("9811111111");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setCountryCode("91");

        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(1).setLoginType("EMAIL");
        userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(1).setLoginId("<EMAIL>");

        userServiceResponse.getResult().getExtendedUser().setPersonalDetails(new UserPersonalDetail());
        userServiceResponse.getResult().getExtendedUser().getPersonalDetails().setName(new UserName());
        userServiceResponse.getResult().getExtendedUser().getPersonalDetails().getName().setFirstName("First");
        userServiceResponse.getResult().getExtendedUser().getPersonalDetails().getName().setLastName("Last");


        userServiceResponse.getResult().getExtendedUser().setUuid("jpgndjcd");
        userServiceResponse.getResult().getExtendedUser().setProfileType("BUSINESS");
        userServiceResponse.getResult().getExtendedUser().setProfileId("pid");

        return userServiceResponse;

    }

    private BeginCheckoutReqBody getPaymentRequest() {

        BeginCheckoutReqBody paymentRequestClient = new BeginCheckoutReqBody();

        paymentRequestClient.setTravelerDetailsList(new ArrayList<>());
        paymentRequestClient.getTravelerDetailsList().add(new TravelerDetail());
        paymentRequestClient.getTravelerDetailsList().get(0).setEmailID("<EMAIL>");
        paymentRequestClient.getTravelerDetailsList().get(0).setMobileNo("*********");
        paymentRequestClient.getTravelerDetailsList().get(0).setIsdCode("91");
        paymentRequestClient.getTravelerDetailsList().get(0).setFirstName("First");
        paymentRequestClient.getTravelerDetailsList().get(0).setLastName("Last");

        paymentRequestClient.setAuthToken("abc");

        paymentRequestClient.setPaymentDetail(new PaymentDetail());
        paymentRequestClient.getPaymentDetail().setChannel("FKPWA");
        paymentRequestClient.setCorrelationKey("ckey");
        paymentRequestClient.setIdContext("corp");
        paymentRequestClient.setSiteDomain("gcc");

        UserDetail userDetail = new UserDetail();
        userDetail.setUuid("UUID");
        paymentRequestClient.setUserDetail(userDetail);


        paymentRequestClient.setRequiredOtpValidation(true);
        return paymentRequestClient;
    }

    @Test
    public void modifyPaymentRequestTest() throws ClientGatewayException, ScramblerClientException {
        BeginCheckoutReqBody beginCheckoutReqBody = getPaymentRequest();
        Mockito.lenient().when(commonHelper.getAuthToken(new HashMap<>())).thenReturn("Auth");

        BeginCheckoutReqBody response = paymentHelper.modifyPaymentRequest(beginCheckoutReqBody, new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void getUserServiceResponseFromUUID_Test() throws ClientGatewayException {
        com.mmt.hotels.clientgateway.request.OfferDetailsRequest offerDetailReq = new OfferDetailsRequest();
        offerDetailReq.setCorrelationKey("abc");
        Map<String, String> map = new HashMap<>();
        map.put(MMT_AUTH, "auth");
        map.put(UUID, "uuid");
        map.put(REGION, "In");
        UserServiceResponse userServiceResponse = new UserServiceResponse();
        Mockito.when(userService.getUserServiceResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userServiceResponse);
        paymentHelper.getUserServiceResponseFromUUID(offerDetailReq, map);
    }


}