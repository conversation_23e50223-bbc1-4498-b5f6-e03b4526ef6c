package com.mmt.hotels.clientgateway.helpers;

import java.lang.reflect.Method;
import java.util.*;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.RoomPaxInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollection;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.env.Environment;

import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertTrue;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class ListingHelperTest {
	
	@InjectMocks
	ListingHelper listingHelper;

	@Mock
	private CommonConfigHelper commonConfigHelper;
	
	@Mock
	Environment env;

	@Mock
	PolyglotService polyglotService;
	
	@Test
	public void testConvertSearchHotelsToPersonalizedHotels() {

		/* Response is null case */
		SearchWrapperResponseBO webApiResponse = null;
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("abc");
		ListingPagePersonalizationResponsBO listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNull(listingBO);

		/* START - test buildPersonalizedResponse */
		webApiResponse = new SearchWrapperResponseBO.Builder().build();
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNull(listingBO.getPersonalizedResponse());

		List<SearchWrapperHotelEntity> hotelList = new ArrayList<>();
		hotelList.add(new SearchWrapperHotelEntity());
		hotelList.add(new SearchWrapperHotelEntity());

		webApiResponse.setHotelList(hotelList);
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Optional<PersonalizedResponse<SearchWrapperHotelEntity>> section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertEquals(section.get().getCount(),webApiResponse.getHotelList().size());

		webApiResponse.setNonAltAccoHotelList(hotelList);
		webApiResponse.setNonAltAccoHeading("abd");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.NON_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertTrue(section.get().getHeading().equalsIgnoreCase(webApiResponse.getNonAltAccoHeading()));
		Assert.assertEquals(section.get().getCount(),webApiResponse.getNonAltAccoHotelList().size());

		webApiResponse.setOtherAltAccoHotelList(hotelList);
		webApiResponse.setOtherAltAccoHeading("def");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.NON_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.OTHER_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertTrue(section.get().getHeading().equalsIgnoreCase(webApiResponse.getOtherAltAccoHeading()));
		Assert.assertEquals(section.get().getCount(),webApiResponse.getOtherAltAccoHotelList().size());

		webApiResponse.setNearbyHotelList(hotelList);
		webApiResponse.setNearbyHeading("ghi");
		webApiResponse.setNearbySubHeading("jkl");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.NON_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.OTHER_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.NEARBY_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertTrue(section.get().getHeading().equalsIgnoreCase(webApiResponse.getNearbyHeading()));
		Assert.assertTrue(section.get().getSubHeading().equalsIgnoreCase(webApiResponse.getNearbySubHeading()));
		Assert.assertEquals(section.get().getCount(),webApiResponse.getNearbyHotelList().size());
		/* END - test buildPersonalizedResponse */

		webApiResponse.setSharingUrl("www.test.com");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertEquals(listingBO.getSharingUrl(),"www.test.com");

	}

	@Test
	public void testUpdateHeading(){
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "Properties in {AREA}", searchHotelsRequest, null));
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "Properties in {AREA}", searchHotelsRequest, "test"));
		String heading = ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "{AREA}" , searchHotelsRequest, "test");
		assertTrue(heading.equalsIgnoreCase("test"));
		searchHotelsRequest.setMatchMakerDetails(new MatchMakerRequest());
		searchHotelsRequest.getMatchMakerDetails().setSelectedTags(new ArrayList<>());
		searchHotelsRequest.getMatchMakerDetails().getSelectedTags().add(new Tags());
		searchHotelsRequest.getMatchMakerDetails().getSelectedTags().get(0).setTagDescription("test1");
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading","Properties in {AREA}" , searchHotelsRequest, "test"));
		heading = ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "{AREA}" , searchHotelsRequest, null);
		assertTrue(heading.equalsIgnoreCase("test1"));
	}

	@Test
	public void testUpdateCollectionCounts(){
		Map<String, Map<String,String>> collCountMap = new HashMap<>();
		Map<String,String> pageContextCountMap = new HashMap<>();
		pageContextCountMap.put("LISTING","3");
		collCountMap.put("MSITE", pageContextCountMap);
		Mockito.when(commonConfigHelper.getCollectionCountMapping()).thenReturn(collCountMap);
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setPageContext("LISTING");
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("msite");
		searchHotelsRequest.setDeviceDetails(deviceDetails);
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		CollectionCriteria collectionCriteria = new CollectionCriteria();
		searchCriteria.setCollectionCriteria(collectionCriteria);
		searchHotelsRequest.setSearchCriteria(searchCriteria);
		listingHelper.updateCollectionCounts(searchHotelsRequest);
		Assert.assertEquals("3", searchHotelsRequest.getSearchCriteria().getCollectionCriteria().getCollectionsCount());
	}

	@Test
	public void testSortBasedOnPriority() {
		List<FetchCollection> fetchCollectionList=new ArrayList<>();
		FetchCollection obj1=new FetchCollection();
		obj1.setPriority("2");
		FetchCollection obj2=new FetchCollection();
		obj2.setPriority("1");
		fetchCollectionList.add(obj1);
		fetchCollectionList.add(obj2);
		listingHelper.sortBasedOnPriority(fetchCollectionList);
		Assert.assertTrue(fetchCollectionList.get(0).getPriority()=="1");
	}


}
