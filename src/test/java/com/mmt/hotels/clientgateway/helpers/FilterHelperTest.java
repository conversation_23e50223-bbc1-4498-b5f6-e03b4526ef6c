package com.mmt.hotels.clientgateway.helpers;

import java.util.ArrayList;
import java.util.LinkedHashMap;

import com.mmt.hotels.clientgateway.businessobjects.FilterConfigDetail;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import org.springframework.test.util.ReflectionTestUtils;


@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class FilterHelperTest {

	@InjectMocks
	FilterHelper filterHelper;

	@Mock
	PolyglotService polyglotService;
	
	@Test
	public void getFilterConfigTest() {
		Assert.assertNotNull(filterHelper.getFilterConfig("{}", "CORP"));
	}
	
	@Test
	public void mergeFilterConfigTest() {
		FilterConfiguration srcFilterConfig = new FilterConfiguration();
		FilterConfiguration addedFilterConfig = new FilterConfiguration();
		srcFilterConfig.setConditions(new LinkedHashMap<>());
		srcFilterConfig.getConditions().put("test", new ArrayList<String>());
		srcFilterConfig.setFilters(new LinkedHashMap<>());
		srcFilterConfig.getFilters().put("test", new FilterConfigCategory());
		addedFilterConfig.setConditions(new LinkedHashMap<>());
		addedFilterConfig.getConditions().put("test", new ArrayList<String>());
		addedFilterConfig.setFilters(new LinkedHashMap<>());
		addedFilterConfig.getFilters().put("test", new FilterConfigCategory());
		
		filterHelper.mergeFilterConfig(new FilterConfiguration(), null, "CORP");
		filterHelper.mergeFilterConfig(new FilterConfiguration(), new FilterConfiguration(), "CORP");
		Assert.assertNotNull(filterHelper.mergeFilterConfig(srcFilterConfig, addedFilterConfig, "B2C"));
	}
	
	
	@Test
	public void fetchPriceTitleTest() {
		LinkedHashMap<String,String>expDataMap = new LinkedHashMap<>();
		expDataMap.put("PDO","PN");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","PNT");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","PRN");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","PRNT");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","TP");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","TPT");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
	}

	@Test
	public void mergeConditionsTest() {
		FilterConfigCategory fCategory = new FilterConfigCategory();
		fCategory.setCondition(new LinkedHashMap<>());
		fCategory.getCondition().put("abc","abc");
		FilterConfigCategory fCategoryAdded = new FilterConfigCategory();
		fCategoryAdded.setCondition(new LinkedHashMap<>());
		fCategoryAdded.getCondition().put("abc","abc");
		ReflectionTestUtils.invokeMethod(filterHelper, "mergeConditions", fCategory,fCategoryAdded);
	}

	@Test
	public void updateIconUrlsTest() {

		FilterConfigCategory fCategory = new FilterConfigCategory();
		fCategory.setGroups(new LinkedHashMap<>());
		fCategory.getGroups().put("abc",new LinkedHashMap<>());
		fCategory.getGroups().get("abc").put("abc",new FilterConfigDetail());
		fCategory.getGroups().get("abc").get("abc").setIconList(new ArrayList<>());
		fCategory.getGroups().get("abc").get("abc").getIconList().add("xyz");
		fCategory.getGroups().get("abc").get("abc").setImageUrl("url");

		FilterConfigCategory fCategoryAdded = new FilterConfigCategory();
		fCategoryAdded.setGroups(new LinkedHashMap<>());
		fCategoryAdded.getGroups().put("abc",new LinkedHashMap<>());
		fCategoryAdded.getGroups().get("abc").put("abc",new FilterConfigDetail());
		ReflectionTestUtils.invokeMethod(filterHelper, "updateIconUrls", fCategory,fCategoryAdded);

	}

	@Test
	public void removeIconUrlsAndImageUrlsTest() {

		FilterConfiguration mergedFilterConfig = new FilterConfiguration();

		mergedFilterConfig.setFilters(new LinkedHashMap<>());
		FilterConfigCategory filterConfig = new FilterConfigCategory();
		filterConfig.setIconUrl("xyz");
		filterConfig.setGroups(new LinkedHashMap<>());
		filterConfig.getGroups().put("abc",new LinkedHashMap<>());
		filterConfig.getGroups().get("abc").put("abc",new FilterConfigDetail());
		filterConfig.getGroups().get("abc").get("abc").setIconList(new ArrayList<>());
		filterConfig.getGroups().get("abc").get("abc").getIconList().add("xyz");
		filterConfig.getGroups().get("abc").get("abc").setImageUrl("url");
		mergedFilterConfig.getFilters().put("abc",filterConfig);

		ReflectionTestUtils.invokeMethod(filterHelper, "removeIconUrlsAndImageUrls", mergedFilterConfig);
	}

	@Test
	public void calculateFiltersToShowTest() {

		FilterConfiguration mergedFilterConfig = new FilterConfiguration();

		mergedFilterConfig.setFilters(new LinkedHashMap<>());
		FilterConfigCategory filterConfig = new FilterConfigCategory();
		filterConfig.setIconUrl("xyz");
		filterConfig.setGroups(new LinkedHashMap<>());
		filterConfig.getGroups().put("abc",new LinkedHashMap<>());
		filterConfig.getGroups().get("abc").put("abc",new FilterConfigDetail());
		filterConfig.getGroups().get("abc").get("abc").setIconList(new ArrayList<>());
		filterConfig.getGroups().get("abc").get("abc").getIconList().add("xyz");
		filterConfig.getGroups().get("abc").get("abc").setImageUrl("url");
		mergedFilterConfig.getFilters().put("abc",filterConfig);

		FilterConfiguration addedFilterConfig = new FilterConfiguration();
		addedFilterConfig.setFiltersToShow(new LinkedHashMap<>());
		addedFilterConfig.getFiltersToShow().put("abc",new LinkedHashMap<>());
		addedFilterConfig.getFiltersToShow().get("abc").put("abc",new ArrayList<>());
		addedFilterConfig.getFiltersToShow().get("abc").get("abc").add("xyz");

		ReflectionTestUtils.invokeMethod(filterHelper, "calculateFiltersToShow", mergedFilterConfig,addedFilterConfig);
	}
}
