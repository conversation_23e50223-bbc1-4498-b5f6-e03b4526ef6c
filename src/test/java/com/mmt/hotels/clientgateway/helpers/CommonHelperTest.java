package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.restexecutors.PokusExperimentExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.request.flyfish.FlyFishReviewsRequest;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class CommonHelperTest {

	@InjectMocks
	CommonHelper commonHelper;

	@Spy
	ObjectMapperUtil objectMapperUtil;

	@Mock
	private CommonConfigHelper commonConfigHelper;
	
	@Mock
	Environment env;
	
	@Mock
	MetricErrorLogger metricErrorLogger;

	@Mock
	PokusExperimentExecutor pokusExperimentExecutor;

	@Mock
	UserServiceExecutor userServiceExecutor;

	@Mock
	PolyglotService polyglotService;

	@Mock
	 DateUtil dateUtil;

	@Mock
	Utility utility;
	
	private static ThreadPoolTaskExecutor userServiceThreadPool = new ThreadPoolTaskExecutor();
	
	private static ThreadPoolTaskExecutor hydraServiceThreadPool = new ThreadPoolTaskExecutor();

	@Before
	public void init() {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
		List<String> nonAltAccoPropertiesList = new ArrayList<>();
		nonAltAccoPropertiesList.add("Hostel");
 		ReflectionTestUtils.setField(commonHelper, "nonAltAccoProperties",nonAltAccoPropertiesList);
	}

	static {
		hydraServiceThreadPool.setCorePoolSize(10);
		hydraServiceThreadPool.setMaxPoolSize(10);
		hydraServiceThreadPool.setQueueCapacity(10);
		hydraServiceThreadPool.setThreadNamePrefix("hydraServiceThreadPool");
		hydraServiceThreadPool.initialize();
		userServiceThreadPool.setCorePoolSize(10);
		userServiceThreadPool.setMaxPoolSize(10);
		userServiceThreadPool.setQueueCapacity(10);
		userServiceThreadPool.setThreadNamePrefix("userServiceThreadPool");
		userServiceThreadPool.initialize();
	}

	@Test
	public void processRequestTest() throws ClientGatewayException {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		AvailPriceCriteria searchCriteria = new AvailPriceCriteria();
		searchCriteria.setCountryCode("IN");
		searchCriteria.setCheckIn("2020-02-21");
		searchCriteria.setCheckOut("2020-02-22");
		baseSearchRequest.setRequestDetails(new RequestDetails());
		baseSearchRequest.getRequestDetails().setFunnelSource("HOMESTAY");
		baseSearchRequest.getRequestDetails().setPageContext("REVIEW");
		baseSearchRequest.getRequestDetails().setSiteDomain("US");
		baseSearchRequest.setDeviceDetails(new DeviceDetails());
		baseSearchRequest.getDeviceDetails().setDeviceId("test");
		baseSearchRequest.setExpData("{APE:36,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,EMIDT:2}");
		Mockito.when(env.getProperty(Mockito.anyString())).thenReturn("1");
		ReflectionTestUtils.setField(commonHelper, "userServiceThreadPool", userServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "hydraServiceThreadPool", hydraServiceThreadPool);
		baseSearchRequest.getRequestDetails().setVisitorId("test");
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(new HashMap<>());
		pokusExperimentResponse.getPerLobMap().put("HOTEL", pokusAssignVariantResponse);
		Mockito.when(pokusExperimentExecutor.getPokusExperimentResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(pokusExperimentResponse);
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("mmt-auth", "mmt-auth");
		baseSearchRequest.getRequestDetails().setFirstTimeUserState(3);
		baseSearchRequest.setCohertVar(new CohertVar());
		baseSearchRequest.getDeviceDetails().setDeviceType("MOBILE");
		ReflectionTestUtils.setField(commonHelper, "cohortValid", true);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setNoOfCoupons(1);
		baseSearchRequest.setFeatureFlags(featureFlags);
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("LISTING");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("DETAIL");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("ALTACCOLANDING");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("PAGE_CONTEXT_HOMEPAGE");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setFunnelSource("GETAWAY");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
	}

	@Test
	public void getMMTAuthTest(){
		Map<String, String> headerMap  = new HashMap<>();
		headerMap.put("backup_auth", "authinMap");
		Assert.assertNull(commonHelper.getMMTAuth(headerMap, "ANDROID"));

		headerMap.clear();
		headerMap.put("backup_auth","mmtAuth=\"MAT123\"");
		Assert.assertNotNull(commonHelper.getMMTAuth(headerMap, "ANDROID"));
		Assert.assertEquals("MAT123",commonHelper.getMMTAuth(headerMap,"ANDROID"));

		headerMap.clear();
		headerMap.put("mmt-auth","MAT456");
		Assert.assertEquals("MAT456",commonHelper.getMMTAuth(headerMap,"DESKTOP"));
	}

	@Test(expected=Exception.class)
	public void getMMTAuthTest_Exception(){
		Map<String, String> headerMap  = new HashMap<>();
		headerMap.put("backup_auth","mmtAuth");
		commonHelper.getMMTAuth(headerMap, "ANDROID");
	}

	@Test
	public void getMcidTest(){
		Map<String, String> headerMap  = new HashMap<>();
		headerMap.put("backup_auth", "authinMap");
		headerMap.put(Constants.ANDROID_MCID, "id");
		Assert.assertNotNull(commonHelper.getMcId(headerMap, "ANDROID"));
	}

	@Test
	public void testGetInboundCurrencyCode(){
		Map<String,String> currCityMap = new HashMap();
		currCityMap.put("IN", "INR");
		
		List<String> enableInboundExpDeviceList = new ArrayList<>();
		enableInboundExpDeviceList.add("DESKTOP");

		Mockito.when(commonConfigHelper.getCurrCityMap()).thenReturn(currCityMap);
		Mockito.when(commonConfigHelper.getEbableInboundExpDeviceList()).thenReturn(enableInboundExpDeviceList);
		
		Assert.assertNotNull(commonHelper.getInboundCurrencyCode("", "", "DESKTOP"));
		Assert.assertNotNull(commonHelper.getInboundCurrencyCode("IN", "US", "DESKTOP"));
		Assert.assertNotNull(commonHelper.getInboundCurrencyCode("US", "", "DESKTOP"));
	}

	@Test
	public void testIsJsonString(){
		Assert.assertTrue(commonHelper.isJsonString("{\"data\":\"\"}"));
		Assert.assertFalse(commonHelper.isJsonString("text"));
	}
	
	@Test
	public void updateIdContextTest() {
		String idContext = commonHelper.updateIdContext("B2C", "ANDROID");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("B2C", "IOS");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("B2C", "MSITE");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("B2C", "PWA");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("CORP", "ANDROID");
		Assert.assertEquals("CORP", idContext);
		idContext = commonHelper.updateIdContext("B2C", "DESKTOP");
		Assert.assertEquals("B2C", idContext);
	}

	@Test
	public void testUpdateLatLngFromHeader() {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("X-Akamai-Edgescape", "georegion=104,country_code=IN,region_code=DL,city=NEWDELHI,lat=28.60,long=77.20,timezone=GMT*****,continent=AS,throughput=vhigh,bw=5000,asnum=24560,location_id=0");
		SearchCriteria searchCriteria = new SearchCriteria();
		commonHelper.updateLatLngFromHeader(searchCriteria, headerMap);
		Assert.assertNotNull(searchCriteria.getLat());
	}

	@Test
	public void testGetCountryAndCityCodeFromHeader() {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("X-Akamai-Edgescape", "georegion=104,country_code=IN,region_code=DL,city=NEWDELHI,lat=28.60,long=77.20,timezone=GMT*****,continent=AS,throughput=vhigh,bw=5000,asnum=24560,location_id=0");
		Assert.assertNotNull(commonHelper.getCountryAndCityCodeFromHeader(headerMap));
	}

	@Test
	public void testChangeDateToEpoch(){
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(commonHelper, "changeDateStringToInteger", "2022-05-18"));
	}

	@Test
	public void testGetAuthToken(){
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("mmt-auth","MAT1618ebdb84b078ea1c6510a73b62d41b5355c4444257c5c959b991402362bce8f64a77a4eacf4c2e07d5990c08dd1edd3P");
		Assert.assertNotNull(commonHelper.getAuthToken(headerMap));
	}

	@Test
	public void testSanitizeInput(){
		String data = "test";
		Assert.assertNotNull(commonHelper.sanitizeInput(data));
	}

	@Test
	public void testGetUserFirstTimeState(){
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setDeviceDetails(new DeviceDetails());
		baseSearchRequest.setRequestDetails(new RequestDetails());
		baseSearchRequest.getDeviceDetails().setBookingDevice("ANDROID");
		ReflectionTestUtils.invokeMethod(commonHelper, "getUserFirstTimeState", "ANDROID", 1, "IN", new ExtendedUser(), "",null,null);
		ReflectionTestUtils.invokeMethod(commonHelper, "buildFlightBookerRequest",  new ExtendedUser(), "");
		ReflectionTestUtils.invokeMethod(commonHelper, "buildUserStateFeatureRequest", baseSearchRequest, new SearchCriteria(), new ExtendedUser(), "");
		Assert.assertNotNull(commonHelper.executeHydraService("CTDEL","ANDROID","abcd","",1,"IN", new ExtendedUser(),null,""));
	}

	@Test
	public void isJsonString_stringProvidedIsNotJson_returnFalse(){
		Assert.assertFalse(commonHelper.isJsonString(""));
	}

	@Test
	public void isJsonString_stringProvidedIsJson_returnTrue(){
		Assert.assertTrue(commonHelper.isJsonString("{\n" +
				"  \"cumulativeRating\": 4.5,\n" +
				"  \"totalReviewsCount\": 89,\n" +
				"  \"totalRatingCount\": 162,\n" +
				"}\n"));
	}

	@Test
	public void updateReviewsRequest_userInfoIsEmpty(){
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setDeviceDetails(new DeviceDetails());
		baseSearchRequest.setRequestDetails(new RequestDetails());
		baseSearchRequest.getDeviceDetails().setBookingDevice("ANDROID");
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCityCode("");
		ReflectionTestUtils.invokeMethod(commonHelper, "getUserLobKey", "","","");
		ReflectionTestUtils.invokeMethod(commonHelper, "getDeviceLobKey", "","");
		ReflectionTestUtils.invokeMethod(commonHelper, "checkValidUserId", new ExtendedUser(),"");
		commonHelper.updateReviewsRequest(null,new FlyFishReviewsRequest());
		Map<String,String> mcid = new HashMap<>();
		mcid.put("mcid","");
		ReflectionTestUtils.invokeMethod(commonHelper, "isFlightBooker", "ANDROID","CTDEL","abcd",new ExtendedUser(),"",mcid);
		Assert.assertEquals("",commonHelper.getMcId(mcid,"ANDROID"));
	}

//	@Test
//	public void testBlockPAHExperimentOn(){
//		boolean b = commonHelper.blockPAHExperimentOn("{\"blockPAH\":\"true\"}");
//		Assert.assertTrue(b);
//		b = commonHelper.blockPAHExperimentOn("{\"blockPAH\":\"false\"}");
//		Assert.assertFalse(b);
//	}

	@Test(expected = AuthenticationException.class)
	public  void processRequestForBkgModFailureTest() throws Exception{
		RatePreviewRequest ratePreviewRequest = new Gson().fromJson("{\"channel\":\"B2C\",\"checkin\":\"2020-10-05\",\"checkout\":\"2020-10-06\",\"city_code\":\"2820046943342890302\",\"country_code\":\"IN\",\"currency\":\"INR\",\"hash_key\":\"\",\"hotel_ids\":[\"3447164777143948329\"],\"id_context\":\"B2C\",\"sub_vendor\":\"\",\"token\":null,\"booking_id\":\"HTLQW576HP\",\"flavour\":\"IOS\",\"app_version\":\"7.8.1\",\"device_id\":\"bc123ed132f\",\"room_criteria\":[{\"room_stay_candidates\":[{\"guest_counts\":[{\"count\":\"2\",\"ages\":[3,5]}]}],\"room_code\":\"45000000263\",\"rate_plan_code\":\"990000423293\",\"supplier_id\":\"ingoibibo\",\"sub_vendor\":\"\"}],\"traveller_email_commId\":[\"sendalltravellersassociatedwithbooker\"]}",
				RatePreviewRequest.class);
		Map<String,String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("REGION","IN");
		httpHeaderMap.put("mmtAuth","a2sacasd");
		httpHeaderMap.put("usr-mcid","sadsasda");
		commonHelper.processRequestForBkgMod(httpHeaderMap,ratePreviewRequest,Constants.PAGE_CONTEXT_REVIEW);

	}

	@Test
	public  void processRequestForBkgModSuccessTest() throws Exception{
		RatePreviewRequest ratePreviewRequest = new Gson().fromJson("{\"channel\":\"B2C\",\"checkin\":\"2020-10-05\",\"checkout\":\"2020-10-06\",\"city_code\":\"2820046943342890302\",\"country_code\":\"IN\",\"currency\":\"INR\",\"hash_key\":\"\",\"hotel_ids\":[\"3447164777143948329\"],\"id_context\":\"B2C\",\"sub_vendor\":\"\",\"token\":null,\"booking_id\":\"HTLQW576HP\",\"flavour\":\"IOS\",\"app_version\":\"7.8.1\",\"device_id\":\"bc123ed132f\",\"room_criteria\":[{\"room_stay_candidates\":[{\"guest_counts\":[{\"count\":\"2\",\"ages\":[3,5]}]}],\"room_code\":\"45000000263\",\"rate_plan_code\":\"990000423293\",\"supplier_id\":\"ingoibibo\",\"sub_vendor\":\"\"}],\"traveller_email_commId\":[\"sendalltravellersassociatedwithbooker\"]}",
				RatePreviewRequest.class);
		Map<String,String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("REGION","IN");
		httpHeaderMap.put("mmt-auth","a2sacasd");
		httpHeaderMap.put("usr-mcid","sadsasda");
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(new UserServiceResult());
		userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
		userServiceResponse.getResult().getExtendedUser().setUuid("ABC");
		userServiceResponse.getResult().getExtendedUser().setProfileType("BUSINESS");
		userServiceResponse.getResult().getExtendedUser().setAccountId("ACDFd");
		userServiceResponse.getResult().getExtendedUser().setProfileId("MMT1231");
		userServiceResponse.getResult().getExtendedUser().setLoginInfoList(new ArrayList<>());
		userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
		userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginId("************");
		userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginType(Constants.LOGIN_TYPE_MOBILE);

		ReflectionTestUtils.setField(commonHelper, "userServiceThreadPool", userServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "hydraServiceThreadPool", hydraServiceThreadPool);
		Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any() , Mockito.any(), Mockito.any(),  Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userServiceResponse);
		Mockito.when(env.getProperty(Mockito.anyString())).thenReturn("1");
		commonHelper.processRequestForBkgMod(httpHeaderMap,ratePreviewRequest,Constants.PAGE_CONTEXT_REVIEW);

	}

	@Test
	public void processRequestCommonsTest() throws ClientGatewayException {
		WishListedHotelsDetailRequest wishListedHotelsDetailRequest = getWishListedHotelsDetailRequest();
		ReflectionTestUtils.setField(commonHelper, "userServiceThreadPool", userServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "hydraServiceThreadPool", hydraServiceThreadPool);
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(new HashMap<>());
		pokusExperimentResponse.getPerLobMap().put("HOTEL", pokusAssignVariantResponse);
		Mockito.when(pokusExperimentExecutor.getPokusExperimentResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(pokusExperimentResponse);
		Map<String, String> headers = new HashMap<>();
		headers.put("mmt-auth", "mmt-auth");
		ReflectionTestUtils.setField(commonHelper, "cohortValid", true);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setNoOfCoupons(1);
		Assert.assertNotNull(commonHelper.processRequest(wishListedHotelsDetailRequest, headers));
	}

	private WishListedHotelsDetailRequest getWishListedHotelsDetailRequest() {
		return new Gson().fromJson("{\"deviceDetails\":{\"appVersion\":\"8.6.4.RC1\",\"bookingDevice\":\"ANDROID\",\"deviceId\":\"311c8e73900a8d98\",\"deviceType\":\"Mobile\"," +
				"\"networkType\":\"WiFi\",\"resolution\":\"xhdpi\"},\"requestDetails\":{\"channel\":\"Native\",\"couponCount\":3,\"funnelSource\":\"HOTELS\"," +
				"\"idContext\":\"B2C\",\"loggedIn\":true,\"pageContext\":\"DETAIL\",\"siteDomain\":\"in\",\"visitNumber\":1," + "\"visitorId\":\"74856825370536007467679936889328709665\"}," +
				"\"searchCriteria\":{\"hotelIds\":[\"201412311314381986\",\"200803050906255532\",\"201007011525075813\",\"201904081604352311\"],\"visitNumber\":1," +
				"\"visitorId\":\"74856825370536007467679936889328709665\",\"cityCode\":\"CTDEL\",\"locationId\":\"CTDEL\",\"locationType\":\"city\",\"countryCode\":\"IN\"," +
				"\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[],\"positionsForAgeLessThan1\":[]}]}," +
				"\"imageDetails\":{\"categories\":[{\"count\":4,\"height\":252,\"imageFormat\":\"webp\",\"type\":\"H\",\"width\":459}],\"types\":[\"professional\",\"traveller\"]}," +
				"\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\",\"MANUAL\",\"OTHER\",\"EXT\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]}}", WishListedHotelsDetailRequest.class);
	}
	@Test
	public void getFunnelSourceTest() {
		List<UserServiceResponse> userServiceResponseList  = new ArrayList<>();
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult userServiceResult = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setProfileType("CTA");
		extendedUser.setAffiliateId("MYPARTNER");
		userServiceResult.setExtendedUser(extendedUser);
		userServiceResponse.setResult(userServiceResult);
		userServiceResponseList.add(userServiceResponse);

		String resp = commonHelper.getFunnelSource("", userServiceResponseList);
		Assert.assertEquals(resp, "MYPARTNER");
	}
	@Test
	public void getApplicationIdTest()
	{
		String pageContext="LISTING";
		Mockito.when(env.getProperty("APPLICATIONID.LISTING")).thenReturn("410");
		Assert.assertEquals(commonHelper.getApplicationId(pageContext), 410);
		Mockito.when(env.getProperty("APPLICATIONID.")).thenReturn("410");
		Assert.assertEquals(commonHelper.getApplicationId(""), 410);
	}

	@Test
	public void checkValidHotelTest(){
		ListingSearchRequest searchHotelsRequest = new ListingSearchRequest();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		searchHotelsRequest.setMatchMakerDetails(new MatchMakerRequest());
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		List<InputHotel> hotels = new ArrayList<>();
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123456");
		hotels.add(inputHotel);
		matchMakerRequest.setHotels(hotels);
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		hotelEntity.setId("123456");
		Assert.assertFalse(commonHelper.checkValidHotel(searchHotelsRequest,hotelEntity));
	}

	@Test
	public void updateRoomStayCandidate() {
		SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
		SearchRoomsCriteria searchRoomsCriteria = new Gson().fromJson("{\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[]}]}",SearchRoomsCriteria.class);
		searchRoomsRequest.setSearchCriteria(searchRoomsCriteria);
		Mockito.when(utility.getPaxStringFromRoomStay(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates())).thenReturn(("1-2-0"));
		commonHelper.updateRoomStayCandidate(searchRoomsRequest);
		Assert.assertEquals(2, searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).getAdultCount().intValue());
	}

	@Test
	public void updateRoomStayCandidate2() {
		SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
		SearchRoomsCriteria searchRoomsCriteria = new Gson().fromJson("{\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[6,7]},{\"adultCount\":2,\"childAges\":[6]},{\"adultCount\":1}]}",SearchRoomsCriteria.class);
		searchRoomsRequest.setSearchCriteria(searchRoomsCriteria);
		Mockito.when(utility.getPaxStringFromRoomStay(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates())).thenReturn(("3-5-2-6_7"));
		commonHelper.updateRoomStayCandidate(searchRoomsRequest);
		Assert.assertEquals(1, searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(2).getChildAges().size());
	}
	@Test
	public void userLocationTest() {
		Map<String, String> httpHeaderMap = new HashMap<>();
		UserLocation userLocation = commonHelper.buildUserLocationFromHeader(httpHeaderMap);
		Assert.assertNull(userLocation.getCity());
		Assert.assertNull(userLocation.getCountry());
		Assert.assertNull(userLocation.getState());
		httpHeaderMap.put(Constants.HEADER_AKAMAI, "georegion=104,country_code=IN,region_code=DL,city=NEWDELHI,lat=28.60,long=77.20,timezone=GMT*****,continent=AS,throughput=vhigh,bw=5000,asnum=133982,proxy=transparent,location_id=0");
		userLocation = commonHelper.buildUserLocationFromHeader(httpHeaderMap);
		Assert.assertEquals("NEWDELHI", userLocation.getCity());
		Assert.assertEquals("DL", userLocation.getState());
		Assert.assertEquals("IN", userLocation.getCountry());
	}

	@Test
	public void getFlavourTest() {
		String deviceType = "ANDROID";
		String expectedFlavour = "android";
		String actualFlavour = commonHelper.getFlavour(deviceType);
		Assert.assertEquals(expectedFlavour, actualFlavour);

		deviceType = "DESKTOP";
		expectedFlavour = "v3";
		actualFlavour = commonHelper.getFlavour(deviceType);
		Assert.assertEquals(expectedFlavour, actualFlavour);

		deviceType = "PWA";
		expectedFlavour = "mobile";
		actualFlavour = commonHelper.getFlavour(deviceType);
		Assert.assertEquals(expectedFlavour, actualFlavour);
	}


	@Test
	public void testCheckValidHotel_NoMatchMakerDetails() {
		ListingSearchRequest searchRequest = new ListingSearchRequest();
		HotelDetails hotelEntity = new HotelDetails();
		CommonHelper commonHelper = new CommonHelper();

		Assert.assertFalse(commonHelper.checkValidHotel(searchRequest, hotelEntity));
	}

	@Test
	public void testCheckValidHotel_EmptyHotelsList() {
		ListingSearchRequest searchRequest = Mockito.mock(ListingSearchRequest.class);
		MatchMakerRequest matchMakerRequest = Mockito.mock(MatchMakerRequest.class);
		Mockito.when(searchRequest.getMatchMakerDetails()).thenReturn(matchMakerRequest);
		Mockito.when(matchMakerRequest.getHotels()).thenReturn(Collections.emptyList());

		HotelDetails hotelEntity = new HotelDetails();
		CommonHelper commonHelper = new CommonHelper();

		Assert.assertTrue(commonHelper.checkValidHotel(searchRequest, hotelEntity));
	}

	@Test
	public void testCheckValidHotel_HotelIdMatches() {
		ListingSearchRequest searchRequest = Mockito.mock(ListingSearchRequest.class);
		MatchMakerRequest matchMakerRequest = Mockito.mock(MatchMakerRequest.class);
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123");
		List<InputHotel> hotels = Collections.singletonList(inputHotel);
		Mockito.when(searchRequest.getMatchMakerDetails()).thenReturn(matchMakerRequest);
		Mockito.when(matchMakerRequest.getHotels()).thenReturn(hotels);

		HotelDetails hotelEntity = new HotelDetails();
		hotelEntity.setId("123");
		CommonHelper commonHelper = new CommonHelper();

		Assert.assertFalse(commonHelper.checkValidHotel(searchRequest, hotelEntity));
	}

	@Test
	public void testCheckValidHotel_HotelIdDoesNotMatch() {
		ListingSearchRequest searchRequest = Mockito.mock(ListingSearchRequest.class);
		MatchMakerRequest matchMakerRequest = Mockito.mock(MatchMakerRequest.class);
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123");
		List<InputHotel> hotels = Collections.singletonList(inputHotel);
		Mockito.when(searchRequest.getMatchMakerDetails()).thenReturn(matchMakerRequest);
		Mockito.when(matchMakerRequest.getHotels()).thenReturn(hotels);

		HotelDetails hotelEntity = new HotelDetails();
		hotelEntity.setId("456");
		CommonHelper commonHelper = new CommonHelper();

		Assert.assertTrue(commonHelper.checkValidHotel(searchRequest, hotelEntity));
	}

}
