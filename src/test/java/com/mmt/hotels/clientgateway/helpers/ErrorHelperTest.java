package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.enums.ErrorCodesCorp;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsApprover;


import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class ErrorHelperTest {

    @InjectMocks
    ErrorHelper errorHelper;

    @Mock
    private PolyglotService polyglotService;

    @Test
    public void getSubtitleForErrorTest() {
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Test String");
        String temp = errorHelper.getSubtitleForError(Mockito.anyString());
        Assert.assertNotNull(temp);
    }

    @Test
    public void getTitleForErrorTest() {
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Test String");
        ReflectionTestUtils.invokeMethod(errorHelper, "getTitleForError", Mockito.anyString());
    }

    @Test
    public void getErrorWithTitleTest() {

        Error error = errorHelper.getErrorWithTitle("cg/get-approvals","",null,"APPROVER");
        Assert.assertNull(error);
        error = errorHelper.getErrorWithTitle("cg/get-approvals","",null,"NOTAPPROVER");
        Assert.assertNull(error);
        error = errorHelper.getErrorWithTitle("xyz","",null,"APPROVER");
        Assert.assertNull(error);

    }


}