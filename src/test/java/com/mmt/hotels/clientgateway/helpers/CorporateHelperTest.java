package com.mmt.hotels.clientgateway.helpers;


import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.UUIDException;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class CorporateHelperTest {

    @InjectMocks
    CorporateHelper corporateHelper;

    @Mock
    CommonHelper commonHelper;

    @Test
    public void generateInvalidApprovalErrorRspTest() {

        com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse errorResponse =  corporateHelper.generateInvalidApprovalErrorRsp();
        Assert.assertNotNull(errorResponse);

    }

    @Test
    public void getCorpUserIdForMyBizUserTest() throws UUIDException, ClientGatewayException {

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        userServiceResponse.setResult(new UserServiceResult());
        userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
        userServiceResponse.getResult().getExtendedUser().setProfileId("1234");
        userServiceResponse.getResult().getExtendedUser().setProfileType("BUSINESS");
        userServiceResponse.getResult().getExtendedUser().setUuid("1234");

        Mockito.when(commonHelper.getAuthToken(Mockito.anyMap())).thenReturn("mmt1234");
        Mockito.when(commonHelper.getUserDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(userServiceResponse);
        UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser("desktop", new HashMap<>(), "abcd","<EMAIL>","IN");
        Assert.assertNotNull(userDetailsDTO);

    }

    @Test
    public void getErrorResponseTest() {

        CBError cbError =  CBError.GENERIC_ERROR;
        ErrorResponse response = corporateHelper.getErrorResponseFromCBError(cbError);
        Assert.assertNotNull(response);

    }
}


