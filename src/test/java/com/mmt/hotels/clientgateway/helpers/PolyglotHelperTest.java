package com.mmt.hotels.clientgateway.helpers;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigDetail;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.thankyou.MyTripCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.response.listpersonalization.LuxeToolTip;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class PolyglotHelperTest {

    @InjectMocks
    PolyglotHelper polyglotHelper;
    @Mock
    PolyglotService polyglotService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void translatePersuasionMapTest() {
        Map<String, Map<String, PersuasionResponse>> persuasionResponseMap = new HashMap<>();
        PersuasionResponse persuasionResponse = new PersuasionResponse();
        Map<String,PersuasionResponse> map = new HashMap<>();
        map.put("samplekey", persuasionResponse);
        persuasionResponseMap.put("samplekey1", map);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
        polyglotHelper.translatePersuasionMap(persuasionResponseMap);

    }

    @Test
    public void translateHotelCategoryDataTest() {
        Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMap = new HashMap<>();
        HotelCategoryDataWeb hotelCategoryDataWeb = new HotelCategoryDataWeb();
        hotelCategoryDataWebMap.put("samplekey", hotelCategoryDataWeb);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
        polyglotHelper.translateHotelCategoryData(hotelCategoryDataWebMap);


    }

    @Test
    public void translateMyTripsCardsTest() {
        Map<String, MyTripCard> myTripsCardTypeToCardDetailsModified = new HashMap<>();
        MyTripCard myTripCard = new MyTripCard();
        myTripsCardTypeToCardDetailsModified.put("samplekey", myTripCard);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
        polyglotHelper.translateMyTripsCards(myTripsCardTypeToCardDetailsModified, null);
    }

    @Test
    public void translateFilterConfigTest() {

        FilterConfiguration fConfig=new FilterConfiguration();
        LinkedHashMap<String, FilterConfigCategory> filters=new LinkedHashMap<>();
        FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
        LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups=new LinkedHashMap<>();
        LinkedHashMap<String, FilterConfigDetail> map=new LinkedHashMap<>();
        FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
        map.put("samplekey2",filterConfigDetail);
        groups.put("samplekey3",map);
        filterConfigCategory.setGroups(groups);
        filters.put("samplekey",filterConfigCategory);
        fConfig.setFilters(filters);
        //Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
        polyglotHelper.translateFilterConfig(fConfig,null);
    }

    @Test
    public void translateValueStaysTooltipTest(){
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
        ValueStaysTooltip tooltip = Mockito.mock(ValueStaysTooltip.class);
        tooltip.setBgImageUrl("testurl.com");
        tooltip.setTitleText("Tooltip title");
        tooltip.setData(Arrays.asList(tooltip));
        polyglotHelper.translateValueStaysTooltip(tooltip);
    }

    @Test
    public void translateMobgenJsonBOTest() {
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
        Map<String, Map<String, MobgenJsonBO>> mobgenJsonBOMap = new HashMap<>();
        MobgenJsonBO mobgenJsonBO = new MobgenJsonBO();
        mobgenJsonBO.setTitle("title");
        mobgenJsonBO.setNegativeBtnText("neg btn text");
        mobgenJsonBO.setPositiveBtnText("pos button txt");
        mobgenJsonBO.setQuickBook("qck book");
        Map<String, MobgenJsonBO> subMap = new HashMap<>();
        subMap.put("a", mobgenJsonBO);
        mobgenJsonBOMap.put("b", subMap);
        polyglotHelper.translateMobgenJsonBO(mobgenJsonBOMap);
    }

    @Test
    public void translateMobgenStringsBOTest() {
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abc");
        Map<String, MobgenStringsBO> map1 = new HashMap<>();
        MobgenStringsBO mobgenStringsBO = new MobgenStringsBO();
        mobgenStringsBO.setCardMessageFreeFailedBooking("abcd");
        mobgenStringsBO.setCardMessageFreeNonRefundablePendingBooking("abcde");
        map1.put("key", mobgenStringsBO);
        polyglotHelper.translateMobgenStringsBO(map1);
    }

    @Test
    public void translateMyBizAssuredTooltipTest(){
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Rama");
        Mockito.lenient().when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Rama");
        polyglotHelper.translateMyBizAssuredTooltip(new MyBizAssuredToolTip());
    }

    @Test
    public void translateLuxeToolTipTest(){
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Rama");
        Mockito.lenient().when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Rama");
        polyglotHelper.translateLuxeToolTip(new LuxeToolTip());
    }

    @Test
    public void translateHotelCategoryDataMapTest(){
        Map<String, HotelCategoryData> hotelCategoryDataMapNode = new HashMap<>();
        hotelCategoryDataMapNode.put("ID", new HotelCategoryData());
        polyglotHelper.translateHotelCategoryDataMap(hotelCategoryDataMapNode);
    }

    @Test
    public void translateHotelCategoryDataWebMapNewTest(){
        Mockito.lenient().when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Hello");
        polyglotHelper.translateHotelCategoryDataWebMapNew(new HotelCategoryDataWeb());
    }

    @Test
    public void translateConfigNodesFromPolyglotTest() throws JsonProcessingException  {
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("abc");
        JsonNode jsonNode = objectMapper.readTree("{\"bookNowModalData\":{\"viewCount\":3,\"iconType\":\"holdHotelIcon\",\"heading\":\"BOOK_NOW_MODAL_HEADING\",\"subHeading\":\"BOOK_NOW_MODAL_SUB_HEADING\",\"description\":\"BOOK_NOW_MODAL_DESCRIPTION\",\"benefits\":[{\"iconType\":\"tickIconRoundedGreen\",\"text\":\"BOOK_NOW_MODAL_HOTEL_BOOKING\"},{\"iconType\":\"tickIconRoundedGreen\",\"text\":\"BOOK_NOW_MODAL_PAY_BEFORE_CANCELLATION\"},{\"iconType\":\"tickIconRoundedGreen\",\"text\":\"BOOK_NOW_MODAL_AUTO_CANCELLED\"}],\"cta\":\"BOOK_NOW_MODAL_OKAY_GOT_IT\"}}");
        polyglotHelper.translateConfigNodesFromPolyglot(jsonNode);
    }
}
