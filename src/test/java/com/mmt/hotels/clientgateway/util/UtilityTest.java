package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gi.hotels.model.response.staticdata.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.rooms.MmtExclusive;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.RequestIdentifier;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.FullPayment;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.MealPlan;
import com.mmt.hotels.model.response.staticdata.SleepingBedInfo;
import com.mmt.hotels.model.response.staticdata.SleepingDetails;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.SleepingArrangement;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STAY_TIME_HOURS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.TIMESLOT_AM_PM;
import static com.mmt.hotels.clientgateway.util.Utility.*;

import com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement;
import static org.junit.Assert.*;


@RunWith(MockitoJUnitRunner.class)
public class UtilityTest {

    @InjectMocks
    Utility utility;

    @Mock
    PolyglotService polyglotService;

    String expString = "{\"APE\":\"10\",\"PAH\":\"5\",\"PAH5\":\"T\",\"WPAH\":\"F\",\"BNPL\":\"t\",\"MRS\":\"T\",\"PDO\":\"PN\",\"MCUR\":\"T\",\"ADDON\":\"T\",\"CHPC\":\"T\",\"AARI\":\"T\",\"NLP\":\"Y\",\"RCPN\":\"T\",\"PLRS\":\"T\",\"MMRVER\":\"V3\",\"BLACK\":\"T\",\"IAO\":\"T\",\"BNPL0\":\"t\",\"EMIDT\":\"1\",\"NEWTY\":\"T\",\"AIP\":\"T\",\"TFT\":\"T\",\"GEC\":\"A\",\"SRRP\":\"T\",\"new_user_pf_ih\":\"0\",\"streaks_video\":\"false\",\"appUpgrade\":\"false\",\"charity_preselect\":\"2\",\"dweb_transit_dh\":\"true\",\"details_budget\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"overViewQuickBook\\\",\\\"goTribeoffer\\\",\\\"quickBook\\\",\\\"featuredReviews\\\",\\\"hostCell\\\",\\\"exploreInfoCard\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"preferredBy\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"villaRule\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\",\\\"similarHotels\\\"]\",\"new_user_review\":\"2\",\"new_user_comm\":\"2\",\"mweb_truecaller_login_hotels\":\"0\",\"details_soldout\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"goTribeoffer\\\",\\\"changeDates\\\",\\\"alternateDates\\\",\\\"similarHotels\\\",\\\"featuredReviews\\\",\\\"hostCell\\\",\\\"exploreInfoCard\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"preferredBy\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"villaRule\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\"]\",\"upsell_dweb\":\"false\",\"h_unif_autos\":\"0\",\"hermes_hero_image\":\"1\",\"aaqb\":\"1\",\"bank_deal_dweb\":\"1\",\"Hotels_charity_show\":\"0\",\"promocode_pwa\":\"false\",\"augur_test_defaultonly\":\"{api=default, config=default}\",\"details_altacco\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"featuredReviews\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"goTribeoffer\\\",\\\"quickBook\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"overViewQuickBook\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"hostCell\\\",\\\"villaRule\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\",\\\"similarHotels\\\"]\",\"Mw2a_hotels_bottomsheet_typage\":\"0\",\"regionAutosuggestResults\":\"false\",\"dh_newtheme_BE\":\"false\",\"centralized_cache\":\"1\",\"streas_video\":\"{streaksvideo=false}\",\"bnplZeroVariant\":\"true\",\"autosuggest_mweb\":\"false\",\"thanku_revamp\":\"true\",\"aaproperty_config\":\"1\",\"valueOverview\":\"true\",\"Autosuggest_exp\":\"0\",\"pwa_login\":\"false\",\"hermes_bnpl_checkin\":\"1\",\"srp_revamp_be\":\"true\",\"external_rating\":\"1\",\"cpPreApply\":\"true\",\"gsdet\":\"false\",\"price_filter_config\":\"false\",\"dh_newtheme\":\"true\",\"upsell_mweb\":\"false\",\"hotels_charity_amount\":\"0\",\"dweb_new_payment\":\"false\",\"srp_cleanup\":\"false\",\"augur_shradha_testdefault\":\"{api=interspersion, config=default}\",\"qb_1room\":\"1\",\"autosuggestrating_dweb\":\"true\",\"autosuggest_exp1\":\"expscore1\",\"aastar\":\"1\",\"food_dt\":\"false\",\"dweb_login\":\"false\",\"enableMergedPropertyType\":\"true\",\"multiroom_dweb\":\"1\",\"dweb_videos\":\"false\",\"hotel_performance_matrix\":\"false\",\"new_bookAt1\":\"false\",\"details_v2_budget\":\"[\\\"headerCard\\\",\\\"paxInfo\\\",\\\"quickBook\\\",\\\"singleRatePlan\\\",\\\"govtPolicy\\\",\\\"indianness\\\",\\\"goStays\\\",\\\"offers\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"aboutHotel\\\",\\\"villaRule\\\",\\\"location\\\",\\\"inclusiveRate\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"similarHotels\\\"]\",\"near_me_exp\":\"3\",\"uni_flow\":\"1\",\"mWebHotelsPrefillDates\":\"1\",\"food_pwa\":\"false\",\"streaksvideo\":\"false\",\"bnpl_login_persuasion\":\"0\",\"Meta_skipRS\":\"1\",\"dweb_details_unif\":\"false\",\"pwa_recent_search\":\"0\",\"ShowFoodMenu\":\"false\",\"mwebNewLoginWidget\":\"false\",\"augur_test_shradha_l2r\":\"{api=l2r_rt, config=default, dsConfig={algoId=gi_l2r_v2, version=v2}}\",\"upsell_review\":\"1\",\"dateless_dweb\":\"0\",\"hotjar_mweb\":\"0\",\"test_SIBI_AA\":\"false\",\"qna_exp_mweb\":\"0\",\"pwa_video\":\"false\",\"taxExperiment\":\"taxamt\",\"details_v2_altacco\":\"[\\\"headerCard\\\",\\\"paxInfo\\\",\\\"quickBook\\\",\\\"singleRatePlan\\\",\\\"govtPolicy\\\",\\\"goStays\\\",\\\"offers\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"inclusiveRate\\\",\\\"aboutHotel\\\",\\\"location\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"villaRule\\\",\\\"similarHotels\\\"]\",\"alternate_dweb\":\"false\",\"autosuggest_dweb\":\"false\",\"hourD0\":\"14\",\"maldives_city_guide\":\"1\",\"new_to_dh\":\"false\",\"hourly_mweb2app_pwa\":\"0\",\"dweb_transit_ih\":\"true\",\"dspersv1_newprice\":\"false\",\"dateless_pwa\":\"0\",\"hourly_funnel_pwa\":\"0\",\"explicit_intent_new_user\":\"2\",\"DH_Price_Filter\":\"1\",\"transfer_exp_Intl\":\"2\",\"single_poi\":\"false\",\"h_unif_autos_mweb\":\"0\",\"be_upsell_review_hotel\":\"1\",\"regionAutosuggestResultsDweb\":\"false\",\"details_v2_soldout\":\"[\\\"headerCard\\\",\\\"changeDates\\\",\\\"similarHotels\\\",\\\"goStays”,”offers\\\",\\\"indianness\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"aboutHotel\\\",\\\"villaRule\\\",\\\"location\\\",\\\"inclusiveRate\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"govtPolicy\\\"]\",\"similarprop\":\"1\",\"budget_gostay\":\"2\",\"APEINTL\":\"36\",\"unificationDetailV2Dummy\":\"false\",\"BPG\":\"1\",\"IH_Price_Filter\":\"2\",\"dhqb\":\"1\",\"Prefillfilter\":\"0\",\"Explicit_Intent_IH\":\"false\",\"showChildBed\":\"2\",\"pflow\":\"b\",\"checkInD0\":\"true\",\"new_user_coachmarks\":\"2\",\"book_at_1_user_restriction\":\"true\",\"test_augur\":\"0\",\"details_v2_prem\":\"[\\\"headerCard\\\",\\\"paxInfo\\\",\\\"quickBook\\\",\\\"singleRatePlan\\\",\\\"govtPolicy\\\",\\\"goStays\\\",\\\"offers\\\",\\\"indianness\\\",\\\"sleepingArrangement\\\",\\\"hostCell\\\",\\\"aboutHotel\\\",\\\"villaRule\\\",\\\"location\\\",\\\"inclusiveRate\\\",\\\"ratingAndReviews\\\",\\\"externalRatingAndReview\\\",\\\"qnaCard\\\",\\\"similarHotels\\\"]\",\"showIndiannness\":\"true\",\"bank_deal_pwa\":\"1\",\"Hourly_intro_banner\":\"true\",\"dweb_transit\":\"false\",\"forceUpgrade\":\"false\",\"unificationDetailV2\":\"true\",\"bnplNewVariant\":\"false\",\"mwebNewPaymentsPage\":\"0\",\"bnpl_v2\":\"2\",\"streaks_hermes\":\"1\",\"hourly_hotels_funnel\":\"true\",\"web_autosuggest\":\"false\",\"Hourly_timesheet\":\"1\",\"multiroom_mweb\":\"1\",\"mweb_details_unif\":\"false\",\"Couple_Friendly_Intent\":\"true\",\"quickBook\":\"false\",\"details_prem\":\"[\\\"goStays\\\",\\\"headerCard\\\",\\\"overViewQuickBook\\\",\\\"goTribeoffer\\\",\\\"quickBook\\\",\\\"hostCell\\\",\\\"exploreInfoCard\\\",\\\"smartEngage\\\",\\\"safetyAndHygiene\\\",\\\"preferredBy\\\",\\\"book1\\\",\\\"freeCancellation\\\",\\\"indianness\\\",\\\"inclusiveRate\\\",\\\"villaRule\\\",\\\"mapPoiHd\\\",\\\"amenities\\\",\\\"fnd\\\",\\\"importantInfo\\\",\\\"sleepingArragement\\\",\\\"featuredReviews\\\",\\\"externalRatingAndReview\\\",\\\"ratingAndReview\\\",\\\"likeDislike\\\",\\\"govtPolicy\\\",\\\"qnaCard\\\",\\\"propertyPolicyCheckInTime\\\",\\\"shareCard\\\",\\\"similarHotels\\\"]\",\"autosuggestrating_mweb\":\"true\",\"dwebPrefilldate\":\"false\",\"featured_reviews\":\"false\",\"thanku_revamp2\":\"false\",\"thanku_revamp1\":\"false\",\"ALC\":\"F\",\"gocashPreApply\":\"true\"}";
    private Map<String, String> expDataMap = null;
    @Before
    public void init(){
        String bedTypes = "King Bed,Queen Bed,Double Bed,Twin Bed,Single Bed,Standard Bed,Bunk Bed,Futon,Sofa Bed,Sofa cum bed,Mattress,2 Seater Sofa,3 Seater Sofa,5 Seater Sofa";
        List<String> bedTypePriorityOrder = Arrays.asList(bedTypes.split(","));
        ReflectionTestUtils.setField(utility,"bedTypePriorityOrder", bedTypePriorityOrder);
        expDataMap = buildExpData();
    }

    private Map<String, String> buildExpData() {
        Map<String, String> expDataAllMap = null;
        if (StringUtils.isNotBlank(expString)) {
            try {
                Type type = new TypeToken<Map<String, String>>() {
                }.getType();
                expString = expString.replaceAll("^\"|\"$", "");
                expDataAllMap = new Gson().fromJson(expString, type);
            } catch (Exception e) {
            }
        }
        return expDataAllMap;
    }

    @Test
    public void isValidAppVersionTest(){
        String appVersion="8.5.9";
        String allowedMinAppVersion="8.5.8";
        Assert.assertTrue(utility.isValidAppVersion(appVersion,allowedMinAppVersion));
        appVersion="8.5.6";
        allowedMinAppVersion="8.5.8";
        Assert.assertFalse(utility.isValidAppVersion(appVersion,allowedMinAppVersion));
        appVersion="8.5.8";
        allowedMinAppVersion="*******";
        Assert.assertFalse(utility.isValidAppVersion(appVersion,allowedMinAppVersion));

    }

    @Test
    public void isExperimentOnTest(){
        Map<String, String> expData=new HashMap<>();
        expData.put("exp1","t");
        Assert.assertTrue(utility.isExperimentOn(expData,"exp1"));
        Assert.assertFalse(utility.isExperimentOn(expData,"exp2"));


    }

    @Test
    public void buildToolTipTest(){
        Assert.assertTrue(utility.buildToolTip("HOMESTAY"));
        Assert.assertFalse(utility.buildToolTip("HOTEL"));
    }

    @Test
    public void shouldDisplayOfferDiscountBreakupTest(){
        Assert.assertFalse(utility.shouldDisplayOfferDiscountBreakup(null));
        Assert.assertFalse(utility.shouldDisplayOfferDiscountBreakup(expDataMap));
        expDataMap.put("LSOF", "t");
        Assert.assertTrue(utility.shouldDisplayOfferDiscountBreakup(expDataMap));
    }

    @Test
    public void isDetailPageAPITest(){
        Assert.assertFalse(utility.isDetailPageAPI(""));
        Assert.assertTrue(utility.isDetailPageAPI("cg/search-rooms/"));
        Assert.assertFalse(utility.isDetailPageAPI("cg/searcoms/"));
    }

    @Test
    public void isReviewPageAPITest(){
        Assert.assertFalse(utility.isReviewPageAPI(""));
        Assert.assertTrue(utility.isReviewPageAPI("cg/avail-rooms/"));
        Assert.assertFalse(utility.isReviewPageAPI("cg/searcoms/"));
    }

    @Test
    public void getComboNameTest(){
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("dsfee");
        Assert.assertNull(utility.getComboName(null));
        Assert.assertNotNull(utility.getComboName("AI"));
    }

    @Test
    public void testGetGuestRoomKeyValue_WithHotelAndEntire() {
        // Given
        Map<String, Integer> roomBedCountMap = new HashMap<>();
        roomBedCountMap.put("1", 1);
        roomBedCountMap.put("2", 2);
        
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.GUESTS)).thenReturn("guest");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ENTIRE)).thenReturn("entire");
        
        // When
        Tuple<String, String> result = utility.getGuestRoomKeyValue(roomBedCountMap, "HOTEL", "entire");
        
        // Then
        assertNotNull(result);
        assertEquals("guest", result.getX());
    }



    @Test
    public void testGetGuestRoomKeyValue_WithServiceApartment() {
        // Given
        Map<String, Integer> roomBedCountMap = new HashMap<>();
        roomBedCountMap.put("1", 1);
        roomBedCountMap.put("2", 2);
        
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.GUESTS)).thenReturn("guest");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ENTIRE)).thenReturn("entire");
        
        // When
        Tuple<String, String> result = utility.getGuestRoomKeyValue(roomBedCountMap, "HOTEL", "entire", true, 2);
        
        // Then
        assertNotNull(result);
        assertEquals("guest", result.getX());
        assertEquals("entire HOTELs", result.getY());
    }



    @Test
    public void testGetGuestRoomKeyValue_WithNullMap() {

        Tuple<String, String> result = utility.getGuestRoomKeyValue(null, "HOTEL", "entire");
        
        // Then
        assertNotNull(result);
        assertEquals(null, result.getX());
    }

    @Test
    public void testGetGuestRoomKeyValue_WithHomestay() {
        // Given
        Map<String, Integer> roomBedCountMap = new HashMap<>();
        roomBedCountMap.put("1", 1);
        roomBedCountMap.put("2", 2);
        
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.GUESTS)).thenReturn("guest");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ENTIRE)).thenReturn("entire");
        
        // When
        Tuple<String, String> result = utility.getGuestRoomKeyValue(roomBedCountMap, "HOMESTAY", "entire");
        
        // Then
        assertNotNull(result);
        assertEquals("guest", result.getX());
        assertEquals("entire HOMESTAY", result.getY());
    }

    @Test
    public void testGetGuestRoomKeyValue_WithVilla() {
        // Given
        Map<String, Integer> roomBedCountMap = new HashMap<>();
        roomBedCountMap.put("1", 1);
        roomBedCountMap.put("2", 2);
        
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.GUESTS)).thenReturn("guest");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ENTIRE)).thenReturn("entire");
        
        // When
        Tuple<String, String> result = utility.getGuestRoomKeyValue(roomBedCountMap, "VILLA", "entire");
        
        // Then
        assertNotNull(result);
        assertEquals("guest", result.getX());
        assertEquals("entire VILLA", result.getY());
    }

    @Test
    public void testGetGuestRoomKeyValue_WithMultipleRooms() {
        // Given
        Map<String, Integer> roomBedCountMap = new HashMap<>();
        roomBedCountMap.put("1", 1);
        roomBedCountMap.put("2", 2);
        roomBedCountMap.put("3", 3);
        
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.GUESTS)).thenReturn("guest");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ENTIRE)).thenReturn("entire");
        
        // When
        Tuple<String, String> result = utility.getGuestRoomKeyValue(roomBedCountMap, "HOTEL", "entire");
        
        // Then
        assertNotNull(result);
        assertEquals("guest", result.getX());
    }


    @Test
    public void transformCancellationPolicyTest(){
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
        List<CancelPenalty> cancelPenaltyList=new ArrayList<>();
        CancelPenalty cancelPenalty=new CancelPenalty();
        cancelPenalty.setCancellationType(CancelPenalty.CancellationType.FREE_CANCELLATON);
        cancelPenalty.setFreeCancellationText("Free Cancellation");
        cancelPenaltyList.add(cancelPenalty);
        Assert.assertNotNull(utility.transformCancellationPolicy(cancelPenaltyList,true, null, null, null, null, null));
        cancelPenalty.setCancellationType(null);
        Assert.assertNotNull(utility.transformCancellationPolicy(cancelPenaltyList,true, null, null, null, null, null));
    }

    @Test
    public void transformInclusionsTest(){
//        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
        List<MealPlan> mealPlanList=new ArrayList<>();
        MealPlan mealPlan=new MealPlan();
        mealPlan.setCode("EP");
        mealPlan.setValue("Value");
        mealPlanList.add(mealPlan);
        Map<String,String> mealPlanMap=new HashMap<>();
        mealPlanMap.put("CP","Dinner");
        Assert.assertNotNull(utility.transformInclusions(mealPlanList,null,mealPlanMap,"DERBY",null,null, MapUtils.EMPTY_SORTED_MAP,null,null,null,null, null, 0));
        mealPlan.setCode("CP");
        Assert.assertNotNull(utility.transformInclusions(mealPlanList,null,mealPlanMap,"DERBY",null, null, MapUtils.EMPTY_SORTED_MAP,null,null,null,null, null, 0));

        utility.getCountDownLatch();
        List<Inclusion> inclusionList=new ArrayList<>();
        Inclusion inclusion=new Inclusion();
        inclusion.setCode("");
        inclusion.setValue("Free Breakfast");
        inclusionList.add(inclusion);
        Assert.assertNotNull(utility.transformInclusions(mealPlanList,inclusionList,mealPlanMap,"DERBY",null,null, MapUtils.EMPTY_SORTED_MAP,null,null,null,null, null, 0));

    }

    @Test
    public void testBuildMmtExclusiveNode(){
        List<Inclusion> inclusionsList = new ArrayList<>();
        Inclusion e = new Inclusion();
        e.setCode("Free Parking");
        inclusionsList.add(e);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Best available rate");
        MmtExclusive mmtExclusive = utility.buildMmtExclusiveNode(inclusionsList);
        MmtExclusive result = new MmtExclusive();
        List<String> inclusion = new ArrayList<>();
        inclusion.add("Best available rate");
        inclusion.add("Free Parking");
        result.setInclusions(inclusion);
        Assert.assertEquals(result.getInclusions(),mmtExclusive.getInclusions());
    }

    @Test
    public void buildSlotTest(){
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        Slot slot = new Slot();
        slot.setDuration(3);
        slot.setTimeSlot(10);
        searchHotelsCriteria.setSlot(slot);
        utility.buildSlot( searchWrapperInputRequest,searchHotelsCriteria);
        Assert.assertEquals(3,searchWrapperInputRequest.getSlot().getDuration().intValue());
    }

    @Test
    public void addFunnelSourceToParameterMapTest(){
        Map<String, String[]> resultMap = new HashMap<>();
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        priceByHotelsRequestBody.setFunnelSource("DAYUSE");
        resultMap = utility.addFunnelSourceToParameterMap( "DAYUSE", getParameterMap(false), new LinkedHashMap<>());
        Assert.assertEquals("DAYUSE",resultMap.get("funnelSource")[0]);
    }

    private Map<String, String[]> getParameterMap(boolean authCodePresent) {
        Map<String, String[]> paramaterMap = new HashMap<>();
        paramaterMap.put("correlationKey", new String[]{"6123da6f-aa49-4feb-ac2f-71b260b9e9a9"});
        if (authCodePresent) {
            paramaterMap.put("authCode", new String[]{"2J08wUWpHH9rrwL%2BCrbU3rvarwAC8fopWY5ooWq6tzdcyCS6qMlrghl%2B1cRZgBqOxpO3e2NOnkm%2Fj13JitRQVg" +
                    "%3D%3D"});
        }
        paramaterMap.put("srcClient", new String[]{"DESKTOP"});
        paramaterMap.put("language", new String[]{"eng"});
        paramaterMap.put("region", new String[]{"in"});
        paramaterMap.put("currency", new String[]{"INR"});
        paramaterMap.put("idContext", new String[]{"B2C"});
        paramaterMap.put("countryCode", null);
        return paramaterMap;
    }

    @Test
    public void testCalculateTimeSlot_Meridiem(){
        com.mmt.hotels.model.response.dayuse.Slot slot = new com.mmt.hotels.model.response.dayuse.Slot();
        slot.setDuration(3);
        slot.setTimeSlot("10");
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("10 AM - 1 PM");
        String result = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("10 AM - 1 PM",result);
    }

    @Test
    public void testTransformLateCheckout(){
        Map<String, DayUsePersuasion> map = new HashMap<>();
        DayUsePersuasion dayUsePersuasion = new DayUsePersuasion();
        dayUsePersuasion.setText("<font color=\\\"#955000\\\">Late checkout is not allowed for this booking</font>");
        map.put("CHECKOUT_MSG", dayUsePersuasion);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("<font color=\\\"#955000\\\">Late checkout is not allowed for this booking</font>");
        utility.transformLateCheckout(map,new ArrayList<>());
    }

    @Test
    public void testThankYouCalculateTimeSlot_Meridiem(){
        com.mmt.hotels.model.request.dayuse.Slot slot = new com.mmt.hotels.model.request.dayuse.Slot();
        slot.setDuration(3);
        slot.setTimeSlot(10);
        Mockito.when(polyglotService.getTranslatedData(TIMESLOT_AM_PM)).thenReturn("10 AM - 1 PM");
        Mockito.when(polyglotService.getTranslatedData(STAY_TIME_HOURS)).thenReturn("HOURS");
        String result = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("10 AM - 1 PM (3 HOURS)",result);
    }
    @Test
    public void testIsCorpBudgetHotelFunnel(){
        String funnelSource = "CORPBUDGET";
        Assert.assertTrue(utility.isCorpBudgetHotelFunnel(funnelSource));

        funnelSource = "";
        Assert.assertFalse(utility.isCorpBudgetHotelFunnel(funnelSource));

        funnelSource = null;
        Assert.assertFalse(utility.isCorpBudgetHotelFunnel(funnelSource));
    }

    public void buildSlot(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, StaticDetailCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            com.mmt.hotels.model.request.dayuse.Slot slot = new com.mmt.hotels.model.request.dayuse.Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            hotelDetailsMobRequestBody.setSlot(slot);
        }
    }

    @Test
    public void testBuildSlot(){
        HotelDetailsMobRequestBody hotelDetailsMobRequestBody = new HotelDetailsMobRequestBody();
        StaticDetailCriteria searchCriteria = new StaticDetailCriteria();
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        Slot slot = new Slot();
        slot.setDuration(3);
        slot.setTimeSlot(10);
        searchCriteria.setSlot(slot);
        utility.buildSlot(hotelDetailsMobRequestBody, searchCriteria);
        utility.buildSlot(priceByHotelsRequestBody,searchCriteria);
    }

    @Test
    public void getCheckinAndCheckoutForDailyUseTest(){
        ReflectionTestUtils.invokeMethod(utility,"getCheckinAndCheckoutForDailyUse","Test");
    }

    @Test
    public void setAdvancePurchaseTest(){
        ReflectionTestUtils.invokeMethod(utility,"setAdvancePurchase","2022-10-25");
    }

    @Test
    public void setLengthOfStayTest(){
        ReflectionTestUtils.invokeMethod(utility,"setLengthOfStay","2022-10-21","2022-10-25");
    }

    @Test
    public void setAdultCountTest(){
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> ageList = new ArrayList<>();
        ageList.add(2);
        roomStayCandidate.setChildAges(ageList);
        List<RoomStayCandidate> list = new ArrayList();
        list.add(roomStayCandidate);
        ReflectionTestUtils.invokeMethod(utility,"setAdultCount",list);
        ReflectionTestUtils.invokeMethod(utility,"setChildCount",list);
    }

    @Test
    public void getTotalChildrenFromRequestTest(){
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> ageList = new ArrayList<>();
        ageList.add(2);
        roomStayCandidate.setChildAges(ageList);
        List<RoomStayCandidate> list = new ArrayList();
        list.add(roomStayCandidate);
        ReflectionTestUtils.invokeMethod(utility,"getTotalChildrenFromRequest",list);
    }

    @Test
    public void getTotalAdultsFromRequestTest(){
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<RoomStayCandidate> list = new ArrayList();
        list.add(roomStayCandidate);
        ReflectionTestUtils.invokeMethod(utility,"getTotalAdultsFromRequest",list);
    }

    @Test
    public void setLoggingParametersToMDCTest(){
        List<RoomStayCandidate> list = new ArrayList<>();
        list.add(new RoomStayCandidate());
        utility.setLoggingParametersToMDC(list,"2022-10-21","2022-10-25");
    }

    @Test
    public void getGroupPriceTextOrSavingPercKey_Test() {
        String resp = Utility.getGroupPriceTextOrSavingPercKey("Hello", "DESKTOP");
        Assert.assertEquals(resp, "Hello_DESKTOP");
    }

    @Test
    public void getGroupPriceTextOrSavingPercKey_Test_client_non_desktop() {
        String resp = Utility.getGroupPriceTextOrSavingPercKey("Hello", "ANDROID");
        Assert.assertEquals(resp, "Hello");
    }

    @Test
    public void appendPageContextToUrlTest() {
        String url = null;
        String pageContext = null;

        String resp = Utility.appendPageContextToURL(url, pageContext);
        Assert.assertEquals(null, resp);

        url = "url";
        resp = Utility.appendPageContextToURL(url, pageContext);
        Assert.assertEquals("url", resp);

        pageContext = "DETAIL";
        resp = Utility.appendPageContextToURL(url, pageContext);
        Assert.assertEquals("url&pageContext=DETAIL", resp);
    }

    public void getPreferredOTAFromExpTest() throws JsonProcessingException {
        ReflectionTestUtils.setField(utility, "mmtRatingsCountThreshold", 50);
        OTA preferredOta = utility.getPreferredOtaFromExp(null, false);
        Assert.assertEquals(OTA.TA, preferredOta);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(
                "{\"cumulativeRating\":4.5,\"totalReviewsCount\":226,\"totalRatingCount\":50}");
        HashMap<OTA,JsonNode> webApiMap = new HashMap<>();
        webApiMap.put(OTA.MMT, jsonNode);
        preferredOta = utility.getPreferredOtaFromExp(webApiMap, false);
        Assert.assertEquals(OTA.TA, preferredOta);
        preferredOta = utility.getPreferredOtaFromExp(webApiMap, true);
        Assert.assertEquals(OTA.TA, preferredOta);

        jsonNode = objectMapper.readTree(
                "{\"cumulativeRating\":4.5,\"totalReviewsCount\":226,\"totalRatingCount\":51}");
        webApiMap.put(OTA.MMT, jsonNode);
        preferredOta = utility.getPreferredOtaFromExp(webApiMap, true);
        Assert.assertEquals(OTA.MMT, preferredOta);
    }

    @Test
    public void testCreateBedInfoTextFromBedInfoMap(){
        LinkedHashMap<String, Integer> bedInfoMap  = new LinkedHashMap<>();
        bedInfoMap.put("Double Bed",2);
        bedInfoMap.put("Cot",1);
        Assert.assertEquals("2 Double Beds, 1 Cot",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
        bedInfoMap.put("Queen Bed",4);
        Assert.assertEquals("4 Queen Beds, 2 Double Beds, 1 Cot",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
        bedInfoMap.put("King Bed",3);
        Assert.assertEquals("3 King Beds, 4 Queen Beds, 2 Double Beds, 1 Cot",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
        bedInfoMap.put("Cot",4);
        Assert.assertEquals("3 King Beds, 4 Queen Beds, 2 Double Beds, 4 Cots",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));

    }

    private List<SleepingInfoArrangement>buildSleepingInfoArrangementList(String spaceType){
        List<SleepingInfoArrangement> sleepingInfoArrangementList = new ArrayList<>();
        SleepingInfoArrangement sleepingInfoArrangement = new SleepingInfoArrangement();
        sleepingInfoArrangement.setSubText("Sleeps 3 guests");
        LinkedHashMap<String, Integer> bedInfos = new LinkedHashMap<>();
        bedInfos.put("King Bed",4);
        bedInfos.put("Mattress",3);
        sleepingInfoArrangement.setBedInfos(bedInfos);
        sleepingInfoArrangement.setBed(10);
        sleepingInfoArrangement.setDescriptionText("4 King Bed, 3 Mattress, Garden View, Attached Bathroom, Attached Balcony");
        sleepingInfoArrangement.setGuest(11);
        sleepingInfoArrangement.setOpenCardText("4 King Bed, 3 Mattress, Room service, Air Conditioning, Wifi, Mineral Water, Bathroom, Housekeeping, Laundry Service, Work Desk, Closet, Mini Fridge, Mirror, Hangers, Chair, Center Table, Blackout curtains, Intercom, Flooring, Blanket, Pillows, TV, Western Toilet Seat, Hot & Cold Water, Shower, Toiletries, Towels, Newspaper, Fan, Balcony, Sanitizers");
        sleepingInfoArrangement.setSpaceType(spaceType);
        sleepingInfoArrangement.setMaxCapacity(13);
        sleepingInfoArrangement.setBathRoom(2);
        sleepingInfoArrangement.setBedRoom(3);
        sleepingInfoArrangementList.add(sleepingInfoArrangement);
        return sleepingInfoArrangementList;
    }
    @Test
    public void testpostProcessSleepingInfoArrangement(){
        LinkedHashMap<String, List<SleepingInfoArrangement>> spaceIdToSleepingInfoArr = new LinkedHashMap<>();
        LinkedHashMap<String, SleepingDetails> spaceIdToSleepingDetailsMap = new LinkedHashMap<>();
        spaceIdToSleepingInfoArr.put("1234",buildSleepingInfoArrangementList("bedroom"));
        spaceIdToSleepingInfoArr.put("4567",buildSleepingInfoArrangementList("living_room"));
        SleepingDetails sleepingDetails = new SleepingDetails();
        sleepingDetails.setExtraBedCount(1);
        sleepingDetails.setBedCount(7);
        sleepingDetails.setBedRoomCount(3);
        sleepingDetails.setMinOccupancy(2);
        sleepingDetails.setMaxOccupancy(14);
        sleepingDetails.setExtraBedCount(1);
        List<SleepingBedInfo> bedInfo = new ArrayList<>();
        List<SleepingBedInfo> extraBedInfo = new ArrayList<>();
        SleepingBedInfo sleepingBedInfo1 = new SleepingBedInfo();
        sleepingBedInfo1.setBedType("King Bed");
        sleepingBedInfo1.setBedCount(4);
        SleepingBedInfo sleepingBedInfo2 = new SleepingBedInfo();
        sleepingBedInfo2.setBedType("Mattress");
        sleepingBedInfo2.setBedCount(3);
        bedInfo.add(sleepingBedInfo1);
        extraBedInfo.add(sleepingBedInfo2);
        sleepingDetails.setBedInfo(bedInfo);
        sleepingDetails.setExtraBedInfo(extraBedInfo);
        spaceIdToSleepingDetailsMap.put("1234",sleepingDetails);
        spaceIdToSleepingDetailsMap.put("4567",sleepingDetails);
        List<RoomDetails> roomDetails = new ArrayList<>();
        RoomDetails roomDetails1 = new RoomDetails();
        SpaceData spaceData = new SpaceData();
        List<Space> spaces = new ArrayList<>();
        Space space = new Space();
        space.setSpaceType("bedroom");
        space.setSleepingInfoArrangement(buildSleepingInfoArrangementList("bedroom"));
        space.setSpaceId("1234");
        spaces.add(space);
        spaceData.setSpaces(spaces);
        roomDetails1.setPrivateSpaces(spaceData);
        roomDetails.add(roomDetails1);
        utility.addSleepingInfoArrangementIntoRoomDetails(roomDetails,spaceIdToSleepingInfoArr);
    }

    @Test
    public void testSleepingInfoArrangementOccupancyRoom(){
        List<RoomDetails> roomDetails = new ArrayList<>();
        RoomDetails roomDetails1 = new RoomDetails();
        SpaceData spaceData = new SpaceData();
        List<Space> spaces = new ArrayList<>();
        Space space = new Space();
        space.setSpaceType("bedroom");
        space.setSleepingInfoArrangement(buildSleepingInfoArrangementList("bedroom"));
        space.setSpaceId("1234");
        spaces.add(space);
        spaceData.setSpaces(spaces);
        roomDetails1.setPrivateSpaces(spaceData);
        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed = new SleepingArrangement();
        bed.setType("Queen Bed");
        bed.setCount(2);
        beds.add(bed);
        roomDetails1.setBeds(beds);
        roomDetails.add(roomDetails1);
        utility.addSleepingInfoArrangementIntoOccupancyRooms(roomDetails);
        Assert.assertEquals("2 Queen Beds", roomDetails.get(0).getPrivateSpaces().getSpaces().get(0).getDescriptionText());
    }

    @Test
    public void testBuildBedInfoText(){
        List<Space> spaces = new ArrayList<>();
        Space space = new Space();
        space.setSpaceType("bedroom");
        space.setSleepingInfoArrangement(buildSleepingInfoArrangementList("bedroom"));
        spaces.add(space);
        Assert.assertEquals("4 King Beds, 3 Mattresses",utility.buildBedInfoText(spaces));

    }

    @Test
    public void testBuildRequestIdentifier(){
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setJourneyId("JourneyId123");
        requestDetails.setRequestId("RequestId123");
        requestDetails.setSessionId("SessionId123");
        RequestIdentifier requestIdentifier = utility.buildRequestIdentifier(requestDetails);
        Assert.assertTrue(requestDetails.getJourneyId().equalsIgnoreCase(requestIdentifier.getJourneyId()));
        Assert.assertTrue(requestDetails.getRequestId().equalsIgnoreCase(requestIdentifier.getRequestId()));
        Assert.assertTrue(requestDetails.getSessionId().equalsIgnoreCase(requestIdentifier.getSessionId()));
    }

    @Test
    public void testReplaceText() {

        // Test Case 1
        String regex1 = "\\[(.*?)\\]";
        String inputString1 = "[apple]";
        String replaceString1 = "$1";
        String expectedOutput1 = "apple";
        Assert.assertEquals(expectedOutput1, utility.replaceText(regex1, inputString1, replaceString1));

        // Test Case 2
        String regex2 = "\\[(.*?)\\]";
        String inputString2 = "[123abc456def]";
        String replaceString2 = "$1";
        String expectedOutput2 = "123abc456def";
        Assert.assertEquals(expectedOutput2, utility.replaceText(regex2, inputString2, replaceString2));

        // Test Case 3
        String regex3 = "\\[(.*?)\\]";
        String inputString3 = "Hello      World";
        String replaceString3 = "$1";
        String expectedOutput3 = "Hello      World";
        Assert.assertEquals(expectedOutput3, utility.replaceText(regex3, inputString3, replaceString3));

        // Test Case 4
        String regex4 = "\\[(.*?)\\]";
        String inputString4 = "Ope\"nA\"I";
        String replaceString4 = "$1";
        String expectedOutput4 = "Ope\"nA\"I";
        Assert.assertEquals(expectedOutput4, utility.replaceText(regex4, inputString4, replaceString4));

        // Test Case 5
        String regex5 = "\\[(.*?)\\]";
        String inputString5 = "Hello, World!";
        String replaceString5 = "$1";
        String expectedOutput5 = "Hello, World!";
        Assert.assertEquals(expectedOutput5, utility.replaceText(regex5, inputString5, replaceString5));
    }

    @Test
    public void isBookingDeviceDesktop_test() {
        com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("desktop");
        boolean result = isBookingDeviceDesktop(deviceDetails);
        Assert.assertEquals(result, true);
    }


    @Test
    public void getFreeChildInclusionTest() {

        BookedInclusion bookedInclusion = utility.getFreeChildInclusion("test", "test");
        Assert.assertEquals(bookedInclusion.getText(),"test");

        Assert.assertNull(utility.getFreeChildInclusion("", "test"));
    }

    @Test
    public void buildPagemakerRenderedTemplateUrlTest() throws UnsupportedEncodingException {
        String pageMakerRenderedTemplateUrl = "http://example.com";
        String flavour = "flavour";
        String version = "v1.0";
        String actualUrl = utility.buildPagemakerRenderedTemplateUrl(pageMakerRenderedTemplateUrl, flavour, version);
        String expectedUrl = "http://example.com?template_ids=%5B%22calendar_json%22%2C%22day_use_banner%22%5D&flavour=flavour&versionCode=v1.0";
        Assert.assertEquals(expectedUrl, actualUrl);
    }

    @Test
    public void buildPageMakerChundDataUrlTest() throws UnsupportedEncodingException {
        String url = "http://example.com";
        String vertical = "dayuse";
        String flavour = "flavour";
        String version = "v1.0";
        String actualUrl = utility.buildPageMakerChundDataUrl(url, vertical, flavour, version);
        String expectedUrl = "http://example.com?chunk=home_carousel&vertical=dayuse&flavour=flavour&mode=app&versionCode=v1.0";
        Assert.assertEquals(expectedUrl, actualUrl);
    }

    @Test
    public void buildSmartEngageUrlTest() throws UnsupportedEncodingException {
        String smartEngageUrl = "http://example.com";
        String flavour = "flavour";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("key", "value");
        String version = "v1.0";
        String deviceType = "desktop";
        String siteDomain = "IN";
        String actualUrl = utility.buildSmartEngageUrl(smartEngageUrl, flavour, paramMap, version, deviceType, siteDomain,"LANDING");
        String expectedUrl = "http://example.com?flavour=flavour&context=hotels_home&status=LIVE&lob=dh&slots=%5B%22dwebHotels_info_home%22%2C%22dwebHotels_banner_home%22%5D&params=%7B%22key%22%3A%22value%22%7D&versionCode=v1.0";
        Assert.assertEquals(expectedUrl, actualUrl);
    }

    @Test
    public void testTransformInclusionsForPackageRatePlan() {

        List<BookedInclusion> inclusions = new ArrayList<>();
        BookedInclusion inclusion1 = new BookedInclusion();
        inclusion1.setLeafCategory("category1");
        inclusions.add(inclusion1);

        BookedInclusion inclusion2 = new BookedInclusion();
        inclusion2.setLeafCategory("category2");
        inclusions.add(inclusion2);

        utility.transformInclusionsForPackageRatePlan(inclusions);

        for (BookedInclusion inclusion : inclusions) {
            Assert.assertTrue(inclusion.isPackageBenefit());
            Assert.assertEquals(Constants.INCLUSIONS_PACKAGE_TEXT_COLOUR, inclusion.getTextColor());
        }
    }

    @Test
    public void testHighlightedAmenitiesPushedToTopForDesktop() {
        HotelResult hotelResult = new HotelResult();
        com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB = new com.mmt.hotels.model.response.staticdata.HotelResult();
        GIStaticData giStaticData = new GIStaticData();
        hotelResultCB.setGiStaticData(giStaticData);
        hotelResultCB.setHotelDetailMetaInfo(new HotelDetailMetaInfo());
        Amenities amenities = new Amenities();
        hotelResultCB.getGiStaticData().setAmenities(amenities);

        Categorized categorized1 = new Categorized();
        categorized1.setLabel("Category 1");
        categorized1.setData(Arrays.asList("C1Am1", "C1Am2"));

        Categorized categorized2 = new Categorized();
        categorized2.setLabel("Category 2");
        categorized2.setData(Arrays.asList("C2Am1", "C2Am2"));

        hotelResultCB.getGiStaticData().getAmenities().setCategorized(new ArrayList<>(Arrays.asList(categorized1, categorized2)));
        hotelResult.setHighlightedAmenities(Arrays.asList("HAM1, HAM2"));

        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.DEVICE_OS_DESKTOP);

        utility.updateHotelResultGI(hotelResult, hotelResultCB, deviceDetails);
        Assert.assertEquals(3, hotelResult.getAmenitiesGI().getCategorized().size());
    }

    @Test
    public void testPopularAmenitiesForApps() {
        HotelResult hotelResult = new HotelResult();
        com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB = new com.mmt.hotels.model.response.staticdata.HotelResult();
        GIStaticData giStaticData = new GIStaticData();
        hotelResultCB.setGiStaticData(giStaticData);
        hotelResultCB.setHotelDetailMetaInfo(new HotelDetailMetaInfo());
        Amenities amenities = new Amenities();
        hotelResultCB.getGiStaticData().setAmenities(amenities);

        Transformed rated1 = new Transformed();
        rated1.setName("AM1");
        rated1.setRating(3.6);

        Transformed rated2 = new Transformed();
        rated2.setName("HAM2");
        rated2.setRating(3.8);

        hotelResultCB.getGiStaticData().getAmenities().setRated(new ArrayList<>(Arrays.asList(rated1, rated2)));
        hotelResult.setHighlightedAmenities(Arrays.asList("HAM1, HAM2"));

        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.DEVICE_OS_ANDROID);

        utility.updateHotelResultGI(hotelResult, hotelResultCB, deviceDetails);
        Assert.assertEquals(3, hotelResult.getAmenitiesGI().getPopular().size());
    }

    @Test
    public void buildFullPaymentTest(){
        FullPayment fullPayment = new FullPayment();
        fullPayment.setFullPaymentSubText("test");
        fullPayment.setFullPaymentText("test1");
        fullPayment.setFinalPrice(2.0);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test {0}");
        com.mmt.hotels.clientgateway.response.FullPayment fullPaymentCg = utility.buildFullPayment(fullPayment,10.0,0.0);
        Assert.assertEquals(fullPaymentCg.getPayEntireBnplApplicableText(), "test 2");
        Assert.assertEquals(fullPaymentCg.getPayEntireBnplNotApplicableText(), "test 2");
    }

    @Test
    public void getcompleteURLTest(){
        String result = getcompleteURL("abc",new HashMap<>(),"sdgcfw");
        Assert.assertNotNull(result);
    }

    @Test
    public void getAskedCurrencyTest(){
        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        persistedMultiRoomData.setAvailReqBody(new PriceByHotelsRequestBody());
        persistedMultiRoomData.getAvailReqBody().setCurrency("INR");
        Assert.assertNotNull(Utility.getAskedCurrency(persistedMultiRoomData));
    }

    @Test
    public void buildRoomStayQualifierFromRoomStayCandidatesTest(){
        List<com.mmt.hotels.model.request.RoomStayCandidate> list = new ArrayList<>();
        list.add(new com.mmt.hotels.model.request.RoomStayCandidate());
        list.get(0).setGuestCounts(new ArrayList<>());
        list.get(0).getGuestCounts().add(new GuestCount());
        list.get(0).getGuestCounts().get(0).setCount("2");
        list.get(0).getGuestCounts().get(0).setAges(new ArrayList<>());
        list.get(0).getGuestCounts().get(0).getAges().add(20);
        String result = buildRoomStayQualifierFromRoomStayCandidates(list,true);
        Assert.assertNotNull(result);
    }

    @Test
    public void appendQueryParamsInUrl_withValidParams_shouldAppendParams() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "value1");
        queryParams.put("key2", "value2");
        String url = "http://example.com";
        String expected = "http://example.com?key1=value1&key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withEmptyParams_shouldReturnOriginalUrl() {
        Map<String, String> queryParams = new HashMap<>();
        String url = "http://example.com";
        String expected = "http://example.com";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withNullParams_shouldReturnOriginalUrl() {
        String url = "http://example.com";
        String expected = "http://example.com";
        String result = Utility.appendQueryParamsInUrl(url, null);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withBlankParamKey_shouldSkipBlankParam() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("", "value1");
        queryParams.put("key2", "value2");
        String url = "http://example.com";
        String expected = "http://example.com?key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withBlankParamValue_shouldSkipBlankValue() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "");
        queryParams.put("key2", "value2");
        String url = "http://example.com";
        String expected = "http://example.com?key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withExistingParams_shouldReplaceExistingParams() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "newValue1");
        String url = "http://example.com?key1=value1&key2=value2";
        String expected = "http://example.com?key1=newValue1&key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
    }

    @Test
    public void appendQueryParamsInUrl_withUrlHavingFragment_shouldAppendParamsBeforeFragment() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "value1");
        String url = "http://example.com#section";
        String expected = "http://example.com?key1=value1#section";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testGetCompleteUrl_withValidParameters() {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});
        parameterMap.put("param2", new String[]{"value2"});

        String expectedUrl = "http://example.com?param1=value1&param2=value2";
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withEmptyUrl() {
        String url = "";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});

        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(url, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withNullUrl() {
        String url = null;
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});

        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertNull(resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withEmptyParameterMap() {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();

        String expectedUrl = "http://example.com";
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withProfileAndRegionParameters() {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("profile", new String[]{"profileValue"});
        parameterMap.put("Region", new String[]{"regionValue"});
        parameterMap.put("param1", new String[]{"value1"});

        String expectedUrl = "http://example.com?param1=value1";
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withEncoding() throws UnsupportedEncodingException {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value 1"});
        parameterMap.put("param2", new String[]{"value&2"});

        String expectedUrl = "http://example.com?param1=" + URLEncoder.encode("value 1", "UTF-8") + "&param2=" + URLEncoder.encode("value&2", "UTF-8");
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testBuildPartialRefundDateText() {
        String result = utility.buildPartialRefundDateText(null);
        Assert.assertEquals("", result);
        com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline cancellationPolicyTimeline = new com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline();
        cancellationPolicyTimeline.setEndDate("11 Jul");
        cancellationPolicyTimeline.setEndDateTime("11:59 am");

        CancellationTimeline cancellationTimeline = new CancellationTimeline();
        cancellationTimeline.setCheckInDate("11 Jul");

        List<CancellationPolicyTimeline> cancellationPolicyTimelineList = new ArrayList<>();
        cancellationPolicyTimelineList.add(cancellationPolicyTimeline);
        cancellationTimeline.setCancellationPolicyTimelineList(cancellationPolicyTimelineList);

        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_CHECKIN_TEXT)).thenReturn("Partially refundable till check-in");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT)).thenReturn("Partially refundable before {0} {1}");

        result = utility.buildPartialRefundDateText(cancellationTimeline);
        Assert.assertEquals("Partially refundable till check-in", result);

        cancellationTimeline.setCheckInDate("15 Jul");
        result = utility.buildPartialRefundDateText(cancellationTimeline);
        Assert.assertEquals("Partially refundable before 11 Jul 11:59 am", result);

        cancellationPolicyTimeline.setEndDateTime("");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_TEXT)).thenReturn("Partially refundable");
        result = utility.buildPartialRefundDateText(cancellationTimeline);
        Assert.assertEquals("Partially refundable", result);
    }
    @Test
    public void testReplaceWithFreeCancellation() {
        Utility utility = new Utility();

        // Test case 1: Input contains "with free cancellation"
        String input1 = "Book now with free cancellation";
        String expectedOutput1 = "Book now <s>with free cancellation</s>";
        String actualOutput1 = utility.replaceWithFreeCancellation(input1);
        Assert.assertEquals(expectedOutput1, actualOutput1);

        // Test case 2: Input contains "free cancellation"
        String input2 = "This offer includes free cancellation";
        String expectedOutput2 = "This offer includes <s>free cancellation</s>";
        String actualOutput2 = utility.replaceWithFreeCancellation(input2);
        Assert.assertEquals(expectedOutput2, actualOutput2);

        // Test case 3: Input contains both "with free cancellation" and "free cancellation"
        String input3 = "Book now with free cancellation and enjoy free cancellation";
        String expectedOutput3 = "Book now <s>with free cancellation</s> and enjoy free cancellation";
        String actualOutput3 = utility.replaceWithFreeCancellation(input3);
        Assert.assertEquals(expectedOutput3, actualOutput3);

        // Test case 4: Input does not contain "with free cancellation" or "free cancellation"
        String input4 = "No cancellation fees";
        String expectedOutput4 = "No cancellation fees";
        String actualOutput4 = utility.replaceWithFreeCancellation(input4);
        Assert.assertEquals(expectedOutput4, actualOutput4);


    }

    @Test
    public void calculateTimeSlot_MeridiemTest() {
        Mockito.when(polyglotService.getTranslatedData("TIMESLOT_AM_AM")).thenReturn("{0} AM - {1} AM");
        Mockito.when(polyglotService.getTranslatedData("TIMESLOT_AM_PM")).thenReturn("{0} AM - {1} PM");
        Mockito.when(polyglotService.getTranslatedData("TIMESLOT_PM_AM")).thenReturn("{0} PM - {1} AM");
        Mockito.when(polyglotService.getTranslatedData("TIMESLOT_PM_PM")).thenReturn("{0} PM - {1} PM");
        com.mmt.hotels.model.response.dayuse.Slot slot = new com.mmt.hotels.model.response.dayuse.Slot();
        slot.setDuration(3);
        slot.setTimeSlot("7");
        String timeSlot = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("7 AM - 10 AM", timeSlot);
        slot.setDuration(6);
        slot.setTimeSlot("7");
        timeSlot = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("7 AM - 1 PM", timeSlot);
        slot.setDuration(6);
        slot.setTimeSlot("13");
        timeSlot = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("1 PM - 7 PM", timeSlot);
        slot.setDuration(12);
        slot.setTimeSlot("20");
        timeSlot = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("8 PM - 8 AM", timeSlot);
    }
}
