package com.mmt.hotels.clientgateway.util;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class PersuasionUtilTest {
    @InjectMocks
    PersuasionUtil persuasionUtil;

    @Mock
    PolyglotService polyglotService;

    private Map<String, Map<String, PersuasionData> > hiddenGemPersuasionConfigMap;

    @Before
    public void setup() {
        Gson gson = new Gson();
        String hiddenGemPersuasionConfigString = "{\"Desktop\":{\"hiddenGem\":{\"style\":{\"textColor\":\"#4A4A4A\"}},\"hiddenGemIcon\":{\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/Hidden_Gem/Hidden_Gem.png\",\"style\":{}},\"homeStayTitle\":{\"style\":{\"styleClasses\":[\"htlHighlt__title\"]},\"topLevelStyle\":{\"styleClasses\":[\"htlHighlt\"]}},\"homeStaySubTitle\":{\"style\":{\"styleClasses\":[\"htlHighlt__subTitle\"]},\"topLevelStyle\":{\"styleClasses\":[\"htlHighlt\"]}}},\"Apps\":{\"hiddenGem\":{\"style\":{\"textColor\":\"#4A4A4A\"}},\"hiddenGemIcon\":{\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/Hidden_Gem/Hidden_Gem.png\",\"style\":{}},\"homeStayTitle\":{\"style\":{\"textColor\":\"#0061aa\",\"fontSize\":\"MEDIUM\",\"fontType\":\"B\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}},\"homeStaySubTitle\":{\"style\":{\"textColor\":\"#4a4a4a\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}}},\"PWA\":{\"hiddenGem\":{\"style\":{\"textColor\":\"#4A4A4A\"}},\"hiddenGemIcon\":{\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/Hidden_Gem/Hidden_Gem.png\",\"style\":{}},\"homeStayTitle\":{\"style\":{\"textColor\":\"#0061aa\",\"fontSize\":\"MEDIUM\",\"fontType\":\"B\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}},\"homeStaySubTitle\":{\"style\":{\"textColor\":\"#4a4a4a\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}}}}";
        hiddenGemPersuasionConfigMap = gson.fromJson(hiddenGemPersuasionConfigString, new TypeToken<Map<String, Map<String, PersuasionData>>>(){
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "hiddenGemPersuasionConfigMap", hiddenGemPersuasionConfigMap);
    }

    @Test
    public void buildHiddenGemPersuasion() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHiddenGemPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion");
        persuasionObject = persuasionUtil.buildHiddenGemPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);
    }

    @Test
    public void buildHiddenGemIconPersuasion() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHiddenGemIconPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        searchWrapperHotelEntity.setHiddenGem(true);
        persuasionObject = persuasionUtil.buildHiddenGemIconPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);
    }


    @Test
    public void buildHomeStaysTitlePersuasion() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        HomeStayDetails homeStayDetails = new HomeStayDetails();
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        homeStayDetails.setStayType(Constants.STAY_TYPE_HOTEL);
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);

        searchWrapperHotelEntity.setLastBooked(true);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);
    }

    @Test
    public void buildHomeStaysSubTitlePersuasion() {

        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHomeStaysSubTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        HomeStayDetails homeStayDetails = new HomeStayDetails();
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysSubTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        homeStayDetails.setStayTypeInfo("Home Stay Sub Title");
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysSubTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);
    }
}
