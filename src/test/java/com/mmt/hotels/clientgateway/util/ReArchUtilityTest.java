package com.mmt.hotels.clientgateway.util;

import com.gi.hotels.model.response.staticdata.Amenities;
import com.gi.hotels.model.response.staticdata.Categorized;
import com.gi.hotels.model.response.staticdata.CategorizedV2;
import com.gi.hotels.model.response.staticdata.Transformed;
import com.gommt.hotels.orchestrator.detail.enums.PriceVariationType;
import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.content.LocationInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SleepingDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.response.HighlightedAmenity;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ReArchUtilityTest {

    @InjectMocks
    private ReArchUtility reArchUtility;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void setup() {
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES)).thenReturn("Star Facilities");
        when(polyglotService.getTranslatedData("SPACE_OCCUPANCY_TEXT")).thenReturn("Accommodates {{OCCUPANCY}} guests");
        when(polyglotService.getTranslatedData("SPACE_SINGLE_OCCUPANCY_TEXT")).thenReturn("Accommodates {{OCCUPANCY}} guest");
    }

    // Test buildSharedInfo method
    @Test
    public void testBuildSharedInfo_withValidDisplayItem_returnsSharedInfo() {
        DisplayItem displayItem = new DisplayItem();
        displayItem.setIconUrl("http://example.com/icon.png");
        displayItem.setText("Test Info Text");

        SharedInfo result = reArchUtility.buildSharedInfo(displayItem);

        assertNotNull(result);
        assertEquals("http://example.com/icon.png", result.getIconUrl());
        assertEquals("Test Info Text", result.getInfoText());
    }

    @Test
    public void testBuildSharedInfo_withNullDisplayItem_returnsNull() {
        SharedInfo result = reArchUtility.buildSharedInfo(null);
        assertNull(result);
    }

    @Test
    public void testBuildSharedInfo_withEmptyDisplayItem_returnsSharedInfoWithNullValues() {
        DisplayItem displayItem = new DisplayItem();

        SharedInfo result = reArchUtility.buildSharedInfo(displayItem);

        assertNotNull(result);
        assertNull(result.getIconUrl());
        assertNull(result.getInfoText());
    }

    // Test buildSpaceInclusion method
    @Test
    public void testBuildSpaceInclusion_withValidSpace_returnsSpaceInclusionList() {
        Space space = createSpaceWithBedInfo();

        List<String> result = reArchUtility.buildSpaceInclusion(space);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.get(0).contains("King"));
    }

    @Test
    public void testBuildSpaceInclusion_withNullSpace_returnsNull() {
        List<String> result = reArchUtility.buildSpaceInclusion(null);
        assertNull(result);
    }

    @Test
    public void testBuildSpaceInclusion_withSpaceWithExtraBeds_includesExtraBedInfo() {
        Space space = createSpaceWithExtraBeds();

        List<String> result = reArchUtility.buildSpaceInclusion(space);

        assertNotNull(result);
        assertTrue(result.get(0).contains("extra"));
        assertTrue(result.get(0).contains("available"));
    }

    @Test
    public void testBuildSpaceInclusion_withSpaceWithDescriptionAndBeds_combinesBoth() {
        Space space = createSpaceWithBedInfoAndDescription();

        List<String> result = reArchUtility.buildSpaceInclusion(space);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.get(0).contains("King"));
        assertEquals(" Test Description", result.get(1)); // Includes space from pipe separator processing
    }

    @Test
    public void testBuildSpaceInclusion_withSpaceWithOnlyDescription_returnsDescription() {
        Space space = new Space();
        space.setDescriptionText("Only Description");

        List<String> result = reArchUtility.buildSpaceInclusion(space);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Only Description", result.get(0));
    }

    @Test
    public void testBuildSpaceInclusion_withSpaceWithNullSleepingDetails_handlesGracefully() {
        Space space = new Space();
        space.setDescriptionText("Test Description");

        List<String> result = reArchUtility.buildSpaceInclusion(space);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test Description", result.get(0));
    }

    // Test getSpaceDataV2 method
    @Test
    public void testGetSpaceDataV2_withValidSpaceData_returnsSpaceData() {
        SpaceData spaceData = createValidSpaceData();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, false, spaceDataList);

        assertNotNull(result);
        assertNotNull(result.getSpaces());
        assertFalse(result.getSpaces().isEmpty());
        assertEquals(1, spaceDataList.size());
    }

    @Test
    public void testGetSpaceDataV2_withNullSpaceData_returnsNull() {
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(null, false, spaceDataList);

        assertNull(result);
    }

    @Test
    public void testGetSpaceDataV2_withPrivateSpace_handlesPrivateSpaceLogic() {
        SpaceData spaceData = createValidSpaceData();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, true, spaceDataList);

        assertNotNull(result);
        // Private space logic should set areaText to null
        assertNull(result.getSpaces().get(0).getAreaText());
    }

    @Test
    public void testGetSpaceDataV2_withBedroomSpace_setsOccupancyText() {
        SpaceData spaceData = createSpaceDataWithBedroom();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, false, spaceDataList);

        assertNotNull(result);
        assertNotNull(result.getSpaces().get(0).getSubText());
        assertTrue(result.getSpaces().get(0).getSubText().contains("guest"));
    }

    @Test
    public void testGetSpaceDataV2_withLivingRoomSpace_setsOccupancyText() {
        SpaceData spaceData = createSpaceDataWithLivingRoom();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, false, spaceDataList);

        assertNotNull(result);
        assertNotNull(result.getSpaces().get(0).getSubText());
    }

    @Test
    public void testGetSpaceDataV2_withSpaceWithMedia_includesMediaData() {
        SpaceData spaceData = createSpaceDataWithMedia();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, false, spaceDataList);

        assertNotNull(result);
        assertNotNull(result.getSpaces().get(0).getMedia());
        assertFalse(result.getSpaces().get(0).getMedia().isEmpty());
    }

    // Test buildRuleTableInfo static method
    @Test
    public void testBuildRuleTableInfo_withValidRuleTableInfo_returnsRuleTableInfo() {
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo input = 
            createValidRuleTableInfo();

        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo result = 
            ReArchUtility.buildRuleTableInfo(input);

        assertNotNull(result);
        assertEquals("Key Title", result.getKeyTitle());
        assertEquals("Value Title", result.getValueTitle());
        assertNotNull(result.getInfoList());
        assertEquals(2, result.getInfoList().size());
    }

    @Test
    public void testBuildRuleTableInfo_withNullInput_returnsNull() {
        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo result = 
            ReArchUtility.buildRuleTableInfo(null);

        assertNull(result);
    }

    @Test
    public void testBuildRuleTableInfo_withEmptyInfoList_returnsNull() {
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo input = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo();
        input.setInfoList(new ArrayList<>());

        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo result = 
            ReArchUtility.buildRuleTableInfo(input);

        assertNull(result);
    }

    @Test
    public void testBuildRuleTableInfo_withNullInfoList_returnsNull() {
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo input = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo();
        input.setInfoList(null);

        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo result = 
            ReArchUtility.buildRuleTableInfo(input);

        assertNull(result);
    }

    @Test
    public void testBuildRuleTableInfo_withBlankTitles_replacesWithEmptyString() {
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo input = 
            createRuleTableInfoWithBlankTitles();

        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo result = 
            ReArchUtility.buildRuleTableInfo(input);

        assertNotNull(result);
        assertEquals("", result.getKeyTitle());
        assertEquals("", result.getValueTitle());
    }

    // Test isInternationalProperty method
    @Test
    public void testIsInternationalProperty_withInternationalLocationInfo_returnsTrue() {
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCountryCode("US");
        StaticDetailCriteria criteria = new StaticDetailCriteria();

        boolean result = reArchUtility.isInternationalProperty(locationInfo, criteria);

        assertTrue(result);
    }

    @Test
    public void testIsInternationalProperty_withDomesticLocationInfo_returnsFalse() {
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCountryCode(Constants.DOM_COUNTRY_CODE);
        StaticDetailCriteria criteria = new StaticDetailCriteria();

        boolean result = reArchUtility.isInternationalProperty(locationInfo, criteria);

        assertFalse(result);
    }

    @Test
    public void testIsInternationalProperty_withInternationalCriteria_returnsTrue() {
        LocationInfo locationInfo = new LocationInfo();
        StaticDetailCriteria criteria = new StaticDetailCriteria();
        criteria.setCountryCode("UK");

        boolean result = reArchUtility.isInternationalProperty(locationInfo, criteria);

        assertTrue(result);
    }

    @Test
    public void testIsInternationalProperty_withDomesticCriteria_returnsFalse() {
        LocationInfo locationInfo = new LocationInfo();
        StaticDetailCriteria criteria = new StaticDetailCriteria();
        criteria.setCountryCode(Constants.DOM_COUNTRY_CODE);

        boolean result = reArchUtility.isInternationalProperty(locationInfo, criteria);

        assertFalse(result);
    }

    @Test
    public void testIsInternationalProperty_withNullInputs_returnsFalse() {
        boolean result = ((ReArchUtility) reArchUtility).isInternationalProperty((LocationInfo) null, null);
        assertFalse(result);
    }

    @Test
    public void testIsInternationalProperty_withNullCountryCodes_returnsFalse() {
        LocationInfo locationInfo = new LocationInfo();
        StaticDetailCriteria criteria = new StaticDetailCriteria();

        boolean result = reArchUtility.isInternationalProperty(locationInfo, criteria);

        assertFalse(result);
    }

    // Test updateAmenitiesGIRearch method
    @Test
    public void testUpdateAmenitiesGIRearch_withValidAmenities_updatesResponse() {
        StaticDetailResponse response = createStaticDetailResponseWithAmenities();
        List<AmenityGroup> amenities = createAmenityGroupList();

        reArchUtility.updateAmenitiesGIRearch(response, amenities);

        assertNotNull(response.getAmenitiesGI().getCategorizedV2());
        assertNull(response.getAmenitiesGI().getCategorized());
    }

    @Test
    public void testUpdateAmenitiesGIRearch_withNullAmenitiesGI_handlesGracefully() {
        StaticDetailResponse response = new StaticDetailResponse();
        List<AmenityGroup> amenities = createAmenityGroupList();

        reArchUtility.updateAmenitiesGIRearch(response, amenities);

        // Should not throw exception
        assertNull(response.getAmenitiesGI());
    }

    @Test
    public void testUpdateAmenitiesGIRearch_withEmptyCategorized_handlesGracefully() {
        StaticDetailResponse response = createStaticDetailResponseWithEmptyAmenities();
        List<AmenityGroup> amenities = createAmenityGroupList();

        reArchUtility.updateAmenitiesGIRearch(response, amenities);

        // Should not modify anything
        assertNotNull(response.getAmenitiesGI());
    }

    // Test getHighlightedAmenitiesV2 method
    @Test
    public void testGetHighlightedAmenitiesV2_withValidAmenities_returnsHighlightedList() {
        List<AmenityGroup> amenities = createAmenityGroupList();

        List<HighlightedAmenity> result = reArchUtility.getHighlightedAmenitiesV2(amenities);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("WiFi", result.get(0).getTitle());
        assertEquals("http://example.com/wifi.png", result.get(0).getIconUrl());
    }

    @Test
    public void testGetHighlightedAmenitiesV2_withEmptyAmenities_returnsEmptyList() {
        List<AmenityGroup> amenities = new ArrayList<>();

        List<HighlightedAmenity> result = reArchUtility.getHighlightedAmenitiesV2(amenities);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetHighlightedAmenitiesV2_withNullAmenities_returnsEmptyList() {
        List<HighlightedAmenity> result = reArchUtility.getHighlightedAmenitiesV2(null);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetHighlightedAmenitiesV2_withEmptyAmenitiesInGroup_returnsEmptyList() {
        List<AmenityGroup> amenities = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setAmenities(new ArrayList<>());
        amenities.add(group);

        List<HighlightedAmenity> result = reArchUtility.getHighlightedAmenitiesV2(amenities);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // Test buildAmenities method
    @Test
    public void testBuildAmenities_withValidInputs_returnsAmenitiesList() {
        List<AmenityGroup> facilityWithGrp = createAmenityGroupList();
        List<AmenityGroup> starFacilities = createStarFacilities();
        List<AmenityGroup> highlightedFacilities = createAmenityGroupList();

        List<com.mmt.hotels.clientgateway.response.FacilityGroup> result = 
            reArchUtility.buildAmenities(facilityWithGrp, starFacilities, highlightedFacilities);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        // Star facilities should be first
        assertEquals("Star Facilities", result.get(0).getName());
    }

    @Test
    public void testBuildAmenities_withEmptyFacilities_returnsNull() {
        List<com.mmt.hotels.clientgateway.response.FacilityGroup> result = 
            reArchUtility.buildAmenities(new ArrayList<>(), null, null);

        assertNull(result);
    }

    @Test
    public void testBuildAmenities_withNullFacilities_returnsNull() {
        List<com.mmt.hotels.clientgateway.response.FacilityGroup> result = 
            reArchUtility.buildAmenities(null, null, null);

        assertNull(result);
    }

    @Test
    public void testBuildAmenities_withoutStarFacilities_returnsNormalList() {
        List<AmenityGroup> facilityWithGrp = createAmenityGroupList();

        List<com.mmt.hotels.clientgateway.response.FacilityGroup> result = 
            reArchUtility.buildAmenities(facilityWithGrp, null, null);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        // Should not have star facilities as first item
        assertNotEquals("Star Facilities", result.get(0).getName());
    }

    @Test
    public void testBuildAmenities_withStarFacilitiesNotInMain_addsStarFacilitiesFromSeparateList() {
        List<AmenityGroup> facilityWithGrp = createAmenityGroupWithDifferentFacilities();
        List<AmenityGroup> starFacilities = createStarFacilities();

        List<com.mmt.hotels.clientgateway.response.FacilityGroup> result = 
            reArchUtility.buildAmenities(facilityWithGrp, starFacilities, null);

        assertNotNull(result);
        assertTrue(result.size() >= 2);
        assertEquals("Star Facilities", result.get(0).getName());
    }

    // Test buildChildAttributesCgFromHes method
    @Test
    public void testBuildChildAttributesCgFromHes_withValidAttributes_returnsAttributesList() {
        List<AmenityAttribute> attributes = createAmenityAttributeList();

        List<com.mmt.hotels.clientgateway.response.AttributesFacility> result = 
            reArchUtility.buildChildAttributesCgFromHes(attributes);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Attribute 1", result.get(0).getName());
        assertEquals("Attribute 2", result.get(1).getName());
    }

    @Test
    public void testBuildChildAttributesCgFromHes_withEmptyAttributes_returnsEmptyList() {
        List<AmenityAttribute> attributes = new ArrayList<>();

        List<com.mmt.hotels.clientgateway.response.AttributesFacility> result = 
            reArchUtility.buildChildAttributesCgFromHes(attributes);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // Edge Cases and Error Scenarios
    @Test
    public void testBuildSpaceInclusion_withComplexBedArrangements_handlesCorrectly() {
        Space space = createComplexSpaceWithMultipleBeds();

        List<String> result = reArchUtility.buildSpaceInclusion(space);

        assertNotNull(result);
        assertTrue(result.get(0).contains("King"));
        assertTrue(result.get(0).contains("Queen"));
        assertTrue(result.get(0).contains("&"));
    }

    @Test
    public void testGetSpaceDataV2_withZeroOccupancy_handlesCorrectly() {
        SpaceData spaceData = createSpaceDataWithZeroOccupancy();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, false, spaceDataList);

        assertNotNull(result);
        // Should handle zero occupancy gracefully
        assertNull(result.getSpaces().get(0).getSubText());
    }

    @Test
    public void testGetSpaceDataV2_withHighOccupancy_usesPlural() {
        SpaceData spaceData = createSpaceDataWithHighOccupancy();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, false, spaceDataList);

        assertNotNull(result);
        assertTrue(result.getSpaces().get(0).getSubText().contains("guests"));
    }

    @Test
    public void testGetSpaceDataV2_withSingleOccupancy_usesSingular() {
        SpaceData spaceData = createSpaceDataWithSingleOccupancy();
        Set<com.mmt.hotels.clientgateway.response.rooms.SpaceData> spaceDataList = new HashSet<>();

        com.mmt.hotels.clientgateway.response.rooms.SpaceData result = 
            reArchUtility.getSpaceDataV2(spaceData, false, spaceDataList);

        assertNotNull(result);
        assertTrue(result.getSpaces().get(0).getSubText().contains("guest"));
    }

    @Test
    public void testBuildAmenities_withChildAttributes_includesChildAttributes() {
        List<AmenityGroup> facilityWithGrp = createAmenityGroupWithChildAttributes();

        List<com.mmt.hotels.clientgateway.response.FacilityGroup> result = 
            reArchUtility.buildAmenities(facilityWithGrp, null, null);

        assertNotNull(result);
        assertNotNull(result.get(0).getFacilities().get(0).getChildAttributes());
    }

    @Test
    public void testBuildAmenities_withNullChildAttributes_handlesGracefully() {
        List<AmenityGroup> facilityWithGrp = createAmenityGroupWithNullChildAttributes();

        List<com.mmt.hotels.clientgateway.response.FacilityGroup> result = 
            reArchUtility.buildAmenities(facilityWithGrp, null, null);

        assertNotNull(result);
        assertNull(result.get(0).getFacilities().get(0).getChildAttributes());
    }

    @Test
    public void testIsInternationalProperty_caseInsensitiveCountryCheck_returnsCorrectly() {
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCountryCode("in"); // lowercase
        StaticDetailCriteria criteria = new StaticDetailCriteria();

        // Explicitly call ReArchUtility's version of the method
        boolean result = ((ReArchUtility) reArchUtility).isInternationalProperty(locationInfo, criteria);

        assertFalse(result);
    }

    @Test
    public void testUpdateAmenitiesGIRearch_withNullCategorized_handlesGracefully() {
        StaticDetailResponse response = new StaticDetailResponse();
        Amenities amenitiesGI = new Amenities();
        amenitiesGI.setCategorized(null);
        response.setAmenitiesGI(amenitiesGI);
        List<AmenityGroup> amenities = createAmenityGroupList();

        reArchUtility.updateAmenitiesGIRearch(response, amenities);

        // Should not throw exception and should not modify
        assertNull(response.getAmenitiesGI().getCategorized());
    }

    // Helper methods for creating test data
    private Space createSpaceWithBedInfo() {
        Space space = new Space();
        SleepingDetails sleepingDetails = new SleepingDetails();
        List<ArrangementInfo> bedInfo = new ArrayList<>();
        ArrangementInfo bed = new ArrangementInfo();
        bed.setCount(1);
        bed.setType("King");
        bedInfo.add(bed);
        sleepingDetails.setBedInfo(bedInfo);
        space.setSleepingDetails(sleepingDetails);
        return space;
    }

    private Space createSpaceWithExtraBeds() {
        Space space = new Space();
        SleepingDetails sleepingDetails = new SleepingDetails();
        List<ArrangementInfo> extraBedInfo = new ArrayList<>();
        ArrangementInfo extraBed = new ArrangementInfo();
        extraBed.setCount(1);
        extraBed.setType("Sofa Bed");
        extraBedInfo.add(extraBed);
        sleepingDetails.setExtraBedInfo(extraBedInfo);
        space.setSleepingDetails(sleepingDetails);
        return space;
    }

    private Space createSpaceWithBedInfoAndDescription() {
        Space space = createSpaceWithBedInfo();
        space.setDescriptionText("Test Description");
        return space;
    }

    private Space createComplexSpaceWithMultipleBeds() {
        Space space = new Space();
        SleepingDetails sleepingDetails = new SleepingDetails();
        List<ArrangementInfo> bedInfo = new ArrayList<>();
        
        ArrangementInfo bed1 = new ArrangementInfo();
        bed1.setCount(1);
        bed1.setType("King");
        bedInfo.add(bed1);
        
        ArrangementInfo bed2 = new ArrangementInfo();
        bed2.setCount(1);
        bed2.setType("Queen");
        bedInfo.add(bed2);
        
        sleepingDetails.setBedInfo(bedInfo);
        space.setSleepingDetails(sleepingDetails);
        return space;
    }

    private SpaceData createValidSpaceData() {
        SpaceData spaceData = new SpaceData();
        spaceData.setDescriptive(Arrays.asList("Test Descriptive"));
        
        DisplayItem displayItem = new DisplayItem();
        displayItem.setIconUrl("http://example.com/icon.png");
        displayItem.setText("Test Display Item");
        spaceData.setDisplayItem(displayItem);
        
        List<Space> spaces = new ArrayList<>();
        Space space = new Space();
        space.setName("Test Space");
        space.setSpaceId("space1");
        space.setSpaceType("BEDROOM");
        space.setAreaText("20 sqm");
        space.setDescriptionText("Test Description");
        space.setSubText("Test Sub Text");
        spaces.add(space);
        
        spaceData.setSpaces(spaces);
        return spaceData;
    }

    private SpaceData createSpaceDataWithBedroom() {
        SpaceData spaceData = createValidSpaceData();
        Space space = spaceData.getSpaces().get(0);
        space.setSpaceType(Constants.BEDROOM);
        space.setBaseOccupancy(2);
        space.setFinalOccupancy(3);
        return spaceData;
    }

    private SpaceData createSpaceDataWithLivingRoom() {
        SpaceData spaceData = createValidSpaceData();
        Space space = spaceData.getSpaces().get(0);
        space.setSpaceType(Constants.LIVING_ROOM);
        space.setBaseOccupancy(4);
        space.setFinalOccupancy(4);
        return spaceData;
    }

    private SpaceData createSpaceDataWithMedia() {
        SpaceData spaceData = createValidSpaceData();
        Space space = spaceData.getSpaces().get(0);
        
        List<RoomEntity> media = new ArrayList<>();
        RoomEntity mediaEntity = new RoomEntity();
        mediaEntity.setCategory("image");
        mediaEntity.setUrl("http://example.com/image.jpg");
        media.add(mediaEntity);
        
        space.setMedia(media);
        return spaceData;
    }

    private SpaceData createSpaceDataWithZeroOccupancy() {
        SpaceData spaceData = createValidSpaceData();
        Space space = spaceData.getSpaces().get(0);
        space.setSpaceType(Constants.BEDROOM);
        space.setBaseOccupancy(0);
        space.setFinalOccupancy(0);
        return spaceData;
    }

    private SpaceData createSpaceDataWithHighOccupancy() {
        SpaceData spaceData = createValidSpaceData();
        Space space = spaceData.getSpaces().get(0);
        space.setSpaceType(Constants.BEDROOM);
        space.setBaseOccupancy(4);
        space.setFinalOccupancy(5);
        return spaceData;
    }

    private SpaceData createSpaceDataWithSingleOccupancy() {
        SpaceData spaceData = createValidSpaceData();
        Space space = spaceData.getSpaces().get(0);
        space.setSpaceType(Constants.BEDROOM);
        space.setBaseOccupancy(1);
        space.setFinalOccupancy(1);
        return spaceData;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo createValidRuleTableInfo() {
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo ruleTableInfo = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo();
        ruleTableInfo.setKeyTitle("Key Title");
        ruleTableInfo.setValueTitle("Value Title");
        
        List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo> infoList = new ArrayList<>();
        
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo rule1 = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo();
        rule1.setKey("Check-in");
        rule1.setValue(Arrays.asList("3:00 PM"));
        infoList.add(rule1);
        
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo rule2 = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo();
        rule2.setKey("Check-out");
        rule2.setValue(Arrays.asList("11:00 AM"));
        infoList.add(rule2);
        
        ruleTableInfo.setInfoList(infoList);
        return ruleTableInfo;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo createRuleTableInfoWithBlankTitles() {
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo ruleTableInfo = createValidRuleTableInfo();
        ruleTableInfo.setKeyTitle("");
        ruleTableInfo.setValueTitle("   ");
        return ruleTableInfo;
    }

    private StaticDetailResponse createStaticDetailResponseWithAmenities() {
        StaticDetailResponse response = new StaticDetailResponse();
        Amenities amenitiesGI = new Amenities();
        
        List<Categorized> categorized = new ArrayList<>();
        Categorized category = new Categorized();
        category.setLabel("General");
        categorized.add(category);
        
        amenitiesGI.setCategorized(categorized);
        response.setAmenitiesGI(amenitiesGI);
        return response;
    }

    private StaticDetailResponse createStaticDetailResponseWithEmptyAmenities() {
        StaticDetailResponse response = new StaticDetailResponse();
        Amenities amenitiesGI = new Amenities();
        amenitiesGI.setCategorized(new ArrayList<>());
        response.setAmenitiesGI(amenitiesGI);
        return response;
    }

    private List<AmenityGroup> createAmenityGroupList() {
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("General Amenities");
        
        List<Amenity> amenities = new ArrayList<>();
        Amenity amenity = new Amenity();
        amenity.setName("WiFi");
        amenity.setIconUrl("http://example.com/wifi.png");
        amenity.setAttributeName("wifi");
        amenity.setCategoryName("internet");
        amenity.setDisplayType("1");
        amenity.setHighlightedName("Free WiFi");
        amenity.setSequence(1);
        // amenity.setTags(Arrays.asList("free", "internet")); // Uncomment if tags supports List<String>
        amenity.setType("basic");
        amenities.add(amenity);
        
        group.setAmenities(amenities);
        amenityGroups.add(group);
        return amenityGroups;
    }

    private List<AmenityGroup> createStarFacilities() {
        List<AmenityGroup> starFacilities = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Star Facilities");
        
        List<Amenity> amenities = new ArrayList<>();
        Amenity amenity = new Amenity();
        amenity.setName("WiFi");
        amenity.setIconUrl("http://example.com/wifi.png");
        amenity.setSequence(1);
        amenities.add(amenity);
        
        group.setAmenities(amenities);
        starFacilities.add(group);
        return starFacilities;
    }

    private List<AmenityGroup> createAmenityGroupWithDifferentFacilities() {
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Different Amenities");
        
        List<Amenity> amenities = new ArrayList<>();
        Amenity amenity = new Amenity();
        amenity.setName("Parking");
        amenity.setIconUrl("http://example.com/parking.png");
        amenities.add(amenity);
        
        group.setAmenities(amenities);
        amenityGroups.add(group);
        return amenityGroups;
    }

    private List<AmenityGroup> createAmenityGroupWithChildAttributes() {
        List<AmenityGroup> amenityGroups = createAmenityGroupList();
        Amenity amenity = amenityGroups.get(0).getAmenities().get(0);
        
        List<AmenityAttribute> childAttributes = createAmenityAttributeList();
        amenity.setChildAttributes(childAttributes);
        
        return amenityGroups;
    }

    private List<AmenityGroup> createAmenityGroupWithNullChildAttributes() {
        List<AmenityGroup> amenityGroups = createAmenityGroupList();
        Amenity amenity = amenityGroups.get(0).getAmenities().get(0);
        amenity.setChildAttributes(null);
        return amenityGroups;
    }

    private List<AmenityAttribute> createAmenityAttributeList() {
        List<AmenityAttribute> attributes = new ArrayList<>();
        
        AmenityAttribute attr1 = new AmenityAttribute();
        attr1.setName("Attribute 1");
        // AmenityAttribute doesn't have iconUrl field
        attributes.add(attr1);
        
        AmenityAttribute attr2 = new AmenityAttribute();
        attr2.setName("Attribute 2");
        // AmenityAttribute doesn't have iconUrl field
        attributes.add(attr2);
        
        return attributes;
    }

    // ===================== GET PRICE COLOR FOR PRICE DROP TESTS =====================

    @Test
    public void testGetPriceColorForPriceDrop_DropType_ShouldReturnDropColor() {
        // Given - Testing PriceVariationType.DROP (Lines 260-267)
        PriceVariationType type = PriceVariationType.DROP;

        // When
        String result = reArchUtility.getPriceColorForPriceDrop(type);

        // Then - Verify DROP color is returned
        assertNotNull(result); // Line 267 - return statement
        assertEquals("#007E7D", result); // Line 263 - DROP color assignment

        // This test covers:
        // Line 261: String color = null;
        // Line 262: if (type == PriceVariationType.DROP) {
        // Line 263: color = "#007E7D";
        // Line 267: return color;
    }

    @Test
    public void testGetPriceColorForPriceDrop_SurgeType_ShouldReturnSurgeColor() {
        // Given - Testing PriceVariationType.SURGE (Lines 260-267)
        PriceVariationType type = PriceVariationType.SURGE;

        // When
        String result = reArchUtility.getPriceColorForPriceDrop(type);

        // Then - Verify SURGE color is returned
        assertNotNull(result); // Line 267 - return statement
        assertEquals("#CF8100", result); // Line 265 - SURGE color assignment

        // This test covers:
        // Line 261: String color = null;
        // Line 262: if (type == PriceVariationType.DROP) { (false condition)
        // Line 264: } else if( type == PriceVariationType.SURGE) {
        // Line 265: color = "#CF8100";
        // Line 267: return color;
    }

    @Test
    public void testGetPriceColorForPriceDrop_TypicalType_ShouldReturnNull() {
        // Given - Testing PriceVariationType.TYPICAL (Lines 260-267)
        PriceVariationType type = PriceVariationType.TYPICAL;

        // When
        String result = reArchUtility.getPriceColorForPriceDrop(type);

        // Then - Verify null is returned for non-DROP/SURGE types
        assertNull(result); // Line 267 - return null (no color assigned)

        // This test covers:
        // Line 261: String color = null;
        // Line 262: if (type == PriceVariationType.DROP) { (false condition)
        // Line 264: } else if( type == PriceVariationType.SURGE) { (false condition)
        // Line 267: return color; (returns null)
    }

    @Test
    public void testGetPriceColorForPriceDrop_NullType_ShouldReturnNull() {
        // Given - Testing null input (Lines 260-267)
        PriceVariationType type = null;

        // When
        String result = reArchUtility.getPriceColorForPriceDrop(type);

        // Then - Verify null is returned for null input
        assertNull(result); // Line 267 - return null (no color assigned)

        // This test covers:
        // Line 261: String color = null;
        // Line 262: if (type == PriceVariationType.DROP) { (false condition - null check)
        // Line 264: } else if( type == PriceVariationType.SURGE) { (false condition - null check)
        // Line 267: return color; (returns null)
    }

    @Test
    public void testGetPriceColorForPriceDrop_ComprehensiveLineCoverage() {
        // Given - Comprehensive test to ensure every line and branch is covered

        // Test 1: DROP type (Line 262-263 branch)
        String dropResult = reArchUtility.getPriceColorForPriceDrop(PriceVariationType.DROP);
        assertEquals("#007E7D", dropResult);

        // Test 2: SURGE type (Line 264-265 branch)
        String surgeResult = reArchUtility.getPriceColorForPriceDrop(PriceVariationType.SURGE);
        assertEquals("#CF8100", surgeResult);

        // Test 3: Other type (neither DROP nor SURGE - Line 262,264 both false)
        String typicalResult = reArchUtility.getPriceColorForPriceDrop(PriceVariationType.TYPICAL);
        assertNull(typicalResult);

        // Test 4: Null type (Line 262,264 both false due to null)
        String nullResult = reArchUtility.getPriceColorForPriceDrop((PriceVariationType) null);
        assertNull(nullResult);

        // This comprehensive test ensures 100% line coverage:
        // Line 261: String color = null; (covered in all tests)
        // Line 262: if (type == PriceVariationType.DROP) { (true in test 1, false in tests 2,3,4)
        // Line 263: color = "#007E7D"; (covered in test 1)
        // Line 264: } else if( type == PriceVariationType.SURGE) { (true in test 2, false in tests 3,4)
        // Line 265: color = "#CF8100"; (covered in test 2)
        // Line 267: return color; (covered in all tests)
    }

    @Test
    public void testGetPriceColorForPriceDrop_AllPriceVariationTypes() {
        // Given - Testing all enum values to ensure complete enum coverage

        // Test all possible PriceVariationType enum values
        assertEquals("#007E7D", reArchUtility.getPriceColorForPriceDrop(PriceVariationType.DROP));
        assertEquals("#CF8100", reArchUtility.getPriceColorForPriceDrop(PriceVariationType.SURGE));
        assertNull(reArchUtility.getPriceColorForPriceDrop(PriceVariationType.TYPICAL));

        // Test null handling
        assertNull(reArchUtility.getPriceColorForPriceDrop((PriceVariationType) null));

        // This test verifies:
        // 1. All known enum values are handled correctly
        // 2. Only DROP and SURGE return colors
        // 3. TYPICAL and null return null
        // 4. Function behavior is consistent across multiple calls
    }
} 