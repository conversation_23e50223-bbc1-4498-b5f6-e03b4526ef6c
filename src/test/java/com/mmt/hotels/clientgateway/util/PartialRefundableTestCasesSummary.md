# Partial Refundable Feature - Test Cases Summary

## Overview
This document outlines the comprehensive test cases that should be written for the 'partial refundable' feature implemented in the Hotels-GI-Clientgateway project.

## Core Components Tested

### 1. `buildPartialRefundDateText` Method (Utility.java)
This is the core method that builds the text for partial refundable policies.

#### Test Cases Already Implemented:
- ✅ `testBuildPartialRefundDateText_NullTimeline` - Tests null input
- ✅ `testBuildPartialRefundDateText_CheckInAndRefundDateSame` - Tests when check-in and refund dates are same
- ✅ `testBuildPartialRefundDateText_DifferentCheckInAndRefundDate` - Tests when dates are different
- ✅ `testBuildPartialRefundDateText_WithEmptyRefundDateTime` - Tests empty refund date time
- ✅ `testBuildPartialRefundDateText_WithNullRefundDateTime` - Tests null refund date time
- ✅ `testBuildPartialRefundDateText_WithEmptyRefundDate` - Tests empty refund date
- ✅ `testBuildPartialRefundDateText_WithNullRefundDate` - Tests null refund date
- ✅ `testBuildPartialRefundDateText_WithEmptyCheckInDate` - Tests empty check-in date
- ✅ `testBuildPartialRefundDateText_WithNullCheckInDate` - Tests null check-in date
- ✅ `testBuildPartialRefundDateText_WithEmptyTimelineList` - Tests empty timeline list
- ✅ `testBuildPartialRefundDateText_WithNullTimelineList` - Tests null timeline list
- ✅ `testBuildPartialRefundDateText_WithNullFirstTimeline` - Tests null first timeline
- ✅ `testBuildPartialRefundDateText_CaseInsensitiveDateComparison` - Tests case insensitive comparison
- ✅ `testBuildPartialRefundDateText_WithSpecialCharactersInDates` - Tests special characters in dates
- ✅ `testBuildPartialRefundDateText_WithVeryLongDateStrings` - Tests long date strings
- ✅ `testBuildPartialRefundDateText_WithPolyglotServiceFailure` - Tests polyglot service failure
- ✅ `testBuildPartialRefundDateText_WithEmptyPolyglotResponse` - Tests empty polyglot response
- ✅ `testBuildPartialRefundDateText_WithComplexTimeFormats` - Tests complex time formats
- ✅ `testBuildPartialRefundDateText_With24HourTimeFormat` - Tests 24-hour time format
- ✅ `testBuildPartialRefundDateText_WithDifferentDateFormats` - Tests different date formats
- ✅ `testPartialRefundable_WithAllNullValues` - Tests all null values
- ✅ `testPartialRefundable_WithEmptyStrings` - Tests empty strings
- ✅ `testPartialRefundable_WithWhitespaceOnly` - Tests whitespace only
- ✅ `testPartialRefundable_WithVeryLongStrings` - Tests very long strings

### 2. `transformCancellationPolicy` Method (Utility.java)
This method handles the transformation of cancellation policies including partial refundable.

#### Test Cases to be Implemented:
- ⏳ `testTransformCancellationPolicy_PartialRefundable` - Basic partial refundable scenario
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithCheckInDateSame` - When check-in and refund dates are same
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithNullTimeline` - With null timeline
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithEmptyTimelineList` - With empty timeline list
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithNullCancellationType` - With null cancellation type
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithEmptyCancelPenaltyList` - With empty cancel penalty list
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithNullCancelPenaltyList` - With null cancel penalty list
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithCancelRules` - With cancel rules
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithNullCancelRules` - With null cancel rules
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithMultipleCancelPenalties` - With multiple penalties
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithAdvancePurchase` - With advance purchase
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithBNPL` - With BNPL
- ⏳ `testTransformCancellationPolicy_PartialRefundableWithConfirmationPolicyType` - With confirmation policy type

### 3. SelectRoomV2ResponseTransformer Integration
Tests the integration with SelectRoomV2ResponseTransformer where partial refundable policies are processed.

#### Test Cases to be Implemented:
- ⏳ `testSetCancellationPolicyDetails_PartialRefundable` - Tests setting cancellation policy details for PR type
- ⏳ `testSetCancellationPolicyDetails_PartialRefundableWithShortHtmlText` - Tests short HTML text formatting
- ⏳ `testSetCancellationPolicyDetails_PartialRefundableWithNullPolicy` - Tests with null policy

### 4. CancellationPolicyHelper Integration
Tests the integration with CancellationPolicyHelper where partial refundable policies are handled.

#### Test Cases Already Implemented:
- ✅ `should_ReturnFCZPN_When_PenaltyTypeIsPARTIAL_REFUNDABLE` - Tests penalty type mapping

#### Test Cases to be Implemented:
- ⏳ `testGetCancellationPolicyType_PartialRefundable` - Tests cancellation policy type mapping
- ⏳ `testGetCancellationPolicyType_PartialRefundableCaseInsensitive` - Tests case insensitive mapping
- ⏳ `testGetCancellationPolicyType_PartialRefundableWithNullPenalties` - Tests with null penalties

### 5. Constants and Translation Tests
Tests the constants and translation keys used for partial refundable feature.

#### Test Cases to be Implemented:
- ⏳ `testPartialRefundableConstants` - Tests all partial refundable constants
- ⏳ `testPartialRefundableTranslationKeys` - Tests translation key mappings
- ⏳ `testPartialRefundableTextFormatting` - Tests text formatting with constants

## Edge Cases and Error Handling

### 1. Data Validation
- ⏳ `testPartialRefundable_WithInvalidDateFormat` - Tests invalid date formats
- ⏳ `testPartialRefundable_WithInvalidTimeFormat` - Tests invalid time formats
- ⏳ `testPartialRefundable_WithMalformedTimeline` - Tests malformed timeline data

### 2. Performance Tests
- ⏳ `testPartialRefundable_PerformanceWithLargeTimelineList` - Tests performance with large data
- ⏳ `testPartialRefundable_MemoryUsage` - Tests memory usage

### 3. Integration Tests
- ⏳ `testPartialRefundable_EndToEndFlow` - Tests complete end-to-end flow
- ⏳ `testPartialRefundable_WithDifferentControllers` - Tests with different controller contexts
- ⏳ `testPartialRefundable_WithDifferentRegions` - Tests with different regions

## Test Data Requirements

### Sample Test Data
```json
{
  "cancellationTimeline": {
    "checkInDate": "15 Jul",
    "cancellationPolicyTimelineList": [
      {
        "endDate": "10 Jul",
        "endDateTime": "11:59 am"
      }
    ]
  },
  "cancelPenalty": [
    {
      "cancellationType": "PARTIAL_REFUNDABLE",
      "cancelRules": []
    }
  ]
}
```

### Expected Outputs
- **Same dates**: "Partially refundable till check-in"
- **Different dates**: "Partially refundable before 10 Jul 11:59 am"
- **Missing data**: "Partially refundable"

## Mock Setup Requirements

### PolyglotService Mocks
```java
when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_CHECKIN_TEXT"))
    .thenReturn("Partially refundable till check-in");
when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT"))
    .thenReturn("Partially refundable before {0} {1}");
when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_TEXT"))
    .thenReturn("Partially refundable");
```

## Test Execution Strategy

### 1. Unit Tests
- Focus on individual method testing
- Mock all external dependencies
- Test all edge cases and error conditions

### 2. Integration Tests
- Test component interactions
- Test with real data structures
- Test end-to-end flows

### 3. Performance Tests
- Test with large datasets
- Monitor memory usage
- Test response times

## Coverage Goals

### Code Coverage
- **Line Coverage**: > 95%
- **Branch Coverage**: > 90%
- **Method Coverage**: 100%

### Test Categories
- **Positive Tests**: 60%
- **Negative Tests**: 30%
- **Edge Cases**: 10%

## Notes

1. **Method Signature Issues**: The `transformCancellationPolicy` method has complex parameter requirements that need to be properly handled in tests.

2. **Class Dependencies**: Some classes have different import paths that need to be resolved.

3. **Mock Setup**: Proper mock setup is crucial for testing the polyglot service integration.

4. **Data Validation**: The feature handles various date and time formats that need comprehensive testing.

5. **Error Handling**: The feature should gracefully handle null values, empty strings, and malformed data.

## Implementation Status

- ✅ **Core `buildPartialRefundDateText` tests**: Complete
- ⏳ **`transformCancellationPolicy` tests**: Pending (method signature issues)
- ⏳ **Integration tests**: Pending
- ⏳ **Performance tests**: Pending
- ⏳ **End-to-end tests**: Pending

## Next Steps

1. Resolve method signature issues for `transformCancellationPolicy` tests
2. Implement integration tests with proper mock setup
3. Add performance and end-to-end tests
4. Validate test coverage meets goals
5. Document any additional edge cases discovered during testing 