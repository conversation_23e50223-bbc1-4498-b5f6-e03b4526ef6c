package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.HotelPrice;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.NINE;

@RunWith(MockitoJUnitRunner.class)
public class DayUseUtilTest {
	@InjectMocks
	DayUseUtil dayUseUtil;

	@Test
	public void shouldSetPriceDetailsForDayUseTest_true(){
		DisplayFare displayFare = new DisplayFare();
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceWithTax(1000);
		displayFare.setSlashedPrice(slashedPrice);

		List<SlotDetail> slotDetails = new ArrayList<>();
		SlotDetail slotDetail = new SlotDetail();
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setDiscountedPriceWithTax(100.0);
		slotDetail.setPriceDetail(priceDetail);
		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.shouldSetPriceDetailsForDayUse(slotDetails, displayFare);

		Assert.assertTrue(resp);
	}

	@Test
	public void shouldSetPriceDetailsForDayUseTest_false(){
		DisplayFare displayFare = new DisplayFare();
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceWithTax(10);
		displayFare.setSlashedPrice(slashedPrice);

		List<SlotDetail> slotDetails = new ArrayList<>();
		SlotDetail slotDetail = new SlotDetail();
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setDiscountedPriceWithTax(100.0);
		slotDetail.setPriceDetail(priceDetail);
		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.shouldSetPriceDetailsForDayUse(slotDetails, displayFare);

		Assert.assertFalse(resp);
	}

	@Test
	public void isXPercentRulePassedTest_false(){
		DisplayFare displayFare = new DisplayFare();
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceWithTax(10);
		displayFare.setSlashedPrice(slashedPrice);

		List<SlotDetail> slotDetails = new ArrayList<>();
		SlotDetail slotDetail = new SlotDetail();
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setDiscountedPriceWithTax(100.0);
		slotDetail.setPriceDetail(priceDetail);

		Slot slot = new Slot();
		slot.setDuration(NINE);
		slot.setTimeSlot("8 AM - 5 PM");
		slotDetail.setSlot(slot);

		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.isXPercentRulePassed(slotDetails, displayFare, 5);
		Assert.assertFalse(resp);
	}

	@Test
	public void isXPercentRulePassedTest_true(){
		DisplayFare displayFare = new DisplayFare();
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceWithTax(10000);
		displayFare.setSlashedPrice(slashedPrice);

		List<SlotDetail> slotDetails = new ArrayList<>();
		SlotDetail slotDetail = new SlotDetail();
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setDiscountedPriceWithTax(100.0);
		slotDetail.setPriceDetail(priceDetail);

		Slot slot = new Slot();
		slot.setDuration(NINE);
		slot.setTimeSlot("8 AM - 5 PM");
		slotDetail.setSlot(slot);

		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.isXPercentRulePassed(slotDetails, displayFare, 5);
		Assert.assertTrue(resp);
	}

	@Test
	public void shouldSetPriceDetailsForDayUseOnDetailPageTest_true(){
		double slashedPrice = 1000.0;

		List<DayUseSlotPlan> slotDetails = new ArrayList<>();
		DayUseSlotPlan slotDetail = new DayUseSlotPlan();
		DayUsePriceDetail priceDetail = new DayUsePriceDetail();
		priceDetail.setTotalPrice(100.0);
		slotDetail.setPriceDetail(priceDetail);
		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.shouldSetPriceDetailsForDayUseOnDetailPage(slotDetails, slashedPrice);

		Assert.assertTrue(resp);
	}

	@Test
	public void shouldSetPriceDetailsForDayUseOnDetailPageTest_False(){
		double slashedPrice = 10.0;

		List<DayUseSlotPlan> slotDetails = new ArrayList<>();
		DayUseSlotPlan slotDetail = new DayUseSlotPlan();
		DayUsePriceDetail priceDetail = new DayUsePriceDetail();
		priceDetail.setTotalPrice(100.0);
		slotDetail.setPriceDetail(priceDetail);
		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.shouldSetPriceDetailsForDayUseOnDetailPage(slotDetails, slashedPrice);

		Assert.assertFalse(resp);
	}

	@Test
	public void isXPercentRulePassedOnDetailPageTest_false(){

		double slashedPrice = 850.0;

		List<DayUseSlotPlan> slotDetails = new ArrayList<>();
		DayUseSlotPlan slotDetail = new DayUseSlotPlan();
		DayUsePriceDetail priceDetail = new DayUsePriceDetail();
		priceDetail.setTotalPrice(846.0);
		slotDetail.setPriceDetail(priceDetail);

		Slot slot = new Slot();
		slot.setDuration(NINE);
		slot.setTimeSlot("8 AM - 5 PM");
		slotDetail.setSlot(slot);

		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.isXPercentRulePassedOnDetailPage(slotDetails, slashedPrice, 5);
		Assert.assertFalse(resp);
	}

	@Test
	public void isXPercentRulePassedOnDetailPageTest_true(){
		double slashedPrice = 5000.0;

		List<DayUseSlotPlan> slotDetails = new ArrayList<>();
		DayUseSlotPlan slotDetail = new DayUseSlotPlan();
		DayUsePriceDetail priceDetail = new DayUsePriceDetail();
		priceDetail.setTotalPrice(100.0);
		slotDetail.setPriceDetail(priceDetail);

		Slot slot = new Slot();
		slot.setDuration(NINE);
		slot.setTimeSlot("8 AM - 5 PM");
		slotDetail.setSlot(slot);

		slotDetails.add(slotDetail);

		boolean resp = dayUseUtil.isXPercentRulePassedOnDetailPage(slotDetails, slashedPrice, 5);
		Assert.assertTrue(resp);
	}
}
