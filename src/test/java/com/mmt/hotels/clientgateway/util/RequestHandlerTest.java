package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.AvailPriceCriteria;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.junit.runner.RunWith;

@RunWith(MockitoJUnitRunner.class)
public class RequestHandlerTest {

    @Mock
    MetricErrorLogger metricErrorLogger;

    @InjectMocks
    private RequestHandler requestHandler;

    @Before
    public void setUp() {
        Mockito.doNothing().when(metricErrorLogger).logErrorInMetric(Mockito.any(), Mockito.any());
    }

    @Test(expected = ClientGatewayException.class)
    public void testValidateAvailRequest_NoGiHotelIdAndVcId() throws ClientGatewayException {
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        requestHandler.validateAvailRequest(availRoomsRequest);
    }

    @Test(expected = ClientGatewayException.class)
    public void testValidateAvailRequest_NoGiHotelId() throws ClientGatewayException {
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setVcId("vcId");
        requestHandler.validateAvailRequest(availRoomsRequest);
    }

    @Test(expected = ClientGatewayException.class)
    public void testValidateAvailRequest_NoVcId() throws ClientGatewayException {
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setGiHotelId("giHotelId");
        requestHandler.validateAvailRequest(availRoomsRequest);
    }

    @Test(expected = ClientGatewayException.class)
    public void testValidateAvailRequest_BlankGiHotelIdAndVcId() throws ClientGatewayException {
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setGiHotelId("");
        availRoomsRequest.getSearchCriteria().setVcId("");
        requestHandler.validateAvailRequest(availRoomsRequest);
    }

    @Test
    public void testValidateAvailRequest_ValidRequest() throws ClientGatewayException {
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setGiHotelId("giHotelId");
        availRoomsRequest.getSearchCriteria().setVcId("vcId");
        requestHandler.validateAvailRequest(availRoomsRequest);
    }
}
