package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.pms.DetailPageOfferCardsConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.jsonviews.LongStayBenefits;
import com.mmt.hotels.pojo.response.CampaignPojo;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class OfferCardUtilTest {

    @Mock
    MobConfigHelper mobConfigHelper;

    @Mock
    Utility utility;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @InjectMocks
    OfferCardUtil offerCardUtil;

    @Before
    public void setUp() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        DetailPageOfferCardsConfig detailPageOfferCardsConfig;
        try {
            detailPageOfferCardsConfig  = new ObjectMapper().readValue(
                    ResourceUtils.getFile("classpath:detailPageOfferCardsConfig.json"),
                    DetailPageOfferCardsConfig.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Mockito.when(mobConfigHelper.getDetailPageOfferCardsConfig()).thenReturn(detailPageOfferCardsConfig);
    }

    @Test
    public void testDSDCard() {
        HotelRates hotelRates = new HotelRates();
        try {
            Object dailyStealDealHESResp = new Gson().fromJson(
                    FileUtils.readFileToString(ResourceUtils.getFile("classpath:detail/hotelPersuasionDeals.json")),
                    Object.class);
            hotelRates.setHotelPersuasions(dailyStealDealHESResp);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<CardData> cardDataList = offerCardUtil.getDetailPageOfferCards(hotelRates, null, null);
        Assertions.assertEquals(1, cardDataList.size());
        Assertions.assertEquals("DAILY_STEAL_DEAL", cardDataList.get(0).getCardInfo().getId());
    }

    @Test
    public void testLongStayBenefitCard() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setLongStayBenefits(new LongStayBenefits());
        hotelRates.getLongStayBenefits().setInclusionsList(new ArrayList<>());
        hotelRates.getLongStayBenefits().getInclusionsList().add(new Inclusion());
        hotelRates.getLongStayBenefits().getInclusionsList().get(0).setCode("LOS1");
        hotelRates.getLongStayBenefits().getInclusionsList().get(0).setValue("LOS Value 1");
        List<CardData> cardDataList = offerCardUtil.getDetailPageOfferCards(hotelRates, null, null);
        Assertions.assertEquals(1, cardDataList.size());
        Assertions.assertEquals("LONG_STAY_BENEFITS", cardDataList.get(0).getCardInfo().getId());
    }

    @Test
    public void testSaleCampaignCard() {
        Mockito.when(utility.isB2CFunnel()).thenReturn(true);
        HotelRates hotelRates = new HotelRates();
        hotelRates.setCampaignPojo(new CampaignPojo());
        hotelRates.getCampaignPojo().setHeading("Sale Campaign Heading");
        List<CardData> cardDataList = offerCardUtil.getDetailPageOfferCards(hotelRates, null,null);
        Assertions.assertEquals(1, cardDataList.size());
        Assertions.assertEquals("SALE_CAMPAIGN", cardDataList.get(0).getCardInfo().getId());
    }

    @Test
    public void testElitePackageCard() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setPackageRooms(new ArrayList<>());
        searchRoomsResponse.getPackageRooms().add(new RoomDetails());
        searchRoomsResponse.getPackageRooms().get(0).setRatePlans(new ArrayList<>());
        searchRoomsResponse.getPackageRooms().get(0).getRatePlans().add(new SelectRoomRatePlan());
        searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).setInclusionsList(new ArrayList<>());
        searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getInclusionsList().add(new BookedInclusion());
        searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getInclusionsList().get(0).setText("Package Room Inclusion 1");
        searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getInclusionsList().get(0).setPackageBenefit(true);
        List<CardData> cardDataList = offerCardUtil.getDetailPageOfferCards(null, searchRoomsResponse,null);
        Assertions.assertEquals(1, cardDataList.size());
        Assertions.assertEquals("ELITE_PACKAGE", cardDataList.get(0).getCardInfo().getId());
    }
}
