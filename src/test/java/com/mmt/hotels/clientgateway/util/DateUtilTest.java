package com.mmt.hotels.clientgateway.util;

import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.text.ParseException;

@RunWith(MockitoJUnitRunner.class)
public class DateUtilTest {
    @InjectMocks
    DateUtil dateUtil;

    @Test
    public void getDateFormattedTest(){
        Assert.assertNotNull(dateUtil.getDateFormatted("21/10/2021","dd/MM/yyyy","dd-MM-yyyy"));
    }

    @Test
    public void convertEpochToDateTimeTest() {
        long epoch = new DateTime().getMillis();
        String format = "dd MMM,hh:mm a ";
        String tillDate = dateUtil.convertEpochToDateTime(epoch,format);
        Assert.assertNotNull(tillDate);
    }

    @Test
    public void getDaysDiffTest() throws ParseException {
        Assert.assertNotNull(dateUtil.getDaysDiff("2022-10-21","2022-10-25"));
        Assert.assertNotNull(dateUtil.getDaysDiff("2022-10-05"));
    }
}

