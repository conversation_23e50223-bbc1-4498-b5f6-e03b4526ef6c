package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test cases for the Partial Refundable feature
 * This test class focuses on the buildPartialRefundDateText method which is the core of the partial refundable functionality
 */
@RunWith(MockitoJUnitRunner.class)
public class PartialRefundableFeatureTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private Utility utility;

    @Before
    public void setUp() {
        // Setup common mock responses
        when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_CHECKIN_TEXT"))
                .thenReturn("Partially refundable till check-in");
        when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT"))
                .thenReturn("Partially refundable before {0} {1}");
        when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_TEXT"))
                .thenReturn("Partially refundable");
    }

    // ==================== buildPartialRefundDateText Tests ====================

    @Test
    public void testBuildPartialRefundDateText_NullTimeline() {
        // When
        String result = utility.buildPartialRefundDateText(null);

        // Then
        assertEquals("", result);
    }

    @Test
    public void testBuildPartialRefundDateText_CheckInAndRefundDateSame() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "15 Jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable till check-in", result);
    }

    @Test
    public void testBuildPartialRefundDateText_DifferentCheckInAndRefundDate() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "10 Jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable before 10 Jul 11:59 am", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithEmptyRefundDateTime() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "10 Jul", "");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithNullRefundDateTime() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "10 Jul", null);

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithEmptyRefundDate() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithNullRefundDate() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", null, "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithEmptyCheckInDate() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("", "10 Jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable before 10 Jul 11:59 am", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithNullCheckInDate() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline(null, "10 Jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable before 10 Jul 11:59 am", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithEmptyTimelineList() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            new com.mmt.hotels.model.response.pricing.CancellationTimeline();
        cancellationTimeline.setCheckInDate("15 Jul");
        cancellationTimeline.setCancellationPolicyTimelineList(new ArrayList<>());

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithNullTimelineList() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            new com.mmt.hotels.model.response.pricing.CancellationTimeline();
        cancellationTimeline.setCheckInDate("15 Jul");
        cancellationTimeline.setCancellationPolicyTimelineList(null);

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithNullFirstTimeline() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            new com.mmt.hotels.model.response.pricing.CancellationTimeline();
        cancellationTimeline.setCheckInDate("15 Jul");
        List<com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline> timelineList = new ArrayList<>();
        timelineList.add(null);
        cancellationTimeline.setCancellationPolicyTimelineList(timelineList);

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_CaseInsensitiveDateComparison() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 JUL", "15 jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable till check-in", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithSpecialCharactersInDates() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15-Jul", "15-Jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable till check-in", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithVeryLongDateStrings() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 December 2024", "10 December 2024", "11:59:59 PM");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable before 10 December 2024 11:59:59 PM", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithPolyglotServiceFailure() {
        // Given
        when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT"))
                .thenReturn(null);
        
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "10 Jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithEmptyPolyglotResponse() {
        // Given
        when(polyglotService.getTranslatedData("PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT"))
                .thenReturn("");
        
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "10 Jul", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithComplexTimeFormats() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "10 Jul", "11:59:59 PM");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable before 10 Jul 11:59:59 PM", result);
    }

    @Test
    public void testBuildPartialRefundDateText_With24HourTimeFormat() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15 Jul", "10 Jul", "23:59");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable before 10 Jul 23:59", result);
    }

    @Test
    public void testBuildPartialRefundDateText_WithDifferentDateFormats() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("15/07/2024", "10/07/2024", "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable before 10/07/2024 11:59 am", result);
    }

    // ==================== Edge Cases and Error Handling ====================

    @Test
    public void testPartialRefundable_WithAllNullValues() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline(null, null, null);

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testPartialRefundable_WithEmptyStrings() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("", "", "");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable", result);
    }

    @Test
    public void testPartialRefundable_WithWhitespaceOnly() {
        // Given
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline("   ", "   ", "   ");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable till check-in", result);
    }

    @Test
    public void testPartialRefundable_WithVeryLongStrings() {
        // Given
        String longDate = "A very long date string that exceeds normal expectations";
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            createCancellationTimeline(longDate, longDate, "11:59 am");

        // When
        String result = utility.buildPartialRefundDateText(cancellationTimeline);

        // Then
        assertEquals("Partially refundable till check-in", result);
    }

    // ==================== Helper Methods ====================

    private com.mmt.hotels.model.response.pricing.CancellationTimeline createCancellationTimeline(
            String checkInDate, String refundDate, String refundDateTime) {
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = 
            new com.mmt.hotels.model.response.pricing.CancellationTimeline();
        cancellationTimeline.setCheckInDate(checkInDate);
        
        if (refundDate != null || refundDateTime != null) {
            List<com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline> timelineList = new ArrayList<>();
            com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline timeline = 
                new com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline();
            timeline.setEndDate(refundDate);
            timeline.setEndDateTime(refundDateTime);
            timelineList.add(timeline);
            cancellationTimeline.setCancellationPolicyTimelineList(timelineList);
        }
        
        return cancellationTimeline;
    }
} 