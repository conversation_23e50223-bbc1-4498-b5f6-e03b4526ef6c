version: '3.3'
services:
  mmt-clientbackend:
    image: "524881529748.dkr.ecr.ap-south-1.amazonaws.com/hotels-gi-clientgateway:${TAG}"
    container_name: hotels-gi-clientgateway
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/clientbackend-gi/cg/healthcheck"]
      interval: 100s
      timeout: 50s
      retries: 5
    volumes:
      - /opt/logs/hotels-gi-clientgateway:/opt/logs/tomcat
      - "/root/.aws/:/root/.aws/"
    ports:
      - "9310:8080"

