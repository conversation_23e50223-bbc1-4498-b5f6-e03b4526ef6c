#!/bin/bash
#
# MMTWeb start/stop script for apache httpd + tomcat
HO=`hostname`.mmt.mmt

USER=root

start() {
/etc/init.d/tomcat8 start
echo "Waiting for tomcat to load:"
sleep 5
echo "Starting  Apache HTTPD:"
/etc/init.d/httpd start
sleep 5
#(sleep 180; curl -d "hostname=$HO&service=all&newstate=on" http://chn-checkit.mmt.mmt/notifications.php)&
echo "MMTWeb Started."
echo
touch /opt/tomcat/subsys/mmtwebd
sleep 3
}

stop() {
echo "Getting Stats first before app shutdown"
su -l $USER -c "/bin/bash /usr/local/bin/get_stats.sh"
echo "Stopping  Apache HTTPD:"
/etc/init.d/httpd stop
#curl -d "hostname=$HO&service=all&newstate=off" http://chn-checkit.mmt.mmt/notifications.php
echo "Stopping Tomcat"
/etc/init.d/tomcat8 stop
echo "Waiting for java to Kill"
sleep 7
## killing All java
echo "going to kill java forcefully using user ${USER}"
su -l ${USER} -c "ps -ef | grep PCService | grep -v grep  | awk '{print \$2}' |xargs -i kill -9 {}"
su -l ${USER} -c "ps -ef | grep Bootstrap | grep -v grep  | awk '{print \$2}' |xargs -i kill -9 {}"
su -l ${USER} -c "rm -rf /opt/tomcat/tomcat8/work/*"
sleep 5
echo "MMTWEB Stopped. Please Check if there are any java/http process running by typing 'pgrep java' and 'pgrep http' "
rm -f /opt/tomcat/subsys/mmtwebd
echo
}

status() {
echo "Apache HTTPD status:"
/etc/init.d/httpd status
echo "Tomcat status:"
/etc/init.d/tomcat8 status
}

case "$1" in
start)
start
;;
stop)
stop
;;
restart)
stop
sleep 3
start
;;
status)
status
;;
*)
echo "Usage: mmtwebd {start|stop|restart|status}"
exit 1
esac
