#!/bin/bash

DIR='/opt/logs/tomcat/tomcat8'
DT=`date +%F_%R`
MEMF='mem-flights.mmt.mmt'
MEMH='mem-hotels.mmt.mmt'
DB='mysql1.mmt.mmt'
file='/opt/logs/tomcat/tomcat8/catalina.out'

echo $DT
mkdir $DIR/$DT
ID=`ps -ef|grep java|grep 'org.apache.catalina.startup.Bootstrap'|awk '{print $2}'|sed -e 's/ //g'`
echo $ID
kill -3 $ID
sleep 5
line_start=`grep -n 'Full thread dump Java HotSpot(TM)' $file| tail -1 | cut -f1 -d:`
tot_line=`wc -l $file | awk '{print $1}'`
if [ -n "$line_start" ]
then
tail_num=$((($tot_line-$line_start)+1))
else
tail_num=3000
fi

tail -$tail_num $file >$DIR/$DT/jvm.dump
top -b -n 1 >$DIR/$DT/top
free -m >$DIR/$DT/memory
netstat -s >$DIR/$DT/network
netstat -an >>$DIR/$DT/netstat
netstat -an|grep '$DB'|wc -l >$DIR/$DT/dbconn
netstat -an|grep '$MEMF'|wc -l >$DIR/$DT/memconn
netstat -an|grep '$MEMH'|wc -l >$DIR/$DT/memconn
netstat -an|grep '8009'|wc -l >>$DIR/$DT/javaconn
