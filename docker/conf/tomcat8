#!/bin/bash
#
# Startup script for Tomcat5
#
# description: Tomcat jakarta JSP server
#Necessary environment variables
export CATALINA_HOME="/opt/tomcat/tomcat8"
USER=root
if [ ! -f $CATALINA_HOME/bin/catalina.sh ]
then
echo "Tomcat not available..."
exit
fi

export  JAVA_OPTS="$JAVA_OPTS -javaagent:/opt/jacoco/lib/jacocoagent.jar=port=36320,destfile=/scm/coverage/hotel/sp-affiliateengineapi/jacoco.exec,output=tcpserver"


start() {
echo -n -e '\E[0;0m'"\033[1;32mStarting Tomcat: \033[0m \n"
su -l $USER -c $CATALINA_HOME/bin/startup.sh
echo
touch /opt/tomcat/subsys/tomcat8
sleep 3
}

stop() {
echo -n -e '\E[0;0m'"\033[1;31mShutting down Tomcat: \033[m \n"
su -l $USER -c $CATALINA_HOME/bin/shutdown.sh
rm -f /opt/tomcat/subsys/tomcat8
echo
}

status() {
ps ax | grep "[o]rg.apache.catalina.startup.Bootstrap start" | awk '{printf $1 " "}' | wc | awk '{print $2}' > /tmp/tomcat8_process_count.txt
read line < /tmp/tomcat8_process_count.txt
if [ $line -gt 0 ]; then
echo -n "tomcat8 ( pid "
ps ax | grep "[o]rg.apache.catalina.startup.Bootstrap start" | awk '{printf $1 " "}'
echo -n ") is running..."
echo
else
echo "Tomcat is stopped"
fi
}

case "$1" in
start)
start
;;
stop)
stop
;;
restart)
stop
sleep 3
start
;;
status)
status
;;
*)
echo "Usage: tomcat8 {start|stop|restart|status}"
exit 1
esac 
