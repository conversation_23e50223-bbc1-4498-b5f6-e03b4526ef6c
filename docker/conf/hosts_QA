# Copyright (c) 1993-2009 Microsoft Corp.
#
# This is a sample HOSTS file used by Microsoft TCP/IP for Windows.
#
# This file contains the mappings of IP addresses to host names. Each
# entry should be kept on an individual line. The IP address should
# be placed in the first column followed by the corresponding host name.
# The IP address and the host name should be separated by at least one
# space.
#
# Additionally, comments (such as these) may be inserted on individual
# lines or following the machine name denoted by a '#' symbol.
#
# For example:
#
#      ************     rhino.acme.com          # source server
#       ***********     x.acme.com              # x client host

# localhost name resolution is handled within DNS itself.
#	127.0.0.1       localhost
#	::1             localhost

##############################################################
# Host entries for running B2B services
************ webservices.sabre.com
*************** securepg.fssnet.co.in
************** tempuri.org
************** production.webservices.amadeus.com

#************* cb-42-40.mmt.mmt cb-42-41.mmt.mmt cb-42-42.mmt.mmt

************* cb-hotel-150.mmt.mmt
************* cb-hotel-151.mmt.mmt
************* cb-hotel-152.mmt.mmt
************* cb-hotel-153.mmt.mmt
************* cb-hotel-154.mmt.mmt
************* cb-hotel-155.mmt.mmt
************* cb-hotel-156.mmt.mmt
************* cb-42-40.mmt.mmt
************* cb-42-41.mmt.mmt
************* cb-42-42.mmt.mmt cb-42-60.mmt.mmt cb-42-61.mmt.mmt cb-42-62.mmt.mmm
m
t
************* cb-42-70.mmt.mmt cb-42-71.mmt.mmt cb-42-72.mmt.mmt cb-42-73.mmt.mmm
m
t

#***********  cb-42-40.mmt.mmt cb-42-41.mmt.mmt cb-42-42.mmt.mmt
*************  loyalty-msg-service.mmt.mmt
************  b2chotels-drools.mmt.mmt
#*************   htlwebapi.mmt.mmt

#************   htlwebapi.mmt.mmt

************  htlwebapi.mmt.mmt
#************  htlwebapi.mpi.mmt.mmt.mmt
*************  htl-content-upsell-api.mmt.mmt
************  solrcloud-api.mmt.mmt
#************  corp-cb.mmt.mmt
#************* corp-cb.mmt.mmt

************  compay.makemytrip.com m-securepay.makemytrip.com c-securepay.makemytrip.com payments.makemytrip.com compay.mmt.mmt

#************ corp-cb.mmt.mmt
#************  corp-cb.mmt.mmt
************ corp-cb.mmt.mmt
************    userservice.makemytrip.com userservice.mmt.mmt userservice_oth.mmt.mmt
#************ userservice.mmt.mmt
#************ userservice.mmt.mmt
#************* userservice.mmt.mmt
#************ userservice.mmt.mmt

************* hcs.mmt.mmt

#************* hcs.mmt.mmt hcs.makemytrip.com
#************* hcs.mmt.mmt hcs.makemytrip.com
#************* hcs.mmt.mmt hcs.makemytrip.com
                            
*********** mysql2.mmt.mmt


*************  ab.mmt.mmt
#*************  fpm.mmt.mmt
************* fpm.mmt.mmt

************ hotels-drools.mmt.mmt

************ walletwebservices.mmt.mmt

#####################ClientBackEnd#####################

#************  hydra.mmt.mmt
************  hydra.mmt.mmt

************ fpm.mmt.mmt
#************  htlwebapi.mmt.mmt htlwebapi.mmt.mmt.other
************* persuasion.mmt.mmt
************* fraud-control.mmt.mmt
#***********  b2chotels-drools.mmt.mmt
************* zk-mmt.mmt.mmt
#************  userservice.mmt.mmt

************* kafka-mmt.mmt.mmt
************* mdm-repo.mmt.mmt
************* kafka-hotels.mmt.mmt
************* kerberos.mmt.mmt
************* kafka-data.mmt.mmt

************ corp-userservice.mmt.mmt
************ corp-bs.mmt.mmt
************ corp-policy.mmt.mmt
************* htl-content-api.mmt.mmt
                                                                   
