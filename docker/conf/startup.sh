#!/bin/sh

# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# -----------------------------------------------------------------------------
# Start Script for the CATALINA Server
#
# $Id: startup.sh 1202062 2011-11-15 06:50:02Z mturk $
# -----------------------------------------------------------------------------

# Better OS/400 detection: see Bugzilla 31132


PRG="$0"

NOW=`date +%Y%m%dT%H%M`
#JAVA_OPTS="-server -Xms5120m -Xmx5120m -Dsun.rmi.dgc.client.gcInterval=********** -Dsun.rmi.dgc.server.gcInterval=********** -Dsun.reflect.inflationThreshold=15 -Dsun.reflect.noInflation=true -Dsun.security.ssl.allowUnsafeRenegotiation=true"

#JAVA_OPTS="$JAVA_OPTS -XX:MaxPermSize=1024m "
#JAVA_OPTS="$JAVA_OPTS -XX:ParallelGCThreads=6"
#JAVA_OPTS="$JAVA_OPTS -XX:+CMSIncrementalMode -XX:+CMSIncrementalPacing"
#JAVA_OPTS="$JAVA_OPTS -XX:CMSIncrementalDutyCycleMin=3"
#JAVA_OPTS="$JAVA_OPTS -XX:CMSIncrementalDutyCycle=20"
#JAVA_OPTS="$JAVA_OPTS -XX:CMSIncrementalSafetyFactor=20"
#JAVA_OPTS="$JAVA_OPTS -XX:+PrintClassHistogram -XX:+PrintHeapAtGC"
#JAVA_OPTS="$JAVA_OPTS  -XX:-UseLargePages"
#JAVA_OPTS="$JAVA_OPTS -verbose:gc -Xloggc:/opt/apache-tomcat-6.0.32/logs/gclog-$NOW "
#JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"
#JAVA_OPTS="$JAVA_OPTS -XX:+PrintTenuringDistribution -XX:+PrintAdaptiveSizePolicy"
#JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCApplicationConcurrentTime"
#JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCApplicationStoppedTime"
#JAVA_OPTS="$JAVA_OPTS -XX:+UsePerfData -XX:SurvivorRatio=6"
#JAVA_OPTS="$JAVA_OPTS -XX:+DisableExplicitGC"
#JAVA_OPTS="$JAVA_OPTS -XX:+CMSPermGenSweepingEnabled -XX:+UseCMSCompactAtFullCollection -XX:CMSFullGCsBeforeCompaction=0 -XX:+CMSClassUnloadingEnabled"
#JAVA_OPTS="$JAVA_OPTS -XX:-CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0"
JAVA_OPTS=" -server -Xms1g -Xmx1g -XX:+UseG1GC"
#JAVA_OPTS="$JAVA_OPTS  -XX:MaxPermSize=512M "
JAVA_OPTS="$JAVA_OPTS -javaagent:/opt/jacoco/lib/jacocoagent.jar=destfile=/scm/coverage/hotel/sp-affiliateengineapi/jacoco.exec,append=false,includes=*,dumponexit=true"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.port=9003 -Dcom.sun.management.jmxremote.password.file=/opt/apache-tomcat-8.0.32/conf/jmxremote.password -Dcom.sun.management.jmxremote.access.file=/opt/apache-tomcat-8.0.32/conf/jmxremote.access -Dcom.sun.management.jmxremote.ssl=false"
JAVA_OPTS="$JAVA_OPTS -Xdebug -Xrunjdwp:transport=dt_socket,address=8000,server=y,suspend=n"
JAVA_OPTS="$JAVA_OPTS -verbose:gc -Xloggc:/opt/apache-tomcat-8.0.32/logs/gclog-$NOW "

export JAVA_OPTS


os400=false
case "`uname`" in
OS400*) os400=true;;
esac

# resolve links - $0 may be a softlink
PRG="$0"

while [ -h "$PRG" ] ; do
  ls=`ls -ld "$PRG"`
  link=`expr "$ls" : '.*-> \(.*\)$'`
  if expr "$link" : '/.*' > /dev/null; then
    PRG="$link"
  else
    PRG=`dirname "$PRG"`/"$link"
  fi
done

PRGDIR=`dirname "$PRG"`
EXECUTABLE=catalina.sh

# Check that target executable exists
if $os400; then
  # -x will Only work on the os400 if the files are:
  # 1. owned by the user
  # 2. owned by the PRIMARY group of the user
  # this will not work if the user belongs in secondary groups
  eval
else
  if [ ! -x "$PRGDIR"/"$EXECUTABLE" ]; then
    echo "Cannot find $PRGDIR/$EXECUTABLE"
    echo "The file is absent or does not have execute permission"
    echo "This file is needed to run this program"
    exit 1
  fi
fi

exec "$PRGDIR"/"$EXECUTABLE" start "$@"