# Base image
FROM 524881529748.dkr.ecr.ap-south-1.amazonaws.com/mmt-ecs-base:java-8-ubuntu22-multiarch

# Tomcat version
ENV TOMCATVER 8.5.41

# Update package lists and install wget (Ubuntu-specific)
RUN apt update && apt install -y wget

# Install Apache HTTP Server
RUN apt update -y
RUN apt-get update && apt-get install -y libapache2-mod-jk
RUN apt install apache2 apache2-dev -y

# Enable the configurations
RUN a2enmod headers rewrite usertrack
RUN a2dismod mpm_worker mpm_event && a2enmod mpm_prefork

# Reload Apache to apply configurations (will ensure no syntax errors during build)
RUN service apache2 reload || true

# Download and extract the specified version of Tomcat
RUN (wget O /tmp/tomcat8.tar.gz http://mirror.cogentco.com/pub/apache/tomcat/tomcat-8/v${TOMCATVER}/bin/apache-tomcat-${TOMCATVER}.tar.gz && \
cd /opt && \
tar zxf /tmp/tomcat8.tar.gz && \
rm /tmp/tomcat8.tar.gz && \
mv /opt/apache-tomcat* /opt/tomcat)

# Copy Tomcat server configuration file
COPY docker/server.xml /opt/tomcat/conf/server.xml

# Set permissions for JMX access control files
COPY .docker/jmxremote.access /opt/tomcat/conf/jmxremote.access
RUN chmod 777 /opt/tomcat/conf/jmxremote.access

COPY .docker/jmxremote.password /opt/tomcat/conf/jmxremote.password
RUN chmod 600 /opt/tomcat/conf/jmxremote.password

# Copy and configure environment settings for Tomcat
COPY .docker/setenv.sh /opt/tomcat/bin/
RUN sed -i '/JAVA_OPTS="$JAVA_OPTS $JSSE_OPTS"/a JAVA_OPTS="$JAVA_OPTS -Xms6g -Xmx6g -XX:MaxPermSize=512m -XX:+UseG1GC -verbose:gc -Xloggc:/opt/tomcat/logs/gc.log -Ddrools.schema.validating=false -Denv=prod"' /opt/tomcat/bin/catalina.sh

# Deploy the application WAR file to Tomcat's webapps directory
COPY Hotels-GI-ClientGateway/target/clientgateway-gi.war /opt/tomcat/webapps/clientgateway-gi.war

# Set the environment variable for the application
ENV env prod

# Copy the entrypoint script and set executable permissions
COPY .docker/entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set the entrypoint script to be executed when the container starts
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# Default command to run Tomcat
CMD ["/opt/tomcat/bin/catalina.sh", "run"]
