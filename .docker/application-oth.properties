pool.name=BOT

search.personalized.hotels.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/listingPersonalization
search.hotels.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/searchHotels
mob.landing.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/hotels/landing
listing.map.hes.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/listingMap
filter.count.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/filter
search.rooms.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/searchPrice
comparator.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/upsellHotels
alternate.dates.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/alternateDates
static.rooms.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/roominfo
updated.price.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/getUpdatedDisplayPricing
avail.rooms.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotels/availPrice
updated.price.occu.less.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/getUpdatedDisplayPricing
nearby.hotels.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/nearbyHotels
fetch.collection.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/fetchCollections
updated.emi.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/getUpdatedEmi
webapi.getPolicies.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotel/getPolicies?correlationKey=%s&txnKey=%s
validate.coupon.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/validateCoupon
place.lat.lng.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v4.0/hotels/suggest/getplacedetails?id=%s
lat.lng.city.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v4.0/hotels/suggest/getcitydetails?lat=%s&lng=%s
webapi.addons.get.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotels/getAddOns
txndata.get.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v3.0/txnData/get/{0}
meta.data.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/city/%s/hotels/metadata
list.personalization.card.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/hotels/listpersonalizedcards
affiliate.update.fee.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotels/affiliate/updateFee
affiliate.create.quote.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotels/affiliate/createQuote
affiliate.get.quote.data.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotels/affiliate/getQuoteDataMerged
hotel.image.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/images
static.details.url=http://hotels-entity-service-detail-oth.ecs.mmt/hotels-entity-detail/api/v2.0/hotel/staticDetails
city.data.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/city/%s?locationId=%s&locationType=%s
payment.checkout.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotels/paymentCheckout
corporate.update.approval.url = http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/corporate/updateApproval/%s
corporate.update.approvals.url = http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/corporate/updateApprovals
corporate.workflow.info.url = http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/corporate/getApproval/%s
corporate.init.approval.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/corporate/initApproval
corporate.update.policy.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/corporate/updatePolicy
city.guide.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/cityGuide
corporate.approvals.info.url = http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/corporate/getApprovals
landing.discovery.url=http://hotels-entity-service-listing-oth.ecs.mmt/hotels-entity-listing/api/v2.0/landingDiscovery
offer.details.url=http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/hotels/offerDetails
orch.search.hotels.url=http://hotels-orchestrator-oth.ecs.mmt/hotels-orchestrator/api/v1.0/searchHotels

orch.static.details.url=http://hotels-orchestrator-detail-oth.ecs.mmt/hotels-orchestrator-detail/api/v1.0/staticDetails
orch.search.rooms.url=http://hotels-orchestrator-detail-oth.ecs.mmt/hotels-orchestrator-detail/api/v1.0/searchRooms
orch.update.price.url=http://hotels-orchestrator-detail-oth.ecs.mmt/hotels-orchestrator-detail/api/v1.0/updatePrice

enable.kafka.logging=false