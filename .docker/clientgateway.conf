<VirtualHost *:80>
    ServerName www.makemytrip.com
    ServerAlias www.makemytrip.com
    JkMountCopy On
    JkUnMount /*.GiF clientbackend-gi
    DocumentRoot /opt/tomcat/webapps/

    <Directory "/opt/tomcat/webapps/">
        DirectoryIndex index.php home.shtml index.html index.jsp
        Options None
        AllowOverride All
        Order allow,deny
        Allow from all
        Require all granted
        Satisfy Any

        Header unset ETag
        FileETag None

        <Limit GET POST >
            Order allow,deny
            Allow from all
        </Limit>
        <LimitExcept GET POST >
            Order deny,allow
            Deny from all
        </LimitExcept>

    </Directory>

    <Directory />
        Options None
            AllowOverride None
            Order deny,allow
            Deny from all
            Require all denied
    </Directory>

    
    LogFormat "%{X-Forwarded-For}i %h %l %u %t \"%r\" %>s %b %D %D \"%{Referer}i\" \"%{User-Agent}i\" \"%{<PERSON>ie}i\" %D %P %T %B %{x-akamai-device-characteristics}i %{Akamai-Bot}i %{x-akamai-edgescape}i %{x-akamai-device-characteristics}o %{Akamai-Bot}o" clientbackend-gi
    CustomLog /opt/logs/httpd/access.log clientbackend-gi
    ErrorLog /opt/logs/httpd/error.log

    ErrorDocument 413 http://www.makemytrip.com/RETL.php
    ErrorDocument 400 http://www.makemytrip.com/RETL.php

    JkMount /* clientbackend-gi
    JkMount /progressivist/* clientbackend-gi
    JkMount /clientbackend-gi/* clientbackend-gi
    JkMount /*.jsp clientbackend-gi
    JkMount /*.do clientbackend-gi
    JkMount /imint/* clientbackend-gi
    JkMount /*.js clientbackend-gi

    JkMount /*.ico clientbackend-gi
    JkMount /*.jpg clientbackend-gi
    JkMount /*.gif clientbackend-gi
    #JkUnMount /*.png clientbackend-gi
    #JkUnMount /*.js clientbackend-gi
    #JkUnMount /*.css clientbackend-gi
    JkMount /*.GIF clientbackend-gi
    JkUnMount /*.php clientbackend-gi

    JkMount /*.ICO clientbackend-gi
    JkMount /*.JPG clientbackend-gi
    JkMount /*.PNG clientbackend-gi
    JkMount /*.JS clientbackend-gi
    JkMount /*.CSS clientbackend-gi
    JkMount /*.GIF clientbackend-gi

    RewriteRule (^|/)(CVS|\.svn|\.git|\.sh)/ - [F,L]

    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript

</VirtualHost>
