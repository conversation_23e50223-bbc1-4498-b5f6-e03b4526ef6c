# Load mod_jk module
LoadModule jk_module /usr/lib/apache2/modules/mod_jk.so

# Specify the workers file
JkWorkersFile /etc/apache2/workers.properties

# Where to put jk shared memory
JkShmFile /var/log/apache2/jk-runtime-status

# Where to put jk logs
JkLogFile /opt/logs/httpd/mod_jk.log

# Set the jk log level [debug/error/info]
JkLogLevel error

# Select the timestamp log format
JkLogStampFormat "[%a %b %d %H:%M:%S %Y] "

# Uncomment and configure additional options as needed
# JkRequestLogFormat "%w %V %T"
# JkEnvVar SSL_CLIENT_V_START worker1