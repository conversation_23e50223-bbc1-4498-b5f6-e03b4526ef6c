PreRequisite - Java8, Maven3.7. and m2/settings.xml is updated correctly See the installations/onboarding file on sharepoint/confluence.
Note: if you have already setup hotels-entity-service, you can skip directly to point no 4

1. setup host file. do minimally below host entry. All the hotels related downstream layers are directly accessible.
 so no need to do host entry for them. Usually host entry required for Couchbase, Redis, Kafka. For rest of the services,
 do host entry when needed. Below are the host entries.

 ************* userservice.mmt.mmt
 ************* hotels-drools.mmt.mmt
 ************* fpm.mmt.mmt
 ************ ldap-ad.mmt.mmt


 ##mmt couchbase
 ************** cb-42-40.mmt.mmt cb-42-41.mmt.mmt cb-42-42.mmt.mmt
 ************** cb-42-42.mmt.mmt cb-42-60.mmt.mmt cb-42-61.mmt.mmt cb-42-62.mmt.mmt
 ************** cb-42-70.mmt.mmt cb-42-71.mmt.mmt cb-42-72.mmt.mmt cb-42-73.mmt.mmt



 #kafka
 ************* kafka-mmt.mmt.mmt kafka-others.mmt.mmt kafka-data.mmt.mmt
 ************* mdm-repo.mmt.mmt
 ************* kafka-hotels.mmt.mmt kafka-hotels-chn.mmt.mmt
 ************* kerberos.mmt.mmt

 ************ htl-redis-cache.mmt.mmt m-htl-redis-cache.ey9a0h.clustercfg.aps1.cache.amazonaws.com m-htl-dynamicdsct.ey9a0h.clustercfg.aps1.cache.amazonaws.com

2. to create some directories and give them access
    a. first go to home directory
     windows - whichever drive your code base resides that partition is treated as root ,. for ex D:\ is ur root/
     linux/mac - cd ~

    b. on linux/mac do following commands. create the directory, then change its owner and
    provide it read write execute rights using below commands
     mkdir /opt/logs/tomcat
     sudo chown -R mmt8853 /opt
     sudo chmod -R 777 /opt/


 3. For kafka we need to run following commands
    a. make folder kerberos in etc using below command
        mkdir /etc/kerberos/
    b. in the codebase of this project you will see a folder kerberos in .docker folder. copy content of this folder
     in your /etc/kerberos file.Make sure you mention your codebase path
        cp ~/Documents/CodeBase/Hotels-ClientGateway/docker/kerberos/conf/* /etc/kerberos
    c. give permissions and rights using below command
        cd /etc/
        sudo chmod -R 777 kerberos/
        sudo chown -R mmt8853 kerberos/


 4. for drools to run copy the file from your codebase/.docker/drools-mvn-settings.xml to /opt/clientbackend
        mkdir /opt/clientbackend
        cp ~/Documents/CodeBase/Hotels-ClientGateway/.docker/drools-mvn-settings.xml /opt/clientbackend

 5. Go to HotelsClientGatewayApplication.java and run the main method to start the server.